package com.org.panaroma.commons.constants;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Enum representing configuration properties with their default values.
 */
@Getter
public enum ConfigPropertiesEnum {

	// [PTH-901] Set default value for filter vertical name enabled to false
	FILTER_VERTICAL_NAME_ENABLED("filter.vertical.name.filter.enabled", "false", "901"),
	// Controls whether listing tags functionality is enabled in the system
	LISTING_TAG_ENABLED("listingTagEnabled", "false", "PTH-613"),

	// Determines if virtual from date should take precedence over actual from date
	VIRTUAL_FROM_DATE_OVER_FROM_DATE_ENABLED("virtual.from.date.over.from.date.enabled", "false", "PTH-839"),

	HIDE_FILTER_FROM_DATE("hide.filter.from.date", "2024-12-01 00:00:00.000 +0530", "PTH-1295"),

	// Timeline keys to enable global timeline, upi cc and upiLite
	TIMELINE_ENABLED("timeline.enabled", "true", "PTH-720"),

	TIMELINE_UPICC_ENABLED("timeline.upicc.enabled", "true", "PTH-720"),

	TIMELINE_UPILITE_ENABLED("timeline.upilite.enabled", "true", "PTH-720"),

	TIMELINE_CONFIG("timeline.config",
			"{\"transactionStatus\":[\"FAILURE\",\"PENDING\"],\"transactionIndicator\":\"DEBIT\",\"txnDetailsTimelineEnabledSuccessTxnOlderDays\":7,\"txnDetailsTimelineEnabledPendingTxnOlderDays\":15,\"txnDetailsTimelineEnabledFailureTxnOlderDays\":7}",
			"PTH-1270"),
	// [PTH-980] Configuration property that specifies the exception codes for which
	// complete stack traces should be printed in logs.
	PTH_ERROR_CODES_FOR_STACK_TRACE_PRINTING("pth.error.codes.for.stack.trace.print", "4003,4004", "PTH-980"),

	// Specifies the number of days to look back for listing filters when using virtual
	// from date
	VIRTUAL_FROM_DATE_LISTING_FILTER_IN_DAYS("virtual.from.date.listing.filter.in.days", "90", "PTH-839"),
	IFSC_BASED_QUERY_ALLOWED_FOR_UPI_FILTER("filter.upi.filter.ifsc.based.es.query.enabled", "true", "PTH-920"),
	DIFFERENT_UPI_FILTER_QUERY_HANDLING_REQ_FOR_OLD_AND_NEW_BANK_ACCT_ID(
			"filter.upi.filter.different.query.req.for.old.and.new.bankAccId", "false", "PTH-920"),

	MONTH_OF_2025_TILL_WHICH_OLD_FORMAT_BANK_ACCT_ID_IS_USED("bankAcctId.old.format.used.last.month.of.2025.year", "3",
			"PTH-920"),

	VIRTUAL_FROM_DATE_SPECIFIC_ERROR_CODES_DISABLED("virtual.from.date.specific.error.codes.disabled", "false",
			"PTH-839"),

	// [PTH-905] UPI app configurations as a single JSON array
	PSP_INFO_FOR_DETAIL_API("psp.configs",
			"[{\"pspName\":\"phonepe\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_PhonePe.png\",\"pspDisplayText\":\"on PhonePe\",\"handles\":[\"ybl\",\"ibl\",\"axl\"]},{\"pspName\":\"amazonpay\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_AmazonPay.png\",\"pspDisplayText\":\"on Amazon Pay\",\"handles\":[\"apl\"]},{\"pspName\":\"googlepay\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_Gpay.png\",\"pspDisplayText\":\"on Google Pay\",\"handles\":[\"okicici\",\"okaxis\",\"oksbi\",\"okhdfcbank\"]},{\"pspName\":\"whatsapp\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_WhatsappPay.png\",\"pspDisplayText\":\"on WhatsApp\",\"handles\":[\"waaxis\",\"wasbi\",\"wahdfcbank\"]},{\"pspName\":\"cred\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_CredPay.png\",\"pspDisplayText\":\"on CRED\",\"handles\":[\"axisb\",\"yescred\"]},{\"pspName\":\"paytm\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_Paytm.png\",\"pspDisplayText\":\"on Paytm\",\"handles\":[\"ptsbi\",\"ptyes\",\"ptaxis\",\"pthdfc\"]},{\"pspName\":\"bharatpe\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_BharatPe.png\",\"pspDisplayText\":\"on BharatPe\",\"handles\":[\"bpunity\"]},{\"pspName\":\"flipkartupi\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_Supermoney.png\",\"pspDisplayText\":\"on Flipkart UPI\",\"handles\":[\"fkaxis\"]},{\"pspName\":\"navi\",\"pspLogo\":\"https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/UPI_Navi.png\",\"pspDisplayText\":\"on Navi\",\"handles\":[\"naviaxis\"]}]",
			"PTH-905"),
	TXN_STREAM_LAG_CACHE_ENABLED_OPEN_SOURCES("txn.stream.lag.cache.enabled.open.sources", "[\"chat\"]", "PTH-1233"),
	// [PTH-1168] Added this property to disable the passbook menu
	PASSBOOK_MENU_DISABLED("passbook.menu.disabled", "false", "PTH-1168"),

	/**
	 * The sequence of sections visible in the passbook menu is determined by the order of
	 * values specified in this property. The sections will be displayed in the same order
	 * as they are listed here.
	 */
	PASSBOOK_MENU_SECTIONS_CONFIG("passbook.menu.sections.config",
			"passbook.menu.section.payment.history,passbook.menu.section.settings,passbook.menu.section.other.actions",
			"PTH-1168"),

	// Section configurations
	PASSBOOK_MENU_SECTION_PAYMENT_HISTORY("passbook.menu.section.payment.history",
			"{\"title\":\"Payment History\",\"titleLocaleKey\":\"passbook.menu.section.payment.history.display.name\",\"entries\":\"passbook.menu.section.payment.history.entry.download.upi.statement,passbook.menu.section.payment.history.entry.view.hidden.payments,passbook.menu.section.payment.history.entry.view.tags\"}",
			"PTH-1168"),

	PASSBOOK_MENU_SECTION_SETTINGS("passbook.menu.section.settings",
			"{\"title\":\"Settings\",\"titleLocaleKey\":\"passbook.menu.section.settings.display.name\",\"entries\":\"passbook.menu.section.settings.entry.layout,passbook.menu.section.settings.entry.sound,passbook.menu.section.settings.entry.upi.settings\"}",
			"PTH-1168"),

	PASSBOOK_MENU_SECTION_OTHER_ACTIONS("passbook.menu.section.other.actions",
			"{\"title\":\"Other Actions\",\"titleLocaleKey\":\"passbook.menu.section.other.actions.display.name\",\"entries\":\"passbook.menu.section.other.actions.entry.refer.and.earn,passbook.menu.section.other.actions.entry.help.and.support\"}",
			"PTH-1168"),

	// Entry configurations for Payment History section
	PASSBOOK_MENU_SECTION_PAYMENT_HISTORY_ENTRY_DOWNLOAD_UPI_STATEMENT(
			"passbook.menu.section.payment.history.entry.download.upi.statement",
			"{\"title\":\"Download UPI Statement\",\"titleLocaleKey\":\"passbook.menu.section.payment.history.entry.download.upi.statement.display.name\",\"logo\":\"https://tpap-logo.paytm.com/uth/ds.png\",\"deeplink\":\"paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2UsImxvYWRlcl9vcGFxdWVuZXNzIjowfSwicGFyYW1zIjoiP3NvdXJjZT1zZWFyY2ggcGFnZSIsInBhdGgiOiIvIy9wYi91cGktc3RhdGVtZW50In0=\"}",
			"PTH-1168"),

	PASSBOOK_MENU_SECTION_PAYMENT_HISTORY_ENTRY_VIEW_HIDDEN_PAYMENTS(
			"passbook.menu.section.payment.history.entry.view.hidden.payments",
			"{\"title\":\"View Hidden Transactions\",\"titleLocaleKey\":\"passbook.menu.section.payment.history.entry.view.hidden.payments.display.name\",\"label\":\"new\",\"logo\":\"https://tpap-logo.paytm.com/uth/hp.png\",\"deeplink\":\"paytmmp://cash_wallet?featuretype=cash_ledger&tab=hidden_txns&source=pb_home\"}",
			"PTH-1168"),

	PASSBOOK_MENU_SECTION_PAYMENT_HISTORY_ENTRY_VIEW_TAGS("passbook.menu.section.payment.history.entry.view.tags",
			"{\"title\":\"Monthly Spend Summary\",\"titleLocaleKey\":\"passbook.menu.section.payment.history.entry.view.tags.display.name\",\"logo\":\"https://tpap-logo.paytm.com/uth/vt.png\",\"deeplink\":\"paytmmp://cash_wallet?featuretype=cash_ledger&tab=user_tags_summary&source=pb_home\"}",
			"PTH-1168"),

	// Entry configurations for Settings section
	PASSBOOK_MENU_SECTION_SETTINGS_ENTRY_LAYOUT("passbook.menu.section.settings.entry.layout",
			"{\"title\":\"Layout\",\"titleLocaleKey\":\"passbook.menu.section.settings.entry.layout.display.name\",\"logo\":\"https://tpap-logo.paytm.com/uth/Layout.png\",\"subTitle\":\"Choose your view for UPI Accounts\",\"type\":\"layout_carousel\"}",
			"PTH-1168"),

	PASSBOOK_MENU_SECTION_SETTINGS_ENTRY_SOUND("passbook.menu.section.settings.entry.sound",
			"{\"title\":\"Sound Effects\",\"titleLocaleKey\":\"passbook.menu.section.settings.entry.sound.display.name\",\"logo\":\"https://tpap-logo.paytm.com/uth/sound.png\",\"type\":\"sound_effect\"}",
			"PTH-1168"),

	PASSBOOK_MENU_SECTION_SETTINGS_ENTRY_UPI_SETTINGS("passbook.menu.section.settings.entry.upi.settings",
			"{\"title\":\"UPI Settings\",\"titleLocaleKey\":\"passbook.menu.section.settings.entry.upi.settings.display.name\",\"logo\":\"https://tpap-logo.paytm.com/uth/settings.png\",\"deeplink\":\"paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2UsInNob3dCYWNrQnV0dG9uIjpmYWxzZX0sInBhcmFtcyI6Ij91dG1fc291cmNlPVBhc3Nib29rXzNkb3RtZW51IiwicGF0aCI6Ii8jL210L3NldHRpbmdzL2xhbmRpbmcifQ==\"}",
			"PTH-1168"),

	// Entry configurations for Other Actions section
	PASSBOOK_MENU_SECTION_OTHER_ACTIONS_ENTRY_REFER_AND_EARN("passbook.menu.section.other.actions.entry.refer.and.earn",
			"{\"title\":\"Refer & Earn\",\"titleLocaleKey\":\"passbook.menu.section.other.actions.entry.refer.and.earn.display.name\",\"logo\":\"https://tpap-logo.paytm.com/uth/ref.png\",\"deeplink\":\"paytmmp://referral?tag=referral_all&flow=new&group=GLOBAL&sfname=https://storefront.paytm.com/v2/h/refer-and-earn&utm_source=uth\"}",
			"PTH-1168"),

	PASSBOOK_MENU_SECTION_OTHER_ACTIONS_ENTRY_HELP_AND_SUPPORT(
			"passbook.menu.section.other.actions.entry.help.and.support",
			"{\"title\":\"Help & Support\",\"titleLocaleKey\":\"passbook.menu.section.other.actions.entry.help.and.support.display.name\",\"logo\":\"https://tpap-logo.paytm.com/uth/help.png\",\"deeplink\":\"paytmmp://csttree?featuretype=cst_issue&verticalId=-1&launchGeneralGptBot=true&routeToHomePage=false\"}",
			"PTH-1168"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_LIST(
			"txn.details.config.mapping.for.onus.merchant.verticalId.list",
			"4,17,26,56,64,66,70,71,72,73,76,83,84,87,90,94,105,174,187,204", "PTH-1283"),

	// Configuration mappings for individual vertical IDs
	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_4(
			"txn.details.config.mapping.for.onus.merchant.verticalId.4",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId4\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_17(
			"txn.details.config.mapping.for.onus.merchant.verticalId.17",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId17\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_26(
			"txn.details.config.mapping.for.onus.merchant.verticalId.26",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId26\",\"deeplink\":\"paytmmp://bus_order_summary?url=https://cart.paytm.com/v1/myOrders/$orderId?&isfromorderhistory=0&vertical=bus\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_56(
			"txn.details.config.mapping.for.onus.merchant.verticalId.56",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId56\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_64(
			"txn.details.config.mapping.for.onus.merchant.verticalId.64",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId64\",\"deeplink\":\"paytmmp://flight_order_summary?url=https://cart.paytm.com/v1/myOrders/$orderId&isfromorderhistory=0\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_66(
			"txn.details.config.mapping.for.onus.merchant.verticalId.66",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId66\",\"deeplink\":\"paytmmp://mini-app?aId=358b2bd4936a4f34918c620a3c7ac4f9&data=$parametersBase64Hash\",\"parameters\":{\"sparams\":{\"showTitleBar\":false},\"params\":\"?utm_source=passbook\",\"path\":\"/myorders/$orderId\"}}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_70(
			"txn.details.config.mapping.for.onus.merchant.verticalId.70",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId70\",\"deeplink\":\"paytmmp://movie_order_summary?order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_71(
			"txn.details.config.mapping.for.onus.merchant.verticalId.71",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId71\",\"deeplink\":\"paytmmp://recharge_order_summary?url=https://cart.paytm.com/v2/myorders/$orderId/detail\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_72(
			"txn.details.config.mapping.for.onus.merchant.verticalId.72",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId72\",\"deeplink\":\"paytmmp://train_order_summary_v2?url=https://cart.paytm.com/v1/myOrders/$orderId&from=h5\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_73(
			"txn.details.config.mapping.for.onus.merchant.verticalId.73",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId73\",\"deeplink\":\"paytmmp://events?insiderH5Url=https://h5.insider.in/payments/status/fromOrders/orderId/$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_76(
			"txn.details.config.mapping.for.onus.merchant.verticalId.76",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId76\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_83(
			"txn.details.config.mapping.for.onus.merchant.verticalId.83",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId83\",\"deeplink\":\"paytmmp://gold?url=https://cart.paytm.com/v1/myOrders/$orderId&order-summary-type=Gold&From=Order_history&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_84(
			"txn.details.config.mapping.for.onus.merchant.verticalId.84",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId84\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_87(
			"txn.details.config.mapping.for.onus.merchant.verticalId.87",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId87\",\"deeplink\":\"paytmmp://mini-app?aId=77a7aa36c00e469482a6219004fde717&data=$parametersBase64Hash\",\"parameters\":{\"params\":\"\",\"path\":\"/city-bus/orderSummary/$orderId\",\"sparams\":{\"pullRefresh\":false,\"canPullDown\":false,\"showTitleBar\":false}}}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_90(
			"txn.details.config.mapping.for.onus.merchant.verticalId.90",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId90\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_94(
			"txn.details.config.mapping.for.onus.merchant.verticalId.94",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId94\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_105(
			"txn.details.config.mapping.for.onus.merchant.verticalId.105",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId105\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_174(
			"txn.details.config.mapping.for.onus.merchant.verticalId.174",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId174\",\"deeplink\":\"paytmmp://mini-app?aId=edc6391a24ac4eec8b2d3558d5b53195&data=$parametersBase64Hash\",\"parameters\":{\"sparams\":{\"showTitleBar\":false},\"params\":\"?utm_source=passbook\",\"path\":\"/myorders/$orderId\"}},{\"ctaType\":\"CHAT_PROFILE\",\"ctaLabelLocaleKey\":null,\"deeplink\":\"paytmmp://mini-app?aId=edc6391a24ac4eec8b2d3558d5b53195&data=$parametersBase64Hash\",\"parameters\":{\"sparams\":{\"pullRefresh\":false,\"canPullDown\":false,\"showTitleBar\":false},\"path\":\"/store/$shopId?utm_source=uth_redirect\"}}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_187(
			"txn.details.config.mapping.for.onus.merchant.verticalId.187",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId187\",\"deeplink\":\"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId\"}]",
			"PTH-1283"),

	TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_204(
			"txn.details.config.mapping.for.onus.merchant.verticalId.204",
			"[{\"ctaType\":\"VIEW_DETAILS_CTA\",\"ctaLabelLocaleKey\":\"ctaLabelLocaleKeyForVerticalId204\",\"deeplink\":\"paytmmp://product?url=https://paytmmall.com/shop/summary/$orderId#html\"}]",
			"PTH-1283"),

	// Configuration for ad details in the system
	AD_DETAILS_SENDER_INSTRUMENT_UPI_RUPAY_CC("ad.details.sender.instrument.upi.rupay.cc",
			"{\"logoUrl\":\"https://example.com/rupay.png\",\"displayText\":\"<html>Pay rent with <b>Rupay Credit Card</b> &amp; win <span style=\\\"color:green\\\">Cashback</span></html>\",\"ctaText\":\"Pay Rent with Credit Card\",\"ctaDeeplink\":\"paytmmp://referral?tag=referral_all&flow=new&group=GLOBAL&sfname=https://storefront.paytm.com/v2/h/refer-and-earn&utm_source=uth_detailpage\"}",
			"PTH-1175"),

	AD_DETAILS_P2P_OUTWARD_SENDER_INSTRUMENT_UPI_LITE("ad.details.p2p.outward.sender.instrument.upi.lite",
			"{\"logoUrl\":\"https://example.com/refer.png\",\"displayText\":\"<html>Refer &amp; win <span style=\\\"color:green\\\">Cashback</span></html>\",\"ctaText\":\"Refer and win\",\"ctaDeeplink\":\"paytmmp://referral?tag=referral_all&flow=new&group=GLOBAL&sfname=https://storefront.paytm.com/v2/h/refer-and-earn&utm_source=uth_detailpage\"}",
			"PTH-1175"),

	AD_DETAILS_P2P_INWARD_OTHER_PARTY_NON_PAYTM_VPA("ad.details.p2p.inward.other.party.non.paytm.vpa",
			"{\"logoUrl\":\"https://example.com/refer.png\",\"displayText\":\"<html>Refer &amp; win <span style=\\\"color:green\\\">Cashback</span></html>\",\"ctaText\":\"Refer and win\",\"ctaDeeplink\":\"paytmmp://referral?tag=referral_all&flow=new&group=GLOBAL&sfname=https://storefront.paytm.com/v2/h/refer-and-earn&utm_source=uth_detailpage\"}",
			"PTH-1175"),

	AD_DETAILS_P2P_OUTWARD_OTHER_PARTY_INSTRUMENT_NON_PAYTM_VPA_AND_AMOUNT_GREATER_THAN_THRESHOLD(
			"ad.details.p2p.outward.other.party.instrument.non.paytm.vpa.and.amount.greater.than.threshold",
			"{\"logoUrl\":\"https://example.com/refer.png\",\"displayText\":\"<html>Refer &amp; win <span style=\\\"color:green\\\">Cashback</span></html>\",\"ctaText\":\"Pay big monthly expenses with credit card (ex - Rent)\",\"ctaDeeplink\":\"paytmmp://referral?tag=referral_all&flow=new&group=GLOBAL&sfname=https://storefront.paytm.com/v2/h/refer-and-earn&utm_source=uth_detailpage\"}",
			"PTH-1175"),

	AD_DETAILS_P2P_OUTWARD_OTHER_PARTY_INSTRUMENT_NON_PAYTM_VPA(
			"ad.details.p2p.outward.other.party.instrument.non.paytm.vpa",
			"{\"logoUrl\":\"https://example.com/refer.png\",\"displayText\":\"<html>Refer &amp; win <span style=\\\"color:green\\\">Cashback</span></html>\",\"ctaText\":\"Refer and win\",\"ctaDeeplink\":\"paytmmp://referral?tag=referral_all&flow=new&group=GLOBAL&sfname=https://storefront.paytm.com/v2/h/refer-and-earn&utm_source=uth_detailpage\"}",
			"PTH-1175"),

	AD_DETAILS_SUPPORTED_STATUS_LIST("ad.details.supported.status.list", "[\"SUCCESS\"]", "PTH-1175"),

	AD_DETAILS_P2P_OUTWARD_TXN_AMOUNT_THRESHOLD("ad.details.p2p.outward.txn.amount.threshold", "5000.00", "PTH-1175"),

	PAYTM_ISSUER_UPI_HANDLES_LIST("paytm.issuer.upi.handles.list",
			"[\"@paytm\", \"@ptsbi\", \"@ptyes\", \"@ptaxis\", \"@pthdfc\"]", "PTH-1175"),

	OMS_WHITELISTED_PAYMENT_METHOD_LIST("oms.whitelisted.payment.method.list", "[\"UPI\",\"CC\",\"DC\",\"NB\"]",
			"PTH-928"),

	NON_TRANSACTING_USER_CACHE_EXPIRY_IN_DAYS("nonTransactingUser.cache.expiry.in.days", "15", "PTH-1376"),

	LIMIT_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST_FEATURE_ENABLED(
			"listing.limit.months.to.scan.in.single.api.request.enabled", "true", "PTH-1263"),

	MAX_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST("listing.max.months.to.scan.in.single.api.request", "3",
			"PTH-1263");

	final String key;

	final String defaultValue;

	final String jiraId;

	public static final Map<String, ConfigPropertiesEnum> keyVsPropertiesMap = new HashMap<>();

	/**
	 * Constructor to initialize the enum constants.
	 * @param key the key of the configuration property
	 * @param defaultValue the default value of the configuration property
	 */
	ConfigPropertiesEnum(String key, String defaultValue, String jiraId) {
		this.key = key;
		this.defaultValue = defaultValue;
		this.jiraId = jiraId;
	}

	static {
		// Populate the map with enum constants for quick lookup
		for (ConfigPropertiesEnum property : ConfigPropertiesEnum.values()) {
			keyVsPropertiesMap.put(property.getKey(), property);
		}
	}

	/**
	 * Returns the ConfigPropertiesEnum for the given key
	 * @param key the property key to look up
	 * @return ConfigPropertiesEnum for the key, or null if not found
	 */
	public static ConfigPropertiesEnum getConfigEnumByKey(String key) {
		return keyVsPropertiesMap.get(key);
	}

}