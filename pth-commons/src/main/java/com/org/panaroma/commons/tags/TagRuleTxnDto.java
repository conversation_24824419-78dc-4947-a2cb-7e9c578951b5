package com.org.panaroma.commons.tags;

import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.tags.utils.IntelligentTagsUtil;
import lombok.Data;

import java.time.LocalDate;

@Data
public class TagRuleTxnDto {

	private Long amount;

	private String uthCategoryId;

	private int txnType;

	private String verticalId;

	private String phonebookName;

	private String payeeVpa;

	private LocalDate currentDate;

	public static TagRuleTxnDto fromTxn(TransformedTransactionHistoryDetail txn) {
		TagRuleTxnDto transformedTxn = new TagRuleTxnDto();
		transformedTxn.setAmount(txn.getAmount() / 100);
		transformedTxn.setTxnType(txn.getMainTxnType());
		UthCategoryEnum uthCategoryEnum = IntelligentTagsUtil.getUthCategory(txn);
		transformedTxn.setUthCategoryId(uthCategoryEnum.getUthCategoryId());
		transformedTxn.setVerticalId(IntelligentTagsUtil.getVerticalId(txn));
		transformedTxn.setCurrentDate(LocalDate.now());
		return transformedTxn;
	}

}
