package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.Constants.INDIAN_ZONE;
import static com.org.panaroma.commons.constants.Constants.IST;
import static com.org.panaroma.commons.constants.WebConstants.CUSTOMER_CREATION_DATE_FORMAT;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_FROM_DATE_FORMAT;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.WEEK_MONTH_DATE_TIME_FORMAT;
import static com.org.panaroma.commons.constants.WebConstants.yyyy_MM_dd_HH_mm_ss;

import com.org.panaroma.commons.constants.Constants;
import com.org.panaroma.commons.enums.Order;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

@Log4j2
public class DateTimeUtility {

	private static DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	public static final DateFormat listingDateFormat = new SimpleDateFormat(LISTING_FROM_DATE_FORMAT);

	private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(yyyy_MM_dd_HH_mm_ss);

	private static final DateTimeFormatter listingDateTimeFormatter = DateTimeFormatter
		.ofPattern(LISTING_FROM_DATE_FORMAT);

	private static final DateFormat customerCreationDateFormat = new SimpleDateFormat(CUSTOMER_CREATION_DATE_FORMAT);

	private static final DateFormat weekMonthDayTimeFormat = new SimpleDateFormat(WEEK_MONTH_DATE_TIME_FORMAT);

	public static final String IST_ZONE_ID = "Asia/Kolkata";

	public static String getStringDate(final long date, final String dateFormat) {
		return getStringDate(date, dateFormat, TimeZone.getTimeZone(IST));
	}

	public static String getStringDate(final long date, final String dateFormat, final TimeZone timeZone) {
		if (date <= 0 || StringUtils.isBlank(dateFormat) || Objects.isNull(timeZone)) {
			log.warn("Not valid params value for parsing date date: {}, format: {}, timeZone: {}", date, dateFormat,
					timeZone);
			return null;
		}
		try {
			SimpleDateFormat df = new SimpleDateFormat(dateFormat);
			df.setTimeZone(timeZone);
			return df.format(date);
		}
		catch (Exception e) {
			log.error("Exception while getting String Date from Epoch with required input format Epoch: {}, format: {}",
					date, dateFormat);
		}
		return null;
	}

	public static String getStringDate(final String date, final String inputFormat, final String outputFormat) {
		if (StringUtils.isBlank(date) || StringUtils.isBlank(inputFormat) || StringUtils.isBlank(outputFormat)) {
			return null;
		}
		try {
			Date inputDate = new SimpleDateFormat(inputFormat).parse(date);
			SimpleDateFormat outputSf = new SimpleDateFormat(outputFormat);

			return outputSf.format(inputDate);
		}
		catch (Exception e) {
			log.error("Exception while getting string Date Date: {}, inputFormat: {}, outputFormat: {}, Exception: {}",
					date, inputFormat, outputFormat, CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public static List<String> getMonthsListBetweenTwoEpochs(final String passedFromDateEpoch,
			final String passedToDateEpoch, final String resultantMonthFormat, final Order order) {
		List<String> monthList = getMonthsListBetweenTwoEpochs(passedFromDateEpoch, passedToDateEpoch,
				resultantMonthFormat);
		if (Order.DESC.equals(order) && Objects.nonNull(monthList)) {
			Collections.reverse(monthList);
		}
		return monthList;
	}

	public static List<String> getMonthsListBetweenTwoEpochs(final String passedFromDateEpoch,
			final String passedToDateEpoch, final String resultantMonthFormat) {
		if (Boolean.FALSE.equals(NumberFormatUtility.isLongParsable(passedFromDateEpoch)
				&& NumberFormatUtility.isLongParsable(passedToDateEpoch))) {
			return null;
		}

		long fromDateEpoch = Long.parseLong(passedFromDateEpoch);
		long toDateEpoch = Long.parseLong(passedToDateEpoch);

		return getMonthsListBetweenTwoEpochs(fromDateEpoch, toDateEpoch, resultantMonthFormat);
	}

	public static List<String> getMonthsListBetweenTwoEpochs(final long passedFromDateEpoch,
			final long passedToDateEpoch, final String resultantMonthFormat) {
		long fromDateEpoch = passedFromDateEpoch;
		long toDateEpoch = passedToDateEpoch;

		if (StringUtils.isBlank(resultantMonthFormat) || !isValidEpoch(passedFromDateEpoch)
				|| !isValidEpoch(passedToDateEpoch)) {
			return null;
		}

		try {
			DateFormat dateFormat = new SimpleDateFormat(resultantMonthFormat);
			List<String> monthList = new ArrayList<>();

			Calendar fromCalendar = Calendar.getInstance();
			fromCalendar.setTimeInMillis(fromDateEpoch);

			fromCalendar.set(Calendar.DAY_OF_MONTH, fromCalendar.getActualMinimum(Calendar.DAY_OF_MONTH));
			fromCalendar.set(Calendar.HOUR_OF_DAY, fromCalendar.getActualMinimum(Calendar.HOUR_OF_DAY));
			fromCalendar.set(Calendar.MINUTE, fromCalendar.getActualMinimum(Calendar.MINUTE));
			fromCalendar.set(Calendar.SECOND, fromCalendar.getActualMinimum(Calendar.SECOND));
			fromCalendar.set(Calendar.MILLISECOND, fromCalendar.getActualMinimum(Calendar.MILLISECOND));

			Calendar toCalender = Calendar.getInstance();
			toCalender.setTimeInMillis(toDateEpoch);

			while (fromCalendar.getTime().getTime() <= toCalender.getTime().getTime()) {
				String month = dateFormat.format(fromCalendar.getTime());
				monthList.add(month);
				fromCalendar.add(Calendar.MONTH, 1);
			}
			return monthList;
		}
		catch (Exception e) {
			log.warn("Exception while getting month list b/w 2 Epochs Exception: {}",
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	private static boolean isValidEpoch(final long epoch) {
		boolean isValidEpoch = false;
		if (epoch >= 0L) {
			isValidEpoch = true;
		}
		return isValidEpoch;
	}

	public static String getPreviousDayDate() {
		return dateFormat.format(yesterday());
	}

	public static Date yesterday() {
		final Calendar cal = Calendar.getInstance();
		cal.setTimeZone(TimeZone.getTimeZone(IST));

		cal.add(Calendar.DATE, -1);
		cal.set(Calendar.HOUR, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		return cal.getTime();
	}

	public static long getstartDateInEpochYesterday() {
		return LocalDate.now().minusDays(1).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
	}

	public static long getEndDateInEpochYesterday() {
		return LocalDate.now()
			.minusDays(1)
			.atTime(23, 59, 59)
			.atZone(ZoneId.systemDefault())
			.toInstant()
			.toEpochMilli();
	}

	public static Long getEpochMillis(final String date, final String dateFormat) {
		return getEpochMillis(date, dateFormat, TimeZone.getTimeZone(IST));
	}

	public static Long getEpochMillis(final String date, final String dateFormat, final TimeZone timeZone) {
		if (StringUtils.isBlank(date) || StringUtils.isBlank(dateFormat) || Objects.isNull(timeZone)) {
			return null;
		}
		try {
			SimpleDateFormat df = new SimpleDateFormat(dateFormat);
			df.setTimeZone(timeZone);
			return df.parse(date).getTime();
		}
		catch (ParseException ex) {
			log.error("Exception while getting Epoch from Date date: {}, format: {}, timezone: {} exception: {}", date,
					dateFormat, timeZone, CommonsUtility.exceptionFormatter(ex));
		}
		return null;
	}

	public static boolean isDateValid(final String dateStr, final String expectedFormat) {
		if (StringUtils.isBlank(dateStr) || StringUtils.isBlank(expectedFormat)) {
			return false;
		}

		try {
			DateFormat dateFormat = new SimpleDateFormat(expectedFormat);
			dateFormat.setLenient(false);
			dateFormat.setTimeZone(TimeZone.getTimeZone(IST));
			dateFormat.parse(dateStr);

			return true;
		}
		catch (ParseException ex) {
			return false;
		}
	}

	// Epoch of 1st day of month at 00:00:00:000
	public static Long getMonthStartEpoch(final Long monthEpoch) {
		if (Objects.isNull(monthEpoch)) {
			return null;
		}

		Calendar calendar = Calendar.getInstance();
		calendar.setTimeZone(TimeZone.getTimeZone(IST));
		calendar.setTimeInMillis(monthEpoch);

		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMinimum(Calendar.HOUR_OF_DAY));
		calendar.set(Calendar.MINUTE, calendar.getActualMinimum(Calendar.MINUTE));
		calendar.set(Calendar.SECOND, calendar.getActualMinimum(Calendar.SECOND));
		calendar.set(Calendar.MILLISECOND, calendar.getActualMinimum(Calendar.MILLISECOND));

		return calendar.getTimeInMillis();
	}

	// Epoch of last day of month at 23:59:59:999
	public static Long getMonthEndEpoch(final Long monthEpoch) {
		if (Objects.isNull(monthEpoch)) {
			return null;
		}

		Calendar calendar = Calendar.getInstance();
		calendar.setTimeZone(TimeZone.getTimeZone(IST));
		calendar.setTimeInMillis(monthEpoch);

		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMaximum(Calendar.HOUR_OF_DAY));
		calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
		calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
		calendar.set(Calendar.MILLISECOND, calendar.getActualMaximum(Calendar.MILLISECOND));

		return calendar.getTimeInMillis();
	}

	public static String getPreviousMonth(final String date, final String monthFormat) {
		if (StringUtils.isBlank(monthFormat)) {
			return null;
		}
		try {
			long epoch = DateTimeUtility.getEpochMillis(date, monthFormat);
			Calendar cal = Calendar.getInstance();
			cal.setTimeZone(TimeZone.getTimeZone(IST));
			cal.setTimeInMillis(epoch);

			cal.add(Calendar.DATE, 0);
			cal.set(Calendar.HOUR, 0);
			cal.set(Calendar.MINUTE, 0);
			cal.set(Calendar.SECOND, 0);
			cal.add(Calendar.MONTH, -1);

			DateFormat dateFormat = new SimpleDateFormat(monthFormat);
			return dateFormat.format(cal.getTime());
		}
		catch (Exception e) {
			log.error("Exception while getting previous month date: {}, Exception: {}", date,
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public static Long currentTimeEpoch() {
		Calendar cal = Calendar.getInstance();
		cal.setTimeZone(TimeZone.getTimeZone(IST));
		return cal.getTime().getTime();
	}

	public static Long getPreviousDayEndDate() {
		LocalDateTime localDateTime = LocalDateTime.now().minusDays(1).withHour(23).withMinute(59).withSecond(59);
		Date testDate = Date.from(localDateTime.toInstant(ZoneOffset.UTC));
		return testDate.getTime();
	}

	public static Long getPreviousDayStartDate() {
		LocalDateTime localDateTime = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0);
		Date testDate = Date.from(localDateTime.toInstant(ZoneOffset.UTC));
		return testDate.getTime();
	}

	public static String getCurrentDate() {
		final Calendar cal = Calendar.getInstance();
		cal.setTimeZone(TimeZone.getTimeZone(IST));
		return dateFormat.format(cal.getTime());
	}

	public static Long getFromDatexMonthOldFromNow(final int month) {
		LocalDate currentDate = LocalDate.now();
		LocalDate date = currentDate.minusMonths(month);
		Instant instant = date.atStartOfDay(ZoneId.systemDefault()).toInstant();
		return instant.toEpochMilli();
	}

	public static boolean isDateWithinRangeOfxMonth(final long txnDate, final int month) {

		// date x month before
		LocalDate currentDate = LocalDate.now();
		LocalDate currentDateMinusXsMonths = currentDate.minusMonths(month);

		// transaction date of the user
		Instant instant = Instant.ofEpochMilli(txnDate);
		ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());

		// ZonedDateTime to LocalDate
		LocalDate dateOfTxn = zonedDateTime.toLocalDate();

		// if txn is older return false
		// else return true
		return !dateOfTxn.isBefore(currentDateMinusXsMonths);
	}

	public static long getTimeFromStringFormattedDate(final String time) throws ParseException {
		return listingDateFormat.parse(time).getTime();
	}

	public static long getCustomerCreationDateFormattedDate(final String customerCreationDate) throws ParseException {
		Date date = customerCreationDateFormat.parse(customerCreationDate);
		return getTimeFromStringFormattedDate(listingDateFormat.format(date));
	}

	public static Calendar getFirstDateOfNthPreviousMonth(final int beforeMonth) {
		Calendar now = Calendar.getInstance();
		now.setTimeZone(TimeZone.getTimeZone(Constants.IST));
		// First second of the first day
		now.set(Calendar.HOUR, 0);
		now.set(Calendar.MINUTE, 0);
		now.set(Calendar.SECOND, 0);
		now.set(Calendar.MILLISECOND, 0);
		now.set(Calendar.HOUR_OF_DAY, 0);
		now.add(Calendar.MONTH, -beforeMonth);
		now.set(Calendar.DAY_OF_MONTH, 1);
		return now;
	}

	public static Date getDateTime(final String date) {
		if (StringUtils.isEmpty(date)) {
			return null;
		}

		try {
			LocalDateTime localDateTime = LocalDateTime.parse(date, dateTimeFormatter);
			Date dateTime = null;
			if (localDateTime != null) {
				Timestamp timestamp = Timestamp.valueOf(localDateTime.format(dateTimeFormatter));
				dateTime = new Date(timestamp.getTime());
			}
			return dateTime;
		}
		catch (DateTimeParseException parseException) {
			log.error("LocalDateTime  parsing exception date :{}, exception:{}", date,
					CommonsUtility.exceptionFormatter(parseException));
		}
		catch (Exception ex) {
			log.error("LocalDateTime exception. date :{} , exception: {}", date, CommonsUtility.exceptionFormatter(ex));
		}
		return null;
	}

	public static Long getMillisForDays(final int days) {
		return days * 24 * 60 * 60 * 1000L;
	}

	/**
	 * Return Duration between two epochMillis values.
	 */
	public static long getDurationBetweenTwoEpochMillisInDays(final long startTime, final long endTime) {
		Instant startInstant = Instant.ofEpochMilli(startTime);
		Instant entInstant = Instant.ofEpochMilli(endTime);
		return Duration.between(startInstant, entInstant).toDays();
	}

	/**
	 * get start day time
	 */
	public static Long getTodayDayStartDate() {
		LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.now(), ZoneId.of(IST_ZONE_ID));
		LocalDateTime startOfDay = localDateTime.toLocalDate().atStartOfDay();
		return startOfDay.atZone(ZoneId.of(IST_ZONE_ID)).toInstant().toEpochMilli();
	}

	/**
	 * getstart day of given epoch
	 */
	public static Long getStartDayEpoch(final Long epochMillis) {
		if (Objects.isNull(epochMillis)) {
			return null;
		}

		LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMillis),
				ZoneId.of(IST_ZONE_ID));
		LocalDateTime startOfDay = localDateTime.toLocalDate().atStartOfDay();
		return startOfDay.atZone(ZoneId.of(IST_ZONE_ID)).toInstant().toEpochMilli();
	}

	/**
	 * getstart day of given epoch
	 */
	public static Long getEndDayEpoch(final Long epochMillis) {
		if (Objects.isNull(epochMillis)) {
			return null;
		}

		LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMillis),
				ZoneId.of(IST_ZONE_ID));
		LocalDateTime endOfDay = localDateTime.toLocalDate().atTime(23, 59, 59);
		return endOfDay.atZone(ZoneId.of(IST_ZONE_ID)).toInstant().toEpochMilli();
	}

	/**
	 * Return Duration between two epochMillis values.
	 */
	public static Duration getDurationBetweenTwoEpochMillis(final long startTime, final long endTime) {
		Instant startInstant = Instant.ofEpochMilli(startTime);
		Instant entInstant = Instant.ofEpochMilli(endTime);
		return Duration.between(startInstant, entInstant);
	}

	public static String convertEpochMillisToString(final Long epochMillis) {
		if (Objects.isNull(epochMillis)) {
			return null;
		}
		Instant instant = Instant.ofEpochMilli(epochMillis);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy", Locale.ENGLISH)
			.withZone(ZoneId.of(IST_ZONE_ID));
		return formatter.format(instant);
	}

	public static boolean isFirstDateLatest(final String firstDateString, final String secondDateString)
			throws ParseException {
		// [PTH-1149] Defensive check: If either date string is blank or null, we cannot
		// compare dates. Log the issue and return false to avoid NumberFormatException or
		// ParseException.
		if (StringUtils.isBlank(firstDateString) || StringUtils.isBlank(secondDateString)) {
			log.warn(
					"isFirstDateLatest: One or both date strings are blank. firstDateString='{}', secondDateString='{}'",
					firstDateString, secondDateString);
			return false;
		}
		Date firstDate = listingDateFormat.parse(firstDateString);
		Date secondDate = listingDateFormat.parse(secondDateString);
		return firstDate.getTime() > secondDate.getTime();
	}

	public static Long getEpochMillisForWeekMonthDayTimeFormat(final String date) {
		if (StringUtils.isBlank(date)) {
			return null;
		}
		try {
			return weekMonthDayTimeFormat.parse(date).getTime();
		}
		catch (ParseException ex) {
			log.error("Exception while getting Epoch from Date date: {}, format: {}, exception: {}", date,
					weekMonthDayTimeFormat, CommonsUtility.exceptionFormatter(ex));
		}
		return null;
	}

	/**
	 * Subtracts the specified number of days from the current day's start time and
	 * returns the result in the LISTING_FROM_DATE_FORMAT.
	 *
	 * This method uses the current day's start time, subtracts the given number of days,
	 * and formats the result according to the pattern defined by
	 * LISTING_FROM_DATE_FORMAT.
	 * @param days the number of days to subtract from the current day's start time
	 * @return a string representing the new date and time after subtracting the specified
	 * number of days, formatted according to LISTING_FROM_DATE_FORMAT
	 *
	 * Example: If the current date is "2024-11-19 00:00:00.000 +0530" and
	 * LISTING_FROM_DATE_FORMAT is "yyyy-MM-dd HH:mm:ss.SSS Z", calling
	 * subtractDaysToListingFormattedDate(30) will return "2024-10-20 00:00:00.000 +0530".
	 */
	public static String subtractDaysToListingFormattedDateFromNow(final int days) {
		// Get start of the current day.
		ZonedDateTime now = ZonedDateTime.now(ZoneId.of(INDIAN_ZONE))
			.toLocalDate()
			.atStartOfDay((ZoneId.of(INDIAN_ZONE)));
		ZonedDateTime subtractedZonedTimeDate = now.minusDays(days);
		return subtractedZonedTimeDate.format(listingDateTimeFormatter);
	}

	/**
	 * This method returns the epoch time in milliseconds for a date that is a specified
	 * number of days older from the current date.
	 * @param days the number of days to subtract from the current date
	 * @return the epoch time in milliseconds for the date that is 'days' days older from
	 * now
	 */
	public static Long getDateNDaysOlderFromNow(final int days) {
		LocalDateTime localDateTime = LocalDateTime.now().minusDays(days);
		return localDateTime.atZone(ZoneId.of(IST_ZONE_ID)).toInstant().toEpochMilli();
	}

	/**
	 * Get the current date in IST zone
	 */
	public static Date getCurrentDateInIST() {
		return Date.from(LocalDateTime.now(ZoneId.of(IST_ZONE_ID)).atZone(ZoneId.of(IST_ZONE_ID)).toInstant());
	}

	public static long getEpochMillisOfFirstDateOfMonth(int daysDiff) {
		LocalDate currentDate = LocalDate.now(ZoneId.of("Asia/Kolkata"));
		LocalDateTime fromDate = currentDate.minusDays(daysDiff).withDayOfMonth(1).atStartOfDay();
		return fromDate.atZone(ZoneId.of("Asia/Kolkata")).toInstant().toEpochMilli();
	}

	public static long getEpochOfBeginningOfNthPreviousMonthFromGivenTime(final int n,
			final long givenDateInEpochMillis) {
		ZonedDateTime givenDate = Instant.ofEpochMilli(givenDateInEpochMillis).atZone(ZoneId.of(INDIAN_ZONE));
		ZonedDateTime zonedDateTime = givenDate.minusMonths(n)
			.withDayOfMonth(1)
			.withHour(0)
			.withMinute(0)
			.withSecond(0)
			.withNano(0);
		return Date.from(zonedDateTime.toInstant()).getTime();
	}

}
