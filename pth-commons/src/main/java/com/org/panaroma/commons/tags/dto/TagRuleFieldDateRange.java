package com.org.panaroma.commons.tags.dto;

import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.tags.TagRuleTxnDto;
import com.org.panaroma.commons.utils.CommonsUtility;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Objects;

@AllArgsConstructor
@Slf4j
@ToString
public class TagRuleFieldDateRange implements TagRuleField {

	private final LocalDate startDate;

	private final LocalDate endDate;

	private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

	@Override
	public boolean isValidRule(TagRuleTxnDto tagRuleDto) {
		return !tagRuleDto.getCurrentDate().isBefore(startDate) && !tagRuleDto.getCurrentDate().isAfter(endDate);
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		TagRuleFieldDateRange that = (TagRuleFieldDateRange) o;
		return Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate);
	}

	@Override
	public int hashCode() {
		return Objects.hash(startDate, endDate);
	}

	public static TagRuleFieldDateRange of(TagConfigurationEntity entity) {
		if (entity == null || StringUtils.isBlank(entity.getDateRange())) {
			return null;
		}
		String dbFormatDate = entity.getDateRange().trim();
		if (StringUtils.isBlank(dbFormatDate)) {
			return null;
		}
		try {
			String[] parts = dbFormatDate.split("/");
			if (parts.length == 2) {
				LocalDate startDate = LocalDate.parse(parts[0], formatter);
				LocalDate endDate = LocalDate.parse(parts[1], formatter);
				return new TagRuleFieldDateRange(startDate, endDate);
			}
			else {
				throw new DateTimeParseException("Invalid date range format. Expected format: dd-MM-yyyy/dd-MM-yyyy",
						dbFormatDate, 0);
			}
		}
		catch (DateTimeParseException e) {
			log.error("Error parsing date: {}, {}", entity, CommonsUtility.exceptionFormatter(e));
			return null;
		}
	}

}
