package com.org.panaroma.commons.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import java.util.HashMap;
import java.util.Map;

/* Enum to hold the locale key and default message for the locale key
   This enum is used to get the localized value for the given localeMsgConstants */
@Log4j2
@Getter
@AllArgsConstructor
public enum LocaleMsgConstants {

	HIDE_TXN_SUCCESS_MSG("HIDE_TXN_SUCCESS", "Payment hidden successfully."),

	UNHIDE_TXN_SUCCESS_MSG("UNHIDE_TXN_SUCCESS", "Payment unhidden successfully."),

	HIDE_TXN_INVALID_REQUEST_ERROR_MSG("HIDE_TXN_INVALID_REQUEST", "Sorry, you cannot hide this payment."),

	UNHIDE_TXN_INVALID_REQUEST_ERROR_MSG("UNHIDE_TXN_INVALID_REQUEST", "Sorry, you cannot unhide this payment."),

	HIDE_TXN_ES_EXP_ERROR_MSG("HIDE_TXN_ES_EXP",
			"Sorry, we experienced an error in hiding this payment. Please try again."),

	UNHIDE_TXN_ES_EXP_ERROR_MSG("UNHIDE_TXN_ES_EXP",
			"Sorry, we experienced an error in unhiding this payment. Please try again."),

	HIDE_TXN_INVALID_PARAMETER_ERROR_MSG("HIDE_TXN_INVALID_PARAMETER",
			"Sorry, we experienced an error in hiding this payment. Please try again."),

	UNHIDE_TXN_INVALID_PARAMETER_ERROR_MSG("UNHIDE_TXN_INVALID_PARAMETER",
			"Sorry, we experienced an error in unhiding this payment. Please try again."),

	HIDE_TXN_NO_TXN_FOUND_ERROR_MSG("HIDE_TXN_NO_TXN_FOUND",
			"Sorry, we experienced an error in hiding this payment. Please try again."),

	UNHIDE_TXN_NO_TXN_FOUND_ERROR_MSG("UNHIDE_TXN_NO_TXN_FOUND",
			"Sorry, we experienced an error in unhiding this payment. Please try again."),

	HIDE_TXN_KAFKA_ERROR_MSG("HIDE_TXN_KAFKA_ERROR",
			"Sorry, we experienced an error in hiding this payment. Please try again."),

	UNHIDE_TXN_KAFKA_ERROR_MSG("UNHIDE_TXN_KAFKA_ERROR",
			"Sorry, we experienced an error in unhiding this payment. Please try again."),

	HIDE_TXN_INVALID_TXNDATE_RANGE_ERROR_MSG("HIDE_TXN_INVALID_TXNDATE_RANGE",
			"Sorry, you can only hide payments made within the last HIDE_MAX_DAYS days."),

	UNHIDE_TXN_INVALID_TXNDATE_RANGE_ERROR_MSG("UNHIDE_TXN_INVALID_TXNDATE_RANGE",
			"Sorry, you can only unhide payments made within the last HIDE_MAX_DAYS days."),

	TXN_DESCRIPTION_FOR_CONVENIENCE_FEE("TXN_DESCRIPTION_FOR_CONVENIENCE_FEE",
			"Convenience fee charged by MERCHANT_NAME"),

	AMOUNT_SPLIT_DETAIL_HEADER("AMOUNT_SPLIT_DETAIL_HEADER", "Charges Detail"),

	AMOUNT_SPLIT_DETAIL_BASE_AMOUNT_LABEL("AMOUNT_SPLIT_DETAIL_BASE_AMOUNT_LABEL", "Base Amount"),

	AMOUNT_SPLIT_DETAIL_CONVENIENCE_FEE_LABEL("AMOUNT_SPLIT_DETAIL_CONVENIENCE_FEE_LABEL", "Convenience Fee"),

	AMOUNT_SPLIT_DETAIL_TOTAL_AMOUNT_LABEL("AMOUNT_SPLIT_DETAIL_TOTAL_AMOUNT_LABEL", "Total Amount"),

	TAG_SUMMARY_SPENT_TITLE("TAG_SUMMARY_SPENT_TITLE", "Money Paid"),
	TAG_SUMMARY_RECEIVED_TITLE("TAG_SUMMARY_RECEIVED_TITLE", "Money Received"),

	DETAIL_NARRATION_P2P_OUTWARD_FAILURE("detail.narration.p2p.outward.failure", "Transfer Failed"),
	DETAIL_NARRATION_P2P_OUTWARD_SUCCESS("detail.narration.p2p.outward.success", "Sent Successfully"),
	DETAIL_NARRATION_P2P_OUTWARD_PENDING("detail.narration.p2p.outward.pending", "Transfer Pending"),

	DETAIL_NARRATION_SELF_TRANSFER_SUCCESS("detail.narration.self.transfer.success", "Transferred Successfully"),

	DETAIL_NARRATION_P2P_INWARD_SUCCESS("detail.narration.p2p.inward.success", "Money Received"),

	DETAIL_NARRATION_P2P_INWARD_3P_APP_SUCCESS("detail.narration.p2p.inward.3p.app.success", "Money Received"),

	DETAIL_NARRATION_CASHBACK_RECEIVED("detail.narration.cashback.received", "Cashback Received"),

	DETAIL_NARRATION_P2M_FAILURE("detail.narration.p2m.failure", "Payment Failed"),
	DETAIL_NARRATION_P2M_SUCCESS("detail.narration.p2m.success", "Paid Successfully"),
	DETAIL_NARRATION_P2M_PENDING("detail.narration.p2m.pending", "Payment Pending"),

	DETAIL_NARRATION_P2M_REFUND_FAILURE("detail.narration.p2m.refund.failure", "Refund Failed"),
	DETAIL_NARRATION_P2M_REFUND_SUCCESS("detail.narration.p2m.refund.success", "Money Refunded"),
	DETAIL_NARRATION_P2M_REFUND_PENDING("detail.narration.p2m.refund.pending", "Refund pending"),

	DETAIL_NARRATION_UPI_LITE_ADD_MONEY_FAILURE("detail.narration.upi.lite.add.money.failure", "Add Money Failed"),
	DETAIL_NARRATION_UPI_LITE_ADD_MONEY_SUCCESS("detail.narration.upi.lite.add.money.success", "Money Added"),
	DETAIL_NARRATION_UPI_LITE_ADD_MONEY_PENDING("detail.narration.upi.lite.add.money.pending", "Add Money Pending"),

	DETAIL_NARRATION_UPI_LITE_ACTIVATION_FAILURE("detail.narration.upi.lite.activation.failure", "Activation Failed"),
	DETAIL_NARRATION_UPI_LITE_ACTIVATION_SUCCESS("detail.narration.upi.lite.activation.success", "UPI Lite Activated"),
	DETAIL_NARRATION_UPI_LITE_ACTIVATION_PENDING("detail.narration.upi.lite.activation.pending", "Activation Pending"),

	DETAIL_NARRATION_UPI_LITE_DEACTIVATION_FAILURE("detail.narration.upi.lite.deactivation.failure",
			"Deactivation Failed"),
	DETAIL_NARRATION_UPI_LITE_DEACTIVATION_SUCCESS("detail.narration.upi.lite.deactivation.success",
			"UPI Lite Deactivated"),
	DETAIL_NARRATION_UPI_LITE_DEACTIVATION_PENDING("detail.narration.upi.lite.deactivation.pending",
			"Deactivation Pending"),

	DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_FAILURE("detail.narration.recurring.mandate.execution.failure",
			"Payment Failed"),
	DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_SUCCESS("detail.narration.recurring.mandate.execution.success",
			"Paid Successfully"),
	DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_PENDING("detail.narration.recurring.mandate.execution.pending",
			"Payment Pending"),

	DETAIL_NARRATION_RECURRING_MANDATE_SETUP_FAILURE("detail.narration.recurring.mandate.setup.failure",
			"Setup Failed"),
	DETAIL_NARRATION_RECURRING_MANDATE_SETUP_SUCCESS("detail.narration.recurring.mandate.setup.success",
			"Setup Successfully"),
	DETAIL_NARRATION_RECURRING_MANDATE_SETUP_PENDING("detail.narration.recurring.mandate.setup.pending",
			"Setup Pending"),

	DETAIL_NARRATION_IPO_MANDATE_CREATE_OR_UPDATE_SUCCESS("detail.narration.ipo.mandate.create.or.update.success",
			"Money Blocked"),

	DETAIL_NARRATION_IPO_MANDATE_REVOKE_SUCCESS("detail.narration.ipo.mandate.revoke.success", "Money Unblocked"),

	DETAIL_NARRATION_IPO_MANDATE_PAYMENT_FAILURE("detail.narration.ipo.mandate.payment.failure", "Payment Failed"),
	DETAIL_NARRATION_IPO_MANDATE_PAYMENT_SUCCESS("detail.narration.ipo.mandate.payment.success", "Paid Successfully"),
	DETAIL_NARRATION_IPO_MANDATE_PAYMENT_PENDING("detail.narration.ipo.mandate.payment.pending", "Payment Pending"),

	DETAIL_NARRATION_ONE_TIME_MANDATE_EXECUTION_FAILURE("detail.narration.one.time.mandate.execution.failure",
			"Payment Failed"),
	DETAIL_NARRATION_ONE_TIME_MANDATE_EXECUTION_SUCCESS("detail.narration.one.time.mandate.execution.success",
			"Paid Successfully"),
	DETAIL_NARRATION_ONE_TIME_MANDATE_EXECUTION_PENDING("detail.narration.one.time.mandate.execution.pending",
			"Payment Pending"),

	DETAIL_NARRATION_ONE_TIME_MANDATE_SETUP_FAILURE("detail.narration.one.time.mandate.setup.failure", "Block Failed"),
	DETAIL_NARRATION_ONE_TIME_MANDATE_SETUP_SUCCESS("detail.narration.one.time.mandate.setup.success", "Money Blocked"),
	DETAIL_NARRATION_ONE_TIME_MANDATE_SETUP_PENDING("detail.narration.one.time.mandate.setup.pending", "Block Pending"),

	DETAIL_NARRATION_ONE_TIME_MANDATE_REVOKE_SUCCESS("detail.narration.one.time.mandate.revoke.success",
			"Money Unblocked"),

	DETAIL_NARRATION_SBMD_MANDATE_EXECUTION_FAILURE("detail.narration.sbmd.mandate.execution.failure",
			"Payment Failed"),
	DETAIL_NARRATION_SBMD_MANDATE_EXECUTION_SUCCESS("detail.narration.sbmd.mandate.execution.success",
			"Paid Successfully"),
	DETAIL_NARRATION_SBMD_MANDATE_EXECUTION_PENDING("detail.narration.sbmd.mandate.execution.pending",
			"Payment Pending"),

	DETAIL_NARRATION_SBMD_MANDATE_SETUP_FAILURE("detail.narration.sbmd.mandate.setup.failure", "Block Failed"),
	DETAIL_NARRATION_SBMD_MANDATE_SETUP_SUCCESS("detail.narration.sbmd.mandate.setup.success", "Money Blocked"),
	DETAIL_NARRATION_SBMD_MANDATE_SETUP_PENDING("detail.narration.sbmd.mandate.setup.pending", "Block Pending"),

	DETAIL_NARRATION_SBMD_MANDATE_REVOKE_SUCCESS("detail.narration.sbmd.mandate.revoke.success", "Money Unblocked"),

	DETAIL_INSTRUMENT_NAME_GIFT_CARD("detail.instrument.name.gift.card", "Gift Card");

	private final String localeMsgKey;

	private final String defaultMsgValue;

	// Map to hold the localeMsgKey and the corresponding LocaleMsgConstants.
	private static final Map<String, LocaleMsgConstants> LOCALE_KEY_TO_ENUM_MAP = new HashMap<>();

	// Static initializer block to populate the LOCALE_KEY_TO_ENUM_MAP.
	static {
		for (LocaleMsgConstants constant : values()) {
			LOCALE_KEY_TO_ENUM_MAP.put(constant.getLocaleMsgKey(), constant);
		}
	}

	// This method is used to get the localeMsgConstants for the given localeMsgKey.
	public static LocaleMsgConstants getLocaleMsgConstants(final String localeMsgKey) {
		LocaleMsgConstants localeMsgConstant = LOCALE_KEY_TO_ENUM_MAP.get(localeMsgKey);
		if (localeMsgConstant == null) {
			log.error("LocaleMsgConstants not found for localeMsgKey : {}", localeMsgKey);
		}
		return localeMsgConstant;
	}

}