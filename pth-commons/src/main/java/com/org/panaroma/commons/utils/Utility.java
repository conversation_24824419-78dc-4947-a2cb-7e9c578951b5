package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.BankDataConstants.DATE_FORMAT;
import static com.org.panaroma.commons.constants.BankDataConstants.IMPS_INWARD_RPT_CODES;
import static com.org.panaroma.commons.constants.BankDataConstants.IMPS_OUTWARD_RPT_CODES;
import static com.org.panaroma.commons.constants.BankDataConstants.NEFT_INWARD_RPT_CODES;
import static com.org.panaroma.commons.constants.BankDataConstants.NEFT_OUTWARD_RPT_CODES;
import static com.org.panaroma.commons.constants.BankDataConstants.PAYTM_BANK_IFSC;
import static com.org.panaroma.commons.constants.BankDataConstants.PPBL_IFSCCODE_IDENTIFIER;
import static com.org.panaroma.commons.constants.BankDataConstants.PPBL_TS_IMPS_REPORT_CODES;
import static com.org.panaroma.commons.constants.BankDataConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.BankDataConstants.UPDATED_COLUMN_FROM_GGTS;
import static com.org.panaroma.commons.constants.BankDataConstants.XFER_RPT_CODES;
import static com.org.panaroma.commons.constants.CartConstants.GV;
import static com.org.panaroma.commons.constants.CartConstants.ONLINE_DEALS;
import static com.org.panaroma.commons.constants.CommonConstants.ADDNPAY_REFUND_BACK_TO_SOURCE;
import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.CommonConstants.FIR_PURPOSE_CODE;
import static com.org.panaroma.commons.constants.CommonConstants.IGNORED_PARTICIPANT;
import static com.org.panaroma.commons.constants.CommonConstants.IS_ADDED_PARTICIPANT_BY_MERGING;
import static com.org.panaroma.commons.constants.CommonConstants.IS_ADD_N_PAY_REFUND;
import static com.org.panaroma.commons.constants.CommonConstants.IS_FOR_GROUPING;
import static com.org.panaroma.commons.constants.CommonConstants.IS_WALLET_INETROP_TXN;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.CommonConstants.TRUE;
import static com.org.panaroma.commons.constants.CommonConstants.WALLET_INTEROP_TXN_TYPE_LIST;
import static com.org.panaroma.commons.constants.CommonsConstants.SPACE;
import static com.org.panaroma.commons.constants.Constants.BENEFICIARY_NAME;
import static com.org.panaroma.commons.constants.Constants.IMPS;
import static com.org.panaroma.commons.constants.Constants.IS_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.Constants.PIPE_SYMBOL;
import static com.org.panaroma.commons.constants.Constants.REMITTER_NAME;
import static com.org.panaroma.commons.constants.Constants.UNDERSCORE;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.POSTPAID_REPAYMENT_MERCHANT_IDS;
import static com.org.panaroma.commons.constants.WebConstants.BANK_TXN_ID;
import static com.org.panaroma.commons.constants.WebConstants.COMMA;
import static com.org.panaroma.commons.constants.WebConstants.GV_PURCHASED;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.LAN;
import static com.org.panaroma.commons.constants.WebConstants.NOTES_KEY;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PASSBOOK_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_HANDLE;
import static com.org.panaroma.commons.constants.WebConstants.PURPOSE_CODE;
import static com.org.panaroma.commons.constants.WebConstants.RMT_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_PURPOSE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;
import static com.org.panaroma.commons.constants.WebConstants.UPI_TXN_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.UPI_VIA_CC_FLAG;
import static com.org.panaroma.commons.constants.WebConstants.VPA2VPA;
import static com.org.panaroma.commons.constants.WebConstants.VPA_HANDLE_FIELD_NAME;
import static com.org.panaroma.commons.constants.WebConstants.VPA_SPLITTER;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_PASSBOOK_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_TYPE_FILTER;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.ADD_MONEY_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2M_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_INWARD;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_OUTWARD;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INVALID_EPOCH;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INVALID_PARAM_SEARCH_AFTER;
import static java.time.temporal.ChronoUnit.DAYS;
import static java.time.temporal.ChronoUnit.SECONDS;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.CharMatcher;
import com.google.gson.Gson;
import com.org.panaroma.commons.config.ConfigurableConstants;
import com.org.panaroma.commons.config.rollout.RolloutStrategy;
import com.org.panaroma.commons.config.rollout.WhiteListingConfig;
import com.org.panaroma.commons.constants.BankDataConstants;
import com.org.panaroma.commons.constants.Constants;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.CardType;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.DateTimeEnum;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.MerchantData;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.PostStitchData;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.commons.dto.cart.CartDetails;
import com.org.panaroma.commons.dto.cart.CartItems;
import com.org.panaroma.commons.dto.cart.CartMetaData;
import com.org.panaroma.commons.dto.cart.CartRespDetails;
import com.org.panaroma.commons.dto.cart.CartRespItems;
import com.org.panaroma.commons.dto.es.TransformedGeoLocation;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.exceptionhandler.ExceptionBuilder;
import com.org.panaroma.commons.utils.rollout.strategy.IRolloutStrategyHelper;
import com.org.panaroma.commons.dto.FwdTxnDetails;
import com.org.panaroma.commons.dto.es.ParentDetails;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Log4j2
@Component
public class Utility implements Serializable {

	private static final List<TransactionTypeEnum> TXN_TYPE_SUPPORTED = Arrays.asList(TransactionTypeEnum.P2P_OUTWARD,
			TransactionTypeEnum.P2P_INWARD, TransactionTypeEnum.P2P2M, TransactionTypeEnum.P2P2M_REFUND,
			TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD, TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD,
			TransactionTypeEnum.PPBL_TRANSACTION, TransactionTypeEnum.P2P2M_INWARD, TransactionTypeEnum.P2P2M_OUTWARD);

	private static final List<String> IMPS_REPORT_CODES = Stream
		.of(BankDataConstants.IMPS_INWARD_RPT_CODES, BankDataConstants.IMPS_OUTWARD_RPT_CODES)
		.flatMap(Collection::stream)
		.collect(Collectors.toList());

	private static final List<String> XFER_REPORT_CODES = BankDataConstants.XFER_RPT_CODES;

	private static final List<String> BANK_REPORT_CODES = Stream.of(IMPS_REPORT_CODES, XFER_REPORT_CODES)
		.flatMap(Collection::stream)
		.collect(Collectors.toList());

	private static final List<Integer> MANDATE_TXN_TYPE = Arrays.asList(
			TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey(),
			TransactionTypeEnum.ONE_TIME_MANDATE.getTransactionTypeKey(),
			TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey(),
			TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey(),
			TransactionTypeEnum.SBMD_MANDATE.getTransactionTypeKey());

	private static final List<TransactionTypeEnum> MANDATE_TXN_TYPE_VALUE = Arrays.asList(
			TransactionTypeEnum.IPO_MANDATE, TransactionTypeEnum.ONE_TIME_MANDATE,
			TransactionTypeEnum.RECURRING_MANDATE, TransactionTypeEnum.LITE_TOPUP_MANDATE,
			TransactionTypeEnum.SBMD_MANDATE);

	private final String addMoneyToWalletRefundMerchantId;

	private static final List<String> MERGING_SPECIAL_HANDLING_FIELD = List.of(NOTES_KEY);

	private String merchantBaseUrl;

	private List<String> whitelistedMerchantVerticalIds;

	private List<String> whitelistedUserIdsForCartNarration;

	private List<String> ondcVerticalIds;

	private static boolean envBasedJacocoSupport;

	private final IRolloutStrategyHelper rolloutStrategyHelper;

	private RolloutStrategy rolloutStrategy;

	private String ondcLogoUrl;

	private String onlineDealsVerticalId;

	private String onlineDealsLogoUrl;

	private String gvVerticalId;

	private String gvLogoUrl;

	private String offlineDealsVerticalId;

	private String offlineDealsLogoUrl;

	private static Gson gson = new Gson();

	private ObjectMapper objectMapper = new ObjectMapper();

	private Map<String, String> verticalIdToMerchantLogoMap;

	@Autowired
	public Utility(@Value("${base.url.for.merchant.logo}") final String merchantBaseUrl,
			@Value("${envBased.jacocoSupport}") final boolean envBasedJacocoSupport,
			@Value("${cart.whitelistedMerchantVerticalIds}") final List<String> whitelistedMerchantVerticalIds,
			@Value("${cart.whitelisted.userIds.list.for.narration}") final List<String> whitelistedUserIdsForCartNarration,
			@Value("${url.for.ondc.logo}") final String ondcLogoUrl,
			@Value("${ondc.verticalId}") final List<String> ondcVerticalIds,
			@Value("${online.deals.verticalId}") final String onlineDealsVerticalId,
			@Value("${online.deals.logoUrl}") final String onlineDealsLogoUrl,
			@Value("${gv.verticalId}") final String gvVerticalId, @Value("${gv.logoUrl}") final String gvLogoUrl,
			@Value("${offline.deals.verticalId}") final String offlineDealsVerticalId,
			@Value("${offline.deals.logoUrl}") final String offlineDealsLogoUrl,
			final ConfigurableConstants configurableConstants, final IRolloutStrategyHelper rolloutStrategyHelper,
			final RolloutStrategy rolloutStrategy) {
		this.merchantBaseUrl = merchantBaseUrl;
		this.ondcLogoUrl = ondcLogoUrl;
		Utility.envBasedJacocoSupport = envBasedJacocoSupport;
		this.whitelistedMerchantVerticalIds = whitelistedMerchantVerticalIds;
		this.whitelistedUserIdsForCartNarration = whitelistedUserIdsForCartNarration;
		this.ondcVerticalIds = ondcVerticalIds;
		this.onlineDealsVerticalId = onlineDealsVerticalId;
		this.onlineDealsLogoUrl = onlineDealsLogoUrl;
		this.gvVerticalId = gvVerticalId;
		this.gvLogoUrl = gvLogoUrl;
		this.offlineDealsVerticalId = offlineDealsVerticalId;
		this.offlineDealsLogoUrl = offlineDealsLogoUrl;
		this.rolloutStrategy = rolloutStrategy;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.addMoneyToWalletRefundMerchantId = configurableConstants.getAddMoneyRefundMerchantId();
		log.info("Jacoco Support for the current Env : {}", Utility.envBasedJacocoSupport);
		updateVerticalIdToMerchantLogoMap();
	}

	public static void removeLocationData(TransactionHistoryDetails thd) {
		if (Objects.nonNull(thd) && Objects.nonNull(thd.getLocation())) {
			thd.setLocation(null);
		}
	}

	private void updateVerticalIdToMerchantLogoMap() {
		Map<String, String> verticalIdToMerchantLogoMapping = new HashMap<>();

		verticalIdToMerchantLogoMapping.put(ondcVerticalIds.get(0), ondcLogoUrl);
		verticalIdToMerchantLogoMapping.put(offlineDealsVerticalId, offlineDealsLogoUrl);
		verticalIdToMerchantLogoMap = Collections.unmodifiableMap(verticalIdToMerchantLogoMapping);
	}

	public static boolean checkPpblTxnType(final Integer streamSource, final Map<String, String> contextMap) {
		return (TransactionSource.PPBL.getTransactionSourceKey().equals(streamSource)
				|| TransactionSource.TS.getTransactionSourceKey().equals(streamSource)) && contextMap != null
				&& contextMap.containsKey("mainTxnType")
				&& TransactionTypeEnum.PPBL_TRANSACTION.getTransactionType().equals(contextMap.get("mainTxnType"))
				&& (!(TransactionSource.TS.getTransactionSourceKey().equals(streamSource)
						&& PPBL_TS_IMPS_REPORT_CODES.contains(contextMap.get(REPORT_CODE))));
	}

	public static long getDiffWithCurrentDate(final Long txnDate) {
		if (Objects.nonNull(txnDate)) {
			try {
				LocalDateTime date1 = LocalDateTime.now();
				LocalDateTime date2 = LocalDateTime.ofInstant(Instant.ofEpochMilli(txnDate), ZoneId.systemDefault());
				return DAYS.between(date2, date1);
			}
			catch (Exception e) {
				return -1;
			}
		}
		return -1;
	}

	public static long getSecDiffWithCurrentDate(final Long txnDate) {
		if (Objects.nonNull(txnDate)) {
			try {
				LocalDateTime date1 = LocalDateTime.now();
				LocalDateTime date2 = LocalDateTime.ofInstant(Instant.ofEpochMilli(txnDate), ZoneId.systemDefault());
				long diff = SECONDS.between(date2, date1);
				// TODO Need to be removed, added just for checking
				log.info("inside getSecDiffWithCurrentDate() {} {} {}", date1, date2, diff);
				return diff;
			}
			catch (Exception e) {
				log.error("inside getSecDiffWithCurrentDate() {} {} ", txnDate, CommonsUtility.exceptionFormatter(e));
				return -1;
			}
		}
		return -1;
	}

	public static boolean mergingSpecialHandlingRequired(final String fieldName) {
		return MERGING_SPECIAL_HANDLING_FIELD.contains(fieldName);
	}

	public static boolean isValidBankAcctIdentifier(final String bankAcctId) {
		if (StringUtils.isBlank(bankAcctId) || bankAcctId.length() <= 6) {
			return false;
		}
		return true;
	}

	public static boolean checkIfEventContainsDebitCardParticipant(
			final TransformedTransactionHistoryDetail transactionHistoryDetails,
			final TransformedParticipant curParticipant) {
		if (curParticipant == null) {
			return false;
		}

		TransactionSource transactionSource = TransactionSource
			.getTransactionSourceEnumByKey(transactionHistoryDetails.getStreamSource());
		if (transactionSource == null) {
			return false;
		}

		return isPaytmDebitCard(transactionHistoryDetails, curParticipant, transactionSource);
	}

	private static boolean isPaytmDebitCard(final TransformedTransactionHistoryDetail transactionHistoryDetails,
			final TransformedParticipant curParticipant, final TransactionSource transactionSource) {
		if (TransactionSource.isPgTypeSource(transactionSource)) {
			if (transactionHistoryDetails.getEntityId().equalsIgnoreCase(curParticipant.getEntityId())
					&& PaymentSystemEnum.PG.getPaymentSystemKey().equals(curParticipant.getPaymentSystem())
					&& curParticipant.getCardData() != null && curParticipant.getBankData() != null) {
				return CardType.DEBIT.getCardTypeKey().equals(curParticipant.getCardData().getCardType())
						&& IfscUtility.getBankNameWithCamelCase(PAYTM_BANK_IFSC)
							.equalsIgnoreCase(curParticipant.getBankData().getBankName());
			}
		}
		return false;
	}

	public static Boolean isDefaultLocationCoordinates(TransformedGeoLocation geolocation) {
		if (WebConstants.DEFAULT_LATITUDE_VALUE.equals(geolocation.getLat())
				&& WebConstants.DEFAULT_LONGITUDE_VALUE.equals(geolocation.getLon())) {
			return true;
		}
		return false;
	}

	public static boolean isImpsFromTs(final TransformedTransactionHistoryDetail transformedTransactionHistoryDetail) {
		return transformedTransactionHistoryDetail.getStreamSource()
			.equals(TransactionSource.TS.getTransactionSourceKey())
				&& transformedTransactionHistoryDetail.getContextMap() != null
				&& transformedTransactionHistoryDetail.getContextMap().get(REPORT_CODE) != null
				&& PPBL_TS_IMPS_REPORT_CODES
					.contains(transformedTransactionHistoryDetail.getContextMap().get(REPORT_CODE));
	}

	public static String getReportCode(final TransformedTransactionHistoryDetail tthd) {
		if (tthd.getContextMap() != null && tthd.getContextMap().containsKey(REPORT_CODE)) {
			return tthd.getContextMap().get(REPORT_CODE);
		}
		return null;
	}

	public boolean isPgAddMoneyToWalletRefund(
			final TransformedTransactionHistoryDetail transformedTransactionHistoryDetail) {
		if (!ADD_MONEY_REFUND.getTransactionTypeKey().equals(transformedTransactionHistoryDetail.getTxnType())) {
			return false;
		}
		if (!TransactionSource.PG.getTransactionSourceKey()
			.equals(transformedTransactionHistoryDetail.getStreamSource())) {
			return false;
		}
		TransformedParticipant otherParticipant = getOtherParticipant(transformedTransactionHistoryDetail);
		if (otherParticipant == null) {
			return false;
		}
		String otherPartyEntityId = otherParticipant.getEntityId();
		return addMoneyToWalletRefundMerchantId.equalsIgnoreCase(otherPartyEntityId);
	}

	/**
	 * Checks if a THD is for an ADD_MONEY_REFUND PG txn.
	 * @param thd THD data to check
	 * @return true if it is an ADD_MONEY_REFUND txn
	 */
	public boolean isPgAddMoneyToWalletRefund(final TransactionHistoryDetails thd) {
		if (!P2M_REFUND.equals(thd.getTxnType())) {
			return false;
		}
		// Fix - PTH-1075 3th Point Fixed null pointer exception in payment system
		Optional<TxnParticipants> pgParticipantOptional = thd.getParticipants()
			.stream()
			.filter(txnParticipant -> PaymentSystemEnum.PG.equals(txnParticipant.getPaymentSystem()))
			.findAny();
		if (pgParticipantOptional.isEmpty()) {
			return false;
		}
		TxnParticipants pgParticipant = pgParticipantOptional.get();
		MerchantData merchantData = pgParticipant.getMerchantData();
		if (merchantData == null) {
			return false;
		}
		return addMoneyToWalletRefundMerchantId.equalsIgnoreCase(merchantData.getMerchantId());
	}

	// this fn is added to avoid reference cloning
	public TransformedTransactionHistoryDetail cloneObjectData(final TransformedTransactionHistoryDetail tthd) {
		TransformedTransactionHistoryDetail clonedData = null;
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		try {
			clonedData = objectMapper.readValue(objectMapper.writeValueAsString(tthd),
					TransformedTransactionHistoryDetail.class);
		}
		catch (Exception e) {
			log.error("Some exception :{} occurred while cloning data system Id : {}",
					CommonsUtility.exceptionFormatter(e), tthd.getEntityId());
		}
		return clonedData;
	}

	public static String getMaskedAccountNumber(final String accountNumber) {
		if (StringUtils.isBlank(accountNumber)) {
			return accountNumber;
		}
		// replace all those characters that have atleast 4 characters after it
		return accountNumber.replaceAll(".(?=.{4})", "X");
	}

	public static String getValidPhoneNumber(final TransformedParticipant participants,
			final TransactionTypeEnum transactionTypeEnum, final boolean forSecondParty) {
		if (Objects.nonNull(participants.getMobileData())
				&& StringUtils.isNotEmpty(participants.getMobileData().getMobileNumber()) && !forSecondParty) {
			return getValidPhoneNumber(participants.getMobileData().getMobileNumber());
		}
		String phoneNumber = getPhoneNumber(participants, transactionTypeEnum, forSecondParty);
		return getValidPhoneNumber(phoneNumber);
	}

	public static String getValidPhoneNumber(final String phoneNumber) {
		if (StringUtils.isNotBlank(phoneNumber)) {
			int phoneNumberLength = phoneNumber.trim().toCharArray().length;
			if (phoneNumberLength == 10 && NumberUtils.isParsable(phoneNumber)) {
				return phoneNumber;
			}
			else if (phoneNumberLength > 10 && NumberUtils.isParsable(phoneNumber)) {
				return phoneNumber.trim().substring(phoneNumber.length() - 10);
			}
		}
		return null;
	}

	private static String getPhoneNumber(final TransformedParticipant participants,
			final TransactionTypeEnum transactionTypeEnum, final boolean forSecondParty) {
		PaymentSystemEnum paymentSystemOfParticipant = PaymentSystemEnum
			.getPaymentSystemEnumByKey(participants.getPaymentSystem());
		if (transactionTypeEnum != null && TXN_TYPE_SUPPORTED.contains(transactionTypeEnum)
				&& paymentSystemOfParticipant != null) {
			if (PaymentSystemEnum.WALLET == paymentSystemOfParticipant && participants.getWalletData() != null) {
				if (participants.getWalletData().getWalletMobileNumber() != null) {
					return participants.getWalletData().getWalletMobileNumber();
				}
			}
			else if (PaymentSystemEnum.UPI == paymentSystemOfParticipant && participants.getContextMap() != null) {
				if (participants.getContextMap().containsKey(Constants.MOBILE_NUMBER)) {
					return participants.getContextMap().get(Constants.MOBILE_NUMBER);
				}
			}
			else if (PaymentSystemEnum.PPBL == paymentSystemOfParticipant && participants.getContextMap() != null) {
				if (BANK_REPORT_CODES.contains(participants.getContextMap().get(BankDataConstants.REPORT_CODE))) {
					if (forSecondParty) {
						if (TransactionIndicator.CREDIT.getTransactionIndicatorKey() == participants.getTxnIndicator()
								&& participants.getContextMap().containsKey(Constants.REMITTER_MOBILE)) {
							return participants.getContextMap().get(Constants.REMITTER_MOBILE);
						}
						else if (TransactionIndicator.DEBIT.getTransactionIndicatorKey() == participants
							.getTxnIndicator()
								&& participants.getContextMap().containsKey(Constants.BENEFICIARY_MOBILE)) {
							return participants.getContextMap().get(Constants.BENEFICIARY_MOBILE);
						}
					}
					else {
						if (TransactionIndicator.DEBIT.getTransactionIndicatorKey() == participants.getTxnIndicator()
								&& participants.getContextMap().containsKey(Constants.REMITTER_MOBILE)) {
							return participants.getContextMap().get(Constants.REMITTER_MOBILE);
						}
						else if (TransactionIndicator.CREDIT.getTransactionIndicatorKey() == participants
							.getTxnIndicator()
								&& participants.getContextMap().containsKey(Constants.BENEFICIARY_MOBILE)) {
							return participants.getContextMap().get(Constants.BENEFICIARY_MOBILE);
						}
					}
				}
			}
		}
		return null;
	}

	public static String getSecUserNameForBankTxn(final TransformedParticipant userParticipant) {
		if (Objects.isNull(userParticipant) || Objects.isNull(userParticipant.getContextMap())) {
			return null;
		}
		String reportCode = userParticipant.getContextMap().get(REPORT_CODE);

		if (XFER_RPT_CODES.contains(reportCode) || IMPS_INWARD_RPT_CODES.contains(reportCode)
				|| IMPS_OUTWARD_RPT_CODES.contains(reportCode) || NEFT_INWARD_RPT_CODES.contains(reportCode)
				|| NEFT_OUTWARD_RPT_CODES.contains(reportCode)) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(userParticipant.getTxnIndicator())) {
				return userParticipant.getContextMap().get(REMITTER_NAME);
			}
			else {
				return userParticipant.getContextMap().get(BENEFICIARY_NAME);
			}
		}
		return null;
	}

	public static boolean checkIfEventContainsPpblParticipant(
			final TransformedTransactionHistoryDetail transactionHistoryDetails,
			final TransformedParticipant curParticipant) {
		if (curParticipant == null) {
			return false;
		}

		TransactionSource transactionSource = TransactionSource
			.getTransactionSourceEnumByKey(transactionHistoryDetails.getStreamSource());
		if (transactionSource == null) {
			return false;
		}
		switch (transactionSource) {
			case PG:
			case PPBL_PG:
				if (transactionHistoryDetails.getEntityId().equalsIgnoreCase(curParticipant.getEntityId())
						&& (PaymentSystemEnum.PG.getPaymentSystemKey().equals(curParticipant.getPaymentSystem())
								// In case of Net-Banking Txn PG debit participant
								// paymentSystem is Bank
								|| PaymentSystemEnum.BANK.getPaymentSystemKey()
									.equals(curParticipant.getPaymentSystem()))
						&& curParticipant.getBankData() != null) {
					String bankName = curParticipant.getBankData().getBankName();
					if (StringUtils.equalsIgnoreCase(IfscUtility.getBankNameWithCamelCase(PAYTM_BANK_IFSC), bankName)) {
						return true;
					}
				}
				break;
			case UPI:
				// Condition or check UPI participant is PPBL participant or not
				if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(curParticipant.getPaymentSystem())
						&& transactionHistoryDetails.getEntityId().equals(curParticipant.getEntityId())) {
					String ifscCode = curParticipant.getBankData() == null ? null
							: curParticipant.getBankData().getIfsc();

					ifscCode = (ifscCode != null && ifscCode.length() >= 4) ? ifscCode.substring(0, 4) : null;

					if (PPBL_IFSCCODE_IDENTIFIER.equals(ifscCode)) {
						return true;
					}
				}
				break;
			default:

		}
		return PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(curParticipant.getPaymentSystem());
	}

	// Todo: Need to move all validation of participant for involving in grouping hre
	public static boolean validateParticipantForGrouping(final TransformedTransactionHistoryDetail tthd,
			final TransformedParticipant participant) {

		// Ignoring Add Money Debit Participant from grouping
		if (TransactionSource.WALLET.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(tthd.getMainTxnType())
				&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
			return false;
		}

		// Ignoring added participant by merging from grouping
		if (Objects.nonNull(participant.getContextMap())
				&& participant.getContextMap().containsKey(IS_ADDED_PARTICIPANT_BY_MERGING)
				&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IS_ADDED_PARTICIPANT_BY_MERGING))) {
			return false;
		}

		return true;
	}

	public static boolean skipMethod(final Method method) {
		// log.info("Jacoco Support for the current Env : {}", envBasedJacocoSupport);
		if (envBasedJacocoSupport && method.isSynthetic()) {
			log.info("Going to skip Method : {} in env : {}", method.getName(),
					System.getProperty(AbstractEnvironment.ACTIVE_PROFILES_PROPERTY_NAME));
			return true;
		}
		return false;
	}

	public static boolean skipField(final Field field) {
		// log.info("Jacoco Support for the current Env : {}", envBasedJacocoSupport);
		if (envBasedJacocoSupport && field.isSynthetic()) {
			log.info("Going to skip Field : {} in env : {}", field.getName(),
					System.getProperty(AbstractEnvironment.ACTIVE_PROFILES_PROPERTY_NAME));
			return true;
		}
		return false;
	}

	public String createLogoUrlForMerchant(final CartDetails cartDetails) {
		try {
			if (Objects.nonNull(cartDetails) && Objects.nonNull(cartDetails.getItems())
					&& cartDetails.getItems().size() == 1) {

				if (Objects.nonNull(cartDetails)
						&& StringUtils.isNotBlank(cartDetails.getItems().get(0).getUthSubVerticalId())) {
					if (gvVerticalId.equals(cartDetails.getItems().get(0).getVerticalId())
							&& GV.equals(cartDetails.getItems().get(0).getUthSubVerticalId())) {
						return gvLogoUrl;
					}
					else if (onlineDealsVerticalId.equals(cartDetails.getItems().get(0).getVerticalId())
							&& ONLINE_DEALS.equals(cartDetails.getItems().get(0).getUthSubVerticalId())) {
						return onlineDealsLogoUrl;
					}
				}

				if (StringUtils
					.isNotBlank(verticalIdToMerchantLogoMap.get(cartDetails.getItems().get(0).getVerticalId()))) {
					return verticalIdToMerchantLogoMap.get(cartDetails.getItems().get(0).getVerticalId());
				}
				else {
					CartItems cartItem = cartDetails.getItems().get(0);
					// https://<domain>/images/<mid>/<pid>.jpg
					return this.merchantBaseUrl + cartItem.getMerchantId() + "/" + cartItem.getProductId() + ".jpg";
				}
			}
		}
		catch (Exception e) {
			log.error("Exception while creating merchant logo from cart details. : {}",
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public void setCartDataIfRequired(final TransformedTransactionHistoryDetail thd,
			final CartRespDetails finalCartResponse) {
		if (thd == null || thd.getParticipants() == null || thd.getParticipants().isEmpty()
				|| !Boolean.TRUE.equals(thd.getIsCartDataFetched())) {
			return;
		}

		if (Objects.nonNull(finalCartResponse)) {
			CartMetaData cartMetaData = getCartMetaData(finalCartResponse.getItems());
			setUthNarrationAndSubVerticalId(thd, cartMetaData);
		}

		String name = null;
		if (thd.getCartDetails() != null && thd.getCartDetails().getItems() != null
				&& !thd.getCartDetails().getItems().isEmpty() && Objects.nonNull(thd.getCartDetails().getItems().get(0))
				&& StringUtils.isNotBlank(thd.getCartDetails().getItems().get(0).getName())) {
			name = thd.getCartDetails().getItems().get(0).getName();
		}
		else {
			return;
		}

		for (TransformedParticipant participant : thd.getParticipants()) {
			if (thd.getEntityId() != null && !thd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
				// here whitelisting is done on name based on
				if (this.needToSetNarration(thd)) {
					if (StringUtils.isNotEmpty(name)) {
						participant.setName(name);// set Name from CartDto
					}
				}
				String merchantLogo = createLogoUrlForMerchant(thd.getCartDetails());
				if (StringUtils.isNotEmpty(merchantLogo)) {
					participant.setLogoUrl(merchantLogo);
				}
			}
		}
	}

	// This method set uthNarration received in oms response to tthd.
	private static void setUthNarrationAndSubVerticalId(final TransformedTransactionHistoryDetail tthd,
			final CartMetaData cartMetaData) {
		if (Objects.isNull(cartMetaData)) {
			return;
		}

		/*
		 * Currently if cart response have multiple items then we don't populate cart
		 * details in tthd. Special handling is done when if cart response have multiple
		 * items then populate cart details with only one key (uth_narration) in tthd.
		 */
		if (Objects.isNull(tthd.getCartDetails())) {
			List<CartItems> cartItems = new ArrayList<>();
			CartItems cartItem = new CartItems();

			if (StringUtils.isNotBlank(cartMetaData.getUthNarration())
					&& StringUtils.length(cartMetaData.getUthNarration()) < 200) {
				cartItem.setUthNarration(cartMetaData.getUthNarration());
			}

			if (StringUtils.isNotBlank(cartMetaData.getUthSubVerticalId())) {
				cartItem.setUthSubVerticalId(cartMetaData.getUthSubVerticalId());
			}

			CartDetails cartDetails = new CartDetails();
			cartItems.add(cartItem);
			cartDetails.setItems(cartItems);
			tthd.setCartDetails(cartDetails);
		}
		else if (Objects.nonNull(tthd.getCartDetails()) && !CollectionUtils.isEmpty(tthd.getCartDetails().getItems())
				&& Objects.nonNull(tthd.getCartDetails().getItems().get(0))) {
			if (StringUtils.isNotBlank(cartMetaData.getUthNarration())
					&& StringUtils.length(cartMetaData.getUthNarration()) < 200) {
				tthd.getCartDetails().getItems().get(0).setUthNarration(cartMetaData.getUthNarration());
			}
			tthd.getCartDetails().getItems().get(0).setUthSubVerticalId(cartMetaData.getUthSubVerticalId());
		}
	}

	// Get cart meta_data from oms response.
	public CartMetaData getCartMetaData(final List<CartRespItems> cartRespItems) {
		CartMetaData cartMetaData = null;
		if (!CollectionUtils.isEmpty(cartRespItems)) {
			// Iterate items and if uth_narration is present in meta_data then return
			// meta_data
			for (CartRespItems cartRespItem : cartRespItems) {
				if (Objects.nonNull(cartRespItem)) {
					try {
						cartMetaData = objectMapper.readValue(cartRespItem.getMetaData(), CartMetaData.class);
						if (Objects.nonNull(cartMetaData) && StringUtils.isNotBlank(cartMetaData.getUthNarration())) {
							log.info("Fetched cart meta_data : {} for vertical Id : {}", cartMetaData,
									cartRespItem.getVerticalId());
							break;
						}
					}
					catch (Exception e) {
						log.error("exception in parsing uth_narration from oms response for cartRespItems: {}",
								cartRespItems);
					}
				}
			}
		}
		return cartMetaData;
	}

	// Get cart meta_data from oms response.
	public CartMetaData getCartMetaData(final CartRespItems cartRespItem, final String orderId) {
		CartMetaData cartMetaData = null;

		if (Objects.nonNull(cartRespItem) && StringUtils.isNotBlank(cartRespItem.getMetaData())) {
			try {
				cartMetaData = objectMapper.readValue(cartRespItem.getMetaData(), CartMetaData.class);
			}
			catch (Exception e) {
				log.error("Exception in parsing uth_narration from oms response for cartRespItems: {}", cartRespItem);
			}
		}
		else {
			log.warn("CartRespItem is null or meta_data is empty for orderId: {}, cartRespItem: {}", orderId,
					cartRespItem);
		}

		return cartMetaData;
	}

	/*
	 * Method to get view status from TTHD on the basis of txnType and Source System
	 * Visibility
	 */
	public static ClientStatusEnum getViewStatusFormTthd(final TransformedTransactionHistoryDetail txn) {
		TransactionTypeEnum transactionTypeEnum = TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getMainTxnType());
		switch (transactionTypeEnum) {
			case ADD_MONEY:
				return ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
			/*
			 * if (TransactionSource.WALLET.getTransactionSourceKey().equals(txn.
			 * getStreamSource())) { return
			 * ClientStatusEnum.getStatusEnumByKey(txn.getStatus()); } else { if
			 * (ClientStatusEnum.FAILURE.getStatusKey().equals(txn.getStatus())) { return
			 * ClientStatusEnum.FAILURE; } else { return ClientStatusEnum.PENDING; } }
			 */
			case P2P_UPI_TO_WALLET_INWARD:
			case P2P_UPI_TO_WALLET_OUTWARD:
			case WALLET_UPI_DEBIT_REVERSAL: // Adding since no guarantee of UPI Doc, No
											// Doc in P2P_Reversal
			case UPI_WALLET_CREDIT_REVERSAL:
				return ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
			case P2M:
				if (TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
						&& TransactionSource.isPgTypeSource(txn.getSourceSystem())) {
					return ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
				}
				else {
					return getStatusFromSourceTxn(txn);
				}
			case P2M_REFUND:
				if (!TransactionSource.isPgTypeSource(txn.getStreamSource())
						&& TransactionSource.isPgTypeSource(txn.getSourceSystem())) {
					return ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
				}
				else {
					return getStatusFromSourceTxn(txn);
				}
			case PPBL_TRANSACTION:
				ClientStatusEnum status = ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
				if (TransactionSource.PPBL.getTransactionSourceKey().equals(txn.getStreamSource())
						&& txn.getContextMap() != null && txn.getContextMap().containsKey(REPORT_CODE)) {
					status = setCbsStatus(txn);
					// https://wiki.mypaytm.com/x/HypjHQ
					if ("20502".equals(txn.getContextMap().get(REPORT_CODE))
							&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(txn.getTxnIndicator())) {
						TransformedParticipant selfParticipant = Utility.getSelfParticipant(txn);
						if (Objects.nonNull(selfParticipant) && Objects.nonNull(selfParticipant.getContextMap())
								&& selfParticipant.getContextMap().containsKey(RMT_ACCT_NUM)
								&& StringUtils.isNotBlank(selfParticipant.getContextMap().get(RMT_ACCT_NUM))
								&& selfParticipant.getContextMap().get(RMT_ACCT_NUM).toLowerCase().endsWith("@paytm")) {
							status = ClientStatusEnum.PENDING;
						}
					}
				}
				return status;
			case IPO_MANDATE:
				return ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
			case RECURRING_MANDATE:
				return ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
			default:
				return getStatusFromSourceTxn(txn);
		}
	}

	private static ClientStatusEnum getStatusFromSourceTxn(final TransformedTransactionHistoryDetail txn) {

		if (Boolean.TRUE.equals(txn.getIsSource())) {
			return ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
		}
		else {
			if (ClientStatusEnum.FAILURE.getStatusKey().equals(txn.getStatus())) {
				return ClientStatusEnum.FAILURE;
			}
			else {
				return ClientStatusEnum.PENDING;
			}
		}
	}

	public static ClientStatusEnum setCbsStatus(final TransformedTransactionHistoryDetail txn) {
		ClientStatusEnum txnStatus = ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
		String reportCode = txn.getContextMap().get(REPORT_CODE);
		ClientStatusEnum status = null;
		Integer txnIndicator = txn.getTxnIndicator();
		if ((PPBL_TS_IMPS_REPORT_CODES.contains(reportCode)
				&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(txnIndicator))) {
			return ClientStatusEnum.PENDING;
		}
		switch (reportCode) {
			case "20261":
			case "20263":
				status = ClientStatusEnum.PENDING;
				break;
			case "20512":
				if (Objects.nonNull(txn.getContextMap())
						&& Objects.nonNull(txn.getContextMap().get(WebConstants.RMT_ACCT_NUM))
						&& containPaytmVpa(txn.getContextMap().get(WebConstants.RMT_ACCT_NUM))) {
					status = ClientStatusEnum.PENDING;
				}
				else {
					status = txnStatus;
				}
				break;
			default:
				status = txnStatus;
		}
		return status;
	}

	public boolean isWhiteListedImpsOutward(final TransformedTransactionHistoryDetail txn) {
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(txn.getStreamSource())
				&& TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(txn.getTxnIndicator())) {
			return false;
		}
		return txn.getContextMap() != null && PPBL_TS_IMPS_REPORT_CODES.contains(txn.getContextMap().get(REPORT_CODE))
				&& rolloutStrategyHelper.isUserWhiteListed(IMPS, txn.getEntityId());
	}

	public boolean isNonWhiteListedImpsOutward(final TransformedTransactionHistoryDetail txn) {
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(txn.getStreamSource())
				&& TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(txn.getTxnIndicator())) {
			return false;
		}
		return txn.getContextMap() != null && PPBL_TS_IMPS_REPORT_CODES.contains(txn.getContextMap().get(REPORT_CODE))
				&& !rolloutStrategyHelper.isUserWhiteListed(IMPS, txn.getEntityId());
	}

	private boolean needToSetNarration(final TransformedTransactionHistoryDetail thd) {
		return whitelistedUserIdsForCartNarration != null && !whitelistedUserIdsForCartNarration.isEmpty()
				&& (whitelistedUserIdsForCartNarration.contains("-1")
						|| whitelistedUserIdsForCartNarration.contains(thd.getEntityId()))
				&& whitelistedMerchantVerticalIds != null && !whitelistedMerchantVerticalIds.isEmpty()
				&& (whitelistedMerchantVerticalIds.contains("-1") || whitelistedMerchantVerticalIds
					.contains(thd.getCartDetails().getItems().get(0).getVerticalId()));
	}

	public static boolean isInternationalRemittance(final Map<String, String> contextMap) {
		if (!Objects.isNull(contextMap) && contextMap.containsKey(TXN_PURPOSE)
				&& WebConstants.INTERNATIONAL_REMITTANCE_OUTWARD.equals(contextMap.get(TXN_PURPOSE))) {
			return true;
		}
		return false;
	}

	public static boolean isP2pInternationalRemittanceOutwardRefundTransaction(final Integer txnType,
			final Map<String, String> contextMap) {
		return TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(txnType)
				&& isInternationalRemittance(contextMap);
	}

	public static boolean isP2pInternationalRemittanceOutwardTransaction(final Integer txnType,
			final Map<String, String> contextMap) {
		return TransactionTypeEnum.P2M.getTransactionTypeKey().equals(txnType) && isInternationalRemittance(contextMap);
	}

	public static TransformedParticipant getSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		TransformedParticipant selfParticipant = null;
		if (Objects.nonNull(tthd) && Objects.nonNull(tthd.getParticipants())) {
			TransactionIndicator orderLevelIndicator = TransactionIndicator
				.getTransactionIndicatorEnumByKey(tthd.getTxnIndicator());
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (Objects.nonNull(orderLevelIndicator)
						&& orderLevelIndicator.equals(
								TransactionIndicator.getTransactionIndicatorEnumByKey(participant.getTxnIndicator()))
						&& StringUtils.isNotBlank(tthd.getEntityId())
						&& tthd.getEntityId().equals(participant.getEntityId())) {
					selfParticipant = participant;
				}
			}
		}
		return selfParticipant;
	}

	public static List<TransformedParticipant> getSelfParticipants(final TransformedTransactionHistoryDetail tthd) {
		List<TransformedParticipant> selfParticipants = new ArrayList<>();
		if (Objects.nonNull(tthd) && Objects.nonNull(tthd.getParticipants())) {
			TransactionIndicator orderLevelIndicator = TransactionIndicator
				.getTransactionIndicatorEnumByKey(tthd.getTxnIndicator());
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (Objects.nonNull(orderLevelIndicator)
						&& orderLevelIndicator.equals(
								TransactionIndicator.getTransactionIndicatorEnumByKey(participant.getTxnIndicator()))
						&& StringUtils.isNotBlank(tthd.getEntityId())
						&& tthd.getEntityId().equals(participant.getEntityId())) {
					selfParticipants.add(participant);
				}
			}
		}
		return selfParticipants;
	}

	// Method to get self participant for PG since PG can have multiple user participant
	// in case of multi payment attempts
	public static TransformedParticipant getSelfUpiParticipantForPg(final TransformedTransactionHistoryDetail tthd) {
		TransformedParticipant selfUpiParticipant = null;
		if (ObjectUtils.isNotEmpty(tthd.getParticipants()) && Objects.nonNull(tthd.getTxnIndicator())
				&& Objects.nonNull(tthd.getEntityId())) {

			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (tthd.getTxnIndicator().equals(participant.getTxnIndicator())
						&& tthd.getEntityId().equals(participant.getEntityId())
						&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					selfUpiParticipant = participant;
				}
			}
		}
		return selfUpiParticipant;
	}

	/* Method to fetch latest txnDate UPI self participant from TTHD */
	public static TransformedParticipant getSelfUpiParticipant(final TransformedTransactionHistoryDetail tthd) {
		if (ObjectUtils.isNotEmpty(tthd.getParticipants()) && Objects.nonNull(tthd.getTxnIndicator())
				&& Objects.nonNull(tthd.getEntityId()) && Objects.nonNull(tthd.getOriginalStatus())) {

			sortParticipantsByTxnDate(tthd.getParticipants());

			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (tthd.getTxnIndicator().equals(participant.getTxnIndicator())
						&& tthd.getEntityId().equals(participant.getEntityId())
						&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())
						&& tthd.getOriginalStatus().equals(participant.getStatus())) {
					return participant;
				}
			}
		}
		return null;
	}

	public static TransformedParticipant getOtherParticipant(final TransformedTransactionHistoryDetail tthd) {
		TransformedParticipant otherParticipant = null;
		if (Objects.nonNull(tthd) && Objects.nonNull(tthd.getParticipants())) {
			TransactionIndicator orderLevelIndicator = TransactionIndicator
				.getTransactionIndicatorEnumByKey(tthd.getTxnIndicator());
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (Objects.nonNull(orderLevelIndicator) && !(orderLevelIndicator
					.equals(TransactionIndicator.getTransactionIndicatorEnumByKey(participant.getTxnIndicator()))
						&& StringUtils.isNotBlank(tthd.getEntityId())
						&& tthd.getEntityId().equals(participant.getEntityId()))) {
					otherParticipant = participant;
				}
			}
		}
		return otherParticipant;
	}

	public static boolean isUserPercentageWiseWhitelisted(final String userId, final Double percentage) {
		if (percentage == null) {
			return false;
		}

		if (percentage > 100 || percentage < 0) {
			log.warn("Rollout percentage specified is invalid for userId : {}", userId);
			return false;
		}

		if (percentage == 0) {
			return false;
		}

		if (percentage == 100) {
			return true;
		}

		if (StringUtils.isBlank(userId)) {
			return false;
		}

		try {
			Long custId = Long.parseLong(userId);
			String percent = String.valueOf(percentage);

			// #countOfZerosAtEnd indicates zeros appended at the end of percentage
			// like 5.10 has 1 extra zero at end which is not required
			int countOfZerosAtEnd = 0;
			// Indicates to which decimalPoint we have to consider for percentage
			// calculation
			int decimalPointInPercentage = 0;

			if (percent.contains(".")) {
				while (percent.charAt(percent.length() - 1 - countOfZerosAtEnd) == '0') {
					countOfZerosAtEnd++;
				}
				decimalPointInPercentage = percent.length() - percent.indexOf('.') - 1 - countOfZerosAtEnd;
			}
			return (custId % (100 * (Math.pow(10, decimalPointInPercentage)))) <= percentage
					* (Math.pow(10, decimalPointInPercentage));
		}
		catch (NumberFormatException e) {
			log.error("CustId : {} , is non numeric for whitelisting check. Exception :{}", userId,
					CommonsUtility.exceptionFormatter(e));
			return false;
		}
		catch (Exception e) {
			log.error("Unknown exception in applying percentage-wise whitelisting for userId : {} .", userId);
			return false;
		}
	}

	/***
	 * isWhiteListingRequired = 1 :: whiteListing required for some users
	 * isWhiteListingRequired = -1 :: whiteListing not required / open for all
	 * isWhiteListingRequired = 0 :: close for all
	 */
	public static boolean checkWhiteListingFlag(final List<String> whiteListedUserList,
			final Integer isWhiteListingRequired, final TransformedTransactionHistoryDetail sourceDto) {

		return checkWhiteListingFlag(whiteListedUserList, isWhiteListingRequired, sourceDto.getEntityId());
	}

	/**
	 * isWhiteListed used when user not in whiteListed list.
	 * @param whiteListedUserList whiteListed users. Overrides isWhiteListingRequired
	 * @param isWhiteListingRequired 0 : false, -1 : true. Default switch for
	 * whiteListing.
	 * @param entityId entry to be checked in whiteListedUserList
	 * @return whiteListed or not
	 */
	public static boolean checkWhiteListingFlag(final List<String> whiteListedUserList,
			final Integer isWhiteListingRequired, final String entityId) {
		if (isWhiteListingRequired == 1) {
			if (whiteListedUserList.contains(entityId)) {
				return true;
			}
			else {
				return false;
			}
		}
		else if (isWhiteListingRequired == 0) {
			return false;
		}
		else if (isWhiteListingRequired == -1) {
			return true;
		}
		return false;
	}

	public static boolean isUserWhitelistedFromPercentageOrUserList(final Double rollOutPercentage,
			final Integer isWhiteListingRequired, final List<String> whiteListedUserList,
			final TransformedTransactionHistoryDetail tthd) {
		return isUserWhitelistedFromPercentageOrUserList(rollOutPercentage, isWhiteListingRequired, whiteListedUserList,
				tthd.getEntityId());
	}

	/***
	 * isWhiteListingRequired = 1 :: whiteListing required for some users
	 * isWhiteListingRequired = -1 :: whiteListing not required or open for all
	 * isWhiteListingRequired = 0 :: close for all
	 */
	public static boolean isUserWhitelistedFromPercentageOrUserList(final Double rollOutPercentage,
			final Integer isWhiteListingRequired, final List<String> whiteListedUserList, final String custId) {
		boolean isUserWhitelisted = false;

		// Case where it is opened for all users
		if (isWhiteListingRequired == -1) {
			isUserWhitelisted = true;
		}
		else if (isWhiteListingRequired == 1) { // case where open for some %age and
												// whitelisted users
			isUserWhitelisted = whiteListedUserList.contains(custId)
					|| isUserPercentageWiseWhitelisted(custId, rollOutPercentage);
		}
		return isUserWhitelisted;
	}

	public static void sortParticipantsByTxnDate(final List<TransformedParticipant> participants) {
		if (Objects.isNull(participants)) {
			return;
		}
		Comparator<TransformedParticipant> c = new Comparator<TransformedParticipant>() {
			@Override
			public int compare(final TransformedParticipant o1, final TransformedParticipant o2) {
				Long o1TxnDate = Long.parseLong(o1.getTxnDate());
				Long o2TxnDate = Long.parseLong(o2.getTxnDate());
				return o2TxnDate.compareTo(o1TxnDate);
			}
		};
		participants.sort(c);
	}

	public static boolean isItIgnoredParticipant(final TxnParticipants participant) {
		// TODO: Will also Add Virtual Participant condition here
		if (participant.getContextMap() != null
				&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IGNORED_PARTICIPANT))) {
			return true;
		}
		return false;
	}

	public static boolean isIgnoredParticipant(final TransformedParticipant participant) {
		if (participant.getContextMap() != null
				&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IGNORED_PARTICIPANT))) {
			return true;
		}
		return false;
	}

	/*
	 * { "all failure instruments 1 success instrument": [ "find the success participant",
	 * "enrich that participant only" ], "some failures some success instruments": [
	 * "find all success participants", "enrich them" ], "all failures instruments":[
	 * "sort participants",
	 * "find the latest participant based on txnDate of participants",
	 * "enrich that participant only" ], "hybrid txns":[
	 * "enrich all non failure participants" ], "instant refund":[
	 * "it has order status failure and participants as success so enrich all non failure participants"
	 * } ] }
	 */
	// Pending instrument is seen as Success as it may be a success in future
	public static List<TransformedParticipant> extractParticipantsToEnrich(
			final TransformedTransactionHistoryDetail detail) {
		List<TransformedParticipant> participantsToEnrich = new ArrayList<>();
		boolean isFailureTxn = false;
		if (ClientStatusEnum.FAILURE.getStatusKey().equals(detail.getStatus())) {
			// a failure txn has most likely chances of 'all failures instruments' rule so
			// will sort the participants
			// to extract latest participant
			sortParticipantsByTxnDate(detail.getParticipants());
			isFailureTxn = true;
		}

		boolean allFailureParticipant = true;
		TransformedParticipant latestFailureUserParticipant = null;

		for (TransformedParticipant participant : detail.getParticipants()) {
			// Ignoring Ignored flag Participant From Search Fields Creation
			if (participant.getContextMap() != null
					&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IGNORED_PARTICIPANT))) {
				continue;
			}

			if (detail.getEntityId().equals(participant.getEntityId())) {
				if (!ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())) {
					participantsToEnrich.add(participant);
					allFailureParticipant = false;
					latestFailureUserParticipant = null;
				}
				else if (latestFailureUserParticipant == null) {
					latestFailureUserParticipant = participant;
				}
			}

			if (!detail.getEntityId().equals(participant.getEntityId())) {
				participantsToEnrich.add(participant);
			}
		}

		if (isFailureTxn && allFailureParticipant && latestFailureUserParticipant != null) {
			participantsToEnrich.add(latestFailureUserParticipant);
		}

		return participantsToEnrich;
	}

	public static boolean isMandateTransaction(final TransformedTransactionHistoryDetail tthd) {
		return MANDATE_TXN_TYPE.contains(tthd.getMainTxnType())
				&& TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource());
	}

	public static boolean isRecurringMandateTransaction(final TransformedTransactionHistoryDetail tthd) {
		return (TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey().equals(tthd.getMainTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey().equals(tthd.getMainTxnType()))
				&& TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource());
	}

	public static boolean isMandateTransaction(final TransactionHistoryDetails thd) {
		return MANDATE_TXN_TYPE_VALUE.contains(thd.getTxnType()) && TransactionSource.UPI.equals(thd.getStreamSource());
	}

	public static TransformedParticipant getDebitParticipant(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd)) {
			return null;
		}
		TransformedParticipant debitParticipant = null;
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				debitParticipant = participant;
			}
		}
		return debitParticipant;
	}

	public static TransformedParticipant getUpiParticipant(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd)) {
			return null;
		}
		TransformedParticipant upiParticipant = null;
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (StringUtils.isNotBlank(tthd.getEntityId()) && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
				upiParticipant = participant;
			}
		}
		return upiParticipant;
	}

	public static String getVpaHandleForUpiDebitParticipant(TransformedTransactionHistoryDetail txn) {
		TransformedParticipant upiParticipant = getUpiParticipant(txn);
		return getVpaHandle(upiParticipant);
	}

	private static String getVpaHandle(TransformedParticipant participant) {

		if (participant != null && participant.getUpiData() != null
				&& StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
			String[] vpaParts = participant.getUpiData().getVpa().split(VPA_SPLITTER);
			if (vpaParts.length >= 2) {
				return vpaParts[1];
			}
		}
		return null;
	}

	public static Long getAmountInLowerDenomination(final String amount) {
		if (StringUtils.isBlank(amount)) {
			return null;
		}
		BigDecimal bigDecimalAmount = new BigDecimal(amount).multiply(new BigDecimal(100));
		return bigDecimalAmount.longValue();
	}

	public static String getAmountStringInLowerDenominationIfValidForSearch(final String amount) {
		if (StringUtils.isBlank(amount)) {
			return null;
		}
		BigDecimal bigDecimalAmount = new BigDecimal(amount).multiply(new BigDecimal(100));
		// As of now we don't provide search for amount >= 1000000
		return String.valueOf(bigDecimalAmount.longValue()).length() <= 8 ? String.valueOf(bigDecimalAmount.longValue())
				: null;
	}

	/**
	 * If both ADDNPAY_REFUND_BACK_TO_SOURCE & IS_ADD_N_PAY_REFUND = true, then special
	 * handling not needed.
	 * <p>
	 * Else if only IS_ADD_N_PAY_REFUND = true, then special handling needed.
	 * <p>
	 * Else false
	 * @param tthd dto to check for special handling
	 * @return true if special handling needed
	 */
	public static boolean isAddnPayRefundTxn(final TransformedTransactionHistoryDetail tthd) {
		if (tthd != null && P2M_REFUND.getTransactionTypeKey().equals(tthd.getMainTxnType())) {
			if (tthd.getContextMap() != null && tthd.getContextMap().containsKey(IS_ADD_N_PAY_REFUND)
					&& TRUE.equalsIgnoreCase(tthd.getContextMap().get(IS_ADD_N_PAY_REFUND))) {
				return !(tthd.getContextMap().containsKey(ADDNPAY_REFUND_BACK_TO_SOURCE)
						&& TRUE.equalsIgnoreCase(tthd.getContextMap().get(ADDNPAY_REFUND_BACK_TO_SOURCE)));

			}
		}
		return false;
	}

	public static String getUpdatedIndexName(final String indexNamePrefix, final String txnDate,
			final List<String> updatedIndexList) {
		Map<DateTimeEnum, String> dateMap = getDateTime(txnDate, DateTimeEnum.year, DateTimeEnum.month,
				DateTimeEnum.date, DateTimeEnum.hour);
		String month = dateMap.get(DateTimeEnum.month);
		String year = dateMap.get(DateTimeEnum.year);
		String date = dateMap.get(DateTimeEnum.date);
		String suffix = IndexUtility.getSuffix(Long.parseLong(txnDate), Integer.parseInt(date));

		String indexName = indexNamePrefix + "-" + month + "-" + year + suffix;
		try {
			if (updatedIndexList.contains(indexName)) {
				indexName = indexNamePrefix + "v2-" + month + "-" + year + suffix;
			}
		}
		catch (Exception e) {
			log.error("Exception while getting updated indexName. upatedIndexList :{} Exception :{}", updatedIndexList,
					CommonsUtility.exceptionFormatter(e));
		}
		// log.warn("Updated IndexName : {} for month : {}, year : {}", indexName, month,
		// year);
		return indexName;
	}

	public static Map<DateTimeEnum, String> getDateTime(final String epochTimeStamp,
			final DateTimeEnum... dateFormats) {
		Map<DateTimeEnum, String> dateMap = new HashMap<>();
		long time = Long.parseLong(epochTimeStamp.length() == 10 ? epochTimeStamp + "000" : epochTimeStamp);
		SimpleDateFormat sdf = null;
		for (DateTimeEnum dateTimeEnum : dateFormats) {
			String dateFormat = dateTimeEnum.getDateFormat();
			if (StringUtils.isEmpty(epochTimeStamp)) {
				throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
			}
			else {
				sdf = new SimpleDateFormat(dateFormat);
				sdf.setTimeZone(TimeZone.getTimeZone("IST"));
				try {
					String parsedDate = sdf.format(new Date(time));
					dateMap.put(dateTimeEnum, parsedDate);
				}
				catch (Exception var6) {
					throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
				}
			}
		}
		return dateMap;
	}

	public static String getDateTime(final String timeStr, final String dateFormat) {
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
		try {
			long time = Long.parseLong(timeStr.length() == 10 ? timeStr + "000" : timeStr);
			String parsedDate = sdf.format(new Date(time));
			return parsedDate;
		}
		catch (Exception e) {
			log.error("Invalid epoch time passed " + timeStr);
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_EPOCH);
		}
	}

	public static String getDateTime(final long epochTime, final String dateFormat) {
		return getDateTime(String.valueOf(epochTime), dateFormat);
	}

	public static long getTotalPages(final Long totalHits, final int pageSize) {
		return totalHits % pageSize == 0 ? totalHits / pageSize : (totalHits / pageSize) + 1;
	}

	public static void validateSearchContext(final SearchContext searchContext, final PaginationParams params) {

		if (searchContext.getFromDate() > searchContext.getToDate()) {
			log.error("fromDate can't be more than toDate");
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		if (searchContext.getPageSize() < 1) {
			log.error("page size cannot be less than 1");
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		if (searchContext.getStreamSource() == null) {
			log.info(
					"source context is not provided, hence we need to show only transactions with showInListing flag as true");
			searchContext.setShowInListing(true);
		}

		if (StringUtils.isNotBlank(searchContext.getMobileNumber())) {
			if (StringUtils.isBlank(Utility.getValidPhoneNumber(searchContext.getMobileNumber()))) {
				log.error("mobile number is not valid");
				throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
			}
		}

		if (searchContext.getPageNo() > 1 && (Objects.isNull(params) || (params.getTransactionDateEpoch() == null
				|| params.getPaginationStreamSource() == null || params.getPaginationTxnId() == null))) {
			log.error("For page number more than 1, transactionDateEpoch, paginationStreamSource must be present");
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAM_SEARCH_AFTER);
		}

		log.debug("Search context validated. searchContext : {}", searchContext);
	}

	public static void validateSearchContext(final SearchContext searchContext) {

		if (searchContext.getFromDate() > searchContext.getToDate()) {
			log.error("fromDate can't be more than toDate");
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		if (searchContext.getPageSize() < 0) {
			log.error("page size cannot be less than 1");
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		if (searchContext.getStreamSource() == null) {
			log.info(
					"source context is not provided, hence we need to show only transactions with showInListing flag as true");
			searchContext.setShowInListing(true);
		}

		log.debug("Search context validated. searchContext : {}", searchContext);
	}

	public static boolean isThirdPartyUpiDoc(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd)
				&& TransactionSource.UPI.equals(TransactionSource.getTransactionSourceEnumByKey(tthd.getStreamSource()))
				&& Objects.isNull(tthd.getSourceSystem())) {
			return true;
		}
		return false;
	}

	public static boolean isWalletInterOpTxn(final TransformedTransactionHistoryDetail txn) {
		if (txn == null) {
			return false;
		}

		boolean isWalletIntropTxn = false;

		// TxnType should be wallet Introp for wallet Txn
		if (TransactionSource.WALLET.getTransactionSourceKey().equals(txn.getStreamSource())
				&& WALLET_INTEROP_TXN_TYPE_LIST
					.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getMainTxnType()))) {
			isWalletIntropTxn = true;
		}

		// If wallet InterOp Txn flag is there
		if (Boolean.FALSE.equals(isWalletIntropTxn) && txn.getContextMap() != null
				&& TRUE.equalsIgnoreCase(txn.getContextMap().get(IS_WALLET_INETROP_TXN))) {
			isWalletIntropTxn = true;
		}

		// Condition for checking wallet InterOp UPI Txn. User Participant would be wallet
		// always
		if (Boolean.FALSE.equals(isWalletIntropTxn)
				&& TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& ObjectUtils.isNotEmpty(txn.getParticipants())) {
			for (TransformedParticipant participant : txn.getParticipants()) {
				if (txn.getEntityId().equals(participant.getEntityId())
						&& PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					isWalletIntropTxn = true;
					break;
				}
			}
		}
		return isWalletIntropTxn;
	}

	public static boolean isUpiFirTxn(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getContextMap())) {
			return false;
		}

		return TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& FIR_PURPOSE_CODE.equalsIgnoreCase(txn.getContextMap().get(PURPOSE_CODE));
	}

	public static boolean isWalletTxn(final TransformedTransactionHistoryDetail txn) {
		if (txn == null) {
			return false;
		}

		boolean isWalletTxn = false;

		// Condition for checking wallet Txn. User Participant will have wallet as Payment
		// System
		if (TransactionSource.PG.getTransactionSourceKey().equals(txn.getStreamSource())
				&& ObjectUtils.isNotEmpty(txn.getParticipants())) {
			for (TransformedParticipant participant : txn.getParticipants()) {
				if (txn.getEntityId().equals(participant.getEntityId())
						&& PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					isWalletTxn = true;
					break;
				}
			}
		}
		return isWalletTxn;
	}

	public static void removeIgnoredParticipantFromView(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getParticipants())) {
			return;
		}

		txn.getParticipants().removeIf(Objects::isNull);
		addBankTxnIdToParticipant(txn);
		txn.getParticipants()
			.removeIf(participant -> (participant.getContextMap() != null
					&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IGNORED_PARTICIPANT))));
	}

	public static String getEpochTime(final String time) {
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
		sdf.setTimeZone(TimeZone.getTimeZone("IST"));
		Date date = null;
		try {
			date = sdf.parse(time);
			return String.valueOf(date.getTime());
		}
		catch (Exception e) {
			log.error("Some exception occured while parsing time {}", CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public static boolean isGiftVoucherPurchasedForOthersCase(final TransformedTransactionHistoryDetail txn) {
		if (txn == null) {
			return false;
		}
		TransformedParticipant userParticipant = null;
		for (TransformedParticipant participant : txn.getParticipants()) {
			if (StringUtils.isNotBlank(txn.getEntityId()) && txn.getEntityId().equals(participant.getEntityId())) {
				userParticipant = participant;
				break;
			}
		}
		if (userParticipant != null && userParticipant.getContextMap() != null
				&& GV_PURCHASED.equals(userParticipant.getContextMap().get(TXN_PURPOSE))) {
			return true;
		}

		return false;
	}

	// this function returns user participant based on entityId check only
	public static TransformedParticipant getUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		TransformedParticipant userParticipant = null;
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (StringUtils.isNotBlank(tthd.getEntityId()) && tthd.getEntityId().equals(participant.getEntityId())) {
				userParticipant = participant;
				break;
			}
		}
		return userParticipant;
	}

	public static TransactionTypeEnum getTxnTypeForSingleDoc(final Set<TransformedTransactionHistoryDetail> groupedDtos,
			final TransformedTransactionHistoryDetail tthd) {
		if (TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(tthd.getTxnType())) {
			Optional<TransformedTransactionHistoryDetail> walletDocOptional = groupedDtos.stream()
				.filter((doc) -> TransactionSource.WALLET.getTransactionSourceKey().equals(doc.getStreamSource())
						&& TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey().equals(doc.getMainTxnType()))
				.findFirst();
			TransformedTransactionHistoryDetail walletDocument = walletDocOptional.orElse(null);
			if (walletDocument != null && isGiftVoucherPurchasedForOthersCase(walletDocument)) {
				return TransactionTypeEnum.P2P_OUTWARD;
			}
		}
		if (ADD_MONEY_REFUND.getTransactionTypeKey().equals(tthd.getTxnType())) {
			return ADD_MONEY_REFUND;
		}
		return TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getMainTxnType());
	}

	public static String readFile(final String fileName) throws IOException {
		InputStream inputStream = Utility.class.getClassLoader().getResourceAsStream(fileName);
		String jsonString = IOUtils.toString(inputStream, Charset.forName("UTF-8"));
		return jsonString;
	}

	public static TransformedTransactionHistoryDetail convertJsonToTthd(final String json)
			throws JsonProcessingException {
		TypeReference<TransformedTransactionHistoryDetail> typeReference = new TypeReference<TransformedTransactionHistoryDetail>() {
		};
		ObjectMapper objectMapper = new ObjectMapper();
		TransformedTransactionHistoryDetail tthd = objectMapper.readValue(json, typeReference);
		return tthd;
	}

	public static void getRefactoredTxnForView(final TransformedTransactionHistoryDetail txn) {
		List<TransformedParticipant> refinedParticipants = getRefinedParticipantListForView(txn);

		if (ObjectUtils.isNotEmpty(refinedParticipants)) {
			txn.setParticipants(refinedParticipants);
		}
	}

	public static List<TransformedParticipant> getRefinedParticipantListForView(
			final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getParticipants())) {
			return new ArrayList<>();
		}

		// Getting refactored participants from search refactoring
		List<TransformedParticipant> refactoredParticipants = Utility.extractParticipantsToEnrich(txn);

		refactoredParticipants.removeIf(Objects::isNull);
		sortParticipantsByTxnDate(refactoredParticipants);

		List<TransformedParticipant> refinedParticipantList = new ArrayList<>();
		Map<String, Long> participantMap = new HashMap<>();

		for (TransformedParticipant participant : refactoredParticipants) {
			if (Boolean.FALSE.equals(isParticipantDuplicate(participantMap, participant))) {
				refinedParticipantList.add(participant);
			}
		}
		return refinedParticipantList;
	}

	/*
	 * Method isParticipantDuplicate
	 *
	 * @param participantMap Map(String, Long)
	 *
	 * @param participant TransformedParticipant
	 *
	 * @return boolean This method is filters participants on basis of primary following
	 * (PaymentSystemValue, TxnIndicator and type of Wallet if it is PaymentSystemValue) A
	 * participant is duplicate if participant's key is already present in participant map
	 * and it's updatedTime(based on txnDate of participant) is less then the value
	 * (updatedTime) present in map for that key. this will mean we have already
	 * encountered the recent participant of same type.
	 *
	 * We are using Wallet type in key because paymentSystem can be wallet but
	 * distinguishing factor will be WalletType
	 */
	private static boolean isParticipantDuplicate(final Map<String, Long> participantMap,
			final TransformedParticipant participant) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		Long curParticipantTxnDate = Long.valueOf(participant.getTxnDate());
		String type = null;
		if (PaymentSystemEnum.WALLET.getPaymentSystemValue().equals(paymentSystem.getPaymentSystemValue())) {
			type = (participant.getWalletData() != null && participant.getWalletData().getWalletType() != null)
					? participant.getWalletData().getWalletType().toString() : null;
		}
		String[] stringArray = { paymentSystem.getPaymentSystemValue(), type,
				participant.getTxnIndicator().toString() };
		String key = StringUtils.join(stringArray, "_");
		if (!participantMap.containsKey(key)) {
			participantMap.put(key, curParticipantTxnDate);
			return false;
		}
		else {
			Long existingUpdateTime = participantMap.get(key);
			if (existingUpdateTime >= curParticipantTxnDate) {
				return true;
			}
			else {
				participantMap.put(key, curParticipantTxnDate);
				return false;
			}
		}
	}

	public static TransformedParticipant getSecPartyParticipant(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getParticipants())) {
			return null;
		}

		TransformedParticipant secUserParticipant = null;

		for (TransformedParticipant participant : txn.getParticipants()) {
			if (Boolean.FALSE.equals(txn.getEntityId().equals(participant.getEntityId()))) {
				if (Objects.isNull(secUserParticipant) || (Long.parseLong(participant.getTxnDate()) > Long
					.parseLong(secUserParticipant.getTxnDate()))) {
					secUserParticipant = participant;
				}
			}
		}
		return secUserParticipant;
	}

	public static TransformedParticipant getPayeeParticipant(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd) || ObjectUtils.isEmpty(tthd.getParticipants())) {
			return null;
		}
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				return participant;
			}
		}
		return null;
	}

	public static String getSecUserAccRefId(final TransformedTransactionHistoryDetail txn) {
		String accRefId = null;
		if (StringUtils.isNotBlank(txn.getOtherPartyEntityId())
				&& Boolean.TRUE.equals(txn.getIsOtherPartyEntityIdVirtual())) {
			accRefId = txn.getOtherPartyEntityId();
		}
		return accRefId;
	}

	public static String getWalletMobileNo(final TransformedParticipant participant) {
		if (Objects.isNull(participant) || Objects.isNull(participant.getWalletData())
				|| StringUtils.isBlank(participant.getWalletData().getWalletMobileNumber())) {
			return null;
		}
		return participant.getWalletData().getWalletMobileNumber();
	}

	public static String getMobileNo(final TransformedParticipant participant) {
		String mobileNo = null;
		if (Objects.nonNull(participant) && Objects.nonNull(participant.getMobileData())
				&& StringUtils.isNotBlank(participant.getMobileData().getMobileNumber())) {
			mobileNo = participant.getMobileData().getMobileNumber();
		}
		if (StringUtils.isBlank(mobileNo)) {
			mobileNo = getWalletMobileNo(participant);
		}
		return mobileNo;
	}

	public static String getLastFourDigitOfAccountNo(final TransformedParticipant participant) {
		if (Objects.isNull(participant) || Objects.isNull(participant.getBankData())
				|| StringUtils.isBlank(participant.getBankData().getAccNumber())) {
			return null;
		}
		String accNo = participant.getBankData().getAccNumber();
		if (accNo.length() > 4) {
			accNo = accNo.substring(accNo.length() - 4);
		}
		return accNo;
	}

	public static String getLastFourDigitOfAccountNo(final String accNum) {
		if (Objects.nonNull(accNum) && accNum.length() > 4) {
			return accNum.substring(accNum.length() - 4);
		}
		return accNum;
	}

	public static boolean isPaytmPostpaidRepaymentTxn(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn)
				|| Boolean.FALSE.equals(TransactionTypeEnum.P2M.getTransactionTypeKey().equals(txn.getMainTxnType()))) {
			return false;
		}
		TransformedParticipant secParticipant = getSecPartyParticipant(txn);
		boolean isaPaytmPostpaidRepaymentTxn = false;
		if (Objects.nonNull(secParticipant)
				&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(secParticipant.getEntityType())
				&& POSTPAID_REPAYMENT_MERCHANT_IDS.contains(secParticipant.getEntityId())) {
			isaPaytmPostpaidRepaymentTxn = true;
		}
		return isaPaytmPostpaidRepaymentTxn;
	}

	public static MerchantTypeEnum getMerchantType(final TransformedParticipant participant) {
		if (Objects.isNull(participant) || Objects.isNull(participant.getMerchantData()) || Boolean.FALSE
			.equals(EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType()))) {
			return null;
		}

		return MerchantTypeEnum.getMerchantTypeByKey(participant.getMerchantData().getMerchantType());
	}

	public static String getTrimmedStr(final String str) {
		if (StringUtils.isBlank(str)) {
			return null;
		}
		return StringUtils.remove(str, SPACE);
	}

	public static String getBankAcctId(final TransformedTransactionHistoryDetail tthd) {
		String bankAcctId = null;
		if (Objects.nonNull(tthd)) {
			TransformedParticipant selfParticipant = getSelfParticipant(tthd);
			if (Objects.nonNull(selfParticipant) && Objects.nonNull(selfParticipant.getBankData())
					&& StringUtils.isNotBlank(selfParticipant.getBankData().getAccNumber())
					&& StringUtils.isNotBlank(selfParticipant.getBankData().getIfsc())) {
				int lengthOfAcctNo = selfParticipant.getBankData().getAccNumber().length();
				if (lengthOfAcctNo <= 4) {
					bankAcctId = selfParticipant.getBankData().getAccNumber() + UNDERSCORE
							+ selfParticipant.getBankData().getIfsc();
				}
				else {
					bankAcctId = selfParticipant.getBankData()
						.getAccNumber()
						.substring(lengthOfAcctNo - 4, lengthOfAcctNo) + UNDERSCORE
							+ selfParticipant.getBankData().getIfsc();
				}
			}
		}
		return bankAcctId;
	}

	public static Map<CacheInfo, String> getListingCacheNameToCacheKeyMappingApplicableOnTthd(
			final TransformedTransactionHistoryDetail tthd) {
		Map<CacheInfo, String> listingCacheNameToCacheKeyMapping = new HashMap<>();
		if (Objects.isNull(tthd) || StringUtils.isBlank(tthd.getEntityId())) {
			return listingCacheNameToCacheKeyMapping;
		}

		if (isOnlyForGrouping(tthd)) {
			/*
			 * In cases where two or more listing transactions are visible to the user for
			 * the same transaction, which is caused by concurrent processing.When the
			 * user clicks on a child transaction to open the detailed view, at the
			 * backend, we push this transaction to the retry Kafka for grouping. But this
			 * particular transaction history detail (TTHD) gets updated in Elastic Search
			 * at the stream enricher flatMap. However, it is not passed to the cache
			 * updater flatMap, which is a part of the Cache Updater pipeline. As a
			 * result, the app-side cache doesn't get updated. This leads to us missing
			 * these types of Elastic Search updates, and the app-side cache remains
			 * unaware of them.
			 */
			listingCacheNameToCacheKeyMapping.put(CacheInfo.UTH_APP_SIDE_CACHE_DATA, tthd.getEntityId());
		}
		else {
			listingCacheNameToCacheKeyMapping.put(CacheInfo.UTH_NTU_CACHE, tthd.getEntityId());
			listingCacheNameToCacheKeyMapping.put(CacheInfo.UTH_APP_SIDE_CACHE_DATA, tthd.getEntityId());
		}
		return listingCacheNameToCacheKeyMapping;
	}

	public boolean checkIfWhiteListed(final String entityId, final String checkWhiteListingFor) {
		// WhiteListing checks.
		// If no rollOut property found then by default it will return true.
		Optional<WhiteListingConfig> config = rolloutStrategy.getConfigList()
			.stream()
			.filter(configs -> configs.getWhitelistingFor().equals(checkWhiteListingFor))
			.findFirst();
		boolean isWhitelisted = config.map(whiteListingConfig -> Utility.isUserWhitelistedFromPercentageOrUserList(
				whiteListingConfig.getPercentage(), whiteListingConfig.getWhitelistingRequired(),
				whiteListingConfig.getUserList(), entityId))
			.orElse(false);
		log.info("isWhitelisted property for {} is {} for user with entityId {}", checkWhiteListingFor, isWhitelisted,
				entityId);
		return isWhitelisted;
	}

	public static String formatSecPartyNameString(final String name) {
		// this change is regarding
		// Camel case should only be done in case all the letters are in either caps or
		// small case.
		// But in case there is only one word and that is in all caps, we won't do
		// camelcasing
		Integer numberOfWords = countNumberOfWords(name);
		// no need to process further if string is blank
		if (numberOfWords == 0) {
			return name;
		}
		String stringOfOnlyAlphabets = name.replaceAll("[^A-Za-z]", "");
		boolean isAllCaps = CharMatcher.javaUpperCase().matchesAllOf(stringOfOnlyAlphabets);
		boolean isAllLowerCase = CharMatcher.javaLowerCase().matchesAllOf(stringOfOnlyAlphabets);
		if (isAllCaps && numberOfWords == 1) {
			return name;
		}
		if (isAllCaps || isAllLowerCase) {
			return WordUtils.capitalizeFully(StringUtils.normalizeSpace(name));
		}
		return name;
	}

	public static Integer countNumberOfWords(final String passedString) {
		if (StringUtils.isBlank(passedString)) {
			return 0;
		}
		String[] numberOfWords = passedString.split("\\s+");
		return numberOfWords.length;
	}

	public static void putExtraInfoInContextMapOfThd(final PostStitchData stitchData,
			final TransactionHistoryDetails thd) {
		// add that extra info to contextMap of Thd
		List<String> listOfUpdatedCol = stitchData.getPayload().getUpdatedColumns();
		String singleStringOfListOfCol = null;
		Map<String, String> contextMap = thd.getContextMap();
		// check the column list and make single comma separated string.
		if (Objects.nonNull(listOfUpdatedCol) && !listOfUpdatedCol.isEmpty() && Objects.nonNull(contextMap)) {
			singleStringOfListOfCol = String.join(COMMA, listOfUpdatedCol);
			contextMap.put(UPDATED_COLUMN_FROM_GGTS, singleStringOfListOfCol);
			log.trace("List of updated column we received from GGTS Service : {}", singleStringOfListOfCol);
		}
		thd.setContextMap(contextMap);
	}

	public static boolean isUpiViaCcTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && ObjectUtils.isNotEmpty(txn.getContextMap())
				&& TRUE.equalsIgnoreCase(txn.getContextMap().get(UPI_VIA_CC_FLAG));
	}

	public static boolean isUpiViaCcTxn(final TransformedParticipant participant) {
		return Objects.nonNull(participant) && ObjectUtils.isNotEmpty(participant.getCardData())
				&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem());
	}

	public static boolean isUpiViaCcTxn(final TransformedTransactionHistoryDetail tthd,
			final TransformedParticipant participant) {
		return Utility.isUpiViaCcTxn(tthd) && participant.getBankData() != null && participant.getCardData() != null;
	}

	public static void addIsUpiViaCcTxnFlag(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn)) {
			return;
		}
		Map<String, String> contextMap = Objects.isNull(txn.getContextMap()) ? new HashMap<>() : txn.getContextMap();
		contextMap.put(UPI_VIA_CC_FLAG, TRUE);
		txn.setContextMap(contextMap);
	}

	public static boolean isValidUpiLiteTxnForPopulatingSearchFields(final TransformedTransactionHistoryDetail tthd) {
		return Objects.nonNull(tthd) && TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& tthd.getIsVisible() && UpiLiteUtility.isUpiLiteTxn(tthd);
	}

	// This method is being used in search drools file for Upi Linked Ppbl bank Handling
	public static boolean isTxnApplicableForUpiLinkedPpblHandling(final TransformedTransactionHistoryDetail txn,
			final TransformedParticipant selfParticipant) {
		if (Objects.isNull(txn)) {
			return false;
		}

		if (TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.getTransactionTypeKey().equals(txn.getMainTxnType())) {
			return true;
		}
		return Boolean.FALSE.equals(UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(selfParticipant));
	}

	public static Object getCopy(final Object object) {
		return gson.fromJson(gson.toJson(object), object.getClass());
	}

	public static Exception getExceptionFinalCause(final Exception e) {
		Exception expPointer = e;
		try {
			int loopCounter = 0;
			// to fetch final/last cause and added counter to handle infinite loop
			// scenario
			while (expPointer.getCause() != null && loopCounter < 20) {
				expPointer = (Exception) expPointer.getCause();
				loopCounter++;
			}
		}
		catch (Exception exp) {
			return e;
		}
		if (expPointer == null) {
			return e;
		}
		else {
			return expPointer;
		}
	}

	public static boolean containPaytmVpa(final String remitterAcctNum) {
		String[] vpa = new String[0];
		boolean flag = false;
		if (!StringUtils.isEmpty(remitterAcctNum) && remitterAcctNum.length() > 6) {
			vpa = remitterAcctNum.split(VPA_SPLITTER);
		}
		flag = vpa[1].equalsIgnoreCase(WebConstants.PAYTM_VPA);
		return flag;
	}

	public static String setSearchTxnStatus(final TransformedTransactionHistoryDetail tthd) {
		// special handling for UPI (psp - other & Bank Paytm )
		// making status success for now
		// will handle in phase 2
		if (UpiInternationalUtility.isUpiInternationalAndOtherPspUpiEvent(tthd)) {
			return ClientStatusEnum.PENDING.getStatusKey().equals(tthd.getStatus())
					? ClientStatusEnum.SUCCESS.getStatusUserViewValue()
					: ClientStatusEnum.getStatusEnumByKey(tthd.getStatus()).getStatusUserViewValue();
		}
		else {
			return ClientStatusEnum.getStatusEnumByKey(tthd.getStatus()).getStatusUserViewValue();
		}
	}

	public static boolean isValidTthdForPopulatingSearchFields(final TransformedTransactionHistoryDetail tthd) {
		// special handling for upi international UPI event
		// for case : psp other & bank paytm
		// where we get only one UPI Event -> with status Pending at order and participant
		// level
		// we make its showInListing false if its child not present.
		// Due to which search Field not populating for this UPI Event
		// So we did special handling for this
		if (Boolean.TRUE.equals(tthd.getShowInListing()) && Boolean.TRUE.equals(tthd.getIsVisible())) {
			return true;
		}
		else if (Boolean.TRUE.equals(tthd.getIsVisible())
				&& UpiInternationalUtility.isUpiInternationalAndOtherPspUpiEvent(tthd)) {
			return true;
		}
		return false;
	}

	public static String getPurpose(final TransformedTransactionHistoryDetail detail) {
		if (detail != null && detail.getContextMap() != null
				&& !StringUtils.isEmpty(detail.getContextMap().get(TRANSACTION_PURPOSE))) {
			return detail.getContextMap().get(TRANSACTION_PURPOSE);
		}
		return null;
	}

	public static String getPurpose(final TransactionHistoryDetails detail) {
		if (detail != null && detail.getContextMap() != null
				&& !StringUtils.isEmpty(detail.getContextMap().get(TRANSACTION_PURPOSE))) {
			return detail.getContextMap().get(TRANSACTION_PURPOSE);
		}
		return null;
	}

	public static boolean isWalletLoanRepaymentTxn(final TransformedTransactionHistoryDetail tthd) {
		return Objects.nonNull(tthd)
				&& TransactionSource.WALLET.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& Objects.nonNull(tthd.getContextMap()) && StringUtils.isNotBlank(tthd.getContextMap().get(LAN));
	}

	public static Boolean isSelfTransferTxn(final TransformedTransactionHistoryDetail detail) {
		return Objects.nonNull(detail) && Objects.nonNull(detail.getContextMap())
				&& TRUE.equalsIgnoreCase(detail.getContextMap().get(IS_SELF_TRANSFER));
	}

	/**
	 * This method returns the second party info for p2p transaction type in which the upi
	 * transaction category is Vpa2Vpa and both party handle should be @paytm
	 */
	public static TransformedParticipant getOtherPartyP2pPaytmVpaParticipant(
			final TransformedTransactionHistoryDetail listingVisibleTxn) {

		if (Objects.isNull(listingVisibleTxn.getContextMap())
				|| !VPA2VPA.equalsIgnoreCase(listingVisibleTxn.getContextMap().get(UPI_TXN_CATEGORY))) {
			return null;
		}

		TransformedParticipant creditParticipant = null;
		TransformedParticipant debitParticipant = null;

		for (TransformedParticipant participant : listingVisibleTxn.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				creditParticipant = participant;
			}
			else {
				debitParticipant = participant;
			}
			if (Objects.isNull(participant.getContextMap())
					|| !PAYTM_HANDLE.equals(participant.getContextMap().get(VPA_HANDLE_FIELD_NAME))) {
				return null;
			}
		}

		if (P2P_OUTWARD.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())) {
			return creditParticipant;
		}
		else if (P2P_INWARD.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())) {
			return debitParticipant;
		}

		return null;
	}

	public static boolean isOnlyForGrouping(final TransformedTransactionHistoryDetail mergedDto) {
		// where IS_FOR_GROUPING is set? for which source? Not for Wallet_settlement
		// When user check the detail of ungrouped transaction, we push that event in
		// kafka for grouping.
		// This IS_FOR_GROUPING indicates that flag.
		return mergedDto.getContextMap() != null
				&& "true".equalsIgnoreCase(mergedDto.getContextMap().get(IS_FOR_GROUPING));
	}

	public static Boolean isWalletPassbookRequest(final Map<String, String> paramMap) {
		return ObjectUtils.isNotEmpty(paramMap) && WALLET_PASSBOOK_TYPE.equalsIgnoreCase(paramMap.get(PASSBOOK_TYPE))
				&& StringUtils.isNotBlank(paramMap.get(WALLET_TYPE_FILTER));
	}

	private static String getBankTxnIdFromParticipant(final TransformedTransactionHistoryDetail txn) {
		String bankTxnId = null;
		for (TransformedParticipant participant : txn.getParticipants()) {
			if (participant.getContextMap() != null
					&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IGNORED_PARTICIPANT))
					&& participant.getContextMap().containsKey(BANK_TXN_ID)) {
				bankTxnId = participant.getContextMap().get(BANK_TXN_ID);
				break;
			}
		}
		return bankTxnId;
	}

	public static void addBankTxnIdToParticipant(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getParticipants())) {
			return;
		}
		String bankTxnId = getBankTxnIdFromParticipant(txn);

		for (TransformedParticipant participant : txn.getParticipants()) {
			if (participant.getContextMap() != null && !participant.getContextMap().containsKey(IGNORED_PARTICIPANT)
					&& !Objects.isNull(bankTxnId)) {
				Map<String, String> contextMap = participant.getContextMap();
				contextMap.put(BANK_TXN_ID, bankTxnId);
				participant.setContextMap(contextMap);
				break;
			}
		}
	}

	public static String getRrn(final TransformedTransactionHistoryDetail txn) {
		if (Objects.nonNull(txn) && Objects.nonNull(txn.getContextMap())
				&& StringUtils.isNotBlank(txn.getContextMap().get("rrn"))) {
			return txn.getContextMap().get("rrn");
		}

		return null;
	}

	public static boolean isSkipOldestEvent(final TransactionHistoryDetails thd) {
		if (Objects.isNull(thd.getContextMap())
				|| StringUtils.isBlank(thd.getContextMap().get(BACKFILLING_IDENTIFIER))) {
			return true;
		}

		return BackFillingIdentifierEnum
			.getBackFillingIdentifierEnumByKey(thd.getContextMap().get(BACKFILLING_IDENTIFIER))
			.isSkipOldestEvent();
	}

	public static boolean isSkipOldestEvent(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd.getContextMap())
				|| StringUtils.isBlank(tthd.getContextMap().get(BACKFILLING_IDENTIFIER))) {
			return true;
		}

		return BackFillingIdentifierEnum
			.getBackFillingIdentifierEnumByKey(tthd.getContextMap().get(BACKFILLING_IDENTIFIER))
			.isSkipOldestEvent();
	}

	/**
	 * Adds a searchable identifier to the transaction's set of identifiers. If the
	 * identifiers set doesn't exist, creates a new one.
	 * @param txn The transaction detail object to update
	 * @param identifier The identifier string to add
	 */
	public static void addSearchableIdentifier(final TransformedTransactionHistoryDetail txn, String identifier) {
		log.debug("Adding identifier for transaction ID: {}", txn.getTxnId());

		Set<String> identifiers = txn.getSearchAbleIdentifiers();
		if (identifiers == null) {
			log.debug("No existing identifiers found, creating new set for transaction ID: {}", txn.getTxnId());
			identifiers = new HashSet<>();
		}

		identifiers.add(identifier);
		txn.setSearchAbleIdentifiers(identifiers);
		log.debug("Added {} identifier to transaction ID: {}", identifier, txn.getTxnId());
	}

	/**
	 * Removes a searchable identifier from the transaction's set of identifiers. Performs
	 * validation checks before removal: - Checks if identifiers set exists and is not
	 * empty - Checks if the specified identifier exists in the set
	 * @param txn The transaction detail object to update
	 * @param identifier The identifier string to remove
	 */
	public static void removeSearchableIdentifier(final TransformedTransactionHistoryDetail txn, String identifier) {
		log.debug("Removing identifier for transaction ID: {}", txn.getTxnId());

		Set<String> identifiers = txn.getSearchAbleIdentifiers();
		if (identifiers == null || identifiers.isEmpty()) {
			log.warn("No identifiers found to remove for transaction ID: {}", txn.getTxnId());
			return;
		}

		if (!identifiers.contains(identifier)) {
			log.warn("Identifier {} not found for transaction ID: {}", identifier, txn.getTxnId());
			return;
		}

		identifiers.remove(identifier);
		txn.setSearchAbleIdentifiers(identifiers);
		log.debug("Removed {} identifier from transaction ID: {}", identifier, txn.getTxnId());
	}

	// This method is used to get the transaction indicator for UTH use cases.
	public static TransactionIndicator getUthVisibleTransactionIndicator(
			final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd)) {
			return null;
		}

		TransactionIndicator transactionIndicator = TransactionIndicator
			.getTransactionIndicatorEnumByKey(tthd.getTxnIndicator());
		TransactionTypeEnum transactionTypeEnum = TransactionTypeEnum
			.getTransactionTypeEnumByKey(tthd.getMainTxnType());
		switch (transactionTypeEnum) {
			case ADD_MONEY_TO_UPI_LITE:
			case DEACTIVATION_OF_UPI_LITE:
			case LITE_TOPUP_MANDATE:
				// These transactions are considered as Self Transfer
				return TransactionIndicator.NO_INDICATOR;
			case ADD_MONEY_TO_BANK:
				return TransactionIndicator.CREDIT;
			case IPO_MANDATE:
			case SBMD_MANDATE:
			case ONE_TIME_MANDATE:
				// These transactions are blocked and unblocked so no fund transfer
				if (Boolean.FALSE.equals(PURPOSE_COLLECT.equals(Utility.getPurpose(tthd)))) {
					return TransactionIndicator.NO_INDICATOR;
				}
				return transactionIndicator;
			case P2P_OUTWARD:
			case P2P_INWARD:
				if (Utility.isSelfTransferTxn(tthd)) {
					// This is self transfer txns
					return TransactionIndicator.NO_INDICATOR;
				}
			default:
				// for default returning what source system sent.
				return transactionIndicator;
		}
	}

	// Method to check if txn is mandate no fund transfer txn or not if txn is in IPO, One
	// Time and SBMD and txnPurpose is not collect
	public static boolean isMandateNoFundTransferTxn(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn)) {
			return false;
		}

		TransactionTypeEnum transactionTypeEnum = TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getTxnType());

		// Mandate No Fund Transfer txn if txn is in IPO, One Time and SBMD and txnPurpose
		// is not collect
		return transactionTypeEnum != null
				&& (transactionTypeEnum.equals(TransactionTypeEnum.IPO_MANDATE)
						|| transactionTypeEnum.equals(TransactionTypeEnum.SBMD_MANDATE)
						|| transactionTypeEnum.equals(TransactionTypeEnum.ONE_TIME_MANDATE))
				&& Boolean.FALSE.equals(PURPOSE_COLLECT.equals(Utility.getPurpose(txn)));

	}

	public static JsonNode mergeJsonNodes(final JsonNode baseNode, final JsonNode overrideNode) {
		if (baseNode == null) {
			return overrideNode;
		}
		if (overrideNode == null) {
			return baseNode;
		}
		if (baseNode instanceof ObjectNode && overrideNode instanceof ObjectNode) {
			ObjectNode merged = (ObjectNode) baseNode;
			overrideNode.fields().forEachRemaining(entry -> {
				String key = entry.getKey();
				JsonNode value = entry.getValue();
				JsonNode baseValue = merged.get(key);

				if (baseValue != null && baseValue.isObject() && value.isObject()) {
					merged.set(key, mergeJsonNodes(baseValue, value));
				}
				else {
					merged.set(key, value);
				}
			});
			return merged;
		}
		return overrideNode;
	}

	/**
	 * Extracts the PSP handle from a VPA. For example, if VPA is "user@okicici", it
	 * returns "@okicici"
	 * @param vpa The VPA to extract handle from
	 * @return The PSP handle with @ prefix, or null if VPA is invalid
	 */
	public static String getPspHandle(String vpa) {
		try {
			if (vpa == null || !vpa.contains("@")) {
				log.warn("Invalid VPA format: {}", vpa);
				return null;
			}
			String pspHandle = vpa.split("@")[1];
			if (pspHandle != null && pspHandle.trim().isEmpty()) {
				log.warn("PSP handle is empty for VPA: {}", vpa);
				return null;
			}
			return pspHandle;
		}
		catch (Exception e) {
			log.error("Error while extracting PSP handle from VPA: {}, {}", vpa, CommonsUtility.exceptionFormatter(e));
			return null;
		}
	}

	/**
	 * Safely fetches a value from the contextMap of TransactionHistoryDetails.
	 * @param thd TransactionHistoryDetails object
	 * @param key The key to fetch from contextMap
	 * @return The value for the key, or null if not present
	 */
	public static String getContextMapValue(final TransactionHistoryDetails thd, final String key) {
		return (thd != null && thd.getContextMap() != null) ? thd.getContextMap().get(key) : null;
	}

	/**
	 * Creates ParentDetails object from FwdTxnDetails
	 * @param fwdTxnDetails Forward transaction details
	 * @return ParentDetails object or null if fwdTxnDetails is null
	 */
	public static ParentDetails createParentDetailsFromFwdTxn(final FwdTxnDetails fwdTxnDetails) {
		if (fwdTxnDetails == null) {
			return null;
		}

		ParentDetails parentDetails = ParentDetails.builder()
			.txnId(fwdTxnDetails.getTxnId())
			.txnSource(fwdTxnDetails.getTxnSource())
			.amount(fwdTxnDetails.getAmount())
			.rrn(fwdTxnDetails.getRrn())
			.build();

		// if fwd txn date is not long parse able and fwd txn source is UPI then it will
		// be in EEE MMM dd HH:mm:ss z yyyy format. So convert it to epoch
		TransactionSource transactionSource = TransactionSource
			.getTransactionSourceEnumByName(fwdTxnDetails.getTxnSource());
		if (TransactionSource.UPI.equals(transactionSource)
				&& !NumberFormatUtility.isLongParsable(fwdTxnDetails.getTxnDate())) {
			Long txnDateEpochTimeStamp = DateTimeUtility.getEpochMillis(fwdTxnDetails.getTxnDate(),
					"EEE MMM dd HH:mm:ss z yyyy");
			if (txnDateEpochTimeStamp != null) {
				parentDetails.setTxnDate(String.valueOf(txnDateEpochTimeStamp));
			}
		}
		return parentDetails;
	}

}
