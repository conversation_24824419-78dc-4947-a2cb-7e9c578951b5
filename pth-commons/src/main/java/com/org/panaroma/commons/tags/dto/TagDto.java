package com.org.panaroma.commons.tags.dto;

import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.tags.enums.TagConfigurationSource;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
public class TagDto implements Serializable {

	private int id;

	private int txnType;

	private String uthCategories;

	private String dateRange;

	private String receiverPhonebookNames;

	private String amountRanges;

	private String verticalIds;

	private List<String> suggestedTags;

	private String autoTag;

	private String payeeVpa;

	private TagConfigurationSource source;

	private int priority;

	public static TagDto fromDbDto(TagConfigurationEntity dbDto) {
		TagDto tagDto = new TagDto();
		tagDto.setId(dbDto.getId());
		tagDto.setTxnType(dbDto.getTxnType());
		tagDto.setUthCategories(dbDto.getUthCategories());
		tagDto.setDateRange(dbDto.getDateRange());
		tagDto.setReceiverPhonebookNames(dbDto.getReceiverPhonebookNames());
		tagDto.setAmountRanges(dbDto.getAmountRanges());
		tagDto.setVerticalIds(dbDto.getVerticalIds());
		tagDto.setSuggestedTags(Stream.of(dbDto.getSuggestedTags().split(","))
			.map(String::trim)
			.filter(StringUtils::isNotBlank)
			.collect(Collectors.toList()));
		tagDto.setAutoTag(dbDto.getAutoTag());
		tagDto.setPriority(dbDto.getPriority());
		tagDto.setSource(TagConfigurationSource.DB);
		return tagDto;
	}

}
