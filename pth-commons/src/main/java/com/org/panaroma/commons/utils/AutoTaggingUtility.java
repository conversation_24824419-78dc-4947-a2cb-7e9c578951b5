package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.Constants.DASH;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.isP2PTxnType;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.isP2pInwardTxnType;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.isP2pOutwardTxnType;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.tag.AutoTagSecondPartyType;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

@Log4j2
public class AutoTaggingUtility {

	/**
	 * Retrieves the secondary party ID for auto-tagging purposes. it could be verticalId
	 * entityId, merchantId, vpa or accRefId
	 *
	 * see PTH-228 Auto-tagging implementation
	 * @param tthd The transformed transaction history detail object
	 * @return The secondary party ID, or null if not found
	 */
	public static String getSecPartyIdForAutoTagging(final TransformedTransactionHistoryDetail tthd) {
		String secPartyId = null;

		// Get the secondary party participant
		TransformedParticipant secPartyParticipant = Utility.getSecPartyParticipant(tthd);
		// If the secondary party participant is null or the transaction is a self
		// transfer, then return null
		if (secPartyParticipant == null || Utility.isSelfTransferTxn(tthd)) {
			return null;
		}

		// If the transaction is an OMS Source transaction, then get the verticalId
		if (TransactionSource.OMS.getTransactionSourceKey().equals(tthd.getStreamSource())) {
			secPartyId = getVerticalId(tthd);
		}
		// if verticalId is present then send verticalId as secPartyId
		if (StringUtils.isNotBlank(secPartyId)) {
			return secPartyId;
		}

		// if verticalId is not present then check for other possible second party Id like
		// entityId, merchantId, vpa or accRefId
		if (StringUtils.isNotBlank(secPartyParticipant.getEntityId())) {
			// If the secondary party participant has an entityId, then get the entityId
			// It could be merchantId or sec party userId
			secPartyId = secPartyParticipant.getEntityId();
		}
		else if (Objects.nonNull(secPartyParticipant.getBankData())
				&& StringUtils.isNotBlank(secPartyParticipant.getBankData().getAccRefNum())) {
			// If the secondary party participant has a bank data and the account
			// reference number is not blank, then get the account reference number
			secPartyId = secPartyParticipant.getBankData().getAccRefNum();
		}
		else if (Objects.nonNull(secPartyParticipant.getUpiData())
				&& StringUtils.isNotBlank(secPartyParticipant.getUpiData().getVpa())) {
			// If the secondary party participant has a UPI data and the VPA is not blank,
			// then get the VPA, it could be merchant vpa or sec party user vpa
			secPartyId = secPartyParticipant.getUpiData().getVpa();
		}

		return secPartyId;
	}

	/**
	 * Retrieves the secondary party Type for auto-tagging purposes. it could be User,
	 * Merchant, ONUS Merchant and Other
	 *
	 * see PTH-228 Auto-tagging implementation
	 * @param tthd The transformed transaction history detail object
	 * @return The secondary party ID, or null if not found
	 */
	public static Integer getSecPartyTypeKeyForAutoTagging(final TransformedTransactionHistoryDetail tthd) {

		// Get the secondary party participant
		TransformedParticipant secPartyParticipant = Utility.getSecPartyParticipant(tthd);
		// If the secondary party participant is null or the transaction is a self
		// transfer, then return null
		if (secPartyParticipant == null || Utility.isSelfTransferTxn(tthd)) {
			return null;
		}

		// If the transaction is an OMS transaction, and vertical id is not blank then
		// it's ONUS Merchant
		if (TransactionSource.OMS.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& StringUtils.isNotBlank(getVerticalId(tthd))) {
			return AutoTagSecondPartyType.ONUS_MERCHANT.getSecPartyTypeKey();
		}

		if (StringUtils.isNotBlank(secPartyParticipant.getEntityId())) {
			// If the secondary party participant has an entityId, then second party type
			// would User or Merchant
			if (isP2PTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()))) {
				return AutoTagSecondPartyType.USER.getSecPartyTypeKey();
			}
			else {
				return AutoTagSecondPartyType.MERCHANT.getSecPartyTypeKey();
			}
		}
		else if (Objects.nonNull(secPartyParticipant.getBankData())
				&& StringUtils.isNotBlank(secPartyParticipant.getBankData().getAccRefNum())) {
			if (isP2PTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()))) {
				return AutoTagSecondPartyType.USER.getSecPartyTypeKey();
			}
			else {
				return AutoTagSecondPartyType.MERCHANT.getSecPartyTypeKey();
			}
		}
		else if (Objects.nonNull(secPartyParticipant.getUpiData())
				&& StringUtils.isNotBlank(secPartyParticipant.getUpiData().getVpa())) {
			if (isP2PTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()))) {
				return AutoTagSecondPartyType.USER.getSecPartyTypeKey();
			}
			else {
				return AutoTagSecondPartyType.MERCHANT.getSecPartyTypeKey();
			}
		}

		return AutoTagSecondPartyType.OTHER.getSecPartyTypeKey();
	}

	// Method to get the verticalId from the transaction history detail
	private static String getVerticalId(final TransformedTransactionHistoryDetail tthd) {
		String verticalId = null;
		if (tthd != null && tthd.getCartDetails() != null && tthd.getCartDetails().getItems() != null
				&& !tthd.getCartDetails().getItems().isEmpty()) {
			verticalId = tthd.getCartDetails().getItems().get(0).getVerticalId();
		}
		return verticalId;
	}

	// Method to get the auto tagging aerospike key
	public static String getAutoTaggingAerospikeKey(final String userId, final String secPartyId,
			final Integer secPartyType) {
		return userId + DASH + secPartyId + DASH + secPartyType;
	}

	// Method to check self transfer transaction Self Transfer would if self transfer flag
	// is there or Add money to upi lite or deactivation of upi lite
	public static boolean isSelfTransferTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && (Utility.isSelfTransferTxn(txn)
				|| TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.getTransactionTypeKey().equals(txn.getTxnType())
				|| TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.getTransactionTypeKey().equals(txn.getTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey().equals(txn.getTxnType()));
	}

	// Method to get the UthCategoryEnum for auto tagging
	public static UthCategoryEnum getUthCategory(final TransformedTransactionHistoryDetail txn) {
		// If the transaction is null, then return null
		if (Objects.isNull(txn)) {
			return null;
		}

		// if txn is self txn then return self transfer category
		if (isSelfTransferTxn(txn)) {
			return UthCategoryEnum.SELF_TRANSFER;
		}

		if (isP2pOutwardTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getTxnType()))) {
			// If the transaction is a P2P Outward transaction, then return the
			// TRANSFERS category
			return UthCategoryEnum.TRANSFERS;
		}
		else if (isP2pInwardTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getTxnType()))) {
			// If the transaction is a P2P Inward transaction, then return the
			// TRANSFERS_INWARD category
			return UthCategoryEnum.TRANSFERS_INWARD;
		}

		else if (TransactionTypeEnum
			.isCashbackTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getTxnType()))) {
			// If the transaction is a cashback transaction, then return the
			// CASHBACK_RECEIVED category
			return UthCategoryEnum.CASHBACK_RECEIVED;
		}

		// If the transaction is not a P2P or cashback transaction, then get the
		// UthCategoryEnum
		UthCategoryEnum category = UtilityExtension.getUthCategoryEnum(txn);

		// If the category is not null, then return the category, otherwise return the
		// OTHERS category
		return Objects.nonNull(category) ? category : UthCategoryEnum.OTHERS;
	}

	/*
	 * Checks if auto-tagging is applicable for the given transaction. Auto-tagging is
	 * considered applicable if the transaction is not null, and no tag is already applied
	 * on this txn has a status of SUCCESS, is marked to be shown in the listing, and is
	 * visible. for OMS txn, auto tagging is applicable if status is SUCCESS
	 */
	public static boolean isAutoTaggingApplicableForTxn(final TransformedTransactionHistoryDetail txn) {

		if (txn == null) {
			return false;
		}

		// if tags are already applied on this txn then it's not applicable for auto
		// tagging
		if (Objects.nonNull(txn.getTags()) && !txn.getTags().isEmpty()) {
			return false;
		}

		// Handling OMS if OMS event 1st come then it's show In Listing would be null
		// until UPI receives. So setting auto Tag without checking showInListing and
		// visibility
		if (TransactionSource.OMS.getTransactionSourceKey().equals(txn.getStreamSource())
				&& ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus())) {
			return true;
		}

		return isTaggingApplicableForThisTxn(txn);
	}

	public static boolean isTaggingApplicableForThisTxn(final TransformedTransactionHistoryDetail txn) {
		if (txn == null) {
			return false;
		}

		// If txn is mandate no fund transfer txn then tagging is not applicable on this
		// txn
		if (Utility.isMandateNoFundTransferTxn(txn)) {
			return false;
		}

		// txn status is success and visibility is true then tagging is applicable
		return ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus())
				&& Boolean.TRUE.equals(txn.getShowInListing()) && Boolean.TRUE.equals(txn.getIsVisible());
	}

}
