package com.org.panaroma.commons.kafka.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.cache.dto.NonTransactingUserCacheData;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.enums.CacheInfo;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@JsonInclude(value = JsonInclude.Include.NON_EMPTY, content = JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class CacheUpdaterKafkaDto implements Serializable {

	private String uid;

	/*
	 * Below map will contain cache info to cache key specific mapping Key -> metadata for
	 * cache which is to be updated Value -> Cache Key
	 */
	private Map<CacheInfo, String> cacheMetaInfo;

	private String entityId;

	private NonTransactingUserCacheData nonTransactingUserCacheData;

	private Boolean isNonTransactingUser;

	private RepoResponseSearchApiDto listingData;

	private AppSideCacheData appSideCacheData;

	private String originator;

	private Long createdDate;

	@EqualsAndHashCode.Exclude
	private Map<String, String> metaInfo;

	public void putInMetaInfo(final String key, final String value) {
		if (metaInfo == null) {
			metaInfo = new HashMap<>();
		}
		metaInfo.put(key, value);
	}

}
