package com.org.panaroma.commons.repository.es;

import static com.org.panaroma.commons.constants.CommonConstants.DISTINCT_ENTITY_ID;
import static com.org.panaroma.commons.constants.CommonConstants.ENTITY_ID;
import static com.org.panaroma.commons.constants.CommonConstants.FROM_DATE;
import static com.org.panaroma.commons.constants.CommonConstants.SEARCH_CONTEXT_TO_ES_MAPPINGS;
import static com.org.panaroma.commons.constants.CommonConstants.STREAM_SOURCE;
import static com.org.panaroma.commons.constants.CommonConstants.TO_DATE;
import static com.org.panaroma.commons.constants.CommonConstants.TRANSACTION_DATE_EPOCH_TIME;
import static com.org.panaroma.commons.constants.CommonConstants.TRANSACTION_ID;
import static com.org.panaroma.commons.constants.Constants.ASTERISK;
import static com.org.panaroma.commons.constants.Constants.DASH;
import static com.org.panaroma.commons.constants.Constants.INDIAN_ZONE;
import static com.org.panaroma.commons.constants.Constants.PARTICIPANTS;
import static com.org.panaroma.commons.constants.Constants.TXN_DATE;

import com.org.panaroma.commons.constants.Constants;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.SearchCriteriaEnum;
import com.org.panaroma.commons.dto.analytics.UserIdFetcherDto;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.ExistenceBasedFilter;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Pair;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class EsUtility {

	// private static FilterTypesFactory filterTypesFactory;

	/*
	 * @Autowired public EsUtility(final FilterTypesFactory filterTypesFactory) {
	 * this.filterTypesFactory = filterTypesFactory; }
	 */

	/**
	 * Builds Elasticsearch query based on search context parameters
	 * @param searchContext Contains search criteria and filters
	 * @return QueryBuilder with all applied filters and conditions
	 */
	public static QueryBuilder getQueryBuilderForDesiredParameters(final SearchContext searchContext) {
		return getBoolQueryBuilderForDesiredParameters(searchContext);
	}

	public static BoolQueryBuilder getBoolQueryBuilderForDesiredParameters(final SearchContext searchContext) {
		log.debug("Building ES query for search context: {}", searchContext);
		BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

		// Add date range filter if dates are provided
		if (Objects.nonNull(searchContext.getFromDate()) && Objects.nonNull(searchContext.getToDate())) {
			log.debug("Adding date range filter: from={}, to={}", searchContext.getFromDate(),
					searchContext.getToDate());
			queryBuilder.filter(QueryBuilders.rangeQuery(TRANSACTION_DATE_EPOCH_TIME)
				.gte(searchContext.getFromDate())
				.lte(searchContext.getToDate()));
		}

		Iterator<Pair<SearchCriteriaEnum, Object>> searchContextIterator = searchContext.iterator();

		try {
			while (searchContextIterator.hasNext()) {
				Pair<SearchCriteriaEnum, Object> pair = searchContextIterator.next();
				if (Objects.nonNull(pair)) {
					// Handle UPI statement filter
					if (isUpiStatementFilter(pair)) {
						log.debug("Adding UPI statement filter");
						queryBuilder.filter(getQueryForUpiStatement());
					}

					// Handle non-excluded search criteria
					if (isNonExcludedSearchCriteria(pair)) {
						log.debug("Adding filter for criteria: {}, value: {}", pair.getKey(), pair.getValue());
						if (pair.getKey().getQueryType().equals("mustNotMatch")) {
							queryBuilder.mustNot(QueryBuilders.termQuery(
									SEARCH_CONTEXT_TO_ES_MAPPINGS.get(pair.getKey().toString()), pair.getValue()));
						}
						else {
							QueryBuilder queryBuilderForCurrentParameter = getQueryBuilder(pair.getKey(),
									pair.getValue());
							queryBuilder.filter(queryBuilderForCurrentParameter);
						}
					}

					// Handle existence based filters
					handleExistenceBasedFilter(queryBuilder, pair);
				}
			}
		}
		catch (Exception e) {
			log.error("Failed to build ES query. Search context: {}, Error: {}", searchContext,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}

		log.debug("Final query built: {}", queryBuilder);
		return queryBuilder;
	}

	/**
	 * Helper methods to improve readability of filter conditions
	 */
	private static boolean isUpiStatementFilter(Pair<SearchCriteriaEnum, Object> pair) {
		return pair.getValue() != null && Boolean.TRUE.equals(pair.getKey().isExcluded())
				&& SearchCriteriaEnum.statementFilter.equals(pair.getKey());
	}

	private static boolean isNonExcludedSearchCriteria(Pair<SearchCriteriaEnum, Object> pair) {
		return pair.getValue() != null && Boolean.FALSE.equals(pair.getKey().isExcluded());
	}

	/**
	 * Handles existence-based filters for Elasticsearch queries. This method processes
	 * filters that check whether certain fields exist or don't exist in documents.
	 * @param queryBuilder The bool query builder to add filter conditions to
	 * @param pair The search criteria and value pair to process
	 */
	private static void handleExistenceBasedFilter(BoolQueryBuilder queryBuilder,
			Pair<SearchCriteriaEnum, Object> pair) {
		ExistenceBasedFilter filter = ExistenceBasedFilter.getFilter(pair.getKey());

		if (filter == null) {
			log.debug("No existence-based filter found for key: {}", pair.getKey());
			return;
		}

		if (!(pair.getValue() instanceof Boolean)) {
			log.warn("Invalid value type for existence filter. Expected Boolean, got: {}",
					pair.getValue() != null ? pair.getValue().getClass().getSimpleName() : "null");
			return;
		}

		Boolean value = (Boolean) pair.getValue();
		log.debug("Processing existence filter: {}, field: {}, value: {}", filter.getDescription(),
				filter.getFieldPath(), value);

		// Special handling for SEARCH_FIELDS filter which has inverted logic
		/*
		 * filter == SEARCH_FIELDS | value | Result |
		 * |-------------------------|-------|------------| | true | true | false | | true
		 * | false | true | | false | true | true | | false | false | false |
		 */
		boolean shouldFieldExist = (filter == ExistenceBasedFilter.SEARCH_FIELDS) != value;

		// Create the existence query
		QueryBuilder existenceQuery = QueryBuilders.existsQuery(filter.getFieldPath());

		if (shouldFieldExist) {
			log.debug("Adding MUST exist condition for field: {}", filter.getFieldPath());
			queryBuilder.must(existenceQuery);
		}
		else {
			log.debug("Adding MUST NOT exist condition for field: {}", filter.getFieldPath());
			queryBuilder.mustNot(existenceQuery);
		}

		log.trace("Updated query builder after existence filter: {}", queryBuilder);
	}

	private static QueryBuilder getQueryForUpiStatement() {
		return QueryBuilders.termsQuery("searchFields.searchPaymentSystem.keyword",
				PaymentSystemEnum.UPI.getPaymentSystemKey().toString(),
				PaymentSystemEnum.UPI_LITE.getPaymentSystemKey().toString());
	}

	/*
	 * private static List<QueryBuilder> getQueryForFilters(final SearchContext
	 * searchContext, final SearchCriteriaEnum key) {
	 * log.debug("Getting query for Filter {}", key); IFilterType filterHandler =
	 * filterTypesFactory.getFilterHandler(key.getFilterTypeIdentifier()); if
	 * (filterHandler == null) {
	 * log.error("No Filter Handler found for this filterType : {}",
	 * key.getFilterTypeIdentifier()); throw new
	 * RuntimeException("No Filter Handler found for filterType : " +
	 * key.getFilterTypeIdentifier() + " " + QUERY_EXCEPTION); } List<QueryBuilder>
	 * queryBuilders = filterHandler.getQueryForFilter(searchContext);
	 * log.debug("queryBuilders from filters method - {}", queryBuilders); return
	 * queryBuilders; }
	 */

	public static SearchSourceBuilder createSearchSourceBuilder(final SearchContext searchContext,
			final PaginationParams paginationParams, final boolean sortInAscending) {
		SearchSourceBuilder searchSourceBuilder = createSearchSourceBuilder(searchContext, paginationParams);

		// set Sorting
		setSortParamInSearchRequest(searchSourceBuilder, sortInAscending, paginationParams);
		return searchSourceBuilder;
	}

	public static SearchSourceBuilder createSearchSourceBuilder(final SearchContext searchContext,
			final PaginationParams paginationParams) {
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		searchSourceBuilder.size(searchContext.getPageSize() + 1); // to check the next
																	// page
		QueryBuilder queryBuilder = getQueryBuilderForDesiredParameters(searchContext);
		searchSourceBuilder.query(queryBuilder);

		if (ObjectUtils.isNotEmpty(paginationParams)) {
			log.info("Search After called with parameters :{}", paginationParams);
			List<Object> searchAfterParams = Stream
				.of(paginationParams.getTransactionDateEpoch(), paginationParams.getPaginationTxnId())
				.collect(Collectors.toCollection(ArrayList::new));
			if (StringUtils.isNotBlank(paginationParams.getPaginationStreamSource())) {
				searchAfterParams.add(paginationParams.getPaginationStreamSource());
			}
			searchSourceBuilder.searchAfter(searchAfterParams.toArray());
		}
		return searchSourceBuilder;
	}

	private static QueryBuilder getQueryBuilder(final SearchCriteriaEnum key, final Object value) {
		QueryBuilder queryBuilder;
		BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
		log.debug("trying to create query for key :{}, value :{}", key, value);
		switch (key.getQueryType()) {
			case "term":
				queryBuilder = QueryBuilders.termQuery(SEARCH_CONTEXT_TO_ES_MAPPINGS.get(key.toString()), value);
				break;
			case "nested":
				queryBuilder = QueryBuilders.nestedQuery(key.getNestedPath(),
						QueryBuilders.termQuery(SEARCH_CONTEXT_TO_ES_MAPPINGS.get(key.toString()), value),
						ScoreMode.None);
				break;
			case "regexpNested":
				String regexp = ".*" + value + ".*";
				queryBuilder = QueryBuilders.nestedQuery(key.getNestedPath(),
						QueryBuilders.regexpQuery(SEARCH_CONTEXT_TO_ES_MAPPINGS.get(key.toString()), regexp),
						ScoreMode.None);
				break;

			case "multiValuedSearchNested":
				for (String val : ((String) value).split(",")) {
					boolQueryBuilder
						.should(QueryBuilders.termQuery(SEARCH_CONTEXT_TO_ES_MAPPINGS.get(key.toString()), val));
				}
				boolQueryBuilder.minimumShouldMatch(1);
				queryBuilder = QueryBuilders.nestedQuery(key.getNestedPath(), boolQueryBuilder, ScoreMode.None);
				break;

			case "multiValuedSearch":
				for (String val : ((String) value).split(",")) {
					boolQueryBuilder
						.should(QueryBuilders.termQuery(SEARCH_CONTEXT_TO_ES_MAPPINGS.get(key.toString()), val));
				}
				boolQueryBuilder.minimumShouldMatch(1);
				queryBuilder = boolQueryBuilder;
				break;

			case "multiTermsSearch":
				List<String> values = (List) value;
				queryBuilder = QueryBuilders.termsQuery(SEARCH_CONTEXT_TO_ES_MAPPINGS.get(key.toString()),
						values.toArray());
				break;

			default:
				log.error("unexpected query type for query creation. key : {}", key);
				throw new IllegalStateException("Unexpected value: " + key.getQueryType());
		}
		return queryBuilder;
	}

	public static BoolQueryBuilder getQueryBuilder(final Map<String, Object> paramMap,
			final TransformedTransactionHistoryDetail tthd) {
		BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
		Object fromDate = null;
		Object toDate = null;
		try {
			setNestedQueryData(paramMap, queryBuilder, tthd);
			for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
				if (entry.getValue() == null) {
					log.warn("Null value found for entry:{} for ParamMap:{} for txnId : {}", entry, paramMap,
							tthd.getTxnId());
					return null;
				}
				else if (entry.getKey().equalsIgnoreCase(FROM_DATE)) {
					fromDate = entry.getValue();
				}
				else if (entry.getKey().equalsIgnoreCase(TO_DATE)) {
					toDate = entry.getValue();
				}
				else if (!entry.getKey().contains(".") && entry.getValue() instanceof String
						&& ((String) entry.getValue()).contains(",")) {
					String[] values = ((String) entry.getValue()).split(",");
					queryBuilder.filter(QueryBuilders.termsQuery(entry.getKey(), values));
				}
				else if (!entry.getKey().contains(".")) {
					queryBuilder.filter(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
				}
			}
			if (fromDate != null && toDate != null) {
				queryBuilder.filter(QueryBuilders.rangeQuery(TXN_DATE).gte(fromDate).lte(toDate));
			}
		}
		catch (Exception e) {
			log.warn("Exception while creating query : {} , for txnId : {}", CommonsUtility.exceptionFormatter(e),
					tthd.getTxnId());
			return null;
		}
		return queryBuilder;
	}

	private static void setNestedQueryData(final Map<String, Object> paramMap, final BoolQueryBuilder queryBuilder,
			final TransformedTransactionHistoryDetail tthd) throws Exception {
		BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
		for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
			if (entry.getValue() == null) {
				log.debug("value is null for ES query : {}, for txnid : {}", paramMap, tthd.getTxnId());
				throw new Exception("value is null for ES query");
			}
			if (entry.getKey().contains(".")) {
				boolQueryBuilder.must().add(QueryBuilders.termQuery(entry.getKey(), paramMap.get(entry.getKey())));
			}
		}
		NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery(PARTICIPANTS, boolQueryBuilder,
				ScoreMode.None);
		queryBuilder.filter().add(nestedQueryBuilder);
	}

	public static void setSortParamInSearchRequest(final SearchSourceBuilder searchSourceBuilder,
			final boolean sortInAscending, final PaginationParams paginationParams) {

		searchSourceBuilder.sort(new FieldSortBuilder(TRANSACTION_DATE_EPOCH_TIME)
			.order(sortInAscending ? SortOrder.ASC : SortOrder.DESC));
		searchSourceBuilder.sort(new FieldSortBuilder(TRANSACTION_ID).order(SortOrder.DESC));
		if (Objects.nonNull(paginationParams) && StringUtils.isNotBlank(paginationParams.getPaginationStreamSource())) {
			searchSourceBuilder.sort(new FieldSortBuilder(STREAM_SOURCE).order(SortOrder.DESC));
		}
	}

	public static String getIndices(final SearchContext searchContext, final String aliasName,
			final String indexNamePrefix) {
		return aliasName;
	}

	public static List<String> getMonthWiseIndicesInTimeRange(final long fromDate, final long toDate,
			final String esIndexPrefix) throws Exception {
		ZonedDateTime fromDateZone = Instant.ofEpochMilli(fromDate).atZone(ZoneId.of(INDIAN_ZONE));
		ZonedDateTime toDateZone = Instant.ofEpochMilli(toDate).atZone(ZoneId.of(INDIAN_ZONE));
		int gapInYears = toDateZone.getYear() - fromDateZone.getYear();
		List<String> indexes = new ArrayList<>();
		if (gapInYears == 0) {
			if (fromDate > toDate) {
				log.error("fromDate : {} > toDate : {} for progressive period", fromDateZone.toString(),
						toDateZone.toString());
				throw new Exception("fromDate > toDate for progressive period");
			}
			else {
				int gapInMonths = toDateZone.getMonthValue() - fromDateZone.getMonthValue();
				addCurrentYearindices(gapInMonths, indexes, fromDateZone, toDateZone, esIndexPrefix);
			}
		}
		else {
			int iterator = fromDateZone.getMonthValue();
			// this loop iterates over year to fetch all possible month wise indices in
			// the time range
			while (gapInYears > 0) {
				// this loop iterates to fetch all months
				while (iterator <= 12) {
					String stringValueOfMonth = iterator < 10 ? ("0" + iterator) : "" + iterator;
					indexes.add(
							esIndexPrefix + stringValueOfMonth + DASH + (toDateZone.getYear() - gapInYears) + ASTERISK);
					iterator++;
				}
				iterator = 1;
				gapInYears--;
			}

			// left over indices which lies in todate's year i.e january to todate's
			// month, are added here
			// -1 is done to avoid 00-2022 index
			int gapInMonths = toDateZone.getMonthValue() - 1;
			addCurrentYearindices(gapInMonths, indexes, fromDateZone, toDateZone, esIndexPrefix);
		}
		return indexes;
	}

	private static void addCurrentYearindices(final int passedGapInMonths, final List<String> indexes,
			final ZonedDateTime fromDateZone, final ZonedDateTime toDateZone, final String esIndexPrefix) {
		int gapInMonths = passedGapInMonths;
		while (gapInMonths >= 0) {
			int month = toDateZone.getMonthValue() - gapInMonths;
			String stringValueOfMonth = month < 10 ? ("0" + month) : "" + month;
			indexes.add(esIndexPrefix + stringValueOfMonth + DASH + toDateZone.getYear() + ASTERISK);
			gapInMonths--;
		}
	}

	public static SearchRequest createSearchRequestToFetchUniqueUsers(final UserIdFetcherDto userIdFetcherDto,
			final String... aliasNames) {
		List<CompositeValuesSourceBuilder<?>> sourceBuilderList = new ArrayList<>();
		sourceBuilderList.add(new TermsValuesSourceBuilder(DISTINCT_ENTITY_ID).field(ENTITY_ID));
		CompositeAggregationBuilder compositeAggregationBuilder = new CompositeAggregationBuilder(
				Constants.COMPOSITE_USERID, sourceBuilderList)
			.size(Math.toIntExact(userIdFetcherDto.getPageSize()));
		if (Objects.nonNull(userIdFetcherDto.getAfterKeyMap())) {
			compositeAggregationBuilder.aggregateAfter(userIdFetcherDto.getAfterKeyMap());
		}
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		searchSourceBuilder.aggregation(compositeAggregationBuilder);
		String[] includeFields = { TRANSACTION_ID };
		searchSourceBuilder.fetchSource(includeFields, null);
		SearchContext searchContext = new SearchContext();
		searchContext.setPageSize(0);
		searchContext.setFromDate(userIdFetcherDto.getFromDate());
		searchContext.setToDate(userIdFetcherDto.getToDate());
		searchContext.setEsDocViewStatus(String.valueOf(ClientStatusEnum.SUCCESS.getStatusKey()));
		searchContext.setIsVisible(Boolean.TRUE);
		searchContext.setShowInListing(Boolean.TRUE);
		QueryBuilder queryBuilder = getQueryBuilderForDesiredParameters(searchContext);
		searchSourceBuilder.query(queryBuilder);
		searchSourceBuilder.size(0);
		SearchRequest searchRequest = new SearchRequest(aliasNames);
		searchRequest.source(searchSourceBuilder);
		return searchRequest;
	}

	public static SearchSourceBuilder createSearchSourceBuilderForAnyTxn(final SearchContext searchContext,
			final PaginationParams paginationParams, final boolean sortInAscending) {
		return createSearchSourceBuilderForAnyTxn(searchContext, paginationParams);
	}

	public static SearchSourceBuilder createSearchSourceBuilderForAnyTxn(final SearchContext searchContext,
			final PaginationParams paginationParams) {
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		searchSourceBuilder.size(searchContext.getPageSize() + 1); // to check the next
																	// page
		QueryBuilder queryBuilder = getQueryBuilderForDesiredParametersForAnyTxn(searchContext);
		searchSourceBuilder.query(queryBuilder);

		if (paginationParams != null) {
			log.info("Search After called with parameters :{}", paginationParams);
			searchSourceBuilder.searchAfter(
					Arrays
						.asList(paginationParams.getTransactionDateEpoch(), paginationParams.getPaginationTxnId(),
								paginationParams.getPaginationStreamSource())
						.toArray());
		}
		return searchSourceBuilder;
	}

	public static QueryBuilder getQueryBuilderForDesiredParametersForAnyTxn(final SearchContext searchContext) {
		BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

		Iterator<Pair<SearchCriteriaEnum, Object>> searchContextIterator = searchContext.iterator();
		try {
			while (searchContextIterator.hasNext()) {
				Pair<SearchCriteriaEnum, Object> pair = searchContextIterator.next();
				if (Objects.nonNull(pair)) {
					if (pair.getValue() != null && Boolean.FALSE.equals(pair.getKey().isExcluded())) {
						QueryBuilder queryBuilderForCurrentParameter = getQueryBuilder(pair.getKey(), pair.getValue());
						queryBuilder.filter(queryBuilderForCurrentParameter);
					}
				}
			}
		}
		catch (Exception e) {
			log.error("exception while iterating over search context and generating query. Exception :{}",
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
		return queryBuilder;
	}

}