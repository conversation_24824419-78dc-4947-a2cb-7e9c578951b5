package com.org.panaroma.commons.dto.kafkaPushAPI;

import static com.org.panaroma.commons.utils.ToStringUtility.getMaskedMapValueV2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.org.panaroma.commons.dto.AmountBreakup;
import com.org.panaroma.commons.dto.FwdTxnDetails;
import com.org.panaroma.commons.dto.Location;
import com.org.panaroma.commons.dto.RepeatPaymentData;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.apache.avro.reflect.Nullable;

@Data
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransactionHistoryDto implements Serializable {

	@Schema(description = "Source from where we received event like from PG or UPI")
	@Nullable
	TransactionSourceDto streamSource;

	@Schema(description = "Source from where the transaction is initiated")
	@Nullable
	TransactionSourceDto sourceSystem;

	@Schema(description = "Transaction Amount")
	@Nullable
	String amount;

	@Schema(description = "Amounts currency")
	@Nullable
	String currency;

	@Schema(description = "Remark")
	@Nullable
	String remarks;

	@Schema(description = "Transaction status")
	@Nullable
	String status;

	@Schema(description = "List of tag applied on transaction")
	@Nullable
	List<TagsDto> tags;

	@Nullable
	String umn;

	@Schema(description = "System id of the transaction")
	@Nullable
	String systemId;

	@Schema(description = "Source System id of the transaction")
	@Nullable
	String sourceSystemId;

	@Schema(description = "Parent Transaction id of the transaction")
	@Nullable
	String parentTxnId;

	@Schema(description = "Transaction type of the transaction")
	@Nullable
	String txnType;

	@Schema(description = "Date of transaction")
	@Nullable
	String txnDate;

	@Schema(description = "Last updated status date of the transaction")
	@Nullable
	String updatedDate;

	@Schema(description = "Location from where transaction happend")
	@Nullable
	Location location;

	@Schema(description = "Contains information received from source system like rrn")
	@Nullable
	@JsonInclude(Include.NON_NULL)
	Map<String, Object> contextMap;

	@Schema(description = "Contains Url link for showing Pay Again button")
	@Nullable
	RepeatPaymentData repeatPaymentData;

	@Schema(description = "List of participants involved")
	@Nullable
	List<TxnParticipantDto> participants;

	@Nullable
	String notesKey;

	@Schema(description = "Contains information about amount breakup")
	@Nullable
	AmountBreakup amountBreakup;

	@Schema(description = "Contains information about chat data")
	@Nullable
	@JsonInclude(Include.NON_NULL)
	Map<String, String> chatData;

	@Schema(description = "Contains information about participants not available")
	@Nullable
	@JsonInclude(Include.NON_NULL)
	boolean participantsNotAvailable;

	@Schema(description = "Contains information about forward transaction details")
	@Nullable
	FwdTxnDetails fwdTxnDetails;

	@Override
	public String toString() {
		return "TransactionHistoryDTO{" + "'streamSource':'" + streamSource + "', " + "'sourceSystem':'" + sourceSystem
				+ "', " + "'amount':'" + "xxxx" + "', " + "'notesKey':'" + notesKey + "', " + "'currency':'" + currency
				+ "', " + "'remarks':'" + remarks + "', " + "'status':'" + status + "', " + "'tags':'" + tags + "', "
				+ "'umn':'" + umn + "', " + "'systemId':'" + systemId + "', " + "'sourceSystemId':'" + sourceSystemId
				+ "', " + "'parentTxnId':'" + parentTxnId + "', " + "'txnType':'" + txnType + "', " + "'txnDate':'"
				+ txnDate + "', " + "'updatedDate':'" + updatedDate + "', " + "'location':'" + location + "', "
				+ "'contextMap':'" + getMaskedMapValueV2(contextMap) + "', " + "'contextMap.errorMessage':'"
				+ (contextMap == null ? "" : contextMap.get("errorMessage")) + "', " + "'repeatPaymentData':'"
				+ repeatPaymentData + "', " + "'participants':'" + participants + "', " + "'amountBreakup':'"
				+ amountBreakup + "'" + "'chatData':'" + chatData + "'" + "'participantsNotAvailable':'"
				+ participantsNotAvailable + "'" + "'fwdTxnDetails':'" + fwdTxnDetails + "'" + "}";
	}

}