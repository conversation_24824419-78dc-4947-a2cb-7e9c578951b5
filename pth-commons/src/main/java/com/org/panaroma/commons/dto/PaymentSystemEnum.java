package com.org.panaroma.commons.dto;

import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum PaymentSystemEnum {

	WALLET(1, "wallet", "Wallet"), BANK(2, "bank", "Paytm Payments Bank"), UPI(3, "upi", "UPI"), PG(4, "pg"),
	PAYTM_POSTPAID(5, "paytm_postpaid", "Postpaid Loan"), MGV(6, "mgv", "Merchant Voucher"), OTHER(7, "other"),
	PPBL(8, "ppbl", "Paytm Payments Bank"), TS(9, "ts"), UPI_LITE(10, "upi_lite", "UPI Lite"),
	STORE_CASH(11, "store_cash", "Store Voucher"), UPI_CREDIT_CARD(12, "upi_credit_card", "UPI linked Credit Card"),
	EMI(13, "emi", "EMI"), LOYALTY_POINT(14, "loyalty_point", "Loyalty Points"),
	BANK_MANDATE(15, "bank_mandate", "Bank Mandate"), PAYTM_CASH(16, "paytm_cash", "Paytm Cash"),
	EMI_DEBIT_CARD(17, "emi_debit_card", "EMI Debit Card"), NET_BANKING(18, "net_banking", "Net Banking"),
	// TODO: Consider changing paymentSystemUserViewValue when filter support for GV is
	// implemented
	GV(19, "gv", "Gift Voucher");

	private final Integer paymentSystemKey;

	private final String paymentSystemValue;

	private final String paymentSystemUserViewValue;

	private static Map<String, PaymentSystemEnum> paymentSystemValueMap = null;

	private static Map<Integer, PaymentSystemEnum> paymentSystemIdMap = null;

	static {
		paymentSystemValueMap = new HashMap<>();
		paymentSystemIdMap = new HashMap<>();
		for (PaymentSystemEnum paymentSystemEnum : PaymentSystemEnum.values()) {
			paymentSystemValueMap.put(paymentSystemEnum.paymentSystemValue, paymentSystemEnum);
			paymentSystemIdMap.put(paymentSystemEnum.paymentSystemKey, paymentSystemEnum);
		}
	}

	PaymentSystemEnum(final Integer paymentSystemKey, final String paymentSystemValue) {
		this(paymentSystemKey, paymentSystemValue, paymentSystemValue);
	}

	PaymentSystemEnum(final Integer paymentSystemKey, final String paymentSystemValue,
			final String paymentSystemUserViewValue) {
		this.paymentSystemKey = paymentSystemKey;
		this.paymentSystemValue = paymentSystemValue;
		this.paymentSystemUserViewValue = paymentSystemUserViewValue;
	}

	public static PaymentSystemEnum getPaymentSystemEnumByValue(final String paymentSystemValue) {
		for (PaymentSystemEnum paymentSystemEnum : PaymentSystemEnum.values()) {
			if (paymentSystemEnum.paymentSystemValue.equalsIgnoreCase(paymentSystemValue)) {
				return paymentSystemEnum;
			}
		}
		return null;
	}

	public static PaymentSystemEnum getPaymentSystemEnumByKey(final Integer paymentSystemKey) {
		for (PaymentSystemEnum paymentSystemEnum : PaymentSystemEnum.values()) {
			if (paymentSystemEnum.paymentSystemKey.equals(paymentSystemKey)) {
				return paymentSystemEnum;
			}
		}
		return null;
	}

	public static PaymentSystemEnum getEnumFromPaymentSystemValue(final String paymentSystemValue) {
		if (StringUtils.isBlank(paymentSystemValue)) {
			return null;
		}
		return paymentSystemValueMap.get(paymentSystemValue.toLowerCase());
	}

	public static PaymentSystemEnum getEnumFromPaymentSystemKey(final Integer paymentSystemKey) {
		if (ObjectUtils.isEmpty(paymentSystemKey)) {
			return null;
		}
		return paymentSystemIdMap.get(paymentSystemKey);
	}

}
