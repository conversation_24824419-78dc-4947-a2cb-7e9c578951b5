package com.org.panaroma.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MandateMissingDataIdentifierEnum {

	RRN_MISSING(1), ERROR_CODE_MISSING_FOR_FAILURE_EVENT(2), EXECUTION_NO_MISSING_FOR_COLLECT_EVENT(3),
	PAUSE_END_DATE_MISSING_FOR_PAUSE_EVENT(4), VALIDITY_START_DATE_MISSING(5), VALIDITY_END_DATE_MISSING(6),
	FREQUENCY_MISSING(7), PAYER_VPA_MISSING(8), PAYER_BANK_DATA_MISSING(9), PAYER_ACC_NUM_MISSING(10),
	PAYER_BANK_NAME_MISSING(11), PAYER_BANK_IFSC_MISSING(12), PAYEE_VPA_MISSING(13),
	MANDATE_AMOUNT_MISSING_FOR_COLLECT_EVENT(14), EXPIRY_ON_DATE_MISSING_FOR_EXPIRY_EVENT(15), OTHER_UMN_MISSING(16);

	private final Integer missingDataIdentifierKey;

}
