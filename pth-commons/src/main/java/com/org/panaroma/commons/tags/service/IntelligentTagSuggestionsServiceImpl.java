package com.org.panaroma.commons.tags.service;

import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.tag.SystemCategoryTagEnum;
import com.org.panaroma.commons.tags.IntelligentTagSuggestionService;
import com.org.panaroma.commons.tags.TagRuleTxnDto;
import com.org.panaroma.commons.tags.dto.TagConfigurationCache;
import com.org.panaroma.commons.tags.dto.TagRule;
import com.org.panaroma.commons.tags.dto.TagDto;
import io.micrometer.common.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Service implementation for providing intelligent tag suggestions for transactions.
 */
@Service
@Slf4j
@AllArgsConstructor
public class IntelligentTagSuggestionsServiceImpl implements IntelligentTagSuggestionService, Serializable {

	private TagConfigurationCache tagConfigurationCache;

	public class MatchResult implements Serializable {

		private final TagRule key;

		private final TagDto dto;

		private final int matchLevel;

		public MatchResult(TagRule key, TagDto dto, int matchLevel) {
			this.key = key;
			this.dto = dto;
			this.matchLevel = matchLevel;
		}

		public TagDto dto() {
			return this.dto;
		}

	}

	/**
	 * Returns a list of tag suggestions for the given transaction based on the highest
	 * matching tag rule.
	 * @param txn the transaction to analyze
	 * @return a list of tag suggestions, or an empty list if no match is found
	 */
	@Override
	public List<String> getTagSuggestionsForTxn(TransformedTransactionHistoryDetail txn) {
		MatchResult matchObject = getHighestMatchObjectForTxn(txn);
		if (Objects.isNull(matchObject)) {
			log.info("No matches found for tags");
			return Collections.emptyList();
		}

		// Otherwise, return the best match (as per sorting, first will be exact match)
		return matchObject.dto.getSuggestedTags();
	}

	/**
	 * Returns the auto tag suggestion for the given transaction based on the highest
	 * matching tag rule.
	 * @param txn the transaction to analyze
	 * @return the auto tag suggestion, or null if no match is found
	 */
	@Override
	public String getAutoTagSuggestionForTxn(TransformedTransactionHistoryDetail txn) {
		MatchResult matchObject = getHighestMatchObjectForTxn(txn);
		if (Objects.isNull(matchObject)) {
			log.info("No matches found for tags");
			return SystemCategoryTagEnum.OTHERS.getAutoCategoryTag();
		}

		// Otherwise, return the best match (as per sorting, first will be exact match)
		return matchObject.dto.getAutoTag();
	}

	/**
	 * Finds the highest matching tag rule and tag DTO for the given transaction by
	 * checking all relevant cache keys.
	 * @param txn the transaction to analyze
	 * @return a MatchResult containing the best matching tag rule and tag DTO, or null if
	 * no match is found
	 */
	private MatchResult getHighestMatchObjectForTxn(TransformedTransactionHistoryDetail txn) {
		// check if cache is loaded, if it is not loaded, return null
		if (CollectionUtils.isEmpty(tagConfigurationCache.getCache())) {
			log.warn("tag configuration cache is empty");
			return null;
		}

		if (txn.getTxnType() == null) {
			log.warn("Transaction type is null for txn: {}", txn);
			return null;
		}
		TagRuleTxnDto transformedTxn = TagRuleTxnDto.fromTxn(txn);

		// Prepare possible cache keys with txnType and category
		List<String> cacheKeys = createTagCacheKeysForTxn(transformedTxn);

		MatchResult matchObject = null;
		for (String key : cacheKeys) {
			Map<TagRule, TagDto> tagMap = tagConfigurationCache.getCache().get(key);
			if (!CollectionUtils.isEmpty(tagMap)) {
				// get the highest match for the current key and compare with all keys
				// matching
				matchObject = this.compareTwoMatchingKeys(matchObject,
						getHighestMatchForCache(txn, transformedTxn, key, tagMap));
			}
			else {
				log.info("No tag configuration found for txnType: {}", key);
			}
		}
		if (matchObject == null) {
			log.info("No tag match found for txn: {}, hence returning default tag", txn.getTxnId());
			Map<TagRule, TagDto> tagMap = tagConfigurationCache.getCache().get("0");
			for (Map.Entry<TagRule, TagDto> entry : tagMap.entrySet()) {
				matchObject = new MatchResult(entry.getKey(), entry.getValue(), 0);
			}
		}
		else {
			log.info("Tag match object for txn: {} is: {}", txn.getTxnId(), matchObject.dto);
		}
		return matchObject;
	}

	/**
	 * Checks the transaction against the provided tag map and returns the highest match
	 * (by priority and match level).
	 * @param txn the transformed transaction to analyze
	 * @param tagMap the map of tag rules to tag DTOs
	 * @return a MatchResult containing the best matching tag rule and tag DTO, or null if
	 * no match is found
	 */
	private MatchResult getHighestMatchForCache(TransformedTransactionHistoryDetail txn, TagRuleTxnDto transformedTxn,
			String txnCacheKey, Map<TagRule, TagDto> tagMap) {
		List<Integer> allMatchesId = new ArrayList<>();
		MatchResult match = null;
		for (Map.Entry<TagRule, TagDto> entry : tagMap.entrySet()) {
			int matchLevel = entry.getKey().isKeyValid(transformedTxn);
			if (matchLevel > 0) { // Only consider partial or exact matches
				MatchResult newMatch = new MatchResult(entry.getKey(), entry.getValue(), matchLevel);
				allMatchesId.add(newMatch.dto().getId());
				match = compareTwoMatchingKeys(match, newMatch);
			}
		}
		log.info("all tag matches for txn: {} and key: {} are: {}", txn.getTxnId(), txnCacheKey, allMatchesId);
		return match;
	}

	/**
	 * Compares two MatchResult objects and returns the one with higher priority or match
	 * level.
	 * @param match1 the first match result
	 * @param match2 the second match result
	 * @return the better match result based on priority and match level
	 */
	private MatchResult compareTwoMatchingKeys(MatchResult match1, MatchResult match2) {
		if (match1 == null)
			return match2;
		if (match2 == null)
			return match1;

		// Compare by priority first, return for highest priority
		if (match1.dto.getPriority() < match2.dto.getPriority()) {
			return match2;
		}
		else if (match1.dto.getPriority() > match2.dto.getPriority()) {
			return match1;
		}

		// If priorities are equal, compare by match level
		if (match1.matchLevel > match2.matchLevel) {
			return match1;
		}
		else {
			return match2;
		}
	}

	/**
	 * Creates a list of cache keys for tag lookup based on the transaction type and
	 * category.
	 * @param txn the transformed transaction
	 * @return a list of cache keys to use for tag lookup
	 */
	private List<String> createTagCacheKeysForTxn(TagRuleTxnDto txn) {
		String txnTypeKey = String.valueOf(txn.getTxnType());
		List<String> cacheKeys = new java.util.ArrayList<>();
		cacheKeys.add(txnTypeKey);
		// Add category-based keys if available
		if (StringUtils.isNotBlank(txn.getUthCategoryId())) {
			cacheKeys.add(txnTypeKey + "_" + txn.getUthCategoryId());
		}
		return cacheKeys;
	}

}
