package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.WebConstants.PTH_VERSION;

import com.org.panaroma.commons.enums.PthVersion;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class PthVersionUtility {

	/**
	 * Retrieves the PTH version from the ThreadContext and converts it to a PthVersion
	 * enum.
	 * @return PthVersion enum corresponding to the current version, or V_1_0 if it is
	 * null
	 */
	// This method is deprecated, only getPthVersionDouble() will be used in place of this
	// method.
	// public static PthVersion getPthVersion() {
	// String pthVersion = ThreadContext.get(PTH_VERSION);
	// return PthVersion.getPthVersionEnumByKey(pthVersion);
	// }

	/**
	 * Retrieves the PTH version from the ThreadContext and converts it to a double value.
	 * Logs a warning and returns a default value if the version is not set.
	 * @return double value of the PTH version, or 1.0 if not set
	 */
	public static double getRequestPthVersionDouble() {
		String pthVersion = ThreadContext.get(PTH_VERSION);
		if (StringUtils.isBlank(pthVersion) || pthVersion.equals("null")) {
			log.warn("PTH version is not set in ThreadContext");
			return PthVersion.getPthVersionEnumByKey(null).getKey(); // Call with null and
																		// get key
		}
		return Double.parseDouble(pthVersion);
	}

	/**
	 * Determines if the updated date range filter node is required. Currently, this
	 * feature is not implemented, so it always returns false.
	 * @return false
	 */
	public static boolean checkIfUpdatedDateRangeFilterNodeRequired() {
		return false;
	}

	/**
	 * Checks if the date range filter is required based on the PTH version. The filter is
	 * required for PTH versions 3.0 and above.
	 * @return true if the current PTH version is 3.0 or higher, false otherwise
	 */
	public static boolean isDateRangeFilterRequired() {
		return PthVersion.V_3_0.getKey() <= getRequestPthVersionDouble();
	}

	/**
	 * [PTH-901] Determines if the Onus filter is supported based on the current PTH
	 * version. The filter is supported for PTH versions 4.1 and above.
	 * @return true if the current PTH version is 4.1 or higher, false otherwise
	 */
	public static boolean isVerticalNameFilterSupported() {
		return getRequestPthVersionDouble() >= PthVersion.V_4_1.getKey();
	}

	/**
	 * Checks if the current PTH version is greater than or equal to V1_0 (1.0).
	 * @return true if the current PTH version is greater than or equal to 1.0, false
	 * otherwise
	 */
	public static boolean isRequestPthVersionGreaterThanOrEqualTo(PthVersion pthVersion) {
		return getRequestPthVersionDouble() >= pthVersion.getKey();
	}

	/**
	 * Checks if the given PTH version is between 2.0 and 5.0 (inclusive). This is used to
	 * determine if the feature to show PSP info as text in transaction details is
	 * enabled.
	 * @return true if the given PTH version is between 2.0 and 5.0 (inclusive), false
	 * otherwise
	 */
	public static boolean isTxnDetailsOtherPartyPspInfoAsTextFeatureEnabled() {
		return getRequestPthVersionDouble() >= PthVersion.V_2_0.getKey()
				&& getRequestPthVersionDouble() <= PthVersion.V_5_0.getKey();
	}

	/**
	 * Checks if both PSP info features (text and logo) are disabled. This is used to
	 * determine if the feature to show PSP info in transaction details is disabled.
	 * @return true if the given PTH version is less than 2.0, false otherwise
	 */
	public static boolean isTxnDetailsOtherPartyPspInfoFeatureDisabled() {
		return getRequestPthVersionDouble() < PthVersion.V_2_0.getKey();
	}

	/**
	 * Checks if the feature to show PSP info as logo in transaction details is enabled.
	 * @return true if the given PTH version is greater than or equal to 5.1, false
	 * otherwise
	 */
	public static boolean isTxnDetailsOtherPartyPspInfoAsLogoFeatureEnabled() {
		return getRequestPthVersionDouble() >= PthVersion.V_5_1.getKey();
	}

	public static boolean isLimitMonthsToScanInSingleApiRequestFeatureSupported() {
		return getRequestPthVersionDouble() >= PthVersion.V_6_0.getKey();
	}

}
