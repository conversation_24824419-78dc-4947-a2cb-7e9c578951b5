package com.org.panaroma.commons.tags.dto;

import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.tags.TagRuleTxnDto;
import com.org.panaroma.commons.utils.CommonsUtility;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@AllArgsConstructor
@Slf4j
@ToString
public class TagRuleFieldAmountRanges implements TagRuleField, Serializable {

	private List<Pair<Long, Long>> amountRanges;

	@Override
	public boolean isValidRule(TagRuleTxnDto tagRuleDto) {
		Long value = tagRuleDto.getAmount();
		if (Objects.isNull(value)) {
			return false;
		}
		for (Pair<Long, Long> range : amountRanges) {
			if (value >= range.getLeft() && value <= range.getRight()) {
				return true; // Value is within one of the ranges
			}
		}
		return false;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		TagRuleFieldAmountRanges that = (TagRuleFieldAmountRanges) o;
		return Objects.equals(amountRanges, that.amountRanges);
	}

	@Override
	public int hashCode() {
		return Objects.hash(amountRanges);
	}

	/*
	 * This function will convert db configuration for amount range into DTO The db format
	 * will be 100-199,200-299,300-399 It will return null if the db format is empty or
	 * null It will return a list of pairs where each pair represents a range The first
	 * element of the pair is the lower bound and the second element is the upper bound If
	 * the db format is invalid, it will log an error and return null
	 */
	public static TagRuleFieldAmountRanges of(TagConfigurationEntity entity) {
		if (entity == null || StringUtils.isBlank(entity.getAmountRanges())) {
			return null;
		}
		String dbFormatValue = entity.getAmountRanges().trim();
		List<Pair<Long, Long>> ranges = Stream.of(dbFormatValue.split(","))
			.map(String::trim)
			.filter(StringUtils::isNotBlank)
			.map(part -> part.split("-"))
			.filter(parts -> parts.length == 2)
			.map(parts -> {
				try {
					return (Pair<Long, Long>) (new ImmutablePair<>(Long.parseLong(parts[0].trim()),
							Long.parseLong(parts[1].trim())));
				}
				catch (NumberFormatException e) {
					log.error("Number format exception while parsing amount range: {}, part: {}, {}", entity, parts,
							CommonsUtility.exceptionFormatter(e));
					return null;
				}
			})
			.filter(Objects::nonNull)
			.sorted((r1, r2) -> {
				int cmp = Long.compare(r1.getLeft(), r2.getLeft());
				if (cmp == 0) {
					return Long.compare(r1.getRight(), r2.getRight());
				}
				return cmp;
			})
			.collect(Collectors.toList());
		return new TagRuleFieldAmountRanges(ranges);
	}

}
