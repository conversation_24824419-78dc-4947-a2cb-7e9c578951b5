package com.org.panaroma.commons.config;

import static com.org.panaroma.commons.config.DateRange.DATE_FORMAT;
import static com.org.panaroma.commons.config.DateRange.NOW;
import static com.org.panaroma.commons.config.DateRange.getNewDateRange;
import static com.org.panaroma.commons.constants.Constants.ES_INDEX_ALIAS;
import static com.org.panaroma.commons.constants.Constants.INDIAN_ZONE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.repository.es.EsUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Log4j2
public class SearchIndexRangeConfig {

	public static final String INDEX_CONFIG = "indexConfig";

	public static final String JSON = "json";

	public static final String DOT = ".";

	public static final String HYPHEN = "-";

	private SearchIndexConfig searchIndexConfig;

	private final ApplicationContext applicationContext;

	private List<String> dcEsHosts;

	private String dcEsPort;

	private List<String> thirdEsHosts;

	private String thirdEsPort;

	private List<String> awsEsHosts;

	private String awsEsPort;

	private String awsEsFromDate;

	private String secondEsFromDate;

	private long updateIndexConfigDate;

	@Value("${elastic-search-index}")
	private String esIndexAlias;

	@Value("${elastic-search-index-prefix}")
	private String esIndexPrefix;

	@Autowired
	public SearchIndexRangeConfig(final ApplicationContext applicationContext,
			@Value("#{'${dc-es-host-list}'.split(',')}") final List<String> dcEsHosts,
			@Value("${dc-es-port}") final String dcEsPort,
			@Value("#{'${third-es-host-list}'.split(',')}") final List<String> thirdEsHosts,
			@Value("${third-es-port}") final String thirdEsPort,
			@Value("#{'${managed-es-v2-host}'.split(',')}") final List<String> awsEsHosts,
			@Value("${es-port}") final String awsEsPort, @Value("${aws-es-from-date}") final String awsEsFromDate,
			@Value("${second-es-from-date}") final String secondEsFromDate) {
		this.applicationContext = applicationContext;
		this.dcEsHosts = dcEsHosts;
		this.dcEsPort = dcEsPort;
		this.thirdEsHosts = thirdEsHosts;
		this.thirdEsPort = thirdEsPort;
		this.awsEsHosts = awsEsHosts;
		this.awsEsPort = awsEsPort;
		this.awsEsFromDate = awsEsFromDate;
		this.secondEsFromDate = secondEsFromDate;
	}

	public SearchIndexConfig getSearchIndexConfig() {
		return searchIndexConfig;
	}

	@PostConstruct
	public void init() throws IOException {
		// TODO :- Need to change esHost list in indexConfig-prodmtdc.json.
		String env = null;
		if (StringUtils.isNotEmpty(System.getProperty(AbstractEnvironment.ACTIVE_PROFILES_PROPERTY_NAME))) {
			env = System.getProperty(AbstractEnvironment.ACTIVE_PROFILES_PROPERTY_NAME);
		}
		InputStream inputStream = null;
		if (env != null) {
			inputStream = this.getClass()
				.getClassLoader()
				.getResourceAsStream(INDEX_CONFIG + HYPHEN + env + DOT + JSON);
		}
		else {
			inputStream = this.getClass().getClassLoader().getResourceAsStream(INDEX_CONFIG + DOT + JSON);
		}

		if (inputStream == null) {
			log.error("No file found for index range config. Shutting Down Application...");
			throw new RuntimeException();
		}
		String jsonString = IOUtils.toString(inputStream, Charset.forName("UTF-8"));
		ObjectMapper objectMapper = new ObjectMapper();
		this.searchIndexConfig = objectMapper.readValue(jsonString, SearchIndexConfig.class);
		validateSearchIndexConfig(this.searchIndexConfig);
		updateIndexConfigDate = getNextMonthEpochMillis(Instant.now().atZone(ZoneId.of(INDIAN_ZONE)));
		generateMonthlyIndicesForAllProgRange(this.searchIndexConfig);
		log.info("indexConfigList: {}", this.searchIndexConfig.getConfigList());
	}

	public void setAwsEsFromDate(final String awsEsFromDate) {
		this.awsEsFromDate = awsEsFromDate;
	}

	public String getAwsEsFromDate() {
		return this.awsEsFromDate;
	}

	public String getSecondEsFromDate() {
		return this.secondEsFromDate;
	}

	public void setSecondEsFromDate(final String secondEsFromDate) {
		this.secondEsFromDate = secondEsFromDate;
	}

	private void generateMonthlyIndicesForAllProgRange(final SearchIndexConfig searchIndexConfig) {
		List<IndexConfig> configList = searchIndexConfig.getConfigList();
		for (IndexConfig indexConfig : configList) {
			generateMonthWiseIndicesInRange(indexConfig, this.esIndexAlias);
		}
	}

	public void generateMonthWiseIndicesInRange(final IndexConfig indexConfig, final String defaultIndex) {
		try {
			long indexConfigFromRange = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getStart(),
					DATE_FORMAT);
			long indexConfigToRange = 0L;
			if (NOW.equals(indexConfig.getRange().getEnd())) {
				indexConfigToRange = Instant.now().toEpochMilli();
			}
			else {
				indexConfigToRange = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getEnd(),
						DATE_FORMAT);
			}
			List<String> indices = EsUtility.getMonthWiseIndicesInTimeRange(indexConfigFromRange,
					indexConfigToRange - 1, this.esIndexPrefix);
			log.warn("Indices Formed for progresive config starttime : {} and endtime : {} : {}",
					indexConfig.getRange().getStart(), indexConfig.getRange().getEnd(), indices);
			indexConfig.setIndexes(indices);
		}
		catch (Exception e) {
			log.error("Exception while generating index config indices during bean init is : {}",
					CommonsUtility.exceptionFormatter(e));
			ArrayList<String> indicesDefaultList = new ArrayList<>();
			indicesDefaultList.add(defaultIndex);
			indexConfig.setIndexes(indicesDefaultList);
		}
	}

	private void validateSearchIndexConfig(final SearchIndexConfig searchIndexConfig) {
		List<IndexConfig> configList = searchIndexConfig.getConfigList();
		List<IndexConfig> sortedConfigListOnStartTime = getSortedConfigListOnStartTime(configList);
		String prevEndTime = null;
		for (IndexConfig indexConfig : sortedConfigListOnStartTime) {
			if (indexConfig.getRange().getStart().compareTo(indexConfig.getRange().getEnd()) > 0) {
				log.error("startDate cannot be greater than endDate "
						+ "for index range config. Shutting Down Application...");
				throw new RuntimeException();
			}
			if (prevEndTime == null) {
				prevEndTime = indexConfig.getRange().getEnd();
				continue;
			}
			if (!prevEndTime.equalsIgnoreCase(indexConfig.getRange().getStart())) {
				log.error("endDate of previous range should be equal to startDate "
						+ "of next range. Shutting Down Application...");
				throw new RuntimeException();
			}
			if (!validateIndexes(indexConfig.getIndexes())) {
				log.error("index given in indexConfig is not valid. Shutting Down Application...");
				throw new RuntimeException();
			}
			prevEndTime = indexConfig.getRange().getEnd();
		}
		if (!prevEndTime.equalsIgnoreCase("now")) {
			log.error(" now should be defined as endDate in last indexConfig...");
			throw new RuntimeException();
		}
		this.searchIndexConfig.setConfigList(sortedConfigListOnStartTime);
	}

	public void updateIndexConfigList(final long fromDate) {
		try {
			IndexConfig lastIndexConfig = this.searchIndexConfig.getConfigList()
				.get(this.searchIndexConfig.getConfigList().size() - 1);
			removeOldIndexConfigFromConfigList(fromDate);
			IndexConfig indexConfigToAdd = getIndexConfigToAdd(fromDate, lastIndexConfig);
			if (indexConfigToAdd != null) {
				List<IndexConfig> configList = this.searchIndexConfig.getConfigList();
				configList.add(indexConfigToAdd);
				configList = getSortedConfigListOnStartTime(configList);
				this.updateConfigList(configList);
			}
		}
		catch (Exception e) {
			log.error("Exception while updating config list : {}", CommonsUtility.exceptionFormatter(e));
		}
	}

	public void updateIndexConfigList(final String fromDate, final String awsEsFromDate,
			final String secondEsFromDate) {
		List<IndexConfig> indexConfigList = searchIndexConfig.getConfigList();

		List<IndexConfig> updatedIndexConfigList = new ArrayList<>();
		for (IndexConfig indexConfig : indexConfigList) {
			List<IndexConfig> updatedIndexes = getUpdatedIndexConfig(indexConfig, awsEsFromDate, secondEsFromDate);
			if (ObjectUtils.isNotEmpty(updatedIndexes)) {
				updatedIndexConfigList.addAll(updatedIndexes);
			}
		}

		if (ObjectUtils.isNotEmpty(updatedIndexConfigList)) {
			List<IndexConfig> indexConfigListSorted = getSortedConfigListOnStartTime(updatedIndexConfigList);
			searchIndexConfig.setConfigList(indexConfigListSorted);
		}
	}

	private List<IndexConfig> getUpdatedIndexConfig(final IndexConfig indexConfig, final String awsFromDate,
			final String secondEsFromDate) {
		if (Objects.isNull(indexConfig) || StringUtils.isBlank(awsFromDate)) {
			return null;
		}
		String indexStartConfigDate = indexConfig.getRange().getStart();
		String indexEndConfigDate = indexConfig.getRange().getEnd();
		if (NOW.equalsIgnoreCase(indexEndConfigDate)) {
			indexEndConfigDate = DateRange.getStringDate(DateTimeUtility.currentTimeEpoch(), DATE_FORMAT);
		}

		List<IndexConfig> updatedIndexConfigs = new ArrayList<>();

		// if start date is greater than aws/first es from date.
		// full config will be of aws/first es only.
		if (indexStartConfigDate.compareTo(awsFromDate) >= 0) {
			updatedIndexConfigs
				.add(getAwsEsIndexConfig(indexConfig.getRange().getStart(), indexConfig.getRange().getEnd()));
		}

		// if start date is greater than secondEsFromDate and less than aws/first es from
		// date.
		// full config will be of dc/secondEs only.
		else if (indexStartConfigDate.compareTo(secondEsFromDate) >= 0
				&& indexEndConfigDate.compareTo(awsFromDate) <= 0) {
			updatedIndexConfigs
				.add(getDcEsIndexConfig(indexConfig.getRange().getStart(), indexConfig.getRange().getEnd()));
		}

		// if end date is less than or equal to secondEsFromDate.
		// full config will be of thirdEs only.
		else if (indexEndConfigDate.compareTo(secondEsFromDate) <= 0) {
			updatedIndexConfigs
				.add(getThirdEsIndexConfig(indexConfig.getRange().getStart(), indexConfig.getRange().getEnd()));
		}

		// if start date is greater than secondEsFromDate.
		// full config will be divided into two parts, between aws/first es and dc/second
		// es.
		else if (indexStartConfigDate.compareTo(secondEsFromDate) >= 0) {
			updatedIndexConfigs.add(getDcEsIndexConfig(indexConfig.getRange().getStart(), awsFromDate));
			updatedIndexConfigs.add(getAwsEsIndexConfig(awsFromDate, indexConfig.getRange().getEnd()));
		}

		// if end date is greater than secondEsFromDate.
		// full config will be divided into two parts, between third es and dc/second es.
		else if (indexEndConfigDate.compareTo(secondEsFromDate) >= 0
				&& indexEndConfigDate.compareTo(awsFromDate) <= 0) {
			updatedIndexConfigs.add(getThirdEsIndexConfig(indexConfig.getRange().getStart(), secondEsFromDate));
			updatedIndexConfigs.add(getDcEsIndexConfig(secondEsFromDate, indexConfig.getRange().getEnd()));
		}

		// if start date is less than secondEsFromDate.
		// full config will be divided into three parts, between third es, dc/second es
		// and aws/first es.
		else if (indexStartConfigDate.compareTo(secondEsFromDate) < 0) {
			updatedIndexConfigs.add(getThirdEsIndexConfig(indexConfig.getRange().getStart(), secondEsFromDate));
			updatedIndexConfigs.add(getDcEsIndexConfig(secondEsFromDate, awsFromDate));
			updatedIndexConfigs.add(getAwsEsIndexConfig(awsFromDate, indexConfig.getRange().getEnd()));
		}
		return updatedIndexConfigs;
	}

	private IndexConfig getDcEsIndexConfig(final String startDate, final String endDate) {
		IndexConfig indexConfig = new IndexConfig();
		indexConfig.setRange(getNewDateRange(startDate, endDate));
		indexConfig.setEsHosts(dcEsHosts);
		indexConfig.setEsPort(Integer.parseInt(dcEsPort));
		indexConfig.setIndexes(List.of(ES_INDEX_ALIAS));

		generateMonthWiseIndicesInRange(indexConfig, ES_INDEX_ALIAS);
		return indexConfig;
	}

	private IndexConfig getAwsEsIndexConfig(final String startDate, final String endDate) {
		IndexConfig indexConfig = new IndexConfig();
		indexConfig.setRange(getNewDateRange(startDate, endDate));
		indexConfig.setEsHosts(awsEsHosts);
		indexConfig.setEsPort(Integer.parseInt(awsEsPort));
		indexConfig.setIndexes(List.of(ES_INDEX_ALIAS));

		generateMonthWiseIndicesInRange(indexConfig, ES_INDEX_ALIAS);
		return indexConfig;
	}

	private IndexConfig getThirdEsIndexConfig(final String startDate, final String endDate) {
		IndexConfig indexConfig = new IndexConfig();
		indexConfig.setRange(getNewDateRange(startDate, endDate));
		indexConfig.setEsHosts(thirdEsHosts);
		indexConfig.setEsPort(Integer.parseInt(thirdEsPort));
		indexConfig.setIndexes(List.of(ES_INDEX_ALIAS));

		generateMonthWiseIndicesInRange(indexConfig, ES_INDEX_ALIAS);
		return indexConfig;
	}

	private void updateConfigList(final List<IndexConfig> indexConfigList) {
		List<IndexConfig> indexConfigListSorted = getSortedConfigListOnStartTime(indexConfigList);
		this.getSearchIndexConfig().setConfigList(indexConfigListSorted);
	}

	private IndexConfig getIndexConfigToAdd(final long fromDate, final IndexConfig lastIndexConfig) {
		IndexConfig indexConfigToAdd = new IndexConfig();
		IndexConfig firstIndexConfig = null;
		if (this.searchIndexConfig.getConfigList().size() > 0) {
			firstIndexConfig = this.searchIndexConfig.getConfigList().get(0);
		}
		if (firstIndexConfig != null
				&& firstIndexConfig.getRange().getStart().equals(DateRange.getStringDate(fromDate, DATE_FORMAT))) {
			return null;
		}
		if (firstIndexConfig != null) {
			setIndexConfigData(fromDate, indexConfigToAdd, firstIndexConfig, firstIndexConfig.getRange().getStart());
		}
		else {
			setIndexConfigData(fromDate, indexConfigToAdd, lastIndexConfig, NOW);
		}
		return indexConfigToAdd;
	}

	private void setIndexConfigData(final long fromDate, final IndexConfig indexConfigToAdd,
			final IndexConfig firstIndexConfig, final String start) {
		BeanUtils.copyProperties(firstIndexConfig, indexConfigToAdd);
		indexConfigToAdd.setRange(getNewDateRange(fromDate, start));

		generateMonthWiseIndicesInRange(indexConfigToAdd, this.esIndexAlias);
	}

	// TODO As we are not comparing end date, so can be issue
	private void removeOldIndexConfigFromConfigList(final long fromDate) {
		List<IndexConfig> indexConfigToRemoveList = new ArrayList<>();
		for (IndexConfig indexConfig : this.getSearchIndexConfig().getConfigList()) {
			String newFromDate = DateRange.getStringDate(fromDate, DATE_FORMAT);
			if (newFromDate.compareTo(indexConfig.getRange().getStart()) > 0) {
				indexConfigToRemoveList.add(indexConfig);
			}
		}
		List<IndexConfig> configList = this.getSearchIndexConfig().getConfigList();
		configList.removeAll(indexConfigToRemoveList);
		configList = getSortedConfigListOnStartTime(configList);
		this.updateConfigList(configList);
	}

	public IndexConfig getIndexConfigUsingRange(final long startTime, final long defaultToDate) throws ParseException {
		List<IndexConfig> configList = this.searchIndexConfig.getConfigList();
		if (CollectionUtils.isEmpty(configList)) {
			log.error("Error while fetching search index config list");
			throw new RuntimeException();
		}

		ZonedDateTime zonedDateTime = Instant.now().atZone(ZoneId.of(INDIAN_ZONE));
		// updateIndexConfigDate represents next month day 1 epoch time at
		// 00(hours):00(mins):00(sec) time
		// if new months starts epoch time of new month will be >= updateIndexConfigDate
		// epoch so we need to update
		// indices and update variable : updateIndexConfigDate to new next moth 1st day
		// epoch time.
		if (updateIndexConfigDate <= zonedDateTime.toInstant().toEpochMilli()) {
			updateIndexConfigAndUpdatingDate(zonedDateTime);
		}

		IndexConfig requiredIndexConfig = null;
		for (IndexConfig indexConfig : configList) {
			long startIndexTime = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getStart(),
					DATE_FORMAT);
			long endIndexTime = defaultToDate;
			if (indexConfig.getRange().getEnd().equalsIgnoreCase(NOW)) {
				if (startIndexTime <= startTime) {
					requiredIndexConfig = indexConfig;
					break;
				}
			}
			else {
				endIndexTime = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getEnd(), DATE_FORMAT);
			}
			if (startIndexTime <= startTime && startTime < endIndexTime) {
				requiredIndexConfig = indexConfig;
				break;
			}
		}

		return requiredIndexConfig;
	}

	// this function updates indices of every progressive range and
	// updates the variable updateIndexConfigDate to next month epoch time
	private void updateIndexConfigAndUpdatingDate(final ZonedDateTime zonedDateTime) {
		try {
			for (IndexConfig indexConfig : this.getSearchIndexConfig().getConfigList()) {
				this.generateMonthWiseIndicesInRange(indexConfig, this.esIndexAlias);
			}
			updateIndexConfigDate = getNextMonthEpochMillis(zonedDateTime);
		}
		catch (Exception e) {
			log.error("Error while updating indexConfigConfigurations: {}", CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	// this function calculates next month's day 1 with 00(hour):00(mins):00(sec) epoch
	// time
	private long getNextMonthEpochMillis(final ZonedDateTime zonedDateTime) {
		int nextMonth = zonedDateTime.getMonthValue() + 1;
		int nextMonthYear = zonedDateTime.getYear();
		if (nextMonth > 12) {
			nextMonth = nextMonth - 12;
			nextMonthYear = nextMonthYear + 1;
		}
		LocalDateTime localDateTime = LocalDateTime.of(nextMonthYear, nextMonth, 1, 0, 0);
		long nextMonthEpoch = ZonedDateTime.of(localDateTime, ZoneId.of(INDIAN_ZONE)).toInstant().toEpochMilli();
		return nextMonthEpoch;
	}

	private List<IndexConfig> getSortedConfigListOnStartTime(final List<IndexConfig> configList) {
		return configList.stream()
			.sorted(Comparator.comparing(o -> o.getRange().getStart()))
			.collect(Collectors.toList());
	}

	private boolean validateIndexes(final List<String> indexes) {
		return true;
	}

	/**
	 * @param date (date in epochMillis)
	 * @param indexConfig (Progressive Search Index Config)
	 * @return true/false -> if this date lies between this indexConfig.range.fromDate and
	 * indexConfig.range.toDate
	 */
	public boolean isDateLieInIndexConfig(final long date, final IndexConfig indexConfig) {
		try {
			// Provided Index Config is not valid
			if (Objects.isNull(indexConfig) || Objects.isNull(indexConfig.getRange())
					|| StringUtils.isBlank(indexConfig.getRange().getStart())
					|| StringUtils.isBlank(indexConfig.getRange().getEnd())) {
				return false;
			}

			// provided date epoch is not valid
			if (date <= 0) {
				return false;
			}

			long indexStartConfigDate = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getStart(),
					DATE_FORMAT);
			long indexEndConfigDate = DateTimeUtility.currentTimeEpoch();

			// index endDate is not NOW
			if (Boolean.FALSE.equals(NOW.equalsIgnoreCase(indexConfig.getRange().getEnd()))) {
				indexEndConfigDate = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getEnd(),
						DATE_FORMAT);
			}

			// if date >= startDate and date <= toDate
			return date >= indexStartConfigDate && date <= indexEndConfigDate;
		}
		catch (Exception ex) {
			log.error("Exception while checking date between index config Exception: {}",
					CommonsUtility.exceptionFormatter(ex));
		}

		return false;
	}

}
