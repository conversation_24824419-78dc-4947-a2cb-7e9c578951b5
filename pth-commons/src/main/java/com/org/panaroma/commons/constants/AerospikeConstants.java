package com.org.panaroma.commons.constants;

public class AerospikeConstants {

	public static final String VALUE_STRING = "value";

	public static final String MANAGED_PIPELINE_PREFIX = "managed_";

	public static final String UTH_RECORD_SET = "uthrecordset";

	public static final String LOCALISED_SET_NAME = "localised";

	public static final String UNLOCALISED_SET_NAME = "unlocalised";

	public static final String PG_P2M_RECORD_SET_NAME = "uthpgp2mrecordset";

	public static final String MANDATE_SET_NAME = "mandaterecordset";

	public static final String UTH_PG_RECORD_SET_NAME = "uthpgrecordset";

	public static final String KAFKA_PROCESSED_RECORD_SET_NAME = "kafkaProcessed";

	public static final String THD_RECORD_SET_NAME = "thdRecordset";

	public static final String NON_TRANSACTING_USER_SET_NAME = "nonTransactingUserSet";

	public static final String NON_TRANSACTING_USER_CACHE = "nonTransactingUserCache";

	public static final String LISTING_CACHE_SET_NAME = "listingApiCacheSet";

	public static final String LISTING_CACHE = "listingCache";

	public static final String IS_LISTING_CACHE_ENABLED = "isNtuCacheEnabled";

	public static final String IS_UPI_PASSBOOK_CACHE_ENABLED = "isUpiPassbookCacheEnabled";

	public static final String V3_PREFIX = "V3_";

	public static final String LAG_CACHE_SET = "txnLagCache";

	public static final String ZERO_DELTA_CACHE_KEY = "zdTime"; // odd name to reduce
																// cache data size

	public static final String OLDEST_TXN_CACHE_KEY = "otfdTime"; // odd name to reduce
																	// cache data

	// size. Also aerospike has a 14 character bin name length limit
	public static final String CACHE_UPDATED_TIME_KEY = "updated";

	public static final String RECENT_TXNS_CACHE_SET = "recentTxnSet";

}