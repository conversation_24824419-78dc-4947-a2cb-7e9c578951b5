package com.org.panaroma.commons.enums;

import com.org.panaroma.commons.dto.PaymentSystemEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OmsPaymentMethodEnum {

	UPI("UPI", "UPI", PaymentSystemEnum.UPI), CC("CC", "Credit Card", PaymentSystemEnum.PG),
	DC("DC", "Debit Card", PaymentSystemEnum.PG), NB("NB", "Net Banking", PaymentSystemEnum.PG),
	STORE_CASH("STORE_CASH", "Store Cash", PaymentSystemEnum.STORE_CASH), EMI("EMI", "EMI", PaymentSystemEnum.EMI),
	LOYALTY_POINT("LOYALTY_POINT", "LOYALTY_POINT", PaymentSystemEnum.LOYALTY_POINT),
	BANK_MANDATE("BANK_MANDATE", "BANK_MANDATE", PaymentSystemEnum.BANK_MANDATE),
	PAYTM_CASH("PAYTM_CASH", "PAYTM_CASH", PaymentSystemEnum.PAYTM_CASH),
	EMI_DEBIT_CARD("EMI_DEBIT_CARD", "EMI_DEBIT_CARD", PaymentSystemEnum.EMI_DEBIT_CARD),
	NET_BANKING("NET_BANKING", "NET_BANKING", PaymentSystemEnum.NET_BANKING);

	private String paymentMethodValue;

	private String paymentMethodDesc;

	private PaymentSystemEnum uthMappedPaymentStatus;

	public static OmsPaymentMethodEnum getStatusEnumByValue(final String paymentMethodValue) {
		for (OmsPaymentMethodEnum paymentMethodEnum : OmsPaymentMethodEnum.values()) {
			if (paymentMethodEnum.getPaymentMethodValue().equalsIgnoreCase(paymentMethodValue)) {
				return paymentMethodEnum;
			}
		}
		return null;
	}

	public static PaymentSystemEnum getPthPaymentStatus(final String paymentMethodValue) {
		for (OmsPaymentMethodEnum paymentMethodEnum : OmsPaymentMethodEnum.values()) {
			if (paymentMethodEnum.getPaymentMethodValue().equalsIgnoreCase(paymentMethodValue)) {
				return paymentMethodEnum.uthMappedPaymentStatus;
			}
		}
		return null;
	}

}
