package com.org.panaroma.commons.tags.service;

import com.org.panaroma.commons.enums.TagConfigurationStatusEnum;
import com.org.panaroma.commons.tags.dto.TagConfigurationCache;
import com.org.panaroma.commons.tags.dto.TagDto;
import com.org.panaroma.commons.tags.dto.TagRule;
import com.org.panaroma.commons.tags.dto.TagRuleField;
import com.org.panaroma.commons.tags.dto.TagRuleFieldAmountRanges;
import com.org.panaroma.commons.tags.dto.TagRuleFieldDateRange;
import com.org.panaroma.commons.tags.dto.TagRuleFieldPhonebookNames;
import com.org.panaroma.commons.tags.dto.TagRuleFieldVerticalIds;
import com.org.panaroma.commons.tags.dto.TagRuleFieldVPA;
import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.repository.TagConfigurationRepo;
import com.org.panaroma.commons.tags.enums.TagConfigurationSource;
import com.org.panaroma.commons.utils.BeanUtil;
import com.org.panaroma.commons.utils.CommonsUtility;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Component
@Slf4j
public class TagConfigurationRefreshService implements Serializable {

	private static TagConfigurationRepo tagConfigurationRepo;

	@Autowired
	private TagConfigurationCache tagConfigurationCache;

	@Autowired
	BeanUtil beanUtil;

	@PostConstruct
	public void init() {
		log.info("loading tag configuration cache on server startup");
		// Load Excel config ONCE at startup
		loadTagConfigurationsFromExcel();
		// Load DB config and merge Excel config
		refreshCache();
	}

	private void ensureTagConfigurationRepoInitialized() {
		if (tagConfigurationRepo != null) {
			return;
		}

		synchronized (this) {
			try {
				if (tagConfigurationRepo == null) {
					tagConfigurationRepo = beanUtil.getBean(TagConfigurationRepo.class);
				}
			}
			catch (Exception e) {
				return;
			}
		}
	}

	// Called by scheduler or manually to refresh DB config only
	public Map<String, Map<TagRule, TagDto>> refreshCache() {
		ensureTagConfigurationRepoInitialized();
		if (tagConfigurationRepo == null) {
			return tagConfigurationCache.getCache();
		}
		List<TagConfigurationEntity> tagConfigurations = tagConfigurationRepo
			.findAllByStatus(TagConfigurationStatusEnum.ENABLE);
		log.info("Found tag configurations from db: {}", tagConfigurations);
		Map<String, Map<TagRule, TagDto>> newCache = new HashMap<>();
		for (TagConfigurationEntity config : tagConfigurations) {
			int txnType = config.getTxnType();
			String uthCategories = config.getUthCategories();
			if (StringUtils.isNotBlank(uthCategories)) {
				Stream.of(uthCategories.split(","))
					.map(String::trim)
					.filter(StringUtils::isNotBlank)
					.forEach(category -> populateCacheForTxnType(newCache, txnType + "_" + category, config));
			}
			populateCacheForTxnType(newCache, String.valueOf(txnType), config);
		}
		log.info("Populated tag configuration: {}", newCache);
		if (!newCache.isEmpty()) {
			tagConfigurationCache.updateCache(newCache);
		}
		return tagConfigurationCache.getCache();
	}

	private void populateCacheForTxnType(Map<String, Map<TagRule, TagDto>> newCache, String key,
			TagConfigurationEntity config) {
		// Logic to populate the cache for the given transaction type
		// This could involve creating a new map for the transaction type
		// or adding it to the newCache map.
		log.info("populating tag cache for key: {}", key);
		Map<TagRule, TagDto> tagMap = newCache.getOrDefault(key, new HashMap<>());

		List<TagRuleField> tagRules = Stream
			.of(TagRuleFieldDateRange.of(config), TagRuleFieldAmountRanges.of(config),
					TagRuleFieldPhonebookNames.of(config), TagRuleFieldVerticalIds.of(config))
			.filter(Objects::nonNull)
			.collect(Collectors.toList());
		// populate tag map with tagKey and tagDto
		tagMap.put(TagRule.builder().tagRules(tagRules).build(), TagDto.fromDbDto(config));
		log.info("tag map populated for key: {}, {}", key, tagMap);
		newCache.put(key, tagMap);
	}

	private void loadTagConfigurationsFromExcel() {
		Map<String, Map<TagRule, TagDto>> cache = new HashMap<>();
		// String excelPath = "tag_configuration/vpa_tag_configurations.xlsx";
		// try (InputStream is =
		// getClass().getClassLoader().getResourceAsStream(excelPath);
		// Workbook workbook = new XSSFWorkbook(is)) {
		// Sheet sheet = workbook.getSheetAt(0);
		// Row headerRow = sheet.getRow(0);
		// int txnTypeIdx = -1, payeeVpaIdx = -1, systemTagIdx = -1, priorityIdx = -1;
		// List<Integer> otherIdxs = new ArrayList<>();
		// for (int i = 0; i < headerRow.getLastCellNum(); i++) {
		// String col = headerRow.getCell(i).getStringCellValue();
		// if (col.equalsIgnoreCase("txn_type"))
		// txnTypeIdx = i;
		// else if (col.equalsIgnoreCase("payee_vpa"))
		// payeeVpaIdx = i;
		// else if (col.equalsIgnoreCase("System Tag"))
		// systemTagIdx = i;
		// else if (col.equalsIgnoreCase("Priority"))
		// priorityIdx = i;
		// else
		// otherIdxs.add(i);
		// }
		// for (int r = 1; r <= sheet.getLastRowNum(); r++) {
		// try {
		// fillCacheForOneRow(sheet.getRow(r), cache, txnTypeIdx, payeeVpaIdx,
		// systemTagIdx, priorityIdx,
		// otherIdxs);
		// }
		// catch (Exception e) {
		// log.error("Error reading row {}: {}", r, e.getMessage());
		// }
		// }
		// log.info("populated tag config from excel: {}", cache);
		tagConfigurationCache.updateStaticCache(cache);
		// }
		// catch (Exception e) {
		// log.error("Error reading tag configuration from Excel: {}", e.getMessage(), e);
		// }
	}

	private void fillCacheForOneRow(Row row, Map<String, Map<TagRule, TagDto>> cache, int txnTypeIdx, int payeeVpaIdx,
			int systemTagIdx, int priorityIdx, List<Integer> otherIdxs) {
		if (row == null)
			return;
		String txnTypeCell = getCellString(row.getCell(txnTypeIdx));
		String payeeVpaCell = getCellString(row.getCell(payeeVpaIdx));
		String systemTagCell = getCellString(row.getCell(systemTagIdx));
		int priority = getCellNumeric(row.getCell(priorityIdx));
		List<String> suggestedTags = new ArrayList<>();
		for (int idx : otherIdxs) {
			String val = getCellString(row.getCell(idx));
			if (StringUtils.isNotBlank(val))
				suggestedTags.add(val);
		}
		if (StringUtils.isBlank(txnTypeCell) || StringUtils.isBlank(payeeVpaCell))
			return;
		List<TagRuleField> ruleFields = new ArrayList<>();
		ruleFields.add(TagRuleFieldVPA.of(payeeVpaCell));
		TagRule tagRule = TagRule.builder().tagRules(ruleFields).build();
		for (String txnTypeKey : txnTypeCell.split(",")) {
			txnTypeKey = txnTypeKey.trim();
			if (txnTypeKey.isEmpty())
				continue;
			TagDto tagDto = new TagDto();
			tagDto.setTxnType(Integer.parseInt(txnTypeKey));
			tagDto.setAutoTag(systemTagCell);
			tagDto.setSuggestedTags(suggestedTags);
			tagDto.setId(row.getRowNum());
			tagDto.setPriority(priority);
			tagDto.setSource(TagConfigurationSource.FILE);
			tagDto.setPayeeVpa(payeeVpaCell);
			Map<TagRule, TagDto> tagMap = cache.getOrDefault(txnTypeKey, new HashMap<>());
			if (tagMap.containsKey(tagRule)) {
				log.warn("Duplicate entry found for tag rule: {}", tagRule);
			}
			tagMap.put(tagRule, tagDto);
			cache.put(txnTypeKey, tagMap);
		}
	}

	private String getCellString(Cell cell) {
		if (cell == null)
			return null;
		if (cell.getCellType() == CellType.STRING)
			return cell.getStringCellValue();
		if (cell.getCellType() == CellType.NUMERIC)
			return String.valueOf(getCellNumeric(cell));
		return cell.toString();
	}

	private int getCellNumeric(Cell cell) {
		return (int) cell.getNumericCellValue();
	}

}
