package com.org.panaroma.commons.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum to identify different types of subsequent request handling scenarios for listing
 * operations. Used to determine how to handle pagination and listing requests based on
 * different configurations and conditions.
 */
@Getter
@AllArgsConstructor
public enum ListingSubsequentRequestHandlerIdentifierEnum {

	/**
	 * Indicates that the request should return an error because it's trying to search
	 * beyond a configured date threshold. Used to prevent searching too far in the past.
	 * being used in search & filtering, not listing e.g. onus based filtering is
	 * supported till one configured date
	 */
	RETURN_NOT_SEARCHING_BEYOND_SOME_CONFIGURED_DATE_ERROR(1),

	/**
	 * Indicates that listing requests should be blocked due to virtual from date. Used
	 * when listing functionality needs to be temporarily disabled for txns older than VFD
	 */
	THROW_VIRTUAL_FROM_DATE_RELATED_ERROR_IF_FLAG_IS_TRUE(2);

	private final Integer subsequentRequestHandlerIdentifierKey;

}
