package com.org.panaroma.commons.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.ToStringUtility;
import java.io.Serializable;
import lombok.Data;
import org.apache.avro.reflect.Nullable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FwdTxnDetails implements Serializable {

	@Nullable
	private String txnId;

	@Nullable
	private String txnSource;

	@Nullable
	private String amount;

	@Nullable
	private String rrn;

	@Nullable
	private String txnDate;

	@Override
	public String toString() {
		return "FwdTxnDetails{" + "txnId='" + txnId + '\'' + ", txnSource='" + txnSource + '\'' + ", amount='"
				+ ToStringUtility.getMaskedStringValue(amount) + '\'' + ", rrn='" + rrn + '\'' + ", txnDate='" + txnDate
				+ '\'' + '}';
	}

}
