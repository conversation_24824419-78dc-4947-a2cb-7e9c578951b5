package com.org.panaroma.commons.tags.utils;

import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.UtilityExtension;
import lombok.extern.log4j.Log4j2;

import java.util.Objects;

@Log4j2
public class IntelligentTagsUtil {

	// Method to check self transfer transaction Self Transfer would if self transfer flag
	// is there or Add money to upi lite or deactivation of upi lite
	public static boolean isSelfTransferTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && (Utility.isSelfTransferTxn(txn)
				|| TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.getTransactionTypeKey().equals(txn.getTxnType())
				|| TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.getTransactionTypeKey().equals(txn.getTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey().equals(txn.getTxnType()));
	}

	// Method to get the verticalId from the transaction history detail
	public static String getVerticalId(final TransformedTransactionHistoryDetail tthd) {
		String verticalId = null;
		if (tthd != null && tthd.getCartDetails() != null && tthd.getCartDetails().getItems() != null
				&& !tthd.getCartDetails().getItems().isEmpty()) {
			verticalId = tthd.getCartDetails().getItems().get(0).getVerticalId();
		}
		return verticalId;
	}

	// Method to get the UthCategoryEnum for auto tagging
	public static UthCategoryEnum getUthCategory(final TransformedTransactionHistoryDetail txn) {
		// If the transaction is null, then return null
		if (Objects.isNull(txn)) {
			return null;
		}

		// if txn is self txn then return self transfer category
		if (isSelfTransferTxn(txn)) {
			return UthCategoryEnum.SELF_TRANSFER;
		}
		else if (TransactionTypeEnum.isP2PTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getTxnType()))) {
			// If the transaction is a P2P transaction, then return the TRANSFERS category
			return UthCategoryEnum.TRANSFERS;
		}
		else if (TransactionTypeEnum
			.isCashbackTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getTxnType()))) {
			// If the transaction is a cashback transaction, then return the
			// CASHBACK_RECEIVED category
			return UthCategoryEnum.CASHBACK_RECEIVED;
		}
		else {
			// If the transaction is not a P2P or cashback transaction, then get the
			// UthCategoryEnum
			UthCategoryEnum category = UtilityExtension.getUthCategoryEnum(txn);

			// If the category is not null, then return the category, otherwise return the
			// OTHERS category
			return Objects.nonNull(category) ? category : UthCategoryEnum.OTHERS;
		}
	}

}
