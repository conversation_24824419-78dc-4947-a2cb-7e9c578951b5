package com.org.panaroma.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TagConfigurationStatusEnum {

	ENABLE(0), DISABLE(1);

	private final int value;

	public static TagConfigurationStatusEnum fromValue(int value) {
		for (TagConfigurationStatusEnum tagConfigurationStatusEnum : TagConfigurationStatusEnum.values()) {
			if (tagConfigurationStatusEnum.getValue() == value) {
				return tagConfigurationStatusEnum;
			}
		}
		throw new IllegalArgumentException("Invalid value: " + value);
	}

}
