package com.org.panaroma.commons.tags.dto;

import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Component
public class TagConfigurationCache implements Serializable {

	private Map<String, Map<TagRule, TagDto>> tagConfigurationStaticCache;

	private Map<String, Map<TagRule, TagDto>> tagConfigurationCache;

	public void updateStaticCache(Map<String, Map<TagRule, TagDto>> newStaticCache) {
		tagConfigurationStaticCache = new HashMap<>(newStaticCache);
		tagConfigurationCache = new HashMap<>(tagConfigurationStaticCache);
	}

	public void updateCache(Map<String, Map<TagRule, TagDto>> newCache) {
		tagConfigurationCache = new HashMap<>(tagConfigurationStaticCache);
		tagConfigurationCache.putAll(newCache);
	}

	public Map<String, Map<TagRule, TagDto>> getCache() {
		return tagConfigurationCache;
	}

}
