package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.CommonConstants.TRUE;
import static com.org.panaroma.commons.constants.CommonConstants.UpiLiteConstants.IS_UPI_LITE_PAYMENT_INSTRUMENT;
import static com.org.panaroma.commons.constants.CommonConstants.UpiLiteConstants.IS_UPI_LITE_TXN;
import static com.org.panaroma.commons.constants.WebConstants.PURPOSE_CODE;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.OFFLINE_LITE_PAY;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.ZERO_BALANCE_DEREGISTER_LITE;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.isP2pInwardTxnType;

import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class UpiLiteUtility implements Serializable {

	public static boolean isUpiLiteTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && ObjectUtils.isNotEmpty(txn.getContextMap())
				&& TRUE.equalsIgnoreCase(txn.getContextMap().get(IS_UPI_LITE_TXN));
	}

	public static boolean isUpiLiteOfflineTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && ObjectUtils.isNotEmpty(txn.getContextMap())
				&& TRUE.equalsIgnoreCase(txn.getContextMap().get(IS_UPI_LITE_TXN))
				&& OFFLINE_LITE_PAY.equals(txn.getContextMap().get(PURPOSE_CODE));
	}

	public static boolean isUpiLiteTxnAndPaymentInstrument(final TransformedParticipant participant) {
		return Objects.nonNull(participant) && ObjectUtils.isNotEmpty(participant.getContextMap())
				&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IS_UPI_LITE_PAYMENT_INSTRUMENT));
	}

	public static void addIsUpiLiteTxnFlag(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn)) {
			return;
		}
		Map<String, String> contextMap = Objects.isNull(txn.getContextMap()) ? new HashMap<>() : txn.getContextMap();
		contextMap.put(IS_UPI_LITE_TXN, TRUE);
		txn.setContextMap(contextMap);
	}

	public static void addIsUpiLiteTxnFlag(final TransformedParticipant participant) {
		if (Objects.isNull(participant)) {
			return;
		}
		Map<String, String> contextMap = Objects.isNull(participant.getContextMap()) ? new HashMap<>()
				: participant.getContextMap();
		contextMap.put(IS_UPI_LITE_PAYMENT_INSTRUMENT, TRUE);
		participant.setContextMap(contextMap);
	}

	public static boolean isTxnUsingUpiLiteInstrument(final TransformedTransactionHistoryDetail txn) {
		return (TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| (isUpiLiteOfflineTxn(txn)
						&& isP2pInwardTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getMainTxnType())))
				|| TransactionTypeEnum.P2M.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(txn.getMainTxnType()))
				&& isUpiLiteTxn(txn);
	}

	public static boolean isTxnUsingUpiLiteInstrumentForStmt(final TransformedTransactionHistoryDetail txn) {
		return (TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| (isUpiLiteOfflineTxn(txn)
						&& TransactionTypeEnum.P2P_INWARD.getTransactionTypeKey().equals(txn.getMainTxnType()))
				|| TransactionTypeEnum.P2M.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(txn.getMainTxnType()))
				&& isUpiLiteTxn(txn);
	}

	public static boolean isUpiLiteOutwardTxn(final TransformedTransactionHistoryDetail txn,
			final TransformedParticipant participant) {

		// This will return true : when user did outward txn like p2p, p2m, self transfer
		// etc. using UpiLite Instrument.
		// false : for activate, addMoney and deactivate UPI Lite.
		return (TransactionTypeEnum.P2M.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(txn.getMainTxnType()))
				&& isUpiLiteTxnAndPaymentInstrument(participant);
	}

	public static boolean isDeactivationEventWithAmtZero(final TransformedTransactionHistoryDetail txn) {
		return TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.getTransactionTypeKey().equals(txn.getMainTxnType())
				&& ObjectUtils.isNotEmpty(txn.getContextMap()) && txn.getContextMap().containsKey(PURPOSE_CODE)
				&& ZERO_BALANCE_DEREGISTER_LITE.equals(txn.getContextMap().get(PURPOSE_CODE));
	}

}
