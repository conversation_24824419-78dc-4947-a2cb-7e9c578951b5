package com.org.panaroma.commons.enums;

import java.util.List;
import java.util.Objects;

import com.org.panaroma.commons.utils.CommonsUtility;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

/**
 * Enum representing different versions of PTH.
 */
@AllArgsConstructor
@Getter
@Log4j2
public enum PthVersion {

	V_1_0(1.0), V_2_0(2.0), V_3_0(3.0), V_4_0(4.0), V_4_1(4.1), V_5_0(5.0), V_5_1(5.1), V_6_0(6.0);

	private final double key;

	/**
	 * Retrieves the PthVersion enum based on the provided key. If the key is blank or
	 * does not match any version, V_1_0 is returned.
	 * @param key the version key as a String
	 * @return the corresponding PthVersion enum
	 * @throws IllegalArgumentException if the key is null
	 * @throws NumberFormatException if the key cannot be parsed to a double
	 */
	public static PthVersion getPthVersionEnumByKey(final String key) throws NumberFormatException {

		if (StringUtils.isBlank(key)) {
			log.warn("Blank key provided, defaulting to V_1_0");
			return V_1_0;
		}
		try {
			double parseKey = Double.parseDouble(key);
			for (PthVersion pthVersionEnum : PthVersion.values()) {
				if (pthVersionEnum.key == parseKey) {
					return pthVersionEnum;
				}
			}
		}
		catch (NumberFormatException e) {
			log.error("Invalid key format: {}, Exception: {}", key, CommonsUtility.exceptionFormatter(e));
			throw new NumberFormatException("Invalid key format: " + key);
		}
		log.warn("No matching PthVersion found for key: {}, defaulting to V_1_0", key);
		return V_1_0;
	}

	/**
	 * Checks if the current version is smaller than or equal to the provided version.
	 * @param otherVersion the version to compare against (as a Double)
	 * @return true if the current version is smaller than or equal to the provided
	 * version, false otherwise
	 */
	// public boolean isSmallerThanEqual(PthVersion otherVersion) {
	// if (Objects.isNull(otherVersion)) {
	// return false;
	// }
	// return this.key <= otherVersion.key;
	// }
	//
	// /**
	// * Checks if the current version is greater than the provided version.
	// * @param otherVersion the version to compare against
	// * @return true if the current version is greater than the provided version, false
	// * otherwise
	// */
	// public boolean isGreaterThan(final PthVersion otherVersion) {
	// if (Objects.isNull(otherVersion)) {
	// return true;
	// }
	// return this.key > otherVersion.key;
	// }

}