package com.org.panaroma.commons.dto.kafkaPushAPI;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum PaymentSystemEnum {

	WALLET(1, "wallet"), BANK(2, "bank"), UPI(3, "upi"), PG(4, "pg"), PAYTM_POSTPAID(5, "paytm_postpaid"),
	MGV(6, "mgv"), OTHER(7, "other"), PPBL(8, "ppbl"), TS(9, "ts"), UPI_LITE(10, "upi_lite"),
	STORE_CASH(11, "store_cash"), UPI_CREDIT_CARD(12, "upi_credit_card"), EMI(13, "emi"),
	LOYALTY_POINT(14, "loyalty_point"), BANK_MANDATE(15, "bank_mandate"), PAYTM_CASH(16, "paytm_cash"),
	EMI_DEBIT_CARD(17, "emi_debit_card"), NET_BANKING(18, "net_banking"), GV(19, "gv");

	private Integer paymentSystemKey;

	private String paymentSystemValue;

	public static PaymentSystemEnum getPaymentSystemEnumByValue(final String paymentSystemValue) {
		for (PaymentSystemEnum paymentSystemEnum : PaymentSystemEnum.values()) {
			if (paymentSystemEnum.paymentSystemValue.equalsIgnoreCase(paymentSystemValue)) {
				return paymentSystemEnum;
			}
		}
		return null;
	}

}
