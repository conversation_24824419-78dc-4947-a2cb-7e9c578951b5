package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.Constants.INDIAN_ZONE;
import static com.org.panaroma.commons.constants.Constants.LAST_DAY_TIME;
import static com.org.panaroma.commons.constants.Constants.STARTNG_DAY_TIME;

import com.org.panaroma.commons.dto.DateTimeEnum;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Range;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

@Log4j2
public class IndexUtility {

	private static final String EMPTY_STRING = "";

	private static final Map<Range<Integer>, String> fourIndicesSuffixMap = new HashMap<>();

	private static final Map<Range<Integer>, String> twoIndicesSuffixMap = new HashMap<>();

	private static final Map<Range<Integer>, String> oneIndicesSuffixMap = new HashMap<>();

	private static final Map<Range<Integer>, String> threeDaysIndicesSuffixMap = new HashMap<>();

	// if any other index config needs to be added. Following needs to be done
	// 1. create a suffix map defining the range
	// 2. add an entry with the date to return the correct suffix config :
	// getSuffixMapBasedOnTxnDate
	// 3. Add all test cases covering boundary cases
	static {
		threeDaysIndicesSuffixMap.put(Range.between(1, 3), EMPTY_STRING);
		threeDaysIndicesSuffixMap.put(Range.between(4, 6), "-2");
		threeDaysIndicesSuffixMap.put(Range.between(7, 9), "-3");
		threeDaysIndicesSuffixMap.put(Range.between(10, 12), "-4");
		threeDaysIndicesSuffixMap.put(Range.between(13, 15), "-5");
		threeDaysIndicesSuffixMap.put(Range.between(16, 18), "-6");
		threeDaysIndicesSuffixMap.put(Range.between(19, 21), "-7");
		threeDaysIndicesSuffixMap.put(Range.between(22, 24), "-8");
		threeDaysIndicesSuffixMap.put(Range.between(25, 27), "-9");
		threeDaysIndicesSuffixMap.put(Range.between(28, 31), "-10");

		fourIndicesSuffixMap.put(Range.between(1, 7), EMPTY_STRING);
		fourIndicesSuffixMap.put(Range.between(8, 15), "-2");
		fourIndicesSuffixMap.put(Range.between(16, 22), "-3");
		fourIndicesSuffixMap.put(Range.between(23, 31), "-4");

		// two indices suffix
		twoIndicesSuffixMap.put(Range.between(1, 15), EMPTY_STRING);
		twoIndicesSuffixMap.put(Range.between(16, 31), "-2");

		// one index suffix
		oneIndicesSuffixMap.put(Range.between(1, 31), EMPTY_STRING);

	}

	private static Map<Range<Integer>, String> getSuffixMapBasedOnTxnDate(final Long txnDate) {

		String stringVersion = txnDate.toString();
		stringVersion = stringVersion.length() == 10 ? stringVersion + "000" : stringVersion;
		Long epochMillis = Long.valueOf(stringVersion);

		if (epochMillis >= 1638815400000L) { // 7 Dec 2021 00:00:00
			return threeDaysIndicesSuffixMap;
		}
		else if (epochMillis >= 1625077800000L) { // 1 july 2021 00:00:00
			return fourIndicesSuffixMap;
		}
		else if (epochMillis >= 1610562600000L) { // 14 jan 00:00:00
			return twoIndicesSuffixMap;
		}
		else {
			return oneIndicesSuffixMap;
		}
	}

	// check the txnDateDay lies in the key range, and return the suffix from valueMap
	// return empty string as default
	private static String getSuffixValueFromMap(final int txnDateDay, final Map<Range<Integer>, String> valueMap) {
		for (Range<Integer> key : valueMap.keySet()) {
			if (key.contains(txnDateDay)) {
				return valueMap.get(key);
			}
		}
		return EMPTY_STRING;
	}

	// check the txnDateDay lies in the key range, and return the boundary state in with
	// the map.
	private static String getBoundaryFromMap(final int txnDateDay, final int hour,
			final Map<Range<Integer>, String> valueMap, final int totalMonthDays) {

		// check if day is last day of the month, return LAST day. LAst day can be among
		// 28,29,30,31
		if (totalMonthDays == txnDateDay && hour >= 23) {
			return LAST_DAY_TIME;
		}

		for (Range<Integer> key : valueMap.keySet()) {

			// if day is not in the range continue
			if (!key.contains(txnDateDay)) {
				continue;
			}
			// if day is starting day of the range
			if (key.getMinimum() == txnDateDay && hour <= 0) {
				return STARTNG_DAY_TIME;
			}
			// if day is end day of range
			if (key.getMaximum() == txnDateDay && hour >= 23) {
				return LAST_DAY_TIME;
			}
		}
		return null;
	}

	// returns the suffix of index based on the range config defined and txn date
	public static String getSuffix(final Long txnDate, final int txnDateDay) {

		Map<Range<Integer>, String> suffixMapBasedOnTxnDate = getSuffixMapBasedOnTxnDate(txnDate);
		if (ObjectUtils.isEmpty(suffixMapBasedOnTxnDate)) {
			return EMPTY_STRING;
		}

		return getSuffixValueFromMap(txnDateDay, suffixMapBasedOnTxnDate);
	}

	public static List<String> getSucceedingSuffixes(final ZonedDateTime txnDate) {
		Map<Range<Integer>, String> suffixMapBasedOnTxnDate = getSuffixMapBasedOnTxnDate(
				txnDate.toInstant().toEpochMilli());
		if (ObjectUtils.isEmpty(suffixMapBasedOnTxnDate)) {
			return new LinkedList<>();
		}
		List<String> suffixes = new LinkedList<>();
		suffixMapBasedOnTxnDate.forEach((range, suffix) -> {
			if (!(range.isBefore(txnDate.getDayOfMonth()))) {
				suffixes.add(suffix);
			}
		});
		return suffixes;
	}

	public static List<String> getSuffixesInRange(final ZonedDateTime fromDate, final ZonedDateTime toDate) {
		Map<Range<Integer>, String> suffixMapBasedOnTxnDate = getSuffixMapBasedOnTxnDate(
				fromDate.toInstant().toEpochMilli());
		if (ObjectUtils.isEmpty(suffixMapBasedOnTxnDate)) {
			return new LinkedList<>();
		}
		List<String> suffixes = new LinkedList<>();
		suffixMapBasedOnTxnDate.forEach((range, suffix) -> {
			if (range.contains(fromDate.getDayOfMonth()) || range.contains(toDate.getDayOfMonth())
					|| (range.isAfter(fromDate.getDayOfMonth()) && range.isBefore(toDate.getDayOfMonth()))) {
				suffixes.add(suffix);
			}
		});
		return suffixes;
	}

	public static List<String> getPrecedingSuffixes(final ZonedDateTime txnDate) {
		Map<Range<Integer>, String> suffixMapBasedOnTxnDate = getSuffixMapBasedOnTxnDate(
				txnDate.toInstant().toEpochMilli());
		if (ObjectUtils.isEmpty(suffixMapBasedOnTxnDate)) {
			return new LinkedList<>();
		}
		List<String> suffixes = new LinkedList<>();
		suffixMapBasedOnTxnDate.forEach((range, suffix) -> {
			if (!(range.isAfter(txnDate.getDayOfMonth()))) {
				suffixes.add(suffix);
			}
		});
		return suffixes;
	}

	// get boundary state based on daye and hour
	// returns last day/startDay/empty string
	public static String getBoundaryStateOnTxnDateAndDay(final Long txnDate, final int date, final int hour,
			final int totalMonthDays) {

		Map<Range<Integer>, String> suffixMapBasedOnTxnDate = getSuffixMapBasedOnTxnDate(txnDate);

		if (ObjectUtils.isEmpty(suffixMapBasedOnTxnDate)) {
			return null;
		}

		return getBoundaryFromMap(date, hour, suffixMapBasedOnTxnDate, totalMonthDays);

	}

	public static String[] getIndexNames(final Long txnDate, final String indexNamePrefix,
			final List<String> updatedIndexList) {
		Set<String> indexNames = new HashSet<>();
		String dateInString = null;
		try {
			dateInString = getDateFromEpochTime(String.valueOf(txnDate), null);
		}
		catch (Exception e) {
			log.error("Exception :{} while convering date.", CommonsUtility.exceptionFormatter(e));
		}
		indexNames.add(getUpdatedIndexName(indexNamePrefix, txnDate, updatedIndexList));
		String boundaryState = getBoundaryState(txnDate);
		long sixtyMinutesInSeconds = 60 * 60;
		if (StringUtils.isNotEmpty(boundaryState)) {
			switch (boundaryState) {
				case STARTNG_DAY_TIME:
					log.warn("In STARTNG_DAY_TIME block for txnDate:{}, epochTxnDateTime:{}", dateInString, txnDate);
					indexNames.add(getUpdatedIndexName(indexNamePrefix,
							addTimeInSeconds(txnDate, -sixtyMinutesInSeconds), updatedIndexList));
					break;
				case LAST_DAY_TIME:
					Long currentEpoch = Instant.now().toEpochMilli();
					Long addedEpoch = addTimeInSeconds(txnDate, sixtyMinutesInSeconds);
					Long consideredEpoch = addedEpoch;
					if (currentEpoch < addedEpoch) {
						consideredEpoch = currentEpoch;
					}
					log.warn(
							"In LAST_DAY_TIME block for txnDate:{}, epochTxnDateTime:{}, currentEpoch:{}, consideredEpoch :{}",
							dateInString, txnDate, currentEpoch, consideredEpoch);
					indexNames.add(getUpdatedIndexName(indexNamePrefix, consideredEpoch, updatedIndexList));
					break;
				default:
			}
		}
		// log-optimisation
		log.debug("Index names on which search will be perform :{} for txnDate :{}", indexNames, txnDate);
		return indexNames.toArray(new String[0]);
	}

	public static String getBoundaryState(final Long txnDate) {
		Map<DateTimeEnum, String> dateMap = Utility.getDateTime(String.valueOf(txnDate), DateTimeEnum.year,
				DateTimeEnum.month, DateTimeEnum.date, DateTimeEnum.hour);
		YearMonth yearMonth = YearMonth.of(Integer.parseInt(dateMap.get(DateTimeEnum.year)),
				Integer.parseInt(dateMap.get(DateTimeEnum.month)));
		int totalMonthDays = yearMonth.lengthOfMonth();
		int date = Integer.parseInt(dateMap.get(DateTimeEnum.date));
		int hour = Integer.parseInt(dateMap.get(DateTimeEnum.hour));

		return IndexUtility.getBoundaryStateOnTxnDateAndDay(txnDate, date, hour, totalMonthDays);

	}

	public static String getDateFromEpochTime(final String epochTimeStamp, final String passedDateFormat)
			throws IllegalArgumentException {
		String dateFormat = passedDateFormat;
		if (StringUtils.isEmpty(dateFormat)) {
			dateFormat = "yyyy-MM-dd HH:mm:ss";
		}

		if (StringUtils.isEmpty(epochTimeStamp)) {
			throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
		}
		else {
			SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);

			try {
				long time = Long.parseLong(epochTimeStamp.length() == 10 ? epochTimeStamp + "000" : epochTimeStamp);
				String parsedDate = sdf.format(new Date(time));
				return parsedDate;
			}
			catch (Exception var6) {
				throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
			}
		}
	}

	private static Long addTimeInSeconds(final Long epochTime, final Long seconds) {
		String epochTimeStamp = String.valueOf(epochTime);
		try {
			long time = Long.parseLong(epochTimeStamp.length() == 10 ? epochTimeStamp + "000" : epochTimeStamp);
			DateTime dt = new DateTime(time);
			return dt.plus(seconds * 1000).getMillis();
		}
		catch (Exception var6) {
			throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
		}
	}

	public static String getUpdatedIndexName(final String indexNamePrefix, final Long txnDate,
			final List<String> updatedIndexList) {
		Map<DateTimeEnum, String> dateMap = Utility.getDateTime(String.valueOf(txnDate), DateTimeEnum.year,
				DateTimeEnum.month, DateTimeEnum.date, DateTimeEnum.hour);
		String month = dateMap.get(DateTimeEnum.month);
		String year = dateMap.get(DateTimeEnum.year);
		String date = dateMap.get(DateTimeEnum.date);
		String suffix = IndexUtility.getSuffix(txnDate, Integer.parseInt(date));

		String indexName = indexNamePrefix + "-" + month + "-" + year + suffix;
		try {
			if (updatedIndexList != null && updatedIndexList.contains(indexName)) {
				indexName = indexNamePrefix + "v2-" + month + "-" + year + suffix;
			}
		}
		catch (Exception e) {
			log.error("Exception while getting updated indexName. upatedIndexList :{} Exception :{}", updatedIndexList,
					CommonsUtility.exceptionFormatter(e));
		}
		// log.warn("Updated IndexName : {} for month : {}, year : {}", indexName, month,
		// year);
		return indexName;
	}

	// current index structure: year wise index
	public static String getIndexNameForUpiUdirDoc(final Long orgTxnDate, final String indexPrefix) {
		ZonedDateTime dateTime = Instant.ofEpochMilli(orgTxnDate).atZone(ZoneId.of(INDIAN_ZONE));
		return indexPrefix + "-" + dateTime.getYear();
	}

	/**
	 * if fromDate > toDate, return empty list
	 * @param indexNamePrefix prefix for index names in format : prefix-MM-YYYY-suffix
	 * @param fromDate fromDate for list of indices
	 * @param toDate toDate for list of indices
	 * @return list of indices
	 */
	public static List<String> getIndexListInRange(final String indexNamePrefix, final Long fromDate,
			final Long toDate) {
		if (fromDate > toDate) {
			return new LinkedList<>();
		}
		ZonedDateTime zonedFrom = Instant.ofEpochMilli(fromDate).atZone(ZoneId.of("Asia/Kolkata"));
		ZonedDateTime zonedTo = Instant.ofEpochMilli(toDate).atZone(ZoneId.of("Asia/Kolkata"));
		YearMonth from = YearMonth.from(zonedFrom);
		YearMonth to = YearMonth.from(zonedTo);
		if (from.equals(to)) {
			List<String> suffixes = getSuffixesInRange(zonedFrom, zonedTo);
			String monthlyIndexName = getIndexName(indexNamePrefix, from);
			return suffixes.stream().map(suffix -> monthlyIndexName + suffix + "*").collect(Collectors.toList());
		}

		List<String> suffixesFirstMonth = getSucceedingSuffixes(zonedFrom);
		List<String> suffixesLastMonth = getPrecedingSuffixes(zonedTo);
		List<String> indexes = new LinkedList<>();

		String indexNameFirstMonth = getIndexName(indexNamePrefix, from);
		String indexNameLastMonth = getIndexName(indexNamePrefix, to);

		suffixesFirstMonth.stream()
			.map(suffix -> indexNameFirstMonth + suffix + "*")
			.collect(Collectors.toCollection(() -> indexes));

		suffixesLastMonth.stream()
			.map(suffix -> indexNameLastMonth + suffix + "*")
			.collect(Collectors.toCollection(() -> indexes));

		YearMonth curr = from.plusMonths(1);
		while (curr.isBefore(to)) {
			indexes.add(getIndexName(indexNamePrefix, curr) + "*");
			curr = curr.plusMonths(1);
		}
		return indexes;
	}

	/*
	 * This will return the exact list of indices in the range. If fromDate > toDate,
	 * return empty list Example :- If the indexNamePrefix is "payment-history" and the
	 * fromDate is "2023-01-01" and toDate is "2023-01-08", it will return
	 * ["payment-history-01-2023", "payment-history-01-2023-2",
	 * "payment-history-01-2023-3"]
	 */

	public static List<String> getExactIndexNamesList(final String indexNamePrefix, final Long fromDate,
			final Long toDate) {
		// Validate input: if fromDate is after toDate, return empty list as it's an
		// invalid range
		if (fromDate > toDate) {
			return new LinkedList<>();
		}

		// Convert epoch timestamps to ZonedDateTime objects in Asia/Kolkata timezone
		// This is necessary because index suffixes are based on date ranges in Indian
		// timezone
		ZonedDateTime zonedFrom = Instant.ofEpochMilli(fromDate).atZone(ZoneId.of("Asia/Kolkata"));
		ZonedDateTime zonedTo = Instant.ofEpochMilli(toDate).atZone(ZoneId.of("Asia/Kolkata"));

		// Extract year-month information from the datetime objects
		// This helps determine if the date range spans single or multiple months
		YearMonth from = YearMonth.from(zonedFrom);
		YearMonth to = YearMonth.from(zonedTo);

		// CASE 1: Both dates fall within the same month
		// In this case, we only need to get suffixes for the date range within that
		// single month
		if (from.equals(to)) {
			// Get all suffixes that cover the date range within the month
			// For example, if range is Jan 5-15, this might return ["", "-2", "-3"]
			// based on the suffix mapping configuration
			List<String> suffixes = getSuffixesInRange(zonedFrom, zonedTo);

			// Build the base index name for the month (e.g., "payment-history-01-2023")
			String monthlyIndexName = getIndexName(indexNamePrefix, from);

			// Combine base name with each suffix to create exact index names
			// Result: ["payment-history-01-2023", "payment-history-01-2023-2",
			// "payment-history-01-2023-3"]
			return suffixes.stream().map(suffix -> monthlyIndexName + suffix).collect(Collectors.toList());
		}

		// CASE 2: Date range spans multiple months
		// We need to handle first month, last month, and all months in between separately

		// Get suffixes for the first month starting from the fromDate
		// This includes all suffixes from the fromDate to the end of that month
		List<String> suffixesFirstMonth = getSucceedingSuffixes(zonedFrom);

		// Get suffixes for the last month up to the toDate
		// This includes all suffixes from the beginning of the month to the toDate
		List<String> suffixesLastMonth = getPrecedingSuffixes(zonedTo);

		// Initialize result list to collect all index names
		List<String> indexes = new LinkedList<>();

		// Build base index names for first and last months
		String indexNameFirstMonth = getIndexName(indexNamePrefix, from);
		String indexNameLastMonth = getIndexName(indexNamePrefix, to);

		// Add all index names for the first month
		// Example: if first month is Jan 2023 and fromDate is Jan 15th,
		// this adds indices covering Jan 15-31: ["payment-history-01-2023-5",
		// "payment-history-01-2023-6", ...]
		suffixesFirstMonth.stream()
			.map(suffix -> indexNameFirstMonth + suffix)
			.collect(Collectors.toCollection(() -> indexes));

		// Add all index names for the last month
		// Example: if last month is Mar 2023 and toDate is Mar 10th,
		// this adds indices covering Mar 1-10: ["payment-history-03-2023",
		// "payment-history-03-2023-2", ...]
		suffixesLastMonth.stream()
			.map(suffix -> indexNameLastMonth + suffix)
			.collect(Collectors.toCollection(() -> indexes));

		// Handle all complete months between first and last month
		// For each complete month, we need all possible suffixes since the entire month
		// is covered
		YearMonth curr = from.plusMonths(1); // Start from month after the first month
		while (curr.isBefore(to)) { // Continue until we reach the last month (exclusive)
			// Build base index name for the current month
			String indexName = getIndexName(indexNamePrefix, curr);

			// Add all possible suffixes for this complete month
			// Since the entire month is covered, we use all values from the suffix map
			// Example: for Feb 2023, this adds all possible indices like:
			// "payment-history-02-2023", "payment-history-02-2023-2",
			// "payment-history-02-2023-3", etc.
			threeDaysIndicesSuffixMap.values().forEach(suffix -> indexes.add(indexName + suffix));

			// Move to the next month
			curr = curr.plusMonths(1);
		}

		// Return the complete list of exact index names covering the entire date range
		return indexes;
	}

	public static void convertToRegexOfGivenIndices(final String[] indices) {
		if (ObjectUtils.isEmpty(indices)) {
			return;
		}
		for (int i = 0; i < indices.length; i++) {
			indices[i] = indices[i] + "*";
		}
	}

	private static String getIndexName(final String indexNamePrefix, final YearMonth curr) {
		return String.format("%s-%02d-%d", indexNamePrefix, curr.getMonthValue(), curr.getYear());
	}

}
