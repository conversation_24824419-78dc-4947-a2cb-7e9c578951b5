package com.org.panaroma.commons.dto.webApi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.JsonUtils;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class PaginationParams implements Serializable {

	String transactionDateEpoch;

	String fromUpdatedDate;

	String paginationStreamSource;

	String paginationTxnId;

	Integer pageNo = -1;

	String nextListingUrl;

	String bgAppSyncNextListingUrl;

	Integer nextRequestHandlerIdentifier;

	Long toDateForSubsequentRequest;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
