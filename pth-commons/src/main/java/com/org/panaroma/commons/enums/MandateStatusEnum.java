package com.org.panaroma.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MandateStatusEnum {

	ACTIVE(1), PAUSED(2), CANCELLED(3), SETUP_FAILED(4), SETUP_PENDING(5), EXPIRED(7),

	// For IPO Mandate COMPLETED means the IPO is alloted
	COMPLETED(6),

	// For mandates that are ported out to another PSP
	PORTED(8);

	/*
	 * CANCELLED will be used for both user initiated IPO Mandate revoke & system
	 * initiated IPO Mandate revoke as there is no difference in events
	 */

	private final Integer mandateStatusKey;

	public static MandateStatusEnum getStatusEnumByKey(final Integer statusKey) {
		for (MandateStatusEnum statusEnum : MandateStatusEnum.values()) {
			if (statusEnum.getMandateStatusKey().equals(statusKey)) {
				return statusEnum;
			}
		}
		return null;
	}

}
