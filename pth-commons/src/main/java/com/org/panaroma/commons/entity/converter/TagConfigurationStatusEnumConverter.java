package com.org.panaroma.commons.entity.converter;

import com.org.panaroma.commons.enums.TagConfigurationStatusEnum;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class TagConfigurationStatusEnumConverter implements AttributeConverter<TagConfigurationStatusEnum, Integer> {

	@Override
	public Integer convertToDatabaseColumn(TagConfigurationStatusEnum tagConfigurationEnum) {
		return tagConfigurationEnum != null ? tagConfigurationEnum.getValue() : null;
	}

	@Override
	public TagConfigurationStatusEnum convertToEntityAttribute(Integer integer) {
		return integer != null ? TagConfigurationStatusEnum.fromValue(integer) : null;
	}

}
