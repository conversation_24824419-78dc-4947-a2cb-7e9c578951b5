package com.org.panaroma.commons.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Configuration for UPI apps. Contains the logo URL, display text, and a list of handles
 * associated with the app.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PspInfo implements Serializable {

	/**
	 * The name of the UPI app (e.g., Google Pay, PhonePe)
	 */
	private String pspName;

	/**
	 * The URL of the app logo
	 */
	private String pspLogo;

	/**
	 * Only Icon no app logo
	 */
	private String pspIconLogo;

	/**
	 * The display text for the app (e.g., "on Google Pay")
	 */
	private String pspDisplayText;

	/**
	 * List of UPI handles associated with this app (e.g., @okicici, @ybl)
	 */
	private List<String> handles;

}
