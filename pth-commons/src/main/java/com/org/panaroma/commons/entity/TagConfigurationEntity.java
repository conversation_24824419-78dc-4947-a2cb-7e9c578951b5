package com.org.panaroma.commons.entity;

import com.org.panaroma.commons.enums.TagConfigurationStatusEnum;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "tag_configuration")
@Data
public class TagConfigurationEntity {

	@Id
	private int id;

	/**
	 * sample data: 1
	 */

	@Column(name = "txn_type")
	private int txnType;

	/**
	 * sample data: 1,2,3
	 */
	@Column(name = "uth_categories")
	private String uthCategories;

	/**
	 * 2025-03-01/2025-06-03
	 */
	@Column(name = "date_range")
	private String dateRange;

	/**
	 * landlord,owner
	 */
	@Column(name = "receiver_phonebook_names")
	private String receiverPhonebookNames;

	/**
	 * 1-100,101-200
	 */
	@Column(name = "amount_ranges")
	private String amountRanges;

	/**
	 * 1,2,3
	 */
	@Column(name = "vertical_ids")
	private String verticalIds;

	/**
	 * 'tag1,tag2'
	 */
	@Column(name = "suggested_tags")
	private String suggestedTags;

	/**
	 * tag
	 */
	@Column(name = "auto_tag")
	private String autoTag;

	/**
	 * 1
	 */
	@Column(name = "priority")
	private int priority;

	@Column(name = "status")
	private TagConfigurationStatusEnum status;

}
