package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.WebConstants.ACCOUNT_NUMBER;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class MaskUtility {

	private static final ObjectMapper objectMapper;

	static {
		objectMapper = new ObjectMapper();
	}

	public static String maskForLogs(final String field) {
		return field == null ? null : "xxxx";
	}

	public static String getMaskedMobileNumber(final String passedMobileNumber) {
		String mobileNumber = passedMobileNumber;
		try {
			if (StringUtils.isEmpty(mobileNumber)) {
				return mobileNumber;
			}

			if (!mobileNumber.contains("XXXX")) { // masking is needed
				int length = mobileNumber.length();
				if (length < 10) {
					return null;
				}
				mobileNumber = mobileNumber.replace(mobileNumber.substring(length - 8, length - 4), "XXXX");
			}

			return mobileNumber;
		}
		catch (Exception e) {
			return null;
		}
	}

	public static String getMaskedAmountForLogger(final String shareScreenText) {
		return (shareScreenText != null) ? shareScreenText.replaceAll("\\d", "X") : " ";
	}

	public static Map<String, String> getMaskedAccountNoInCtaNode(final Object value) {
		if (value instanceof String) {
			return null;
		}

		Map<String, String> valueMap = Objects.nonNull(value) ? objectMapper.convertValue(value, Map.class)
				: new HashMap<>();
		if (valueMap.containsKey(ACCOUNT_NUMBER) && StringUtils.isNotBlank(valueMap.get(ACCOUNT_NUMBER))) {
			String accNumber = MaskUtility.getMaskedAccountNumber(valueMap.get(ACCOUNT_NUMBER));
			valueMap.put(ACCOUNT_NUMBER, accNumber);
		}
		return valueMap;
	}

	public static String getMaskedAccountNumber(final String accountNumber) {
		if (StringUtils.isBlank(accountNumber)) {
			return accountNumber;
		}
		// replace all those characters that have atleast 4 characters after it
		return accountNumber.replaceAll(".(?=.{4})", "X");
	}

	/**
	 * Masks SearchResponse to show only specified fields (entityId, txnId, txnDate,
	 * streamSource) in _source and replaces other fields with masked values
	 */
	public static String maskSearchResponse(SearchResponse searchResponse) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			JsonNode responseNode = objectMapper.readTree(searchResponse.toString());

			// Create a copy of the response
			ObjectNode maskedResponse = responseNode.deepCopy();

			// Process hits to mask _source fields
			JsonNode hitsNode = maskedResponse.get("hits");
			if (hitsNode != null && hitsNode.has("hits")) {
				ArrayNode hitsArray = (ArrayNode) hitsNode.get("hits");

				for (JsonNode hit : hitsArray) {
					if (hit.has("_source")) {
						ObjectNode sourceNode = (ObjectNode) hit.get("_source");

						// List of fields to keep unmasked
						Set<String> fieldsToKeep = Set.of("entityId", "txnId", "txnDate", "streamSource",
								"docUpdatedDate", "docCreatedDate", "status", "originalStatus", "amount");

						// Iterate through all fields in _source
						Iterator<Map.Entry<String, JsonNode>> fields = sourceNode.fields();
						List<String> fieldNames = new ArrayList<>();
						fields.forEachRemaining(entry -> fieldNames.add(entry.getKey()));

						// Mask fields that are not in the keep list
						for (String fieldName : fieldNames) {
							if (!fieldsToKeep.contains(fieldName)) {
								sourceNode.put(fieldName, maskForLogs("sensitive_data"));
							}
						}
					}
				}
			}

			return objectMapper.writeValueAsString(maskedResponse);

		}
		catch (Exception e) {
			log.error("Error while masking SearchResponse using MaskUtility: {}", e.getMessage());
			return MaskUtility.maskForLogs("Error masking response");
		}
	}

}
