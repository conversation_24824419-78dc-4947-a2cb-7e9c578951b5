package com.org.panaroma.commons.dto.es;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.org.panaroma.commons.utils.JsonUtils;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.avro.reflect.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class ParentDetails implements Serializable {

	@Nullable
	@JsonProperty("txnId")
	private String txnId;

	@Nullable
	@JsonProperty("txnDate")
	private String txnDate;

	@Nullable
	@JsonProperty("txnSource")
	private String txnSource;

	@Nullable
	@JsonProperty("amount")
	private String amount;

	@Nullable
	@JsonProperty("rrn")
	private String rrn;

	@Nullable
	@JsonProperty("parentExistsInDb")
	private boolean parentExistsInDb;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}