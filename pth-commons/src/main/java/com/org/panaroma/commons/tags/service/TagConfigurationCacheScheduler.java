package com.org.panaroma.commons.tags.service;

import com.org.panaroma.commons.utils.CommonsUtility;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class TagConfigurationCacheScheduler {

	private TagConfigurationRefreshService tagConfigurationRefreshService;

	@Scheduled(cron = "0 * * * * *")
	public void refreshTagConfigurationCache() {
		log.info("Refreshing tag configuration cache...");
		try {
			// Logic to refresh the tag configuration cache
			tagConfigurationRefreshService.refreshCache();
			log.info("refresh tag cache successful");
		}
		catch (Exception e) {
			log.error("Exception while refreshing tag configuration cache: {}", CommonsUtility.exceptionFormatter(e));
		}
	}

}
