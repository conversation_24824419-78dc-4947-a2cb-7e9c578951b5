package com.org.panaroma.commons.constants;

public class DatabaseFieldNameConstants {

	public class ElasticSearchFieldNameConstants {

		public static final String ENTITY_ID = "entityId";

		public static final String TRANSACTION_ID = "txnId";

		public static final String DOC_UPDATED_DATE = "docUpdatedDate";

		public static final String SEARCH_FIELDS_SEARCH_TAGS = "searchFields.searchTags";

		public static final String SEARCH_FIELDS_SEARCH_TAGS_KEYWORD = "searchFields.searchTags.keyword";

		public static final String SEARCH_FIELDS_SEARCH_UTH_CATEGORY = "searchFields.searchUthMerchantCategory";

		public static final String SEARCH_FIELDS_TXN_INDICATOR = "searchFields.txnIndicator";

		public static final String SEARCHABLE_IDENTIFIERS = "searchAbleIdentifiers";

	}

}
