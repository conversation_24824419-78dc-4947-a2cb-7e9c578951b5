package com.org.panaroma.commons.dto.mandate;

import static com.org.panaroma.commons.constants.CommonConstants.TERMINAL_STATUS;
import static com.org.panaroma.commons.constants.Constants.PIPE_SYMBOL;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.MANDATE_ACTIVITY_INDEX_NAME_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.OTHER_UMN;
import static com.org.panaroma.commons.utils.ToStringUtility.getMaskedStringValue;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.enums.MandateTypeEnum;
import com.org.panaroma.commons.utils.ToStringUtility;
import java.io.Serializable;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.avro.reflect.Nullable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class MandateActivityData extends MandateBaseDto implements Serializable {

	@Nullable
	@JsonProperty("entityId")
	String entityId;

	/*
	 * This will be null by default. We will make it false in some cases This is used to
	 * identify whether entry for this doc is required to be shown to user or not
	 */
	@Nullable
	@JsonProperty("showInHistory")
	Boolean showInHistory;

	/*
	 * We can plan to populate this field in future if we plan to keep data in db when
	 * incomplete data is received from mandate team
	 *
	 * @Nullable
	 *
	 * @JsonProperty("isVisible") Boolean isVisible;
	 */

	// will only be populated in case of financial txn. For all other cases it is equal to
	// the mandateAmount
	@Nullable
	@JsonProperty("amount")
	Double amount;

	@Nullable
	@JsonProperty("mandateAmount")
	Double mandateAmount;

	// key is saved corresponding to MandateActionEnum
	@Nullable
	@JsonProperty("action")
	Integer action;

	@Nullable
	@JsonProperty("status")
	Integer status;

	@Nullable
	@JsonProperty("viewStatus")
	Integer viewStatus;

	@Nullable
	@JsonProperty("umn")
	String umn;

	@Nullable
	@JsonProperty("txnId")
	String txnId;

	@Nullable
	@JsonProperty("rrn")
	String rrn;

	@Nullable
	@JsonProperty("errorCode")
	String errorCode;

	@Nullable
	@JsonProperty("txnDate")
	Long txnDate;

	@Nullable
	@JsonProperty("updatedDate")
	Long updatedDate;

	@Nullable
	@JsonProperty("docCreatedDate")
	Long docCreatedDate;

	@Nullable
	@JsonProperty("docUpdatedDate")
	Long docUpdatedDate;

	@Nullable
	@JsonProperty("actionMetaData")
	ActionMetadata actionMetaData;

	@Nullable
	@JsonProperty("missingDataIdentifier")
	Set<Integer> missingDataIdentifier;

	@Nullable
	@JsonIgnore
	String createTxnId;

	@Nullable
	@JsonIgnore
	String merchantVpa;

	@Nullable
	@JsonIgnore
	MandateTypeEnum mandateType;

	// Direct field for otherUMN instead of storing in metaDataMap
	@Nullable
	@JsonProperty(OTHER_UMN)
	String otherUMN;

	public String docId() {
		return entityId + PIPE_SYMBOL + txnId;
	}

	@Override
	public String getCacheKey() {
		return this.getEntityId() + PIPE_SYMBOL + this.getTxnId();
	}

	@Override
	public void setDatesInMandateDoc(final MandateBaseDto storedMandateDoc, final Long currentTime) {
		MandateActivityData storedMandateActivityDoc = (MandateActivityData) storedMandateDoc;
		if (Objects.isNull(storedMandateDoc)) {
			this.setDocCreatedDate(currentTime);
		}
		else {
			this.setDocCreatedDate(storedMandateActivityDoc.getDocCreatedDate());
		}
		this.setDocUpdatedDate(currentTime);
	}

	@Override
	public boolean isSourceDtoUpdated(final MandateBaseDto storedMandateDoc) {
		if (Objects.isNull(storedMandateDoc)) {
			return true;
		}
		MandateActivityData stored = (MandateActivityData) storedMandateDoc;
		ClientStatusEnum sourceStatus = ClientStatusEnum.getStatusEnumByKey(this.getStatus());
		ClientStatusEnum storedStatus = ClientStatusEnum.getStatusEnumByKey(stored.getStatus());

		if (Objects.nonNull(sourceStatus) && sourceStatus.equals(storedStatus)) {
			return this.getUpdatedDate() >= stored.getUpdatedDate();
		}
		else if (TERMINAL_STATUS.contains(sourceStatus) && TERMINAL_STATUS.contains(storedStatus)) {
			return this.getUpdatedDate() >= stored.getUpdatedDate();
		}
		else {
			return TERMINAL_STATUS.contains(sourceStatus);
		}
	}

	@Override
	public String getIndexNameIdentifier() {
		return MANDATE_ACTIVITY_INDEX_NAME_IDENTIFIER;
	}

	@Override
	public TypeReference getObjectType() {
		return new TypeReference<MandateActivityData>() {
		};
	}

	@Override
	public String toString() {
		return "MandateActivityData{" + "entityId='" + entityId + '\'' + ", showInHistory=" + showInHistory
				+ ", amount=" + getMaskedStringValue(amount) + ", mandateAmount=" + getMaskedStringValue(mandateAmount)
				+ ", action=" + action + ", status=" + status + ", viewStatus=" + viewStatus + ", umn='" + umn + '\''
				+ ", txnId='" + txnId + '\'' + ", rrn='" + rrn + '\'' + ", errorCode='" + errorCode + '\''
				+ ", txnDate=" + txnDate + ", updatedDate=" + updatedDate + ", docCreatedDate=" + docCreatedDate
				+ ", docUpdatedDate=" + docUpdatedDate + ", actionMetadata=" + actionMetaData + ", createTxnId='"
				+ createTxnId + '\'' + ", merchantVpa='" + ToStringUtility.getMaskedStringValue(merchantVpa) + '\''
				+ ", missingDataIdentifier=" + missingDataIdentifier + ", metaDataMap=" + getMetaDataMap()
				+ ", mandateType=" + mandateType + ", otherUMN='" + otherUMN + '\'' + '}';
	}

}
