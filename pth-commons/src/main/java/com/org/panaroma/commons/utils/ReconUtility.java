package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.CommonConstants.TERMINAL_STATUS_LIST;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class ReconUtility {

	public static final String RETRY_COUNT_FOR_RECON = "retryCountForRecon";

	public static final Integer RETRY_LIMIT_FOR_RECON = 3;

	public static final long RECON_TXN_DATE_LIMIT = 120L * 24 * 60 * 60 * 1000;

	public static Boolean isStoredUpdated(final TransformedTransactionHistoryDetail detail) {
		if (Objects.nonNull(detail)) {
			// in terminal state
			if (TERMINAL_STATUS_LIST.contains(detail.getOriginalStatus())) {
				return Boolean.TRUE;
			}
		}
		return Boolean.FALSE;
	}

	public static TransactionSource getTransactionSourceToFetchStatus(
			final TransformedTransactionHistoryDetail detail) {
		if (Boolean.TRUE.equals(detail.getIsSource())) {
			if (isStoredUpdated(detail)) {
				// no need to check status now as current dto is parent and we have data
				// in terminal state.
				return null;
			}
			else {
				// else, we dont have the updated status so fetching from the source.
				return TransactionSource.getTransactionSourceEnumByKey(detail.getStreamSource());
			}
		}
		else {
			// fetch parent source
			return TransactionSource.getTransactionSourceEnumByKey(detail.getSourceSystem());
		}
	}

	public static Boolean isNeedToReprocess(final TransactionHistoryDetails updatedRecord,
			final TransformedTransactionHistoryDetail storedDto) {
		if (Objects.isNull(updatedRecord) || Objects.isNull(storedDto)) {
			return Boolean.FALSE;
		}
		ClientStatusEnum storedStatus = ClientStatusEnum.getStatusEnumByKey(storedDto.getStatus());
		ClientStatusEnum updatedRecordStatus = updatedRecord.getStatus();

		String backFillingIdentifier = null;

		if (Objects.nonNull(storedDto.getContextMap())) {
			backFillingIdentifier = storedDto.getContextMap().get(BACKFILLING_IDENTIFIER);
		}

		if (storedStatus != null && updatedRecordStatus != null
				&& (!storedStatus.equals(updatedRecordStatus)
						|| BackFillingIdentifierEnum.MISSING_PARTICIPANT.getBackFillingIdentifierKey()
							.equals(backFillingIdentifier))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	public static Boolean eligibleForReProcessing(final TransformedTransactionHistoryDetail transactionHistoryDetails) {
		Integer retryCount = null;
		if (transactionHistoryDetails != null) {
			Map<String, String> contextMap = transactionHistoryDetails.getContextMap() == null ? new HashMap<>()
					: transactionHistoryDetails.getContextMap();
			if (contextMap.get(RETRY_COUNT_FOR_RECON) != null) {
				retryCount = Integer.parseInt(contextMap.get(RETRY_COUNT_FOR_RECON)) + 1;
			}
			else {
				retryCount = 1;
			}
			contextMap.put(RETRY_COUNT_FOR_RECON, String.valueOf(retryCount));
			transactionHistoryDetails.setContextMap(contextMap);
		}
		if (retryCount != null && retryCount < RETRY_LIMIT_FOR_RECON) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;

	}

	public static Boolean needToCheckStatus(final TransformedTransactionHistoryDetail detail) {
		if (Objects.isNull(detail) || Objects.isNull(detail.getTxnDate())) {
			return Boolean.FALSE;
		}
		Long currentTime = System.currentTimeMillis();
		Long txnDate = detail.getTxnDate();
		if (currentTime - txnDate > RECON_TXN_DATE_LIMIT && Utility.isSkipOldestEvent(detail)) {
			log.warn("Not checking status as txnDate is oder than 120 days. txnId :{}", detail.getTxnId());
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

}
