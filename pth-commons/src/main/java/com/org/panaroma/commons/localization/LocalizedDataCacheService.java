package com.org.panaroma.commons.localization;

import static com.org.panaroma.commons.constants.LocalizationConstants.CLS_PANEL_EMPTY_STRING;
import static com.org.panaroma.commons.constants.LocalizationConstants.DEFAULT_SUPPORTED_LOCALES;
import static com.org.panaroma.commons.constants.LocalizationConstants.ENGLISH_LOCALE;
import static com.org.panaroma.commons.constants.LocalizationConstants.LOCALE;
import static com.org.panaroma.commons.constants.LocalizationConstants.LOCALIZATION_GET_LANGUAGES_API;
import static com.org.panaroma.commons.constants.WebConstants.EMPTY_STRING;

import com.org.panaroma.commons.constants.LocaleMsgConstants;
import com.org.panaroma.commons.constants.LocaleMsgVariable;
import com.org.panaroma.commons.utils.MdcUtility;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Log4j2
public class LocalizedDataCacheService implements ILocalizedDataCacheService {

	@Autowired
	private ILocalizationClient localizationClient;

	@Autowired
	private IInternalLocalizationClient internalLocalizationClient;

	private static Set<String> supportedLocales = new HashSet<>();

	private static Map<String, Map<String, String>> localizedDataMap = new HashMap<>();

	/*
	 * This method returns the localized value corresponding to a key if present. Else it
	 * returns the english value. If english value is also not present, then it returns
	 * the defaultValue which is sent as a parameter to this method Whenever we need to
	 * replace some value with empty string at run-time, to support this "EMPTY_STRING"
	 * will be added as value on CLS Panel because making the value of key as blank is not
	 * allowed on CLS Panel
	 */
	public static String getLocalizedValue(final String key, final String defaultValue, final String locale) {
		if (StringUtils.isNotBlank(key) && !CollectionUtils.isEmpty(localizedDataMap)) {
			if (!CollectionUtils.isEmpty(localizedDataMap.get(locale))
					&& StringUtils.isNotBlank(localizedDataMap.get(locale).get(key))) {
				return CLS_PANEL_EMPTY_STRING.equalsIgnoreCase(localizedDataMap.get(locale).get(key)) ? EMPTY_STRING
						: localizedDataMap.get(locale).get(key);
			}
			else if (!ENGLISH_LOCALE.equalsIgnoreCase(locale)
					&& !CollectionUtils.isEmpty(localizedDataMap.get(ENGLISH_LOCALE))
					&& StringUtils.isNotBlank(localizedDataMap.get(ENGLISH_LOCALE).get(key))) {
				return CLS_PANEL_EMPTY_STRING.equalsIgnoreCase(localizedDataMap.get(ENGLISH_LOCALE).get(key))
						? EMPTY_STRING : localizedDataMap.get(ENGLISH_LOCALE).get(key);
			}
		}
		return defaultValue;
	}

	// This method will get the locale from the MDC and then call the getLocalizedValue
	// method
	public static String getLocalizedValue(final String key, final String defaultValue) {
		String locale = MdcUtility.getConstantValue(LOCALE);
		return getLocalizedValue(key, defaultValue, locale);
	}

	// This method is used to get the localized value for the given localeMsgConstants
	public static String getLocalizedValue(final LocaleMsgConstants localeMsgConstants) {
		String locale = MdcUtility.getConstantValue(LOCALE);
		String msg = getLocalizedValue(localeMsgConstants.getLocaleMsgKey(), localeMsgConstants.getDefaultMsgValue(),
				locale);
		log.info("Localisation key : {} and localized message : {}", localeMsgConstants.getLocaleMsgKey(), msg);
		return msg;
	}

	// This method is used to get the localized value for the given localeMsgConstants and
	// the variable values
	public static String getLocalizedValue(final LocaleMsgConstants localeMsgConstants,
			final Map<LocaleMsgVariable, String> variableValues) {
		String msg = getLocalizedValue(localeMsgConstants.getLocaleMsgKey(), localeMsgConstants.getDefaultMsgValue());
		log.info("Localisation key : {} and localized message : {}", localeMsgConstants.getLocaleMsgKey(), msg);
		return generateLocaleMsg(msg, variableValues);
	}

	/*
	 * This method is used to replace the variables in the message with the values
	 * provided in the map. The map should have the variable name as the key and the value
	 * as the value
	 */
	private static String generateLocaleMsg(final String msg, final Map<LocaleMsgVariable, String> variableValues) {
		String finalMsg = msg;
		for (Map.Entry<LocaleMsgVariable, String> entry : variableValues.entrySet()) {
			finalMsg = msg.replace(entry.getKey().getLocaleMsgVarName(), entry.getValue());
		}
		return finalMsg;
	}

	public void refreshSupportedLocalesCache(final Boolean shouldRequestInternalLocalizationApi) throws Exception {
		log.info("Refreshing supported locales cache.");
		Set<String> newSupportedLocales;
		if (shouldRequestInternalLocalizationApi) {
			newSupportedLocales = internalLocalizationClient.getResponseFromInternalLanguagesApi();
			// call that controller which will directly call getResponseFromLanguagesApi
			// method
		}
		else {
			newSupportedLocales = localizationClient.getResponseFromLanguagesApi();
		}
		if (!CollectionUtils.isEmpty(newSupportedLocales)) {
			log.info("Supported locale values received from " + LOCALIZATION_GET_LANGUAGES_API + " are : {}",
					newSupportedLocales);
			supportedLocales = newSupportedLocales;
		}
	}

	public void refreshLocalizedDataCache(final Boolean shouldRequestInternalLocalizationApi) throws Exception {
		log.info("Refreshing localized data cache.");
		/*
		 * If somehow we didn't get supported languages from localization system, we will
		 * use the default set of locales
		 */
		Set<String> localeSet = CollectionUtils.isEmpty(supportedLocales) ? DEFAULT_SUPPORTED_LOCALES
				: supportedLocales;
		for (String locale : localeSet) {
			Map<String, String> keyMessageMap;
			if (shouldRequestInternalLocalizationApi) {
				keyMessageMap = internalLocalizationClient.getResponseFromInternalMessagesApi(locale);
				// call that controller which will directly call
				// getResponseFromMessagesApi method
			}
			else {
				keyMessageMap = localizationClient.getResponseFromMessagesApi(locale);
			}
			if (!CollectionUtils.isEmpty(keyMessageMap)) {
				log.info("Messages received from localization system for {} locale are : {}", locale, keyMessageMap);
				localizedDataMap.put(locale, keyMessageMap);
			}
		}
	}

}
