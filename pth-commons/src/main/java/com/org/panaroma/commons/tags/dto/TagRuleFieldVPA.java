package com.org.panaroma.commons.tags.dto;

import com.org.panaroma.commons.tags.TagRuleTxnDto;
import io.micrometer.common.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@AllArgsConstructor
@ToString
public class TagRuleFieldVPA implements TagRuleField, Serializable {

	private String vpa;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		TagRuleFieldVPA that = (TagRuleFieldVPA) o;
		return vpa.equals(that.vpa);
	}

	@Override
	public int hashCode() {
		return vpa.hashCode();
	}

	@Override
	public boolean isValidRule(TagRuleTxnDto tagRuleDto) {
		return vpa.equals(tagRuleDto.getPayeeVpa());
	}

	public static TagRuleFieldVPA of(String dbFormatValue) {
		if (StringUtils.isBlank(dbFormatValue)) {
			return null;
		}
		return new TagRuleFieldVPA(dbFormatValue.trim());
	}

}
