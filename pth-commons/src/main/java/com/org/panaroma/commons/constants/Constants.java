package com.org.panaroma.commons.constants;

import com.org.panaroma.commons.dto.TransactionTypeEnum;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class Constants {

	public static final String MOBILE_NUMBER = "mobileNumber";

	public static final String REMITTER_MOBILE = "remitterMobile";

	public static final String REMITTER_NAME = "remitterName";

	public static final String BENEFICIARY_NAME = "benefName";

	public static final String DYNAMIC_KEY_REGEX = "#\\{[^#{]*\\}";

	public static final String BENEFICIARY_MOBILE = "benefMobile";

	public static final String USER_IMAGE_URL_KEY = "userImageUrl";

	public static final String PAYTM_IFSC = "PYTM0123456";

	public static final String NON_MERCHANT_MCC = "0000";

	public static final String DOT_PNG = ".png";

	public static final String DEBIT_FLAG = "D";

	public static final String CREDIT_FLAG = "C";

	public static final String UNDERSCORE_DOC = "_doc";

	public static final String OTHER_PARTY_ENTITY_ID = "otherPartyEntityId";

	public static final String IS_OTHER_PARTY_ENTITY_ID_VIRTUAL = "isOtherPartyEntityIdVirtual";

	public static final String DOC_UPDATED_DATE = "docUpdatedDate";

	public static final String CONNECT_TIMEOUT = "connectTimeout";

	public static final String SOCKET_TIMEOUT = "socketTimeout";

	public static final String CONNECTION_REQUEST_TIMEOUT = "connectionRequestTimeout";

	public static final String UNDERSCORE_COUNT = "_COUNT";

	public static final String UNDERSCORE_FAILURE_COUNT = "_FAILURE_COUNT";

	public static final String UNDERSCORE_TIME_TAKEN = "_TIME_TAKEN";

	public static final String BRANCH_CODE = "branchcode";

	public static final String UNDERSCORE = "_";

	public static final String DEFAULT = "default";

	public static final String META_DATA = "metaData";

	public static final String LAST_DAY_TIME = "lastDayAndTime";

	public static final String STARTNG_DAY_TIME = "startingDayTime";

	public static final String HYPHEN = "-";

	public static final String DOT_JSON = ".json";

	public static final String IST = "IST";

	public static final String STREAM_SOURCE = "streamSource";

	public static final String ENTITY_ID = "entityId";

	public static final String PARTICIPANTS_AMOUNT = "participants.amount";

	public static final String PARTICIPANTS_CARD_DATA_LAST_FOUR_DIGITS = "participants.cardData.lastFourDigits";

	public static final String AMOUNT = "amount";

	public static final String TXN_DATE = "txnDate";

	public static final String BANK_NAME_FOR_3P_APP_RECEIVER = "Bank account linked to";

	public static final String PARTICIPANTS = "participants";

	public static final String PARTICIPANTS_DOT_ENTITY_ID = "participants.entityId";

	public static final String PARTICIPANTS_DOT_BANK_DATA_DOT_IFSC = "participants.bankData.ifsc";

	public static final String DEBIT_CARD_TXNS = "debitCardTxns";

	/*
	 * Ifsc query means we are fetching the data from ES on the basis of ifsc & then
	 * filtering it in memory to return only those txns to the client where last 2 digits
	 * of account number matches
	 */
	public static final String IFSC_QUERY_USED_FOR_UPI_FILTER = "ifscQueryUsedForUpiFilter";

	public static final String CAN_IFSC_QUERY_BE_USED_FOR_UPI_FILTER_IF_REQUIRED = "canIfscQueryBeUsedForUpiFilterIfRequired";

	// In some cases instead of last 4 digits of account number, we are getting only the
	// last two digits after XX at the beginning
	public static final String XX = "XX";

	public static final String INDIAN_ZONE = "Asia/Kolkata";

	public static final String ES_INDEX_PREFIX = "payment-history-";

	public static final String ES_INDEX_START = "payment-history";

	public static final String DASH = "-";

	public static final String ASTERISK = "*";

	public static final String ES_INDEX_ALIAS = "payment_history_alias";

	public static final String IS_SELF_TRANSFER = "isSelfTransfer";

	public static final String COMPOSITE_USERID = "composite_userId";

	public static final String RRN = "rrn";

	public static final String CACHE = "cache";

	public static final String PIPE_SYMBOL = "|";

	public static final String COMMA = ",";

	public static final String SINGLE_QUOTE = "'";

	public static final String IMPS = "imps";

	public static final String V1 = "v1";

	public static final String BASE_AMOUNT = "baseAmount";

	public static final String GST_AMOUNT = "gstAmount";

	public static final String DEEMED_IDENTIFIER_KEY = "isDeemed";

	public static final String V2 = "v2";

	public static final String V3 = "v3";

	public static final String V4 = "v4";

	public static final String FOREIGN_CURRENCY = "foreignCurrency";

	public static final String Uth_ANALYTICS_WHITE_LISTING = "UthAnalyticsWhiteListing";

	public static final String BANK_NAME = "bankName";

	public static final String RECEIVED_IN = "Received in";

	public static final String PAID_FROM = "Paid from";

	public static final String RESPONSE = "response";

	public static final String ZERO_DELTA = "zero_delta_";

	public static final String USER_RATE_LIMITING_WHITE_LISTING = "UserRateLimitingWhiteListing";

	public static final String CHAT = "Chat";

	public static final String CLIENTID = "clientid";

	public static final String CHANNEL = "channel";

	public static final String DUMMY_VPA = "dummyvpa@upi";

	public static final String UTH_DC_CLIENT_FOR_INTERNAL_LOCALIZATION_APIS = "uthdc";

	public static final String UPI_TXN_ID = "upiTxnId";

	public static final String PTH = "PTH";

	public static final String FUTURE_LOG_REMOVER_IDENTIFIER = ">>>>>>>> ";

	public static final String BACK_FILLING_IDENTIFIER_MANDATE = "backfillingIdentifier";

	public static final String ES = "ES";

	public static final long MILLISECONDS_IN_A_DAY = 24 * 60 * 60 * 1000L;

	public static class RelayConstants {

		public static final String IS_FROM_RELAY = "ifr";

		public static final String ONE = "1";

	}

	public static String JIRA_ID_CONTEXT_MAP_KEY = "jiraId";

	public static final String ACTOR_CONTEXT_MAP_KEY = "actor";

	public static String STATUS_UPDATED_VIA_UPDATE_TXN_STATUS_API_IDENTIFIER = "statusUpdatedViaUpdateTxnStatusApi";

	public static final List<TransactionTypeEnum> P2P_TXN_TYPES_LIST = Collections
		.unmodifiableList(Arrays.asList(TransactionTypeEnum.P2P_OUTWARD, TransactionTypeEnum.P2P_INWARD,
				TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD, TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD,
				TransactionTypeEnum.P2P_OUTWARD_REVERSAL, TransactionTypeEnum.P2P_INWARD_REVERSAL,
				TransactionTypeEnum.P2P2M, TransactionTypeEnum.P2P2M_REFUND, TransactionTypeEnum.P2P2M_INWARD,
				TransactionTypeEnum.P2P2M_OUTWARD));

}
