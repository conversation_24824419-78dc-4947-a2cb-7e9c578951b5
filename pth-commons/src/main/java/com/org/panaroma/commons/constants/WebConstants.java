package com.org.panaroma.commons.constants;

import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.enums.MandateActionEnum;
import com.org.panaroma.commons.enums.MandateStatusEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.Getter;

import static com.org.panaroma.commons.constants.Constants.UTH_DC_CLIENT_FOR_INTERNAL_LOCALIZATION_APIS;

public class WebConstants {

	public static final String TRANSACTION_ID = "txnId";

	public static final String TXN_DATE = "txnDate";

	public static final String SOURCE_CONTEXT = "sourceContext";

	public static final String LOCALE = "locale";

	public static final String SHOW_BANK_DATA = "showBankData";

	public static final String SHOW_ONLY_TIME_LINE = "showOnlyTimeline";

	public static final String VERSION = "version";

	public static final String OPEN_SOURCE = "openSource";

	public static final String SHOW_HIDDEN_TXN = "showHiddenTxn";

	public static final String ES_HOST_TYPE = "esHostType";

	public static final String NEXT_REQ_HANDLER_IDENTIFIER = "nextRequestHandlerIdentifier";

	public static final String VIRTUAL_FROM_DATE_USED = "virtualFromDateUsed";

	public static final String HTTP_CODE_STR = "httpCodeStr";

	public static final String TRANSACTION_DATE_EPOCH_TIME = "txnDate";

	public static final String STREAM_SOURCE = "streamSource";

	public static final String ENTITY_ID = "entityId";

	public static final String FE_REPORTED_RECEIVER_CUST_ID = "feReportedReceiverCustId";

	public static final String CUSTOMER_CREATION_DATE = "customerCreationDate";

	public static final String CUTOFF_DATE_TO_IDENTIFY_NEW_CUSTOMER_IN_EPOCH = "cutoffDateToIdentifyNewCustomerInEpoch";

	public static final String IS_NEW_CUSTOMER = "isNewCustomer";

	public static final String SHOW_IN_LISTING = "showInListing";

	public static final String PRODUCES = "application/json; charset=UTF-8";

	public static final String SECOND_PARTY_ID = "secondPartyId";

	public static final String ES_SECOND_PARTY_ID = "esSecondPartyId";

	public static final String CREDIT = "credit";

	public static final String DEBIT = "debit";

	public static final String TXN_INDICATOR = "txnIndicator";

	public static final String ES_DOC_TXN_INDICATOR = "esDocTxnIndicator";

	public static final String ES_DOC_STATUS = "esDocStatus";

	public static final String PARTICIPANTS_LIST_IDENTIFIER = "participants";

	public static final String TAGS_LIST_IDENTIFIER = "tags";

	public static final String CART_DATA_LIST_IDENTIFIER = "cartDetails";

	public static final String ES_TAG = "esTag";

	public static final String PANAROMA_SERVICE = "PS";

	public static final String PANAROMA_PASSBOOK = "PP";

	public static final String PANAROMA_INTERNAL_SERVICE = "PIS";

	public static final String AUTHORIZATION = "Authorization";

	public static final String CHANNEL = "channel";

	public static final String ONLINE_ONUS_NATIVE = "ONLINE_ONUS_NATIVE";

	public static final String USERID_SMALLCASE = "userid";

	public static final String LATEST_TXN_API = "latestTxn";

	public static final String ANY_TXN_API = "anyTxn";

	public static final String LATEST_CONTROLLER_SERVICE_IDENTIFIER = "/payments-history-v2/int/v1/latestTxn";

	public static final String PASSBOOK_API = "passbook";

	public static final String CHAT_DEEPLINK_SOURCE_VALUE = "passbook_view_history";

	public static final String TXN_DETAIL_API = "txnDetail";

	public static final String RECENT_TXNS_API = "recentTxns";

	public static final String TOGGLE_VISIBILITY_API = "toggleVisibility";

	public static final String CST_DETAILS_API = "cstBotDetail";

	public static final String CST_PANEL_API = "cstPanel";

	public static final String CST_SEARCH_API = "cstSearch";

	public static final String TXN_LIST = "txnList";

	public static final String SOURCE = "source";

	public static final String REQUEST_TOKEN = "Request-Token";

	public static final String UPDATE_TAGS = "/updateTags";

	public static final String UPDATE_TAGS_API = "updateTags";

	public static final String GET_TAGS = "/getTags";

	public static final String GET_TAGS_API = "getTags";

	public static final String GET_USER_TAGS_SUMMARY_API = "/getUserTagsSummary";

	public static final String MONTHLY_SPENT_RECEIVED_ON_TAG_API = "/tagSummary";

	public static final String TAG = "tag";

	public static final String IVR_TXN_LIST = "/txnList";

	public static final String TXN_TAGS = "txnTags";

	public static final String CURRENT_TXN_TAGS = "currentTxnTags";

	public static final String USER_TAGS = "userTags";

	public static final String SYSTEM_TAGS = "systemTags";

	public static final String DEFAULT_TAGS = "DEFAULT_TAGS";

	public static final String SUGGESTED_TAGS = "suggestedTags";

	public static final String SEARCH_SUPPORTED_TAGS = "searchSupportedTags";

	public static final String REQUEST_ID = "requestId";

	public static final String ORIGINAL_REQUEST_ID = "originalRequestId";

	public static final String REDIRECTED_REQUEST = "redirectedRequest";

	public static final String SERVER_NAME = "serverName";

	public static final String TOMCAT = "tomcat";

	public static final String NETTY = "netty";

	public static final String PTH_VERSION = "pthVersion";

	public static final String CLIENT_REQUEST_ID = "clientRequestId";

	public static final String CATEGORY_ID = "categoryId";

	public static final String OAUTH_TOKEN = "Session-Token";

	public static final String JWT_TOKEN = "jwtToken";

	public static final String USER_TOKEN = "userToken";

	public static final String USER_ID = "USERID";

	public static final String USER_ID_FROM_REQUEST = "USER_ID_FROM_REQUEST";

	public static final String TRUE_CLIENT_IP = "true-client-ip";

	public static final String USER_ID_2 = "UserId";

	public static final String USER_ID_DEFAULT = "USERID,DEFAULT";

	public static final String USERID = "userId";

	public static final String CST_HOME = "CST_HOME";

	public static final String TS_CST = "TS_CST";

	public static final String PAYLOAD_HASH = "payloadHash";

	public static final String IS_JWT_ENABLED_API = "isJwtEnabledApi";

	public static final String TXN_NARRATION = "txn_narration";

	public static final String ERROR_MESSAGE = "errorMessage";

	public static final String NOTES_KEY = "NotesKey";

	public static final String NOTES = "notes";

	public static final String MERCHANT_NAME = "merchantName";

	public static final String WALLET_TXN_ID = "Wallet Ref No";

	public static final String UPI_REFERENCE_NO = "UPI Ref No";

	public static final String UMN_REFERENCE_NO = "UMN";

	public static final String GET_DETAIL_FUNC = "getDetail";

	public static final String GET_DETAILS_FUNC = "getDetails";

	public static final String GET_LISTING_FUNC = "getListingData";

	public static final String IMPS_REF_NUM = "IMPS Ref No";

	public static final String NEFT_REF_NUM = "NEFT Ref No";

	public static final String MANDT_REF_NUM = "Mandate Ref No";

	public static final String FXD_DPT_NUM = "Fixed Deposit No";

	public static final String RRN = "rrn";

	public static final String MOBILE = "mobile";

	// public static final String BANK_REFERENCE_NO = "Bank Reference No.";
	public static final String PARTNER = "Partner";

	public static final String VENDOR_NAME = "vendorName";

	public static final String BANK_REFERENCE_NO = "Bank Ref No";

	public static final String RIA_MONEY_TRANSFER_REF_NO = "Ria Money Transfer Ref. No.";

	public static final String ORDER_ID = "Order ID";

	public static final String REFUND_REF_NO = "Refund Ref No";

	public static final String TXN_ID = "Transaction Id";

	public static final String AC_NO = "A/c No. ";

	public static final String CARD = "Card";

	public static final String CARD_NO = "Card No. ";

	public static final String CREDIT_CARD = "Credit Card";

	public static final String DEBIT_CARD = "Debit Card";

	public static final String NET_BANKING = "Net Banking";

	public static final String CREDIT_CARD_NO = "Credit Card No.";

	public static final String DEBIT_CARD_NO = "Debit Card No.";

	public static final String GROUP_ID = "groupId";

	public static final String IS_VISIBLE = "isVisible";

	public static final String P2M_LOGO = "P2M_LOGO";

	public static final String P2M_INTERNATIONAL_LOGO = "P2M_INTERNATIONAL_LOGO";

	public static final String P2M_REVERSAL_INTERNATIONAL_LOGO = "P2M_REVERSAL_INTERNATIONAL_LOGO";

	public static final String MERCHANT_DEFAULT_LOGO = "MERCHANT_DEFAULT_LOGO";

	public static final String UPI_MERCHANT_LOGO = "UPI_MERCHANT_LOGO";

	public static final String ON_HOLD_LOGO = "ON_HOLD";

	public static final String RELEASED_LOGO = "RELEASED_LOGO";

	public static final String P2M_REFUND_LOGO = "P2M_REFUND_LOGO";

	public static final String CASHBACK_LOGO = "Cashback_Logo";

	public static final String IPO_MANDATE_LOGO = "IPO_MANDATE_LOGO";

	public static final String PAYTM_ICON = "Paytm_Logo";

	public static final String DEFAULT_USER_LOGO = "default_user_logo";

	public static final String WALLET_P2P_OUT_LOGO = "WALLET_P2P_OUT_LOGO";

	public static final String UPI_P2P_OUT_LOGO = "UPI_P2P_OUT_LOGO";

	public static final String UPI_P2P_IN_LOGO = "UPI_P2P_IN_LOGO";

	public static final String WALLET_P2P_IN_LOGO = "WALLET_P2P_IN_LOGO";

	public static final String UPI_P2P_OUT_REV_LOGO = "UPI_P2P_OUT_REV_LOGO";

	public static final String WALLET_P2P_OUT_REV_LOGO = "WALLET_P2P_OUT_REV_LOGO";

	public static final String ADD_MONEY_LOGO = "ADD_MONEY_LOGO";

	public static final String AUTOMATIC_ADD_MONEY_LOGO = "AUTOMATIC_ADD_MONEY_LOGO";

	public static final String ADD_MONEY_BANK_LOGO = "ADD_MONEY_BANK_LOGO";

	public static final String UPI_WALLET_CREDIT_REVERSAL_LOGO = "UPI_WALLET_CREDIT_REVERSAL_LOGO";

	public static final String SELF = "Self";

	public static final String TRANSFER = "Transfer";

	public static final String SELF_TRANSFER = "SELF_TRANSFER";

	public static final String SELF_TRANSFER_CATEGORY = "self_transfer";

	/*
	 * This flag will be received from TS & will have value "ToMobile" for txns initiated
	 * via To Mobile flow on App & will have value "ToAccount" for txns initiated via To
	 * Bank Account flow on App
	 */
	public static final String TXN_INITIATION_MODE = "txnInitiationMode";

	public static final String TO_MOBILE_MODE = "ToMobile";

	public static final String TO_ACCOUNT_MODE = "ToAccount";

	public static final String SELF_TRANSFER_KEYWORD = "Self Transfer";

	public static final String RECURRING_MANDATE_POWEREDBY_LOGO = "RECURRING_MANDATE_POWEREDBY_LOGO";

	public static final String ULTRA_RECURRING_MANDATE_POWEREDBY_LOGO = "ULTRA_RECURRING_MANDATE_POWEREDBY_LOGO";

	public static final String UPI_CC_POWEREDBY_LOGO = "UPI_CC_POWEREDBY_LOGO";

	public static final String ULTRA_UPI_CC_POWEREDBY_LOGO = "ULTRA_UPI_CC_POWEREDBY_LOGO";

	public static final String RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO = "RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO";

	public static final String ULTRA_RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO = "ULTRA_RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO";

	public static final String OC_100_GUIDELINES_LOGO = "OC_100_GUIDELINES_LOGO";

	public static final String ULTRA_UPI_POWEREDBY_LOGO = "ULTRA_UPI_POWEREDBY_LOGO";

	public static final String UPI_LOGO = "UPI_LOGO";

	public static final String WALLET_POWEREDBY_LOGO = "WALLET_POWEREDBY_LOGO";

	public static final String ULTRA_WALLET_POWEREDBY_LOGO = "ULTRA_WALLET_POWEREDBY_LOGO";

	public static final String BANK_POWEREDBY_LOGO = "BANK_POWEREDBY_LOGO";

	public static final String ULTRA_BANK_POWEREDBY_LOGO = "ULTRA_BANK_POWEREDBY_LOGO";

	public static final String POSTPAID_LOGO = "POSTPAID_LOGO";

	public static final String RUPAY_LOGO = "rupay";

	public static final String RUPAY_LOGO_NAME = "rupay_credit_card.png";

	public static final String UPI_VIA_CC_FLAG = "isUpiViaCC";

	public static final String UPI_CC_EMI_CTA = "upiCcEmiCta";

	public static final String PPBL_CONSTANT_LOGO = "PPBL_CONSTANT_LOGO";

	public static final String GIFT_VOUCHER_RECEIVED = "Gift voucher received";

	public static final String GIFT_VOUCHER_NARRATION = "As Gift Voucher";

	public static final String GV_PURCHASED = "GV_PURCHASED";

	public static final String GV_REDEEM = "GV_REDEEM";

	public static final String WALLET_TO_WALLET = "walletToWalletTransfer";

	public static final String TXN_PURPOSE = "txn_purpose";

	public static final String TRANSACTION_PURPOSE = "txnPurpose";

	public static final String MANDATE_TXN_TYPE = "mandateTxnType";

	public static final String VALIDITY = "validity";

	public static final String VALIDITY_END_DATE = "validityEndDate";

	public static final String IPO_MONEY_BLOCKED_CTA_LABEL = "How does IPO blocking work?";

	public static final String IPO_MONEY_UNBLOCKED_CTA_LABEL = "What does money unblocked mean?";

	public static final String PURPOSE_CODE = "purpose";

	public static final String PAYTM_GIFT_VOUCHER = "Paytm Gift Voucher";

	public static final String GIFT_VOUCHER = "Gift Voucher";

	public static final String PAYMENT_SYSTEM_TYPE_UPI = "UPI";

	public static final String PAYMENT_SYSTEM_TYPE_GV = "GV";

	public static final String PAYTMGIFTVOUCHER = "PaytmGiftVoucher";

	public static final String PAYER_NAME = "payerName";

	public static final String PAYER_MOBILE_NUMBER = "payerPhoneNo";

	public static final String PAYTM = "Paytm";

	public static final String PAYTM_VPA = "paytm";

	public static final String VPA_SPLITTER = "@";

	public static final String IS_BANK_ONLY_TXN = "isBankOnlyTxn";

	public static final String WALLET_CLOSING_BALANCE = "wallet_closing_balance";

	public static final String MONEY_RECEIVED = "Money Received";

	public static final String MONEY_SENT = "Money Sent";

	public static final String MONEY_TRANSFER_FAILED = "Money Transfer Failed";

	public static final String MONEY_TRANSFER_PENDING = "Money Transfer Pending";

	public static final String TXN_HISTORY_USER_IMAGE_CLIENT = "txnHistoryUserImageClient";

	public static final String USER_IMAGE_TAG = "userImageTag";

	public static final String NAME_VERIFIED_LOGO = "name_verified_logo";

	public static final Integer OK_200 = 200;

	public static final String TRANSFER_FAILED = "Transfer Failed";

	public static final String TRANSFER_PENDING = "Transfer Pending";

	public static final String SENT_SUCCESSFULLY = "Sent Successfully";

	public static final String FOR_FAILED_TRANSFER_FROM = "For failed transfer from";

	public static final String MONEY_REVERSED = "Money Reversed";

	public static final String ES_OPTIMISATION_FOR_INACTIVE_USERS_ENABLED = "esOptimisationForInactiveUsersEnabled";

	public static final String IS_NTU_CACHE_ENABLED = "isNtuCacheEnabled";

	public static final String NON_TRANSACTING_USER_CACHE = "nonTransactingUserCache";

	public static final String LISTING_CACHE = "listingCache";

	public static final String ERROR_CODE = "errorCode";

	public static final String ERROR_MSG = "errorMessage";

	public static final String MERCHANT_GENRE = "merchantGenre";

	public static final String PARENT_TXN_ID = "parentTxnId";

	public static final String UPI_ORDER_ID = "upiOrderId";

	public static final String PAYMT_USER = "Paytm User";

	public static final String VPA_HANDLE_FIELD_NAME = "handle";

	public static final String PAYTM_HANDLE = "@paytm";

	public static final String BHIM_UPI = "bhim-upi";

	public static final String BHIM_UPI_PNG = "bhim-upi.png";

	public static final String FROM = "From";

	public static final String PAID_FROM = "PAID FROM";

	public static final String IN_YOUR = "In Your";

	public static final String FOR = "For";

	public static final String FOR_FAILED_TRANSFER_TO = "For failed transfer to";

	public static final String REVERSAL_OF_FAILED_MONEY_TRANSFER_VIA_UPI = "Reversal of Failed Money transfer via UPI";

	public static final String FROM_YOUR = "From Your";

	public static final String USING_YOUR = "Using Your";

	public static final String TO = "To";

	public static final String TO_YOUR = "To Your";

	public static final String TRANSFERRED_TO_SELF = "Transferred to Self";

	public static final String BY = "By";

	public static final String VIA_NET_BANKING = "via Net Banking";

	public static final String AMOUNT_PARAM = "amount";

	public static final String REFUND_TO_SOURCE = "Refund To Source";

	public static final String S_CARD = "%s card";

	public static final String PASSBOOK_TYPE = "passbookType";

	public static final String RESULT_MSG = "resultMsg";

	public static final String API_VERSION = "apiVersion";

	public static final String SEARCH_API_VERSION = "searchApiVersion";

	public static final String COLLECT = "COLLECT";

	public static final String HTTP_401 = "401";

	public static final String HTTP_530 = "530";

	public static final String FOR_APP_CACHE_RECON = "forAppCacheRecon";

	public static final String INVALIDATE_VERSION = "invalidateVersion";

	public static final String TO_DATE = "toDate";

	public static final String GROUPING_RECON = "groupingRecon";

	public static final String LISTING_TAG_ENABLED = "listingTagEnabled";

	public static final String DEAF_TXN_CTA_LABEL = "Know more or reclaim";

	public static final String DEAF_TXN_NARRATION = "Unclaimed deposits to DEA fund";

	public static final String DEAF_TXN_CTA_LABEL_FOR_LOCALISATION = "deafTxnCtaLabelForLocalisation";

	public static final String BG_APP_SYNC = "bg_app_sync";

	public static final String RPT_PAYMENT_DISABLED_BANK_AND_WALLET = "rptPayment.disabled.bankAndWallet";

	public static final String BLACKLISTED_HANDLES_FOR_VPA_RPT_PMT = "blacklisted.handles.for.vpa.rpt.pmt";

	public static final String MOBILE_NO_DEEPLINK_ENABLED_FOR_VPA2VPA = "mobileNo.deeplink.enabled.for.vpa2vpa";

	public static final String RPT_PAYMENT_ENABLED_FOR_SELF_TRANSFER = "rptPayment.enabled.for.selfTransfer";

	public static final String RPT_PAYMENT_ENABLED_FOR_VPA2ACCOUNT = "rptPayment.enabled.for.vpa2account";

	public static final String DEFAULT_BANK_NAME = "UPI Linked Bank";

	public static class WalletMaintenanceChargeConstants {

		public static final String CHARGES_DESCRIPTION = "Know more or get refund";

		public static final String DEEPLINK = "deeplink";

		public static final String REACTIVATE_PAYTM_WALLET = "Reactivate Paytm Wallet";

		public static final String WMC_DEDUCTED = "wallet_maintenance_charges_deducted";

		public static final String WMC_REVERSED = "wallet_maintenance_charges_reversed";

		public static final String CHARGES_DEDUCTED = "Charges Deducted";

		public static final String WALLET_MAINTENANCE_CHARGE_IS_LEVIED_ON_YOUR_ACCOUNT = "Wallet maintenance charge is levied on your account";

		public static final String FOR = "For";

		public static final String WMC_NAME = "Quarterly Wallet Maintenance Charge";

		public static final String CHARGES_REVERSED = "Charges Reversed";

		public static final String WALLET_MAINTENANCE_CHARGE_REFUNDED_TO_YOUR_ACCOUNT = "Wallet maintenance charge refunded to your account";

		public static final String WMC_REVERSAL_NAME = "Reversal of Quarterly Wallet Maintenance Charge";

		public static final String GST_18 = "GST(@18%)";

		public static final String BASE_AMOUNT_VIEW = "Base Amount";

		public static final String DEDUCTED_AT = "Deducted at";

		public static final String WMC_REVERSAL_NARRATION = "Reversal of Quarterly Wallet Maintenance Charges";

		public static final String WMC_NARRATION = "Deduction of Quarterly Wallet Maintenance Charges";

	}

	// Loan Repayment Constants
	public static class LoanRepaymentConstants {

		public static final String MONEY_ON_HOLD_FOR = "Money on hold for";

		public static final String LOAN_REPAYMENT = "Loan Repayment";

		public static final String MONEY_RELEASED_FOR = "Money released for";

		public static final String PAID_FOR = "Paid for";

		public static final String REFUND_FOR = "Refund for";

		public static final String REPAYMENT_OF_LOAN = "Repayment of Loan";

		public static final String LOAN_APPLICATION_NUMBER = "Loan Application No:";

		public static final String FOR = "For";

		public static final String LAN = "lan";

		public static final String SENT_FROM = "Sent from";

	}

	// Upi Lite Constants Class
	public static class UpiLiteConstants {

		public static final String IS_UPI_LITE_TXN = "isUpiLiteTxn";

		public static final String IS_UPI_LITE_PAYMENT_INSTRUMENT = "isUpiLitePaymentInstrument";

		// Constant related to UPI Lite
		public static final String UPI_LITE_LOGO = "UPI_LITE_LOGO";

		public static final String UPI_LITE = "UPI Lite";

		public static final String UPI_LITE_FOR_DEACTIVATION = "UPI Lite for Deactivation";

		public static final String INITIAL_LITE_TOP_UP = "41";

		public static final String RECHARGE_LITE_TOP_UP = "42";

		public static final String DEREGISTER_LITE = "43";

		public static final String ONLINE_LITE_PAY = "44";

		public static final String OFFLINE_LITE_PAY = "45";

		public static final String ZERO_BALANCE_DEREGISTER_LITE = "50";

		public static final String LISTING_NARR_42 = "UPI Lite";

		public static final String P2P = "P2P";

		public static final String P2M = "P2M";

		public static final String ADD_MONEY = "ADD_MONEY";

		public static final String UPI_TO_WALLET = "UPI_TO_WALLET";

		public static final String VIEW_UPI_LITE = "View UPI Lite";

		public static final String REACTIVATE = "Reactivate";

	}

	public static class MandateConstants {

		public static final String RECURRING_MANDATE_LOGO = "RECURRING_MANDATE_LOGO";

		public static final String EXECUTION_NO = "executionNo";

		public static final String MANDATE_AMOUNT = "mandateAmount";

		public static final String EXECUTION_NO_ONE = "1";

		public static final String FOR = " for";

		public static final String MASKED_ACCOUNT_NO = "maskedAccountNumber";

		public static final String PAYEENAME = "payeeName";

		public static final String VALIDITY_START_DATE = "validityStartDate";

		public static final String RECURRING_AUTOMATIC_PAYMENT_CTA_LABEL = "How Does Automatic Payment Work?";

		public static final String AUTOMATIC_PAYMENT_SETUP = "Automatic payment of ₹";

		public static final String SETUP = " setup";

		public static final String AUTOMATIC_PAYMENT = "Automatic payment";

		public static final String dd_MMM_YYYY = "dd MMM YYYY";

		public static final String SENT_FROM = "Sent from";

		public static final String RETRY_ATTEMPT = "retryAttempt";

		public static final String SUCCESS = "SUCCESS";

		public static final String MANAGE_AUTOMATIC_PAYMENT_CTA_DEEPLINK = "manage.automatic.payment.cta.base.deeplink";

		public static final String PAUSE_END_DATE = "pauseEndDate";

		public static final String EXPIRE_ON = "expireOn";

		public static final String VALIDITY_END_DATE = "validityEndDate";

		public static final String FREQUENCY = "frequency";

		public static final String MANDATE_ACTIVITY_INDEX_NAME_IDENTIFIER = "activity";

		public static final String MANDATE_INFO_INDEX_NAME_IDENTIFIER = "info";

		public static final String MANDATE_INFO_INDEX_NAME = "mandate-info-2024";

		public static final String DATE_FORMAT = "dd-MM-yyyy HH:mm:ss";

		public static final String WEEK_MONTH_DATE_TIME_FORMAT = "EEE MMM dd HH:mm:ss z yyyy";

		public static final List<Integer> NON_FINANCIAL_NON_CREATE_RECURRING_MANDATE_ACTION_KEYS = Arrays.asList(
				MandateActionEnum.PAUSE.getMandateActionKey(), MandateActionEnum.UNPAUSE.getMandateActionKey(),
				MandateActionEnum.REVOKE.getMandateActionKey(), MandateActionEnum.UPDATE.getMandateActionKey(),
				MandateActionEnum.EXPIRE.getMandateActionKey());

		public static final List<MandateStatusEnum> MANDATE_TERMINAL_STATUS = Arrays.asList(MandateStatusEnum.ACTIVE,
				MandateStatusEnum.PAUSED, MandateStatusEnum.CANCELLED, MandateStatusEnum.SETUP_FAILED,
				MandateStatusEnum.COMPLETED, MandateStatusEnum.EXPIRED);

		public static final String MANDATE_JOURNEY_API_ES_FETCH_LIMIT_KEY = "mandate-journey-api-es-fetch-limit";

		public static final String NEED_TO_HIT_UPI_MANDATE_JOURNEY_API = "need-to-hit-upi-mandate-journey-api";

	}

	public static class UpiInitiationModeMap {

		/*
		 * DEFAULT("00"), QR("01"), SECURE_QR("02"), BHARAT_QR("03"), INTENT("04"),
		 * SECURE_INTENT("05"), NFC("06"), BLE("07"), UHF("08"), AADHAAR("09"), SDK("10"),
		 * UPI_MANDATE("11"), FIR("12"), QR_MANDATE("13"), BBPS("14"), IVR("31")
		 */
		public static final String INITIATION_MODE = "initiationMode";

		public static final String INITIATION_MODE_DEFAULT = "00";

		public static final String INITIATION_MODE_QR = "01";

		public static final String INITIATION_MODE_SECURE_QR = "02";

		public static final String INITIATION_MODE_BHARAT_QR = "03";

		public static final String INITIATION_MODE_INTENT = "04";

		public static final String INITIATION_MODE_SECURE_INTENT = "05";

		public static final String INITIATION_MODE_NFC = "06";

		public static final String INITIATION_MODE_BLE = "07";

		public static final String INITIATION_MODE_UHF = "08";

		public static final String INITIATION_MODE_AADHAAR = "09";

		public static final String INITIATION_MODE_SDK = "10";

		public static final String INITIATION_MODE_UPI_MANDATE = "11";

		public static final String INITIATION_MODE_FIR = "12";

		public static final String INITIATION_MODE_QR_MANDATE = "13";

		public static final String INITIATION_MODE_BBPS = "14";

		public static final String INITIATION_MODE_IVR = "31";

	}

	public static class NachDishonourConstants {

		public static final String SENT_FROM = "Sent from";

		public static final String NACH_RETURN_CHARGES = "NACH Return Charges";

		public static final String UPI_P2P_OUT_LOGO = "UPI_P2P_OUT_LOGO";

		public static final String DATE_TIME_LABEL = "Deducted at";

		public static final String RECEIVED_IN = "Received in";

		public static final String UPI_P2P_IN_LOGO = "UPI_P2P_IN_LOGO";

	}

	public static class VisaOctConstant {

		public static final String VISA_DIRECT_REVERSAL = "Visa Direct Reversal";

		public static final String VISA_DIRECT_CREDIT = "Visa Direct Credit";

	}

	public static final List<String> VISA_OCT_REVERSAL_REPORT_CODE = Arrays.asList("25002", "25102");

	public static final List<String> VISA_OCT_CREDIT_REPORT_CODE = Arrays.asList("25001", "25101");

	public static final String LISTING_USER_INSTRUMENT_NARRATION_FROM = "From";

	public static final String LISTING_USER_INSTRUMENT_NARRATION_IN = "In";

	public static final String DOLLAR_SIGN = "$";

	public static final String PLACEHOLDER_STRING_USED_AT_FE = "%$0$s";

	public static final String MANDATE_AMOUNT_PLACEHOLDER = "$MANDATE_AMOUNT";

	public static final String LISTING_RESPONSE_MAPPING_ENUM_DEFAULT_SUFFIX = "_DEFAULT";

	public static final String LISTING_NARRATION_CLS_PANEL_PREFIX = "listing_narration_";

	public static final String LISTING_DATE_TIME_LABEL_PREFIX = "listing_date_time_label_";

	public static final String CLS_PANEL_KEY_LISTING_INSTRUMENT_NARRATION_DEBIT_INDICATOR = "listing_instrument_narration_for_debit_txn_indicator";

	public static final String CLS_PANEL_KEY_LISTING_INSTRUMENT_NARRATION_CREDIT_INDICATOR = "listing_instrument_narration_for_credit_txn_indicator";

	public static final List<TransactionTypeEnum> DEBIT_INSTRUMENT_NARRATION_USED_FOR_CREDIT_TXN_INDICATOR_TXN_TYPES = Arrays
		.asList(TransactionTypeEnum.ADD_MONEY, TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE,
				TransactionTypeEnum.LITE_TOPUP_MANDATE);

	public static final String SUCCESS = "Success";

	public static final String PASSBOOK_CONTROLLER_SERVICE_IDENTIFIE = "/pth/int/v1/source-passbook";

	public static final String HISTORY_V2_URL = "/pth/";

	public static final String HISTORY_V2_URL_DC = "/uth-v2/";

	@Getter
	public enum Passbook {

		DETAIL("/txnDetail"), LISTING("/passbook");

		final String value;

		Passbook(final String value) {
			this.value = value;
		}

	}

	public static final String FAILED = "Failed";

	public static final String PENDING = "Pending";

	public static final String WALLET = "Wallet";

	public static final String PASSBOOK = "Passbook";

	public static final String WALLET_PASSBOOK_TYPE = "wallet";

	public static final String UPI = "UPI";

	public static final String CST = "CST";

	public static final String TPAP_PANEL = "TPAP_PANEL";

	public static final String UPI_SWITCH_V2 = "UPI_SWITCH";

	public static final String RECURRING_MANDATE = "RECURRING_MANDATE";

	public static final String UPI_CC = "UPI_CC";

	public static final String RECURRING_MANDATE_AND_UPI_CC = "RECURRING_MANDATE_AND_UPI_CC";

	public static final String UPI_LITE = "UPI_LITE";

	// The below list contains clients which are used in apis which doesn't work on
	// payload hash jwt token authorization concept
	public static final List<String> NON_PAYLOAD_HASH_JWT_AUTHORIZATION_CLIENTS = Arrays.asList("PMS", "upiswitch",
			UTH_DC_CLIENT_FOR_INTERNAL_LOCALIZATION_APIS);

	public static final String PMS = "PMS";

	public static final String UPI_SWITCH = "upiswitch";

	public static final String PPBL = "PPBL";

	public static final String PG = "PG";

	public static final String TOLL_TRANSACTION_ID = "tollTransactionId";

	public static final String WALLET_TXN_TYPE = "walletTxnType";

	public static final String WALLET_TYPE = "walletType";

	public static final String WALLET_AUTOMATIC_TXN_TYPE = "53";

	public static final String PAYTM_WALLET = "Paytm Wallet";

	public static final String POSTPAID_LOAN = "Postpaid Loan";

	public static final String IFSC_NPCI = "ifsc.npci";

	public static final String DEFAULT_BANK = "default";

	public static final String TXN_CATEGORY = "txnCategory";

	public static final String TXN_TYPE = "txnType";

	public static final String SUB_CLIENT = "subClient";

	public static final String CST_SUB_CLIENT = "cst";

	public static final String FILTER_TYPE = "filterType";

	public static final String ES = "es";

	public static final String NAME = "name";

	public static final String IFSC_FILTER = "ifsc";

	public static final String DEEPLINK = "deeplink";

	public static final String VIEW_DETAILS = "View Details";

	public static final String TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID = "txn.details.config.mapping.for.onus.merchant.verticalId";

	public static final String TXN_DETAILS_CONFIG_MAPPING_FOR_ONUS_MERCHANT_VERTICALID_PREFIX = "txn.details.config.mapping.for.onus.merchant.verticalId.";

	public static final String NEED_HELP_CTA_CONFIG_MAP = "need.help.cta.config.map";

	public static final String ACCOUNT_TYPE = "account_type";

	public static final String FILTER_TYPE_ACCOUNT_TYPE = "accountType";

	public static final String PAYMENT_SYSTEM = "paymentSystem";

	public static final String PASSBOOK_FILTER = "passbookFilter";

	public static final String CARD_TYPE = "cardType";

	public static final String DATE = "date";

	public static final String DATE_RANGE_FILTER_TYPE = "Date_Range";

	public static final String DATE_RANGE = "dateRange";

	public static final String AMOUNT_RANGE = "amountRange";

	public static final String ES_DATE_RANGE = "esDateRange";

	public static final String DATE_RANGE_VALUE = "dateRangeValue";

	public static final String LISTING_FROM_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS Z";

	public static final String CUSTOMER_CREATION_DATE_FORMAT = "MMM d, yyyy h:mm:ss a";

	public static final String DEFAULT_CUSTOMER_CREATION_DATE = "Aug 1, 2021 0:00:00 PM";

	public static final String DEFAULT_FROM_DATE_LISTING_MONTH_DURATION = "default-from-date-listing-month-duration";

	public static final String yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";

	public static final String EEE_MMM_dd_HH_mm_ss_z_yyyy = "EEE MMM dd HH:mm:ss z yyyy";

	public static final String yyyy_MM_dd_T_HH_mm_ss_fff_Z = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

	public static final String yyyy_MM_dd = "yyyy-MM-dd";

	public static final String DYNAMIC_IDENTIFIER = "%s";

	public static final String WALLET_LINKED_TO = "Wallet linked to ";

	public static final String PAYTM_USER = "Paytm User";

	public static final String USER = "User";

	public static final String PAYTM_MERCHANT = "Paytm Merchant";

	public static final String MERCHANT = "Merchant";

	public static final String BANK_TXN_ID = "bankTxnId";

	public static final String IS_FILTER_APPLIED = "filterApplied";

	public static final String LISTING_CUSTOM_NARRATION = "listingCustomNarration";

	public static final String LISTING_CUSTOM_NARRATION_NAME = "listingCustomNarrationName";

	public static final String DETAILS_CUSTOM_NARRATION = "detailsCustomNarration";

	public static final String IS_BANK_DATA = "isBankData";

	public static final String BANK = "BANK";

	public static final String C_D_SERIAL_NO = "creditDebitSerialNumber";

	public static final String REPORT_CODE = "reportCode";

	public static final String PART_TRN_TYPE = "part_trn_type";

	public static final String DYNAMIC_KEY_REGEX = "#\\{[^#{]*\\}";

	public static final String DYNAMIC_HIGHLIGHT_QUERY_REGEX = "<[^<]*>";

	public static final String DROOLS_ACCT_NUM_DYNAMIC_KEY_REGEX = "%\\{AcctNum[^%{]*\\}";

	public static final String DROOLS_MOB_NUM_DYNAMIC_KEY_REGEX = "%\\{MobNum[^%{]*\\}";

	public static final String DEFAULT = "default";

	public static final String OCL = "ocl";

	public static final String TOLL = "toll";

	public static final String TRAN_ID = "transactionId";

	public static final String LISTING = "Listing";

	public static final String DETAIL = "Detail";

	public static final String DETAIL_V2 = "DetailV2";

	public static final String DETAIL_V3 = "detailV3";

	public static final String TRAN_PARTICULAR = "tranParticular";

	public static final String TRANSACTIONID = "transactionId";

	public static final String ACC_NUM = "accNum";

	public static final String TXN_DESC2_VALUE = "txnDesc2Value";

	public static final String TXN_DESC3_VALUE = "txnDesc3Value";

	public static final String TXN_DESC4_VALUE = "txnDesc4Value";

	public static final String CONTEXT_AMOUNT = "amount";

	public static final String STATUS = "status";

	public static final String VERTICAL_ID = "verticalId";

	public static final String VERTICAL_NAME = "verticalName";

	public static final String IDENTIFIER = "Identifier";

	public static final String TDS_AMOUNT = "tds_amount";

	public static final String INTEREST_AMOUNT = "interest_amount";

	public static final String INTEREST_RATE = "interest_rate";

	public static final String MATURITY_PERIOD_DAYS = "maturity_period_days";

	public static final String MATURITY_PERIOD_MONTHS = "maturity_period_months";

	public static final String CBS_TRAN_ID = "cbs_tran_id";

	public static final String CBS_REFUND_ID = "cbs_refund_id";

	public static final String PAYTM_PAYMENTS_BANK = "Paytm Payments Bank";

	public static final String FD_CREATION_FAILED_MESSAGE = "Your Fixed Deposit status will be confirmed shortly";

	public static final String CUSTOM_SECOND_PARTY_LOGO = "customSecondPartyLogo";

	public static final String FIXED_DEPOSIT = "Fixed Deposit";

	public static final String CHAT_IDENTIFIER = "identifier";

	public static final String CBS_REPORT_CODE = "cbsReportCode";

	public static final String CBS_TXN_ID = "cbsTxnId";

	public static final String CBS_TXN_TYPE = "cbsTxnType";

	public static final String RRN_NUMBER = "rrnNumber";

	public static final String ICON = "icon";

	public static final String CHAT_PROFILE_TYPE = "type";

	public static final String VPA_TYPE = "VPA";

	public static final String VPAM_TYPE = "VPAM";

	public static final String CUSTOMER_TYPE = "CUSTOMER";

	public static final String MERCHANT_TYPE = "MERCHANT";

	public static final String MERCHANT_ID = "mid";

	public static final String BANK_TYPE = "BANK";

	public static final String STRING_EMPTY = "";

	public static final String UPI_LINKED_BANK_ACCOUNT = "UPI linked Bank Account";

	public static final String AC = " A/C ";

	public static final String LINKED_TO = " linked to ";

	public static final String BHIM_UPI_NAME = "Bhim Upi";

	public static final String PAYTM_POSTPAID = "Paytm Postpaid";

	public static final String LINKED_BANK_ACCOUNT = "Linked bank account";

	public static final String ACCOUNT_NAME = "Account";

	// Request Money Cta Constant
	public static final String REQUEST_MONEY_TYPE = "REQUEST_MONEY_TYPE";

	public static final String URN = "urn";

	public static final String IR_TRANSACTION_ID = "transactionId";

	public static final String REQUEST_MONEY = "Request Money";

	public static final String REQUEST_MONEY_ENABLE = "requestMoneyEnabled";

	public static final String REQUEST_MONEY_TRUE = "true";

	public static final String PG_TRANSACTION_ID = "pgTxnId";

	public static final String ACCOUNT_NUMBER = "accountNumber";

	public static final String IFSC = "ifsc";

	public static final String FOR_UPDATES = "forUpdates";

	// Fastag constants
	public static final String FASTAG = "FASTAG";

	public static final String FASTAG_TOPUP = "FASTAG_TOPUP";

	public static final String VEHICLE_REG_NO = "Vehicle Reg no:";

	public static final String TOLL_CROSSED_ON = "Toll crossed on:";

	public static final String MONEY_DEDUCTED_ON = "Money deducted on:";

	public static final String FASTAG_ID = "Fastag ID:";

	public static final String LINKED_BANK = " ";

	public static final String LINKED_MOBILE_NO = "Linked Mobile No.:";

	public static final String SENT_FROM = "Sent from:";

	public static final String FASTAG_ORDER_ID = "Order ID:";

	public static final String SPACE = " ";

	public static final String PTH = "pth";

	public static final String BEARER = "Bearer";

	public static final String IPO = "IPO";

	public static final String ADD_DETAIL_SEPARATOR = "\n";

	public static final String CLIENT = "client";

	public static final String ANDROID_CLIENT = "androidapp";

	public static final String IOS_CLIENT = "iosapp";

	public static final String CHAT = "chat";

	public static final String API = "api";

	public static final String APP_VERSION = "version";

	public static final String MONTH = "month";

	public static final String QR_CODE_ID = "qrCodeId";

	public static final String VPA = "vpa";

	public static final String VPA2VPA = "VPA2VPA";

	public static final String VPA2ACCOUNT = "VPA2ACCOUNT";

	public static final String VPA2MERCHANT = "VPA2MERCHANT";

	public static final String UPI_TXN_CATEGORY = "upiTxnCategory";

	public static final String TXNPURPOSE = "txnPurpose";

	public static final String TXNTYPE = "txnType";

	public static final String CHANNEL_CODE = "channelCode";

	public static final String IVR = "IVR";

	public static final String EXPIRE_AFTER = "expireAfter";

	public static final String REF_URL = "refUrl";

	public static final String RESP_CODE = "respCode";

	public static final String REV_RESP_CODE = "reversalRespCode";

	public static final String NPCI_RESP_CODE = "npciRespCode";

	public static final String FIRST_PHASE = "firstPhase";

	public static final String INITIATION_MODE = "initiationMode";

	public static final String REF_CATEGORY = "refCategory";

	public static final String RAW_ACCOUNT_NO = "rawAccountNo";

	public static final String SPACE_REPLACEMENT = "%20";

	public static final String AMPERSAND = "&";

	public static final String COMMA = ",";

	public static final Character COMMA_CHARACTER = ',';

	public static final String HYPHEN = "-";

	public static final String EQUAL_SYMBOL = "=";

	public static final String EMPTY_STRING = "";

	public static final String QR_ID = "%{qrId}";

	public static final String MID = "%{mId}";

	public static final String ACCOUNT = "%{account}";

	public static final String ACC_REF_NUM = "%{accRefNum}";

	public static final String IFSC_REGEX = "%{ifsc}";

	public static final String PAYEE_NAME = "%{pn}";

	public static final String BANK_NAME = "%{bank_name}";

	public static final String AMOUNT = "%{am}";

	public static final String AMOUNT_LABEL = "Amount";

	public static final String ONUS_MERCHANT_FLAG_KEY_FROM_UPI = "onUsMerchant";

	public static final String AMOUNT_LABEL_SMALLCAPS = "amount";

	public static final String VERIFIED_NAME_LABEL = "Verified Name:";

	public static final String UPI_ID = "UPI ID:";

	public static final String RECEIVED_IN2 = "Received In:";

	public static final String REMARKS = "%{tn}";

	public static final String PAYEE_VPA = "%{pa}";

	public static final String RECIPIENT = "%{recipient}";

	public static final String PATTERN_STRING = "%\\{.*?\\}";

	public static final String TIMELINE_URL_PATTERN = "\\{.*?\\}";

	public static final String PERCENT_OPEN_BRACE_REGEX = "%\\{";

	public static final String CLOSE_BRACE_REGEX = "\\}";

	public static final String OPEN_BRACE_REGEX = "\\{";

	public static final String RPT_PYMNT_BENEF_NAME = "%{benefName}";

	public static final String RPT_PYMNT_BENEF_ACCT_NUM = "%{benefAcctNum}";

	public static final String RPT_PYMNT_BENEF_IFSC = "%{benefIfsc}";

	public static final String RPT_PYMNT_BENEF_BANK_NAME = "%{beneficiaryBank}";

	public static final String RPT_PYMNT_REMIT_NAME = "%{remitterName}";

	public static final String RPT_PYMNT_REMIT_ACCT_NUM = "%{remitterAcctNum}";

	public static final String RPT_PYMNT_REMIT_IFSC = "%{remitterIfsc}";

	public static final String RPT_PYMNT_REMIT_BANK_NAME = "%{remitterBank}";

	public static final String BENEF_NAME = "benefName";

	public static final String BENEF_ACCT_NUM = "benefAcctNum";

	public static final String BENEF_IFSC = "benefIfsc";

	public static final String BENEF_BANK_NAME = "beneficiaryBank";

	public static final String REMIT_NAME = "remitterName";

	public static final String REMIT_ACCT_NUM = "remitterAcctNum";

	public static final String REMIT_IFSC = "remitterIfsc";

	public static final String REMIT_BANK_NAME = "remitterBank";

	public static final String RPT_PYMT_BANK_NAME = "bank_name";

	public static final String REPLACABLE_BENEF_BANK_NAME = "%\\{beneficiaryBank\\}";

	public static final String RPT_PYMNT_MERCHANT_NAME = "%{merchantName}";

	public static final String MERCHANT_VPA = "%{vpa}";

	public static final String FD_ASYNC_INTEREST_REPORT_CODE = "20276_C";

	public static final String FD_REPORT_CODES = "20261|20265|20263|20264|20250|20260|20205|20275|20276|23002|20277|20267";

	public static final String FD_ASYNC_RECOVERY_REPORT_CODE = "23002_D";

	public static final String UPI_OUTWARD_REVERSAL_REPORT_CODES = "20504";

	public static final String UPI_INTERNATIONAL_REPORT_CODE = "20512|20513";

	public static final String P2M_REPORT_CODES = "20234|20214|20256|20252|20254|20255|20251|20266|70288|"
			+ "70206|70217|70218|70220|70215|70216|70236|70287";

	public static final String P2M_CARD_LOGO_RPT_CODES = "20210|20234|20214|70203|70205|70216|70236";

	public static final List<String> DOMESTIC_DC_REPORT_CODE = Arrays.asList("20210", "20234", "20214", "20253",
			"20256", "20252", "20254", "20255", "20251", "20266", "70289", "70288");

	public static final List<String> DOMESTIC_DC_REFUND_REPORT_CODE = Arrays.asList("20253", "20256", "20252", "20255",
			"20251", "20266", "70289");

	public static final List<String> RPT_PAYMENT_REPORT_CODE = Arrays.asList("20212", "20420");

	public static final List<String> IMPS_REPORT_CODE = Arrays.asList("20211", "20212");

	public static final List<String> XFER_RPT_PAYMENT_REPORT_CODE = Arrays.asList("20271");

	public static final List<String> P2P_INWARD_RPT_PAYMENT_REPORT_CODE = Arrays.asList("20410");

	public static final List<String> P2P_INWARD_XFER_RPT_PAYMENT_REPORT_CODE = Arrays.asList("20270");

	public static final List<String> P2P_INWARD_VPA2ACCOUNT_RPT_PAYMENT_REPORT_CODE = new ArrayList<>();

	public static final String RMT_ACCT_NUM = "remitterAcctNum";

	public static final List<TransactionTypeEnum> MERCHANT_TRANSACTION_TYPES = Arrays.asList(TransactionTypeEnum.P2M,
			TransactionTypeEnum.P2M_REFUND, TransactionTypeEnum.ADD_AND_PAY, TransactionTypeEnum.P2M_INTERNATIONAL,
			TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL);

	public static final List<String> PAYTM_IFSC_CODE = Arrays.asList("PYTM0123456");

	public static final String MERCHANT_CATEGORY = "merchantCategory";

	public static final String UTH_CATEGORY = "uthCategory";

	public static final String TRUE = "true";

	public static final String FALSE = "false";

	public static final String FILTER_TYPE_MERCHANT_CATEGORY = "merchant_category";

	public static final String FILTER_TYPE_TXN_INDICATOR = "txn_indicator";

	public static final String PAID_SUCCESSFULLY = "Paid Successfully";

	public static final String PAYMENT_FAILED = "Payment Failed";

	public static final String PAYMENT_PENDING = "Payment Pending";

	public static final String UTH_CATEGORY_OTHERS = "OTHERS";

	public static final String ES_TXN_ID_FIELD = "txnId";

	public static final String ES_TXN_SOURCE_FIELD = "streamSource";

	public static final String[] SOURCE_INCLUDES_ARRAY = new String[] { ES_TXN_ID_FIELD, ES_TXN_SOURCE_FIELD };

	public static final Long LONG_NEG_ONE = -1L;

	public static final Long DEFAULT_40_QUERY_SIZE = 40L;

	public static final int ALLOWED_ES_HITS_FOR_DETAIL_METRICS = 5;

	public static final int LISTING_DEFAULT_PAGE_SIZE = 10;

	public static final String PAGE_NO = "pageNo";

	public static final String ONE = "1";

	public static final String FILTER_TYPE_SECOND_PARTY_ID = "entity";

	public static final String IS_RECENT_PAGE_LISTING = "isRecentPageListing";

	public static final String RECENT_WIDGET_NARRATION = "recentWidgetNarration";

	public static final String PROPERTY_NAME = "propertyName";

	public static final String PROPERTY_VALUE = "propertyValue";

	public static final String CREATED_DATE = "createdDate";

	public static final String FROM_DATE_LISTING_FILTER = "from-date-listing-filter";

	public static final String DATE_RANGE_FROM_DATE_LISTING_FILTER = "date-range-from-date-listing-filter";

	public static final String ONUS_VERTICAL_FILTER_FROM_DATE = "onus-vertical-filter-from-date";

	public static final String UPI_CC_FILTER_FROM_DATE = "upi-cc-filter-from-date";

	public static final String MANDATE_HISTORY_FROM_DATE = "mandate-history-from-date";

	public static final String DC_FROM_DATE_LISTING_FILTER = "prodmtdc-from-date-listing-filter";

	public static final String DETAIL_FROM_PARENT_ENABLED = "detail.view.from.parent.txn.enabled";

	public static final String OTHER_PARTY_BANK_DETAILS_FOR_VPA2ACCOUNT_ENABLED = "other-party-bank-details-for-vpa2account-txn-enabled";

	public static final String OTHER_PARTY_BANK_DETAILS_ENABLED = "other-party-bank-details-enabled";

	public static final String OTHER_PARTY_BANK_DETAILS_ENABLED_INWARD = "other-party-bank-details-enabled-inward";

	public static final String TXN_DATE_MANDATORY_FOR_DETAIL_API = "txn-date-mandatory-for-detail-api";

	public static final String SEARCH_FILTER_DISABLED = "search-filter-disabled";

	public static final String AMOUNT_RANGE_FILTER_ENABLED = "amount-range-filter-enabled";

	public static final String LISTING_PAGE_SIZE = "listing-page-size";

	public static final String MAX_PAGE_SIZE_OF_RANGE = "max-page-size-of-range";

	public static final String MAX_PAGE_SIZE_FUNCTIONALITY_ENABLE = "max-page-size-functionality-enable";

	public static final String AWS_ES_FROM_DATE = "aws-es-from-date";

	public static final String SECOND_ES_FROM_DATE = "second-es-from-date";

	public static final String MAX_LENGTH_OF_AUTOCOMPLETE_QUERY = "max-length-of-autoComplete-query";

	public static final String USE_BANK_OAUTH = "use-bank-oauth";

	public static final String USE_TOKEN_INFO_API = "use-tokenInfo-api";

	public static final String PAGE_SIZE_AUTO_COMPLETE = "page-size-auto-complete";

	public static final String SHOW_BENEF_ACC_NUMBER = "show-benef-acc-number";

	public static final String NEED_TO_CONVERT_RECEIVED_QUERY = "need-to-convert-received-Query";

	public static final String AUTOCOMPELTE_NEW_QUERY_WHITELISTING_PERCENTAGE = "autocomp-new-query-whitelisting-percentage";

	public static final String FROM_SEARCH_DATE = "FROM_SEARCH_DATE";

	public static final String DEFAULT_FROM_SEARCH_DATE_MSG = "It seems you have not transacted recently.";

	public static final String DEFAULT_SEARCH_NO_DATA_MSG = "No results for the search. Try searching with a different word.";

	public static final String CLOSING_BALANCE = "Closing Balance: ₹";

	public static final String AUTO_COMPLETE_SEARCH_CONTEXT_IDENTIFIER = "autoComplete";

	public static final String CST_SEARCH_CONTEXT_IDENTIFIER = "cst";

	public static final String QUERY = "query";

	public static final String CAPITAL_QUERY = "QUERY";

	public static final String FROM_AUTO_COMPLETE = "fromAutoComplete";

	public static final String SEARCH_FUNCTIONALITY = "searchFunctionality";

	public static final String SEARCH_QUERY_AS_KEYWORD = "searchQueryAsKeyword";

	public static final String IS_FOR_SEARCH = "forSearch";

	public static final String IS_FOR_SEARCH_AND_FILTER = "forSearchAndFilter";

	public static final String SEARCH = "search-";

	public static final String SEARCH_FILTER = "search-filter-";

	public static final String ES_TXN_PURPOSE = "esTxnPurpose";

	public static final String SEARCH_WITHOUT_AUTOCOMPLETE = "search_without_autocomplete";

	public static final String SEARCH_WITH_AUTOCOMPLETE = "search_with_autocomplete";

	public static final String SEARCH_TYPE_FOR_METRICS = "searchTypeForMetrics";

	public static final String SEARCH_TYPE_FIELD = "searchType";

	public static final String SEARCH_TYPE_CONTACT_BOOK_IDENTIFIER = "contactBook";

	public static final String SEARCH_TYPE_AMOUNT_IDENTIFIER = "amount";

	public static final String ONLY_AMOUNT_SEARCH_IDENTIFIER = "amountOnly";

	public static final String CONTACT_BOOK_NUMBERS_FIELD = "contactBookNumbers";

	public static final String SEARCH_QUERY = "searchQuery";

	public static final String AUTO_COMP_SEARCH_CONTACTBOOK_NUMBERS = "autocompSearchContactBookNumbers";

	public static final String SEARCH_CONTACTBOOK_NUMBERS = "searchContactBookNumbers";

	public static final String SEARCH_CUSTOM_CONTEXT_MAP = "searchCustomContextMap";

	public static final String SEARCH_ADDITIONAL_METRIC_PARAMS = "searchAdditionalMetricParams";

	public static final String EXCEPTION_TAG = "expTag";

	public static final String CONTACTBOOK_SEARCH_WHITELISTED_USERS_CUSTID_IDENTIFIER = "contactbook-search-whitelisted-users-custId-Identifier";

	public static final String CONTACTBOOK_SEARCH_WHITELISTING_PERCENTAGE = "contactbook-search-whitelisting-percentage";

	public static final String MAX_NUM_OF_CONTACTBOOK_NUMBERS = "max-Num-Of-ContactBook-Numbers";

	public static final String SEARCHFIELDS_USERVIEW_MAPPING = "searchFields-userView-mapping";

	public static final String SEARCH_AMOUNT = "searchAmount";

	public static final String SEARCH_SELF_TRANSFER = "searchSelfTransfer";

	public static final String SEARCH_TXN_CATEGORY_MAP = "searchTxnCategoryMap";

	public static final String SEARCH_QUERY_FIELDS = "searchQueryFields";

	public static final String SEARCH_MONTH_DATE_RANGE = "searchMonthDateRange";

	public static final String SEARCH_TYPE_MONTH_NAME = "month";

	public static final List<String> POST_REQUESTS = Arrays.asList("cst", "search", "updates", "listing", "filter");

	// cron constant
	public static final String CONFIG_READER_CRON = "configReaderCron";

	public static final String RECON_CONFIG_PREFIX = "uth.recon.config";

	public static final String DOT = ".";

	// Spend Analytics Constant
	public static final String SPEND_FILTER_TYPE = "spendFilterType";

	public static final String SPEND_FILTER_TYPE_VALUE = "spendFilterTypeValue";

	public static final String ANALYTICS_FILTER = "analyticsFilter";

	public static final String SPEND_UTH_CATEGORY = "spendUthCategory";

	public static final String SPEND_STORE_ID = "spendStoreId";

	// service
	public static final String OAUTH = "oauth";

	public static final String ES_UPI_IDENTIFIER = "esUpiIdentifier";

	public static final String UPI_IDENTIFIER = "upiIdentifier";

	public static final String PPBL_IDENTIFIER = "ppblIdentifier";

	public static final String ES_PPBL_IDENTIFIER = "esPpblIdentifier";

	public static final String ES_PAYMENT_SYSTEM = "esPaymentSystem";

	public static final String SEARCH_WALLET_TYPE = "searchWalletType";

	public static final String ES_TXN_TYPE = "esTxnType";

	public static final String GRP_ID_SEPARATOR = ":GrpId:";

	public static final String TXN_DATE_SEPARATOR = ":tDate:";

	public static final String STREAM_SOURCE_SEPARATOR = ":source:";

	public static final String ACCT_TYPE_PARAM = "acctType";

	public static final String ES_ACCT_TYPE_PARAM = "esAcctType";

	public static final String WALLET_TYPE_FILTER = "walletTypeFilter";

	public static final String WALLET_ALL_TXNS_FILTER = "ALL";

	public static final String COMBINED_WALLET_PASSBOOK = "combinedWalletPaasbook";

	public static final String WALLET_TXN_TYPE_FILTER = "walletTxnTypeFilter";

	public static final int FUZZY_SEARCH_LOWER_LIMIT = 5;

	public static final Set<String> VALID_ACCT_TYPE = new HashSet<>(Arrays.asList("ICA", "ISA"));

	// StoreCash contants
	public static final String PAYTM_STORECASH = "Store Voucher";

	public static final String STORECASH_LOGO = "STORECASH_LOGO";

	public static final String VIEW_STORECASH_BALANCE_CTA = "View Store Voucher Balance";

	public static final String STORECASH_CTA_ENABLED = "storecash-cta-enabled";

	public static final String LOCALIZATION_KEY_VIEW_STORECASH_BALANCE_CTA_LABEL = "ctaLabelViewStoreCashBalance";

	public static final String LOCALIZATION_KEY_VIEW_HISTORY_CTA_LABEL = "ctaLabelViewHistory";

	public static final String LOCALIZATION_KEY_STORECASH_DISPLAY_NAME = "storeCashDisplayName";

	public static final String AUTO_COMPLETE_PASSBOOK_TYPE_TIME = "AUTO_COMPLETE_PASSBOOK_TYPE_TIME";

	public static final String PAYTM_TPAP_HANDLES_LIST = "paytm.tpap.handles.list";

	public static final Map<String, String> SearchContextToESMappings;

	public static final List<String> REPORT_CODES_FOR_REGROUPING = Arrays.asList(// UPI
																					// Report
																					// Codes
			"20501", "20502", "20503", "20504", "20505", "20506", "20507", "20508", "20509", "20510", "20512", "20513",
			"20261", "20265", "20275", "20276", "20277", "20263", "20264", "20250", "20260", "20205", "60225", "60226",
			// DC report codes
			"20210", "20234", "20214", "20253", "20256", "20252", "20254", "20255", "20251", "20256", "70289", "70288",
			"70203", "70204", "70205", "70206", "70217", "70218", "70220", "70215", "70216", "70236", "70219", "70266",
			"70290", "70287", "21210", "21234", "21214", "21253", "21256", "21252", "21254", "21255", "21251", "21266",
			"71288", "71203", "71204", "71205", "71206", "71217", "71218", "71220", "71215", "71216", "71236", "71219",
			"71266", "71287", "23234", "23214", "23253", "23256", "23252", "23254", "23255", "23251", "23266", "73288",
			"73203", "73204", "73205", "73206", "73217", "73218", "73220", "73215", "73216", "73236", "73219", "73266",
			"73287", "20212",
			// UPI UDIR ReportCodes
			"20525", "20526");

	public static final Map<TransactionTypeEnum, Boolean> IS_SEC_INST_DEBIT_INSTRUMENT;

	// List to Identify which PPBL Transactions to handle for Common Logo.
	public static final List<TransactionTypeEnum> PPBL_TXN_TYPES_FOR_COMMON_LOGOS = Arrays.asList(
			TransactionTypeEnum.CASHBACK_RECEIVED, TransactionTypeEnum.P2M_INTERNATIONAL,
			TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL);

	public static final String BLANK_STRING = "";

	public static final String RECEIVED_IN = "RECEIVED IN";

	public static final String FOR_OUTWARD_REMITTANCE_TO = "For outward remittance to";

	public static final String PAID_FOR_OUTWARD_REMITTANCE = "Paid for Outward Remittance";

	public static final String INTERNATIONAL_REMITTANCE_OUTWARD = "INTERNATIONAL_REMITTANCE_OUTWARD";

	public static final String FOREIGN_REMITTANCE_RECEIVED = "Foreign Remittance Received";

	public static final String TRANSFERRED_TO = "Transferred to";

	public static final String MONEY_TRANSFERRED = "Money Transferred";

	public static final String VIEW_MORE_DETAILS = "View more details";

	public static final String ORDER_ID_CAMEL_CASE = "orderId";

	public static final String TRANSFER_REF_NO = "transferRefNo";

	public static final String CUSTOMER = "CUSTOMER";

	public static final String OMS_NARRATION = "omsNarration";

	public static final String GV_RECEIVER_ID = "gvReceiverId";

	public static final String GV_RECEIVER_PHONE_NO = "gvReceiverPhoneNo";

	public static final String GV_RECEIVER_NAME = "gvReceiverName";

	public static final String ES_HIGHLIGHT_TYPE_PLAIN = "plain";

	public static final String SEARCHABLE_FIELDS = "searchFields.searchOtherName,searchFields.searchOtherMobileNo,"
			+ "searchFields.searchSelfBankName,searchFields.searchSelfAccountNo,"
			+ "searchFields.searchOtherVpa,searchFields.searchOtherBankName,searchFields.searchOtherAccountNo,"
			+ "searchFields.searchWalletType,searchFields.searchUthMerchantCategory,"
			+ "searchFields.searchVoucherName,searchFields.searchPaymentSystem,"
			+ "searchFields.searchTxnIndication,searchFields.searchTxnStatus,searchFields.searchTxnCategory,"
			+ "searchFields.searchTags";

	public static final String RANGE = "range";

	public static final String RANGE_IN_MONTH = "rangeInMonth";

	public static final String TERM = "term";

	public static final String TERMS = "terms";

	public static final String NESTED = "nested";

	public static final String REGEXP_NESTED = "regexpNested";

	public static final String MULTI_VALUED_SEARCH_NESTED = "multiValuedSearchNested";

	public static final String MULTI_VALUED_SEARCH = "multiValuedSearch";

	public static final String MULTI_MATCH_SEARCH = "multi_match_search";

	public static final String MULTI_MATCH_AUTOCOMPLETE = "multi_match_autoComplete";

	public static final String MULTI_MATCH_AUTOCOMPLETE_EXACT_MATCH_BOOST = "multi_match_autoComplete_exact_match_boost";

	public static final String MULTI_MATCH_OVER_KEYWORD = "multi_match_over_keyword";

	public static final String FILTER = "filter";

	public static final String MUST = "must";

	public static final String POSTPAID_PAYMENT_MODE_LOWER_CASE = "paytm_digital_credit";

	public static final String POSTPAID_REF_NO = "Postpaid Ref No:";

	public static final String POSTPAID_TXN_ID_KEY = "postpaid_txn_id";

	public static final String MULTI_MONTH_SEARCH = "multiMonthSearch";

	public static final String INCLUDE_ONLY_ALPHAS_AND_NUMS_REGEX = "[^a-zA-Z0-9]";

	public static final String OTHER_PARTY_ENTITY_ID = "otherPartyEntityId";

	public static final String CHAT_CTA_LABEL = "View History";

	public static final String CHAT_AND_PAY = "Chat & Pay";

	public static final String CUST_PHONE = "custPhone";

	public static final String CUST_NAME = "custName";

	public static final String CUST_ID = "custId";

	public static final String USER_TYPE = "userType";

	public static final String IS_MERGED_DOCUMENT = "isMergedDocument";

	public static final String SPLIT_NAME = "splitName";

	public static final String SPLIT_DESC = "splitDesc";

	public static final String LONGITUDE = "longitude";

	public static final String LATITUDE = "latitude";

	public static final String TIMEZONE = "timezone";

	public static final Double DEFAULT_LATITUDE_VALUE = 0.0;

	public static final Double DEFAULT_LONGITUDE_VALUE = 0.0;

	public static final String BC_AGENT_LOGO = "BC_AGENT_LOGO";

	public static final String MOBILE_NUMBER = "mobileNo";

	public static final String BANK_NAME_PARAM = "bankName";

	public static final String ACC_REF_ID = "accRefId";

	public static final String MASKED_ACC_NO = "maskedAccNo";

	public static final String BANK_LOGO_URL = "bankLogoUrl";

	public static final String LISTING_REQUEST_SERVED_FROM_CACHE = "listingRequestServedFromCache";

	public static final String LISTING_REQUEST_APPLICABLE_TO_BE_SERVED_USING_CACHE = "listingRequestApplicableToBeServedUsingCache";

	public static final String LISTING_REQUEST_APPLICABLE_TO_CHECK_NO_TXN_FOUND = "listingRequestApplicableToCheckNoTxnFound";

	public static final String NO_TXN_PRESENT_IDENTIFIED_USING_CACHE = "noTxnPresentIdentifiedUsingCache";

	public static final String WHY_IS_THIS_MONEY_ON_HOLD = "Why is this money on hold?";

	public static final String LOYALTY_POINT = "loyalty_point";

	public static final String UMN = "umn";

	public static final String CUSTOMER_ID = "customerId";

	public static final String GET_ANALYTICS_API = "getAnalytics";

	public static final String AUTOCOMPLETE_SUGGESTION_DISPLAY_STRING = "%s in ";

	public static final String SEARCH_FIELDS = "searchFields";

	public static final String IS_UNAUTH_ERROR_REQ = "isUnAuthErrorReq";

	public static final String SEND_DISPLAY_DATA_PARAMETER = "sendDisplayData";

	public static final String RECENT_PAYMENTS = "recentPayment";

	public static final String QR_SCAN = "qrScan";

	public static final String AUTOMATIC_PAYMENTS = "automaticPayment";

	public static final String LINKED_WALLET = "linkedWallet";

	public static final String ONLINE_PAYMENT = "onlinePayment";

	public static final String DEEMED_TXN_MESSAGE_KEY = "deemed-txn-msg";

	public static final String REPEAT_PAYMENT_FILTER_ENABLED = "repeat-payment-filter-enabled";

	public static final String ROLLOUT_CONFIG = "rolloutConfig";

	public static final String FILTER_FOR_ALL_UPI_ACCOUNTS = "ALL_UPI";

	public static final String INWARD_TXN_REPEAT_PAYMENT_CTA_TEXT = "Pay";

	public static final String OUTWARD_TXN_REPEAT_PAYMENT_CTA_TEXT = "Pay Again";

	public static final String AUTOCOMPLETE_PASSBOOK_TYPE_HIT_COUNT = "AUTOCOMPLETE_PASSBOOK_TYPE_HIT_COUNT";

	public static final String NORMAL = "NORMAL";

	public static final String IS_DQR = "isDQR";

	public static final String PAY = "PAY";

	// Identifier for Paytm QR: channelCode = PAYTM_QR_MERCHANTS
	public static final String PAYTM_QR_MERCHANTS = "PAYTM_QR_MERCHANTS";

	public static final String VERTICAL_NAME_TO_IDS_MAPPING = "vertical.name.to.ids.mapping";

	public static class ConfigurationPropertiesValidatorConstants {

		public static final int FROM_DATE_MIN_DIFF = 10;

	}

	public static class LogoPurpose {

		public static final String OAUTH_IMAGE = "OauthImage";

		public static final String KYC_INITIALS = "KYC_INITIALS";

		public static final String KYC_IMAGE = "KYC_IMAGE";

	}

	public static class NamePurpose {

		public static final String KYC = "KYC";

	}

	public static class RoutingConstants {

		public static final String UTH_V1_LISTING_URL = "UTH_V1_LISTING_URL";

		public static final String UTH_V1_DETAIL_URL = "UTH_V1_DETAIL_URL";

		public static final String UTH_V2_LISTING_URL = "UTH_V2_LISTING_URL";

		public static final String UTH_V2_DETAIL_URL = "UTH_V2_DETAIL_URL";

		public static final String UTH_BG_APP_SYNC_LISTING_URL = "UTH_BG_APP_SYNC_LISTING_URL";

		public static final String CURRENT_DATE_IDENTIFIER = "-1";

		public static final String PTH_LISTING_URL_V1 = "PTH_LISTING_URL_V1";

		public static final String PTH_LISTING_FILTER_URL_V1 = "PTH_LISTING_FILTER_URL_V1";

		public static final String PTH_LISTING_UPI_PASSBOOK_URL_V1 = "PTH_LISTING_UPI_PASSBOOK_URL_V1";

		public static final String PTH_LISTING_UPI_LITE_PASSBOOK_URL_V1 = "PTH_LISTING_UPI_LITE_PASSBOOK_URL_V1";

		public static final String PTH_LISTING_UPI_CC_PASSBOOK_URL_V1 = "PTH_LISTING_UPI_CC_PASSBOOK_URL_V1";

		public static final String PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V1 = "PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V1";

		public static final String PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V1 = "PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V1";

		public static final String PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V1 = "PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V1";

		public static final String PTH_LISTING_URL_V2 = "PTH_LISTING_URL_V2";

		public static final String PTH_LISTING_FILTER_URL_V2 = "PTH_LISTING_FILTER_URL_V2";

		public static final String PTH_LISTING_UPI_PASSBOOK_URL_V2 = "PTH_LISTING_UPI_PASSBOOK_URL_V2";

		public static final String PTH_LISTING_UPI_LITE_PASSBOOK_URL_V2 = "PTH_LISTING_UPI_LITE_PASSBOOK_URL_V2";

		public static final String PTH_LISTING_UPI_CC_PASSBOOK_URL_V2 = "PTH_LISTING_UPI_CC_PASSBOOK_URL_V2";

		public static final String PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V2 = "PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V2";

		public static final String PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V2 = "PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V2";

		public static final String PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V2 = "PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V2";

	}

	public static class PoweredByLogoConstant {

		public static final String MANDATE = "mandate_";

		public static final String CC = "cc_";

		public static final String ULTRA = "ultra_";

		// default Logo
		public static final String DEFAULT_POWERED_BY_UPI = "powered_by_UPI";

	}

	public static Map<String, List<String>> mandatoryParamMapForCtaChatProfile;

	static {
		final Map<String, List<String>> map = new HashMap<>();
		final List<String> customerMandatoryParamList = new ArrayList<>();
		final List<String> vpaMandatoryParamList = new ArrayList<>();
		final List<String> bankMandatoryParamList = new ArrayList<>();
		final List<String> merchantMandatoryParamList = new ArrayList<>();

		// Mandatory param in case of customer
		customerMandatoryParamList.add(WebConstants.CHAT_IDENTIFIER);
		customerMandatoryParamList.add(WebConstants.CHAT_PROFILE_TYPE);

		// Mandatory param in case of vpa/vpam
		vpaMandatoryParamList.add(WebConstants.CHAT_IDENTIFIER);
		vpaMandatoryParamList.add(WebConstants.VPA);
		vpaMandatoryParamList.add(WebConstants.TXN_CATEGORY);
		vpaMandatoryParamList.add(WebConstants.CHAT_PROFILE_TYPE);

		// Mandatory param in case of bank
		bankMandatoryParamList.add(WebConstants.CHAT_IDENTIFIER);
		bankMandatoryParamList.add(WebConstants.BANK_NAME_PARAM);
		bankMandatoryParamList.add(WebConstants.ACC_REF_ID);
		bankMandatoryParamList.add(WebConstants.MASKED_ACC_NO);
		bankMandatoryParamList.add(WebConstants.CHAT_PROFILE_TYPE);

		// Mandatory param in case of merchant
		merchantMandatoryParamList.add(WebConstants.CHAT_IDENTIFIER);
		merchantMandatoryParamList.add(WebConstants.NAME);
		merchantMandatoryParamList.add(WebConstants.CHAT_PROFILE_TYPE);

		// map with key as type and value as list of mandatory param
		map.put(WebConstants.CUSTOMER_TYPE, customerMandatoryParamList);
		map.put(WebConstants.VPA_TYPE, vpaMandatoryParamList);
		map.put(WebConstants.VPAM_TYPE, vpaMandatoryParamList);
		map.put(WebConstants.BANK_TYPE, bankMandatoryParamList);
		map.put(WebConstants.MERCHANT_TYPE, merchantMandatoryParamList);

		mandatoryParamMapForCtaChatProfile = Collections.unmodifiableMap(map);
	}

	static {
		Map<String, String> map = new HashMap<>();
		map.put("merchantCategory", "participants.merchantData.merchantCategory");
		map.put("streamSource", "streamSource");
		map.put(TXN_INDICATOR, "txnIndicator");
		map.put("txnCategory", "txnType");
		map.put(ES_TAG, "searchFields.searchTags.keyword");
		map.put("entityId", "entityId");
		map.put(ES_SECOND_PARTY_ID, "participants.entityId");
		map.put(OTHER_PARTY_ENTITY_ID, "otherPartyEntityId");
		map.put("secondPartyType", "participants.entityType");
		map.put("walletType", "participants.walletData.walletType");
		map.put("upiVpa", "participants.upiData.vpa");
		map.put("cardNumber", "participants.cardData.cardNum");
		map.put("cardType", "participants.cardData.cardType");
		map.put("isVisible", "isVisible");
		map.put("showInListing", "showInListing");
		map.put(ES_TXN_TYPE, "txnType");
		map.put("esDocTxnIndicator", "txnIndicator");
		map.put("esDocStatus", "status");

		// Spend Analytics Filter Mapping
		map.put("spendUthCategory", "spendAnalyticsFields.txnCategory");
		map.put("spendStoreId", "spendAnalyticsFields.storeId");

		map.put("secondPartyName", "participants.name");
		map.put("showBankData", "isBankData");
		map.put("name", "participants.name");
		map.put("benefName", "participants.contextMap.benefName");
		map.put("remitterName", "participants.contextMap.remitterName");
		map.put(UTH_CATEGORY, "participants.merchantData.uthCategory");
		map.put(ES_PAYMENT_SYSTEM, "searchFields.searchPaymentSystem.keyword");
		map.put(ES_ACCT_TYPE_PARAM, "searchFields.searchSelfAccTyp");
		map.put(ES_UPI_IDENTIFIER, "searchFields.bankAcctId");
		map.put(SEARCH_WALLET_TYPE, "searchFields.searchWalletType");
		map.put("ifsc", "participants.bankData.ifsc.keyword");
		map.put("txnDateRange", "txnDate");
		map.put(ES_TXN_PURPOSE, "contextMap.txn_purpose");
		map.put("searchQuery",
				"searchFields.searchRemarks,searchFields.searchOtherName,searchFields.searchOtherMobileNo,"
						+ "searchFields.searchOtherVpa,"
						+ "searchFields.searchWalletType,searchFields.searchUthMerchantCategory,"
						+ "searchFields.searchVoucherName," + "searchFields.searchTags");
		map.put("autoCompleteQuery",
				"searchFields.searchOtherName,searchFields.searchOtherMobileNo," + "searchFields.searchOtherVpa,"
						+ "searchFields.searchWalletType,searchFields.searchUthMerchantCategory,"
						+ "searchFields.searchVoucherName," + "searchFields.searchTags");
		map.put("searchQueryAsKeyword", "searchFields.search*.keyword");
		map.put("searchContactBookNumbers", "searchFields.searchOtherMobileNo.keyword");
		map.put("autocompSearchContactBookNumbers", "searchFields.searchOtherMobileNo.keyword");
		map.put("searchAmount", "amount");
		map.put("reportCode", "contextFilterMap.reportCode");
		map.put("fromUpdatedDate", "docUpdatedDate");
		map.put(SEARCH_SELF_TRANSFER, "searchFields.searchKeywords.keyword");
		map.put("updatedDateRangeValue", "docUpdatedDate");
		map.put("amountRange", "amount");
		map.put("verticalId", "searchFields.verticalIds");
		map.put("walletTxnType", "contextFilterMap.walletTxnType");
		map.put("esDocViewStatus", "status");
		map.put("txnId", "txnId");
		map.put("showHiddenTxn", "isHiddenTxn");
		map.put("amount", "amount");

		/**
		 * will add below mapping in future if required as of now commenting below mapping
		 * since this is not required map.put("txnId","txnId");
		 * map.put("rrn","participants.bankData.rrn");
		 * map.put("parentTxnId","parentTxnId");
		 * map.put("mobileNumber","participants.mobileData.mobileNumber");
		 * map.put("sourceTxnId","sourceTxnId");
		 * map.put("channelCode","searchFields.searchChannelCode");
		 */

		SearchContextToESMappings = Collections.unmodifiableMap(map);

		// Map to identify that secondInstrument is debit instrument or not in detailApi
		// response for particular TxnType
		IS_SEC_INST_DEBIT_INSTRUMENT = new HashMap<>();
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.CASHBACK_RECEIVED, false);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.PPBL_TRANSACTION, true);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.P2M, true);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.P2M_REFUND, false);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.P2M_INTERNATIONAL, true);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL, false);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.ADD_AND_PAY, true);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.IPO_MANDATE, true);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.RECURRING_MANDATE, true);
		IS_SEC_INST_DEBIT_INSTRUMENT.put(TransactionTypeEnum.WALLET_UPI_DEBIT_P2M, true);
	}

	public static class UpiInternationalConstant {

		public static final String FOREIGN_CURRENCY = "foreignCurrency";

		public static final String MARK_UP_FEE = "markupFee";

		public static final String FOREIGN_AMOUNT = "foreignAmount";

		public static final String CONVERSION_RATE = "conversionRate";

		public static final String RUPEE_SYMBOL = "₹";

	}

	public static class UpiErrorCodes {

		public static List<String> ERROR_CODE_FOR_WRONG_UPI_PIN = Arrays.asList("U30-ZM", "U30-Z6");

		public static final String ERROR_CODE = "errorCode";

		public static final String RESULT_CODE_ID = "resultCodeId";

		public static final String NPCI_RESP_CODE = "npciRespCode";

	}

	public static final String UPI_LITE_FILTER_EXTRA_HANDLING_ENABLED_PROPERTY_KEY = "upi-lite-filter-extra-handling-enabled";

	public static final String CC_FILTER_4034_ERROR_MESSAGE_DYNAMIC_VALUE = " for the selected Rupay Credit Card";

	public static final String FILTERS_4034_ERROR_MESSAGE_DYNAMIC_VALUE = "matching the applied filters";

	public static final String SET_LOGO_URL = "setLogoUrl";

	public static final String SET_DISPLAY_TEXT = "setDisplayText";

	public static final String VIRTUAL_FROM_DATE_VALUE_IN_EPOCH_PARAM_MAP_KEY_WHEN_VFD_IS_USED = "virtualFromDate";

	public static final String TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY = "toDateForSubsequentRequest";

	public static final String EPOCH_BEGINNING_OF_MONTH_TILL_WHICH_MAX_SCAN_TO_BE_DONE_PARAM_MAP_KEY = "epochOfBeginningOfMonthTillWhichMaxScanIsToBeDone";

	public static final String THREAD_CONTEXT_KEY_CONTAINING_EPOCH_TILL_WHICH_SCAN_HAS_BEEN_DONE = "scannedTill";

	public static final String PARAM_MAP_KEY_CONTAINING_FLAG_THAT_COMPLETE_DATA_SCAN_IS_DONE = "scannedAllIndexes";

}
