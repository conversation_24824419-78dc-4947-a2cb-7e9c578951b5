package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.CommonConstants.DEFAULT_PAGE_SIZE;
import static com.org.panaroma.commons.constants.CommonConstants.INPUT_LCHG_DATE_FORMAT;
import static com.org.panaroma.commons.constants.CommonConstants.VALID_LCHG_DATE_FORMAT;

import com.org.panaroma.commons.dto.AccountTypeEnum;
import com.org.panaroma.commons.dto.BackFillingQueryObject;
import com.org.panaroma.commons.dto.BackFillingRequestObject;
import com.org.panaroma.commons.dto.BankData;
import com.org.panaroma.commons.dto.CardData;
import com.org.panaroma.commons.dto.CardType;
import com.org.panaroma.commons.dto.CategoryTags;
import com.org.panaroma.commons.dto.ChatBackFillingKafkaObject;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.MerchantData;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.MobileData;
import com.org.panaroma.commons.dto.OtherData;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.PostStitchData;
import com.org.panaroma.commons.dto.RepeatPaymentData;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.StitchDataPayload;
import com.org.panaroma.commons.dto.TagType;
import com.org.panaroma.commons.dto.Tags;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.commons.dto.UPIData;
import com.org.panaroma.commons.dto.UpiChannelTypeEnum;
import com.org.panaroma.commons.dto.WalletData;
import com.org.panaroma.commons.dto.WalletType;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.kafkaPushAPI.TransactionHistoryDto;
import com.org.panaroma.commons.dto.kafkaPushAPI.TransactionSourceDto;
import com.org.panaroma.commons.dto.kafkaPushAPI.TxnParticipantDto;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

@Log4j2
public class ConversionUtility {

	private static final DateTimeFormatter INPUT_LCHG_FORMAT = new DateTimeFormatterBuilder()
		.appendPattern(INPUT_LCHG_DATE_FORMAT)
		.toFormatter();

	private static final DateTimeFormatter VALID_LCHG_FORMAT = new DateTimeFormatterBuilder()
		.appendPattern(VALID_LCHG_DATE_FORMAT)
		.toFormatter();

	public static final String CHAT_BACK_FILLING = "chat-back-filling";

	public static TransactionHistoryDetails convertToTransactionHistoryDetailObject(
			final TransactionHistoryDto txnHistory) {
		TransactionHistoryDetails details = new TransactionHistoryDetails();
		details.setAmount(txnHistory.getAmount());
		details.setContextMap(getStringifiedMap(txnHistory.getContextMap()));
		details.setCurrency(Currency.getCurrencyByValue(txnHistory.getCurrency()));
		details.setLocation(txnHistory.getLocation());
		details.setParentTxnId(txnHistory.getParentTxnId());
		details.setRemarks(txnHistory.getRemarks());
		details.setSourceSystemId(txnHistory.getSourceSystemId());
		details.setStatus(ClientStatusEnum.getStatusEnumByValue(txnHistory.getStatus()));
		details.setUmn(txnHistory.getUmn());
		details.setStreamSource(
				TransactionSource.getTransactionSourceEnumByName(txnHistory.getStreamSource().getTransactionSource()));
		details.setSystemId(txnHistory.getSystemId());
		details.setTxnDate(txnHistory.getTxnDate());
		details.setUpdatedDate(txnHistory.getUpdatedDate());
		details.setTxnType(TransactionTypeEnum.getTransactionTypeEnumByName(txnHistory.getTxnType()));
		details.setNotesKey(txnHistory.getNotesKey());
		details.setAmountBreakup(txnHistory.getAmountBreakup());
		details.setChatData(txnHistory.getChatData());
		details.setParticipantsNotAvailable(isParticipantNotAvailable(txnHistory));
		details.setFwdTxnDetails(txnHistory.getFwdTxnDetails());

		if (Objects.nonNull(txnHistory.getRepeatPaymentData())) {
			RepeatPaymentData repeatPaymentData = new RepeatPaymentData();
			repeatPaymentData.setIsRepeatable(txnHistory.getRepeatPaymentData().getIsRepeatable());
			repeatPaymentData.setUrl(txnHistory.getRepeatPaymentData().getUrl());
			details.setRepeatPaymentData(repeatPaymentData);
		}
		// If source txn ID is present set source system to PG.
		// TODO:Vikas remove it after changes at client end are done.
		// if (txnHistory.getSourceSystem() == null && txnHistory.getSourceSystemId() !=
		// null) {
		// txnHistory.setSourceSystem(TransactionSourceDTO.PG);
		// }
		details.setSourceSystem(TransactionSource.getTransactionSourceEnumByName(
				txnHistory.getSourceSystem() == null ? null : txnHistory.getSourceSystem().getTransactionSource()));

		// setting tags
		if (txnHistory.getTags() != null && !txnHistory.getTags().isEmpty()) {
			List<Tags> tags = new ArrayList<>();
			txnHistory.getTags().forEach(tagsDTO -> {
				if (tagsDTO.getTag() == null || tagsDTO.getType() == null) {
					log.info("Invalid tag {} received. Both values tag and tagType should be present. Removing tag ",
							tagsDTO);
					return;
				}
				Tags tag = new Tags();
				tag.setTag(tagsDTO.getTag());
				tag.setType(TagType.getTagTypeByValue(tagsDTO.getType().getTagTypeValue()));
				tags.add(tag);
			});
			details.setTags(tags);
		}

		// if Participant is available the set participants
		if (Boolean.FALSE.equals(isParticipantNotAvailable(txnHistory))) {
			// setting participants
			List<TxnParticipants> txnParticipantsList = new ArrayList<>();
			txnHistory.getParticipants().forEach(txnParticipantObject -> {
				TxnParticipants txnParticipant = new TxnParticipants();
				txnParticipant.setAmount(txnParticipantObject.getAmount());
				txnParticipant.setContextMap(getStringifiedMap(txnParticipantObject.getContextMap()));
				txnParticipant.setCurrency(Currency.getCurrencyByValue(txnParticipantObject.getCurrency()));
				txnParticipant.setCustomerId(txnParticipantObject.getCustomerId());
				txnParticipant.setLogoUrl(txnParticipantObject.getLogoUrl());
				txnParticipant.setName(txnParticipantObject.getName());
				txnParticipant.setPaymentSystem(
						PaymentSystemEnum.getPaymentSystemEnumByKey(txnParticipantObject.getPaymentSystem() == null
								? null : txnParticipantObject.getPaymentSystem().getPaymentSystemKey()));
				txnParticipant.setPaymentTxnId(txnParticipantObject.getPaymentTxnId());
				txnParticipant.setRemarks(txnParticipantObject.getRemarks());
				txnParticipant.setStatus(ClientStatusEnum.getStatusEnumByValue(txnParticipantObject.getStatus()));
				txnParticipant.setTxnDate(txnParticipantObject.getTxnDate());
				txnParticipant.setUpdatedDate(txnParticipantObject.getUpdatedDate());
				txnParticipant.setTxnIndicator(TransactionIndicator
					.getTransactionIndicatorEnumByKey(txnParticipantObject.getTxnIndicator() == null ? null
							: txnParticipantObject.getTxnIndicator().getTransactionIndicatorKey()));
				// set tags
				if (txnParticipantObject.getTags() != null && !txnParticipantObject.getTags().isEmpty()) {
					List<Tags> tagList = new ArrayList<>();
					txnParticipantObject.getTags().forEach(tagsDTO -> {
						if (tagsDTO.getTag() == null || tagsDTO.getType() == null) {
							log.info(
									"Invalid tag {} received. Both values tag and tagType should be present. Removing tag ",
									tagsDTO);
							return;
						}
						Tags tag = new Tags();
						tag.setTag(tagsDTO.getTag());
						tag.setType(TagType.getTagTypeByValue(tagsDTO.getType().getTagTypeValue()));
						tagList.add(tag);
					});
					txnParticipant.setTags(tagList);
				}
				// setting bank data
				if (txnParticipantObject.getBankData() != null) {
					BankData bankData = new BankData();
					bankData.setAccNum(txnParticipantObject.getBankData().getAccNum());
					bankData.setUpiAccRefId(txnParticipantObject.getBankData().getUpiAccRefId());
					bankData.setAccRefNum(txnParticipantObject.getBankData().getAccRefNum());
					bankData.setBankName(txnParticipantObject.getBankData().getBankName());
					bankData.setIfsc(txnParticipantObject.getBankData().getIfsc());
					bankData.setAccType(
							AccountTypeEnum.getAccountTypeEnumByValue(txnParticipantObject.getBankData().getAccType()));
					txnParticipant.setBankData(bankData);
				}
				// setting wallet data
				if (txnParticipantObject.getWalletData() != null) {
					WalletData walletData = new WalletData();
					walletData.setWalletIssuer(txnParticipantObject.getWalletData().getWalletIssuer());
					walletData.setWalletUserId(txnParticipantObject.getWalletData().getWalletUserId());
					walletData.setWalletMobileNumber(txnParticipantObject.getWalletData().getWalletMobileNumber());
					walletData.setWalletType(
							WalletType.getWalletTypeByValue(txnParticipantObject.getWalletData().getWalletType()));
					walletData.setClosingBalance(txnParticipantObject.getWalletData().getClosingBalance());
					txnParticipant.setWalletData(walletData);
				}
				// setting merchant Data
				if (txnParticipantObject.getMerchantData() != null) {
					MerchantData merchantData = new MerchantData();
					merchantData.setMccCode(txnParticipantObject.getMerchantData().getMccCode());
					merchantData.setMerchantId(txnParticipantObject.getMerchantData().getMerchantId());
					merchantData.setMerchantPayMode(txnParticipantObject.getMerchantData().getMerchantPayMode());
					merchantData.setMerchantType(MerchantTypeEnum
						.getMerchantTypeByValue(txnParticipantObject.getMerchantData().getMerchantType()));
					// In case of stream source as wallet,
					// merchant category is expected in the merchant data and will be set
					// accordingly
					if (TransactionSourceDto.WALLET.equals(txnHistory.getStreamSource())) {
						merchantData.setMerchantCategory(CategoryTags
							.getCategoryTagEnumByValue(txnParticipantObject.getMerchantData().getMerchantCategory()));
					}
					merchantData
						.setMerchantSubCategory(txnParticipantObject.getMerchantData().getMerchantSubCategory());
					txnParticipant.setMerchantData(merchantData);
				}
				// setting upi data
				if (txnParticipantObject.getUpiData() != null) {
					UPIData upiData = new UPIData();
					upiData.setVpa(txnParticipantObject.getUpiData().getVpa());
					upiData.setChannel(UpiChannelTypeEnum
						.getUpiChannelTypeEnumByValue(txnParticipantObject.getUpiData().getChannel()));
					upiData.setChannelId(txnParticipantObject.getUpiData().getChannelId());
					upiData.setChannelMode(txnParticipantObject.getUpiData().getChannelMode());
					upiData.setUpiCcInfo(txnParticipantObject.getUpiData().getUpiCcInfo());
					upiData.setPmtInstMetaInfo(txnParticipantObject.getUpiData().getPmtInstMetaInfo());
					txnParticipant.setUpiData(upiData);
				}
				// setting card data
				if (txnParticipantObject.getCardData() != null) {
					setCardData(txnParticipantObject, txnParticipant);
				}
				if (txnParticipantObject.getOtherData() != null) {
					OtherData otherData = new OtherData();
					BeanUtils.copyProperties(txnParticipantObject.getOtherData(), otherData);
					txnParticipant.setOtherData(otherData);
				}
				if (txnParticipantObject.getMobileData() != null) {
					MobileData mobileData = new MobileData();
					BeanUtils.copyProperties(txnParticipantObject.getMobileData(), mobileData);
					txnParticipant.setMobileData(mobileData);
				}
				// adding participant to list
				txnParticipantsList.add(txnParticipant);
			});
			details.setParticipants(txnParticipantsList);
		}

		return details;
	}

	private static boolean isParticipantNotAvailable(TransactionHistoryDto tdo) {
		if (CollectionUtils.isEmpty(tdo.getParticipants()) || tdo.isParticipantsNotAvailable()) {

			// Check if getting invalid flag from source system
			if (!tdo.isParticipantsNotAvailable()) {
				log.warn("Getting Invalid participant not available flag from source system for txnId: {}",
						tdo.getSystemId());
			}

			return true;
		}
		return false;
	}

	public static void setCardData(final TxnParticipantDto txnParticipantObject, final TxnParticipants txnParticipant) {
		if (txnParticipantObject != null && txnParticipantObject.getCardData() != null) {
			CardData cardData = new CardData();
			cardData.setBin(txnParticipantObject.getCardData().getBin());
			cardData.setCardNetwork(txnParticipantObject.getCardData().getCardNetwork());
			cardData.setCardNum(txnParticipantObject.getCardData().getCardNum());
			cardData.setCardType(CardType.getCardTypeByValue(txnParticipantObject.getCardData().getCardType()));
			txnParticipant.setCardData(cardData);
			cardData.setCreditAccRefNum(txnParticipantObject.getCardData().getCreditAccRefNum());
		}
	}

	public static Map<String, String> getStringifiedMap(final Map<String, Object> contextMap) {
		if (contextMap == null) {
			return null;
		}

		Map<String, String> map = new HashMap<>();
		contextMap.keySet().forEach(key -> {
			if (Objects.nonNull(contextMap.get(key))) {
				map.put(key, String.valueOf(contextMap.get(key)));
			}
		});
		return map;
	}

	public static PostStitchData convertBankDataToStichData(final String inputData, final String fileName) {
		try {
			String[] data = inputData.trim().split("\\t");
			for (int i = 0; i < data.length; i++) {
				if (data[i].equalsIgnoreCase("NULL")) {
					data[i] = null;
				}
			}
			log.info("Processing line number :{} for fileName : {}", data[0], fileName);
			StitchDataPayload stitchDataPayload = StitchDataPayload.builder()
				.transactionDate(data[1])
				.valueDate(data[2])
				.postedDate(data[3])
				.transactionId(data[4])
				.amountValue(data[5])
				.creditDebitFlag(data[6])
				.creditDebitSerialNumber(data[7])
				.reportCode(data[8])
				.transactionParticular(data[9])
				.solId(data[10])
				.internalAcctNumber(data[11])
				.custId(data[12])
				.dccType(data[13])
				.dccId(data[14])
				.dccSrcName(data[15])
				.dccTranId(data[16])
				.longitude(data[17])
				.latitude(data[18])
				.merchantId(data[19])
				.merchantName(data[20])
				.address1(data[21])
				.address2(data[22])
				.address3(data[23])
				.pinCode(data[24])
				.freeField1(data[25])
				.freeField2(data[26])
				.freeField3(data[27])
				.freeField4(data[28])
				.freeField5(data[29])
				.freeField6(data[30])
				.freeField7(data[31])
				.freeField8(data[32])
				.freeField9(data[33])
				.freeField10(data[34])
				.freeField11(data[35])
				.freeField12(data[36])
				.freeField13(data[37])
				.freeField14(data[38])
				.freeField15(data[39])
				.freeField16(data[40])
				.freeField17(data[41])
				.freeField18(data[42])
				.freeField19(data[43])
				.freeField20(data[44])
				.benefName(data[45])
				.benefAcctNum(data[46])
				.benefIfsc(data[47])
				.remitterName(data[48])
				.remitterAcctNum(data[49])
				.remitterIfsc(data[50])
				.rrn(data[51])
				.dcMerchantId(data[52])
				.dcMerchantTermId(data[53])
				.dcMerchantNmLoc(data[54])
				.impsMsgId(data[55])
				.impsTranRemarks(data[56])
				.benefMmid(data[57])
				.benefMobile(data[58])
				.remitterMmid(data[59])
				.remitterMobile(data[60])
				.mwTranId(data[61])
				.cacquirerId(data[62])
				.ccardOrCustId(data[63])
				.cacctNum(data[64])
				.cdeviceId(data[65])
				.lchgTime(data[66])
				.parentTranId(data[67])
				.userAccountName(data[68])
				.schemeCode(data[69])
				.externalCustId(data[70])
				.externalAcctNumber(data[71])
				.status(ClientStatusEnum.SUCCESS.getStatusValue())
				.build();

			// converting lchg time since input format is not expected.
			stitchDataPayload.setLchgTime(convertLchgTimeFormat(stitchDataPayload.getLchgTime(), fileName,
					stitchDataPayload.getTransactionId(), stitchDataPayload.getTransactionDate()));

			if (StringUtils.isBlank(stitchDataPayload.getLchgTime())) {
				log.info(
						"Setting lchgTime same as postedDate as lchgTime after conversion is null for tranID: {} "
								+ "tranDate: {} fileName: {} inputLchgTime: {}",
						stitchDataPayload.getTransactionId(), stitchDataPayload.getTransactionDate(), fileName,
						stitchDataPayload.getLchgTime());
				stitchDataPayload.setLchgTime(stitchDataPayload.getPostedDate());
			}

			return PostStitchData.builder().payload(stitchDataPayload).eventName(null).header(null).build();
		}
		catch (Exception e) {
			log.error("Exception while processing data from file : {} line : {} Exception :{}", fileName, inputData,
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public static String convertLchgTimeFormat(final String lchgTime, final String fileName, final String tranId,
			final String tranDate) {
		if (StringUtils.isBlank(lchgTime)) {
			return lchgTime;
		}
		try {

			LocalDateTime dateTime = parseDate(lchgTime);
			if (!Objects.isNull(dateTime)) {
				return dateTime.format(VALID_LCHG_FORMAT);
			}
			return null;
		}
		catch (Exception ex) {
			log.error(
					"some exception in converting lchg time into valid date format for tranID: {} tranDate: {} "
							+ "fileName: {} inputLchgTime: {} Exception: {}",
					tranId, tranDate, fileName, lchgTime, CommonsUtility.exceptionFormatter(ex));
		}

		return null;
	}

	public static LocalDateTime parseDate(final String date) {
		try {
			if (!ObjectUtils.isEmpty(INPUT_LCHG_FORMAT) && !ObjectUtils.isEmpty(date)) {
				LocalDateTime localDateTime = LocalDateTime.parse(date, INPUT_LCHG_FORMAT);
				return localDateTime;
			}
		}
		catch (Exception e) {
			log.error("Some exception occurred while parsing string date : {} to object date, Exception : {}", date,
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public static SearchContext getSearchContext(final Long fromDate, final Long toDate, final String reportCodes,
			final Integer pageNo, final Integer pageSize) {
		SearchContext searchContext = new SearchContext();
		searchContext.setFromDate(fromDate);
		searchContext.setToDate(toDate);
		if (pageNo != null) {
			searchContext.setPageNo(pageNo);
		}
		if (pageSize != null) {
			searchContext.setPageSize(pageSize);
		}
		else {
			searchContext.setPageSize(DEFAULT_PAGE_SIZE);
		}
		searchContext.setReportCode(reportCodes);
		return searchContext;
	}

	public static SearchContext getSearchContext(final BackFillingRequestObject requestObject) {
		try {
			SearchContext searchContext = new SearchContext();
			if (requestObject.getPageNo() != null) {
				searchContext.setPageNo(requestObject.getPageNo());
			}
			if (requestObject.getPageSize() != null) {
				searchContext.setPageSize(requestObject.getPageSize());
			}
			else {
				searchContext.setPageSize(DEFAULT_PAGE_SIZE);
			}
			searchContext.setStreamSource(requestObject.getStreamSource());
			searchContext.setFromDate(requestObject.getFromDate());
			searchContext.setToDate(requestObject.getToDate());
			searchContext.setIsVisible(requestObject.getIsVisibleInListing());
			searchContext.setShowInListing(requestObject.getIsVisibleInListing());
			if (requestObject.getTxnType() != null) {
				searchContext.setTxnType(requestObject.getTxnType().getTransactionTypeKey().toString());
			}
			return searchContext;
		}
		catch (Exception e) {
			log.error("Error while creating SearchContext {}", CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public static PaginationParams getPaginationParamsForNextQuery(final BackFillingQueryObject backFillingQueryObject,
			final TransformedTransactionHistoryDetail lastRecord) {
		PaginationParams paginationParams = getPaginationParams(lastRecord);
		SearchContext searchContext = backFillingQueryObject.getSearchContext();
		int currentPageNo = searchContext.getPageNo();
		searchContext.setPageNo(currentPageNo + 1);
		paginationParams.setPageNo(currentPageNo + 1);
		return paginationParams;
	}

	public static PaginationParams getPaginationParams(final TransformedTransactionHistoryDetail lastRecord) {
		PaginationParams paginationParams = new PaginationParams();
		paginationParams.setPaginationStreamSource(String.valueOf(lastRecord.getStreamSource()));
		paginationParams.setPaginationTxnId(lastRecord.getTxnId());
		paginationParams.setTransactionDateEpoch(String.valueOf(lastRecord.getTxnDate()));
		return paginationParams;
	}

	public static ChatBackFillingKafkaObject getInitialChatBackFillingKafkaObject(final String reportCodes,
			final SearchContext searchContext) {
		return ChatBackFillingKafkaObject.builder()
			.paginationParams(null)
			.searchContext(searchContext)
			.tag(CHAT_BACK_FILLING)
			.build();
	}

}
