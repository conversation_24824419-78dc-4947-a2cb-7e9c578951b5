package com.org.panaroma.commons.dto;

import static com.org.panaroma.commons.utils.ToStringUtility.getMaskedMapValue;
import static com.org.panaroma.commons.utils.ToStringUtility.getMaskedStringValue;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.org.panaroma.commons.dto.cart.CartDetails;
import com.org.panaroma.commons.utils.Utility;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.avro.reflect.AvroSchema;
import org.apache.avro.reflect.Nullable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class TransactionHistoryDetails implements Serializable {

	@Nullable
	TransactionSource streamSource;

	@Nullable
	TransactionSource sourceSystem;

	@Nullable
	Boolean isBankData;

	@Nullable
	String amount;

	@Nullable
	Currency currency;

	@Nullable
	String remarks;

	@Nullable
	ClientStatusEnum status;

	@Nullable
	List<Tags> tags;

	@Nullable
	String umn;

	@Nullable
	String systemId;

	@Nullable
	String sourceSystemId;

	@Nullable
	String parentTxnId;

	@Nullable
	TransactionTypeEnum txnType;

	@Nullable
	String txnDate;

	@Nullable
	String updatedDate;

	@Nullable
	Location location;

	@Nullable
	Map<String, String> contextMap;

	@Nullable
	RepeatPaymentData repeatPaymentData;

	@Nullable
	List<TxnParticipants> participants;

	@Nullable
	String notesKey;

	@Nullable
	AmountBreakup amountBreakup;

	@Nullable
	Map<String, String> chatData;

	@Nullable
	@JsonProperty("cartDetails")
	CartDetails cartDetails;

	@Nullable
	boolean participantsNotAvailable;

	@Nullable
	FwdTxnDetails fwdTxnDetails;

	@JsonIgnore
	public TransactionTypeEnum getMainTxnType() {
		if (Utility.checkPpblTxnType(streamSource.getTransactionSourceKey(), contextMap)) {
			return TransactionTypeEnum.PPBL_TRANSACTION;
		}
		return txnType;
	}

	@Override
	public String toString() {
		String rrn = contextMap != null && contextMap.containsKey("rrn") ? contextMap.get("rrn") : null;
		return "TransactionHistoryDetails{" + "streamSource=" + streamSource + ", sourceSystem=" + sourceSystem
				+ ", isBankData=" + isBankData + ", amount='" + getMaskedStringValue(amount) + '\'' + ", currency="
				+ currency + ", remarks='" + remarks + '\'' + ", status=" + status + ", tags=" + tags + ", umn=" + umn
				+ ", systemId='" + systemId + '\'' + ", sourceSystemId='" + sourceSystemId + '\'' + ", notesKey='"
				+ notesKey + '\'' + ", parentTxnId='" + parentTxnId + '\'' + ", txnType=" + txnType + ", txnDate='"
				+ txnDate + '\'' + ", updatedDate='" + updatedDate + '\'' + ", location=" + location + ", contextMap="
				+ getMaskedMapValue(contextMap) + ", contextMap.errorMessage='"
				+ (contextMap == null ? "" : contextMap.get("errorMessage")) + '\'' + ", rrn=" + rrn
				+ ", repeatPaymentData=" + repeatPaymentData + ", participants=" + participants + ", amountBreakup="
				+ amountBreakup + ", cartDetails=" + cartDetails + ", participantsNotAvailable="
				+ participantsNotAvailable + ", fwdTxnDetails=" + fwdTxnDetails + '}';
	}

}
