package com.org.panaroma.commons.tags.dto;

import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.tags.TagRuleTxnDto;
import lombok.AllArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@AllArgsConstructor
@ToString
public class TagRuleFieldVerticalIds implements TagRuleField {

	private Set<String> verticalIds;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		TagRuleFieldVerticalIds that = (TagRuleFieldVerticalIds) o;
		return Objects.equals(verticalIds, that.verticalIds);
	}

	@Override
	public int hashCode() {
		return Objects.hash(verticalIds);
	}

	@Override
	public boolean isValidRule(TagRuleTxnDto tagRuleDto) {
		String value = tagRuleDto.getVerticalId();
		if (StringUtils.isBlank(value)) {
			return false;
		}
		return verticalIds.contains(value);
	}

	public static TagRuleFieldVerticalIds of(TagConfigurationEntity entity) {
		if (entity == null || StringUtils.isBlank(entity.getVerticalIds())) {
			return null;
		}
		String dbFormatValue = entity.getVerticalIds().trim();
		if (StringUtils.isBlank(dbFormatValue)) {
			return null;
		}
		Set<String> verticalIdsSet = Stream.of(dbFormatValue.split(","))
			.map(String::trim)
			.filter(StringUtils::isNotBlank)
			.collect(Collectors.toSet());
		return new TagRuleFieldVerticalIds(verticalIdsSet);
	}

}
