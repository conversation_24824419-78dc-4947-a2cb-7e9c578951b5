package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.Constants.DOT_PNG;
import static com.org.panaroma.commons.constants.WebConstants.AUTOMATIC_PAYMENTS;
import static com.org.panaroma.commons.constants.WebConstants.BANK_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.BHIM_UPI_NAME;
import static com.org.panaroma.commons.constants.WebConstants.CARD;
import static com.org.panaroma.commons.constants.WebConstants.CREDIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT_USER_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.EMPTY_STRING;
import static com.org.panaroma.commons.constants.WebConstants.IPO_MANDATE_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_BANK_ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_WALLET;
import static com.org.panaroma.commons.constants.WebConstants.LOYALTY_POINT;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_DEFAULT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.MOBILE_NUMBER;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.RECURRING_MANDATE_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.OC_100_GUIDELINES_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ONLINE_PAYMENT;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_POSTPAID;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_LOAN;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_PAYMENT_MODE_LOWER_CASE;
import static com.org.panaroma.commons.constants.WebConstants.PoweredByLogoConstant.DEFAULT_POWERED_BY_UPI;
import static com.org.panaroma.commons.constants.WebConstants.PoweredByLogoConstant.ULTRA;
import static com.org.panaroma.commons.constants.WebConstants.QR_SCAN;
import static com.org.panaroma.commons.constants.WebConstants.RECENT_PAYMENTS;
import static com.org.panaroma.commons.constants.WebConstants.RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.RECURRING_MANDATE_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.RUPAY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.RUPAY_LOGO_NAME;
import static com.org.panaroma.commons.constants.WebConstants.SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.ULTRA_BANK_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ULTRA_RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ULTRA_RECURRING_MANDATE_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ULTRA_UPI_CC_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ULTRA_UPI_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ULTRA_WALLET_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_CC_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_POWEREDBY_LOGO;

import com.org.panaroma.commons.cache.UthCategoriesCacheHolder;
import com.org.panaroma.commons.config.MerchantLogoVpaMapper;
import com.org.panaroma.commons.constants.Constants;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.es.TransformedCardData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoOrderTypeEnum;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.Theme;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import jakarta.annotation.Nullable;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class LogoUtility {

	@Autowired
	Environment environment;

	@Autowired
	ApplicationContext applicationContext;

	private static String BANK_LOGO_BASE_URL;

	private static String CATEGORY_LOGO_BASE_URL;

	private static String WALLET_LOGO_BASE_URL;

	private static String STATUS_LOGO_BASE_URL;

	private static String PAYTM_LOGO_BASE_URL;

	private static String MERCHANT_LOGO_BASE_URL;

	private static String UPI_MERCHANT_LOGO_BASE_URL;

	private static UthCategoriesCacheHolder CACHE_HOLDER;

	private static MerchantLogoVpaMapper MERCHANT_VPA_LOGO_MAP;

	private static String PAYMENT_MODE_LOGO_BASE_URL;

	private static String MANDATE_HISTORY_MERCHANT_VPA_LOGO_BASE_URL;

	private static String PASSBOOK_SINGLEAPI_LOGO_BASE_URL;

	@Autowired(required = false)
	LogoUtility(@Value("${static-bank-logo-base-url}") final String bankLogoBaseUrl,
			@Value("${static-category-logo-base-url}") final String categoryBaseUrl,
			@Value("${static-wallet-logo-base-url}") final String walletBaseUrl,
			@Value("${static-status-logo-base-url}") final String statusBaseUrl,
			@Value("${static-paytm-logo-base-url}") final String paytmBaseUrl,
			@Value("${static-merchant-logo-base-url}") final String merchantBaseUrl,
			@Value("${static-upi-merchant-logo-base-url}") final String upiMerchantLogoBaseUrl,
			@Value("${static-paymentMode-logo-base-url}") final String paymentModeLogoBaseUrl,
			@Value("${static-mandate-history-merchant-vpa-logo-base-url}") final String mandateHistoryMerchantVpaLogoBaseUrl,
			@Value("${static-passbook-singleapi-logo-base-url}") final String paytmPassbookLogoBaseUrl,
			@Nullable final UthCategoriesCacheHolder cacheHolder, final MerchantLogoVpaMapper merchantLogoVpaMapper) {
		BANK_LOGO_BASE_URL = bankLogoBaseUrl;
		CATEGORY_LOGO_BASE_URL = categoryBaseUrl;
		WALLET_LOGO_BASE_URL = walletBaseUrl;
		STATUS_LOGO_BASE_URL = statusBaseUrl;
		PAYTM_LOGO_BASE_URL = paytmBaseUrl;
		MERCHANT_LOGO_BASE_URL = merchantBaseUrl;
		UPI_MERCHANT_LOGO_BASE_URL = upiMerchantLogoBaseUrl;
		CACHE_HOLDER = cacheHolder;
		MERCHANT_VPA_LOGO_MAP = merchantLogoVpaMapper;
		PAYMENT_MODE_LOGO_BASE_URL = paymentModeLogoBaseUrl;
		MANDATE_HISTORY_MERCHANT_VPA_LOGO_BASE_URL = mandateHistoryMerchantVpaLogoBaseUrl;
		PASSBOOK_SINGLEAPI_LOGO_BASE_URL = paytmPassbookLogoBaseUrl;
		updateIcons();
	}

	// updating all icons map;
	private void updateIcons() {
		updateWalletIcons();
		updateCategoryIcons();
		updateOtherIcons();
		updateStatusIcons();
		updateBankCategoryIcons();
		updateCardLogo();
		updatePaymentModeIcons();
	}

	private static Map<String, String> walletIconMap;

	private static Map<String, String> categoryMap;

	private static Map<String, String> statusMap;

	private static Map<String, String> otherIconMap;

	private static Map<TransactionTypeEnum, String> bankCategoryMap;

	private static Map<String, String> cardLogoMap;

	private static final Map<String, String> bankNameToIfscMap;

	private static Map<String, String> paymentModeMap;

	void updatePaymentModeIcons() {
		Map<String, String> iconMap = new HashMap<>();
		iconMap.put(ONLINE_PAYMENT, PAYMENT_MODE_LOGO_BASE_URL + "Online_Payment.jpeg");
		iconMap.put(RECENT_PAYMENTS, PAYMENT_MODE_LOGO_BASE_URL + "Recent_Payments.jpeg");
		iconMap.put(AUTOMATIC_PAYMENTS, PAYMENT_MODE_LOGO_BASE_URL + "Automatic_payments.jpeg");
		iconMap.put(MOBILE_NUMBER, PAYMENT_MODE_LOGO_BASE_URL + "Mobile.jpeg");
		iconMap.put(QR_SCAN, PAYMENT_MODE_LOGO_BASE_URL + "QR_Code.jpeg");
		iconMap.put(LINKED_WALLET, PAYMENT_MODE_LOGO_BASE_URL + "Paytm_Wallet.jpeg");
		paymentModeMap = Collections.unmodifiableMap(iconMap);
	}

	void updateCardLogo() {
		Map<String, String> logoMap = new HashMap<>();
		logoMap.put(RUPAY_LOGO, PAYTM_LOGO_BASE_URL + RUPAY_LOGO_NAME);
		cardLogoMap = Collections.unmodifiableMap(logoMap);
	}

	void updateWalletIcons() {
		Map<String, String> logoMap = new HashMap<>();
		logoMap.put("Wallet", WALLET_LOGO_BASE_URL + "generic-wallet.png");
		logoMap.put(WalletTypesEnum.SCLW.getDisplayName(), WALLET_LOGO_BASE_URL + "paytm-wallet.png");
		logoMap.put(WalletTypesEnum.CLW.getDisplayName(), WALLET_LOGO_BASE_URL + "generic-wallet.png");
		logoMap.put(WalletTypesEnum.FOOD_WALLET.getDisplayName(), WALLET_LOGO_BASE_URL + "food-wallet.png");
		logoMap.put(WalletTypesEnum.GIFT_WALLET.getDisplayName(), WALLET_LOGO_BASE_URL + "gift-wallet.png");
		logoMap.put(WalletTypesEnum.MULTI_PURPOSE_GIFT.getDisplayName(), WALLET_LOGO_BASE_URL + "generic-wallet.png");
		logoMap.put(WalletTypesEnum.BLOCKED_WALLET.getDisplayName(), WALLET_LOGO_BASE_URL + "generic-wallet.png");
		logoMap.put(WalletTypesEnum.TOLL_WALLET.getDisplayName(), WALLET_LOGO_BASE_URL + "toll-wallet.png");
		logoMap.put(WalletTypesEnum.CLOSED_LOOP_WALLET.getDisplayName(), WALLET_LOGO_BASE_URL + "generic-wallet.png");
		logoMap.put(WalletTypesEnum.CLOSED_LOOP_SUB_WALLET.getDisplayName(),
				WALLET_LOGO_BASE_URL + "generic-wallet.png");
		logoMap.put(WalletTypesEnum.FUEL_WALLET.getDisplayName(), WALLET_LOGO_BASE_URL + "fuel-wallet.png");
		logoMap.put(WalletTypesEnum.INTERNATIONAL_FUNDS_TRANSFER_WALLET.getDisplayName(),
				WALLET_LOGO_BASE_URL + "international-remittance-wallet.png");
		logoMap.put(WalletTypesEnum.CASHBACK_WALLET.getDisplayName(), WALLET_LOGO_BASE_URL + "generic-wallet.png");
		logoMap.put(WalletTypesEnum.GIFT_VOUCHER.getDisplayName(), WALLET_LOGO_BASE_URL + "gift-voucher.png");
		logoMap.put(WalletTypesEnum.COMMUNICATION_WALLET.getDisplayName(),
				WALLET_LOGO_BASE_URL + "communication-wallet.png");
		// commenting this logo as it's overriding main wallet logo url
		// logoMap.put(WalletTypesEnum.OTHER.getDisplayName(), WALLET_LOGO_BASE_URL +
		// "generic-wallet.png");
		logoMap.put(WebConstants.POSTPAID_LOGO, WALLET_LOGO_BASE_URL + "postpaid.png");

		walletIconMap = Collections.unmodifiableMap(logoMap);
	}

	void updateOtherIcons() {
		Map<String, String> otherIcons = new HashMap<>();
		otherIcons.put(UPI_LOGO, BANK_LOGO_BASE_URL + "bhim-upi.png");
		otherIcons.put(WebConstants.NAME_VERIFIED_LOGO, PAYTM_LOGO_BASE_URL + "name-verified.png");
		otherIcons.put(WebConstants.BC_AGENT_LOGO, CATEGORY_LOGO_BASE_URL + "add-money-through-bc-agent.png");
		otherIcons.put(OC_100_GUIDELINES_LOGO, CATEGORY_LOGO_BASE_URL + "oc100guideline.png");
		otherIcons.put(LOYALTY_POINT, CATEGORY_LOGO_BASE_URL + "cashback_points.png");
		otherIcons.put(DEFAULT_USER_LOGO, PAYTM_LOGO_BASE_URL + "default_user.png");
		otherIcons.put(WebConstants.WalletMaintenanceChargeConstants.WMC_DEDUCTED,
				WALLET_LOGO_BASE_URL + "wallet_maintenance_charges.png");
		otherIcons.put(WebConstants.WalletMaintenanceChargeConstants.WMC_REVERSED,
				WALLET_LOGO_BASE_URL + "wallet_maintenance_charges_reversed.png");
		otherIcons.put(SELF_TRANSFER, CATEGORY_LOGO_BASE_URL + "self_transfer.png");
		otherIcons.put(IPO_MANDATE_LOGO, PAYTM_LOGO_BASE_URL + "ipo.png");
		otherIcons.put(RECURRING_MANDATE_LOGO, PAYTM_LOGO_BASE_URL + "recurring-mandate.png");
		otherIcons.put(WALLET_POWEREDBY_LOGO, CATEGORY_LOGO_BASE_URL + "powered_by_ppbl.png");
		otherIcons.put(BANK_POWEREDBY_LOGO, CATEGORY_LOGO_BASE_URL + "powered_by_ppbl.png");
		otherIcons.put(ULTRA_WALLET_POWEREDBY_LOGO, CATEGORY_LOGO_BASE_URL + "ultra_powered_by_ppbl.png");
		otherIcons.put(ULTRA_BANK_POWEREDBY_LOGO, CATEGORY_LOGO_BASE_URL + "ultra_powered_by_ppbl.png");
		otherIcons.put(ULTRA_UPI_POWEREDBY_LOGO, CATEGORY_LOGO_BASE_URL + "ultra_powered_by_upi_ppbl.png");
		otherIcons.put(RECURRING_MANDATE_POWEREDBY_LOGO, CATEGORY_LOGO_BASE_URL + "powered_by_UPIAutopay_ppbl.png");
		otherIcons.put(ULTRA_RECURRING_MANDATE_POWEREDBY_LOGO,
				CATEGORY_LOGO_BASE_URL + "ultra_powered_by_UPIAutopay_ppbl.png");
		otherIcons.put(UPI_CC_POWEREDBY_LOGO, CATEGORY_LOGO_BASE_URL + "powered_by_UPI_CC_ppbl.png");
		otherIcons.put(ULTRA_UPI_CC_POWEREDBY_LOGO, CATEGORY_LOGO_BASE_URL + "ultra_powered_by_UPI_CC_ppbl.png");
		otherIcons.put(RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO,
				CATEGORY_LOGO_BASE_URL + "powered_by_UPIAutopay_CC_ppbl.png");
		otherIcons.put(ULTRA_RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO,
				CATEGORY_LOGO_BASE_URL + "ultra_powered_by_UPIAutopay_CC_ppbl.png");
		otherIconMap = Collections.unmodifiableMap(otherIcons);
	}

	void updateStatusIcons() {
		Map<String, String> statusmap = new HashMap<>();
		statusmap.put("FAILURE", STATUS_LOGO_BASE_URL + "failed.png");
		statusmap.put("SUCCESS", STATUS_LOGO_BASE_URL + "success.png");
		statusmap.put("PENDING", STATUS_LOGO_BASE_URL + "pending.png");
		statusmap.put("SUCCESS-2", STATUS_LOGO_BASE_URL + "success-2.png");
		statusmap.put("FAILURE-2", STATUS_LOGO_BASE_URL + "failed-2.png");
		statusmap.put("PENDING-2", STATUS_LOGO_BASE_URL + "pending-2.png");

		statusMap = Collections.unmodifiableMap(statusmap);
	}

	void updateCategoryIcons() {
		Map<String, String> categorymap = new HashMap<>();
		categorymap.put(WebConstants.P2M_LOGO, CATEGORY_LOGO_BASE_URL + "p2m.png");
		categorymap.put(WebConstants.P2M_REFUND_LOGO, CATEGORY_LOGO_BASE_URL + "p2m-refund.png");
		categorymap.put(WebConstants.P2M_INTERNATIONAL_LOGO, CATEGORY_LOGO_BASE_URL + "p2m-international.png");
		categorymap.put(WebConstants.P2M_REVERSAL_INTERNATIONAL_LOGO, CATEGORY_LOGO_BASE_URL + "p2m-international.png");
		categorymap.put(WebConstants.WALLET_P2P_OUT_REV_LOGO, WALLET_LOGO_BASE_URL + "wallet-refund.png");
		categorymap.put(WebConstants.UPI_P2P_OUT_LOGO, CATEGORY_LOGO_BASE_URL + "bank-p2p-out.png");
		categorymap.put(WebConstants.WALLET_P2P_IN_LOGO, WALLET_LOGO_BASE_URL + "wallet-p2p-in.png");
		categorymap.put(WebConstants.WALLET_P2P_OUT_LOGO, WALLET_LOGO_BASE_URL + "wallet-p2p-out.png");
		categorymap.put(WebConstants.UPI_P2P_IN_LOGO, CATEGORY_LOGO_BASE_URL + "bank-p2p-in.png");
		categorymap.put(WebConstants.UPI_P2P_OUT_REV_LOGO, CATEGORY_LOGO_BASE_URL + "bank-p2p-out-reversal.png");
		categorymap.put(WebConstants.ON_HOLD_LOGO, WALLET_LOGO_BASE_URL + "wallet-on-hold.png");
		categorymap.put(WebConstants.RELEASED_LOGO, WALLET_LOGO_BASE_URL + "wallet-on-hold-release.png");
		categorymap.put(WebConstants.CASHBACK_LOGO, WALLET_LOGO_BASE_URL + "cashback.png");
		categorymap.put(WebConstants.ADD_MONEY_LOGO, WALLET_LOGO_BASE_URL + "add-money-wallet.png");
		categorymap.put(WebConstants.AUTOMATIC_ADD_MONEY_LOGO, WALLET_LOGO_BASE_URL + "automatic-add-money-wallet.png");
		categorymap.put(WebConstants.MERCHANT_DEFAULT_LOGO, CATEGORY_LOGO_BASE_URL + "merchant-default.png");
		categorymap.put(WebConstants.ADD_MONEY_BANK_LOGO, CATEGORY_LOGO_BASE_URL + "add-money-bank.png");
		categorymap.put(WebConstants.PPBL_CONSTANT_LOGO, BANK_LOGO_BASE_URL + "PYTM.png");
		categorymap.put(WebConstants.UPI_WALLET_CREDIT_REVERSAL_LOGO,
				CATEGORY_LOGO_BASE_URL + "wallet-p2p-in-reversal.png");

		categoryMap = Collections.unmodifiableMap(categorymap);
	}

	void updateBankCategoryIcons() {
		Map<TransactionTypeEnum, String> map = new HashMap<>();
		map.put(TransactionTypeEnum.ADD_MONEY, CATEGORY_LOGO_BASE_URL + "add-money-bank.png");
		map.put(TransactionTypeEnum.P2P_OUTWARD, CATEGORY_LOGO_BASE_URL + "bank-p2p-out.png");
		map.put(TransactionTypeEnum.CASH_WITHDRAWAL, CATEGORY_LOGO_BASE_URL + "cash-withdrawal.png");
		map.put(TransactionTypeEnum.CHEQUE_CREDIT, CATEGORY_LOGO_BASE_URL + "cheque-credit.png");
		map.put(TransactionTypeEnum.DEPOSIT_CREATED_REVERSAL, CATEGORY_LOGO_BASE_URL + "fd-create-reverse.png");
		map.put(TransactionTypeEnum.P2M_REFUND, CATEGORY_LOGO_BASE_URL + "p2m-refund.png");
		map.put(TransactionTypeEnum.P2P_INWARD_REVERSAL, CATEGORY_LOGO_BASE_URL + "bank-p2p-in-reversal.png");
		map.put(TransactionTypeEnum.CASHBACK_RECEIVED, CATEGORY_LOGO_BASE_URL + "cashback.png");
		map.put(TransactionTypeEnum.CHEQUE_DEBIT_REVERSAL, CATEGORY_LOGO_BASE_URL + "cheque-debit-reversal.png");
		map.put(TransactionTypeEnum.DEPOSIT_CREATED, CATEGORY_LOGO_BASE_URL + "fd-create.png");
		map.put(TransactionTypeEnum.P2M, CATEGORY_LOGO_BASE_URL + "p2m.png");
		map.put(TransactionTypeEnum.P2P_INWARD, CATEGORY_LOGO_BASE_URL + "bank-p2p-in.png");
		map.put(TransactionTypeEnum.CASH_DEPOSIT, CATEGORY_LOGO_BASE_URL + "cash-deposit.png");
		map.put(TransactionTypeEnum.CHARGES_REVERSAL, CATEGORY_LOGO_BASE_URL + "charges-refund.png");
		map.put(TransactionTypeEnum.CHEQUE_DEBIT, CATEGORY_LOGO_BASE_URL + "cheque-debit.png");
		map.put(TransactionTypeEnum.DEPOSIT_REDEEMED, CATEGORY_LOGO_BASE_URL + "fd-redeem.png");
		map.put(TransactionTypeEnum.DEPOSIT_INTEREST, CATEGORY_LOGO_BASE_URL + "fd-redeem.png");
		map.put(TransactionTypeEnum.FD_RECOVERY, CATEGORY_LOGO_BASE_URL + "fd-redeem.png");
		map.put(TransactionTypeEnum.STANDING_INSTRUCTION, CATEGORY_LOGO_BASE_URL + "recurring.png");
		map.put(TransactionTypeEnum.P2P_OUTWARD_REVERSAL, CATEGORY_LOGO_BASE_URL + "bank-p2p-out-reversal.png");
		map.put(TransactionTypeEnum.CASH_WITHDRAWAL_REVERSAL, CATEGORY_LOGO_BASE_URL + "cash-withdrawal-reversal.png");
		map.put(TransactionTypeEnum.CHARGES, CATEGORY_LOGO_BASE_URL + "charges.png");
		map.put(TransactionTypeEnum.COMPENSATION, CATEGORY_LOGO_BASE_URL + "compensation.png");
		map.put(TransactionTypeEnum.INTEREST_CREDIT, CATEGORY_LOGO_BASE_URL + "interest.png");
		map.put(TransactionTypeEnum.SALARY_CREDIT, CATEGORY_LOGO_BASE_URL + "salary.png");
		map.put(TransactionTypeEnum.OTHER_CREDIT, CATEGORY_LOGO_BASE_URL + "bank-p2p-in.png");
		map.put(TransactionTypeEnum.OTHER_DEBIT, CATEGORY_LOGO_BASE_URL + "bank-p2p-out.png");
		map.put(TransactionTypeEnum.NACH_CREDIT, CATEGORY_LOGO_BASE_URL + "bank-p2p-in.png");
		map.put(TransactionTypeEnum.NACH_DEBIT, CATEGORY_LOGO_BASE_URL + "bank-p2p-out.png");
		map.put(TransactionTypeEnum.NACH_CREDIT_REVERSAL, CATEGORY_LOGO_BASE_URL + "bank-p2p-in-reversal.png");
		map.put(TransactionTypeEnum.NACH_DEBIT_REVERSAL, CATEGORY_LOGO_BASE_URL + "bank-p2p-out-reversal.png");
		bankCategoryMap = Collections.unmodifiableMap(map);
		// "20212_D|20212_C|20211_C|20213_C|20221_C|20222_C|40107_C|20295_C|20270_C|20271_D|20273_D|20215|20216|20218|30103|40103|40108"
	}

	static {
		Map<String, String> bankIfscMap = new HashMap<>();
		bankIfscMap.put("icici", "ICIC");
		bankIfscMap.put("icici bank", "ICIC");
		bankIfscMap.put("icici bank limited", "ICIC");
		bankIfscMap.put("icici bank ltd", "ICIC");
		bankIfscMap.put("state bank of india", "SBIN");
		bankIfscMap.put("sbi", "SBIN");
		bankIfscMap.put("paytm payments bank limited", "PYTM");
		bankIfscMap.put("paytm payments bank ltd", "PYTM");
		bankIfscMap.put("paytm payments bank", "PYTM");
		bankIfscMap.put("PAYTM PAYMENTS BANK LTD", "PYTM");
		bankIfscMap.put("paytm bank", "PYTM");
		bankIfscMap.put("ppbl", "PYTM");
		bankIfscMap.put("hdfc bank", "HDFC");
		bankIfscMap.put("hdfc bank limited", "HDFC");
		bankIfscMap.put("hdfc", "HDFC");
		bankIfscMap.put("hdfc bank ltd", "HDFC");
		bankIfscMap.put("pnb", "PUNB");
		bankIfscMap.put("punjab national bank", "PUNB");
		bankIfscMap.put("union bank of india", "UBIN");
		bankIfscMap.put("union bank", "UBIN");
		bankIfscMap.put("hsbc bank oman saog", "OIBA");
		bankIfscMap.put("punjab and sind bank", "PSIB");
		bankIfscMap.put("amex", "AMEX");
		bankIfscMap.put("citibank", "CITI");
		bankIfscMap.putAll(IfscUtility.getBankToIfscMap());

		bankNameToIfscMap = Collections.unmodifiableMap(bankIfscMap);
	}

	public static String getLogo(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn, final String defaultLogoKey,
			final LogoType defaultLogoType) {

		Logo logo = LogoUtility.checkAndGetLogoByVpa(participant, txn);
		if (Objects.nonNull(logo)) {
			return logo.getValue();
		}
		// return default logo
		return LogoUtility.getLogo(defaultLogoKey, defaultLogoType);
	}

	public static String getLogo(final String key, final LogoType type) {
		if (StringUtils.isBlank(key)) {
			return null;
		}
		String logoUrl = null;
		switch (type) {
			case WALLET_ICON:
				logoUrl = walletIconMap.containsKey(key) ? walletIconMap.get(key)
						: walletIconMap.get(WalletTypesEnum.SCLW.getDisplayName());
				break;
			case TRANSACTION_CATEGORY_ICON:
				logoUrl = categoryMap.containsKey(key) ? categoryMap.get(key) : null;
				break;
			case STATUS_ICON:
				logoUrl = statusMap.containsKey(key) ? statusMap.get(key) : null;
				break;
			case PAYTM_ICON:
				logoUrl = PAYTM_LOGO_BASE_URL + "paytm-icon.png";
				break;
			case MERCHANT_ICON:
				logoUrl = MERCHANT_LOGO_BASE_URL + key + ".png";
				break;
			case UPI_LITE_ICON:
				logoUrl = PAYTM_LOGO_BASE_URL + "upi_lite_updated.png";
				break;
			case STORECASH_ICON:
				logoUrl = PAYTM_LOGO_BASE_URL + "store_cash.png";
				break;
			case GIFT_VOUCHER_ICON:
				logoUrl = PASSBOOK_SINGLEAPI_LOGO_BASE_URL + "gift_voucher.png";
				break;
			case OTHER:
				logoUrl = otherIconMap.containsKey(key) ? otherIconMap.get(key) : null;
				break;
			default:
				// Do nothing
		}
		return logoUrl;
	}

	public static String getBankLogo(final String ifsc, final String bankName) {
		return getBankLogoWithDefault(ifsc, bankName, WebConstants.DEFAULT_BANK);
	}

	public static String getBankLogo(final String ifsc, final String bankName, final String bankLogoBaseUrl) {
		return getBankLogoWithDefault(ifsc, bankName, WebConstants.DEFAULT_BANK, bankLogoBaseUrl);
	}

	public static String getCardLogo(final String cardNetwork) {
		return cardLogoMap.getOrDefault(cardNetwork.toLowerCase(), otherIconMap.get(UPI_LOGO));
	}

	public static String getBankLogoOrNull(final String ifsc, final String bankName) {
		return getBankLogoWithDefault(ifsc, bankName, null);
	}

	public static String getOrgKsLogo() {
		return getBankLogoWithDefault(Constants.PAYTM_IFSC, null, WebConstants.DEFAULT_BANK);
	}

	private static String getBankIdentifier(final String passedIfsc, final String bankName, final String defaultLogo) {
		String ifsc = passedIfsc;
		String bankIdentifier;
		if (StringUtils.isNotBlank(ifsc) && ifsc.length() >= 4) {
			String shortIfsc = ifsc.substring(0, 4);
			bankIdentifier = checkIfValidIfsc(shortIfsc) ? shortIfsc.toUpperCase() : defaultLogo;
		}
		else if (StringUtils.isNotBlank(bankName)) {
			ifsc = getIfscFromBankNameWithDefaultIfsc(bankName.toLowerCase());
			bankIdentifier = checkIfValidIfsc(ifsc) ? ifsc.substring(0, 4).toUpperCase() : defaultLogo;
		}
		else {
			bankIdentifier = defaultLogo;
		}
		return bankIdentifier;
	}

	private static String getBankLogoWithDefault(final String passedIfsc, final String bankName,
			final String defaultLogo) {
		String bankIdentifier = getBankIdentifier(passedIfsc, bankName, defaultLogo);
		return getLogoUrl(bankIdentifier);
	}

	private static String getBankLogoWithDefault(final String passedIfsc, final String bankName,
			final String defaultLogo, final String bankLogoBaseUrl) {
		String bankIdentifier = getBankIdentifier(passedIfsc, bankName, defaultLogo);
		return getLogoUrl(bankIdentifier, bankLogoBaseUrl);
	}

	/*
	 * This method for get Bank logo of bank linked with UPI if Bank information is not
	 * valid then Default Bhim logo will be used
	 */
	public static String getUpiBankLogo(final String ifsc, final String bankName) {
		return getBankLogoWithDefault(ifsc, bankName, WebConstants.BHIM_UPI);
	}

	public static String formLogoUrl(final String image, final LogoType type) {
		if (StringUtils.isBlank(image)) {
			return null;
		}
		String logoUrl = null;
		switch (type) {
			case TRANSACTION_CATEGORY_ICON:
				logoUrl = CATEGORY_LOGO_BASE_URL + image;
				break;
			default:
				logoUrl = null;
		}
		return logoUrl;
	}

	public static String getBankCategoryLogo(final Integer txnType) {
		if (txnType == null) {
			return null;
		}
		return bankCategoryMap.get(TransactionTypeEnum.getTransactionTypeEnumByKey(txnType));
	}

	public static String getLogoHelper(final TransformedParticipant participant) {
		// log.info("Size of uthCategoryDetails cache is :{}",
		// CACHE_HOLDER.getUthCategoriesCache().size());
		if (!Objects.isNull(participant.getLogoUrl())) {
			return participant.getLogoUrl();
		}
		else if (!Objects.isNull(participant.getMerchantData())
				&& !WebConstants.UTH_CATEGORY_OTHERS.equalsIgnoreCase(participant.getMerchantData().getUthCategory())
				&& StringUtils.isNotBlank(participant.getMerchantData().getUthCategory())) {
			return CACHE_HOLDER.getUthCategoriesCache().get(participant.getMerchantData().getUthCategory());
		}
		return null;
	}

	public static String getUthCategoryLogo(final UthCategoryEnum uthCategoryEnum) {
		if (Objects.isNull(uthCategoryEnum)) {
			return null;
		}
		return CACHE_HOLDER.getUthCategoriesCache().get(uthCategoryEnum.getUthCategoryId());
	}

	// Method for getting checking vpa and getting logo by vpa
	public static Logo checkAndGetLogoByVpa(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn) {
		/*
		 * Condition for P2M and P2M REFUND UPI Txns streamSource - UPI isSource - True
		 * And sourceSystem -Null
		 */
		if ((TransactionTypeEnum.P2M.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(txn.getMainTxnType()))
				&& TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& Boolean.TRUE.equals(txn.getIsSource()) && Objects.isNull(txn.getSourceSystem())
				&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {

			String merchantVpa = participant.getUpiData() != null ? participant.getUpiData().getVpa() : null;

			String logoStoredVpa = MERCHANT_VPA_LOGO_MAP.getMappedVpa(merchantVpa);
			return logoStoredVpa != null ? getUrlTypeLogo(logoStoredVpa, WebConstants.UPI_MERCHANT_LOGO) : null;
		}

		if ((TransactionTypeEnum.P2M_INTERNATIONAL.getTransactionTypeKey().equals(txn.getMainTxnType())
				|| TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL.getTransactionTypeKey().equals(txn.getMainTxnType()))
				&& TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& Boolean.TRUE.equals(txn.getIsSource()) && Objects.isNull(txn.getSourceSystem())
				&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {

			String logoForP2mInt = categoryMap.get(WebConstants.P2M_INTERNATIONAL_LOGO);
			return new Logo(LogoOrderTypeEnum.URL, logoForP2mInt);
		}
		return null;
	}

	private static boolean checkIfValidIfsc(final String ifsc) {
		return IfscUtility.checkIfValidIfsc(ifsc);
	}

	public static String getLogoUrl(final String logoIdentifier) {
		if (StringUtils.isNotBlank(logoIdentifier)) {
			return BANK_LOGO_BASE_URL + logoIdentifier + ".png";
		}
		return null;
	}

	private static String getLogoUrl(final String logoIdentifier, final String bankLogoBaseUrl) {
		if (StringUtils.isNotBlank(logoIdentifier)) {
			return bankLogoBaseUrl + logoIdentifier + ".png";
		}
		return null;
	}

	private static String getIfscFromBankNameWithDefaultIfsc(final String bankName) {
		return bankNameToIfscMap.containsKey(bankName) ? bankNameToIfscMap.get(bankName) : WebConstants.DEFAULT_BANK;
	}

	public static Logo getUrlTypeLogo(final String logoCreationValue, final String logoDynamicKey) {
		if (StringUtils.isBlank(logoCreationValue)) {
			return null;
		}

		switch (logoDynamicKey) {
			case WebConstants.UPI_MERCHANT_LOGO: {
				String logoUrl = UPI_MERCHANT_LOGO_BASE_URL + logoCreationValue + ".png";
				return new Logo(LogoOrderTypeEnum.URL, logoUrl);
			}
			default:
				return null;
		}
	}

	public static String getMerchantLogoUrlFromVpa(final String vpa) {
		if (StringUtils.isBlank(vpa)) {
			return null;
		}
		String logoStoredVpa = MERCHANT_VPA_LOGO_MAP.getMappedVpa(vpa);
		Logo logo = StringUtils.isNotBlank(logoStoredVpa)
				? getUrlTypeLogo(logoStoredVpa, WebConstants.UPI_MERCHANT_LOGO) : null;

		return Objects.nonNull(logo) ? logo.getValue() : null;
	}

	public static String getUpiUrlString(final TransformedParticipant participant) {
		String logoUrl = participant.getLogoUrl() != null ? participant.getLogoUrl() : null;
		if (logoUrl == null) {
			if (participant.getBankData() != null && participant.getBankData().getIfsc() != null
					&& participant.getBankData().getBankName() != null) {
				logoUrl = LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
						participant.getBankData().getBankName());
			}
			else {
				logoUrl = LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER);
			}
		}
		return logoUrl;
	}

	public static String getIfscFromBankName(final String bankName) {
		if (StringUtils.isBlank(bankName)) {
			return null;
		}
		return bankNameToIfscMap.get(bankName.toLowerCase());
	}

	public static Map<String, String> getBankNameToIfscMap() {
		return bankNameToIfscMap;
	}

	public static List<String> getUserLogoV2ForAddMoney(final TransformedTransactionHistoryDetail esDto) {
		if (Objects.isNull(esDto)) {
			return null;
		}
		TransactionSource source = TransactionSource.getTransactionSourceEnumByKey(esDto.getStreamSource());
		TransformedParticipant debitParticipant = Utility.getDebitParticipant(esDto);
		// String logo = null;
		List<String> logoDetails = null;
		if (Objects.nonNull(debitParticipant)) {
			logoDetails = getUserLogoV2ForAddMoneyBasedOnPaymentSystem(debitParticipant, esDto);
		}
		if (logoDetails == null && TransactionSource.WALLET.equals(source)) {
			logoDetails = getDefaultLogoForAddMoneyWallet(esDto);
		}
		return logoDetails;
	}

	private static List<String> getUserLogoV2ForAddMoneyBasedOnPaymentSystem(
			final TransformedParticipant debitParticipant, final TransformedTransactionHistoryDetail esDto) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum
			.getPaymentSystemEnumByKey(debitParticipant.getPaymentSystem());
		/*
		 * The below if condition will never be true as when paymentSystem is null,
		 * isVisible flag will be false But the null check is placed just to avoid any
		 * NullPointer if paymentSystem becomes null somehow
		 */
		if (Objects.isNull(paymentSystem)) {
			log.error("paymentSystem is null for fetching user logo v2 for Add Money listing response for es doc : {}",
					esDto);
			return null;
		}
		String logoUrl = null;
		String instrumentName = null;
		switch (paymentSystem) {
			case UPI:
				if (Utility.isUpiViaCcTxn(esDto, debitParticipant)) {
					logoUrl = LogoUtility.getCardLogo(debitParticipant.getCardData().getCardNetwork());

					if (StringUtils.isNotBlank(debitParticipant.getCardData().getCardNetwork())) {
						instrumentName = WordUtils.capitalizeFully(debitParticipant.getCardData().getCardNetwork())
								+ SPACE + CARD;
					}
					else {
						instrumentName = CREDIT_CARD;
					}
				}
				else if (Objects.nonNull(debitParticipant.getBankData())) {
					logoUrl = LogoUtility.getBankLogo(debitParticipant.getBankData().getIfsc(),
							debitParticipant.getBankData().getBankName());

					if (StringUtils.isNotBlank(debitParticipant.getBankData().getBankName())) {
						instrumentName = debitParticipant.getBankData().getBankName();
					}
					else {
						instrumentName = StringUtils.isNotBlank(debitParticipant.getBankData().getIfsc())
								? IfscUtility.getBank(debitParticipant.getBankData().getIfsc()) : BHIM_UPI_NAME;
					}
				}
				else {
					logoUrl = LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER);
					instrumentName = BHIM_UPI_NAME;
				}
				break;

			case PG:
			case BANK:
				TransformedCardData cardData = debitParticipant.getCardData();
				if (Objects.nonNull(cardData)) {
					logoUrl = LogoUtility.getBankLogo(null, cardData.getCardIssuer());
					instrumentName = cardData.getCardIssuer();
				}
				else if (Objects.nonNull(debitParticipant.getBankData())) {
					logoUrl = LogoUtility.getBankLogo(debitParticipant.getBankData().getIfsc(),
							debitParticipant.getBankData().getBankName());

					if (StringUtils.isNotBlank(debitParticipant.getBankData().getBankName())) {
						instrumentName = debitParticipant.getBankData().getBankName();
					}
					else {
						instrumentName = StringUtils.isNotBlank(debitParticipant.getBankData().getIfsc())
								? IfscUtility.getBank(debitParticipant.getBankData().getIfsc()) : LINKED_BANK_ACCOUNT;
					}
				}
				else {
					logoUrl = LogoUtility.getBankLogo(null, null);
					instrumentName = LINKED_BANK_ACCOUNT;
				}
				break;
			case PAYTM_POSTPAID:
				logoUrl = LogoUtility.getLogo(POSTPAID_LOGO, LogoType.WALLET_ICON);
				instrumentName = POSTPAID_LOAN;
				break;
			default:
				/*
				 * We need to keep a track if paymentSystem value other than UPI, PG, BANK
				 * or Postpaid is seen. That's why whole doc is printed
				 */
				log.error(
						"Unexpected paymentSystem for fetching user logo v2 for Add Money listing response for es doc : {} ",
						esDto);
				return null;
		}
		List<String> logoDetails = new ArrayList<>();
		logoDetails.add(logoUrl);
		logoDetails.add(instrumentName);
		return logoDetails;
	}

	private static List<String> getDefaultLogoForAddMoneyWallet(final TransformedTransactionHistoryDetail walletTxn) {
		if (Objects.isNull(walletTxn.getContextMap())) {
			return null;
		}
		String paymentMode = walletTxn.getContextMap().getOrDefault("paymentMode", null);
		String bankName = walletTxn.getContextMap().getOrDefault("bankName", EMPTY_STRING);
		if (StringUtils.isNotBlank(paymentMode)) {
			List<String> logoDetails = new ArrayList<>();
			switch (paymentMode.toLowerCase()) {
				case "upi":
					logoDetails.add(LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER));
					logoDetails.add(BHIM_UPI_NAME);
					return logoDetails;
				// return LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER);

				case "cc": // credit card
				case "nb": // net banking
				case "dc": // debit card
					logoDetails.add(LogoUtility.getBankLogo(null, bankName));
					logoDetails.add(bankName);
					return logoDetails;

				case POSTPAID_PAYMENT_MODE_LOWER_CASE:
					logoDetails.add(LogoUtility.getLogo(POSTPAID_LOGO, LogoType.WALLET_ICON));
					logoDetails.add(PAYTM_POSTPAID);
					return logoDetails;

				case LOYALTY_POINT: // Loyalty Point
					logoDetails.add(LogoUtility.getLogo(LOYALTY_POINT, LogoType.OTHER));
					logoDetails.add(bankName);
					return logoDetails;

				default:
					if (StringUtils.isNotEmpty(bankName)) {
						logoDetails.add(LogoUtility.getBankLogo(null, bankName));
						logoDetails.add(bankName);
						return logoDetails;
					}
			}
		}
		else {
			// Need to check why this else block is in AddMoneyDetailResponseBuilder
			// too???
			String merchantName = walletTxn.getContextMap().getOrDefault("merchantName", null);
			if (StringUtils.isNotBlank(merchantName)) {
				List<String> logoDetails = new ArrayList<>();
				logoDetails.add(LogoUtility.getLogo(MERCHANT_DEFAULT_LOGO, LogoType.TRANSACTION_CATEGORY_ICON));
				logoDetails.add(merchantName);
				return logoDetails;
			}
		}
		return null;
	}

	public static String defaultLogo(final PaymentSystemEnum paymentSystemEnum) {
		if (PaymentSystemEnum.UPI.equals(paymentSystemEnum)) {
			return LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER);
		}

		return getLogoUrl(WebConstants.DEFAULT_BANK);
	}

	/*
	 * public static String getPoweredByRecurringMandateLogoUrl() { return
	 * otherIconMap.containsKey(RECURRING_MANDATE_POWEREDBY_LOGO) ?
	 * otherIconMap.get(RECURRING_MANDATE_POWEREDBY_LOGO) : null; }
	 *
	 * public static String getUltraPoweredByRecurringMandateLogoUrl() { return
	 * otherIconMap.containsKey(ULTRA_RECURRING_MANDATE_POWEREDBY_LOGO) ?
	 * otherIconMap.get(ULTRA_RECURRING_MANDATE_POWEREDBY_LOGO) : null; }
	 *
	 * public static String getPoweredByUpiCcLogoUrl() { return
	 * otherIconMap.containsKey(UPI_CC_POWEREDBY_LOGO) ?
	 * otherIconMap.get(UPI_CC_POWEREDBY_LOGO) : null; }
	 *
	 * public static String getUltraPoweredByUpiCcLogoUrl() { return
	 * otherIconMap.containsKey(ULTRA_UPI_CC_POWEREDBY_LOGO) ?
	 * otherIconMap.get(ULTRA_UPI_CC_POWEREDBY_LOGO) : null; }
	 *
	 * public static String getPoweredByRecurringMandateAndUpiCcLogoUrl() { return
	 * otherIconMap.containsKey(RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO) ?
	 * otherIconMap.get(RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO) : null; }
	 *
	 * public static String getUltraPoweredByRecurringMandateAndUpiCcLogoUrl() { return
	 * otherIconMap.containsKey(ULTRA_RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO) ?
	 * otherIconMap.get(ULTRA_RECURRING_MANDATE_AND_UPI_CC_POWEREDBY_LOGO) : null; }
	 *
	 * public static String getUltraPoweredByUpiLogoUrl() { return
	 * otherIconMap.containsKey(ULTRA_UPI_POWEREDBY_LOGO) ?
	 * otherIconMap.get(ULTRA_UPI_POWEREDBY_LOGO) : null; }
	 */

	public static String createPoweredByUpiLogoUrl(final String logoName, final Theme theme) {
		if (StringUtils.isEmpty(logoName) || StringUtils.isBlank(logoName)) {
			return CATEGORY_LOGO_BASE_URL + (Theme.DARK.equals(theme) ? ULTRA : EMPTY_STRING) + DEFAULT_POWERED_BY_UPI
					+ DOT_PNG;
		}
		else {
			return CATEGORY_LOGO_BASE_URL + logoName + DOT_PNG;
		}
	}

	public static String getPoweredByWalletLogoUrl() {
		return otherIconMap.containsKey(WALLET_POWEREDBY_LOGO) ? otherIconMap.get(WALLET_POWEREDBY_LOGO) : null;
	}

	public static String getUltraPoweredByWalletLogoUrl() {
		return otherIconMap.containsKey(ULTRA_WALLET_POWEREDBY_LOGO) ? otherIconMap.get(ULTRA_WALLET_POWEREDBY_LOGO)
				: null;
	}

	public static String getUltraPoweredByBankLogoUrl() {
		return otherIconMap.containsKey(ULTRA_BANK_POWEREDBY_LOGO) ? otherIconMap.get(ULTRA_BANK_POWEREDBY_LOGO) : null;
	}

	public static String getPoweredByBankLogoUrl() {
		return otherIconMap.containsKey(BANK_POWEREDBY_LOGO) ? otherIconMap.get(BANK_POWEREDBY_LOGO) : null;
	}

	public static String getPaymentModeLogoUrl(final String paymentMode) {
		if (StringUtils.isBlank(paymentMode)) {
			return null;
		}
		return paymentModeMap.getOrDefault(paymentMode, null);
	}

	public static String setDefaultLogoUrlForUpiLiteP2m() {
		String defaultLogoUrl = LogoUtility.getLogo(MERCHANT_DEFAULT_LOGO, LogoType.TRANSACTION_CATEGORY_ICON);
		return defaultLogoUrl;
	}

	public static List<Logo> setLogoOrderForUpiLiteP2m(final TransformedTransactionHistoryDetail tthd) {

		List<Logo> logoOrder = new ArrayList<>();
		TransformedParticipant participant = null;

		// find merchant participant
		for (TransformedParticipant part : tthd.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(part.getTxnIndicator())
					&& Boolean.FALSE.equals(tthd.getEntityId().equals(part.getEntityId()))) {
				participant = part;
			}
		}

		if (Objects.isNull(participant)) {
			return null;
		}

		// first fetch logo from UPI doc.
		if (ObjectUtils.isNotEmpty(participant.getLogoUrl())) {
			Logo logo = new Logo(LogoOrderTypeEnum.URL, participant.getLogoUrl());
			logoOrder.add(logo);
		}

		// get logo using vpa of merchant
		Logo logo1 = LogoUtility.checkAndGetLogoByVpa(participant, tthd);
		if (Objects.nonNull(logo1)) {
			logoOrder.add(logo1);
		}

		// get UTH Category Logo w.r.t merchant data
		String logoUrlFromUthCategory = LogoUtility.uthCategoryLogo(participant);
		if (ObjectUtils.isNotEmpty(logoUrlFromUthCategory)) {
			Logo logo3 = new Logo(LogoOrderTypeEnum.URL, logoUrlFromUthCategory);
			logoOrder.add(logo3);
		}

		return logoOrder;
	}

	public static String uthCategoryLogo(final TransformedParticipant participant) {
		if (Objects.nonNull(participant.getMerchantData())
				&& !WebConstants.UTH_CATEGORY_OTHERS.equalsIgnoreCase(participant.getMerchantData().getUthCategory())
				&& StringUtils.isNotBlank(participant.getMerchantData().getUthCategory())) {
			return CACHE_HOLDER.getUthCategoriesCache().get(participant.getMerchantData().getUthCategory());
		}
		return null;
	}

	public static String mandateHistoryMerchantVpaLogo(final String vpa) {
		if (StringUtils.isBlank(vpa)) {
			return null;
		}

		return MANDATE_HISTORY_MERCHANT_VPA_LOGO_BASE_URL + vpa + DOT_PNG;
	}

}
