package com.org.panaroma.commons.aop;

import com.org.panaroma.commons.config.IndexConfig;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Parameter;
import java.util.Objects;

import static com.org.panaroma.commons.constants.WebConstants.ES_HOST_TYPE;

@Aspect
@Component
public class EsHostTypeLoggingAspect {

	@Value("${es-host-list}")
	private String primaryEsClusterHost;

	@Value("${es-v2-host-list}")
	private String secondaryEsClusterHost;

	@Value("${third-es-host-list}")
	private String tertiaryEsClusterHost;

	@Around("@annotation(logEsHostType)")
	public Object logEsHostType(ProceedingJoinPoint joinPoint, LogEsHostType logEsHostType) throws Throwable {

		// Get the IndexConfig parameter
		IndexConfig indexConfig = extractIndexConfig(joinPoint, logEsHostType);
		if (indexConfig != null) {
			setEsHostTypeInMDC(indexConfig);
		}
		return joinPoint.proceed();

	}

	private IndexConfig extractIndexConfig(ProceedingJoinPoint joinPoint, LogEsHostType logEsHostType) {
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Parameter[] parameters = signature.getMethod().getParameters();
		Object[] args = joinPoint.getArgs();

		// Find IndexConfig parameter
		for (int i = 0; i < parameters.length; i++) {
			if (parameters[i].getName().equals(logEsHostType.indexConfigParam()) && args[i] instanceof IndexConfig) {
				return (IndexConfig) args[i];
			}
		}
		return null;
	}

	/**
	 * Sets the Elasticsearch host type in MDC based on the configured host. Maps the host
	 * to one of: Primary_ES_Host, Secondary_ES_Host, Tertiary_ES_Host, or
	 * Unknown_ES_Host.
	 * @param indexConfig The index configuration containing ES hosts
	 */
	private void setEsHostTypeInMDC(final IndexConfig indexConfig) {
		String esHostType = "Unknown_ES_Host";
		if (Objects.nonNull(indexConfig) && !indexConfig.getEsHosts().isEmpty()) {
			String esConfiguredHost = indexConfig.getEsHosts().get(indexConfig.getEsHosts().size() - 1);

			if (esConfiguredHost.equalsIgnoreCase(primaryEsClusterHost)) {
				esHostType = "Primary_ES_Host";
			}
			else if (esConfiguredHost.equalsIgnoreCase(secondaryEsClusterHost)) {
				esHostType = "Secondary_ES_Host";
			}
			else if (esConfiguredHost.equalsIgnoreCase(tertiaryEsClusterHost)) {
				esHostType = "Tertiary_ES_Host";
			}
		}
		MDC.put(ES_HOST_TYPE, esHostType);
	}

}
