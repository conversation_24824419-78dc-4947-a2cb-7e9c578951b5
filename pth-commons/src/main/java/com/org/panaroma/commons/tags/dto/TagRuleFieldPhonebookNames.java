package com.org.panaroma.commons.tags.dto;

import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.tags.TagRuleTxnDto;
import lombok.AllArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@AllArgsConstructor
@ToString
public class TagRuleFieldPhonebookNames implements TagRuleField {

	private List<String> patternStrings;

	@Override
	public boolean isValidRule(TagRuleTxnDto tagRuleDto) {
		String value = tagRuleDto.getPhonebookName();
		if (StringUtils.isBlank(value)) {
			return false;
		}
		return patternStrings.stream().anyMatch(value::contains);
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		TagRuleFieldPhonebookNames that = (TagRuleFieldPhonebookNames) o;
		return Objects.equals(patternStrings, that.patternStrings);
	}

	@Override
	public int hashCode() {
		return Objects.hash(patternStrings);
	}

	public static TagRuleFieldPhonebookNames of(TagConfigurationEntity entity) {
		if (entity == null || StringUtils.isBlank(entity.getReceiverPhonebookNames())) {
			return null;
		}
		String dbFormatValue = entity.getReceiverPhonebookNames().trim();
		// sorting is necessary to avoid duplicates if they are in different order
		List<String> phonebookNames = Stream.of(dbFormatValue.split(","))
			.map(String::trim)
			.filter(StringUtils::isNotBlank)
			.sorted()
			.collect(Collectors.toList());
		return new TagRuleFieldPhonebookNames(phonebookNames);
	}

}
