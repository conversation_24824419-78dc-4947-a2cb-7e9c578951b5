package com.org.panaroma.commons.dto;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

	SAVINGS(1, "savings"), CURRENT(2, "current", Arrays.asList("ICA", "Paytm current account")), OTHER(3, "Other"),
	FD(4, "fd"), CREDITLINE(5, "CREDITLINE"), CREDITLINE01(6, "CREDITLINE01"), CREDITLINE02(7, "CREDITLINE02"),
	CREDITLINE03(8, "CREDITLINE03"), CREDITLINE04(9, "CREDITLINE04"), CREDITLINE05(10, "CREDITLINE05"),
	CREDITLINE06(11, "CREDITLINE06"), CREDITLINE07(12, "CREDITLINE07"), CREDITLINE08(13, "CREDITLINE08"),
	CREDITLINE09(14, "CREDITLINE09"), CREDITLINE10(15, "CREDITLINE10"), CL01(16, "CL01"), CL02(17, "CL02"),
	CL03(18, "CL03"), CL04(19, "CL04"), CL05(20, "CL05"), CL06(21, "CL06"), CL07(22, "CL07"), CL08(23, "CL08"),
	CL09(24, "CL09"), CL10(25, "CL10"), CL011(26, "CL011"), CL012(27, "CL012"), CL013(28, "CL013"), CL014(29, "CL014"),
	CL015(30, "CL015");

	private Integer accountTypeEnumKey;

	private String accountType;

	private List<String> viewValue;

	AccountTypeEnum(final Integer accountTypeEnumKey, final String accountType) {
		this.accountTypeEnumKey = accountTypeEnumKey;
		this.accountType = accountType;
	}

	private static Map<String, Integer> acctTypeStringToKeyMap = null;

	static {
		acctTypeStringToKeyMap = new HashMap<>();
		acctTypeStringToKeyMap.put("ICA", CURRENT.accountTypeEnumKey);
		acctTypeStringToKeyMap.put("ISA", SAVINGS.accountTypeEnumKey);
		// no savings filter currently, removing this, this will help in applying no
		// filter
	}

	public static AccountTypeEnum getAccountTypeEnumByValue(final String accountType) {
		for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
			if (accountTypeEnum.accountType.equalsIgnoreCase(accountType)) {
				return accountTypeEnum;
			}
		}
		return OTHER;
	}

	public static AccountTypeEnum getAccountTypeEnumByKey(final Integer accountType) {
		for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
			if (accountTypeEnum.accountTypeEnumKey.equals(accountType)) {
				return accountTypeEnum;
			}
		}
		return null;
	}

	public static Integer getAcctTypeEnumKeyByString(final String input) {
		if (StringUtils.isBlank(input)) {
			return null;
		}
		return acctTypeStringToKeyMap.getOrDefault(input.toUpperCase(), null);
	}

}
