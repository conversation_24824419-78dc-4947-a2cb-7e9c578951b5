package com.org.panaroma.commons.repository;

import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.enums.TagConfigurationStatusEnum;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface TagConfigurationRepo extends JpaRepository<TagConfigurationEntity, Integer> {

	List<TagConfigurationEntity> findAllByStatus(TagConfigurationStatusEnum status);

}
