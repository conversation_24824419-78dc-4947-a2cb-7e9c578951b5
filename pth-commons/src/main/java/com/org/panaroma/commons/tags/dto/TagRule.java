package com.org.panaroma.commons.tags.dto;

import com.org.panaroma.commons.tags.TagRuleTxnDto;
import lombok.Builder;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Builder
@ToString
public class TagRule implements Serializable {

	private List<TagRuleField> tagRules;

	/*
	 * This method checks if the rule is valid for the given transaction. 0, if the tagKey
	 * is not applicable (it means, either 0 field matches) greater than 0, it tells how
	 * many fields matches.
	 */
	public int isKeyValid(TagRuleTxnDto transformedTxn) {
		List<FieldMatcher> matchers = tagRules.stream()
			.map(rule -> (FieldMatcher) () -> rule.isValidRule(transformedTxn))
			.collect(java.util.stream.Collectors.toList());
		return (int) matchers.stream().filter(FieldMatcher::isValid).count();
	}

	private interface FieldMatcher {

		boolean isValid();

	}

}
