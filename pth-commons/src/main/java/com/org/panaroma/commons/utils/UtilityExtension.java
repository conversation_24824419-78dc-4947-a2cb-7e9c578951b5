package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.BankDataConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.BankDataConstants.ReversalReportCode.P2P_OUTWARD_REVERSAL;
import static com.org.panaroma.commons.constants.CartConstants.OMS_ORDER;
import static com.org.panaroma.commons.constants.CartConstants.UDF1;
import static com.org.panaroma.commons.constants.CommonsConstants.FREEFIELD19;
import static com.org.panaroma.commons.constants.CommonsConstants.NUMBER_192;
import static com.org.panaroma.commons.constants.Constants.P2P_TXN_TYPES_LIST;
import static com.org.panaroma.commons.constants.Constants.PTH;
import static com.org.panaroma.commons.constants.Constants.UNDERSCORE;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.DATE_FORMAT;
import static com.org.panaroma.commons.constants.WebConstants.*;
import static com.org.panaroma.commons.constants.WebConstants.UpiInitiationModeMap.*;
import static com.org.panaroma.commons.constants.WebConstants.UpiInitiationModeMap.INITIATION_MODE;
import static com.org.panaroma.commons.utils.PthVersionUtility.isLimitMonthsToScanInSingleApiRequestFeatureSupported;

import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnFlowTypeEnum;
import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.es.TransformedBankData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.ApiVersion;
import com.org.panaroma.commons.enums.WalletTxnTypesEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Log4j2
@Component
public class UtilityExtension implements Serializable {

	// Add netty apiName which we will migrate to tomcat in below array
	private static String[] migratedApisName = { "detail", "search", "updates", "recon", "cst", "autocomplete",
			"cstSearch", "listing", "filter" };

	public static boolean isUpiCollectOrIntentTxn(final TransformedTransactionHistoryDetail txn) {

		// Helps to check whether txn is UPI Intent or UPI Collect Txn.
		return TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& Boolean.FALSE.equals(CollectionUtils.isEmpty(txn.getContextMap()))
				&& (COLLECT.equalsIgnoreCase(txn.getContextMap().get(TXN_TYPE))
						|| INITIATION_MODE_INTENT.equals(txn.getContextMap().get(INITIATION_MODE)));
	}

	public static boolean isp2pOutwardReversalUsingPpbl(final TransformedTransactionHistoryDetail tthd) {
		return Objects.nonNull(tthd) && TransactionSource.PPBL.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& Objects.nonNull(tthd.getContextMap())
				&& P2P_OUTWARD_REVERSAL.equalsIgnoreCase(tthd.getContextMap().get(REPORT_CODE));
	}

	public static boolean isNodalAccount(final Map<String, String> contextMap) {

		if (ObjectUtils.isNotEmpty(contextMap) && contextMap.containsKey(FREEFIELD19)
				&& contextMap.get(FREEFIELD19).equals(NUMBER_192)) {
			return true;
		}
		else {
			return false;
		}
	}

	public static boolean isDebitParticipantBankDetailsPresent(final TransformedTransactionHistoryDetail tthd) {
		if (CollectionUtils.isEmpty(tthd.getParticipants())) {
			return false;
		}
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())
					&& (ObjectUtils.isEmpty(participant.getBankData())
							|| StringUtils.isBlank(participant.getBankData().getBankName())
							|| StringUtils.isBlank(participant.getBankData().getAccNumber()))) {
				return false;
			}
		}
		return true;
	}

	public static TransformedParticipant getUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd) || ObjectUtils.isEmpty(tthd.getParticipants())) {
			return null;
		}

		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (tthd.getEntityId().equals(participant.getEntityId())) {
				return participant;
			}
		}

		return null;
	}

	public static boolean isCurrentFilterNotRequired(final String apiUri) {

		// Check if the original string contains any of the substrings
		boolean isApiValidForFilter = Arrays.stream(migratedApisName).anyMatch(apiUri::contains);

		if (apiUri.contains("pth/ext") && isApiValidForFilter) {
			return false;
		}
		return true;
	}

	/**
	 * Retrieves the API name based on the provided API URL.
	 * @param apiUrl The URL of the API to extract the name from.
	 * @return The extracted API name or null if not found. Migrated api is present in the
	 * string array apiName which is initialized initially
	 */
	public static String getApiNameByApiUrl(final String apiUrl) {
		// Check if the original string contains any of the substrings
		boolean isRequestForMigratedApiUrl = Arrays.stream(migratedApisName).anyMatch(apiUrl::contains);
		// If the API URL corresponds to a migrated API then Split the apiUrl and return
		// the last segment as the API name
		if (isRequestForMigratedApiUrl) {
			String[] apiName = apiUrl.split("/");
			return apiName != null ? apiName[apiName.length - 1] : null;
		}
		// Return null if the API URL is not associated with a migrated API
		return null;
	}

	public static boolean isWallet2AccountTxn(final TransformedTransactionHistoryDetail tthd) {
		return Objects.nonNull(tthd)
				&& TransactionSource.WALLET.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& ObjectUtils.isNotEmpty(tthd.getContextMap())
				&& WalletTxnTypesEnum.WALLET_2_ACCOUNT.getWalletTxnTypeKey()
					.equals(tthd.getContextMap().get(WebConstants.WALLET_TXN_TYPE));
	}

	public static boolean bothArePaytmUser(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd) || Objects.isNull(tthd.getParticipants())) {
			return false;
		}
		boolean bothUserAreOnPaytm = true;
		/*
		 * Participant is not Paytm User if case 1 : entityId is null. case 2 : type is
		 * not USER.
		 */
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (StringUtils.isBlank(participant.getEntityId()) || Boolean.FALSE
				.equals(EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType()))) {
				bothUserAreOnPaytm = false;
				break;
			}
		}
		return bothUserAreOnPaytm;
	}

	public static boolean isSearchApiRequestWithV3OrAbove(final Map<String, String> paramMap) {
		return paramMap.containsKey(SEARCH_API_VERSION) && !Arrays.asList(ApiVersion.v1.name(), ApiVersion.v2.name())
			.contains(paramMap.get(SEARCH_API_VERSION));
	}

	public static String removeTrailingZeroes(final String s) {
		try {
			if (StringUtils.isNotBlank(s)) {
				BigDecimal value = new BigDecimal(s);
				return value.stripTrailingZeros().toPlainString();
			}
		}
		catch (NumberFormatException exec) {
			log.error("Exception while parsing string : {}", s);
		}
		return s;
	}

	// This method check if given txn is onus or not.
	public static boolean isOnusTxn(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd)) {
			TransformedParticipant merchantParticipant = Utility.getSecPartyParticipant(tthd);
			if (Objects.nonNull(merchantParticipant)) {
				MerchantTypeEnum merchantTypeEnum = Utility.getMerchantType(merchantParticipant);
				if (Objects.nonNull(merchantTypeEnum)) {
					return MerchantTypeEnum.ONUS.equals(merchantTypeEnum);
				}
			}
		}
		return false;
	}

	// This method return toll txnId if present in contextMap.
	public static String getTollTxnId(final TransformedTransactionHistoryDetail txn) {
		if (Objects.nonNull(txn) && Objects.nonNull(txn.getContextMap())
				&& txn.getContextMap().containsKey(TOLL_TRANSACTION_ID)
				&& StringUtils.isNoneEmpty(txn.getContextMap().get(TOLL_TRANSACTION_ID))) {
			return txn.getContextMap().get(TOLL_TRANSACTION_ID);
		}
		return null;
	}

	// This method return sorted participant list based on debit amount in decreasing
	// order.
	public static List<TransformedParticipant> getSortedParticipantBasedOnDebitAmt(
			final TransformedTransactionHistoryDetail txn) {
		List<TransformedParticipant> participants = null;

		if (Objects.isNull(txn) || CollectionUtils.isEmpty(txn.getParticipants())) {
			return participants;
		}

		return txn.getParticipants()
			.stream()
			.filter(x -> TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(x.getTxnIndicator()))
			.sorted(Comparator.comparingLong(TransformedParticipant::getAmount).reversed())
			.collect(Collectors.toList());
	}

	public static Double getApiVersion(final Map<String, String> paramMap) {
		try {
			return Double.parseDouble(paramMap.get(API_VERSION));
		}
		catch (NumberFormatException | NullPointerException e) {
			return 0.0;
		}
	}

	public static boolean isOnlineTxn(final TransformedTransactionHistoryDetail txn) {

		// Helps to check whether txn is UPI Intent or UPI Collect Txn.
		return TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& Boolean.FALSE.equals(CollectionUtils.isEmpty(txn.getContextMap()))
				&& (COLLECT.equalsIgnoreCase(txn.getContextMap().get(TXN_TYPE)) || (PAY
					.equalsIgnoreCase(txn.getContextMap().get(TXN_TYPE))
						&& (INITIATION_MODE_INTENT.equals(txn.getContextMap().get(INITIATION_MODE))
								|| INITIATION_MODE_UPI_MANDATE.equals(txn.getContextMap().get(INITIATION_MODE)))));
	}

	public static boolean isOnusTransaction(final TransformedTransactionHistoryDetail tthd) {
		if (isOnusTxn(tthd)) {
			return true;
		}

		if (Objects.isNull(tthd.getContextMap())) {
			return false;
		}

		return StringUtils.equalsIgnoreCase(tthd.getContextMap().get(CHANNEL_CODE), ONLINE_ONUS_NATIVE);
	}

	public static boolean isUpiOnusTxn(final TransactionHistoryDetails thd) {
		if (Objects.isNull(thd.getContextMap())) {
			return false;
		}

		return StringUtils.equalsIgnoreCase(thd.getContextMap().get(ONUS_MERCHANT_FLAG_KEY_FROM_UPI), TRUE)
				|| StringUtils.equalsIgnoreCase(thd.getContextMap().get(CHANNEL_CODE), "ONLINE_ONUS_NATIVE");
	}

	public static boolean isUpiOnusTxn(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd.getContextMap())) {
			return false;
		}

		return StringUtils.equalsIgnoreCase(tthd.getContextMap().get(ONUS_MERCHANT_FLAG_KEY_FROM_UPI), TRUE)
				|| StringUtils.equalsIgnoreCase(tthd.getContextMap().get(CHANNEL_CODE), "ONLINE_ONUS_NATIVE");
	}

	public static List<TransformedTransactionHistoryDetail> removeDuplicateDocuments(
			final List<TransformedTransactionHistoryDetail> txnsList) {
		Map<String, TransformedTransactionHistoryDetail> seen = new LinkedHashMap<>();
		for (TransformedTransactionHistoryDetail txn : txnsList) {
			try {
				// below code block is for ignoring OMS docs which are not handled on view
				// layer
				if (!CollectionUtils.isEmpty(txn.getParticipants())) {
					boolean isValid = true;
					for (TransformedParticipant participant : txn.getParticipants()) {
						if (PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem()) == null) {
							isValid = false;
							break;
						}
					}
					if (!isValid) {
						log.info("Skipping txn with txnId : {}, txnDate : {}, streamSource : {} as it is not valid",
								txn.getTxnId(), DateTimeUtility.getStringDate(txn.getTxnDate(), DATE_FORMAT),
								txn.getStreamSource());
						continue;
					}
				}
			}
			catch (Exception e) {
				log.error("Exception while checking txn validity for txnId : {}, streamSource : {}", txn.getTxnId(),
						txn.getStreamSource(), e);
			}

			String key = txn.getTxnId() + "_" + txn.getStreamSource() + "_" + txn.getEntityId();
			if (seen.containsKey(key)) {
				if (Objects.isNull(txn.getContextMap()) || !txn.getContextMap().containsKey("ifr")) {
					seen.put(key, txn); // Giving preference to txn with not
										// contextMap.ifr key
				}
			}
			else {
				seen.put(key, txn);
			}
		}
		return new ArrayList<>(seen.values());
	}

	/*
	 * This method filters the ES Docs fetched using the ifsc query & returns only the
	 * docs where last 2 digits of account number of bankData in self participant matches
	 * the last 2 digits of account number in upiIdentifier received from app
	 */
	public static List<TransformedTransactionHistoryDetail> filterEsDocsIfDataFetchedUsingIfscQueryForUpiFilter(
			final List<TransformedTransactionHistoryDetail> txnsList, final String esUpiIdentifier) {

		if (CollectionUtils.isEmpty(txnsList) || StringUtils.isBlank(esUpiIdentifier)) {
			return Collections.emptyList();
		}

		String[] upiIdentifiers = esUpiIdentifier.split(COMMA);
		Map<String, List<String>> ifscToLastTwoDigitsOfAccNumMap = new HashMap<>();
		List<String> upiCcIdentifiers = new ArrayList<>();

		for (String upiIdentifier : upiIdentifiers) {
			// We also have a case of upi cc filter & upiIdentifier in that case doesn't
			// have _ in it
			if (upiIdentifier.contains(UNDERSCORE)) {
				String[] parts = upiIdentifier.split(UNDERSCORE);
				String lastTwoDigits = parts[0].substring(2);
				String ifsc = parts[1];
				ifscToLastTwoDigitsOfAccNumMap.computeIfAbsent(ifsc, k -> new ArrayList<>()).add(lastTwoDigits);
			}
			else {
				upiCcIdentifiers.add(upiIdentifier);
			}
		}

		return txnsList.stream().filter(txn -> {

			/*
			 * Any UPI LITE txn will reach here only if user has selected upi_lite filter.
			 * So we can simply add these txns in final list
			 */
			if (UpiLiteUtility.isUpiLiteTxn(txn)) {
				return true;
			}
			TransformedParticipant participant = Utility.getSelfUpiParticipant(txn);
			if (Objects.isNull(participant)) {
				return false;
			}
			if (Objects.nonNull(participant.getCardData())
					&& StringUtils.isNotBlank(participant.getCardData().getCardNum())) {
				return upiCcIdentifiers.contains(participant.getCardData().getCardNum());
			}
			TransformedBankData bankData = participant.getBankData();
			if (Objects.isNull(bankData) || StringUtils.isBlank(bankData.getAccNumber())
					|| StringUtils.isBlank(bankData.getIfsc())
					|| !ifscToLastTwoDigitsOfAccNumMap.containsKey(bankData.getIfsc())) {
				return false;
			}
			String lastTwoDigits = bankData.getAccNumber().substring(bankData.getAccNumber().length() - 2);
			return ifscToLastTwoDigitsOfAccNumMap.get(bankData.getIfsc()).contains(lastTwoDigits);
		}).collect(Collectors.toList());
	}

	public static boolean isNumeric(String str) {
		if (str == null || str.isEmpty()) {
			return false;
		}
		for (char c : str.toCharArray()) {
			if (!Character.isDigit(c)) {
				return false;
			}
		}
		return true;
	}

	/*
	 * There are multiple methods in the code which checks for ONUS txn with different
	 * logic & every method is used at different places. This new method is created to
	 * handle all the cases
	 */
	public static boolean isOnusTxnDoc(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd)) {
			return false;
		}

		if (TransactionSource.OMS.getTransactionSourceKey().equals(tthd.getStreamSource())
				|| TransactionSource.OMS.getTransactionSourceKey().equals(tthd.getSourceSystem())) {
			return true;
		}

		/*
		 * Code for checking ONUS txn for PG documents is taken from
		 * CartUtility.isThisOmsOrder method For older ONUS txns, PG doc used to be the
		 * doc with showInListing = true, hence below code will be used to check whether
		 * the txn is ONUS or not.
		 */
		if (TransactionSource.isPgTypeSource(tthd.getStreamSource())) {

			if (Objects.nonNull(tthd.getContextMap()) && OMS_ORDER.equalsIgnoreCase(tthd.getContextMap().get(UDF1))) {
				return true;
			}

			TransformedParticipant merchantParticipant = Utility.getSecPartyParticipant(tthd);
			MerchantTypeEnum merchantTypeEnum = Utility.getMerchantType(merchantParticipant);

			return MerchantTypeEnum.ONUS.equals(merchantTypeEnum) && StringUtils.isNumeric(tthd.getOrderId());
		}
		else if (TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& Objects.nonNull(tthd.getContextMap())
				&& StringUtils.equalsIgnoreCase(tthd.getContextMap().get(CHANNEL_CODE), ONLINE_ONUS_NATIVE)) {
			/*
			 * In TPAP world as per the contract with UPI team, channelCode will always be
			 * ONLINE_ONUS_NATIVE for an ONUS txn Hence only this condition is used to
			 * check for ONUS txn
			 */
			return true;
		}
		return false;
	}

	public static TxnFlowTypeEnum getTxnFlowType(final TransformedTransactionHistoryDetail txn,
			final TransformedParticipant secondPartyParticipant) {

		if (Utility.isSelfTransferTxn(txn)) {
			return TxnFlowTypeEnum.SELF_TRANSFER_TXN;
		}

		if (isVpa2AccountTxn(txn)) {
			return TxnFlowTypeEnum.VPA_TO_ACCOUNT_TXN;
		}

		if (Objects.isNull(secondPartyParticipant)) {
			log.warn("Second party participant is null for txn : {}", txn);
			return null;
		}

		if (isP2pTransaction(txn)) {

			if (EntityTypesEnum.USER.getEntityTypeKey().equals(secondPartyParticipant.getEntityType())
					&& StringUtils.isNotBlank(secondPartyParticipant.getEntityId())) {
				return TxnFlowTypeEnum.VPA_TO_VPA_TXN;
			}

			return TxnFlowTypeEnum.VPA_TO_3P_VPA_TXN;
		}
		else if (MERCHANT_TRANSACTION_TYPES
			.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getTxnType()))) {

			if (Objects.isNull(secondPartyParticipant.getEntityId())) {
				/*
				 * paytm merchants have merchantId in entityId field For 3P merchants this
				 * field is null
				 */
				return TxnFlowTypeEnum.THIRD_PARTY_MERCHANT_TXN;
			}

			if (UtilityExtension.isOnusTxnDoc(txn)) {
				return TxnFlowTypeEnum.ONUS_MERCHANT_TXN;
			}

			if (Objects.nonNull(secondPartyParticipant.getMerchantData())
					&& StringUtils.isNotBlank(secondPartyParticipant.getMerchantData().getMerchantId())) {
				return TxnFlowTypeEnum.OFUS_MERCHANT_TXN;
			}

			log.warn("No txn flow type configured for merchant txn : {}", txn);
		}
		return null;
	}

	public static boolean isVpa2AccountTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && !CollectionUtils.isEmpty(txn.getContextMap())
				&& VPA2ACCOUNT.equalsIgnoreCase(txn.getContextMap().get(UPI_TXN_CATEGORY));
	}

	public static boolean isP2pTransaction(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd)) {
			TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getMainTxnType());
			if (P2P_TXN_TYPES_LIST.contains(txnType)) {
				return true;
			}
		}
		return false;
	}

	public static String generateRequestId() {
		return PTH + UUID.randomUUID();
	}

	/**
	 * Retrieves the UthCategoryEnum for a given transaction.
	 * @param txn The transformed transaction history detail object
	 * @return The UthCategoryEnum for the transaction, or null if not found
	 */
	public static UthCategoryEnum getUthCategoryEnum(final TransformedTransactionHistoryDetail txn) {
		// If the transaction is null, then return null
		if (Objects.isNull(txn)) {
			return null;
		}

		// Get the secondary party participant
		TransformedParticipant secPartyParticipant = Utility.getSecPartyParticipant(txn);

		// If the secondary party participant is null, then return null
		if (Objects.isNull(secPartyParticipant)) {
			return null;
		}

		// Get the UthCategoryId from the merchant data because we set UthCategoryId in
		// merchant data for P2M Txns
		String uthCategoryId = null;
		if (Objects.nonNull(secPartyParticipant.getMerchantData())
				&& StringUtils.isNotBlank(secPartyParticipant.getMerchantData().getUthCategory())) {
			uthCategoryId = secPartyParticipant.getMerchantData().getUthCategory();
		}

		// Get the UthCategoryEnum from the UthCategoryId
		UthCategoryEnum uthCategoryEnum = UthCategoryEnum.getEnumFromUthCategoryId(uthCategoryId);

		// Return the UthCategoryEnum
		return uthCategoryEnum;
	}

	public static boolean isLimitMonthsToScanInSingleApiRequestFeatureApplicable(
			final Boolean isLimitMonthsToScanInSingleApiRequestFeatureEnabled) {
		return Boolean.TRUE.equals(isLimitMonthsToScanInSingleApiRequestFeatureEnabled)
				&& isLimitMonthsToScanInSingleApiRequestFeatureSupported();
	}

}