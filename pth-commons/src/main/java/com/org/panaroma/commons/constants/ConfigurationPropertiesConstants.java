package com.org.panaroma.commons.constants;

import static com.org.panaroma.commons.constants.WebConstants.AWS_ES_FROM_DATE;
import static com.org.panaroma.commons.constants.WebConstants.FROM_DATE_LISTING_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.SECOND_ES_FROM_DATE;

import java.util.Arrays;
import java.util.List;

public class ConfigurationPropertiesConstants {

	public ConfigurationPropertiesConstants() {
		throw new RuntimeException("Can't make object of this class");
	}

	public static class WebApiV1Constants {

		public static final String TAG_FEATURE_ENABLE = "tagFeatureEnable";

		public static final String INVALID_TOKEN_EXCEPTION_REQUIRED = "oauth.invalid.token.exception.response.enable";

		public static final String REPEAT_PAYMENT_LEGACY_IMPLEMENTATION_ENABLED = "repeatPayment.legacyImplementationEnabled";

		public static final String REPEAT_PAYMENT_VPA_BASED_FOR_PAYTM_QR_MERCHANTS_ENABLE = "repeatPayment.vpaBasedForPaytmQrMerchantsEnable";

		public static final String REPEAT_PAYMENT_BLOCKING_PAYTM_MERCHANT_VPA_PREFIX_FOR_VPA_BASED_REPEAT_PAYMENT = "repeatPayment.blockingPaytmMerchantVpaPrefixForVpaBasedRepeatPayment";

		public static final String MINIMUM_AMOUNT_REQUIRED = "min-amt-req-to-show-other-party-info-in-outward-txn-in-paisa";

		public static final String STATUS_WISE_CUSTOM_MESSAGE_ENABLED = "status-wise-custom-message-enabled-in-detail-page";

		public static final String SHOW_TAG_ENABLE = "showTagEnable";

		public static final String TAGS_UPDATION_ENABLE = "tagsUpdationEnable";

		public static final String ENABLE_DEEPLINK_WITH_MOBILE_NO_FOR_P2P_TXN = "cta.repeatPayment.use-mobileNo-deeplink-for-upi-p2p-txns";

		public static final String UPI_CC_EMI_CTA_ENABLE = "upi.cc.emi.cta.enable";

		public static final String UPI_CC_EMI_CREDIT_CARD_CYCLE_DAYS = "upi.cc.emi.credit.card.cycle.days";

		public static final String MIN_AMT_LIMIT_IN_PAISA_TO_SHOW_CONVERT_EMI_CTA = "min.amt.limit.in.paisa.to.show.convert.emi.cta";

		public static final String FUZZY_SERACH_REQUIRED = "fuzzy.search.required.for.autoComplete.api";

		public static final String USE_TOKEN_INFO_API = "use-tokenInfo-api";

		public static final String USE_OF_CUSTOMER_CREATION_DATE_ENABLED = "isUseOfCustomerCreationDateEnabled";

		public static final String POSTPAID_VERTICAL_ID = "postpaid.vertical.id";

		public static final String CHAT_PROFILE_CTA_FOR_ONUS_ENABLED = "chat.profile.cta.for.onus.enabled";

		public static final String CHAT_PROFILE_CTA_FOR_NON_ONUS_ENABLED = "chat.profile.cta.for.non.onus.enabled";

		public static final String ANALYTICS_FROM_DATE = "analytics-from-date";

		public static final String TXN_STREAM_LAG_BASED_TXN_DETAILS_REJECTION_ENABLED = "txn.stream.lag.based.txn.details.rejections.enabled";

		public static final String TXN_DETAILS_SOURCE_TXN_STREAM_LAG_REJECTION_BUFFER_SECONDS = "txn.details.source.txn.stream.max.lag.rejection.buffer.seconds";

		public static final String TXN_STREAM_LAG_BASED_TRACKING_FEATURE_ENABLED = "txn.stream.lag.based.tracking.feature.enabled";

		public static final String APP_CACHE_INVALIDATE_VERSION = "appSideCache.invalidateVersion";

		public static final String APP_CACHE_LATEST_INVALIDATE_VERSION = "appSideCache.latestInvalidateVersion";

		public static final String APP_CACHE_INVALIDATE_VERSION_ENABLED = "appSideCache.invalidateVersion.enabled";

		public static final String MERCHANT_TYPE_CHANGE_BASED_ON_OMS_ORDER_LOGIC_ENABLE = "merchant.type.change.based.on.oms.order.logic.enable";

		public static final String ENABLE_DEAF_TXN_FAQ_CTA = "enable-deaf-txn-faq-cta";

		public static final String DEEPLINK_DEAF_TXN_FAQ_CTA = "deepLink.deaf.txn.faq.cta";

		public static final String IS_SEARCHABLE_STRING_ENABLE = "isSearchableStringEnable";

		public static final String SET_MCC_CODE_IN_RESPONSE = "setMccCodeInResponse";

		public static final String BLACKLISTED_APIS_FOR_RATE_LIMIT = "blacklisted.apis.for.rate.limit";

		public static final String BLACKLISTED_APIS_FOR_FILTERS = "blacklisted.apis.for.filters";

		public static final String TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_LIST = "txn.details.repeat.payment.cta.disabled.txn.status.list";

		public static final String TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_FAILURE_ERROR_CODES_LIST = "txn.details.repeat.payment.cta.disabled.txn.status.failure.error.codes.list";

		public static final String SUFFIX_FOR_NUMBER_OF_DAYS_PROPERTIES = "-in-days";

		public static final List<String> DEFAULT_ELIGIBLE_PROPERTIES_FOR_NUMBER_OF_DAYS_CONVERSION = Arrays
			.asList(FROM_DATE_LISTING_FILTER, AWS_ES_FROM_DATE, SECOND_ES_FROM_DATE);

		public static final String SUFFIX_FOR_GLOBAL_RATE_LIMIT = "-globalRateLimit";

		public static final String IS_GLOBAL_RATE_LIMIT_ENABLED = "globalRateLimitEnabled";

		public static final String TXN_DESCRIPTION_ENABLED_FOR_UPI_TXNS = "txn.description.enabled.for.upi.txns";

	}

	public static class WebApiV2Constants {

		public static final String ANALYTICS_FROM_MONTH = "analytics-from-month";

		public static final String REWIND_META_DATA_CACHE_ENABLED = "rewind.meta.data.cache.enabled";

		public static final String REWIND_TXN_ANALYTICS_DATA_CACHE_ENABLED = "rewind.txn.analytics.data.cache.enabled";

		public static final String REWIND_API_AUTH_SKIPPING_ENABLED = "rewind.api.auth.skipping.enabled";

		public static final String RECAP_ES_INDEXING_ENABLED = "recap.es.indexing.enabled";

		public static final String RECAP_CACHE_WRITE_ENABLED = "recap.cache.write.enabled";

		public static final String PTL_START_API_VERSION = "ptl.apiVersion.start";

		public static final String DETAIL_BLACKLISTED_STREAM_SOURCE = "detail.blacklisted.stream.source";

		public static final String LIST_OF_CTA_TYPE_ENABLED_FOR_DETAIL_PAGE = "detailPage.listOfCtaType.enabled";

		public static final String DISABLE_REPEAT_PAYMENT_CTA_VPA_LIST_P2P_INWARD_PAYOUT = "disable.repeat.payment.cta.vpa.list.p2p.inward.payout";

		public static final String LISTING_DESCRIPTION_CTA_FOR_RECURRING_MANDATE_ENABLED = "listing.description.cta.for.recurring.mandate.enabled";

		public static final String LISTING_DESCRIPTION_CTA_FOR_IPO_MANDATE_ENABLED = "listing.description.cta.for.ipo.mandate.enabled";

		public static final String DETAIL_DESCRIPTION_CTA_FOR_RECURRING_MANDATE_ENABLED = "detail.description.cta.for.recurring.mandate.enabled";

		public static final String DETAIL_DESCRIPTION_CTA_FOR_IPO_MANDATE_ENABLED = "detail.description.cta.for.ipo.mandate.enabled";

		public static final String TOOGLE_VISIBILITY_TXN_STATUS_DISABLED = "toggleVisibility.feature.txnStatus.not.supported.list";

		public static final String TOOGLE_VISIBILITY_TXN_TYPE_DISABLED = "toggleVisibility.feature.txnType.not.supported.list";

		public static final String TOOGLE_VISIBILITY_FOR_ONUS_TXN_ENABLED = "toggleVisibility.feature.forOnusTxn.isSupported";

		public static final String MAX_ALLOWED_DAY_DIFF_FOR_TOGGLE_VISIBILITY = "toggleVisibility.feature.max.allowed.day.diff.supported";

		public static final String JWT_VERFICATION_SUPPORTED_CLIENTS = "jwt.verification.supported.clients";

	}

	public static class ReconApi {

		public static final String APP_CACHE_RECON_ENABLED = "appCacheRecon.enabled";

		public static final String APP_CACHE_RECON_FROM_DATE_GAP = "appCacheRecon.fromDateGap";

		public static final String INVALIDATE_STORED_DATA_FLAG_ENABLE_FOR_RECON = "appCacheRecon.invalidateStoredDataFlag.enable";

	}

	public static class RateLimitConstants {

		// per hour rate limit based token
		public static final String UTH_USER_RATE_LIMIT_PER_HOUR = "uth.user.rate.limit.per.hour";

		// per minute rate limit based on token
		public static final String UTH_USER_DAILY_RATE_LIMIT_THRESHOLD = "uth.user.daily.rate.limit.threshold";

		// default algo is sha256
		public static final String UTH_USER_RATE_LIMIT_HASH_ALGO = "uth.user.rate.limit.hash.algo";

		// true if rate limit else false default value is true
		public static final String UTH_USER_RATE_LIMIT_ENABLE = "uth.user.rate.limit.enable";

		public static final String UTH_USER_DAILY_RATE_LIMIT_ENABLE = "uth.user.daily.rate.limit.enable";

		public static final String UTH_USER_RATE_LIMIT_APIS_BLACKLIST = "uth.user.rate.limit.apis.blacklisted";

	}

	public static class DwhProperties {

		public static final String UTH_ANALYTICS_SEND_TO_KAFKA = "uth.analytics.send.to.kafka";

	}

	public static class CircuitBreakerProperties {

		// "{\"refreshPeriod\":\"5\",\"limit\":\"300\"}
		public static final String UTH_DOWNSTREAM_AUTH_CIRCUIT_BREAKER_CONFIG = "uth.downstream.auth.circuit.breaker.config";

		public static final String UTH_DOWNSTREAM_AUTH_CIRCUIT_BREAKER_ENABLE = "uth.downstream.auth.circuit.breaker.enable";

		public static final String UTH_DOWNSTREAM_AUTH_CIRCUIT_BREAKER_FAILURE_HTTPCODES = "uth.downstream.auth.circuit.breaker.failure.httpcodes";

		public static final String UTH_DOWNSTREAM_AUTH_CIRCUIT_BREAKER_FAILURE_CLASSES = "uth.downstream.auth.circuit.breaker.failure.classes";

	}

	public static class UthDownStreamName {

		public static final String AUTH_DOWNSTREAM_NAME = "auth";

		public static final String USE_BANK_OAUTH = "use-bank-oauth";

	}

	public static class ServiceDisableConstants {

		public static final String RECENT_TXNS_DISABLED = "recentTxns.disabled";

		public static final String TAGGING_USER_TAGS_SUMMARY_DISABLED = "tagging.userTagsSummary.disabled";

		public static final String TAGGING_TAG_SUMMARY_DISABLED = "tagging.tagSummary.disabled";

	}

	public static class PoweredByLogoConstant {

		public static final String POWERED_BY_UPI_LOGO_LIST = "poweredBy.upiLogo.list";

	}

	public static class LoggingRqRsConstants {

		public static final String BLACKLISTED_APIS_FOR_REQUEST_RESPONSE_LOGGING = "blacklisted.apis.for.rq.rs.logging";

		public static final String FIELDS_TO_BE_MASKED_IN_REQUEST_RESPONSE_LOGGING = "fields.to.be.masked.in.rq.rs.logging";

		public static final String MASKING_PATTERN = "******";

	}

	public static class ApiUrlsInfo {

		public static final String PTH_UPDATES_API_URL = "pth.updates.api.url";

		public static final String PTH_LISTING_URL = "pth.listing.url";

		public static final String PTH_DETAIL_URL = "pth.detail.url";

		public static final String PTH_RECON_API_URL = "pth.recon.api.url";

		public static final String PTH_GET_TAGS_API_URL = "pth.get.tags.api.url";

		public static final String PTH_UPDATE_TAGS_API_URL = "pth.update.tags.api.url";

		public static final String PTH_BG_LISTING_API_URL = "pth.bg.listing.api.url";

		public static final String PTH_MANDATE_HISTORY_API_URL = "pth.mandate.history.api.url";

		public static final String PTH_TOGGLE_VISIBILITY_API_URL = "pth.toggle.visibility.api.url";

		public static final String PTH_SEARCH_FILTER_API_URL = "pth.search.filter.api.url";

		public static final String PTH_UPI_PASSBOOK_API_URL = "pth.upi.passbook.api.url";

		public static final String PTH_UPI_LITE_PASSBOOK_API_URL = "pth.upi.lite.passbook.api.url";

		public static final String PTH_UPI_CC_PASSBOOK_API_URL = "pth.upi.cc.passbook.api.url";

		public static final String PTH_USER_TAGS_SUMMARY_API_URL = "pth.user.tags.summary.api.url";

		public static final String PTH_TAG_SUMMARY_API_URL = "pth.tag.summary.api.url";

		// V4 Constants
		public static final String PTH_UPDATES_API_URL_V4 = "pth.updates.api.url.v4";

		public static final String PTH_LISTING_URL_V4 = "pth.listing.url.v4";

		public static final String PTH_DETAIL_URL_V4 = "pth.detail.url.v4";

		public static final String PTH_RECON_API_URL_V4 = "pth.recon.api.url.v4";

		public static final String PTH_GET_TAGS_API_URL_V4 = "pth.get.tags.api.url.v4";

		public static final String PTH_UPDATE_TAGS_API_URL_V4 = "pth.update.tags.api.url.v4";

		public static final String PTH_BG_LISTING_API_URL_V4 = "pth.bg.listing.api.url.v4";

		public static final String PTH_MANDATE_HISTORY_API_URL_V4 = "pth.mandate.history.api.url.v4";

		public static final String PTH_TOGGLE_VISIBILITY_API_URL_V4 = "pth.toggle.visibility.api.url.v4";

		public static final String PTH_SEARCH_FILTER_API_URL_V4 = "pth.search.filter.api.url.v4";

		public static final String PTH_UPI_PASSBOOK_API_URL_V4 = "pth.upi.passbook.api.url.v4";

		public static final String PTH_UPI_LITE_PASSBOOK_API_URL_V4 = "pth.upi.lite.passbook.api.url.v4";

		public static final String PTH_UPI_CC_PASSBOOK_API_URL_V4 = "pth.upi.cc.passbook.api.url.v4";

		public static final String PTH_USER_TAGS_SUMMARY_API_URL_V4 = "pth.user.tags.summary.api.url.v4";

		public static final String PTH_TAG_SUMMARY_API_URL_V4 = "pth.tag.summary.api.url.v4";

	}

	public static class TagFeatureConstants {

		public static final String TAGGING_USE_SECONDARY_ES_FOR_USER_TAGS = "tagging.use.secondary.es.for.user.tags";

		public static final String TAGGING_TOTAL_TXNS_FOR_CREATING_USER_TAGS = "tagging.total.txns.for.creating.user.tags.from.es";

		public static final String TAGGING_NO_OF_DAYS_OLDER_TXNS_FOR_USER_TAGS_FROM_ES = "tagging.no.of.days.older.txns.for.user.tags.from.es";

		public static final String MAX_TAG_COUNT = "tagging.max.tags.count.per.txn";

		public static final String TAG_MAX_LENGTH = "tagging.tag.max.length";

		public static final String TAG_MIN_LENGTH = "tagging.tag.min.length";

		// User tags limit in get tags v1 response
		public static final String USER_TAG_LIMIT = "tagging.user.tags.limit";

		public static final String USER_TAG_COUNT_IN_SUGGESTED_TAGS = "tagging.user.tags.count.in.suggested.tags";

		public static final String SUGGESTED_TAGS_MAX_COUNT = "tagging.suggested.tags.max.count";

		public static final String SEARCH_SUPPORTED_TAGS_MAX_COUNT = "tagging.search.supported.tags.max.count";

		public static final String AUTO_TAGGING_FEATURE_ROLLOUT = "autoTaggingFeature";

		public static final String USE_SECONDARY_ES_FOR_TAGS_USER_SUMMARY = "tagging.user.tags.summary.use.secondary.es";

		public static final String USE_SECONDARY_ES_FOR_TAG_SUMMARY = "tagging.tag.summary.use.secondary.es";

		public static final String USER_TAGS_SUMMARY_FROM_DATE_IN_DAYS = "tagging.user.tags.summary.from.date.in.days";

		public static final String USER_TAGS_SUMMARY_PAGE_SIZE = "tagging.user.tags.summary.page.size";

		public static final String MAX_TAGS_SIZE_FOR_USER_TAGS_SUMMARY = "tagging.user.tags.summary.max.tags.size";

		public static final String USER_TAGS_SUMMARY_EXCLUDE_HIDDEN_TXNS = "tagging.user.tags.summary.exclude.hidden.txns";

		public static final String TAG_SUMMARY_MONTH_FROM_USING_UTH_VISIBLE_TXN_INDICATOR = "tagging.month.from.using.uth.visible.txn.indicator.for.aggregation";

	}

}
