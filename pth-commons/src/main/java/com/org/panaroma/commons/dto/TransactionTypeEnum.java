package com.org.panaroma.commons.dto;

import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransactionTypeEnum {

	P2P_OUTWARD(1, "P2P_OUTWARD", "P2P", "P2P"), P2P_OUTWARD_REVERSAL(2, "P2P_OUTWARD_REVERSAL", "P2P", "P2P"),
	P2P_INWARD(3, "P2P_INWARD", "P2P", "P2P"), P2M(4, "P2M", "P2M", "P2M"), P2M_REFUND(5, "P2M_REFUND", "P2M", "M2P"),
	P2P2M(6, "P2P2M", "", ""), P2P2M_REFUND(7, "P2P2M_REFUND", "", ""), ADD_MONEY(8, "ADD_MONEY", "", "P2M"),
	CASHBACK_RECEIVED(9, "CASHBACK_RECEIVED", "", "M2P"), OTHER_CREDIT(10, "OTHER_CREDIT", "", ""),
	OTHER_DEBIT(11, "OTHER_DEBIT", "", ""), ON_HOLD(12, "ON_HOLD", "", ""), RELEASED(13, "RELEASED", "", ""),
	ADD_AND_PAY(14, "ADD_AND_PAY", "", "P2M"), ADD_MONEY_TO_BANK(15, "ADD_MONEY_TO_BANK", "", ""),
	WALLET_SETTLEMENT(16, "WALLET_SETTLEMENT", "", ""),
	P2P_UPI_TO_WALLET_OUTWARD(17, "P2P_UPI_TO_WALLET_OUTWARD", "P2P", "P2P"),
	P2P_UPI_TO_WALLET_INWARD(18, "P2P_UPI_TO_WALLET_INWARD", "P2P", ""),
	PPBL_TRANSACTION(19, "PPBL_TRANSACTION", "", ""), P2P_INWARD_REVERSAL(20, "P2P_INWARD_REVERSAL", "P2P", ""),
	INTEREST_CREDIT(21, "INTEREST_CREDIT", "", ""), CASH_WITHDRAWAL(22, "CASH_WITHDRAWAL", "", ""),
	CASH_WITHDRAWAL_REVERSAL(23, "CASH_WITHDRAWAL_REVERSAL", "", ""), CASH_DEPOSIT(24, "CASH_DEPOSIT", "", ""),
	SALARY_CREDIT(25, "SALARY_CREDIT", "", ""), STANDING_INSTRUCTION(26, "STANDING_INSTRUCTION", "", ""),
	CHARGES(27, "CHARGES", "", ""), CHARGES_REVERSAL(28, "CHARGES_REVERSAL", "", ""),
	COMPENSATION(29, "COMPENSATION", "", ""), CHEQUE_DEBIT(30, "CHEQUE_DEBIT", "", ""),
	CHEQUE_CREDIT(31, "CHEQUE_CREDIT", "", ""), CHEQUE_DEBIT_REVERSAL(32, "CHEQUE_DEBIT_REVERSAL", "", ""),
	DEPOSIT_CREATED(33, "DEPOSIT_CREATED", "", ""), DEPOSIT_CREATED_REVERSAL(34, "DEPOSIT_CREATED_REVERSAL", "", ""),
	DEPOSIT_REDEEMED(35, "DEPOSIT_REDEEMED", "", ""), NACH_CREDIT(36, "NACH_CREDIT", "", ""),
	NACH_DEBIT(37, "NACH_DEBIT", "", ""), NACH_CREDIT_REVERSAL(38, "NACH_CREDIT_REVERSAL", "", ""),
	NACH_DEBIT_REVERSAL(39, "NACH_DEBIT_REVERSAL", "", ""),
	P2P_INWARD_REMITTANCE(40, "P2P_INWARD_REMITTANCE", "P2P", "P2P"),
	P2P_OUTWARD_REMITTANCE(41, "P2P_OUTWARD_REMITTANCE", "P2P", "P2P"),
	P2P_OUTWARD_REMITTANCE_REFUND(42, "P2P_OUTWARD_REMITTANCE_REFUND", "P2P", "P2P"),
	P2P2M_INWARD(43, "P2P2M_INWARD", "", ""), P2P2M_OUTWARD(44, "P2P2M_OUTWARD", "", ""),
	DEPOSIT_INTEREST(48, "DEPOSIT_INTEREST", "", ""), IPO_MANDATE(45, "IPO_MANDATE", "", "P2M"),
	ONETIME_MANDATE(46, "not being used", "", "P2M"), RECURRING_MANDATE(47, "RECURRING_MANDATE", "", "P2M"),
	WALLET_UPI_DEBIT_P2M(49, "WALLET_UPI_DEBIT_P2M", "P2M", ""),
	WALLET_UPI_DEBIT_P2P(50, "WALLET_UPI_DEBIT_P2P", "P2P", ""), UPI_WALLET_CREDIT(51, "UPI_WALLET_CREDIT", "", ""),
	WALLET_UPI_DEBIT_REVERSAL(52, "WALLET_UPI_DEBIT_REVERSAL", "", ""),
	UPI_WALLET_CREDIT_REVERSAL(53, "UPI_WALLET_CREDIT_REVERSAL", "", ""), FD_RECOVERY(54, "FD_RECOVERY", "", ""),
	P2M_INTERNATIONAL(55, "P2M_INTERNATIONAL", "P2M", "P2M"),
	P2M_REVERSAL_INTERNATIONAL(56, "P2M_REVERSAL_INTERNATIONAL", "P2M", "M2P"),
	ADD_MONEY_TO_UPI_LITE(57, "ADD_MONEY_TO_UPI_LITE", "", "P2M"),
	DEACTIVATION_OF_UPI_LITE(58, "DEACTIVATION_OF_UPI_LITE", "", "M2P"), CHARGES_REFUND(59, "CHARGES_REFUND", "", ""),
	ADD_MONEY_REFUND(60, "ADD_MONEY_REFUND", "", "M2P"), SYSTEM_DEBIT(61, "SYSTEM_DEBIT", "", ""),
	LITE_TOPUP_MANDATE(62, "LITE_TOPUP_MANDATE", "", "P2M"), SBMD_MANDATE(63, "SBMD_MANDATE", "", "P2M"),
	/*
	 * JIRA ID : https://jira.paytm.in/browse/PTH-930 ONETIME_MANDATE value is not being
	 * used because UPI is sending ONE_TIME_MANDATE & we can't change the existing enum
	 * name because of incompatibility with older kafka schema registory versions. So
	 * added new enum value
	 */
	ONE_TIME_MANDATE(64, "ONE_TIME_MANDATE", "", "P2M"), P2P_INWARD_3P_APP(65, "P2P_INWARD_3P_APP", "", "P2P");

	private final Integer transactionTypeKey;

	private final String transactionType;

	private final String promoTxnType;

	private final String cstTxnTypeCategory;

	private static List<TransactionTypeEnum> P2P_TXN_TYPES_LIST = Arrays.asList(TransactionTypeEnum.P2P_OUTWARD,
			TransactionTypeEnum.P2P_INWARD, TransactionTypeEnum.P2P_INWARD_3P_APP);

	private static List<TransactionTypeEnum> P2P_OUTWARD_TXN_TYPES_LIST = Arrays
		.asList(TransactionTypeEnum.P2P_OUTWARD);

	private static List<TransactionTypeEnum> P2P_INWARD_TXN_TYPES_LIST = Arrays.asList(TransactionTypeEnum.P2P_INWARD,
			TransactionTypeEnum.P2P_INWARD_3P_APP);

	// we consume only these types in p2p txn now.

	public static TransactionTypeEnum getTransactionTypeEnumByName(final String transactionType) {
		for (TransactionTypeEnum transactionTypeEnum : TransactionTypeEnum.values()) {
			if (transactionTypeEnum.transactionType.equalsIgnoreCase(transactionType)) {
				return transactionTypeEnum;
			}
		}
		return null;
	}

	public static TransactionTypeEnum getTransactionTypeEnumByKey(final Integer transactionTypeKey) {
		for (TransactionTypeEnum transactionTypeEnum : TransactionTypeEnum.values()) {
			if (transactionTypeEnum.transactionTypeKey.equals(transactionTypeKey)) {
				return transactionTypeEnum;
			}
		}
		return null;
	}

	public static boolean isP2PTxnType(final TransactionTypeEnum transactionTypeEnum) {
		return P2P_TXN_TYPES_LIST.contains(transactionTypeEnum);
	}

	public static boolean isP2pInwardTxnType(final TransactionTypeEnum txnTypeEnum) {
		return P2P_INWARD_TXN_TYPES_LIST.contains(txnTypeEnum);
	}

	public static boolean isP2pOutwardTxnType(final TransactionTypeEnum txnTypeEnum) {
		return P2P_OUTWARD_TXN_TYPES_LIST.contains(txnTypeEnum);
	}

	public static boolean isCashbackTxnType(final TransactionTypeEnum transactionTypeEnum) {
		return TransactionTypeEnum.CASHBACK_RECEIVED.equals(transactionTypeEnum);
	}

}
