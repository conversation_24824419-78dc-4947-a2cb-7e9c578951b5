package com.org.panaroma.commons.dto.mandate;

import static com.org.panaroma.commons.constants.Constants.PIPE_SYMBOL;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.MANDATE_INFO_INDEX_NAME_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.MANDATE_TERMINAL_STATUS;
import static com.org.panaroma.commons.utils.ToStringUtility.getMaskedStringValue;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.org.panaroma.commons.enums.MandateStatusEnum;
import java.io.Serializable;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.avro.reflect.Nullable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class MandateInfoData extends MandateBaseDto implements Serializable {

	@Nullable
	@JsonProperty("entityId")
	String entityId;

	/*
	 * We can plan to populate this field in future if we see a chance to power mandate
	 * listing & if there a case that some mandates doesn't needs to be shown
	 *
	 * @Nullable
	 *
	 * @JsonProperty("showInListing") Boolean showInListing;
	 */

	/*
	 * We can plan to populate this field in future if we plan to keep data in db when
	 * incomplete data is received from mandate team
	 *
	 * @Nullable
	 *
	 * @JsonProperty("isVisible") Boolean isVisible;
	 */

	@Nullable
	@JsonProperty("amount")
	Double amount;

	@Nullable
	@JsonProperty("type")
	Integer type;

	@Nullable
	@JsonProperty("status")
	Integer status;

	@Nullable
	@JsonProperty("umn")
	String umn;

	@Nullable
	@JsonProperty("mandateMetaData")
	MandateMetaData mandateMetaData;

	@Nullable
	@JsonProperty("txnDate")
	Long txnDate;

	@Nullable
	@JsonProperty("lastActivityTxnDate")
	Long lastActivityTxnDate;

	@Nullable
	@JsonProperty("updatedDate")
	Long updatedDate;

	@Nullable
	@JsonProperty("docCreatedDate")
	Long docCreatedDate;

	@Nullable
	@JsonProperty("docUpdatedDate")
	Long docUpdatedDate;

	@Nullable
	@JsonProperty("createTxnId")
	String createTxnId;

	@Nullable
	@JsonProperty("payerData")
	ParticipantData payerData;

	@Nullable
	@JsonProperty("payeeData")
	ParticipantData payeeData;

	@Nullable
	@JsonProperty("missingDataIdentifier")
	Set<Integer> missingDataIdentifier;

	@Nullable
	@JsonProperty("createEventType")
	Integer createEventType;

	public String docId() {
		return entityId + PIPE_SYMBOL + umn;
	}

	@Override
	public String getCacheKey() {
		return this.getEntityId() + PIPE_SYMBOL + this.getUmn();
	}

	@Override
	public void setDatesInMandateDoc(final MandateBaseDto storedMandateDoc, final Long currentTime) {
		MandateInfoData storedMandateInfoDoc = (MandateInfoData) storedMandateDoc;
		if (Objects.isNull(storedMandateDoc)) {
			this.setDocCreatedDate(currentTime);
		}
		else {
			this.setDocCreatedDate(storedMandateInfoDoc.getDocCreatedDate());
		}
		this.setDocUpdatedDate(currentTime);
	}

	@Override
	public boolean isSourceDtoUpdated(final MandateBaseDto storedMandateDoc) {
		if (Objects.isNull(storedMandateDoc)) {
			return true;
		}
		MandateInfoData stored = (MandateInfoData) storedMandateDoc;
		MandateStatusEnum sourceStatus = MandateStatusEnum.getStatusEnumByKey(this.getStatus());
		MandateStatusEnum storedStatus = MandateStatusEnum.getStatusEnumByKey(stored.getStatus());
		if (Objects.nonNull(sourceStatus) && sourceStatus.equals(storedStatus)) {
			return this.getUpdatedDate() >= stored.getUpdatedDate();
		}
		else if (MANDATE_TERMINAL_STATUS.contains(sourceStatus) && MANDATE_TERMINAL_STATUS.contains(storedStatus)) {
			return this.getUpdatedDate() >= stored.getUpdatedDate();
		}
		else {
			return MANDATE_TERMINAL_STATUS.contains(sourceStatus);
		}
	}

	@Override
	public String getIndexNameIdentifier() {
		return MANDATE_INFO_INDEX_NAME_IDENTIFIER;
	}

	// txnId only exists in MandateActivity object
	@Override
	public String getTxnId() {
		return null;
	}

	@Override
	public TypeReference getObjectType() {
		return new TypeReference<MandateInfoData>() {
		};
	}

	@Override
	public String toString() {
		return "MandateInfoData{" + "entityId='" + entityId + '\'' + ", amount=" + getMaskedStringValue(amount)
				+ ", type=" + type + ", status=" + status + ", umn='" + umn + '\'' + ", mandateMetaData="
				+ mandateMetaData + ", txnDate=" + txnDate + ", updatedDate=" + updatedDate + ", docCreatedDate="
				+ docCreatedDate + ", docUpdatedDate=" + docUpdatedDate + ", payerData=" + payerData
				+ ", createEventType=" + createEventType + ", payeeData=" + payeeData + ", createTxnId='" + createTxnId
				+ '\'' + ", missingDataIdentifier=" + missingDataIdentifier + '\'' + ", metaDataMap=" + getMetaDataMap()
				+ '\'' + '}';
	}

}
