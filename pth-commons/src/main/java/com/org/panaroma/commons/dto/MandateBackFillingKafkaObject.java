package com.org.panaroma.commons.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MandateBackFillingKafkaObject {

	private SearchContext searchContext;

	private PaginationParams paginationParams;

	@Override
	public String toString() {
		return "MandateBackFillingKafkaObject{" + "searchContext=" + searchContext + ", paginationParams="
				+ paginationParams + '}';
	}

}
