package com.org.panaroma.commons.cache.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import java.io.Serializable;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListingSpecificCacheData implements Serializable {

	// either of the below 2 fields will be null at any point of time

	private RepoResponseSearchApiDto listingData;

	// private NonTransactingUserCacheData nonTransactingUserCacheData;
	private Boolean isNonTransactingUser;

	// denotes the time when this particular data was saved in cache
	private Long createdDate;

	// denotes the time when this particular data was last updated in cache
	private Long updatedDate;

	private Map<String, String> metaInfo;

}