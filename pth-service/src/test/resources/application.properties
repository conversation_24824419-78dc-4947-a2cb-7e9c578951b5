server.port     = 10030
build.timestamp = @timestamp@
app.name        = @project.artifactId@
app.version     = @project.version@
git.commit.id   = 122
git.branch      = master
logging.config  = classpath:log4j2-local.json

elastic-search-index                  = payment_history_alias
elastic-search-index-prefix = payment-history-
uth.pms.txn.history.secret.key                     = pmstest
ocl.oauth.service.base.url                = https://accounts-plus-proxy.paytm.com
ocl.oauth.client.id                       = jbjhb
ocl.oauth.client.secret                   = hjbjh
use-bank-oauth                            = true

es-host-list                          = 127.0.0.1
es-port                               = 9200
es-socket-timeout                     = 2000
es-max-retry-timeout                  = 2000
es-connect-timeout                    = 1000
es-connect-request-timeout            = 1000

#V2 ES Configuration
es-v2-host-list                                    = 127.0.0.1
es-v2-port                                         = 9200

#Mandate es configuration
mandate-es-host-list = 127.0.0.1

server.tomcat.accesslog.enabled                    = true
server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
server.tomcat.accesslog.directory                  = /log/payment-transaction-history-service
server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
server.tomcat.accesslog.rotate                     = true
server.tomcat.accesslog.rename-on-rotate           = true
server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
server.tomcat.accesslog.buffered                   = false
server.tomcat.accesslog.request-attributes-enabled = true

spend_analytics.status.table = analytic_status_data
shedlock.table = Shedlock
txn.tags.table                                 = txn_tag_data-uth
user.tags.table                                = user_tag_data
userId.updatedTimeStamp.index                  = userId-updatedTimeStamp-index
sysyem.tags.table                              = system_tag_data
tagging.max.tags.count.per.txn                              = 1
tagging.tag.max.length                                     = 20
tagging.tag.min.length                                     = 3
tagging.user.tags.limit                                     = 100
tagging.user.tags.count.in.suggested.tags                  = 2
tagging.suggested.tags.max.count                          = 4
tagging.search.supported.tags.max.count                   = 100

#this is month from this month new txn indicator field will be used It's in MM-YYYY format JiraId - PTH-1000
tagging.month.from.using.uth.visible.txn.indicator.for.aggregation = 05-2025

amazon.dynamodb.endpoint = https://dynamodb.ap-south-1.amazonaws.com
amazon.aws.accesskey = ********************
amazon.aws.secretkey = lP6MqI0DRdGI00gsmuLVwHhLNBsOPmpPkJQLOoar
external-integration-service-http-url = http://external-integration.default:80

#bank-oauth
#bank.oauth.service.base.url                        = http://localhost:9091
bank.oauth.service.url.category                    = /bank-oauth/ext
bank.oauth.client.id                               = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET
bank.oauthClient.connectionProvider.maxConnections      = 500
bank.oauthClient.connectionProvider.acquireTimeout      = 45000
bank.oauthClient.connectionProvider.maxIdleTime         = 5
bank.oauth.client.connection.timeout                    = 2000
bank.oauth.read.timeout                                = 2000
bank.oauth.socket.timeout                               = 2000

es-v2-host = localhost
updated.index.month.list = payment-history-08-2020
index.name.prefix = payment-history
upi-udir-index.name.prefix = uth-cst

prometheus.explorer                                         = UNIFIED_HiSTORY_SERVICE
prometheus.hostname                                         = 1
prometheus.port                                             = 2

envBased.jacocoSupport = true

jwt.varification.enable = true
uth.upi.txn.history.secret.key = jwt
pth.upi.switch.secret.key                          = jwt

base.url.for.merchant.logo = https://catalog-staging.paytm.com/images/
url.for.ondc.logo = https://assetscdn1.paytm.com/images/catalog/view_item/1519347/*************.png
ondc.verticalId = 204
cart.whitelistedMerchantVerticalIds = 204
cart.whitelisted.userIds.list.for.narration = -1

oauth.token.evaluation.skip = true
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration
spring.main.allow-bean-definition-overriding = true


analytic.first.page.spent.logo.url = "logo"
spend_analytics.source.table = table_name

#time in seconds
cache.time.for.month.spent.data = 5
cache.time.for.months.agg.spent.data = 5
cache.time.for.cst.bot.details = 10

#Cache time for rewind data at client end (In Hours)
cache.time.for.rewind.data = 24

spend.index.name.prefix = spend-history
spend-history-alias = spend_history_alias

static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-mandate-history-merchant-vpa-logo-base-url = https://pwebassets.paytm.com/ocl-upi/upi/images/merchant-logo/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/

generic-tag-logo = tags.png

map.of.uth.category.logo.url          = {\
                                            '1': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/DAIRY_AND_GROCERIES.png',\
                                            '2': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/SERVICES.png',\
                                            '3': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/FUEL_AND_AUTOMOBILES.png',\
                                            '4': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/FOOD_AND_BEVERAGE.png',\
                                            '5': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/SHOPPING.png',\
                                            '6': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/PERSONAL_AND_HEALTH_CARE.png',\
                                            '7': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/TAXI_AND_TRANSPORTATION.png',\
                                            '8': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/TRAVEL.png',\
                                            '9': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/RECHARGES_AND_BILL_PAYMENT.png',\
                                            '10': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/ENTERTAINMENT.png',\
                                            '11': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/Transfers.png',\
                                            '12': 'https://static-ite.orgk.com/uth/images/merchant-category-logo/DEVOTION.png',\
                                            '13': 'https://static-ite.orgk.com/uth/images/category-logo/Financial_Services.png',\
                                            '14': 'https://static-ite.orgk.com/uth/images/category-logo/Education.png',\
                                            '15': 'https://static-ite.orgk.com/uth/images/category-logo/Interest_Credited.png',\
                                            '16': 'https://static-ite.orgk.com/uth/images/category-logo/Cashback_Received.png',\
                                            '17': 'https://static-ite.orgk.com/uth/images/category-logo/Salary_Credited.png',\
                                            '18': 'https://static-ite.orgk.com/uth/images/category-logo/Others.png'\
                                        }

analytics-from-month = 01-2022
analytics-dataBreakUp-page-size = 5
cache.time.for.analytics.agg.and.break.up = 5

#kafka.target-v2-list[0].kafka-client-name               = USERID_FETCHER_KAFKA_CLIENT
#kafka.target-v2-list[0].kafka-producer-key              = USERID_FETCHER_KAFKA_CLIENT_PRODUCER
#kafka.target-v2-list[0].topic                           = middleware_transaction_history_userid_fetcher_data
#kafka.target-v2-list[0].bootstrap-servers               = localhost:9092
#kafka.target-v2-list[0].batch-size-bytes                = 3000
#kafka.target-v2-list[0].linger-ms                       = 15
#kafka.target-v2-list[0].request-timeout-ms              = 5000

#1hour
analytic.config.cron.delay.in.milli = 3600000
#15 min
analytic.config.cron.lock.at.most.in.milli = 900000

analytic.scheduler.enabled.for.whitelisted.users.only = true
whitelisted.custids.for.analytics = 123,456

user.tag.agg.data.table = test

s3.bucket.name = flink-checkpoints-middleware
s3.bucket.region = ap-south-1
s3.bucket.access.key.value = MIDDLEWARE_UNIFIED_HISTORY_S3_ACCESSKEYID
s3.bucket.secret.key.value = MIDDLEWARE_UNIFIED_HISTORY_S3_SECRETACCESSKEY

all.types.of.meta.data = filter,search,spendAnalytics,autoComplete,passbookMenu
metaDataApi.whitelisted.users = -1
#Cache time in ms
cache.ttl.for.metadata.at.app.side = 5000
supported.filters.list.for.all.user = txnCategory
auth-validation-required-for-meta-data = false
meta.data.cache.time = 300

configurable_properties-index-alias = configurable_properties_alias
configurable_properties.source = ElasticSearch
configurable_properties-index = configurable_properties-1

scheduler.schedulerPropertyMap.configurablePropertiesServiceScheduler.name = configurablePropertiesServiceScheduler
scheduler.schedulerPropertyMap.configurablePropertiesServiceScheduler.cronExpression = * * * * * ?
scheduler.schedulerPropertyMap.configurablePropertiesServiceScheduler.schedulerType = configurablePropertiesServiceScheduler
scheduler.schedulerPropertyMap.configurablePropertiesServiceScheduler.isSchedulerEnabled = true

scheduler.schedulerPropertyMap.metaInfoScheduler.name = metaInfoScheduler
scheduler.schedulerPropertyMap.metaInfoScheduler.cronExpression = * * * * * ?
scheduler.schedulerPropertyMap.metaInfoScheduler.schedulerType = metaInfoScheduler
scheduler.schedulerPropertyMap.metaInfoScheduler.isSchedulerEnabled = true

aws-es-from-date                      = 2022-08-01 00:00:00.000 +0530
from-date-listing-filter              = 2022-08-01 00:00:00.000 +0530
date-range-from-date-listing-filter   = 2022-08-01 00:00:00.000 +0530
bank-passbook-migration-date = *************
uth.ppbl.txn.history.secret.key = abc


scheduler.schedulerPropertyMap.localizedDataCacheScheduler.name = localizedDataCacheScheduler
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.cronExpression = 0 * * ? * *
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.schedulerType = localizedDataCacheScheduler
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.isSchedulerEnabled = false

# this cron explanation is to run the scheduler 1AM every night.
scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.cronExpression = 0 0 1 * * ?
scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.schedulerType = indexAvailabilityCheckerScheduler
scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.isSchedulerEnabled = true

#Bo Panel Properties
configurable_properties.source.bo_panel = BOPanel

localization-enabled = false
container.hostname                                = testHost
internal_localization_api_call_enabled = false

search-filter-disabled = false

UTH_V1_LISTING_URL = https://api.orgk.com/transaction-history/ext/v3/search
UTH_V1_DETAIL_URL = https://api.orgk.com/transaction-history/ext/v3
UTH_V2_LISTING_URL = https://api.orgk.com/transaction-history-v2/ext/v3/search
UTH_V2_DETAIL_URL = https://api.orgk.com/transaction-history-v2/ext/v3

uth.cst.txn.history.secret.key = uthcst#jwt@12345


# web v1 properties(would need to revisit commented properties)

#elastic-search-host-1                         = localhost
#elastic-search-port                             = 9200
#elastic-search-scheme                           = http
test.controller.enabled=true
spring.profiles.active=test
oauth.client.id                                    = abc
oauth.client.secret                                = abc
oauth.client.connection.timeout                    = 2000
oauth.read.timeout                                 = 1500

#bank-oauth
# need to check as value is different
#bank.oauth.service.url.category                    = /bank-oauth/ext
#bank.oauth.client.id                               = abc
#bank.oauth.client.secret                           = abc
oauthClient.connectionProvider.name                = webflux
oauthClient.connectionProvider.maxConnections      = 500
oauthClient.connectionProvider.acquireTimeout      = 10000
oauthClient.connectionProvider.maxIdleTime         = 5


# need to check value is different
#whetherto use bankOauth or not
#use-bank-oauth = false

# initially this value will be picked from BO-Panel if it's not present there then this will be default value
# Whether to use OAuth tokeninfo API or not, if it's value is false then /v2/user will be used
use-tokenInfo-api = false




# from date to filter data based on backfill completion format: yyyy-MM-dd HH:mm:ss.SSS Z
# error code 4010 needs to be updated whenever this date changes

# need to check this as value is different
#from-date-listing-filter = 2020-03-18 00:00:00.000 +0530


default-from-date-listing-month-duration = 1
#default page size
page-size-auto-complete = 20

shut-auto-complete-service = false




# need to check as value is different
#external-integration-service-http-url              = http://eis-ite.orgk.com
oauth.service.base.url                             = http://localhost:9991
bank.oauth.service.base.url                        = https://oauth-ite.orgk.com




dc-es-host-list                       = 1127.0.0.1
dc-es-port                            = 9200

analytics-from-date                   = 2022-07-01 00:00:00.000 +0530

#fetching max these no txns from range
max-page-size-of-range   = 20
max-page-size-functionality-enable = false


elastic-search-index-prefix-for-details            = payment-history




# need to check this as value is different
# Prometheus Monitoring Constants
#prometheus.explorer                                         = TRANSACTION_HISTORY
#prometheus.hostname                                         = localhost
#prometheus.port                                             = 9200

#known - outage, any of our system down, to throw non-retriable error
known-issue-at-backend = false

kafka.bootstrap-servers = localhost:9092
aerospike.namespace                               = test
aerospike.host-name                               = localhost
aerospike.port                                    = 3000
aerospike.cache-name                              = upi
aerospike.cache-expiry-time                       = 7200
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
nonTransactingUser.cache.expiry.in.seconds = -1

localise.detail.fields=detailNarration,firstInstrument.narration,secondInstrument.narration,cstorderItem.label
batch.size=50
localise.listing.fields=narration
prepopulate.data.list=Add Money Failed,Add Money Pending,Add Money to %s,Add Money to Paytm Payments Bank,Added Back to Your,Cashback Received,Cashback Received from %s,For Order At,For reversal of money transfer to,From,From Your,In,In Your,Money Added,Money Debited,Money On Hold,Money Paid,Money Received,Money Refunded,Money Restored,Money Sent,Money Transfer Failed,Money Transfer Pending,Money added to %s,Money added to Paytm Payments Bank,Money on hold for Order at %s,Money on hold released by %s,Paid to %s,Payment Failed,Payment Pending,Payment to %s,Received from %s,Refund Failed,Refund for failed transfer to %s,Refund from %s,Refund pending,Reversal of Money Sent,Sent to %s,To,To Your,Transfer to %s,Using Your,Need Help with this Payment

retry.topic.name = pth_retry_data
show-bank-data = false
bankData.blackListed.reportCodes=-1
reportcodes.blocked.for.ShowInListing = 1
bankPassbook.Rptcodes.whilelisted.userslist = 1
bankPassbook.Rptcodes.whilelisteding.percent= 100
bankData.blackListed.dccId = 1
bankData.blackListed.key.deviceId.value.acquirerId = {1:1}
bankData.whiteListed.SchemeCodes = SAINF,SAIND,SALRF,SALRY,WMNSA,WMNSF,SRCIT,SRCIF,BSBDF,BSBDA,MIIND,MIINF


rpt.paymnt.to.mobile.url                  = paytmmp://cash_wallet?featuretype=sendmoneymobile&recipient=%{recipient}
rpt.paymnt.url.p2p.vpa2vpa                = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{pn}&pa=%{pa}&am=%{am}
rpt.paymnt.url.p2m.android                = https://qr.paytm.in/%{qrId}
rpt.paymnt.url.p2m.ios                    = paytmmp://cash_wallet?recipient=%{qrId}
rpt.paymnt.url.p2m.paytQR.mid             = paytmmp://cashier_page?mid=%{mId}
rpt.paymnt.url.p2m.paytmQR.vpa            = paytmmp://cashier_page?vpa=%{vpa}
rpt.paymnt.url.imps.neft.xfer             = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{benefName}&account=%{benefAcctNum}&ifsc=%{benefIfsc}&bank_name=%{beneficiaryBank}
rpt.paymnt.url.p2m.upi                    = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{merchantName}&pa=%{vpa}&am=%{am}
rpt.paymnt.url.p2p.ppblaccount2vpa.inward         = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{remitterName}&pa=%{remitterAcctNum}
rpt.paymnt.url.imps.neft.xfer.inward      = paytmmp://cash_wallet?featuretype=money_transfer&pn=%{remitterName}&account=%{remitterAcctNum}&ifsc=%{remitterIfsc}&bank_name=%{remitterBank}
rpt.payment.url.for.selfTransfer=paytmmp://cash_wallet?featuretype=money_transfer_self&popToRoot=false
rpt.paymnt.url.p2p.vpa2account=paytmmp://pay?paytmAr=%{accRefNum}&pn=%{pn}
rptPayment.enabled.for.selfTransfer=true
rptPayment.enabled.for.vpa2account=false

#deepLink for UPI Lite Cta
deepLink.upi.lite.cta                     = paytmmp://mini-app?aId=7ee85e9b76c544f59ccc0c8b5fcdd655&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZSwicGF5dG1DaGFuZ2VTdGF0dXNCYXJDb2xvciI6IiNjZWVjZmYiLCJzdGF0dXNCYXJTdHlsZSI6MX0sInBhdGgiOiIvIy9tdC91cGktbGl0ZS9iYW5rLXNlbGVjdGlvbiJ9

whitelisted-bank-users = *********,*********
whitelisted-localisation-users=*********,*********
salary.reportCodes = 90100,90110,90120
clientConfig.cron.expression = 0 0 1 * * ?
scheduler.cron.expression = 0 ${random.int[0,59]} 0 * * ?

configurable_properties.scheduler.cron.expression = */60 * * * * *
configurable_properties.source.elastic_search = ElasticSearch
configurable_properties.source.dynamodb = dynamoDB
configurable_properties.source.table = configurable_properties



jwt.verification.enable=true
spring.datasource.url=jdbc:h2:mem:pth_pc;DATABASE_TO_UPPER=false;CASE_INSENSITIVE_IDENTIFIERS=TRUE;INIT=RUNSCRIPT FROM 'classpath:init.sql';
spring.datasource.username=sa
spring.datasource.password=sa
spring.datasource.driver-class-name=org.h2.Driver
hibernate.hbm2ddl.auto=create
spring.jpa.generate-ddl=true
spring.jpa.show-sql=true
spring.jpa.database=h2

mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'configurable_properties_template': 'esConfigurationPropertiesTemplate.json' \
                                                   }
updateElasticSearchTemplate                      = false
updateElasticSearchV2Template                    = false

upi.whitelisted.users = -1
#Merchant logo Base url

# need to check value is different
#metaDataApi.whitelisted.users = 123456


#Value is request Param in filterNode
#Cache time in ms
#All types of metaData

# Need to check as value is different
# all.types.of.meta.data = filter,search

# need to check as value is different
#envBased.jacocoSupport=false


fetch-user-image-from-cache = true
push-missing-user-image-list-to-kafka = true



upi.timeline.baseUrl = http://localhost:8092
upi.timeline.uri =  /upi/int/txn/v4/transaction/status
upi.timeline.sync.connection.timeout = 10000
upi.only.timeline.connection.timeout = 10000
upi.timeline.socket.timeout = 10000
upi.timeline.timeout = 10000
upi.timeline.secret.key = xyz

timeline.url.path = /transaction-history/ext/v2/{sourceContext}/detail?txnId={txnId}&status={status}&showOnlyTimeline=true
timeline.server = http://localhost:8092

#Kafka properties
kafka.target-v2-list[0].kafka-client-name               = recon_config
kafka.target-v2-list[0].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_recon_config_data
kafka.target-v2-list[0].bootstrap-servers               = localhost:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://localhost:8081
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = uthAnalyticsKafkaClient
kafka.target-v2-list[1].kafka-producer-key              = uthAnalyticsProducer
kafka.target-v2-list[1].topic                           = uth-dwh-ingestion-topic
kafka.target-v2-list[1].bootstrap-servers               = localhost:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].linger-ms                       = 1
kafka.target-v2-list[1].request-timeout-ms              = 100

kafka.target-v2-list[2].kafka-client-name               = DUAL_WRITE_AUDIT_CLIENT
kafka.target-v2-list[2].kafka-producer-key              = dualWriteAuditProducer
kafka.target-v2-list[2].topic                           = uth_audit_data
kafka.target-v2-list[2].bootstrap-servers               = localhost:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].linger-ms                       = 1
kafka.target-v2-list[2].request-timeout-ms              = 100

recon.config.cron1.delay.in.milli = 50000
recon.config.cron1.lock.at.most.in.milli = 500000

chat.url.path = paytmmp://chat?featuretype=start_chat

outwardInternationalRemittance.url = paytmmp://payment_bank?featuretype=outward_remittance

min.Length.Of.AutoComplete.Query = 3
min.Length.Of.Search.Query = 3
max.Time.To.Print.AutoComplete.Logger = 100

detail-retriable-count = 1
detail-retriable-wait-time = 0


managed-es-host = localhost
whitelisted.users.list.for.managed.es = 123
managed.roll.out.percentage = 0

managed-es-v2-host = localhost

enable.unmask.accNumber = false

primary.es.cluster = V0
max-length-of-autoComplete-query = 20

#isWhiteListingRequired = 1 :: whiteListing required for some users
#isWhiteListingRequired = -1 :: whiteListing not required or open for all
#isWhiteListingRequired = 0 :: close for all
white.listing.flag.for.add.money.debit.participant = 1
rollOut.percent.for.add.money.debit.participant = 10
white.listed.users.list = 124,3436,**********,**********,**********,************,**********

rollout.config-list[0].percentage = 100
rollout.config-list[0].whitelisting-required = 0
rollout.config-list[0].whitelisting-for = detailPageRoleOutFeature

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = -1
rollout.config-list[1].whitelisting-for = listingCache

rollout.config-list[2].percentage = 100
rollout.config-list[2].whitelisting-required = -1
rollout.config-list[2].whitelisting-for = upiPassbookCache

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = -1
rollout.config-list[3].user-list = 0
rollout.config-list[3].whitelisting-for = taggingOnDetails

rollout.config-list[4].percentage = 0
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,********87
rollout.config-list[4].whitelisting-for = DcListingRouting

rollout.config-list[5].percentage = 0
rollout.config-list[5].whitelisting-required = 0
rollout.config-list[5].whitelisting-for = DcNonTransactingHandling

rollout.config-list[6].percentage = 0
rollout.config-list[6].whitelisting-required = 1
rollout.config-list[6].user-list = *********,*********
rollout.config-list[6].whitelisting-for = UpiCcEmiCta

rollout.config-list[7].percentage = 0
rollout.config-list[7].whitelisting-required = 1
rollout.config-list[7].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,********87,6236193
rollout.config-list[7].whitelisting-for = UthAnalyticsWhiteListing

rollout.config-list[8].percentage = 0
rollout.config-list[8].whitelisting-required = 1
rollout.config-list[8].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,********87
rollout.config-list[8].whitelisting-for = trafficDiversionToDcForWhiteListedApis

rollout.config-list[9].percentage = 0
rollout.config-list[9].whitelisting-required = 1
rollout.config-list[9].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,********87
rollout.config-list[9].whitelisting-for = listingPageNoMoreThanOneTrafficDiversionToDc

rollout.config-list[10].percentage = 0
rollout.config-list[10].whitelisting-required = 1
rollout.config-list[10].user-list = **********
rollout.config-list[10].whitelisting-for = UserRateLimitingWhiteListing

rollout.config-list[11].percentage = 0
rollout.config-list[11].whitelisting-required = 1
rollout.config-list[11].user-list = *********,*********,*********,********,*********,*********,**********,********87,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[11].whitelisting-for = bankOauthApiRollout

rollout.config-list[12].percentage = 0
rollout.config-list[12].whitelisting-required = 1
rollout.config-list[12].user-list = *********,*********,*********,********,*********,*********,**********,********87,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[12].whitelisting-for = newInvalidationVersionApplicable


detailPageApiCachingEnabled = true
isNtuCacheEnabled = true
isUpiPassbookCacheEnabled = true

need-to-convert-received-Query = true
autocomplete-whitelisted-custIds = -1
autocomp-new-query-whitelisting-percentage = 100


dynamo.max-error-retries = 10

searchFields-userView-mapping = {\
                                    "searchFields.searchOtherName":"Name", \
                                    "searchFields.searchOtherBankName":"Bank Name", \
                                    "searchFields.searchOtherMobileNo":"Mobile No.", \
                                    "searchFields.searchPaymentSystem":"Account", \
                                    "searchFields.searchTxnIndication":"Type", \
                                    "searchFields.searchTxnCategory":"Type", \
                                    "searchFields.searchTxnStatus":"Status", \
                                    "searchFields.searchUthMerchantCategory":"Category", \
                                    "searchFields.searchOtherVpa":"UPI ID", \
                                    "searchFields.searchVoucherName":"Voucher", \
                                    "searchFields.searchWalletType":"Sub Wallet", \
                                    "searchFields.searchOtherAccountNo":"Account Number", \
                                    "searchFields.searchSelfBankName":"Bank Name", \
                                    "searchFields.searchTags":"Tag", \
                                    "searchFields.searchSelfAccountNo":"Account Number",\
                                    "searchFields.searchSelfAccTyp":"Account" }

#metadata api Oauth Call
oauth.call.enable.in.metadata.api = true

#ForEnabling Req Money Cta
enable.requestMoney.cta = true

ipoMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9pcG8ifQ==
manage.automatic.payment.cta.base.deeplink =  paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZX0sInBhdGgiOiIvIy9tdC9hdXRvbWF0aWMtcGF5bWVudHMvbWFuZGF0ZS1kZXRhaWxzIn0=&umn=
recurringMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9tYW5kYXRlcyJ9

# need to check as value is different
# updated.index.month.list = payment-history-07-2020,payment-history-08-2020,payment-history-09-2020,payment-history-10-2020,payment-history-11-2020

env-prefix = ""
#For routing to Dc cluster.
isListingRoutingEnabled = true
isDetailRoutingEnabled = true
isTrafficDiversionEnabled = false

#Maximum Days Diff for tagging a txn.
maxAllowedDayDiffForTagging = 30

#Url to get next listing hit
listingUrl = https://api.orgk.com/transaction-history-v2/ext/v3/search

txn-date-mandatory-for-detail-api = false


onHold.h5.base.url = paytmmp://cash_wallet?featuretype=cash_ledger&tab=subwallet&cta_type=onhold


#<br><br> is added in below message to provide 2 line breaks on App
deemed-txn-msg = This payment is pending because the receiver's bank is facing issues in accepting the payment.<br><br>Don't worry, your money is safe. We are continuously checking the status of your payment and request you to not make multiple payments. Banks may take upto 3-5 working days to confirm the final status of your payment. In case this payment fails, your bank will automatically refund money to your account.


appSideCache.invalidateVersion = 6
appSideCache.latestInvalidateVersion = 13
appSideCache.invalidateVersion.enabled = false
appCacheRecon.invalidateStoredDataFlag.enable = false

# 15*24*60*60*1000 = ********** 15 days
updatesApi.maxTimeGapForUpdatesApi = **********
# 30*24*60*60*1000 = ********** 1 month
updatesApi.fromDateGap = **********
updatesApi.pageSize = 100

deepLinks.wmcDescription = paytmmp://sflanding?url=https://storefront.paytm.com/v2/h/know-more-wallet-maintenance-charges&backbtn=true
deepLinks.walletReactivate = paytmmp://cash_wallet?featuretype=add_money&tab=wallet

listing-localization-enabled = false

other-party-bank-details-enabled = false
other-party-bank-details-for-vpa2account-txn-enabled = true
other-party-bank-details-enabled-inward = false
env.based.prefix =
min-amt-req-to-show-other-party-info-in-outward-txn-in-paisa = 1000


#Route nonTransactingUsers to DC or not.
nonTransactingRoutingToDc_upiLite = true
nonTransactingRoutingToDc_allTxns = true

#Whether to use customerCreationDate or not
isUseOfCustomerCreationDateEnabled = false

deepLink.storecash.cta                    = paytmmp://mini-app?aId=74c710393f38477eb7994901e7e80307
cta.repeatPayment.use-mobileNo-deeplink-for-upi-p2p-txns = false
poweredbyurl.enable.in.footerlogo = true

repeatPayment.legacyImplementationEnabled = false

#deeplink for Storecash Cta
storecash-cta-enabled = true
uth.analytics.send.to.kafka = true


deepLink.convert.emi.cta = paytmmp://mini-app?aId=840e7060809249e0a9b4a50a7b86388f&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvY2MtZW1pL2FjdGl2YXRlIn0=
deepLink.view.emi.cta = paytmmp://mini-app?aId=840e7060809249e0a9b4a50a7b86388f&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvY2MtZW1pL2JyZWFrdXAifQ==
upi.cc.emi.credit.card.cycle.days = 45
upi.cc.emi.cta.enable = true

uth.user.rate.limit.per.hour = 360
uth.user.rate.limit.hash.algo = SHA256
uth.user.rate.limit.enable = false

oauth.invalid.token.exception.response.enable = false

amount-range-filter-enabled = true
min-amount-limit-for-amount-range-filter = 0
max-amount-limit-for-amount-range-filter = 1000000

uth.user.daily.rate.limit.enable = false
uth.user.daily.rate.limit.threshold = 10

uth.user.rate.limit.apis.blacklisted = /payments-history-v2/ext/v1/metaData
#key is verticalId and value will contain List<GenericCtaInfo>
txn.details.config.mapping.for.onus.merchant.verticalId = {\
  "94": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId94","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false,"order_id":"$orderId"},"path":"/h5-recharge-pos/v1/index.html"}}], \
  "174": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId174","deeplink":"paytmmp://mini-app?aId=edc6391a24ac4eec8b2d3558d5b53195&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false},"params":"?utm_source=passbook","path":"/myorders/$orderId"}}], \
  "66": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId66","deeplink":"paytmmp://mini-app?aId=358b2bd4936a4f34918c620a3c7ac4f9&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false},"params":"?utm_source=passbook","path":"/myorders/$orderId"}}],\
  "87": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId87","deeplink":"paytmmp://mini-app?aId=77a7aa36c00e469482a6219004fde717&data=$parametersBase64Hash","parameters":{"params":"","path":"/city-bus/orderSummary/$orderId","sparams":{"pullRefresh":false,"canPullDown":false,"showTitleBar":false}}}], \
  "72": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId72","deeplink":"paytmmp://train_order_summary_v2?url=https://cart.paytm.com/v1/myOrders/$orderId&from=h5"}], \
  "26": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId26","deeplink":"paytmmp://bus_order_summary?url=https://cart.paytm.com/v1/myOrders/$orderId?&isfromorderhistory=0&vertical=bus"}],\
  "64": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId64","deeplink":"paytmmp://flight_order_summary?url=https://cart.paytm.com/v1/myOrders/$orderId&isfromorderhistory=0"}],\
  "70": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId70","deeplink":"paytmmp://movie_order_summary?order_id=$orderId"}],\
  "73": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId73","deeplink":"paytmmp://events?insiderH5Url=https://h5.insider.in/payments/status/fromOrders/$orderId"}],\
  "204": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId204","deeplink":"paytmmp://product?url=https://paytmmall.com/shop/summary/$orderId#html"}],\
  "84": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId84","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "76": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId76","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "83": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId83","deeplink":"paytmmp://gold?url=https://cart.paytm.com/v1/myOrders/$orderId&order-summary-type=Gold&From=Order_history&order_id=$orderId"}],\
  "105": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId105","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "17": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId17","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "90": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId90","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "4": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId4","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "56": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId56","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "187": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId187","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "71": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId71","deeplink":"paytmmp://order_summary?url=https://cart.paytm.com/v2/myorders/$orderId/detail"}]\
  }

view-details-cta-enabled = true
postpaid.vertical.id = 94
show-benef-acc-number = true

pushToKafkaForAuditEnabled = true
minTimeDiffForDualWriteAuditMs = 4000
dualWriteAuditStartDate = 2023-10-28 00:00:00.000 +0530

uth.downstream.auth.circuit.breaker.config = {\
  "refreshPeriod":"60",\
  "limit":"1000"}

uth.downstream.auth.circuit.breaker.enable = false
uth.downstream.auth.circuit.breaker.failure.httpcodes = 500,502,503,504
uth.downstream.auth.circuit.breaker.failure.classes = RequestTimeOutException

txn.stream.lag.based.tracking.feature.enabled           = false
txn.stream.lag.based.txn.details.rejections.enabled     = false
txn.details.source.txn.stream.max.lag.rejection.buffer.seconds   = 1
white-listed-apis-for-traffic-diversion.on.rate.limit =
white-listed-apis-for-traffic-diversion = search,detail

pth-pth.consumer.service-secret = PTH_CONSUMER_SERVICE_SECRET
statement-service-es-fetch-page-size = 10
statement-service-disabled = false

cta.enable-view-history-for-onus-txns = true
cta.view-history-deeplink-for-onus-txns = paytmmp://mini-app?aId=840e7060809249e0a9b4a50a7b86388f&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvcGIvaGlzdG9yeS1saXN0In0=

min.amt.limit.in.paisa.to.show.convert.emi.cta = 200000

uth.ts.cst.secret.key = f2srt64r57mT45cdws

detail.view.from.parent.txn.enabled = true

merchant.type.change.based.on.oms.order.logic.enable = true
recap.meta.data.index = recap-meta-data-index

rewind.api.auth.skipping.enabled = true

recap.es.indexing.enabled = true

recap.cache.write.enabled = true
fuzzy.search.required.for.autoComplete.api = false


need.help.cta.config.map = {\
  "ocl" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForOclCstDeeplink","deeplink":"paytmmp://csttree?featuretype=cst_issue&isOnusTxn=$isOnusTxn&PI=$PI&urlType=launchBot&calledFrom=uth_detail_page&transactionId=$txnId&showHomeOnBack=false"},\
  "upi" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForUpiCstDeeplink","deeplink":"paytmmp://mini-app?aId=5bdde7c737ee423b98beb769b281df5e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXJhbXMiOiI/ZmVhdHVyZXR5cGU9Y29tbW9uX21pbmkmb3Blbl9zY3JlZW49Y3N0X2Zsb3cifQ==&upiTxnType=$txnType&upiTxnDate=$txnDate&upiTxnAmount=$amount&upiTransactionId=$txnId&rrnNumber=$rrn&itemName=$itemName&itemStatus=$itemStatus&launchOrderBot=true"},\
  "wallet" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForWalletCstDeeplink","deeplink":"paytmmp://mini-app?aId=5bdde7c737ee423b98beb769b281df5e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXJhbXMiOiI/ZmVhdHVyZXR5cGU9Y29tbW9uX21pbmkmb3Blbl9zY3JlZW49Y3N0X2Zsb3cifQ==&itemStatus=$itemStatus&itemName=$itemName&narration=$narration&walletTxnId=$txnId&walletTxnTypeCode=$txnType&launchOrderBot=true"},\
  "bank" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForBankCstDeeplink","deeplink":"paytmmp://mini-app?aId=5bdde7c737ee423b98beb769b281df5e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXJhbXMiOiI/ZmVhdHVyZXR5cGU9Y29tbW9uX21pbmkmb3Blbl9zY3JlZW49Y3N0X2Zsb3cifQ==&cbsTxnId=$txnId&cbsTxnSerialNumber=$txnSerialNum&cbsReportCode=$reportCode&cbsTxnDate=$txnPostDate&cbsAccountId=$accountId&cbsTxnType=$txnType&itemName=$itemName&rrnNumber=$rrn&launchOrderBot=true"},\
  "toll" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForTollCstDeeplink","deeplink":"paytmmp://mini-app?aId=5bdde7c737ee423b98beb769b281df5e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXJhbXMiOiI/ZmVhdHVyZXR5cGU9Y29tbW9uX21pbmkmb3Blbl9zY3JlZW49Y3N0X2Zsb3cifQ==&tollTxnId=$tollTxnId&itemName=$itemName&itemStatus=$itemStatus&launchOrderBot=true"},\
  "default" : {"ctaType":"CST_NEED_HELP","ctaLabelLocaleKey":"ctaLabelLocaleKeyForDefaultCstDeeplink","deeplink":"paytmmp://contactus"}\
  }

need-help-cta-enabled = true

online.deals.verticalId  = 66
gv.verticalId            = 66
offline.deals.verticalId = 174
online.deals.logoUrl     = https://assetscdn1.paytm.com/images/catalog/view_item/2283581/*************.png
gv.logoUrl               = https://consumergv.paytm.com/gv-custom-files/images/gift-cards.png
offline.deals.logoUrl    = https://assetscdn1.paytm.com/images/catalog/view_item/1799274/*************.png

recentTxns.disabled = false
recentTxns.list.of.supportedStatus = 1,2,3
recentTxns.cache.ttl.seconds = 300
ptl.apiVersion.start = 2.0

enable-deaf-txn-faq-cta = false
deepLink.deaf.txn.faq.cta = http://orgk.com/customer-service/DEAFund

bootWithoutConfigs.boPanelConfigs = true

rptPayment.disabled.bankAndWallet = true

# Powered By Logo mapping wrt upiHandle
poweredBy.upiLogo.list = paytm

fullScanForSecondaryDbEnabled=false
cst.pth.txnList.api.secret = cA9AJxZSC7tLASkY


mandate-elastic-search-alias = mandate_history_alias
mandate.history.cst.secret = *********
mandate-history-from-date = 2022-08-01 00:00:00.000 +0530

mandate-journey-api-es-fetch-limit = 100

upi-lite-filter-extra-handling-enabled = false

blacklisted.apis.for.filters = payments-history-v2.ext.v1.ping,payments-history-v2.ext.v1.health,pth.ext.v1.ping,pth.ext.v1.health

upi-cc-filter-from-date = 2024-10-01 00:00:00.000 +0530
onus-vertical-filter-from-date = 2024-11-01 00:00:00.000 +0530
vertical.name.to.ids.mapping={\
  "Recharges & Bill Payments":[4,17,56,71,76,84,86,87,90,105,155],\
  "Gift Cards & Deals":[5,66,85,174],\
  "Travel":[26,64,60,72,167,126,181],\
  "Movies & Events":[70,40,55,73,74],\
  "Shopping":[2,6,7,8,9,10,11,12,13,14,15,18,19,20,22,23,24,25,30,31,33,34,35,36,37,38,41,42,43,44,45,46,47,49,50,51,53,58,63,67,68,69,77,78,80,99,107,117,118,125,127,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,156,157,159,162,163,166,169,170,171,178,182,186,204],\
  "Insurance":[79,172,173],\
  "Paytm Gold":[82,83,91],\
  "Paytm Stores":[120,180,190,191,194]\
  }
view_history_cta_enabled_for_vpa_to_account_txn = true
view_history_cta_enabled_for_vpa_to_vpa_txn = true
view_history_cta_enabled_for_vpa_to_3p_vpa_txn = true
view_history_cta_enabled_for_third_party_merchant_txn = true
view_history_cta_enabled_for_ofus_merchant_txn = true
view_history_cta_enabled_for_onus_merchant_txn = true

view_history_cta_deeplink_for_vpa_to_account_txn = paytmmp://chat?featuretype=start_chat&userType=BANK&bankName=$bankName&accRefId=$accRefId&maskedAccNo=$maskedAccountNo&source=passbook_view_history
view_history_cta_deeplink_for_vpa_to_vpa_txn = paytmmp://chat?featuretype=start_chat&userType=CUSTOMER&custId=$custId &custName=$name&source=passbook_view_history
view_history_cta_deeplink_for_vpa_to_3p_vpa_txn = paytmmp://chat?featuretype=start_chat&userType=VPA&vpa=$vpa&txnCategory=VPA2VPA&source=passbook_view_history
view_history_cta_deeplink_for_third_party_merchant_txn = paytmmp://chat?featuretype=start_chat&userType=VPA&vpa=$vpa&txnCategory=VPA2MERCHANT&source=passbook_view_history
view_history_cta_deeplink_for_ofus_merchant_txn = paytmmp://chat?featuretype=start_chat&userType=MERCHANT&mid=$merchantId&name=$name&source=passbook_view_history
view_history_cta_deeplink_for_onus_merchant_txn = paytmmp://mini-app?aId=7ee85e9b76c544f59ccc0c8b5fcdd655&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2luZGV4Lmh0bWwjL3BiL2hpc3RvcnktbGlzdCJ9&verticalId=$verticalId

autoTagging.details.aerospike.set.name = auto_tagging_details_set
tagging.auto.tagging.enabled = true
# Expiry time in secs or -1 means no expiry
autoTagging.details.aerospike.expiry.time.in.secs = -1
autoTagging.aerospike.namespace                = pth
autoTagging.aerospike.host-name                = localhost
autoTagging.aerospike.port                     = 3000

jwt.verification.supported.clients = UPI,PPBL,CST_HOME,pth_consumer_service,CST,TPAP_PANEL,UPI_SWITCH