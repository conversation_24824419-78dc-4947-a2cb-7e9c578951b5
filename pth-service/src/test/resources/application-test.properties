server.port     = 12001
build.timestamp = @timestamp@
app.name        = @project.artifactId@
app.version     = @project.version@
git.commit.id   = 122
git.branch      = master
logging.config  = classpath:log4j2-local.json

elastic-search-index                  = payment_history_alias

es-socket-timeout                     = 2000
es-max-retry-timeout                  = 2000
es-connect-timeout                    = 1000
es-connect-request-timeout            = 1000

es-host-list                          = 127.0.0.1
es-port                               = 12001

#V2 ES Configuration
es-v2-host-list                                    = 127.0.0.1
es-v2-port                                         = 12001

#Mandate es configuration
mandate-es-host-list = 127.0.0.1

server.tomcat.accesslog.enabled                    = true
server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
server.tomcat.accesslog.directory                  = /log/payment-transaction-history-service
server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
server.tomcat.accesslog.rotate                     = true
server.tomcat.accesslog.rename-on-rotate           = true
server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
server.tomcat.accesslog.buffered                   = false
server.tomcat.accesslog.request-attributes-enabled = true

txn.tags.table                                 = txn_tag_data-uth
user.tags.table                                = user_tag_data
userId.updatedTimeStamp.index                  = userId-updatedTimeStamp-index
sysyem.tags.table                              = system_tag_data
tagging.max.tags.count.per.txn                              = 1
tagging.tag.max.length                                     = 20
tagging.tag.min.length                                     = 3
tagging.user.tags.limit                                     = 100
tagging.user.tags.count.in.suggested.tags                  = 2
tagging.suggested.tags.max.count                          = 4
tagging.search.supported.tags.max.count                   = 100

jwt.varification.enable = true
uth.upi.txn.history.secret.key = 83738hbfjfbfjb2565265lkldkldk
uth.ppbl.txn.history.secret.key = abc
uth.pms.txn.history.secret.key = uthpms#latestTxn@1234
pth.upi.switch.secret.key                          = 83738hbfjfbfjb2565265lkldkldk

cache.time.for.rewind.data= 24

#bank-oauth
bank.oauth.service.base.url                        = http://127.0.0.1:12001
bank.oauth.service.url.category                    = /bank-oauth/ext
bank.oauth.client.id                               = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET
bank.oauthClient.connectionProvider.maxConnections      = 500
bank.oauthClient.connectionProvider.acquireTimeout      = 45000
bank.oauthClient.connectionProvider.maxIdleTime         = 5
bank.oauth.client.connection.timeout                    = 2000
bank.oauth.read.timeout                                = 2000
bank.oauth.socket.timeout                               = 2000

updated.index.month.list = payment-history-08-2020
index.name.prefix = payment-history

prometheus.explorer                                         = UNIFIED_HiSTORY_SERVICE
prometheus.hostname                                         = localhost
prometheus.port                                             = 1234

analytics-from-month = 01-2022
analytics-dataBreakUp-page-size = 5

envBased.jacocoSupport = true

oauth.token.evaluation.skip = true

static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-mandate-history-merchant-vpa-logo-base-url = https://pwebassets.paytm.com/ocl-upi/upi/images/merchant-logo/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/

all.types.of.meta.data = filter,search,spendAnalytics,autoComplete,passbookMenu
metaDataApi.whitelisted.users = -1
#Cache time in ms
cache.ttl.for.metadata.at.app.side = 5000
supported.filters.list.for.all.user = txnCategory
auth-validation-required-for-meta-data = false
meta.data.cache.time = 300

configurable_properties-index-alias = configurable_properties_alias
configurable_properties.source = ElasticSearch
configurable_properties-index = configurable_properties-1

aws-es-from-date                      = 2022-08-01 00:00:00.000 +0530
from-date-listing-filter              = 2020-08-01 00:00:00.000 +0530
date-range-from-date-listing-filter   = 2020-08-01 00:00:00.000 +0530
#Bo Panel Properties
configurable_properties.source.bo_panel = BOPanel
container.hostname                                = 127.0.0.1

uth.cst.txn.history.secret.key = uthcst#jwt@12345

# web v1 properties(would need to revisit commented properties)
# server.port                                        = 8092

#app.name                                           = @project.artifactId@
#app.version                                        = @project.version@


external-integration-service-http-url              = http://eis-ite.orgk.com
oauth.service.base.url                             = http://127.0.0.1:12001

oauthClient.connectionProvider.name                = webflux
oauthClient.connectionProvider.maxConnections      = 500
oauthClient.connectionProvider.acquireTimeout      = 10000
oauthClient.connectionProvider.maxIdleTime         = 5

#bank.oauth.service.base.url                        = https://oauth-ite.orgk.com


#es-host-list                                       = *************
dc-es-host-list                                    = *************
managed-es-v2-host                                 = 127.0.0.1
#es-port                                            = 9200

#fetching max these no txns from range
max-page-size-of-range   = 20
max-page-size-functionality-enable = false

#elastic-search-index                               = payment_history_alias
elastic-search-index-prefix                        = payment-history-
elastic-search-index-prefix-for-details            = payment-history

#server.tomcat.accesslog.enabled                    = true
#server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
#server.tomcat.accesslog.directory                  = /log/middleware-transaction-history-service
#server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
#server.tomcat.accesslog.rotate                     = true
#server.tomcat.accesslog.rename-on-rotate           = true
#server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
#server.tomcat.accesslog.buffered                   = false
#server.tomcat.accesslog.request-attributes-enabled = true


#CBS sftp config

#static-bank-logo-base-url = https://static.orgk.com/uth/images/bank-logo/
#static-category-logo-base-url = https://static.orgk.com/uth/images/category-logo/
#static-status-logo-base-url = https://static.orgk.com/uth/images/status-logo/
#static-wallet-logo-base-url =  https://static.orgk.com/uth/images/wallet-logo/
#static-paymentMode-logo-base-url = https://static.orgk.com/uth/images/payment-intiation-mode/

#Prometheus Monitoring Constants
#prometheus.explorer                                         = TRANSACTION_HISTORY
#prometheus.hostname                                         = localhost
#prometheus.port                                             = 9200

#known - outage, any of our system down, to throw non-retriable error
known-issue-at-backend = false

#Default Value should be false
use-bank-oauth = false

# initially this value will be picked from BO-Panel if it's not present there then this will be default value
# Whether to use OAuth tokeninfo API or not, if it's value is false then /v2/user will be used
use-tokenInfo-api = false

# from date to filter data based on backfill completion format: yyyy-MM-dd HH:mm:ss.SSS Z
# error code 4010 needs to be updated whenever this date changes
#from-date-listing-filter = 2020-08-01 00:00:00.000 +0530
default-from-date-listing-month-duration = 11
kafka.bootstrap-servers = *************:9092, *************:9092, *************:9092

#logging.config                                  = classpath:log4j2-dev.json

aerospike.namespace                               = test
aerospike.host-name                               = localhost
aerospike.port                                    = 3000
aerospike.cache-name                              = upi
aerospike.cache-expiry-time                       = 7200
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
nonTransactingUser.cache.expiry.in.seconds = -1
localisation.asyc.corePool =5
localisation.asyc.maxPool=7
localisation.asyc.queueCapacity=10

kafka.localisation.target.client =marketplace
kafka.localisation.target.topic =uth_localisation_data
kafka.localisation.target.bootstrapServers =*************:9092, *************:9092, *************:9092
kafka.localisation.target.batchSizeBytes =30000
kafka.localisation.target.lingerMs =15
kafka.localisation.target.requestTimeoutMs =5000
kafka.localisation.target.confluentKafkaRegistryUrl=http://*************:8081/
localise.detail.fields=detailNarration,firstInstrument.narration,secondInstrument.narration,recentTransactionInfo.narration,cstorderItem.label,repeatPayment.name
batch.size=50
localise.listing.fields=narration
prepopulate.data.list=Add Money Failed,Add Money Pending,Add Money to %s,Add Money to Paytm Payments Bank,Added Back to Your,Cashback Received,Cashback Received from %s,For Order At,For reversal of money transfer to,From,From Your,In,In Your,Money Added,Money Debited,Money On Hold,Money Paid,Money Received,Money Refunded,Money Restored,Money Sent,Money Transfer Failed,Money Transfer Pending,Money added to %s,Money added to Paytm Payments Bank,Money on hold for Order at %s,Money on hold released by %s,Paid to %s,Payment Failed,Payment Pending,Payment to %s,Received from %s,Refund Failed,Refund for failed transfer to %s,Refund from %s,Refund pending,Reversal of Money Sent,Sent to %s,To,To Your,Transfer to %s,Using Your,Settlement Received from %s,Gift Voucher Received from %s,Gift Voucher Sent To %s,For reversal of failed transfer to,Compensation Received,As compensation for,Automatic Payment made to %s,For Automatic Payment to,Refund Received,Interest earned for %s,Interest Received,Interest Earned,Cash Withdrawal at %s,Cash Withdrawal,At,Cash Withdrawal at,For,Cash Withdrawal at Paytm Bank Agent,ATM Cash Withdrawal at %s,At ATM,At Store,Refund for failed cash withdrawal,For reversal of cash withdrawal at ATM,Cash Deposited at %s,Cash Deposited,Cash Deposited with Paytm Bank Agent,Cash Deposit at ATM\,%s,At ATM Recycler,Cash Deposit,Salary Received,Flexi-Salary Received,Reimbursement Received,Charges Paid,Charges Paid for purchase of Paytm Payments Bank Debit Card,For Purchase of,For Cash Withdrawal at ATM,Charges Paid for transaction at ATM,For Transaction at ATM,For dispute raised against ATM Cash Withdrawal at,For dispute raised against store payment at,Cashback Received from Paytm Payments Bank,Cashback Received from Paytm,Cheque Return Charges,Aadhaar based direct benefit received,Recovery Deduction,Lien Recovery Reversal,Refund of excess card charges,Issuance Fee charged for Debit card,Added for failed transaction,Money transferred,Money paid to %s via cheque,Money paid via cheque,Money received from cheque payment,LPG subsidy received,Deducted by %s,Reversal of Debit Card fees,Charges Refunded,For Failed Purchase of,Charges Refunded for Debit Card,Annual Charge Paid for Debit Card,Charges Refunded for Cheque Book,Charges Refunded for cash withdrawal at ATM,Charges Refunded for ATM transaction,Charges Paid for payment at %s,For Payment at,Charges Refunded for payment at %s,Charges Paid for cash withdrawal at ATM,Charges Paid for ATM transaction,For dispute raised against,For ATM Transaction at,For Failed ATM Transaction at,For Failed Payment at,For dispute raised against online payment at,Charges Paid for payment to %s,For Payment to,For Failed Payment to,Refund for failed cheque payment,Refund for failed cheque payment to %s,For reversal of Cheque Payment to,Money Deducted,For reversal of money received for,For reversal of money received from,Refund for failed payment to %s,Money reversed for failed transfer from %s,Refund for failed transfer via UPI,For reversal of,Fixed Deposit Created,Refund for failed Fixed Deposit creation,Fixed Deposit Redeemed,For reversal of failed,Refund of AePS fund transfer,Refund for AePS Cash withdrawal,AePS fund transfer,AePS Cash withdrawal,Paid Successfully,Recent Payments With %s,Paid,Received,Refunded,Recent Gift Vouchers,Sent,Added to %s,Pending Add Money to %s,Failed Add Money to %s,Created,Deposited at %s,Withdrawal at Paytm Bank Agent,Withdrawal at %s,Earned for %s,Need Help with this Payment,View more details,Foreign Remittance Received from,Paid for Outward Remittance to,Refund of Outward Remittance sent to,RECEIVED IN
whitelisted-bank-users = *********,*********
whitelisted-localisation-users = -1
show-bank-data = true

#spring.datasource.url                            = jdbc:mysql://*************:3306/mw_txn_history
#spring.datasource.username                       = uth
#spring.datasource.password                       = Uth@12345
#spring.datasource.driver-class-name              = com.mysql.jdbc.Driver
#spring.datasource.configuration.maximum-pool-size=2
#clientConfig.cron.expression = 0 30 14 * * ?
#jwt.verification.enable=true
spring.datasource.url=**********************************
spring.datasource.username=root
spring.datasource.password=${PTH_MYSQL_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.configuration.maximum-pool-size=2
clientConfig.cron.expression=0 30 14 * * ?
jwt.verification.enable=true


#configurable_properties-index-alias = configurable_properties_alias
configurable_properties.scheduler.cron.expression = * * * * * ?
configurable.scheduler.create.new.indices.cron.expression = 0,10,20,30 0-5 0 1 * ?
#configurable_properties.source = ElasticSearch
#configurable_properties-index = configurable_properties-1

#container.hostname                                = ${HOSTNAME}
fetch-user-image-from-cache = true
push-missing-user-image-list-to-kafka = false
#Kafka properties
kafka.target-v2-list[0].kafka-client-name               = recon_config
kafka.target-v2-list[0].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_recon_config_data
kafka.target-v2-list[0].bootstrap-servers               = *************:9092,*************:9092,*************:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://*************:8081/
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = txnHistoryUserImageClient
kafka.target-v2-list[1].kafka-producer-key              = user_image_txn_history_producer
kafka.target-v2-list[1].topic                           = user_image_url_data
kafka.target-v2-list[1].bootstrap-servers               = *************:9092,*************:9092,*************:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 5000

kafka.target-v2-list[2].kafka-client-name               = nonTransactingUserKafkaClient
kafka.target-v2-list[2].kafka-producer-key              = nonTransactingUserProducer
kafka.target-v2-list[2].topic                           = uth_cache_updater_data
kafka.target-v2-list[2].bootstrap-servers               = *************:9092,*************:9092,*************:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].confluent-kafka-registry-url    = http://*************:8081/
kafka.target-v2-list[2].linger-ms                       = 15
kafka.target-v2-list[2].request-timeout-ms              = 5000

kafka.target-v2-list[3].kafka-client-name               = uthAnalyticsKafkaClient
kafka.target-v2-list[3].kafka-producer-key              = uthAnalyticsProducer
kafka.target-v2-list[3].topic                           = uth-dwh-ingestion-topic
kafka.target-v2-list[3].bootstrap-servers               = *************:9092,*************:9092,*************:9092
kafka.target-v2-list[3].batch-size-bytes                = 3000
kafka.target-v2-list[3].linger-ms                       = 1
kafka.target-v2-list[3].request-timeout-ms              = 100

kafka.target-v2-list[4].kafka-client-name               = DUAL_WRITE_AUDIT_CLIENT
kafka.target-v2-list[4].kafka-producer-key              = dualWriteAuditProducer
kafka.target-v2-list[4].topic                           = uth_audit_data
kafka.target-v2-list[4].bootstrap-servers               = localhost:9092
kafka.target-v2-list[4].batch-size-bytes                = 3000
kafka.target-v2-list[4].linger-ms                       = 1
kafka.target-v2-list[4].request-timeout-ms              = 100


test.controller.enabled = false

upi.timeline.baseUrl = http://localhost:8092
upi.timeline.uri =  /upi/int/txn/v4/transaction/status
upi.timeline.sync.connection.timeout = 10000
upi.only.timeline.connection.timeout = 10000
upi.timeline.socket.timeout = 10000
upi.timeline.timeout = 10000
upi.timeline.secret.key = xyz
uth.analytics.send.to.kafka = true

timeline.url.path = /payments-history-v2/ext/v2/{sourceContext}/detail?txnId={txnId}&status={status}&showOnlyTimeline=true
timeline.server = http://localhost:8092

recon.config.cron1.delay.in.milli = 50000
recon.config.cron1.lock.at.most.in.milli = 500000

#metaDataApi.whitelisted.users = -1

chat.url.path = paytmmp://chat?featuretype=start_chat


outwardInternationalRemittance.url = paytmmp://payment_bank?pageId=passbookPage&featuretype=outward_remittance
updateElasticSearchTemplate                      = false
max-length-of-autoComplete-query = 20

max-Num-Of-ContactBook-Numbers = 4
contactbook-search-whitelisted-users-custId-Identifier = -1
contactbook-search-whitelisting-percentage = 100

#oauth.token.evaluation.skip = true

rollout.config-list[0].percentage = 100
rollout.config-list[0].whitelisting-required = 0
rollout.config-list[0].whitelisting-for = detailPageRoleOutFeature

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = 1
rollout.config-list[1].user-list = 0
rollout.config-list[1].whitelisting-for = taggingOnDetails

rollout.config-list[2].percentage = 100
rollout.config-list[2].whitelisting-required = -1
rollout.config-list[2].whitelisting-for = uthNtuCache

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = -1
rollout.config-list[3].whitelisting-for = upiPassbookListingCache

detailPageApiCachingEnabled = true
isNtuCacheEnabled = false
isUpiPassbookCacheEnabled = false


rollout.config-list[4].percentage = 0
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = *********
rollout.config-list[4].whitelisting-for = imps

rollout.config-list[5].percentage = 100
rollout.config-list[5].whitelisting-required = 1
rollout.config-list[5].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[5].whitelisting-for = DcListingRouting

rollout.config-list[6].percentage = 100
rollout.config-list[6].whitelisting-required = 1
rollout.config-list[6].user-list = *********,*********
rollout.config-list[6].whitelisting-for = UpiCcEmiCta

rollout.config-list[7].percentage = 0
rollout.config-list[7].whitelisting-required = 1
rollout.config-list[7].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********,6236193,***************
rollout.config-list[7].whitelisting-for = UthAnalyticsWhiteListing

rollout.config-list[8].percentage = 0
rollout.config-list[8].whitelisting-required = 1
rollout.config-list[8].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[8].whitelisting-for = trafficDiversionToDcForWhiteListedApis

rollout.config-list[9].percentage = 0
rollout.config-list[9].whitelisting-required = 1
rollout.config-list[9].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[9].whitelisting-for = listingPageNoMoreThanOneTrafficDiversionToDc

rollout.config-list[10].percentage = 0
rollout.config-list[10].whitelisting-required = 1
rollout.config-list[10].user-list = 1813018145
rollout.config-list[10].whitelisting-for = UserRateLimitingWhiteListing

rollout.config-list[11].percentage = 0
rollout.config-list[11].whitelisting-required = 1
rollout.config-list[11].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[11].whitelisting-for = newInvalidationVersionApplicable

#metadata api Oauth Call
oauth.call.enable.in.metadata.api = false
springdoc.api-docs.path=/int/v3/api-docs

#ForEnabling Req Money Cta
enable.requestMoney.cta = true

#For routing to Dc cluster.
isListingRoutingEnabled = true
isDetailRoutingEnabled = true

#Maximum Days Diff for tagging a txn.
maxAllowedDayDiffForTagging = 30
ipoMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9pcG8ifQ==
manage.automatic.payment.cta.base.deeplink =  paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZX0sInBhdGgiOiIvIy9tdC9hdXRvbWF0aWMtcGF5bWVudHMvbWFuZGF0ZS1kZXRhaWxzIn0=&umn=
recurringMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9tYW5kYXRlcyJ9

#uth.cst.txn.history.secret.key = uthcst#jwt@12345

showTagEnable = true
tagsUpdationEnable = true
ondc.verticalId = 204

uth.user.rate.limit.per.hour = 360
uth.user.rate.limit.hash.algo = SHA256
uth.user.rate.limit.enable = true

min-amount-limit-for-amount-range-filter = 0
max-amount-limit-for-amount-range-filter = 1000000

repeat-payment-filter-enabled = false
status-wise-custom-message-enabled-in-detail-page = true
#<br><br> is added in below message to provide 2 line breaks on App
deemed-txn-msg = This payment is pending because the receiver's bank is facing issues in accepting the payment.<br><br>Don't worry, your money is safe. We are continuously checking the status of your payment and request you to not make multiple payments. Banks may take upto 3-5 working days to confirm the final status of your payment. In case this payment fails, your bank will automatically refund money to your account.
white-listed-apis-for-traffic-diversion.on.rate.limit =
white-listed-apis-for-traffic-diversion = search,detail
isTrafficDiversionEnabled = false
pushToKafkaForAuditEnabled = true
minTimeDiffForDualWriteAuditMs = 4000
dualWriteAuditStartDate = 2023-10-28 00:00:00.000 +0530
amount-range-filter-enabled = true
uth.user.daily.rate.limit.enable = false
uth.user.daily.rate.limit.threshold = 10
uth.user.rate.limit.apis.blacklisted = /payments-history-v2/ext/v1/metaData
uth.downstream.auth.circuit.breaker.config = {\
  "refreshPeriod":"60",\
  "limit":"1000"}

uth.downstream.auth.circuit.breaker.enable = false
uth.downstream.auth.circuit.breaker.failure.httpcodes = 500,502,503,504

txn.stream.lag.based.tracking.feature.enabled           = false
txn.stream.lag.based.txn.details.rejections.enabled     = false
txn.details.source.txn.stream.max.lag.rejection.buffer.seconds   = 1

pth-pth.consumer.service-secret = PTH_CONSUMER_SERVICE_SECRET
statement-service-es-fetch-page-size = 10
statement-service-disabled = false

cta.enable-view-history-deeplink-for-ofus-merchant-txns = false
cta.enable-view-history-deeplink-for-vpa2account-txns = false
cta.enable-view-history-deeplink-for-vpa2vpa-txns = false
cta.enable-view-history-deeplink-for-vpa2vpa-3p-txns = false

cta.enable-view-history-for-onus-txns = true
searchApi.setUpdatedDate.onlyWhenDiffIsWithinXDays = 7
cta.view-history-deeplink-for-onus-txns = paytmmp://mini-app?aId=840e7060809249e0a9b4a50a7b86388f&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvcGIvaGlzdG9yeS1saXN0In0=
internal_localization_api_call_enabled = false

mandate-history-parent-es-alias = mandate_info_alias
mandate-history-child-es-alias = mandate_activity_alias
mandate.history.cst.secret = *********
mandate-history-from-date = 2022-08-01 00:00:00.000 +0530

appSideCache.latestInvalidateVersion = 13

mandate-journey-api-es-fetch-limit = 100

upi-lite-filter-extra-handling-enabled = false

blacklisted.apis.for.filters = payments-history-v2.ext.v1.ping,payments-history-v2.ext.v1.health,pth.ext.v1.ping,pth.ext.v1.health

upi-cc-filter-from-date = 2024-10-01 00:00:00.000 +0530

onus-vertical-filter-from-date = 2024-11-01 00:00:00.000 +0530
vertical.name.to.ids.mapping={\
  "Recharges & Bill Payments":[4,17,56,71,76,84,86,87,90,105,155],\
  "Gift Cards & Deals":[5,66,85,174],\
  "Travel":[26,64,60,72,167,126,181],\
  "Movies & Events":[70,40,55,73,74],\
  "Shopping":[2,6,7,8,9,10,11,12,13,14,15,18,19,20,22,23,24,25,30,31,33,34,35,36,37,38,41,42,43,44,45,46,47,49,50,51,53,58,63,67,68,69,77,78,80,99,107,117,118,125,127,129,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,156,157,159,162,163,166,169,170,171,178,182,186,204],\
  "Insurance":[79,172,173],\
  "Paytm Gold":[82,83,91],\
  "Paytm Stores":[120,180,190,191,194]\
  }

view_history_cta_enabled_for_vpa_to_account_txn = true
view_history_cta_enabled_for_vpa_to_vpa_txn = true
view_history_cta_enabled_for_vpa_to_3p_vpa_txn = true
view_history_cta_enabled_for_third_party_merchant_txn = true
view_history_cta_enabled_for_ofus_merchant_txn = true
view_history_cta_enabled_for_onus_merchant_txn = true

view_history_cta_deeplink_for_vpa_to_account_txn = paytmmp://chat?featuretype=start_chat&userType=BANK&bankName=$bankName&accRefId=$accRefId&maskedAccNo=$maskedAccountNo&source=passbook_view_history
view_history_cta_deeplink_for_vpa_to_vpa_txn = paytmmp://chat?featuretype=start_chat&userType=CUSTOMER&custId=$custId &custName=$name&source=passbook_view_history
view_history_cta_deeplink_for_vpa_to_3p_vpa_txn = paytmmp://chat?featuretype=start_chat&userType=VPA&vpa=$vpa&txnCategory=VPA2VPA&source=passbook_view_history
view_history_cta_deeplink_for_third_party_merchant_txn = paytmmp://chat?featuretype=start_chat&userType=VPA&vpa=$vpa&txnCategory=VPA2MERCHANT&source=passbook_view_history
view_history_cta_deeplink_for_ofus_merchant_txn = paytmmp://chat?featuretype=start_chat&userType=MERCHANT&mid=$merchantId&name=$name&source=passbook_view_history
view_history_cta_deeplink_for_onus_merchant_txn = paytmmp://mini-app?aId=7ee85e9b76c544f59ccc0c8b5fcdd655&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2luZGV4Lmh0bWwjL3BiL2hpc3RvcnktbGlzdCJ9&verticalId=$verticalId

autoTagging.details.aerospike.set.name = auto_tagging_details_set
tagging.auto.tagging.enabled = true
# Expiry time in secs or -1 means no expiry
autoTagging.details.aerospike.expiry.time.in.secs = -1
autoTagging.aerospike.namespace                = pth
autoTagging.aerospike.host-name                = localhost
autoTagging.aerospike.port                     = 3000

tagging.user.tags.summary.from.date.in.days = 180
tagging.user.tags.summary.use.secondary.es = true
tagging.user.tags.summary.max.tags.size = 100
tagging.user.tags.summary.page.size = 100

jwt.verification.supported.clients = UPI,PPBL,CST_HOME,pth_consumer_service,CST,TPAP_PANEL,UPI_SWITCH