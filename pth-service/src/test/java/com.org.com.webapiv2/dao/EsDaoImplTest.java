package com.org.com.webapiv2.dao;

import com.org.com.webapiv2.util.PropertiesReader;
import com.org.com.webapiv2.dao.impl.EsDaoImpl;
import com.org.com.webapiv2.dto.upiSearch.EsRepoResponseDto;
import com.org.com.webapiv2.monitoring.MetricAgent;
import com.org.com.webapiv2.repository.IEsRepo;
import com.org.com.webapiv2.util.TestUtils;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.cache.AerospikeCacheClient;
import com.org.panaroma.web.cache.UserRateLimiterAerospikeCacheClient;
import com.org.panaroma.web.cache.UserRateLimiterDailyAerospikeCacheClient;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.FileNotFoundException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.script.*")
@PrepareForTest({ SearchRequest.class, Utility.class })
public class EsDaoImplTest {

	@MockBean
	AerospikeCacheClient aerospikeCacheClient;

	@MockBean
	UserRateLimiterAerospikeCacheClient userRateLimiterAerospikeCacheClient;

	@MockBean
	UserRateLimiterDailyAerospikeCacheClient userRateLimiterMinuteWiseAerospikeCacheClient;

	@InjectMocks
	private EsDaoImpl esDao;

	@Mock
	private ResultsMapper resultsMapper;

	@Mock
	private IEsRepo esRepo;

	@Mock
	MetricAgent metricAgent;

	private PropertiesReader propertiesReader;

	@Before
	public void init() throws FileNotFoundException {
		propertiesReader = new PropertiesReader("src/main/resources/application-local.properties");
		ReflectionTestUtils.setField(esDao, "indexAliasName", "index");
		ReflectionTestUtils.setField(esDao, "indexNamePrefix", propertiesReader.getValString("index.name.prefix"));
	}

	@Test
	public void getResponseTxnTest() throws Exception {

		SearchContext searchContext = (SearchContext) TestUtils.getObjectFromFile("SearchContextForUpi",
				SearchContext.class);
		SearchResponse searchResponse = (SearchResponse) TestUtils.getObjectFromFile("SearchResponseForUpiTxnHistory",
				SearchResponse.class);
		PaginationParams paginationParams = PaginationParams.builder()
			.transactionDateEpoch("1640103096734")
			.paginationStreamSource("3")
			.paginationTxnId("PTM3fd4b509f2e34c9fb0b6ee1dada88737")
			.pageNo(1)
			.build();

		PowerMockito.when(esRepo.getSearchResponse(any())).thenReturn(searchResponse);

		EsRepoResponseDto repoResponseDto = esDao.getResponseTxn(searchContext, paginationParams);

		assertEquals(1, repoResponseDto.getTotalHits());
	}

	@Test
	public void getTagsFromEsTest() {

		try {
			SearchContext searchContext = (SearchContext) TestUtils.getObjectFromFile("SearchContext",
					SearchContext.class);
			SearchResponse searchResponse = (SearchResponse) TestUtils.getObjectFromFile("SearchResponse",
					SearchResponse.class);
			TransformedTransactionHistoryDetail detail = (TransformedTransactionHistoryDetail) TestUtils
				.getObjectFromFile("TransformedTransactionHistoryDetail", TransformedTransactionHistoryDetail.class);

			PowerMockito.when(esRepo.getSearchResponse(any())).thenReturn(new SearchResponse());

			assertThrows(NullPointerException.class, () -> esDao.getTxnFromEs(searchContext, "1653311648"));

			PowerMockito.when(esRepo.getSearchResponse(any())).thenReturn(searchResponse);
			PowerMockito.when(resultsMapper.mapSearchHit(any(), any())).thenReturn(detail);

			assertEquals(detail.getTags(), esDao.getTxnFromEs(searchContext, "1653311648"));
		}
		catch (Exception e) {
			fail("Something went wrong with getTagsFromEsTest");
		}
	}

}
