server.port                                        = 8080
build.timestamp                                    = @timestamp@
app.name                                           = @project.artifactId@
app.version                                        = @project.version@
git.commit.id                                      = 122
git.branch                                         = master
logging.config                                     = classpath:log4j2.json

server.tomcat.max-connections                      = 1000
server.tomcat.max-threads                          = 500
server.tomcat.threads.max                          = 500


server.tomcat.accesslog.enabled                    = true
server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
server.tomcat.accesslog.directory                  = /log/payment-transaction-history-service
server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
server.tomcat.accesslog.rotate                     = true
server.tomcat.accesslog.rename-on-rotate           = true
server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
server.tomcat.accesslog.buffered                   = false
server.tomcat.accesslog.request-attributes-enabled = true
server.tomcat.accesslog.maxDays                    = 3

elastic-search-index                               = payment_history_alias
updated.index.month.list                           = payment-history-07-2020,payment-history-08-2020,payment-history-09-2020,payment-history-10-2020,payment-history-11-2020
index.name.prefix                                  = payment-history
upi-udir-index.name.prefix                         = uth-cst
es-socket-timeout                                  = 2000
es-max-retry-timeout                               = 2000
es-connect-timeout                                 = 1000
es-connect-request-timeout                         = 1000

es-host-list                                       = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es-port                                            = 9200

#V2 ES Configuration
es-v2-host-list                                    = uth-es-app2-nlb-85972d6b09d6e362.elb.ap-south-1.amazonaws.com
es-v2-port                                         = 9200
txn.tags.table=txn_tags_data-pth
user.tags.table=user_tag_data-pth
userId.updatedTimeStamp.index                      = userId-updatedTimeStamp-index
sysyem.tags.table                                  = system_tag_data-pth
tagging.max.tags.count.per.txn                    = 1
tagging.tag.max.length                             = 30
tagging.tag.min.length                             = 3
tagging.user.tags.limit                           = 100

#Ocl OAuth
# >>>>>>need to change these
external-integration-service-http-url = http://external-integration.default:80
ocl.oauth.service.base.url                = https://oauth.paytm.com
ocl.oauth.client.id                       = payment-transaction-history-prod
ocl.oauth.client.secret                   = ${PTH_OAUTHCONFIG_CLIENTSECRET}

use-bank-oauth                            = false

#bank-oauth
bank.oauth.service.base.url                        = https://oauth-internal.orgk.com
bank.oauth.service.url.category                    = /bank-oauth/ext
bank.oauth.client.id                               = UNIFIED_TRANSACTION_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = UNIFIED_TRANSACTION_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET
bank.oauthClient.connectionProvider.maxConnections = 500
bank.oauthClient.connectionProvider.acquireTimeout = 45000
bank.oauthClient.connectionProvider.maxIdleTime    = 5
bank.oauth.client.connection.timeout               = 2000
bank.oauth.read.timeout                            = 2000
bank.oauth.socket.timeout                          = 2000

# >>>>>>need to change these
#Prometheus Monitoring Constants
prometheus.explorer                                = UNIFIED_HISTORY_SERVICE
prometheus.hostname                                = localhost
prometheus.port                                    = 8130

jwt.varification.enable                            = true
uth.upi.txn.history.secret.key                     = ${PTH_UPI_SECRET_KEY}
uth.pms.txn.history.secret.key                     = ${PAYMENT_TRANSACTION_HISTORY_PMS_SECRET_KEY}
pth.upi.switch.secret.key                          = ${PTH_UPI_SWITCH_SECRET_KEY}

envBased.jacocoSupport                             = true

oauth.token.evaluation.skip = false

static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/
generic-tag-logo = tags.png

map.of.uth.category.logo.url          = {\
                                            '1': 'https://tpap-logo.paytm.com/uth/images/category-logo/DAIRY_AND_GROCERIES.png',\
                                            '2': 'https://tpap-logo.paytm.com/uth/images/category-logo/SERVICES.png',\
                                            '3': 'https://tpap-logo.paytm.com/uth/images/category-logo/FUEL_AND_AUTOMOBILE.png',\
                                            '4': 'https://tpap-logo.paytm.com/uth/images/category-logo/Food_And_Beverages.png',\
                                            '5': 'https://tpap-logo.paytm.com/uth/images/category-logo/Shoppings.png',\
                                            '6': 'https://tpap-logo.paytm.com/uth/images/category-logo/PERSONAL_AND_HEALTH_CARE.png',\
                                            '7': 'https://tpap-logo.paytm.com/uth/images/category-logo/Taxi_And_Transportations.png',\
                                            '8': 'https://tpap-logo.paytm.com/uth/images/category-logo/TRAVEL.png',\
                                            '9': 'https://tpap-logo.paytm.com/uth/images/category-logo/RECHARGES_AND_BILL_PAYMENT.png',\
                                            '10': 'https://tpap-logo.paytm.com/uth/images/category-logo/ENTERTAINMENT.png',\
                                            '11': 'https://tpap-logo.paytm.com/uth/images/category-logo/Transfers.png',\
                                            '12': 'https://tpap-logo.paytm.com/uth/images/category-logo/DEVOTION.png',\
                                            '13': 'https://tpap-logo.paytm.com/uth/images/category-logo/Financial_Services.png',\
                                            '14': 'https://tpap-logo.paytm.com/uth/images/category-logo/Education.png',\
                                            '15': 'https://tpap-logo.paytm.com/uth/images/category-logo/Interest_Credited.png',\
                                            '16': 'https://tpap-logo.paytm.com/uth/images/category-logo/Cashback_Received.png',\
                                            '17': 'https://tpap-logo.paytm.com/uth/images/category-logo/Salary_Credited.png',\
                                            '18': 'https://tpap-logo.paytm.com/uth/images/category-logo/Others.png',\
                                            '19': 'https://tpap-logo.paytm.com/uth/images/category-logo/EDUCATIONAL.png',\
                                            '20': 'https://tpap-logo.paytm.com/uth/images/category-logo/CLOTHING_SHOES_JEWELRY.png',\
                                            '21': 'https://tpap-logo.paytm.com/uth/images/category-logo/SWEETS_BAKERY.png',\
                                            '22': 'https://tpap-logo.paytm.com/uth/images/category-logo/BOOKS_STATIONARY.png',\
                                            '23': 'https://tpap-logo.paytm.com/uth/images/category-logo/MOBILE_ACCESSORIES.png',\
                                            '24': 'https://tpap-logo.paytm.com/uth/images/category-logo/SMALL_SHOP.png',\
                                            '25': 'https://tpap-logo.paytm.com/uth/images/category-logo/ELECTRONICS.png',\
                                            '26': 'https://tpap-logo.paytm.com/uth/images/category-logo/INDUSTRIAL_SCIENTIFIC.png',\
                                            '27': 'https://tpap-logo.paytm.com/uth/images/category-logo/LEGAL_FINANCIAL.png',\
                                            '28': 'https://tpap-logo.paytm.com/uth/images/category-logo/LIFESTYLE.png',\
                                            '30': 'https://tpap-logo.paytm.com/uth/images/category-logo/gov_services.png',\
                                            '31': 'https://tpap-logo.paytm.com/uth/images/category-logo/software.png'\
                                        }
#time in seconds - 4 hr
cache.time.for.month.spent.data = 14400
cache.time.for.months.agg.spent.data = 14400
cache.time.for.analytics.agg.and.break.up = 14400

# Cache time for rewind data at client end (In Hours)
cache.time.for.rewind.data = 24

spend.index.name.prefix = spend-history
spend-history-alias = spend_history_alias
#From Date Jan 2022 (MM-yyyy)
# >>>>>>need to change these
analytics-from-month = 11-2022
analytics-dataBreakUp-page-size = 20

spend_analytics.source.table = user_analytics_month_agg_data-uth

kafka.target-v2-list[0].kafka-client-name               = USERID_FETCHER_KAFKA_CLIENT
kafka.target-v2-list[0].kafka-producer-key              = USERID_FETCHER_KAFKA_CLIENT_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_userid_fetcher_data
kafka.target-v2-list[0].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = SPEND_DOC_CREATOR_KAFKA_CLIENT_KEY
kafka.target-v2-list[1].kafka-producer-key              = SPEND_DOC_CREATOR_KAFKA_CLIENT_PRODUCER
kafka.target-v2-list[1].topic                           = uth_spend_user_kafka_config_data
kafka.target-v2-list[1].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 5000

kafka.target-v2-list[2].kafka-client-name               = spend_back_filling
kafka.target-v2-list[2].kafka-producer-key              = spend_back_filling_CLIENT_PRODUCER
kafka.target-v2-list[2].topic                           = middleware_transaction_history_back_fill_data
kafka.target-v2-list[2].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].linger-ms                       = 15
kafka.target-v2-list[2].request-timeout-ms              = 5000

kafka.target-v2-list[3].kafka-client-name               = SPEND_DOC_CREATOR_KAFKA_CLIENT_KEY_DC
kafka.target-v2-list[3].kafka-producer-key              = SPEND_DOC_CREATOR_KAFKA_CLIENT_PRODUCER_DC
kafka.target-v2-list[3].topic                           = middleware_transaction_history_back_fill_data
kafka.target-v2-list[3].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[3].batch-size-bytes                = 3000
kafka.target-v2-list[3].linger-ms                       = 15
kafka.target-v2-list[3].request-timeout-ms              = 5000

kafka.target-v2-list[4].kafka-client-name               = SPEND_DOC_CREATOR_KAFKA_CLIENT_KEY_DC_V1
kafka.target-v2-list[4].kafka-producer-key              = SPEND_DOC_CREATOR_KAFKA_CLIENT_PRODUCER_DC_V1
kafka.target-v2-list[4].topic                           = middleware_transaction_history_retry_back_fill_data
kafka.target-v2-list[4].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[4].batch-size-bytes                = 3000
kafka.target-v2-list[4].linger-ms                       = 15
kafka.target-v2-list[4].request-timeout-ms              = 5000

kafka.target-v2-list[5].kafka-client-name               = API_HIT_CLIENT_1
kafka.target-v2-list[5].kafka-producer-key              = API_HIT_CLIENT_1
kafka.target-v2-list[5].topic                           = uth_year_in_rewind_cache_v1
kafka.target-v2-list[5].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[5].batch-size-bytes                = 3000
kafka.target-v2-list[5].linger-ms                       = 15
kafka.target-v2-list[5].request-timeout-ms              = 5000

kafka.target-v2-list[6].kafka-client-name               = API_HIT_CLIENT_2
kafka.target-v2-list[6].kafka-producer-key              = API_HIT_CLIENT_2
kafka.target-v2-list[6].topic                           = uth_year_in_rewind_cache_v2
kafka.target-v2-list[6].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[6].batch-size-bytes                = 3000
kafka.target-v2-list[6].linger-ms                       = 15
kafka.target-v2-list[6].request-timeout-ms              = 5000

kafka.target-v2-list[7].kafka-client-name               = SAPI_HIT_CLIENT_3
kafka.target-v2-list[7].kafka-producer-key              = API_HIT_CLIENT_3
kafka.target-v2-list[7].topic                           = uth_year_in_rewind_cache_v3
kafka.target-v2-list[7].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[7].batch-size-bytes                = 3000
kafka.target-v2-list[7].linger-ms                       = 15
kafka.target-v2-list[7].request-timeout-ms              = 5000

kafka.target-v2-list[8].kafka-client-name               = SAPI_HIT_CLIENT_4
kafka.target-v2-list[8].kafka-producer-key              = API_HIT_CLIENT_4
kafka.target-v2-list[8].topic                           = uth_year_in_rewind_cache_v4
kafka.target-v2-list[8].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[8].batch-size-bytes                = 3000
kafka.target-v2-list[8].linger-ms                       = 15
kafka.target-v2-list[8].request-timeout-ms              = 5000

kafka.target-v2-list[12].kafka-client-name               = uthAnalyticsKafkaClient
kafka.target-v2-list[12].kafka-producer-key              = uthAnalyticsProducer
kafka.target-v2-list[12].topic                           = uth-dwh-ingestion-topic
kafka.target-v2-list[12].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[12].batch-size-bytes                = 3000
kafka.target-v2-list[12].linger-ms                       = 1
kafka.target-v2-list[12].request-timeout-ms              = 100

kafka.target-v2-list[14].kafka-client-name               = api_response_cache_population
kafka.target-v2-list[14].kafka-producer-key              = api_response_cache_population_CLIENT_PRODUCER
kafka.target-v2-list[14].topic                           = uth_year_in_rewind_cache_v1
kafka.target-v2-list[14].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[14].batch-size-bytes                = 3000
kafka.target-v2-list[14].linger-ms                       = 15
kafka.target-v2-list[14].request-timeout-ms              = 5000

kafka.target-v2-list[15].kafka-client-name               = toggle-visibility
kafka.target-v2-list[15].kafka-producer-key              = toggle-visibility_CLIENT_PRODUCER
kafka.target-v2-list[15].topic                           = pth_toggle_visibility_data
kafka.target-v2-list[15].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[15].batch-size-bytes                = 3000
kafka.target-v2-list[15].linger-ms                       = 15
kafka.target-v2-list[15].request-timeout-ms              = 5000

kafka.target-v2-list[16].kafka-client-name               = TXN_TAG_KAFKA_CLIENT
kafka.target-v2-list[16].kafka-producer-key              = TXN_TAG_KAFKA_CLIENT_CLIENT_PRODUCER
kafka.target-v2-list[16].topic                           = pth_txn_tags_data
kafka.target-v2-list[16].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[16].batch-size-bytes                = 3000
kafka.target-v2-list[16].linger-ms                       = 15
kafka.target-v2-list[16].request-timeout-ms              = 5000


#1hour
analytic.config.cron.delay.in.milli = 3600000
#15 min
analytic.config.cron.lock.at.most.in.milli = 900000

analytic.scheduler.enabled.for.whitelisted.users.only = true
whitelisted.custids.for.analytics = 19568374,********,1059237905,153618874,*********,957604,6257441,56612796,317040440,7842086,191733975,1222490353,1502241615,3199968,420071448,581715142,27210811,893354,29111688,230076407,594493697,191685349,2784010,349174251,6236193,486443710,6529780,29670233,243232292,287665477,219858099,24971888,254133495,518726118,419781662,*********,9944379,512796856,68336144,47975662,1400206311,342260451,201278171,201791995,230041375,2564256,*********,132499998,296230701,*********,*********,*********,********,9268107,3770465,*********,1490589582,*********,*********,*********,267806444,**********,424941292,240546394,********,136465278,42129353,********,*********,*********,*********,738496,*********,********,1368664650,********,18011324,22237406,78349274,269241912,293383009,27044578,232113247,157021860,18365600,291199149,228146609,550288448,308967198,18480851,1284033,********,108768010,1251621668,25209101,147547192,144760378,1237163713,217145117,293275297,3072317,30809950,25188981,1574406,1476173203,5445595,1141567389,585426764,1147729658,*********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********,1830830837,1383841722,423492510,1479674579,5238511,495910024,55963648,1284347,311680506,217570033,1310394,167391292,21336322,1195317475,291466963,1175315007,330528549,1416564493,227241735,1194270239,30009242,1032714351,1243823346,145831564,294876831,249702262,582637070,1199277624,576599582,245219894,23966029,1185595049,1002577556,303949014,15577609,1350008752,1196189794,488556860,437236362,428213990,1204690964,1136602049,256978359,1184860434,1182369218,1380866871,1170061,1001891860,1297379853,30927120,112525454,1255927086,227776525,88018292,1156189866,679187971,1159929074,1191607769,1197285672,307098214,132990992,1175310601,27667028,1144292352,251450578,1392476388,558457242,138604100,311477762,475154816,1183367693,1174599949,681467605,27530283,1033175770,1185814119,187887579,588620372,1013934927,1153859583,531218630,560527798,1181017027,255979703,612737708,589549310,1275018336,302825736,397257394,517434924,1408189245,189952369,1005222697,1135762328,292903779,24062482,11553558,1179139496,1002467053,1184896575,142227588,433908936,1206861812,1259848719,22106833,605341957,1254938635,345264713,1387361839,350530205,342136775,91656778,1197498674,1269530121,452519806,1140913287,287753231,230782731,316007628,341030201,1215024435,1285163169,1018873698,1354666444,99001044,1607014,1262472564,1174243682,1228823223,209126223,1147121687,224081463,2610876,291742131,462749422,411348640,1009034969,250768708,313714252,306056680,1106536065,45430856,1169372683,1135772011,240892512,1093015375,300808479,548963724,382353050,16814568,564030982,311251718,1033346919,1185314433,347046839,25895865,1054407551,1185615072,346162591,233750937,1184948357,307707404,1206572542,419728806,1214000923,1166952443,505537108,667861349,1304303915,1446911837,304662370,1170286928,1198316914,1274144037,397622530,198430405,520560068,1034842834,555123240,658245107,305399594,329441893,423143734,1153256977,1309887733,2939882,358568536,494463384,1190641946,1106037848,30039003,1166878365,1290008463,1021030524,558108434,40832638,1180949181,1005879564,1183948274,186835455,1183797992,27597456,571293604,1197976720,1304219388,1006274057,385272432,1344008333,1139350955,305890430,554792984,1019546858,244415762,302229186,346274299,1202966225,307361604,456333496,1248214662,231213247,1002333248,1148472693,429871206,335125907,1304578397,1390985303,313223484,673898515,11834554,577223598,1197078765,1296147822,290183857,1371000588,239167006,190286793,1039843354,137439462,219418173,1089761692,323506452,616846934,1035883563,1179132671,1072564282,405776460,180261669,1133154836,292795577,5661338,579303802,1286624471,1167541560,12743422,316783016,1394391095,1016639270,270811376,586721810,307959776,1253167843,327147007,1075529972,155721436,286085187,1188832917,1256880113,288740665,1359281192,246438022,1350068727,1158149065,1202216532,414256040,69126040,566774514,1176632794,1027448990,1128241696,1346843280,18361038,587387770,1283706834,499629994,441714228,1386555435,1180467691,23065016,1176430,1327391190,1060453949,297104087,1149634777,431186084,1723342550,411708466,1188866234,1151560431,239141326,308534450,264668977,596614367,1163062452,1199202148,257583049,22359048,1416220085,1189962751,1136339225,228658379,576009532,1235196000,221468665,1332194140,356124986,1215712422,66620494,517603542,247800034,1042759530,1195673163,503784582,1189215989,441864438,1349670091,1240065286,1034415302,1002084750,1168252368,1285261341,1297433982,1329182044,226156177,1131474568,290771763,25127217,1284905,1255871571,1192540139,91136252,19811232,1017841652,1183925087,1261165563,346360923,185971907,1320582753,421342748,186062185,241580316,300112657,2265174,1011965106,1197369448,1089240642,316860800,1299914537,1188338825,1202981255,1381745935,1276504787,1190360844,10378535,1165490839,1198984845,546081874,319844922,184464229,649916631,1198515034,656670917,1142076438,1056234145,1314194027,330921057,300933593,571031642,1335869282,1171648359,56429924,1095383110,1103095479,1129541703,303148144,1359147978,172835707,646312387,307388562,572410514,1038135113,1178131130,1188327589,1155997326,146839722,1207970111,204015151,1257422428,652021967,6367918,1189305719,1094705799,1414346603,28977770,433373814,29881364,119605092,1146984798,135540388,1274129812,287441765,407116168,1165836907,343525735,1184053893,1240378584,619186868,1191104732,363370332,1065963490,1199967538,1357166668,643783945,1116195295,1129358651,544905860,286327715,1185128014,503383712,1196997754,1230593893,599217903,1262112224,1194653320,312805476,1303065998,1369588242,1209260286,1254766649,1200148202,1249655514,1395652427,404444998,1153761673,1376608160,1151399448,56811708,1150367573,1193501904,523664854,1226425253,1185762014,270903406,1418986124,261315853,672424343,139251098,120435812,1005233029,1021156335,293823621,615647740,1157590759,1304697389,532628764,1411810686,1319499218,1196210110,1109908811,243320654,1122394458,386724604,1293973195,1302630040,394051962,603317763,551619352,1417067710,1270979466,9419235,1068666251,552096950,1186527024,1255705271,650189267,295093861,1341905044,355255872,1042797739,590810886,1204826119,1218030999,231105007,1260434861,664759711,350562963,123611146,294165093,1185867282,1176593679,452407788,1349174361,650409965,251170092,1048590949,39997354,5191493,598780075,670735213,332485043,1167233134,674695143,565875894,603770603,650057389,1120989561,1397910062,1005224491,340683865,1146944848,1246271519,1102097889,448936230,1259883968,1135832679,561687796,457567492,589472010,197154429,252469628,1133227114,1066027513,1166126556,262798297,1385090923,1186225401,1420498312,1108365254,1275451727,18029336,186668233,506075544,1336821011,1108168740,653807215,187706967,1181732811,197884953,95962592,6014735,117089488,1395787207,2461554,22498179,19397336,251240380
spend_analytics.status.table = analytic_status_data-uth
shedlock.table = Shedlock-uth

rollout.config-list[0].percentage = 0
rollout.config-list[0].whitelisting-required = 1
rollout.config-list[0].user-list = 19568374,********,1059237905,153618874,*********,957604,6257441,56612796,317040440,7842086,191733975,1222490353,1502241615,3199968,420071448,581715142,27210811,893354,29111688,230076407,594493697,191685349,2784010,349174251,6236193,486443710,6529780,29670233,243232292,287665477,219858099,24971888,254133495,518726118,419781662,*********,9944379,512796856,68336144,47975662,1400206311,342260451,201278171,201791995,230041375,2564256,*********,132499998,296230701,*********,*********,*********,********,9268107,3770465,*********,1490589582,*********,*********,*********,267806444,**********,424941292,240546394,********,136465278,42129353,********,*********,*********,*********,738496,*********,********,1368664650,********,18011324,22237406,78349274,269241912,293383009,27044578,232113247,157021860,18365600,291199149,228146609,550288448,308967198,18480851,1284033,********,108768010,1251621668,25209101,147547192,144760378,1237163713,217145117,293275297,3072317,30809950,25188981,1574406,1476173203,5445595,1141567389,585426764,1147729658,*********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********,1830830837,1383841722,423492510,1479674579,5238511,495910024,55963648,1284347,311680506,217570033,1310394,167391292,21336322,1195317475,291466963,1175315007,330528549,1416564493,227241735,1194270239,30009242,1032714351,1243823346,145831564,294876831,249702262,582637070,1199277624,576599582,245219894,23966029,1185595049,1002577556,303949014,15577609,1350008752,1196189794,488556860,437236362,428213990,1204690964,1136602049,256978359,1184860434,1182369218,1380866871,1170061,1001891860,1297379853,30927120,112525454,1255927086,227776525,88018292,1156189866,679187971,1159929074,1191607769,1197285672,307098214,132990992,1175310601,27667028,1144292352,251450578,1392476388,558457242,138604100,311477762,475154816,1183367693,1174599949,681467605,27530283,1033175770,1185814119,187887579,588620372,1013934927,1153859583,531218630,560527798,1181017027,255979703,612737708,589549310,1275018336,302825736,397257394,517434924,1408189245,189952369,1005222697,1135762328,292903779,24062482,11553558,1179139496,1002467053,1184896575,142227588,433908936,1206861812,1259848719,22106833,605341957,1254938635,345264713,1387361839,350530205,342136775,91656778,1197498674,1269530121,452519806,1140913287,287753231,230782731,316007628,341030201,1215024435,1285163169,1018873698,1354666444,99001044,1607014,1262472564,1174243682,1228823223,209126223,1147121687,224081463,2610876,291742131,462749422,411348640,1009034969,250768708,313714252,306056680,1106536065,45430856,1169372683,1135772011,240892512,1093015375,300808479,548963724,382353050,16814568,564030982,311251718,1033346919,1185314433,347046839,25895865,1054407551,1185615072,346162591,233750937,1184948357,307707404,1206572542,419728806,1214000923,1166952443,505537108,667861349,1304303915,1446911837,304662370,1170286928,1198316914,1274144037,397622530,198430405,520560068,1034842834,555123240,658245107,305399594,329441893,423143734,1153256977,1309887733,2939882,358568536,494463384,1190641946,1106037848,30039003,1166878365,1290008463,1021030524,558108434,40832638,1180949181,1005879564,1183948274,186835455,1183797992,27597456,571293604,1197976720,1304219388,1006274057,385272432,1344008333,1139350955,305890430,554792984,1019546858,244415762,302229186,346274299,1202966225,307361604,456333496,1248214662,231213247,1002333248,1148472693,429871206,335125907,1304578397,1390985303,313223484,673898515,11834554,577223598,1197078765,1296147822,290183857,1371000588,239167006,190286793,1039843354,137439462,219418173,1089761692,323506452,616846934,1035883563,1179132671,1072564282,405776460,180261669,1133154836,292795577,5661338,579303802,1286624471,1167541560,12743422,316783016,1394391095,1016639270,270811376,586721810,307959776,1253167843,327147007,1075529972,155721436,286085187,1188832917,1256880113,288740665,1359281192,246438022,1350068727,1158149065,1202216532,414256040,69126040,566774514,1176632794,1027448990,1128241696,1346843280,18361038,587387770,1283706834,499629994,441714228,1386555435,1180467691,23065016,1176430,1327391190,1060453949,297104087,1149634777,431186084,1723342550,411708466,1188866234,1151560431,239141326,308534450,264668977,596614367,1163062452,1199202148,257583049,22359048,1416220085,1189962751,1136339225,228658379,576009532,1235196000,221468665,1332194140,356124986,1215712422,66620494,517603542,247800034,1042759530,1195673163,503784582,1189215989,441864438,1349670091,1240065286,1034415302,1002084750,1168252368,1285261341,1297433982,1329182044,226156177,1131474568,290771763,25127217,1284905,1255871571,1192540139,91136252,19811232,1017841652,1183925087,1261165563,346360923,185971907,1320582753,421342748,186062185,241580316,300112657,2265174,1011965106,1197369448,1089240642,316860800,1299914537,1188338825,1202981255,1381745935,1276504787,1190360844,10378535,1165490839,1198984845,546081874,319844922,184464229,649916631,1198515034,656670917,1142076438,1056234145,1314194027,330921057,300933593,571031642,1335869282,1171648359,56429924,1095383110,1103095479,1129541703,303148144,1359147978,172835707,646312387,307388562,572410514,1038135113,1178131130,1188327589,1155997326,146839722,1207970111,204015151,1257422428,652021967,6367918,1189305719,1094705799,1414346603,28977770,433373814,29881364,119605092,1146984798,135540388,1274129812,287441765,407116168,1165836907,343525735,1184053893,1240378584,619186868,1191104732,363370332,1065963490,1199967538,1357166668,643783945,1116195295,1129358651,544905860,286327715,1185128014,503383712,1196997754,1230593893,599217903,1262112224,1194653320,312805476,1303065998,1369588242,1209260286,1254766649,1200148202,1249655514,1395652427,404444998,1153761673,1376608160,1151399448,56811708,1150367573,1193501904,523664854,1226425253,1185762014,270903406,1418986124,261315853,672424343,139251098,120435812,1005233029,1021156335,293823621,615647740,1157590759,1304697389,532628764,1411810686,1319499218,1196210110,1109908811,243320654,1122394458,386724604,1293973195,1302630040,394051962,603317763,551619352,1417067710,1270979466,9419235,1068666251,552096950,1186527024,1255705271,650189267,295093861,1341905044,355255872,1042797739,590810886,1204826119,1218030999,231105007,1260434861,664759711,350562963,123611146,294165093,1185867282,1176593679,452407788,1349174361,650409965,251170092,1048590949,39997354,5191493,598780075,670735213,332485043,1167233134,674695143,565875894,603770603,650057389,1120989561,1397910062,1005224491,340683865,1146944848,1246271519,1102097889,448936230,1259883968,1135832679,561687796,457567492,589472010,197154429,252469628,1133227114,1066027513,1166126556,262798297,1385090923,1186225401,1420498312,1108365254,1275451727,18029336,186668233,506075544,1336821011,1108168740,653807215,187706967,1181732811,197884953,95962592,6014735,117089488,1395787207,2461554,22498179,19397336,251240380
rollout.config-list[0].whitelisting-for = ext/v1/spend-analytics/monthly

rollout.config-list[1].percentage = 0
rollout.config-list[1].whitelisting-required = 1
rollout.config-list[1].user-list = 19568374,********,1059237905,153618874,*********,957604,6257441,56612796,317040440,7842086,191733975,1222490353,1502241615,3199968,420071448,581715142,27210811,893354,29111688,230076407,594493697,191685349,2784010,349174251,6236193,486443710,6529780,29670233,243232292,287665477,219858099,24971888,254133495,518726118,419781662,*********,9944379,512796856,68336144,47975662,1400206311,342260451,201278171,201791995,230041375,2564256,*********,132499998,296230701,*********,*********,*********,********,9268107,3770465,*********,1490589582,*********,*********,*********,267806444,**********,424941292,240546394,********,136465278,42129353,********,*********,*********,*********,738496,*********,********,1368664650,********,18011324,22237406,78349274,269241912,293383009,27044578,232113247,157021860,18365600,291199149,228146609,550288448,308967198,18480851,1284033,********,108768010,1251621668,25209101,147547192,144760378,1237163713,217145117,293275297,3072317,30809950,25188981,1574406,1476173203,5445595,1141567389,585426764,1147729658,*********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********,1830830837,1383841722,423492510,1479674579,5238511,495910024,55963648,1284347,311680506,217570033,1310394,167391292,21336322,1195317475,291466963,1175315007,330528549,1416564493,227241735,1194270239,30009242,1032714351,1243823346,145831564,294876831,249702262,582637070,1199277624,576599582,245219894,23966029,1185595049,1002577556,303949014,15577609,1350008752,1196189794,488556860,437236362,428213990,1204690964,1136602049,256978359,1184860434,1182369218,1380866871,1170061,1001891860,1297379853,30927120,112525454,1255927086,227776525,88018292,1156189866,679187971,1159929074,1191607769,1197285672,307098214,132990992,1175310601,27667028,1144292352,251450578,1392476388,558457242,138604100,311477762,475154816,1183367693,1174599949,681467605,27530283,1033175770,1185814119,187887579,588620372,1013934927,1153859583,531218630,560527798,1181017027,255979703,612737708,589549310,1275018336,302825736,397257394,517434924,1408189245,189952369,1005222697,1135762328,292903779,24062482,11553558,1179139496,1002467053,1184896575,142227588,433908936,1206861812,1259848719,22106833,605341957,1254938635,345264713,1387361839,350530205,342136775,91656778,1197498674,1269530121,452519806,1140913287,287753231,230782731,316007628,341030201,1215024435,1285163169,1018873698,1354666444,99001044,1607014,1262472564,1174243682,1228823223,209126223,1147121687,224081463,2610876,291742131,462749422,411348640,1009034969,250768708,313714252,306056680,1106536065,45430856,1169372683,1135772011,240892512,1093015375,300808479,548963724,382353050,16814568,564030982,311251718,1033346919,1185314433,347046839,25895865,1054407551,1185615072,346162591,233750937,1184948357,307707404,1206572542,419728806,1214000923,1166952443,505537108,667861349,1304303915,1446911837,304662370,1170286928,1198316914,1274144037,397622530,198430405,520560068,1034842834,555123240,658245107,305399594,329441893,423143734,1153256977,1309887733,2939882,358568536,494463384,1190641946,1106037848,30039003,1166878365,1290008463,1021030524,558108434,40832638,1180949181,1005879564,1183948274,186835455,1183797992,27597456,571293604,1197976720,1304219388,1006274057,385272432,1344008333,1139350955,305890430,554792984,1019546858,244415762,302229186,346274299,1202966225,307361604,456333496,1248214662,231213247,1002333248,1148472693,429871206,335125907,1304578397,1390985303,313223484,673898515,11834554,577223598,1197078765,1296147822,290183857,1371000588,239167006,190286793,1039843354,137439462,219418173,1089761692,323506452,616846934,1035883563,1179132671,1072564282,405776460,180261669,1133154836,292795577,5661338,579303802,1286624471,1167541560,12743422,316783016,1394391095,1016639270,270811376,586721810,307959776,1253167843,327147007,1075529972,155721436,286085187,1188832917,1256880113,288740665,1359281192,246438022,1350068727,1158149065,1202216532,414256040,69126040,566774514,1176632794,1027448990,1128241696,1346843280,18361038,587387770,1283706834,499629994,441714228,1386555435,1180467691,23065016,1176430,1327391190,1060453949,297104087,1149634777,431186084,1723342550,411708466,1188866234,1151560431,239141326,308534450,264668977,596614367,1163062452,1199202148,257583049,22359048,1416220085,1189962751,1136339225,228658379,576009532,1235196000,221468665,1332194140,356124986,1215712422,66620494,517603542,247800034,1042759530,1195673163,503784582,1189215989,441864438,1349670091,1240065286,1034415302,1002084750,1168252368,1285261341,1297433982,1329182044,226156177,1131474568,290771763,25127217,1284905,1255871571,1192540139,91136252,19811232,1017841652,1183925087,1261165563,346360923,185971907,1320582753,421342748,186062185,241580316,300112657,2265174,1011965106,1197369448,1089240642,316860800,1299914537,1188338825,1202981255,1381745935,1276504787,1190360844,10378535,1165490839,1198984845,546081874,319844922,184464229,649916631,1198515034,656670917,1142076438,1056234145,1314194027,330921057,300933593,571031642,1335869282,1171648359,56429924,1095383110,1103095479,1129541703,303148144,1359147978,172835707,646312387,307388562,572410514,1038135113,1178131130,1188327589,1155997326,146839722,1207970111,204015151,1257422428,652021967,6367918,1189305719,1094705799,1414346603,28977770,433373814,29881364,119605092,1146984798,135540388,1274129812,287441765,407116168,1165836907,343525735,1184053893,1240378584,619186868,1191104732,363370332,1065963490,1199967538,1357166668,643783945,1116195295,1129358651,544905860,286327715,1185128014,503383712,1196997754,1230593893,599217903,1262112224,1194653320,312805476,1303065998,1369588242,1209260286,1254766649,1200148202,1249655514,1395652427,404444998,1153761673,1376608160,1151399448,56811708,1150367573,1193501904,523664854,1226425253,1185762014,270903406,1418986124,261315853,672424343,139251098,120435812,1005233029,1021156335,293823621,615647740,1157590759,1304697389,532628764,1411810686,1319499218,1196210110,1109908811,243320654,1122394458,386724604,1293973195,1302630040,394051962,603317763,551619352,1417067710,1270979466,9419235,1068666251,552096950,1186527024,1255705271,650189267,295093861,1341905044,355255872,1042797739,590810886,1204826119,1218030999,231105007,1260434861,664759711,350562963,123611146,294165093,1185867282,1176593679,452407788,1349174361,650409965,251170092,1048590949,39997354,5191493,598780075,670735213,332485043,1167233134,674695143,565875894,603770603,650057389,1120989561,1397910062,1005224491,340683865,1146944848,1246271519,1102097889,448936230,1259883968,1135832679,561687796,457567492,589472010,197154429,252469628,1133227114,1066027513,1166126556,262798297,1385090923,1186225401,1420498312,1108365254,1275451727,18029336,186668233,506075544,1336821011,1108168740,653807215,187706967,1181732811,197884953,95962592,6014735,117089488,1395787207,2461554,22498179,19397336,251240380
rollout.config-list[1].whitelisting-for = ext/v1/spend-analytics/data

rollout.config-list[2].percentage = 0
rollout.config-list[2].whitelisting-required = 1
rollout.config-list[2].user-list = 19568374,********,1059237905,153618874,*********,957604,6257441,56612796,317040440,7842086,191733975,1222490353,1502241615,3199968,420071448,581715142,27210811,893354,29111688,230076407,594493697,191685349,2784010,349174251,6236193,486443710,6529780,29670233,243232292,287665477,219858099,24971888,254133495,518726118,419781662,*********,9944379,512796856,68336144,47975662,1400206311,342260451,201278171,201791995,230041375,2564256,*********,132499998,296230701,*********,*********,*********,********,9268107,3770465,*********,1490589582,*********,*********,*********,267806444,**********,424941292,240546394,********,136465278,42129353,********,*********,*********,*********,738496,*********,********,1368664650,********,18011324,22237406,78349274,269241912,293383009,27044578,232113247,157021860,18365600,291199149,228146609,550288448,308967198,18480851,1284033,********,108768010,1251621668,25209101,147547192,144760378,1237163713,217145117,293275297,3072317,30809950,25188981,1574406,1476173203,5445595,1141567389,585426764,1147729658,*********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********,1830830837,1383841722,423492510,1479674579,5238511,495910024,55963648,1284347,311680506,217570033,1310394,167391292,21336322,1195317475,291466963,1175315007,330528549,1416564493,227241735,1194270239,30009242,1032714351,1243823346,145831564,294876831,249702262,582637070,1199277624,576599582,245219894,23966029,1185595049,1002577556,303949014,15577609,1350008752,1196189794,488556860,437236362,428213990,1204690964,1136602049,256978359,1184860434,1182369218,1380866871,1170061,1001891860,1297379853,30927120,112525454,1255927086,227776525,88018292,1156189866,679187971,1159929074,1191607769,1197285672,307098214,132990992,1175310601,27667028,1144292352,251450578,1392476388,558457242,138604100,311477762,475154816,1183367693,1174599949,681467605,27530283,1033175770,1185814119,187887579,588620372,1013934927,1153859583,531218630,560527798,1181017027,255979703,612737708,589549310,1275018336,302825736,397257394,517434924,1408189245,189952369,1005222697,1135762328,292903779,24062482,11553558,1179139496,1002467053,1184896575,142227588,433908936,1206861812,1259848719,22106833,605341957,1254938635,345264713,1387361839,350530205,342136775,91656778,1197498674,1269530121,452519806,1140913287,287753231,230782731,316007628,341030201,1215024435,1285163169,1018873698,1354666444,99001044,1607014,1262472564,1174243682,1228823223,209126223,1147121687,224081463,2610876,291742131,462749422,411348640,1009034969,250768708,313714252,306056680,1106536065,45430856,1169372683,1135772011,240892512,1093015375,300808479,548963724,382353050,16814568,564030982,311251718,1033346919,1185314433,347046839,25895865,1054407551,1185615072,346162591,233750937,1184948357,307707404,1206572542,419728806,1214000923,1166952443,505537108,667861349,1304303915,1446911837,304662370,1170286928,1198316914,1274144037,397622530,198430405,520560068,1034842834,555123240,658245107,305399594,329441893,423143734,1153256977,1309887733,2939882,358568536,494463384,1190641946,1106037848,30039003,1166878365,1290008463,1021030524,558108434,40832638,1180949181,1005879564,1183948274,186835455,1183797992,27597456,571293604,1197976720,1304219388,1006274057,385272432,1344008333,1139350955,305890430,554792984,1019546858,244415762,302229186,346274299,1202966225,307361604,456333496,1248214662,231213247,1002333248,1148472693,429871206,335125907,1304578397,1390985303,313223484,673898515,11834554,577223598,1197078765,1296147822,290183857,1371000588,239167006,190286793,1039843354,137439462,219418173,1089761692,323506452,616846934,1035883563,1179132671,1072564282,405776460,180261669,1133154836,292795577,5661338,579303802,1286624471,1167541560,12743422,316783016,1394391095,1016639270,270811376,586721810,307959776,1253167843,327147007,1075529972,155721436,286085187,1188832917,1256880113,288740665,1359281192,246438022,1350068727,1158149065,1202216532,414256040,69126040,566774514,1176632794,1027448990,1128241696,1346843280,18361038,587387770,1283706834,499629994,441714228,1386555435,1180467691,23065016,1176430,1327391190,1060453949,297104087,1149634777,431186084,1723342550,411708466,1188866234,1151560431,239141326,308534450,264668977,596614367,1163062452,1199202148,257583049,22359048,1416220085,1189962751,1136339225,228658379,576009532,1235196000,221468665,1332194140,356124986,1215712422,66620494,517603542,247800034,1042759530,1195673163,503784582,1189215989,441864438,1349670091,1240065286,1034415302,1002084750,1168252368,1285261341,1297433982,1329182044,226156177,1131474568,290771763,25127217,1284905,1255871571,1192540139,91136252,19811232,1017841652,1183925087,1261165563,346360923,185971907,1320582753,421342748,186062185,241580316,300112657,2265174,1011965106,1197369448,1089240642,316860800,1299914537,1188338825,1202981255,1381745935,1276504787,1190360844,10378535,1165490839,1198984845,546081874,319844922,184464229,649916631,1198515034,656670917,1142076438,1056234145,1314194027,330921057,300933593,571031642,1335869282,1171648359,56429924,1095383110,1103095479,1129541703,303148144,1359147978,172835707,646312387,307388562,572410514,1038135113,1178131130,1188327589,1155997326,146839722,1207970111,204015151,1257422428,652021967,6367918,1189305719,1094705799,1414346603,28977770,433373814,29881364,119605092,1146984798,135540388,1274129812,287441765,407116168,1165836907,343525735,1184053893,1240378584,619186868,1191104732,363370332,1065963490,1199967538,1357166668,643783945,1116195295,1129358651,544905860,286327715,1185128014,503383712,1196997754,1230593893,599217903,1262112224,1194653320,312805476,1303065998,1369588242,1209260286,1254766649,1200148202,1249655514,1395652427,404444998,1153761673,1376608160,1151399448,56811708,1150367573,1193501904,523664854,1226425253,1185762014,270903406,1418986124,261315853,672424343,139251098,120435812,1005233029,1021156335,293823621,615647740,1157590759,1304697389,532628764,1411810686,1319499218,1196210110,1109908811,243320654,1122394458,386724604,1293973195,1302630040,394051962,603317763,551619352,1417067710,1270979466,9419235,1068666251,552096950,1186527024,1255705271,650189267,295093861,1341905044,355255872,1042797739,590810886,1204826119,1218030999,231105007,1260434861,664759711,350562963,123611146,294165093,1185867282,1176593679,452407788,1349174361,650409965,251170092,1048590949,39997354,5191493,598780075,670735213,332485043,1167233134,674695143,565875894,603770603,650057389,1120989561,1397910062,1005224491,340683865,1146944848,1246271519,1102097889,448936230,1259883968,1135832679,561687796,457567492,589472010,197154429,252469628,1133227114,1066027513,1166126556,262798297,1385090923,1186225401,1420498312,1108365254,1275451727,18029336,186668233,506075544,1336821011,1108168740,653807215,187706967,1181732811,197884953,95962592,6014735,117089488,1395787207,2461554,22498179,19397336,251240380
rollout.config-list[2].whitelisting-for = ext/v1/spend-analytics/dataBreakup

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = 1
rollout.config-list[3].user-list = 19568374,********,1059237905,153618874,*********,957604,6257441,56612796,317040440,7842086,191733975,1222490353,1502241615,3199968,420071448,581715142,27210811,893354,29111688,230076407,594493697,191685349,2784010,349174251,6236193,486443710,6529780,29670233,243232292,287665477,219858099,24971888,254133495,518726118,419781662,*********,9944379,512796856,68336144,47975662,1400206311,342260451,201278171,201791995,230041375,2564256,*********,132499998,296230701,*********,*********,*********,********,9268107,3770465,*********,1490589582,*********,*********,*********,267806444,**********,424941292,240546394,********,136465278,42129353,********,*********,*********,*********,738496,*********,********,1368664650,********,18011324,22237406,78349274,269241912,293383009,27044578,232113247,157021860,18365600,291199149,228146609,550288448,308967198,18480851,1284033,********,108768010,1251621668,25209101,147547192,144760378,1237163713,217145117,293275297,3072317,30809950,25188981,1574406,1476173203,5445595,1141567389,585426764,1147729658,*********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********,1830830837,1383841722,423492510,1479674579,5238511,495910024,55963648,1284347,311680506,217570033,1310394,167391292,21336322,1195317475,291466963,1175315007,330528549,1416564493,227241735,1194270239,30009242,1032714351,1243823346,145831564,294876831,249702262,582637070,1199277624,576599582,245219894,23966029,1185595049,1002577556,303949014,15577609,1350008752,1196189794,488556860,437236362,428213990,1204690964,1136602049,256978359,1184860434,1182369218,1380866871,1170061,1001891860,1297379853,30927120,112525454,1255927086,227776525,88018292,1156189866,679187971,1159929074,1191607769,1197285672,307098214,132990992,1175310601,27667028,1144292352,251450578,1392476388,558457242,138604100,311477762,475154816,1183367693,1174599949,681467605,27530283,1033175770,1185814119,187887579,588620372,1013934927,1153859583,531218630,560527798,1181017027,255979703,612737708,589549310,1275018336,302825736,397257394,517434924,1408189245,189952369,1005222697,1135762328,292903779,24062482,11553558,1179139496,1002467053,1184896575,142227588,433908936,1206861812,1259848719,22106833,605341957,1254938635,345264713,1387361839,350530205,342136775,91656778,1197498674,1269530121,452519806,1140913287,287753231,230782731,316007628,341030201,1215024435,1285163169,1018873698,1354666444,99001044,1607014,1262472564,1174243682,1228823223,209126223,1147121687,224081463,2610876,291742131,462749422,411348640,1009034969,250768708,313714252,306056680,1106536065,45430856,1169372683,1135772011,240892512,1093015375,300808479,548963724,382353050,16814568,564030982,311251718,1033346919,1185314433,347046839,25895865,1054407551,1185615072,346162591,233750937,1184948357,307707404,1206572542,419728806,1214000923,1166952443,505537108,667861349,1304303915,1446911837,304662370,1170286928,1198316914,1274144037,397622530,198430405,520560068,1034842834,555123240,658245107,305399594,329441893,423143734,1153256977,1309887733,2939882,358568536,494463384,1190641946,1106037848,30039003,1166878365,1290008463,1021030524,558108434,40832638,1180949181,1005879564,1183948274,186835455,1183797992,27597456,571293604,1197976720,1304219388,1006274057,385272432,1344008333,1139350955,305890430,554792984,1019546858,244415762,302229186,346274299,1202966225,307361604,456333496,1248214662,231213247,1002333248,1148472693,429871206,335125907,1304578397,1390985303,313223484,673898515,11834554,577223598,1197078765,1296147822,290183857,1371000588,239167006,190286793,1039843354,137439462,219418173,1089761692,323506452,616846934,1035883563,1179132671,1072564282,405776460,180261669,1133154836,292795577,5661338,579303802,1286624471,1167541560,12743422,316783016,1394391095,1016639270,270811376,586721810,307959776,1253167843,327147007,1075529972,155721436,286085187,1188832917,1256880113,288740665,1359281192,246438022,1350068727,1158149065,1202216532,414256040,69126040,566774514,1176632794,1027448990,1128241696,1346843280,18361038,587387770,1283706834,499629994,441714228,1386555435,1180467691,23065016,1176430,1327391190,1060453949,297104087,1149634777,431186084,1723342550,411708466,1188866234,1151560431,239141326,308534450,264668977,596614367,1163062452,1199202148,257583049,22359048,1416220085,1189962751,1136339225,228658379,576009532,1235196000,221468665,1332194140,356124986,1215712422,66620494,517603542,247800034,1042759530,1195673163,503784582,1189215989,441864438,1349670091,1240065286,1034415302,1002084750,1168252368,1285261341,1297433982,1329182044,226156177,1131474568,290771763,25127217,1284905,1255871571,1192540139,91136252,19811232,1017841652,1183925087,1261165563,346360923,185971907,1320582753,421342748,186062185,241580316,300112657,2265174,1011965106,1197369448,1089240642,316860800,1299914537,1188338825,1202981255,1381745935,1276504787,1190360844,10378535,1165490839,1198984845,546081874,319844922,184464229,649916631,1198515034,656670917,1142076438,1056234145,1314194027,330921057,300933593,571031642,1335869282,1171648359,56429924,1095383110,1103095479,1129541703,303148144,1359147978,172835707,646312387,307388562,572410514,1038135113,1178131130,1188327589,1155997326,146839722,1207970111,204015151,1257422428,652021967,6367918,1189305719,1094705799,1414346603,28977770,433373814,29881364,119605092,1146984798,135540388,1274129812,287441765,407116168,1165836907,343525735,1184053893,1240378584,619186868,1191104732,363370332,1065963490,1199967538,1357166668,643783945,1116195295,1129358651,544905860,286327715,1185128014,503383712,1196997754,1230593893,599217903,1262112224,1194653320,312805476,1303065998,1369588242,1209260286,1254766649,1200148202,1249655514,1395652427,404444998,1153761673,1376608160,1151399448,56811708,1150367573,1193501904,523664854,1226425253,1185762014,270903406,1418986124,261315853,672424343,139251098,120435812,1005233029,1021156335,293823621,615647740,1157590759,1304697389,532628764,1411810686,1319499218,1196210110,1109908811,243320654,1122394458,386724604,1293973195,1302630040,394051962,603317763,551619352,1417067710,1270979466,9419235,1068666251,552096950,1186527024,1255705271,650189267,295093861,1341905044,355255872,1042797739,590810886,1204826119,1218030999,231105007,1260434861,664759711,350562963,123611146,294165093,1185867282,1176593679,452407788,1349174361,650409965,251170092,1048590949,39997354,5191493,598780075,670735213,332485043,1167233134,674695143,565875894,603770603,650057389,1120989561,1397910062,1005224491,340683865,1146944848,1246271519,1102097889,448936230,1259883968,1135832679,561687796,457567492,589472010,197154429,252469628,1133227114,1066027513,1166126556,262798297,1385090923,1186225401,1420498312,1108365254,1275451727,18029336,186668233,506075544,1336821011,1108168740,653807215,187706967,1181732811,197884953,95962592,6014735,117089488,1395787207,2461554,22498179,19397336,251240380
rollout.config-list[3].whitelisting-for = ext/v1/spend-analytics/aggregatedData

rollout.config-list[4].percentage = 100
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = 317040440,420071448,581715142,3199968,230076407,337424149,236917725,893354,1450613099,19901318,1246651699,3772088,247811954,255680287,191733975,187041613,132846878,289573801,6236193,6257441,1059237905,********,*********,*********,*********,*********,*********,*********,********,*********,*********,********,**********,**********,**********,**********,*********,*********,*********,*********,**********,1450447543,*********,**********,243009112,12231789,1421485832,3770465,9268107,*********,234514291,19568374,118501134,267806444,99392518,1147729658,5445595,1284033,25209109,217145117,293275297,136465278,957604,24971888,108768010,1284347,56612796,*********,296230701,7842086,2164859,330511075,*********,********,*********,7881770,*********,********,16211563,418569378,*********,11201492,9178274,131765382,2953086,298394047,230084261,255453517,244889640,9831647,20422332,137938892,23024634,1240933502,182183537,1452122,272527877,*********,9661621,204402215,131639348,54517546,*********,55772598,*********,423215038,*********,2556302,399697124,117523406,20511073,2461554,7202709,14012198,27669850,401264800,2003295,30378965,24405049,4759401,********,25209101,1574406,7524428,********,********,*********,*********,********,**********,********,*********,*********,*********,*********,738496,*********,*********,**********,********,*********,*********,**********,**********,********,*********,2844400,********,**********,********,*********,3366855,437535,********,********,,********,**********,********,*********,********,1284033,*********,5662742,*********,********
rollout.config-list[4].whitelisting-for = ext/v1/cstBotDetail

# >>>>>>need to change these
s3.bucket.name = paymentsbank-middleware-statement-service
s3.bucket.region = ap-south-1
s3.bucket.access.key.value = MIDDLEWARE_ACCOUNT_SUMMARY_S3_ACCESSKEYID
s3.bucket.secret.key.value = MIDDLEWARE_ACCOUNT_SUMMARY_S3_SECRETACCESSKEY

springdoc.api-docs.path=/int/v3/api-docs

uth.ppbl.txn.history.secret.key = UNIFIED_TRANSACTION_HISTORY_PPBL_SECRET_KEY

all.types.of.meta.data = filter,search,spendAnalytics,autoComplete,passbookMenu
metaDataApi.whitelisted.users = -1
#Cache time in ms
cache.ttl.for.metadata.at.app.side = 5000
supported.filters.list.for.all.user = txnCategory
auth-validation-required-for-meta-data = false
meta.data.cache.time = 300

configurable_properties-index-alias = configurable_properties_alias
configurable_properties.source = ElasticSearch
configurable_properties-index = configurable_properties-1

scheduler.schedulerPropertyMap.localizedDataCacheScheduler.name = localizedDataCacheScheduler
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.cronExpression = 0 0 */4 ? * *
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.schedulerType = localizedDataCacheScheduler
scheduler.schedulerPropertyMap.localizedDataCacheScheduler.isSchedulerEnabled = true

# this cron explanation is to run the scheduler 1AM every night.
scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.cronExpression = 0 0 1 * * ?
scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.schedulerType = indexAvailabilityCheckerScheduler
scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.isSchedulerEnabled = false

aws-es-from-date                      = 2023-03-01 00:00:00.000 +0530
# >>>>>>need to change these, need to add tpap live date and time
from-date-listing-filter              = 2024-03-01 00:00:00.000 +0530
date-range-from-date-listing-filter   = 2024-03-01 00:00:00.000 +0530

#Bo Panel Properties
configurable_properties.source.bo_panel = BOPanel
container.hostname                                = ${HOSTNAME}

# >>>>>>need to change these
UTH_V1_LISTING_URL = https://upi.paytm.com/pth/ext/v3/search
UTH_V1_DETAIL_URL = https://upi.paytm.com/pth/ext/v3
UTH_V2_LISTING_URL = https://upi.paytm.com/pth/ext/v3/search
UTH_V2_DETAIL_URL = https://upi.paytm.com/pth/ext/v3
UTH_BG_APP_SYNC_LISTING_URL = https://upi.paytm.com/pth/ext/v3/bg/listing

#New Listing API Keys
PTH_LISTING_URL_V1 = https://upi.paytm.com/pth/ext/v3/listing
PTH_LISTING_FILTER_URL_V1 = https://upi.paytm.com/pth/ext/v3/listing/filter
PTH_LISTING_UPI_PASSBOOK_URL_V1 = https://upi.paytm.com/pth/ext/v3/upi/listing
PTH_LISTING_UPI_LITE_PASSBOOK_URL_V1 = https://upi.paytm.com/pth/ext/v3/upi_lite/listing
PTH_LISTING_UPI_CC_PASSBOOK_URL_V1 = https://upi.paytm.com/pth/ext/v3/upi_cc/listing
PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V1 = https://upi.paytm.com/pth/ext/v3/upi/listing/filter
PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V1 = https://upi.paytm.com/pth/ext/v3/upi_lite/listing/filter
PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V1 = https://upi.paytm.com/pth/ext/v3/upi_cc/listing/filter
PTH_LISTING_URL_V2 = https://upi.paytm.com/pth/ext/v3/listing
PTH_LISTING_FILTER_URL_V2 = https://upi.paytm.com/pth/ext/v3/listing/filter
PTH_LISTING_UPI_PASSBOOK_URL_V2 = https://upi.paytm.com/pth/ext/v3/upi/listing
PTH_LISTING_UPI_LITE_PASSBOOK_URL_V2 = https://upi.paytm.com/pth/ext/v3/upi_lite/listing
PTH_LISTING_UPI_CC_PASSBOOK_URL_V2 = https://upi.paytm.com/pth/ext/v3/upi_cc/listing
PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V2 = https://upi.paytm.com/pth/ext/v3/upi/listing/filter
PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V2 = https://upi.paytm.com/pth/ext/v3/upi_lite/listing/filter
PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V2 = https://upi.paytm.com/pth/ext/v3/upi_cc/listing/filter


#Url to get next listing hit
listingUrl = UTH_V1_LISTING_URL

uth.cst.txn.history.secret.key = ${PAYMENT_TRANSACTION_HISTORY_CST_SECRET_KEY}



# web v1 properties(would need to revisit commented properties)
#server.port                           = 10700

#app.name                              = @project.artifactId@
#app.version                           = @project.version@

# >>>>>>need to change these
#external-integration-service-http-url = http://external-integration.default:80
oauth.service.base.url                = https://oauth.paytm.com
oauth.client.id                       = payment-transaction-history-prod
oauth.client.secret                   = ${PTH_OAUTHCONFIG_CLIENTSECRET}

oauthClient.connectionProvider.name                = webflux
oauthClient.connectionProvider.maxConnections      = 500
oauthClient.connectionProvider.acquireTimeout      = 10000
oauthClient.connectionProvider.maxIdleTime         = 5

#es-host-list                          = ************,************,*************,************,*************,************,*************,*************,************,************,************,*************,*************,*************,************,*************
#primary.es.cluster = V2
managed-es-v2-host = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com

# >>>>>>need to change these, as Serie won't be able to make connection and will fail
dc-es-host-list                       = uth-es-app2-nlb-85972d6b09d6e362.elb.ap-south-1.amazonaws.com
dc-es-port                            = 9200

#aws-es-from-date                      = 2022-08-01 00:00:00.000 +0530
# >>>>>>need to change these
analytics-from-date                   = 2022-11-01 00:00:00.000 +0530

#fetching max these no txns from range
max-page-size-of-range   = 20
max-page-size-functionality-enable = true

#elastic-search-index                  = payment_history_alias
elastic-search-index-prefix           = payment-history-
elastic-search-index-prefix-for-details = payment-history
#es-socket-timeout                     = 2000
#es-max-retry-timeout                  = 2000
#es-connect-timeout                    = 1000
#es-connect-request-timeout            = 1000

#static-bank-logo-base-url             = https://static.orgk.com/uth/images/bank-logo/
#static-category-logo-base-url         = https://static.orgk.com/uth/images/category-logo/
#static-paytm-logo-base-url            = https://static.orgk.com/uth/images/paytm-logo/
#static-paymentMode-logo-base-url = https://static.orgk.com/uth/images/payment-intiation-mode/


oauth.client.connection.timeout       = 500
oauth.read.timeout                    = 200

#Prometheus Monitoring Constants
#prometheus.explorer                                         = TRANSACTION_HISTORY
#prometheus.hostname                                         = ${CUSTOM_MONITORING_SERVICE_HOST}
#prometheus.port                                             = ${CUSTOM_MONITORING_SERVICE_PORT}

#bank-oauth
#bank.oauth.service.base.url                        = http://oauth-internal.orgk.com
#bank.oauth.service.url.category                    = /bank-oauth/ext
#bank.oauth.client.id                               = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID}
#bank.oauth.client.secret                           = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET}

#whetherto use bankOauth or not
#use-bank-oauth = false

# initially this value will be picked from BO-Panel if it's not present there then this will be default value
# Whether to use OAuth tokeninfo API or not, if it's value is false then /v2/user will be used
use-tokenInfo-api = false

# from date to filter data based on backfill completion format: yyyy-MM-dd HH:mm:ss.SSS Z
# error code 4010 needs to be updated whenever this date changes
#from-date-listing-filter = 2022-01-01 00:00:00.000 +0530
default-from-date-listing-month-duration = 1

#known - outage, any of our system down, to throw non-retriable error
known-issue-at-backend = false

retry.topic.name = pth_retry_data
#kafka.bootstrap-servers = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.bootstrap-servers = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
whitelisted-bank-users = *********,*********,7881770,*********,**********,2164859,*********,********,*********,9246658,*********,*********,*********,********,*********,1306150,*********,*********,*********,*********,*********,*********,7082329,**********,**********,9831647,********,********,********,2953086,*********,********,5840750,*********,7202709,*********,*********
show-bank-data = true
aerospike.namespace                = pth
aerospike.host-name                = uth-aerospike1-new.uth-prod.paytm.local
aerospike.port                     = 3000
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
localise.detail.fields=detailNarration,firstInstrument.narration,secondInstrument.narration,recentTransactionInfo.narration,cstorderItem.label,repeatPayment.name
batch.size=50
localise.listing.fields=narration
whitelisted-localisation-users = -1
prepopulate.data.list=Add Money Failed,Add Money Pending,Add Money to %s,Add Money to Paytm Payments Bank,Added Back to Your,Cashback Received,Cashback Received from %s,For Order At,For reversal of money transfer to,From,From Your,In,In Your,Money Added,Money Debited,Money On Hold,Money Paid,Money Received,Money Refunded,Money Restored,Money Sent,Money Transfer Failed,Money Transfer Pending,Money added to %s,Money added to Paytm Payments Bank,Money on hold for Order at %s,Money on hold released by %s,Paid to %s,Payment Failed,Payment Pending,Payment to %s,Received from %s,Refund Failed,Refund for failed transfer to %s,Refund from %s,Refund pending,Reversal of Money Sent,Sent to %s,To,To Your,Transfer to %s,Using Your,Settlement Received from %s,Gift Voucher Received from %s,Gift Voucher Sent To %s,For reversal of failed transfer to,Compensation Received,As compensation for,Automatic Payment made to %s,For Automatic Payment to,Refund Received,Interest earned for %s,Interest Received,Interest Earned,Cash Withdrawal at %s,Cash Withdrawal,At,Cash Withdrawal at,For,Cash Withdrawal at Paytm Bank Agent,ATM Cash Withdrawal at %s,At ATM,At Store,Refund for failed cash withdrawal,For reversal of cash withdrawal at ATM,Cash Deposited at %s,Cash Deposited,Cash Deposited with Paytm Bank Agent,Cash Deposit at ATM\,%s,At ATM Recycler,Cash Deposit,Salary Received,Flexi-Salary Received,Reimbursement Received,Charges Paid,Charges Paid for purchase of Paytm Payments Bank Debit Card,For Purchase of,For Cash Withdrawal at ATM,Charges Paid for transaction at ATM,For Transaction at ATM,For dispute raised against ATM Cash Withdrawal at,For dispute raised against store payment at,Cashback Received from Paytm Payments Bank,Cashback Received from Paytm,Cheque Return Charges,Aadhaar based direct benefit received,Recovery Deduction,Lien Recovery Reversal,Refund of excess card charges,Issuance Fee charged for Debit card,Added for failed transaction,Money transferred,Money paid to %s via cheque,Money paid via cheque,Money received from cheque payment,LPG subsidy received,Deducted by %s,Reversal of Debit Card fees,Charges Refunded,For Failed Purchase of,Charges Refunded for Debit Card,Annual Charge Paid for Debit Card,Charges Refunded for Cheque Book,Charges Refunded for cash withdrawal at ATM,Charges Refunded for ATM transaction,Charges Paid for payment at %s,For Payment at,Charges Refunded for payment at %s,Charges Paid for cash withdrawal at ATM,Charges Paid for ATM transaction,For dispute raised against,For ATM Transaction at,For Failed ATM Transaction at,For Failed Payment at,For dispute raised against online payment at,Charges paid for payment to %s,For Payment to,For Failed Payment to,Refund for failed cheque payment,Refund for failed cheque payment to %s,For reversal of Cheque Payment to,Money Deducted,For reversal of money received for,For reversal of money received from,Refund for failed payment to %s,Money reversed for failed transfer from %s,Refund for failed transfer via UPI,For reversal of,Fixed Deposit Created,Refund for failed Fixed Deposit creation,Fixed Deposit Redeemed,For reversal of failed,Refund of AePS fund transfer,Refund for AePS Cash withdrawal,AePS fund transfer,AePS Cash withdrawal,Paid Successfully,Recent Payments With %s,Paid,Received,Refunded,Recent Gift Vouchers,Sent,Added to %s,Pending Add Money to %s,Failed Add Money to %s,Created,Deposited at %s,Withdrawal at Paytm Bank Agent,Withdrawal at %s,Earned for %s,Need Help with this Payment,View more details,Foreign Remittance Received from,Paid for Outward Remittance to,Refund of Outward Remittance sent to,RECEIVED IN,Charges paid for Debit Card Dispute,Cashback received for payment at %s,Tip paid to %s,Tip Paid,Surcharge for payment at %s,Surcharge Paid,Refunded for payment at %s
salary.reportCodes = 90100,90110,90120

scheduler.cron.expression = 0 ${random.int[0,59]} 0 * * ?
clientConfig.cron.expression = 0 0 1 * * ?
spring.jpa.hibernate.ddl-auto                    = none
# >>>>>>need to change these, do not knwo production Ips of mysql
spring.datasource.url                            = *****************************************************
spring.datasource.username                       = pth_pc_w
spring.datasource.password                       = ${MW_PTH_DB_PASSWORD}
spring.datasource.driver-class-name              = com.mysql.jdbc.Driver
spring.jpa.show-sql                              = true
spring.datasource.configuration.maximum-pool-size=2
jwt.verification.enable=true

#container.hostname                                = ${HOSTNAME}

#configurable_properties-index-alias = configurable_properties_alias
configurable_properties.scheduler.cron.expression = 0 * * * * ?
#configurable_properties.source = ElasticSearch
#configurable_properties-index = configurable_properties-1
configurable_properties.source.elastic_search = ElasticSearch
configurable_properties.source.dynamoDB = dynamoDB
configurable_properties.source.table = configurable_properties

#Bo Panel Properties
#configurable_properties.source.bo_panel = BOPanel

mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'configurable_properties_template': 'esConfigurationPropertiesTemplate.json' \
                                                   }
updateElasticSearchTemplate                      = true

#metaDataApi.whitelisted.users = *********,*********,*********,*********,2164859,330511075,*********,********,*********,********,7881770,*********,********,16211563,418569378,*********,*********,118501134,3770465,9268107,99392518,*********,*********,11201492,*********,9178274,957604,131765382,2953086,267806444,298394047,230084261,255453517,244889640,*********,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,*********,230084261,272527877,*********,9661621,204402215,131639348,54517546,*********,55772598,*********,*********,423215038,********,*********,2556302,16211563,399697124,418569378,117523406,20511073,3770465,*********,2953086,2461554,7202709,14012198,27669850,401264800,2003295,30378965,24405049,4759401,********,230076407,1284033,25209101,1574406,6236193,24971888,108768010,957604,********,7524428,********,1284347,********,*********,*********,********,**********,********,191733975,*********,*********,*********,*********,738496,*********,*********,**********,********,*********,*********,**********,**********,********,*********,2844400,********,**********,********,********,*********,3366855,1147729658,7202709,56612796,437535,217145117
fetch-user-image-from-cache = true
push-missing-user-image-list-to-kafka = false

# >>>>>>need to change these
upi.timeline.baseUrl = https://tpaptransactionalswitch-internal.paytm.com
upi.timeline.uri =  /upi/int/txn/v4/transaction/status
upi.timeline.sync.connection.timeout = 500
upi.only.timeline.connection.timeout = 500
upi.timeline.socket.timeout = 500
upi.timeline.timeout = 500
upi.timeline.secret.key = ${UPI_PTH_SECRET_KEY}

timeline.url.path = /pth/ext/v3/{sourceContext}/detail?txnId={txnId}&status={status}&showOnlyTimeline=true
timeline.server = https://upi.paytm.com

#Kafka properties
kafka.target-v2-list[9].kafka-client-name               = recon_config
kafka.target-v2-list[9].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[9].topic                           = pth_recon_config_data
kafka.target-v2-list[9].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[9].batch-size-bytes                = 3000
kafka.target-v2-list[9].linger-ms                       = 15
kafka.target-v2-list[9].request-timeout-ms              = 5000

kafka.target-v2-list[10].kafka-client-name               = txnHistoryUserImageClient
kafka.target-v2-list[10].kafka-producer-key              = user_image_txn_history_producer
kafka.target-v2-list[10].topic                           = pth_user_image_url_data
kafka.target-v2-list[10].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[10].batch-size-bytes                = 3000
kafka.target-v2-list[10].linger-ms                       = 15
kafka.target-v2-list[10].request-timeout-ms              = 500

kafka.target-v2-list[11].kafka-client-name               = nonTransactingUserKafkaClient
kafka.target-v2-list[11].kafka-producer-key              = nonTransactingUserProducer
kafka.target-v2-list[11].topic                           = pth_cache_updater_data
kafka.target-v2-list[11].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[11].batch-size-bytes                = 3000
kafka.target-v2-list[11].confluent-kafka-registry-url    = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-v2-list[11].linger-ms                       = 15
kafka.target-v2-list[11].request-timeout-ms              = 5000

kafka.target-v2-list[13].kafka-client-name               = DUAL_WRITE_AUDIT_CLIENT
kafka.target-v2-list[13].kafka-producer-key              = dualWriteAuditProducer
kafka.target-v2-list[13].topic                           = pth_audit_data
kafka.target-v2-list[13].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[13].batch-size-bytes                = 3000
kafka.target-v2-list[13].linger-ms                       = 1
kafka.target-v2-list[13].request-timeout-ms              = 100

#Revisit these prop later
recon.config.cron1.delay.in.milli = 900000
recon.config.cron1.lock.at.most.in.milli = 500000

# >>>>>>need to change these
chat.url.path = paytmmp://chat?featuretype=start_chat
outwardInternationalRemittance.url = paytmmp://payment_bank?pageId=passbookPage&featuretype=outward_remittance

#managed-es-host = https://vpc-prod-v1-mid-paymenthistoryae-xtb44doayjgizjt4b53julngp4.ap-south-1.es.amazonaws.com
#whitelisted.users.list.for.managed.es = *********,*********,*********,*********,*********,7881770,*********,********,*********,*********,2953086
#whitelisted.users.list.for.managed.es = -1
#managed.roll.out.percentage = 100

max-length-of-autoComplete-query = 20
max-Num-Of-ContactBook-Numbers = 4
contactbook-search-whitelisted-users-custId-Identifier = -1
contactbook-search-whitelisting-percentage = 100

#oauth.token.evaluation.skip = false

rollout.config-list[5].percentage = 100
rollout.config-list[5].whitelisting-required = 1
rollout.config-list[5].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[5].whitelisting-for = DcListingRouting

rollout.config-list[6].percentage = 100
rollout.config-list[6].whitelisting-required = 0
rollout.config-list[6].whitelisting-for = DcNonTransactingHandling

rollout.config-list[7].percentage = 100
rollout.config-list[7].whitelisting-required = 1
rollout.config-list[7].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********,********,*********
rollout.config-list[7].whitelisting-for = detailPageRoleOutFeature

rollout.config-list[8].percentage = 100
rollout.config-list[8].whitelisting-required = 1
rollout.config-list[8].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********,*********,********
rollout.config-list[8].whitelisting-for = uthNtuCache

rollout.config-list[9].percentage = 100
rollout.config-list[9].whitelisting-required = 1
rollout.config-list[9].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********,*********,********
rollout.config-list[9].whitelisting-for = upiPassbookListingCache

rollout.config-list[10].percentage = 100
rollout.config-list[10].whitelisting-required = 1
rollout.config-list[10].user-list = 317040440,420071448,581715142,3199968,230076407,337424149,236917725,893354,1450613099,19901318,1246651699,3772088,247811954,255680287,191733975,187041613,132846878,289573801,6236193,6257441,1059237905,********,*********,*********,*********,*********,*********,*********,********,*********,*********,********,**********,**********,**********,**********,*********,*********,*********,*********,**********,1450447543,*********,**********,243009112,12231789,1421485832,3770465,9268107,*********,234514291,19568374,118501134,267806444,99392518,1147729658,5445595,1284033,25209109,217145117,293275297,136465278,957604,24971888,108768010,1284347,56612796,*********,296230701,7842086,2164859,330511075,*********,********,*********,7881770,*********,********,16211563,418569378,*********,11201492,9178274,131765382,2953086,298394047,230084261,255453517,244889640,9831647,20422332,137938892,23024634,1240933502,182183537,1452122,272527877,*********,9661621,204402215,131639348,54517546,*********,55772598,*********,423215038,*********,2556302,399697124,117523406,20511073,2461554,7202709,14012198,27669850,401264800,2003295,30378965,24405049,4759401,********,25209101,1574406,7524428,********,********,*********,*********,********,**********,********,*********,*********,*********,*********,738496,*********,*********,**********,********,*********,*********,**********,**********,********,*********,2844400,********,**********,********,*********,3366855,437535,********,1059237905,********,**********,********,*********,********,1284033,*********,5662742
rollout.config-list[10].whitelisting-for = taggingOnDetails


rollout.config-list[11].percentage = 10
rollout.config-list[11].whitelisting-required = 1
rollout.config-list[11].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,*********,56612796,********,**********
rollout.config-list[11].whitelisting-for = imps

rollout.config-list[12].percentage = 0
rollout.config-list[12].whitelisting-required = 1
rollout.config-list[12].user-list = 3199968,29111688,11169955,196578757,25147277,96294160,296230701
rollout.config-list[12].whitelisting-for = UpiCcEmiCta

rollout.config-list[13].percentage = 100
rollout.config-list[13].whitelisting-required = 1
rollout.config-list[13].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********,6236193
rollout.config-list[13].whitelisting-for = UthAnalyticsWhiteListing

rollout.config-list[14].percentage = 100
rollout.config-list[14].whitelisting-required = 0
rollout.config-list[14].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[14].whitelisting-for = trafficDiversionToDcForWhiteListedApis

rollout.config-list[15].percentage = 100
rollout.config-list[15].whitelisting-required = 0
rollout.config-list[15].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[15].whitelisting-for = listingPageNoMoreThanOneTrafficDiversionToDc

rollout.config-list[16].percentage = 0
rollout.config-list[16].whitelisting-required = 1
rollout.config-list[16].user-list = 1813018145
rollout.config-list[16].whitelisting-for = UserRateLimitingWhiteListing

rollout.config-list[17].percentage = 100
rollout.config-list[17].whitelisting-required = 1
rollout.config-list[17].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[17].whitelisting-for = TomcatListingRoleOutFeature

rollout.config-list[18].percentage = 0
rollout.config-list[18].whitelisting-required = 1
rollout.config-list[18].user-list = **********
rollout.config-list[18].whitelisting-for = RecapInvalidDataCheckSkip

rollout.config-list[19].percentage = 0
rollout.config-list[19].whitelisting-required = 1
rollout.config-list[19].user-list = 9178274,********,12231789,*********,********,1059237905,1830830837,*********,*********,*********,**********,*********,********,*********,********,*********
rollout.config-list[19].whitelisting-for = appCacheOptimisation

rollout.config-list[20].percentage = 100
rollout.config-list[20].whitelisting-required = 1
rollout.config-list[20].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[20].whitelisting-for = bankOauthApiRollout

rollout.config-list[21].percentage = 0
rollout.config-list[21].whitelisting-required = 1
rollout.config-list[21].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[21].whitelisting-for = recentTxns

rollout.config-list[22].percentage = 0
rollout.config-list[22].whitelisting-required = 1
rollout.config-list[22].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[22].whitelisting-for = newInvalidationVersionApplicable

rollout.config-list[23].percentage = 0
rollout.config-list[23].whitelisting-required = 1
rollout.config-list[23].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********,1059237905,1830830837
rollout.config-list[23].whitelisting-for = autoTaggingFeature

#Default property for listing cache.
# >>>>>>need to change these
detailPageApiCachingEnabled = false
isNtuCacheEnabled = true
isUpiPassbookCacheEnabled = true

need-to-convert-received-Query = true
autocomplete-whitelisted-custIds = *********,*********,*********,*********,2164859,330511075,*********,********,*********,********,7881770,*********,********,16211563,418569378,*********,*********,118501134,3770465,9268107,99392518,*********,*********,11201492,*********,9178274,957604,131765382,2953086,267806444,298394047,230084261,255453517,244889640,*********,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,*********,230084261,272527877,*********,9661621,204402215,131639348,54517546,*********,55772598,*********,*********,423215038,********,*********,2556302,16211563,399697124,418569378,117523406,20511073,3770465,*********,2953086,2461554,7202709,14012198,27669850,401264800,2003295,30378965,24405049,4759401,********,230076407,1284033,25209101,1574406,6236193,24971888,108768010,957604,********,7524428,********,1284347,********,*********,*********,********,**********,********,191733975,*********,*********,*********,*********,738496,*********,*********,**********,********,*********,*********,**********,**********,********,*********,2844400,********,**********,********,********,*********,3366855,1147729658,7202709,56612796,437535,217145117
autocomp-new-query-whitelisting-percentage = 0

#metadata api Oauth Call
oauth.call.enable.in.metadata.api = false

#dynamo.secret-key = ${UTH_DYNAMO_SECRET_KEY}
#dynamo.access-key = ${UTH_DYNAMO_ACCESS_KEY}
#dynamo.connection-timeout = 1000
#dynamo.client-execution-timeout = 1000
#dynamo.request-timeout = 1000
#dynamo.socket-timeout = 1000
#dynamo.max-error-retries = 10


searchFields-userView-mapping = {\
                                    "searchFields.searchOtherName":"Name", \
                                    "searchFields.searchOtherBankName":"Bank Name", \
                                    "searchFields.searchOtherMobileNo":"Mobile No.", \
                                    "searchFields.searchPaymentSystem":"Account", \
                                    "searchFields.searchTxnIndication":"Type", \
                                    "searchFields.searchTxnCategory":"Type", \
                                    "searchFields.searchTxnStatus":"Status", \
                                    "searchFields.searchUthMerchantCategory":"Category", \
                                    "searchFields.searchOtherVpa":"UPI ID", \
                                    "searchFields.searchVoucherName":"Voucher", \
                                    "searchFields.searchWalletType":"Sub Wallet", \
                                    "searchFields.searchOtherAccountNo":"Account Number", \
                                    "searchFields.searchSelfBankName":"Bank Name", \
                                    "searchFields.searchTags":"Tag", \
                                    "searchFields.searchSelfAccountNo":"Account Number", \
                                    "searchFields.searchSelfAccTyp":"Account" }
#this cron runs every 0th,10th,20th,20th second of 0 to 5th minute at 12:00 am on 1st of every month
configurable.scheduler.create.new.indices.cron.expression = 0,10,20,30 0-5 0 1 * ?
#updated.index.month.list = payment-history-07-2020,payment-history-08-2020,payment-history-09-2020,payment-history-10-2020,payment-history-11-2020

#ForEnabling Req Money Cta
enable.requestMoney.cta = true

# >>>>>>need to change these
ipoMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9pcG8ifQ==

#For routing to Dc cluster.
isListingRoutingEnabled = true
isDetailRoutingEnabled = true
isTrafficDiversionEnabled = true

#Maximum Days Diff for tagging a txn.
maxAllowedDayDiffForTagging = 60

#UTH_V1_LISTING_URL = https://api.orgk.com/transaction-history/ext/v3/search
#UTH_V1_DETAIL_URL = https://api.orgk.com/transaction-history/ext/v3
#UTH_V2_LISTING_URL = https://api.orgk.com/transaction-history-v2/ext/v3/search
#UTH_V2_DETAIL_URL = https://api.orgk.com/transaction-history-v2/ext/v3

localization-enabled = true
listing-localization-enabled = false
internal_localization_api_call_enabled = false

other-party-bank-details-enabled = false
other-party-bank-details-for-vpa2account-txn-enabled = true
other-party-bank-details-enabled-inward = false
min-amt-req-to-show-other-party-info-in-outward-txn-in-paisa = 1000
manage.automatic.payment.cta.base.deeplink =  paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZX0sInBhdGgiOiIvIy9tdC9hdXRvbWF0aWMtcGF5bWVudHMvbWFuZGF0ZS1kZXRhaWxzIn0=&umn=
recurringMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9tYW5kYXRlcyJ9

#uth.cst.txn.history.secret.key = ${MIDDLEWARE_TRANSACTION_HISTORY_CST_SECRET_KEY}

oauth.invalid.token.exception.response.enable = false

#Route nonTransactingUsers to DC or not.
nonTransactingRoutingToDc_upiLite = true
nonTransactingRoutingToDc_allTxns = true

#Whether to use customerCreationDate or not
isUseOfCustomerCreationDateEnabled = true

showTagEnable = false
tagsUpdationEnable = false
tagFeatureEnable = false

repeat-payment-filter-enabled = true

ondc.verticalId = 204

cta.repeatPayment.use-mobileNo-deeplink-for-upi-p2p-txns = false

repeatPayment.legacyImplementationEnabled = true

#deeplink for Storecash Cta
storecash-cta-enabled = true
deepLink.storecash.cta = paytmmp://mini-app?aId=74c710393f38477eb7994901e7e80307&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9fQ==

uth.user.rate.limit.per.hour = 200
uth.user.rate.limit.hash.algo = SHA256
uth.user.rate.limit.enable = true

status-wise-custom-message-enabled-in-detail-page = true

deepLink.convert.emi.cta = paytmmp://mini-app?aId=11260227834849508d8d135d2ca00ba2&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZSwic3RhdHVzQmFyU3R5bGUiOjEsInBheXRtQ2hhbmdlU3RhdHVzQmFyQ29sb3IiOiIjMTAxMDEwIn0sInBhdGgiOiIvaXRlMS9pbmRleC5odG1sI210L2NjLWVtaS9hY3RpdmF0ZSJ9
deepLink.view.emi.cta = paytmmp://mini-app?aId=11260227834849508d8d135d2ca00ba2&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZSwic3RhdHVzQmFyU3R5bGUiOjEsInBheXRtQ2hhbmdlU3RhdHVzQmFyQ29sb3IiOiIjMTAxMDEwIn0sInBhdGgiOiIvaXRlMS9pbmRleC5odG1sI210L2NjLWVtaS9icmVha3VwIn0=
upi.cc.emi.credit.card.cycle.days = 45
upi.cc.emi.cta.enable = false

uth.analytics.send.to.kafka = true

min-amount-limit-for-amount-range-filter = 0
max-amount-limit-for-amount-range-filter = 1000000

uth.user.daily.rate.limit.enable = false
uth.user.daily.rate.limit.threshold = 600

uth.downstream.auth.circuit.breaker.config = {\
  "refreshPeriod":"20",\
  "limit":"300"}

uth.downstream.auth.circuit.breaker.enable = true
uth.downstream.auth.circuit.breaker.failure.httpcodes = 500,502,503,504
uth.user.rate.limit.apis.blacklisted = /pth/ext/v1/metaData
upi-cstPanel-api-enabled = true

txn.stream.lag.based.tracking.feature.enabled           = true
txn.stream.lag.based.txn.details.rejections.enabled     = true
txn.details.source.txn.stream.max.lag.rejection.buffer.seconds   = 1

pth-pth.consumer.service-secret = ${PTH_CONSUMER_SERVICE_SECRET}
statement-service-es-fetch-page-size = 500
statement-service-disabled = false

cta.enable-view-history-for-onus-txns = false

cta.view-history-deeplink-for-onus-txns = paytmmp://mini-app?aId=7ee85e9b76c544f59ccc0c8b5fcdd655&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2luZGV4Lmh0bWwjL3BiL2hpc3RvcnktbGlzdCJ9
min.amt.limit.in.paisa.to.show.convert.emi.cta = 200000

rewind.meta.data.cache.enabled = true
rewind.txn.analytics.data.cache.enabled = true

uth.ts.cst.secret.key = UTH_TS_CST_SECRET_KEY

merchant.type.change.based.on.oms.order.logic.enable = true

rewind.api.auth.skipping.enabled = true

recap.es.indexing.enabled = true

recap.cache.write.enabled = true

RecapInvalidDataCheckSkip = **********,*********

fuzzy.search.required.for.autoComplete.api = false

recentTxns.disabled = true
recentTxns.list.of.supportedStatus = 1,2,3
recentTxns.cache.ttl.seconds = 300
ptl.apiVersion.start = 2.0

enable-deaf-txn-faq-cta = false

# >>>>>>need to change these
pushToKafkaForAuditEnabled=false


# Powered By Logo mapping wrt upiHandle
poweredBy.upiLogo.list = paytm
cst.pth.txnList.api.secret = ${CST_PTH_TXN_LIST_API_SECRET_VALUE}


# List of CTA Type Enabled
detailPage.listOfCtaType.enabled = CST_NEED_HELP,VIEW_UPI_LITE,SPLIT_BILL,DESCRIPTION,MANAGE_AUTOMATIC_PAYMENT


#secret for TPAP PANEL client for cstPanel api
cst.pth.cstPanel.api.secret = ${PTH_TPAP_PANEL_SECRET_KEY}

#mandate history api
mandate.history.baseUrl = https://tpaptransactionalbff-internal.paytm.com
mandate.history.uri =  /upi/int/bff/v1/mandate/journey
mandate.history.connection.timeout = 500
mandate.history.socket.timeout = 500
mandate.history.timeout = 500
mandate.history.api.secret.key = ${UPI_PTH_SECRET_KEY}

toggleVisibility.feature.txnStatus.not.supported.list = PENDING
toggleVisibility.feature.forOnusTxn.isSupported = true
toggleVisibility.feature.max.allowed.day.diff.supported = 45

autoTagging.details.aerospike.set.name = auto_tagging_details_set
autoTagging.aerospike.host-name = uth-ai-aerospike1.uth-prod.paytm.local,uth-ai-aerospike2.uth-prod.paytm.local,uth-ai-aerospike3.uth-prod.paytm.local,uth-ai-aerospike4.uth-prod.paytm.local
autoTagging.aerospike.port = 3000
autoTagging.aerospike.namespace = pth_persistent