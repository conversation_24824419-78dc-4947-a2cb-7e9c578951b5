server.port                                        = 8080
build.timestamp                                    = @timestamp@
app.name                                           = @project.artifactId@
app.version                                        = @project.version@
git.commit.id                                      = 122

git.branch                                         = master
logging.config                                     = classpath:log4j2-stage2.json


server.tomcat.accesslog.enabled                    = true
server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
server.tomcat.accesslog.directory                  = /log/payment-transaction-history-service
server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
server.tomcat.accesslog.rotate                     = true
server.tomcat.accesslog.rename-on-rotate           = true
server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
server.tomcat.accesslog.buffered                   = false
server.tomcat.accesslog.request-attributes-enabled = true
server.tomcat.accesslog.maxDays                    = 1

elastic-search-index                               = stage2_payment_history_alias
updated.index.month.list                           = stage2-payment-history-08-2020
index.name.prefix                                  = stage2-payment-history
upi-udir-index.name.prefix                         = stage2-uth-cst
es-socket-timeout                                  = 2000
es-max-retry-timeout                               = 2000
es-connect-timeout                                 = 1000
es-connect-request-timeout                         = 1000

es-host-list                                       = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es-port                                            = 9200

#V2 ES Configuration
es-v2-host-list                                    = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es-v2-port                                         = 9200

#Mandate es configuration
mandate-es-host-list = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local

txn.tags.table                                     = stage2-txn_tags_data-pth
user.tags.table                                    = stage2-user_tag_data-pth
userId.updatedTimeStamp.index                      = stage2-userId-updatedTimeStamp-index
sysyem.tags.table                                  = stage2-system_tag_data-pth
tagging.max.tags.count.per.txn                    = 1
tagging.tag.max.length                             = 30
tagging.tag.min.length                             = 3
tagging.user.tags.limit                           = 100

jwt.varification.enable                            = true
uth.upi.txn.history.secret.key                     = ${PTH_UPI_SECRET_KEY}
uth.pms.txn.history.secret.key                     = ${PAYMENT_TRANSACTION_HISTORY_PMS_SECRET_KEY}
pth.upi.switch.secret.key                          = ${PTH_UPI_SWITCH_SECRET_KEY}


envBased.jacocoSupport                             = true

#bank-oauth
bank.oauth.service.base.url                        = https://oauth-ite-internal.orgk.com
bank.oauth.service.url.category                    = /bank-oauth/ext
bank.oauth.client.id                               = UNIFIED_TRANSACTION_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = UNIFIED_TRANSACTION_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET
bank.oauthClient.connectionProvider.maxConnections      = 500
bank.oauthClient.connectionProvider.acquireTimeout      = 45000
bank.oauthClient.connectionProvider.maxIdleTime         = 5
bank.oauth.client.connection.timeout                    = 2000
bank.oauth.read.timeout                                 = 2000
bank.oauth.socket.timeout                               = 2000

#Prometheus Monitoring Constants
prometheus.explorer                                         = UNIFIED_HISTORY_SERVICE_STAGE2
prometheus.hostname                                         = localhost
prometheus.port                                             = 8130

oauth.token.evaluation.skip = true

static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/

map.of.uth.category.logo.url          = {\
                                            '1': 'https://tpap-logo.paytm.com/uth/images/category-logo/DAIRY_AND_GROCERIES.png',\
                                            '2': 'https://tpap-logo.paytm.com/uth/images/category-logo/SERVICES.png',\
                                            '3': 'https://tpap-logo.paytm.com/uth/images/category-logo/FUEL_AND_AUTOMOBILE.png',\
                                            '4': 'https://tpap-logo.paytm.com/uth/images/category-logo/Food_And_Beverages.png',\
                                            '5': 'https://tpap-logo.paytm.com/uth/images/category-logo/Shoppings.png',\
                                            '6': 'https://tpap-logo.paytm.com/uth/images/category-logo/PERSONAL_AND_HEALTH_CARE.png',\
                                            '7': 'https://tpap-logo.paytm.com/uth/images/category-logo/Taxi_And_Transportations.png',\
                                            '8': 'https://tpap-logo.paytm.com/uth/images/category-logo/TRAVEL.png',\
                                            '9': 'https://tpap-logo.paytm.com/uth/images/category-logo/RECHARGES_AND_BILL_PAYMENT.png',\
                                            '10': 'https://tpap-logo.paytm.com/uth/images/category-logo/ENTERTAINMENT.png',\
                                            '11': 'https://tpap-logo.paytm.com/uth/images/category-logo/Transfers.png',\
                                            '12': 'https://tpap-logo.paytm.com/uth/images/category-logo/DEVOTION.png',\
                                            '13': 'https://tpap-logo.paytm.com/uth/images/category-logo/Financial_Services.png',\
                                            '14': 'https://tpap-logo.paytm.com/uth/images/category-logo/Education.png',\
                                            '15': 'https://tpap-logo.paytm.com/uth/images/category-logo/Interest_Credited.png',\
                                            '16': 'https://tpap-logo.paytm.com/uth/images/category-logo/Cashback_Received.png',\
                                            '17': 'https://tpap-logo.paytm.com/uth/images/category-logo/Salary_Credited.png',\
                                            '18': 'https://tpap-logo.paytm.com/uth/images/category-logo/Others.png',\
                                            '19': 'https://tpap-logo.paytm.com/uth/images/category-logo/EDUCATIONAL.png',\
                                            '20': 'https://tpap-logo.paytm.com/uth/images/category-logo/CLOTHING_SHOES_JEWELRY.png',\
                                            '21': 'https://tpap-logo.paytm.com/uth/images/category-logo/SWEETS_BAKERY.png',\
                                            '22': 'https://tpap-logo.paytm.com/uth/images/category-logo/BOOKS_STATIONARY.png',\
                                            '23': 'https://tpap-logo.paytm.com/uth/images/category-logo/MOBILE_ACCESSORIES.png',\
                                            '24': 'https://tpap-logo.paytm.com/uth/images/category-logo/SMALL_SHOP.png',\
                                            '25': 'https://tpap-logo.paytm.com/uth/images/category-logo/ELECTRONICS.png',\
                                            '26': 'https://tpap-logo.paytm.com/uth/images/category-logo/INDUSTRIAL_SCIENTIFIC.png',\
                                            '27': 'https://tpap-logo.paytm.com/uth/images/category-logo/LEGAL_FINANCIAL.png',\
                                            '28': 'https://tpap-logo.paytm.com/uth/images/category-logo/LIFESTYLE.png',\
                                            '30': 'https://tpap-logo.paytm.com/uth/images/category-logo/gov_services.png',\
                                            '31': 'https://tpap-logo.paytm.com/uth/images/category-logo/software.png'\
                                        }

spend.index.name.prefix = stage2-spend-history
spend-history-alias = stage2_spend_history_alias
#From Date Jan 2022 (MM-yyyy)
analytics-from-month = 01-2022
analytics-dataBreakUp-page-size = 5

all.types.of.meta.data = filter,search,spendAnalytics,autoComplete,passbookMenu
metaDataApi.whitelisted.users = -1
#Cache time in ms
cache.ttl.for.metadata.at.app.side = 5000
supported.filters.list.for.all.user = txnCategory
auth-validation-required-for-meta-data = false
meta.data.cache.time = 300

configurable_properties-index-alias = stage2_configurable_properties_alias
configurable_properties.source      = ElasticSearch
configurable_properties-index       = stage2-configurable_properties-1

aws-es-from-date                      = 2024-03-06 00:00:00.000 +0530

#Bo Panel Properties
configurable_properties.source.bo_panel = BOPanel
springdoc.api-docs.path=/int/v3/api-docs
container.hostname                                = ${HOSTNAME}

#uth.cst.txn.history.secret.key = ${MIDDLEWARE_TRANSACTION_HISTORY_CST_SECRET_KEY}
uth.cst.txn.history.secret.key = MIDDLEWARE_TRANSACTION_HISTORY_CST_SECRET_KEY




# web v1 properties(would need to revisit commented properties)

#server.port                                        = 10700


#app.name                                           = @project.artifactId@
#app.version                                        = @project.version@

external-integration-service-http-url              = http://external-integration.default:80
oauth.service.base.url                             = https://accounts-staging.paytm.in
oauth.client.id                                    = upi-pth-staging
oauth.client.secret                                = VWbL9i6sLxDUCpdmi7IekZzuMImcggwA

ocl.oauth.service.base.url                = https://accounts-staging.paytm.in
ocl.oauth.client.id                       = upi-pth-staging
ocl.oauth.client.secret                   = VWbL9i6sLxDUCpdmi7IekZzuMImcggwA



oauthClient.connectionProvider.name                = webflux
oauthClient.connectionProvider.maxConnections      = 500
oauthClient.connectionProvider.acquireTimeout      = 10000
oauthClient.connectionProvider.maxIdleTime         = 5

#es-host-list                                       = *************,*************
#es-port                                            = 9200

dc-es-host-list                       = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
dc-es-port                            = 9200

#aws-es-from-date                      = 2022-08-01 00:00:00.000 +0530

#es-socket-timeout                     = 5000
#es-max-retry-timeout                  = 5000
#es-connect-timeout                    = 5000
#es-connect-request-timeout            = 5000
#elastic-search-index                               = payment_history_alias
elastic-search-index-prefix                        = stage2-payment-history-
elastic-search-index-prefix-for-details            = stage2-payment-history

#server.tomcat.accesslog.enabled                    = true
#server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
#server.tomcat.accesslog.directory                  = /log/middleware-transaction-history-service
#server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
#server.tomcat.accesslog.rotate                     = true
#server.tomcat.accesslog.rename-on-rotate           = true
#server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
#server.tomcat.accesslog.buffered                   = false
#server.tomcat.accesslog.request-attributes-enabled = true

#static-bank-logo-base-url = https://static-ite.orgk.com/uth/images/bank-logo/
#static-category-logo-base-url = https://static-ite.orgk.com/uth/images/category-logo/
#static-status-logo-base-url = https://static-ite.orgk.com/uth/images/status-logo/
#static-wallet-logo-base-url =  https://static-ite.orgk.com/uth/images/wallet-logo/
#static-merchant-logo-base-url  =  https://static-ite.orgk.com/uth/images/merchant-logo/
#static-upi-merchant-logo-base-url  =  https://static-ite.orgk.com/upi/images/merchant-logo/
#static-paytm-logo-base-url = https://static-ite.orgk.com/uth/images/paytm-logo/
#static-paymentMode-logo-base-url = https://static-ite.orgk.com/uth/images/payment-intiation-mode/


oauth.client.connection.timeout                    = 5000
oauth.read.timeout                                 = 5000

#bank-oauth
#bank.oauth.service.base.url                        = https://oauth-ite-internal.orgk.com
#bank.oauth.service.base.url                        = https://bankapiautomation-ite.orgk.com/genericMock2/Svs-BankOauthAuto
#bank.oauth.service.url.category                    = /bank-oauth/ext
#bank.oauth.client.id                               = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID}
#bank.oauth.client.secret                           = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET}

#hardcoding clientId and secret as environment variable for these is not created in unified service
#bank.oauth.client.id                               = bank-oauth-transaction-history-service-staging
#bank.oauth.client.secret                           = 3b861bc3-0315-4a85-b895-da3530d70099

#whetherto use bankOauth or not
use-bank-oauth = true

# initially this value will be picked from BO-Panel if it's not present there then this will be default value
# Whether to use OAuth tokeninfo API or not, if it's value is false then /v2/user will be used
use-tokenInfo-api = false

# from date to filter data based on backfill completion format: yyyy-MM-dd HH:mm:ss.SSS Z
# error code 4010 needs to be updated whenever this date changes
from-date-listing-filter = 2020-10-01 00:00:00.000 +0530
date-range-from-date-listing-filter = 2020-10-01 00:00:00.000 +0530
default-from-date-listing-month-duration = 1
show-bank-data = true
#known - outage, any of our system down, to throw non-retriable error
known-issue-at-backend = false
whitelisted-bank-users = *********,*********
whitelisted-localisation-users = -1

aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
aerospike.namespace = pth
aerospike.host-name = pth-comb-new-aerospike-1.uth.paytm.local
aerospike.port=3000
localise.detail.fields=detailNarration,firstInstrument.narration,secondInstrument.narration,recentTransactionInfo.narration,cstorderItem.label,repeatPayment.name
batch.size=50
localise.listing.fields=narration
prepopulate.data.list=Add Money Failed,Add Money Pending,Add Money to %s,Add Money to Paytm Payments Bank,Added Back to Your,Cashback Received,Cashback Received from %s,For Order At,For reversal of money transfer to,From,From Your,In,In Your,Money Added,Money Debited,Money On Hold,Money Paid,Money Received,Money Refunded,Money Restored,Money Sent,Money Transfer Failed,Money Transfer Pending,Money added to %s,Money added to Paytm Payments Bank,Money on hold for Order at %s,Money on hold released by %s,Paid to %s,Payment Failed,Payment Pending,Payment to %s,Received from %s,Refund Failed,Refund for failed transfer to %s,Refund from %s,Refund pending,Reversal of Money Sent,Sent to %s,To,To Your,Transfer to %s,Using Your,Settlement Received from %s,Gift Voucher Received from %s,Gift Voucher Sent To %s,For reversal of failed transfer to,Compensation Received,As compensation for,Automatic Payment made to %s,For Automatic Payment to,Refund Received,Interest earned for %s,Interest Received,Interest Earned,Cash Withdrawal at %s,Cash Withdrawal,At,Cash Withdrawal at,For,Cash Withdrawal at Paytm Bank Agent,ATM Cash Withdrawal at %s,At ATM,At Store,Refund for failed cash withdrawal,For reversal of cash withdrawal at ATM,Cash Deposited at %s,Cash Deposited,Cash Deposited with Paytm Bank Agent,Cash Deposit at ATM\,%s,At ATM Recycler,Cash Deposit,Salary Received,Flexi-Salary Received,Reimbursement Received,Charges Paid,Charges Paid for purchase of Paytm Payments Bank Debit Card,For Purchase of,For Cash Withdrawal at ATM,Charges Paid for transaction at ATM,For Transaction at ATM,For dispute raised against ATM Cash Withdrawal at,For dispute raised against store payment at,Cashback Received from Paytm Payments Bank,Cashback Received from Paytm,Cheque Return Charges,Aadhaar based direct benefit received,Recovery Deduction,Lien Recovery Reversal,Refund of excess card charges,Issuance Fee charged for Debit card,Added for failed transaction,Money transferred,Money paid to %s via cheque,Money paid via cheque,Money received from cheque payment,LPG subsidy received,Deducted by %s,Reversal of Debit Card fees,Charges Refunded,For Failed Purchase of,Charges Refunded for Debit Card,Annual Charge Paid for Debit Card,Charges Refunded for Cheque Book,Charges Refunded for cash withdrawal at ATM,Charges Refunded for ATM transaction,Charges Paid for payment at %s,For Payment at,Charges Refunded for payment at %s,Charges Paid for cash withdrawal at ATM,Charges Paid for ATM transaction,For dispute raised against,For ATM Transaction at,For Failed ATM Transaction at,For Failed Payment at,For dispute raised against online payment at,Charges Paid for payment to %s,For Payment to,For Failed Payment to,Refund for failed cheque payment,Refund for failed cheque payment to %s,For reversal of Cheque Payment to,Money Deducted,For reversal of money received for,For reversal of money received from,Refund for failed payment to %s,Money reversed for failed transfer from %s,Refund for failed transfer via UPI,For reversal of,Fixed Deposit Created,Refund for failed Fixed Deposit creation,Fixed Deposit Redeemed,For reversal of failed,Refund of AePS fund transfer,Refund for AePS Cash withdrawal,AePS fund transfer,AePS Cash withdrawal,Paid Successfully,Recent Payments With %s,Paid,Received,Refunded,Recent Gift Vouchers,Sent,Added to %s,Pending Add Money to %s,Failed Add Money to %s,Created,Deposited at %s,Withdrawal at Paytm Bank Agent,Withdrawal at %s,Earned for %s,Need Help with this Payment,View more details,Foreign Remittance Received from,Paid for Outward Remittance to,Refund of Outward Remittance sent to,RECEIVED IN

spring.jpa.hibernate.ddl-auto                    = none
spring.datasource.url                            = *********************************************
#spring.datasource.username                       = ${MW_UTH_DB_USERNAME}
#spring.datasource.password                       = ${MW_UTH_DB_PASSWORD}

#hardcoding username and password as environment variable for these is not created in unified service
spring.datasource.username                       = app_pth_pc
spring.datasource.password=${PTH_MYSQL_PASSWORD}

spring.datasource.driver-class-name              = com.mysql.jdbc.Driver

spring.jpa.show-sql                              = true
spring.datasource.configuration.maximum-pool-size=2
clientConfig.cron.expression = 0 0 1 * * ?
scheduler.cron.expression = 0 */30 * * * ?
jwt.verification.enable=true
#container.hostname                                = ${HOSTNAME}

#configurable_properties-index-alias = configurable_properties_alias
configurable_properties.scheduler.cron.expression = * * * * * ?
#configurable_properties.source = ElasticSearch
#configurable_properties-index = configurable_properties-1
configurable_properties.source.elastic_search = ElasticSearch
configurable_properties.source.dynamoDB = dynamoDB
configurable_properties.source.table = configurable_properties

mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'configurable_properties_template': 'esConfigurationPropertiesTemplate.json' \
                                                   }
updateElasticSearchTemplate                      = true

#metaDataApi.whitelisted.users = 1107235986,1107229601,118501134,3770465
#envBased.jacocoSupport=true
fetch-user-image-from-cache = true
push-missing-user-image-list-to-kafka = false

#Kafka properties
kafka.target-v2-list[0].kafka-client-name               = recon_config
kafka.target-v2-list[0].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_recon_config_data_stage2
kafka.target-v2-list[0].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
#172.23.73.105:9092,172.23.73.162:9092,172.23.73.161:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://schema-registry.uth.paytm.local:8081/
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 500

kafka.target-v2-list[1].kafka-client-name               = txnHistoryUserImageClient
kafka.target-v2-list[1].kafka-producer-key              = user_image_txn_history_producer
kafka.target-v2-list[1].topic                           = user_image_url_data_stage2
kafka.target-v2-list[1].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
#172.23.73.105:9092,172.23.73.162:9092,172.23.73.161:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 500

kafka.target-v2-list[2].kafka-client-name               = nonTransactingUserKafkaClient
kafka.target-v2-list[2].kafka-producer-key              = nonTransactingUserProducer
kafka.target-v2-list[2].topic                           = uth_cache_updater_data_stage2
kafka.target-v2-list[2].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
#172.23.73.105:9092,172.23.73.162:9092,172.23.73.161:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].confluent-kafka-registry-url    = http://schema-registry.uth.paytm.local:8081/
kafka.target-v2-list[2].linger-ms                       = 15
kafka.target-v2-list[2].request-timeout-ms              = 5000

kafka.target-v2-list[3].kafka-client-name               = uthAnalyticsKafkaClient
kafka.target-v2-list[3].kafka-producer-key              = uthAnalyticsProducer
kafka.target-v2-list[3].topic                           = uth-dwh-ingestion-topic_stage2
kafka.target-v2-list[3].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[3].batch-size-bytes                = 3000
kafka.target-v2-list[3].linger-ms                       = 1
kafka.target-v2-list[3].request-timeout-ms              = 100

kafka.target-v2-list[4].kafka-client-name               = DUAL_WRITE_AUDIT_CLIENT
kafka.target-v2-list[4].kafka-producer-key              = dualWriteAuditProducer
kafka.target-v2-list[4].topic                           = pth_audit_data_stage2
kafka.target-v2-list[4].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[4].batch-size-bytes                = 3000
kafka.target-v2-list[4].linger-ms                       = 1
kafka.target-v2-list[4].request-timeout-ms              = 100

kafka.target-v2-list[5].kafka-client-name               = api_response_cache_population
kafka.target-v2-list[5].kafka-producer-key              = api_response_cache_population_CLIENT_PRODUCER
kafka.target-v2-list[5].topic                           = middleware_transaction_history_back_fill_data_stage2
kafka.target-v2-list[5].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[5].batch-size-bytes                = 3000
kafka.target-v2-list[5].linger-ms                       = 15
kafka.target-v2-list[5].request-timeout-ms              = 5000

kafka.target-v2-list[6].kafka-client-name               = toggle-visibility
kafka.target-v2-list[6].kafka-producer-key              = toggle-visibility_CLIENT_PRODUCER
kafka.target-v2-list[6].topic                           = pth_toggle_visibility_data_stage2
kafka.target-v2-list[6].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[6].batch-size-bytes                = 3000
kafka.target-v2-list[6].linger-ms                       = 15
kafka.target-v2-list[6].request-timeout-ms              = 5000

kafka.target-v2-list[7].kafka-client-name               = TXN_TAG_KAFKA_CLIENT
kafka.target-v2-list[7].kafka-producer-key              = TXN_TAG_KAFKA_CLIENT_CLIENT_PRODUCER
kafka.target-v2-list[7].topic                           = pth_txn_tags_data_stage2
kafka.target-v2-list[7].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[7].batch-size-bytes                = 3000
kafka.target-v2-list[7].linger-ms                       = 15
kafka.target-v2-list[7].request-timeout-ms              = 5000

upi.timeline.baseUrl = https://tpaptransactionalswitch-internal-staging.paytm.com
upi.timeline.uri =  /upi/int/txn/v4/transaction/status
upi.timeline.sync.connection.timeout = 5000
upi.only.timeline.connection.timeout = 5000
upi.timeline.socket.timeout = 5000
upi.timeline.timeout = 5000

upi.timeline.secret.key = ${UPI_PTH_SECRET_KEY}

timeline.url.path = /pth/ext/v3/{sourceContext}/detail?txnId={txnId}&status={status}&showOnlyTimeline=true
timeline.server = https://api-staging.paytm.com

recon.config.cron1.delay.in.milli = 50000
recon.config.cron1.lock.at.most.in.milli = 500000


chat.url.path = paytmmp://chat?featuretype=start_chat

outwardInternationalRemittance.url = paytmmp://payment_bank?pageId=passbookPage&featuretype=outward_remittance


managed-es-host = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
whitelisted.users.list.for.managed.es = 123
managed.roll.out.percentage = 0

managed-es-v2-host = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
#primary.es.cluster = V0
max-length-of-autoComplete-query = 20

max-Num-Of-ContactBook-Numbers = 4
contactbook-search-whitelisted-users-custId-Identifier = -1
contactbook-search-whitelisting-percentage = 100

rollout.config-list[0].percentage = 100
rollout.config-list[0].whitelisting-required = 0
rollout.config-list[0].whitelisting-for = detailPageRoleOutFeature

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = 0
rollout.config-list[1].whitelisting-for = uthNtuCache

rollout.config-list[2].percentage = 100
rollout.config-list[2].whitelisting-required = 0
rollout.config-list[2].whitelisting-for = upiPassbookListingCache

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = 1
rollout.config-list[3].user-list = 0
rollout.config-list[3].whitelisting-for = taggingOnDetails

rollout.config-list[4].percentage = 0
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********
rollout.config-list[4].whitelisting-for = imps

rollout.config-list[5].percentage = 100
rollout.config-list[5].whitelisting-required = 1
rollout.config-list[5].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[5].whitelisting-for = DcListingRouting

rollout.config-list[6].percentage = 100
rollout.config-list[6].whitelisting-required = 1
rollout.config-list[6].user-list = *********,*********
rollout.config-list[6].whitelisting-for = UpiCcEmiCta

rollout.config-list[7].percentage = 0
rollout.config-list[7].whitelisting-required = 1
rollout.config-list[7].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********,6236193
rollout.config-list[7].whitelisting-for = UthAnalyticsWhiteListing

rollout.config-list[8].percentage = 100
rollout.config-list[8].whitelisting-required = 0
rollout.config-list[8].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[8].whitelisting-for = trafficDiversionToDcForWhiteListedApis

rollout.config-list[9].percentage = 100
rollout.config-list[9].whitelisting-required = 0
rollout.config-list[9].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[9].whitelisting-for = listingPageNoMoreThanOneTrafficDiversionToDc

rollout.config-list[10].percentage = 0
rollout.config-list[10].whitelisting-required = 1
rollout.config-list[10].user-list = **********
rollout.config-list[10].whitelisting-for = UserRateLimitingWhiteListing

rollout.config-list[11].percentage = 0
rollout.config-list[11].whitelisting-required = 1
rollout.config-list[11].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[11].whitelisting-for = bankOauthApiRollout

rollout.config-list[12].percentage = 0
rollout.config-list[12].whitelisting-required = 1
rollout.config-list[12].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[12].whitelisting-for = recentTxns

rollout.config-list[13].percentage = 0
rollout.config-list[13].whitelisting-required = 1
rollout.config-list[13].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[13].whitelisting-for = newInvalidationVersionApplicable

rollout.config-list[14].percentage = 0
rollout.config-list[14].whitelisting-required = 1
rollout.config-list[14].user-list = *********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[14].whitelisting-for = autoTaggingFeature

scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.cronExpression = 0 * * * * ?
scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.schedulerType = indexAvailabilityCheckerScheduler
scheduler.schedulerPropertyMap.indexAvailabilityCheckerScheduler.isSchedulerEnabled = true

detailPageApiCachingEnabled = false
isNtuCacheEnabled = true
isUpiPassbookCacheEnabled = true

#ForEnabling Req Money Cta
enable.requestMoney.cta = true

ipoMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9pcG8ifQ==
manage.automatic.payment.cta.base.deeplink =  paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZX0sInBhdGgiOiIvIy9tdC9hdXRvbWF0aWMtcGF5bWVudHMvbWFuZGF0ZS1kZXRhaWxzIn0=&umn=
recurringMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9tYW5kYXRlcyJ9

#For routing to Dc cluster.
isListingRoutingEnabled = false
isDetailRoutingEnabled = false

maxAllowedDayDiffForTagging = 30
ondc.verticalId = 204
showTagEnable = true
tagsUpdationEnable = true
tagFeatureEnable = true
updatesApi.pageSize = 20

#deeplink for Storecash Cta
storecash-cta-enabled = true
deepLink.storecash.cta = paytmmp://mini-app?aId=74c710393f38477eb7994901e7e80307&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9fQ==

uth.user.rate.limit.per.hour = 360
uth.user.rate.limit.hash.algo = SHA256
uth.user.rate.limit.enable = true

deepLink.convert.emi.cta = paytmmp://mini-app?aId=840e7060809249e0a9b4a50a7b86388f&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvY2MtZW1pL2FjdGl2YXRlIn0=
deepLink.view.emi.cta = paytmmp://mini-app?aId=840e7060809249e0a9b4a50a7b86388f&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvY2MtZW1pL2JyZWFrdXAifQ==
upi.cc.emi.credit.card.cycle.days = 45
upi.cc.emi.cta.enable = true

uth.analytics.send.to.kafka = true

min-amount-limit-for-amount-range-filter = 0
max-amount-limit-for-amount-range-filter = 1000000

uth.user.daily.rate.limit.enable = false
uth.user.daily.rate.limit.threshold = 10

uth.user.rate.limit.apis.blacklisted = /payments-history-v2/ext/v1/metaData
txn.details.config.mapping.for.onus.merchant.verticalId = {\
  "97": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId94","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false,"order_id":"$orderId"},"path":"/h5-recharge-pos/v1/index.html"}}], \
  "1218": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId174","deeplink":"paytmmp://mini-app?aId=edc6391a24ac4eec8b2d3558d5b53195&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false},"params":"?utm_source=passbook","path":"/myorders/$orderId"}},  \
          {"ctaType":"CHAT_PROFILE","ctaLabelLocaleKey": null,"deeplink":"paytmmp://mini-app?aId=edc6391a24ac4eec8b2d3558d5b53195&data=$parametersBase64Hash","parameters":{"sparams":{"pullRefresh":false,"canPullDown":false,"showTitleBar":false},"path":"/store/$shopId?utm_source=uth_redirect"}}], \
  "66": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId66","deeplink":"paytmmp://mini-app?aId=358b2bd4936a4f34918c620a3c7ac4f9&data=$parametersBase64Hash","parameters":{"sparams":{"showTitleBar":false},"params":"?utm_source=passbook","path":"/myorders/$orderId"}}],\
  "154": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId87","deeplink":"paytmmp://mini-app?aId=77a7aa36c00e469482a6219004fde717&data=$parametersBase64Hash","parameters":{"params":"","path":"/city-bus/orderSummary/$orderId","sparams":{"pullRefresh":false,"canPullDown":false,"showTitleBar":false}}}], \
  "72": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId72","deeplink":"paytmmp://train_order_summary_v2?url=https://cart.paytm.com/v1/myOrders/$orderId&from=h5"}], \
  "26": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId26","deeplink":"paytmmp://bus_order_summary?url=https://cart.paytm.com/v1/myOrders/$orderId?&isfromorderhistory=0&vertical=bus"}],\
  "64": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId64","deeplink":"paytmmp://flight_order_summary?url=https://cart.paytm.com/v1/myOrders/$orderId&isfromorderhistory=0"}],\
  "70": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId70","deeplink":"paytmmp://movie_order_summary?order_id=$orderId"}],\
  "73": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId73","deeplink":"paytmmp://events?insiderH5Url=https://h5.insider.in/payments/status/fromOrders/orderId/$orderId"}],\
  "204": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId204","deeplink":"paytmmp://product?url=https://paytmmall.com/shop/summary/$orderId#html"}],\
  "84": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId84","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "76": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId76","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "83": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId83","deeplink":"paytmmp://gold?url=https://cart.paytm.com/v1/myOrders/$orderId&order-summary-type=Gold&From=Order_history&order_id=$orderId"}],\
  "105": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId105","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "17": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId17","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "90": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId90","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "4": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId4","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "56": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId56","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "187": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId187","deeplink":"paytmmp://mini-app?aId=08f1adddf843430c9cbe7729a3d000a2&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiL2g1LXJlY2hhcmdlLXBvcy92MS9pbmRleC5odG1sIn0=&utm_soruce=app&utm_medium=passbook-view-detail&order_id=$orderId"}],\
  "71": [{"ctaType":"VIEW_DETAILS_CTA","ctaLabelLocaleKey":"ctaLabelLocaleKeyForVerticalId71","deeplink":"paytmmp://order_summary?url=https://cart.paytm.com/v2/myorders/$orderId/detail"}]\
  }

postpaid.vertical.id = 97

pth-pth.consumer.service-secret = ${PTH_CONSUMER_SERVICE_SECRET}

cta.view-history-deeplink-for-onus-txns = paytmmp://mini-app?aId=840e7060809249e0a9b4a50a7b86388f&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvcGIvaGlzdG9yeS1saXN0In0=
merchant.type.change.based.on.oms.order.logic.enable = false

internal_localization_api_call_enabled = false

#secret for TPAP PANEL client for cstPanel api
cst.pth.cstPanel.api.secret = ${PTH_TPAP_PANEL_SECRET_KEY}

#mandate history api
mandate.history.baseUrl = https://tpaptransactionalbff-internal-staging.paytm.com
mandate.history.uri =  /upi/int/bff/v1/mandate/journey
mandate.history.connection.timeout = 500
mandate.history.socket.timeout = 500
mandate.history.timeout = 500
mandate.history.api.secret.key = 83738hbfjfbfjb2565265lkldkldk
mandate-history-parent-es-alias = stage2_mandate_info_alias
mandate-history-child-es-alias = stage2_mandate_activity_alias

recentTxns.disabled = true

# API URLs for Transaction History
pth.updates.api.url = https://api-staging.paytm.com/pth/ext/v1/updates
pth.listing.url = https://api-staging.paytm.com/pth/ext/v3/listing
pth.detail.url = https://api-staging.paytm.com/pth/ext/v3
pth.recon.api.url = https://api-staging.paytm.com/pth/ext/v1/recon
pth.get.tags.api.url = https://api-staging.paytm.com/pth/ext/v1/tag/getTags
pth.update.tags.api.url = https://api-staging.paytm.com/pth/ext/v1/tag/updateTags
pth.bg.listing.api.url = https://api-staging.paytm.com/pth/ext/v3/bg/listing
pth.mandate.history.api.url = https://api-staging.paytm.com/pth/ext/v1/mandateHistory
pth.toggle.visibility.api.url = https://api-staging.paytm.com/pth/ext/v3/toggleVisibility
pth.search.filter.api.url = https://api-staging.paytm.com/pth/ext/v3/listing/filter
pth.upi.passbook.api.url = https://api-staging.paytm.com/pth/ext/v3/upi/listing
pth.upi.lite.passbook.api.url = https://api-staging.paytm.com/pth/ext/v3/upi_lite/listing
pth.upi.cc.passbook.api.url = https://api-staging.paytm.com/pth/ext/v3/upi_cc/listing
pth.tag.summary.api.url = https://api-staging2.paytm.com/pth/ext/v1/tag/tagSummary
pth.user.tags.summary.api.url = https://api-staging2.paytm.com/pth/ext/v1/tag/getUserTagsSummary

autoTagging.details.aerospike.set.name = auto_tagging_details_set
autoTagging.aerospike.host-name = pth-comb-new-aerospike-1.uth.paytm.local,pth-comb-new-aerospike-2.uth.paytm.local
autoTagging.aerospike.port = 3000
autoTagging.aerospike.namespace = pth