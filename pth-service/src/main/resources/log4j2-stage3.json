{"configuration": {"name": "ppb", "properties": {"property": [{"name": "log-path", "value": "/log/payment-transaction-history-service"}, {"name": "archive", "value": "${log-path}/archive"}, {"name": "pattern", "value": "%X{requestId} - [%-5level] %d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z'} [%t] %c{1} - %msg%n"}, {"name": "jsonPattern", "value": "{\"requestId\" : \"%X{requestId}\",\"level\" : \"%-5level\",\"time\" : \"%d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z'}\",\"thread\" : \"%t\",\"class\" : \"%c{1}\",\"line\" : \"%L\",\"message\" : \"%msg\"}%n"}, {"name": "accessLogPattern", "value": "{\"requestId\" :%X{requestId}, \"level\" : [%-5level], \"time\" : %d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z'},\"thread\" : [%t],\"class\" :  %c{1}, \"message\" : %msg}%n"}]}, "appenders": {"Console": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "SYSTEM_OUT", "PatternLayout": {"pattern": "${pattern}"}}, "RollingFile": [{"name": "RollingLogFile-<PERSON><PERSON><PERSON>", "fileName": "${log-path}/stage3_${hostName}_application.log", "filePattern": "${archive}/stage3_${hostName}_application.log.%d{dd-MM-yy-HH}-%i.zst", "PatternLayout": {"pattern": "${jsonPattern}"}, "Policies": {"SizeBasedTriggeringPolicy": {"size": "1 GB"}, "TimeBasedTriggeringPolicy": {"interval": "24", "modulate": "true"}}, "DefaultRolloverStrategy": {"fileIndex": "nomax"}}, {"name": "AccessRollingLogFile-Appender", "fileName": "${log-path}/stage3_${hostName}_access.log", "filePattern": "${archive}/stage3_${hostName}_access.log.%d{dd-MM-yy-HH}_%i.zst", "PatternLayout": {"pattern": "${accessLogPattern}"}, "Policies": {"SizeBasedTriggeringPolicy": {"size": "2 GB"}, "TimeBasedTriggeringPolicy": {"interval": "24", "modulate": "true"}}, "DefaultRolloverStrategy": {"fileIndex": "nomax"}}], "Rewrite": {"name": "rewrite", "LogInterceptor": {}, "AppenderRef": {"ref": "RollingLogFile-<PERSON><PERSON><PERSON>"}}}, "loggers": {"logger": [{"name": "com.org", "level": "debug", "additivity": "false", "appender-ref": [{"ref": "rewrite", "level": "debug"}]}, {"name": "com.jcraft.jsch", "level": "error", "additivity": "false", "appender-ref": [{"ref": "RollingLogFile-<PERSON><PERSON><PERSON>", "level": "debug"}]}, {"name": "reactor.netty.http.server.AccessLog", "level": "info", "additivity": "false", "appender-ref": [{"ref": "AccessRollingLogFile-Appender", "level": "info"}]}, {"name": "org.zalando.logbook", "level": "trace", "additivity": "false", "appender-ref": [{"ref": "rewrite", "level": "trace"}]}], "root": {"level": "info", "appender-ref": [{"ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "level": "info"}, {"ref": "rewrite", "level": "info"}]}}}}