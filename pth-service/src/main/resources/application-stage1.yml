cache:
  typeMap:
    aerospikeCache:
      type: AEROSPIKE
      cacheManagerName: aerospike_cache_manager
      config:
        host: pth-comb-new-aerospike-1.uth.paytm.local
        port: 3000
        writeDefaultSleepBetweenRetries: 50
        writeDefaultCacheSocketTimeout: 300
        writeDefaultCacheTotalTimeout: 500
        readDefaultSleepBetweenRetries: 30
        readDefaultCacheSocketTimeout: 300
        readDefaultCacheTotalTimeout: 500
      instances:
        latestTransactionCache:
          setName: latestApiSet
          ttl: 1d
          namespace: pth
        detailApiCache:
          setName: detailApiSet
          ttl: 1m
          namespace: pth
        rewind_txn_analytics_data_cache:
          setName: rewind_txn_analytics_data_cache
          ttl: 60s
          namespace: pth
        rewind_meta_data_cache:
          setName: rewind_meta_data_cache
          ttl: 60s
          namespace: pth

# ratelimiter instance will be union of application.yml and environmental yml file
ratelimiter:
  instances:
    yearInRewindRateLimiter:
      paramMap:
        api: ext/v1/spend-analytics/rewindData
        client: all
      rateLimitInstanceConfig:
        limitForPeriod: 5
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    monthlyAnalyticsRateLimiter:
      paramMap:
        api: ext/v1/spend-analytics/monthly
      rateLimitInstanceConfig:
        limitForPeriod: 5
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    monthlySpendDataRateLimiter:
      paramMap:
        api: ext/v1/spend-analytics/data
      rateLimitInstanceConfig:
        limitForPeriod: 5
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    spendDataBreakUpRateLimiter:
      paramMap:
        api: ext/v1/spend-analytics/dataBreakup
      rateLimitInstanceConfig:
        limitForPeriod: 5
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    spendAggDataRateLimiter:
      paramMap:
        api: ext/v1/spend-analytics/aggregatedData
      rateLimitInstanceConfig:
        limitForPeriod: 10
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    getTagRateLimiter:
      paramMap:
        api: GET_TAGS_API
      rateLimitInstanceConfig:
        limitForPeriod: 15
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    updateTagRateLimiter:
      paramMap:
        api: ext/v1/tag/updateTags
      rateLimitInstanceConfig:
        limitForPeriod: 10
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    getUserTagsSummaryRateLimiter:
      paramMap:
        api: ext/v1/tag/getUserTagsSummary
      rateLimitInstanceConfig:
        limitForPeriod: 5
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    latestTxnRateLimiter:
      paramMap:
        client: PMS
        api: int/v1/latestTxn
      rateLimitInstanceConfig:
        limitForPeriod: 1
        limitRefreshPeriod: 10s
        timeoutDuration: 0
    metaDataRateLimiter:
      paramMap:
        api: ext/v1/metaData
      rateLimitInstanceConfig:
        # >>>>>>need to change these, revisit this values
        limitForPeriod: 150
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    uthCstBot:
      paramMap:
        api: ext/v1/cstBotDetail
      rateLimitInstanceConfig:
        limitForPeriod: 30
        limitRefreshPeriod: 5s
        timeoutDuration: 0
    detailRateLimiter:
      paramMap:
        api: DETAIL_API
      rateLimitInstanceConfig:
        limitForPeriod: 25
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    cstPanel:
      paramMap:
        client: UPI
        api: int/v1/txnList/cstPanel
      rateLimitInstanceConfig:
        limitForPeriod: 100
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    chatDetailRateLimiter:
      paramMap:
        client: chat
        api: DETAIL_API
      rateLimitInstanceConfig:
        limitForPeriod: 5
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    txnList:
      paramMap:
        client: UPI
        api: int/v1/txnList
      rateLimitInstanceConfig:
        limitForPeriod: 10
        limitRefreshPeriod: 10s
        timeoutDuration: 0
    cstTxnListRateLimiter:
      paramMap:
        client: CST
        api: int/v1/txnList
      rateLimitInstanceConfig:
        limitForPeriod: 2
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    anyTxnRateLimiter:
      paramMap:
        client: upiswitch
        api: int/v1/fetch/anyTxn
      rateLimitInstanceConfig:
        limitForPeriod: 11
        limitRefreshPeriod: 10s
        timeoutDuration: 0
    searchRateLimiter:
      paramMap:
        api: LISTING_API
      rateLimitInstanceConfig:
        limitForPeriod: 90
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    upiPassbookListingRateLimiter:
      paramMap:
        api: UPI_PASSBOOK_LISTING_API
      rateLimitInstanceConfig:
        limitForPeriod: 30
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    searchRateLimiterBgAppSync:
      paramMap:
        api: ext/v3/bg/listing
      rateLimitInstanceConfig:
        limitForPeriod: 2
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    statementApiRateLimiter:
      paramMap:
        client: pth_consumer_service
        api: int/v1/statement
      rateLimitInstanceConfig:
        limitForPeriod: 100
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    recentTxnRateLimiter:
      paramMap:
        api: ext/v1/recent-txn
      rateLimitInstanceConfig:
        limitForPeriod: 75
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    mandateHistoryAndroid:
      paramMap:
        client: androidapp
        api: ext/v1/mandate/history
      rateLimitInstanceConfig:
        limitForPeriod: 50
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    mandateHistoryIos:
      paramMap:
        client: iosapp
        api: ext/v1/mandate/history
      rateLimitInstanceConfig:
        limitForPeriod: 50
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    mandateHistoryCst:
      paramMap:
        client: CST
        api: int/v1/mandate/history
      rateLimitInstanceConfig:
        limitForPeriod: 50
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    cstSearch:
      paramMap:
        client: CST
        api: int/v3/cstSearch
      rateLimitInstanceConfig:
        limitForPeriod: 10
        limitRefreshPeriod: 1s
        timeoutDuration: 0
    othersRateLimiter:
      paramMap:
        api: /*
      rateLimitInstanceConfig:
        limitForPeriod: 10
        limitRefreshPeriod: 1s
        timeoutDuration: 0

external.client:
  properties:
    BMSService:
      baseUrl: https://phs-staging-internal.paytm.com
      secretKey: aB9htNYgOa
      socketTimeout: 1000
      connectionTimeout: 1000
      writeTimeout: 1000
      noOfRetries: 1
    KubernetesService:
      baseUrl: http://watchdog.default.svc.cluster.local
      path: /api/v1/namespace/uthstage/app/pth-service
      socketTimeout: 1000
      connectionTimeout: 1000
      writeTimeout: 1000
      noOfRetries: 1

user-aerospike-ratelimit-config:
  aerospike-url: pth-comb-new-aerospike-1.uth.paytm.local
  aerospike-namespace: pth
  aerospike-port: 3000
  setname: ratelimit-uth
  write-default-cache-socket-timeout: 500
  write-default-cache-total-timeout: 900
  write-default-sleep-between-retries: 50
  write-default-cache-expiry-time: 7200
  read-default-cache-total-timeout: 900
  read-default-cache-socket-timeout: 400
  read-default-sleep-between-retries: 50

user-aerospike-daily-ratelimit-config:
  aerospike-url: pth-comb-new-aerospike-1.uth.paytm.local
  aerospike-namespace: pth
  aerospike-port: 3000
  setname: daily-ratelimit-uth
  write-default-cache-socket-timeout: 500
  write-default-cache-total-timeout: 900
  write-default-sleep-between-retries: 50
  write-default-cache-expiry-time: 86400
  read-default-cache-total-timeout: 900
  read-default-cache-socket-timeout: 400
  read-default-sleep-between-retries: 50

internal-localization-service:
  base-url: https://payment-transaction-history-service-ite.orgk.com
  messages-path: /payments-history-v2/int/v1/getMessages
  languages-path: /payments-history-v2/int/v1/getLanguages
  connection-request-timeout: 1000
  connect-timeout: 1000
  socket-timeout: 1000
  secret: ${UTH_UPI_SECRET_KEY}

localization-service:
  registration-name: PTH
  base-url: https://digitalapiproxy-staging.paytm.com
  messages-path: /localisation/v1/getMessages
  languages-path: /localisation/v1/getLanguages
  connection-request-timeout: 1000
  connect-timeout: 1000
  socket-timeout: 1000