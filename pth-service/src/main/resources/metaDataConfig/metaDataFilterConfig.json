[{"filterName": "AccountType", "requestParam": "paymentSystem", "displayName": "Payment Source", "displayNameLocaleKey": "metadata_accounts_filter_header", "values": [{"requestParamValue": "upi", "displayName": "APP_ACCOUNTS_Upi", "isActive": true, "identifierKey": "upiIdentifier", "displayOrder": 1, "type": "dynamic"}, {"requestParamValue": "upi_credit_card", "displayName": "APP_ACCOUNTS_UpiCreditCard", "isActive": true, "identifierKey": "upiIdentifier", "displayOrder": 2, "type": "dynamic"}, {"requestParamValue": "paytm_postpaid", "displayName": "APP_ACCOUNTS_PaytmPostpaid", "isActive": true, "displayOrder": 3, "type": "dynamic"}, {"requestParamValue": "upi_lite", "displayName": "<PERSON><PERSON>", "isActive": true, "displayOrder": 4, "type": "dynamic"}], "isActive": true, "multiValueSelect": true, "displayOrder": 1, "whitelistedUsersCustIds": -1, "whitelistingPercentage": 0, "widget": {"type": "CHECKBOX_VERTICAL", "maxVersion": 0, "minVersion": 0}, "clientDetails": {"UPI": {"clientName": "UPI", "isActive": false}}}, {"filterName": "TxnIndicator", "requestParam": "txnIndicator", "displayName": "TxnIndicator", "values": [{"requestParamValue": "credit", "displayName": "Credit", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 1, "type": "static"}, {"requestParamValue": "debit", "displayName": "Debit", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 2, "type": "static"}], "isActive": false, "multiValueSelect": true, "displayOrder": 2, "whitelistedUsersCustIds": 0, "whitelistingPercentage": 0, "widget": {"type": "RECT_BOX_HORIZONTAL", "maxVersion": 0, "minVersion": 0}, "clientDetails": {"UPI": {"clientName": "UPI", "isActive": true}}}, {"filterName": "Status", "requestParam": "status", "displayName": "Status", "displayNameLocaleKey": "metadata_status_filter_header", "values": [{"requestParamValue": "success", "displayName": "Successful", "displayNameLocaleKey": "metadata_status_filter_successful_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 1, "type": "static"}, {"requestParamValue": "pending", "displayName": "Pending", "displayNameLocaleKey": "metadata_status_filter_pending_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 2, "type": "static"}, {"requestParamValue": "failure", "displayName": "Failed", "displayNameLocaleKey": "metadata_status_filter_failed_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 3, "type": "static"}], "isActive": true, "multiValueSelect": true, "displayOrder": 3, "whitelistedUsersCustIds": -1, "whitelistingPercentage": 0, "widget": {"type": "RECT_BOX_HORIZONTAL", "maxVersion": 0, "minVersion": 0}, "clientDetails": {"UPI": {"clientName": "UPI", "isActive": true}}}, {"filterName": "TxnCategory", "requestParam": "txnCategory", "displayName": "Type", "displayNameLocaleKey": "metadata_txn_type_filter_header", "values": [{"requestParamValue": "money_sent", "displayName": "Paid", "displayNameLocaleKey": "metadata_txn_type_filter_paid_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 1, "type": "static"}, {"requestParamValue": "money_received", "displayName": "Received", "displayNameLocaleKey": "metadata_txn_type_filter_received_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 2, "type": "static"}, {"requestParamValue": "self_transfer", "displayName": "Self Transfer", "displayNameLocaleKey": "metadata_txn_type_filter_self_transfer_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 3, "type": "static"}], "isActive": true, "multiValueSelect": true, "displayOrder": 4, "whitelistedUsersCustIds": -1, "whitelistingPercentage": 0, "widget": {"type": "RECT_BOX_HORIZONTAL", "maxVersion": 0, "minVersion": 0}, "clientDetails": {"UPI": {"clientName": "UPI", "isActive": true}}}, {"filterName": "AmountRange", "requestParam": "amountRange", "displayName": "Amount", "displayNameLocaleKey": "metadata_amount_filter_header", "values": [{"requestParamValue": "APP_MIN_AMOUNT", "displayName": "<PERSON>", "displayNameLocaleKey": "metadata_amount_filter_min_value", "displayOrder": 1, "type": "dynamic", "isActive": true, "validation": {"value": "%s", "message": "Please select Min amount greater than ₹%s", "messageLocaleKey": "metadata_amount_filter_min_value_validation"}}, {"requestParamValue": "APP_MAX_AMOUNT", "displayName": "<PERSON>", "displayNameLocaleKey": "metadata_amount_filter_max_value", "displayOrder": 2, "type": "dynamic", "isActive": true, "validation": {"value": "%s", "message": "Please select Max amount less than ₹%s", "messageLocaleKey": "metadata_amount_filter_max_value_validation"}}], "isActive": true, "validation": {"message": "Min amount should be less than Max amount", "messageLocaleKey": "metadata_amount_filter_validation"}, "displayOrder": 5, "whitelistedUsersCustIds": -1, "whitelistingPercentage": 0, "widget": {"type": "RANGE_INPUT", "minVersion": 0, "maxVersion": 0}, "clientDetails": {"UPI": {"clientName": "UPI", "isActive": true}}}, {"filterName": "DateRange", "requestParam": "date", "displayName": "Date Range", "displayNameLocaleKey": "metadata_date_range_filter_header", "values": [{"requestParamValue": "m:1-0", "displayName": "Last 1 Month", "displayNameLocaleKey": "metadata_date_range_filter_value_one_month", "nestedFilter": null, "isActive": true, "multiValueSelect": false, "displayOrder": 1, "type": "dynamic"}, {"requestParamValue": "m:3-0", "displayName": "Last 3 Months", "displayNameLocaleKey": "metadata_date_range_filter_value_three_month", "nestedFilter": null, "isActive": true, "multiValueSelect": false, "displayOrder": 2, "type": "dynamic"}, {"requestParamValue": "m:6-0", "displayName": "Last 6 Months", "displayNameLocaleKey": "metadata_date_range_filter_value_six_month", "nestedFilter": null, "isActive": true, "multiValueSelect": false, "displayOrder": 3, "type": "dynamic"}, {"requestParamValue": "m:12-0", "displayName": "Last 1 Year", "displayNameLocaleKey": "metadata_date_range_filter_value_one_year", "nestedFilter": null, "isActive": true, "multiValueSelect": false, "displayOrder": 4, "type": "dynamic"}, {"filterValueName": "customDateRange", "requestParamValue": "date<PERSON><PERSON><PERSON>", "displayName": "<PERSON>ose <PERSON>", "nestedFilter": [{"nestedFilterRequestParam": "dateRangeValue", "nestedFilterDisplayName": "APP_DATE_INPUT", "nestedFilterRequestParamValues": [{"paramValue": "APP_START_DATE_EPOCH", "displayName": "Start Date", "displayOrder": 1}, {"paramValue": "APP_END_DATE_EPOCH", "displayName": "End Date", "displayOrder": 2}]}], "isActive": true, "multiValueSelect": false, "displayOrder": 4, "type": "dynamic"}], "isActive": true, "multiValueSelect": false, "displayOrder": 5, "whitelistedUsersCustIds": -1, "whitelistingPercentage": 0, "widget": {"type": "RECT_BOX_WITH_DATE_INPUT", "minVersion": 0, "maxVersion": 0}, "clientDetails": {"UPI": {"clientName": "UPI", "isActive": true}, "WALLET": {"clientName": "WALLET", "isActive": true}}}, {"filterName": "VerticalName", "requestParam": "verticalName", "displayName": "Orders & Bookings", "displayNameLocaleKey": "metadata_vertical_name_filter_header", "values": [{"requestParamValue": "Recharges & Bill Payments", "displayName": "Recharges & Bill Payments", "displayNameLocaleKey": "metadata_vertical_name_filter_recharge_and_bill_payments_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 1, "type": "static"}, {"requestParamValue": "Gift Cards & Deals", "displayName": "Gift Cards & Deals", "displayNameLocaleKey": "metadata_vertical_name_filter_gift_card_and_deals_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 2, "type": "static"}, {"requestParamValue": "Travel", "displayName": "Travel", "displayNameLocaleKey": "metadata_vertical_name_filter_travel_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 3, "type": "static"}, {"requestParamValue": "Movies & Events", "displayName": "Movies & Events", "displayNameLocaleKey": "metadata_vertical_name_filter_movies_and_events_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 4, "type": "static"}, {"requestParamValue": "Shopping", "displayName": "Shopping", "displayNameLocaleKey": "metadata_vertical_name_filter_shopping_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 5, "type": "static"}, {"requestParamValue": "Insurance", "displayName": "Insurance", "displayNameLocaleKey": "metadata_vertical_name_filter_insurance_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 6, "type": "static"}, {"requestParamValue": "Paytm Gold", "displayName": "Paytm Gold", "displayNameLocaleKey": "metadata_vertical_name_filter_paytm_gold_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 7, "type": "static"}, {"requestParamValue": "Paytm Stores", "displayName": "Paytm Stores", "displayNameLocaleKey": "metadata_vertical_name_filter_paytm_stores_value", "nestedFilter": null, "isActive": true, "multiValueSelect": null, "displayOrder": 8, "type": "static"}], "isActive": true, "multiValueSelect": true, "displayOrder": 7, "whitelistedUsersCustIds": -1, "whitelistingPercentage": 0, "widget": {"type": "RECT_BOX_HORIZONTAL", "maxVersion": 0, "minVersion": 0}, "clientDetails": {"UPI": {"clientName": "UPI", "isActive": true}}}]