package com.org.com.webapiv2.dto.tagConfiguration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.enums.TagConfigurationStatusEnum;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TagConfigurationRequestDto {

	private Integer id;

	private int txnType;

	private String uthCategories;

	private String dateRange;

	private String receiverPhonebookNames;

	private String amountRanges;

	private String verticalIds;

	private String suggestedTags;

	private String autoTag;

	private int priority;

	public static TagConfigurationEntity toEntity(TagConfigurationRequestDto requestDto,
			TagConfigurationEntity entity) {
		if (entity == null) {
			entity = new TagConfigurationEntity();
		}
		entity.setTxnType(requestDto.getTxnType());
		entity.setUthCategories(requestDto.getUthCategories());
		entity.setDateRange(requestDto.getDateRange());
		entity.setReceiverPhonebookNames(requestDto.getReceiverPhonebookNames());
		entity.setAmountRanges(requestDto.getAmountRanges());
		entity.setVerticalIds(requestDto.getVerticalIds());
		entity.setSuggestedTags(requestDto.getSuggestedTags());
		entity.setAutoTag(requestDto.getAutoTag());
		entity.setPriority(requestDto.getPriority());
		entity.setStatus(TagConfigurationStatusEnum.ENABLE);
		return entity;
	}

}
