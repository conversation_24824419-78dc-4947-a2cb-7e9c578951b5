package com.org.com.webapiv2.cstCategoryCode;;

import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
public class CategoryNarration {

	@NonNull
	private long narrationId;

	private String narration;

	private String narrationDirection;

	private String detailNarration;

	private String passbookCategory;

	private String displayName;

	private String createdOn;

	private String updatedOn;

	private String updatedBy;

	private String narrationText;

	private String narrationTextPlaceHolder;

	private String defaultNarrationText;

}
