package com.org.com.webapiv2.dto.upiCstPanel;

import com.org.panaroma.commons.utils.JsonUtils;

import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class CstPanelResponseTxn {

	String txnId;

	String status;

	String amount;

	String date;

	String updatedDate;

	String note;

	String initiationType;

	String rrn;

	String errorCode;

	String refCategory;

	String merchantId;

	String orderId;

	String businessType;

	String category;

	String orgTxnId;

	String channel;

	String npciRespCode;

	String expAfterMin;

	String merchantTxnId;

	String refUrl;

	String txnType;

	String irc;

	String firstPhase;

	String npciTs;

	String applicationUpdatedOn;

	String umn;

	FwdTxnDetails fwdTxnDetails; // PTH-918 --> CST Bot Flow Requirements

	List<RefundDetails> refundDetails; // PTH-918 --> CST Bot Flow Requirements

	Map<String, String> contextMap;

	Long txnDateEpoch;

	Long updatedDateEpoch;

	boolean isHiddenTxn;

	UserInfo payer;

	UserInfo payee;

	String OtherUpiAppName; // PTH-1170 , PSP UPI App Names

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}