package com.org.com.webapiv2.service.impl.metaData;

import static com.org.com.webapiv2.constants.MetaDataConstants.AMOUNT_RANGE;
import static com.org.com.webapiv2.constants.MetaDataConstants.CUSTOM_DATE_RANGE_FILTER_VALUE_NAME;
import static com.org.com.webapiv2.constants.MetaDataConstants.ConfigFileName.FILTER_CONFIG;
import static com.org.com.webapiv2.constants.MetaDataConstants.DATE_RANGE_FILTER_NAME;
import static com.org.com.webapiv2.constants.MetaDataConstants.Status.ALL;
import static com.org.com.webapiv2.constants.MetaDataConstants.Status.FALSE;
import static com.org.com.webapiv2.constants.MetaDataConstants.Status.TRUE;
import static com.org.com.webapiv2.constants.MetaDataConstants.Types.FILTER;
import static com.org.panaroma.commons.constants.ConfigPropertiesEnum.FILTER_VERTICAL_NAME_ENABLED;
import static com.org.panaroma.commons.constants.Constants.SINGLE_QUOTE;
import static com.org.panaroma.commons.constants.LocalizationConstants.LOCALE;
import static com.org.panaroma.commons.constants.LocalizationConstants.LOCALIZATION_ENABLED;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT_RANGE_FILTER_ENABLED;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_FILTER_DISABLED;
import static com.org.panaroma.commons.constants.WebConstants.VERTICAL_NAME;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.com.webapiv2.dto.metaData.FilterNode;
import com.org.com.webapiv2.dto.metaData.FilterTypeEnum;
import com.org.com.webapiv2.dto.metaData.FilterValueNode;
import com.org.com.webapiv2.dto.metaData.MetaDataRequest;
import com.org.com.webapiv2.dto.metaData.MetaDataResponse;
import com.org.com.webapiv2.dto.metaData.Validation;
import com.org.com.webapiv2.scheduler.MetaInfoScheduler;
import com.org.com.webapiv2.service.metaData.IMetaDataHandler;
import com.org.com.webapiv2.utility.FileUtility;
import com.org.com.webapiv2.utility.MetaDataUtility;
import com.org.panaroma.commons.enums.PthVersion;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.NumberFormatUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.commons.utils.PthVersionUtility;
import com.org.panaroma.commons.utils.Utility;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class MetaDataFilterHandler implements IMetaDataHandler {

	private ObjectMapper objectMapper;

	private MetaDataUtility metaDataUtility;

	private Map<String, List<FilterNode>> filtersMap = new HashMap<>();

	private Map<String, List<FilterNode>> defaultFiltersMap = new HashMap<>();

	private List<String> supportedFiltersForAllUser;

	private MetaInfoScheduler metaInfoScheduler;

	private Integer minAmountLimit;

	private Integer maxAmountLimit;

	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public MetaDataFilterHandler(
			@Value("${supported.filters.list.for.all.user}") final List<String> supportedFiltersForAllUser,
			@Value("${min-amount-limit-for-amount-range-filter}") final Integer minAmountLimit,
			@Value("${max-amount-limit-for-amount-range-filter}") final Integer maxAmountLimit,
			final ObjectMapper objectMapper, final MetaDataUtility metaDataUtility,
			final MetaInfoScheduler metaInfoScheduler,
			final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.supportedFiltersForAllUser = supportedFiltersForAllUser;
		this.minAmountLimit = minAmountLimit;
		this.maxAmountLimit = maxAmountLimit;
		this.objectMapper = objectMapper;
		this.metaDataUtility = metaDataUtility;
		this.metaInfoScheduler = metaInfoScheduler;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	@PostConstruct
	public void init() {
		try {
			String jsonString = FileUtility.readFile(FILTER_CONFIG);
			TypeReference<List<FilterNode>> typeReference = new TypeReference<List<FilterNode>>() {
			};
			List<FilterNode> filtersList = objectMapper.readValue(jsonString, typeReference);
			initializeFilterMap(filtersList);
		}
		catch (Exception e) {
			log.error("Error while initializing Filter Config for metaData : {}", CommonsUtility.exceptionFormatter(e));
			throw new RuntimeException();
		}
	}

	private void initializeFilterMap(final List<FilterNode> filterList) throws Exception {
		filtersMap.put(TRUE, new ArrayList<>());
		filtersMap.put(FALSE, new ArrayList<>());
		filtersMap.put(ALL, new ArrayList<>());

		defaultFiltersMap.put(TRUE, new ArrayList<>());
		defaultFiltersMap.put(FALSE, new ArrayList<>());
		defaultFiltersMap.put(ALL, new ArrayList<>());

		for (FilterNode filter : filterList) {
			if (AMOUNT_RANGE.equals(filter.getFilterName())) {
				populateAmountRangeFilterLimits(filter);
			}
			boolean isActive = filter.getIsActive() != null ? filter.getIsActive() : false;
			filtersMap.get(ALL).add(filter);
			checkAndAddDefaultFilter(filter, ALL);
			if (isActive) {
				FilterNode filterNode = (FilterNode) Utility.getCopy(filter);
				if (!AMOUNT_RANGE.equals(filterNode.getFilterName())) {
					filterNode.getValues().removeIf(value -> !(Boolean.TRUE.equals(value.getIsActive())));
				}
				filtersMap.get(TRUE).add(filterNode);
				checkAndAddDefaultFilter(filterNode, TRUE);
			}
			else {
				filtersMap.get(FALSE).add(filter);
				checkAndAddDefaultFilter(filter, FALSE);
			}
		}
	}

	private void populateAmountRangeFilterLimits(final FilterNode filterNode) {
		for (FilterValueNode filterValueNode : filterNode.getValues()) {
			Validation validation = filterValueNode.getValidation();
			if ("APP_MIN_AMOUNT".equals(filterValueNode.getRequestParamValue())) {
				validation.setValue(String.format(validation.getValue(), minAmountLimit));
				validation.setMessage(String.format(validation.getMessage(),
						NumberFormatUtility.numberInIndianAmountFormatForAmountRangeFilter(minAmountLimit)));
			}
			else if ("APP_MAX_AMOUNT".equals(filterValueNode.getRequestParamValue())) {
				validation.setValue(String.format(validation.getValue(), maxAmountLimit));
				validation.setMessage(String.format(validation.getMessage(),
						NumberFormatUtility.numberInIndianAmountFormatForAmountRangeFilter(maxAmountLimit)));
			}
		}
	}

	private void checkAndAddDefaultFilter(final FilterNode filterNode, final String key) {
		if (filterNode != null && supportedFiltersForAllUser.contains(filterNode.getRequestParam())) {
			defaultFiltersMap.get(key).add(filterNode);
		}
	}

	private List<FilterNode> getFilterNodes(final String activeFilters) {
		List<FilterNode> filterNodes = new ArrayList<>();
		List<FilterNode> nodes = filtersMap.get(activeFilters.toLowerCase());
		if (nodes == null || nodes.isEmpty()) {
			nodes = defaultFiltersMap.get(activeFilters.toLowerCase());
		}
		// copy of data present in filtersMap is used as it is a shared variable & can
		// cause issues in multi-threaded env if updated
		for (FilterNode filterNode : nodes) {
			filterNodes.add((FilterNode) Utility.getCopy(filterNode));
		}
		return filterNodes;
	}

	@Override
	public void getData(final MetaDataRequest request, final MetaDataResponse response) {
		if (configurablePropertiesHolder.getProperty(SEARCH_FILTER_DISABLED, Boolean.class)
				|| StringUtils.isEmpty(request.getActiveFilters())) {
			return;
		}

		List<FilterNode> activeFilterNodes = getFilterNodes(request.getActiveFilters());
		if (activeFilterNodes == null || activeFilterNodes.isEmpty()) {
			return;
		}
		List<FilterNode> whitelistedFilterNodes = new ArrayList<>();

		double metaDataVersion;
		try {
			metaDataVersion = Double.parseDouble(request.getMetadataApiVersion());
		}
		catch (NullPointerException e) {
			metaDataVersion = 0;
			log.info("No metaData version passed");
		}
		catch (NumberFormatException e) {
			metaDataVersion = 0;
			log.warn("Error parsing metaData version : {}", metaDataVersion);
		}

		for (FilterNode node : activeFilterNodes) {
			// [PTH-901] Skip ONUS filter for older app versions or if disabled via config
			if (VERTICAL_NAME.equals(node.getRequestParam()) && (!PthVersionUtility.isVerticalNameFilterSupported()
					|| !configurablePropertiesHolder.getProperty(FILTER_VERTICAL_NAME_ENABLED, Boolean.class))) {
				continue;
			}
			// Don't set dateRangeFilter node if pthVersion is less than 2.
			// Due to frontEnd android issue :-
			// https://jira.mypaytm.com/browse/CA-206965?src=confmacro
			if (DATE_RANGE_FILTER_NAME.equals(node.getFilterName()) && !PthVersionUtility.isDateRangeFilterRequired()) {
				continue;
			}
			if (DATE_RANGE_FILTER_NAME.equalsIgnoreCase(node.getFilterName())
					&& PthVersionUtility.checkIfUpdatedDateRangeFilterNodeRequired()
					&& (request.getMetadataApiVersion() == null || metaDataVersion < 2)) {
				/*
				 * getUpdatedDateRangeFilterNode will return null if there is some
				 * exception while updating the DateRange filter node So we will not
				 * include the DateRange filter node in response
				 */
				node = getUpdatedDateRangeFilterNode(metaInfoScheduler.getMetaInfo().getFromDate(), node);
			}
			/*
			 * Since Amount Range filter is not supported in older app versions, app will
			 * be sending a new param metadataApiVersion=1.0 in Metadata API request. We
			 * will send Amount Range filter in Metadata API response only when it's
			 * enabled and metadataApiVersion >= 1.0
			 */
			if (AMOUNT_RANGE.equalsIgnoreCase(node.getFilterName())) {
				if (Boolean.FALSE
					.equals(configurablePropertiesHolder.getProperty(AMOUNT_RANGE_FILTER_ENABLED, Boolean.class))
						|| !PthVersionUtility.isRequestPthVersionGreaterThanOrEqualTo(PthVersion.V_1_0)) {
					continue;
				}
			}

			if (Objects.nonNull(node) && metaDataUtility.isNodeWhitelistedForUser(node.getWhitelistedUsersCustIds(),
					node.getWhitelistingPercentage(), request.getEntityId())) {
				if (configurablePropertiesHolder.getProperty(LOCALIZATION_ENABLED, Boolean.class)) {
					metaDataUtility.localizeFilterNode(node);
				}
				whitelistedFilterNodes.add(node);
			}
		}
		response.setFilters(whitelistedFilterNodes);
	}

	@Override
	public boolean getType(final String type) {
		return FILTER.equalsIgnoreCase(type);
	}

	// Method for updating months list in date range filter
	private FilterNode getUpdatedDateRangeFilterNode(final String listingFromDate,
			final FilterNode existingDateFilterNode) {
		existingDateFilterNode.setDisplayName("Months");
		existingDateFilterNode.setMultiValueSelect(true);
		try {
			FilterValueNode lastValueNode = existingDateFilterNode.getValues()
				.get(existingDateFilterNode.getValues().size() - 1);
			existingDateFilterNode.getValues().clear();

			// got month display name and request param value pair list
			List<Pair<String, String>> monthsDisplayAndRequestNameList = metaDataUtility
				.getMonthDisplayNamesAndRequestParamValues(listingFromDate);

			int displayOrder = 1;

			// traversing month display name and request param value pair list and adding
			// value node in filter node
			for (Pair<String, String> displayAndRequestParamPair : monthsDisplayAndRequestNameList) {
				FilterValueNode filterValueNode = new FilterValueNode();

				if (configurablePropertiesHolder.getProperty(LOCALIZATION_ENABLED, Boolean.class)) {
					// If localization is enabled, localize the month name which is of
					// October'2022 format
					String month = displayAndRequestParamPair.getKey()
						.substring(0, displayAndRequestParamPair.getKey().indexOf(SINGLE_QUOTE));
					String localizedMonth = LocalizedDataCacheService.getLocalizedValue(month.toLowerCase(), month,
							ThreadContext.get(LOCALE));
					filterValueNode.setDisplayName(displayAndRequestParamPair.getKey().replace(month, localizedMonth));
				}
				else {
					filterValueNode.setDisplayName(displayAndRequestParamPair.getKey());
				}

				filterValueNode.setRequestParamValue(displayAndRequestParamPair.getValue());
				filterValueNode.setDisplayOrder(displayOrder);
				filterValueNode.setType(FilterTypeEnum.STATIC.getValue());
				filterValueNode.setIsActive(Boolean.TRUE);

				existingDateFilterNode.getValues().add(filterValueNode);
				// increment of display order
				displayOrder++;
			}

			// adding custom date range node at end if it is active
			if (CUSTOM_DATE_RANGE_FILTER_VALUE_NAME.equalsIgnoreCase(lastValueNode.getFilterValueName())) {
				lastValueNode.setDisplayOrder(displayOrder);
				existingDateFilterNode.getValues().add(lastValueNode);
			}
		}
		catch (Exception exception) {
			log.error("Some exception occurred while updating date range filter node. Exception : {}",
					CommonsUtility.exceptionFormatter(exception));
			return null;
		}
		return existingDateFilterNode;
	}

}
