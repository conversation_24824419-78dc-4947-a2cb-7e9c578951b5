package com.org.com.webapiv2.dao.impl;

import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INTERNAL_SERVER_ERROR;

import com.org.com.webapiv2.dao.IEsDao;
import com.org.com.webapiv2.dao.ITagDao;
import com.org.com.webapiv2.dto.tags.TagSummaryResponse;
import com.org.com.webapiv2.dto.tags.UserTagsSummaryResponse;
import com.org.com.webapiv2.model.SystemTagData;
import com.org.com.webapiv2.model.UserTagData;
import com.org.com.webapiv2.utility.LogExectutionTime;
import com.org.com.webapiv2.utility.aspect.LogExecutionTimeAndHitCount;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.entity.AutoTaggingDetails;
import com.org.panaroma.commons.enums.tag.SystemCategoryTagEnum;
import com.org.panaroma.commons.exceptionhandler.ExceptionBuilder;
import com.org.panaroma.commons.repository.AutoTaggingDetailsRepo;
import com.org.panaroma.commons.tags.IntelligentTagSuggestionService;
import com.org.panaroma.commons.utils.AutoTaggingUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.web.cache.AutoTaggingCacheClient;
import java.util.List;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Service
public class TagDaoImpl implements ITagDao {

	private final IEsDao esDao;

	private final AutoTaggingDetailsRepo autoTaggingDetailsRepo;

	private final AutoTaggingCacheClient autoTaggingCacheClient;

	private final String autoTaggingDetailsSetName;

	private final Integer autoTaggingDetailsExpiryTime;

	private final IntelligentTagSuggestionService intelligentTagSuggestionService;

	@Autowired
	public TagDaoImpl(final IEsDao esDao, final AutoTaggingDetailsRepo autoTaggingDetailsRepo,
			final AutoTaggingCacheClient autoTaggingCacheClient,
			@Value("${autoTagging.details.aerospike.set.name}") final String autoTaggingDetailsSetName,
			@Value("${autoTagging.details.aerospike.expiry.time.in.secs}") final Integer autoTaggingDetailsExpiryTime,
			final IntelligentTagSuggestionService intelligentTagSuggestionService) {
		this.esDao = esDao;
		this.autoTaggingCacheClient = autoTaggingCacheClient;
		this.autoTaggingDetailsRepo = autoTaggingDetailsRepo;
		this.autoTaggingDetailsSetName = autoTaggingDetailsSetName;
		this.autoTaggingDetailsExpiryTime = autoTaggingDetailsExpiryTime;
		this.intelligentTagSuggestionService = intelligentTagSuggestionService;
	}

	@Override
	@LogExectutionTime
	public List<UserTagData> getUserTagData(final String userId, final SearchContext searchContext) {
		return esDao.getUserTagData(userId, searchContext);
	}

	@Override
	public SystemTagData getSystemTagData(final TransformedTransactionHistoryDetail txn) {
		try {
			// Getting list of system tags from SystemCategoryTagEnum after removal of
			// dynamo DB
			List<String> systemTags = intelligentTagSuggestionService.getTagSuggestionsForTxn(txn);
			if (ObjectUtils.isNotEmpty(systemTags)) {
				SystemTagData systemTagData = new SystemTagData();
				systemTagData.setSystemId(txn.getTxnId());
				systemTagData.setTags(systemTags);
				return systemTagData;
			}

			// if system tags are not found for the given category id then return null
			log.info("System tags not found for the given txn id : {}", txn.getTxnId());
			return null;
		}
		catch (Exception e) {
			log.error("Exception in getting system tags : {}", CommonsUtility.exceptionFormatter(e));
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * Updates auto tagging details in both database and cache. This method is
	 * transactional to ensure atomicity - if any error occurs, the entire transaction
	 * will be rolled back.
	 * @param userId User identifier
	 * @param secPartyId Secondary party identifier
	 * @param autoTaggingDetails Auto tagging details to be updated
	 * @throws Exception if update operation fails
	 *
	 * see PTH-228 Auto-tagging implementation
	 */
	@Override
	@LogExecutionTimeAndHitCount
	@Transactional(rollbackFor = Exception.class)
	public void updateAutoTaggingDetails(final String userId, final String secPartyId,
			final AutoTaggingDetails autoTaggingDetails) throws Exception {
		if (StringUtils.isBlank(userId) || StringUtils.isBlank(secPartyId) || autoTaggingDetails == null) {
			log.error(
					"Invalid input parameters for updating auto tagging details userId: {}, secPartyId: {}, autoTaggingDetails: {}",
					userId, secPartyId, autoTaggingDetails);
			return;
		}

		// Update the auto tagging details in the database
		autoTaggingDetailsRepo.save(autoTaggingDetails);

		// Update the auto tagging details in the cache
		String aerospikeKey = AutoTaggingUtility.getAutoTaggingAerospikeKey(userId, secPartyId,
				autoTaggingDetails.getSecPartyType());
		autoTaggingCacheClient.saveAutoTaggingDetails(aerospikeKey, autoTaggingDetailsSetName,
				autoTaggingDetailsExpiryTime, autoTaggingDetails);
	}

	/**
	 * Retrieves auto tagging details from the database for a given user and secondary
	 * party.
	 * @param userId User identifier
	 * @param secPartyId Secondary party identifier
	 * @return AutoTaggingDetails if found, otherwise null
	 */
	@Override
	@LogExecutionTimeAndHitCount
	public AutoTaggingDetails getAutoTaggingDetails(final String userId, final String secPartyId,
			final Integer secPartyType) {
		if (StringUtils.isBlank(userId) || StringUtils.isBlank(secPartyId) || secPartyType == null) {
			log.error(
					"Invalid input parameters for getting auto tagging details userId: {}, secondPartyId: {}, type: {}",
					userId, secPartyId, secPartyType);
			return null;
		}

		// Get the auto tagging details from the database
		Optional<AutoTaggingDetails> autoTaggingDetailsOptional = autoTaggingDetailsRepo
			.findByUserIdAndSecPartyIdAndSecPartyType(userId, secPartyId, secPartyType);
		return autoTaggingDetailsOptional.orElse(null);
	}

	@Override
	@LogExectutionTime
	public UserTagsSummaryResponse getUserTagsSummary(final String userId, final String month,
			final SearchContext searchContext) {
		return esDao.getUserTagsSummary(userId, month, searchContext);
	}

	@Override
	@LogExectutionTime
	public TagSummaryResponse getTagSummary(final String userId, final String month, final String tag,
			final SearchContext searchContext) {
		return esDao.getTagSummary(userId, month, tag, searchContext);
	}

}
