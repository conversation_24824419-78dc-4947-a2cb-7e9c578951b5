package com.org.com.webapiv2.scheduler.helper;

import com.org.com.webapiv2.dto.elasticsearch.EsType;
import com.org.com.webapiv2.monitoring.MetricAgent;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.IndexUtility;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.http.util.EntityUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import java.util.Map;

import static com.org.com.webapiv2.constants.MonitoringConstants.COLON;
import static com.org.com.webapiv2.constants.MonitoringConstants.ES_CLUSTER;
import static com.org.com.webapiv2.constants.MonitoringConstants.INDEX;
import static com.org.com.webapiv2.constants.MonitoringConstants.INDEX_AVAILABILITY_VERIFICATION_COUNT;
import static com.org.com.webapiv2.constants.MonitoringConstants.RESULT;

@Component
@Log4j2
public class IndexAvailabilityCheckerSchedulerHelper {

	/**
	 * Enum representing the status of index availability check
	 */
	// Index exists in the cluster
	// Index confirmed to not exist
	// Error occurred while checking
	public enum IndexStatus {

		EXISTS, NOT_EXISTS, ERROR

	}

	private final String indexNamePrefix;

	private final RestHighLevelClient primaryEsRestClient;

	private final RestHighLevelClient secondaryEsRestClient;

	private final RestHighLevelClient tertiaryEsRestClient;

	private final MetricAgent metricAgent;

	private final ObjectMapper objectMapper;

	// Reduced 3 days from each property as one index deletion will cause its
	// unavailability 3 days beforehand.
	private static final Integer PRIMARY_ES_DATA_AVAILABILITY_DAYS = 210 - 3;

	private static final Integer SECONDARY_ES_DATA_AVAILABILITY_DAYS = 395 - 3;

	// Not reducing 3 days as only restoration will be there on this cluster and index
	// should available from start time only.
	private static final Integer TERTIARY_ES_DATA_AVAILABILITY_START_TIME_DAYS_DIFF = 365;

	@Autowired
	IndexAvailabilityCheckerSchedulerHelper(@Value("${index.name.prefix}") final String indexNamePrefix,
			@Qualifier("EsV1RestClient") final RestHighLevelClient primaryEsRestClient,
			@Qualifier("EsV2RestClient") final RestHighLevelClient secondaryEsRestClient,
			@Qualifier("TertiaryEsRestClient") final RestHighLevelClient tertiaryEsRestClient,
			final MetricAgent metricAgent, final ObjectMapper objectMapper) {
		this.indexNamePrefix = indexNamePrefix;
		this.primaryEsRestClient = primaryEsRestClient;
		this.secondaryEsRestClient = secondaryEsRestClient;
		this.tertiaryEsRestClient = tertiaryEsRestClient;
		this.metricAgent = metricAgent;
		this.objectMapper = objectMapper;
	}

	public void checkIndexAvailability() {
		try {
			// Current date in epoch
			long currentEpoch = System.currentTimeMillis();

			long es1From = currentEpoch
					- ChronoUnit.DAYS.getDuration().getSeconds() * 1000 * PRIMARY_ES_DATA_AVAILABILITY_DAYS;
			checkIndicesForRange(primaryEsRestClient, es1From, currentEpoch, EsType.PRIMARY);

			long es2From = currentEpoch
					- ChronoUnit.DAYS.getDuration().getSeconds() * 1000 * SECONDARY_ES_DATA_AVAILABILITY_DAYS;
			checkIndicesForRange(secondaryEsRestClient, es2From, currentEpoch, EsType.SECONDARY);

			long es3From = LocalDate.of(2023, 4, 1).atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000;
			long es3To = currentEpoch - ChronoUnit.DAYS.getDuration().getSeconds() * 1000
					* TERTIARY_ES_DATA_AVAILABILITY_START_TIME_DAYS_DIFF;
			checkIndicesForRange(tertiaryEsRestClient, es3From, es3To, EsType.TERTIARY);

		}
		catch (Exception e) {
			log.error("Error occurred while checking index availability: {}", CommonsUtility.exceptionFormatter(e));
		}
	}

	private void checkIndicesForRange(final RestHighLevelClient restHighLevelClient, final long fromEpoch,
			final long toEpoch, final EsType esType) {
		// Get list of expected indices
		List<String> expectedIndices = IndexUtility.getExactIndexNamesList(indexNamePrefix, fromEpoch, toEpoch);

		// Get all existing indices from ES cluster.
		Set<String> existingIndices = getAllExistingIndices(restHighLevelClient, esType);

		// If we couldn't fetch existing indices (API error), mark all as ERROR
		if (existingIndices == null) {
			for (String index : expectedIndices) {
				log.error("Error :- Exception while fetching indices from ES : {}, index : {}", esType, index);
				metricAgent.incrementCount(INDEX_AVAILABILITY_VERIFICATION_COUNT,
						RESULT + COLON + IndexStatus.ERROR.name(), INDEX + COLON + index,
						ES_CLUSTER + COLON + esType.name());
			}
			return;
		}

		// Check each expected index against the existing indices set
		for (String expectedIndex : expectedIndices) {
			IndexStatus status = existingIndices.contains(expectedIndex) ? IndexStatus.EXISTS : IndexStatus.NOT_EXISTS;

			switch (status) {
				case NOT_EXISTS:
					log.error("Error :- Index missing on ES : {}, index : {}", esType, expectedIndex);
					break;

				case EXISTS:
					log.info("Index available on ES : {}, index : {} ", esType, expectedIndex);
					break;

				default:
					log.warn("Unknown index status for ES : {}, index : {}", esType, expectedIndex);
					continue;
			}

			metricAgent.incrementCount(INDEX_AVAILABILITY_VERIFICATION_COUNT, RESULT + COLON + status.name(),
					INDEX + COLON + expectedIndex, ES_CLUSTER + COLON + esType.name());
		}
	}

	/**
	 * Get all existing indices from ES cluster using cat indices API directly. Equivalent
	 * to: curl -X GET "localhost:9200/_cat/indices?format=json&h=index"
	 * @param restHighLevelClient The ES client to use
	 * @param esType The ES cluster type for logging
	 * @return Set of existing index names, or null if API call failed
	 */
	private Set<String> getAllExistingIndices(final RestHighLevelClient restHighLevelClient, final EsType esType) {
		try {

			if (restHighLevelClient == null || restHighLevelClient.getLowLevelClient() == null) {
				log.error("ES client or low level client is null for cluster: {}", esType);
				return null;
			}

			// Use cat indices API directly - more efficient and lightweight
			Request request = new Request("GET", "/_cat/indices");
			request.addParameter("format", "json");
			// Only return index names
			request.addParameter("h", "index");

			Response response = restHighLevelClient.getLowLevelClient().performRequest(request);

			if (response == null || response.getEntity() == null) {
				log.warn("Response or response entity is null for ES cluster: {}", esType);
				return new HashSet<>(); // Empty cluster scenario
			}

			String responseBody = EntityUtils.toString(response.getEntity());

			if (responseBody == null || responseBody.trim().isEmpty() || responseBody.trim().equals("[]")) {
				// Return empty set if no indices exist
				return new HashSet<>();
			}

			// Parse JSON array of index objects
			List<Map<String, Object>> indices = objectMapper.readValue(responseBody,
					new TypeReference<List<Map<String, Object>>>() {
					});

			if (indices == null || indices.isEmpty()) {
				return new HashSet<>();
			}

			// Extract index names from the response
			return indices.stream()
				.filter(indexMap -> indexMap != null) // Filter out null maps
				.map(indexMap -> indexMap.get("index")) // Get index name
				.filter(indexName -> indexName != null) // Filter out null index names
				.map(Object::toString) // Ensure string conversion
				.filter(indexName -> !indexName.trim().isEmpty()) // Filter out empty
																	// names
				.collect(Collectors.toSet());

		}
		catch (Exception e) {
			log.error("Exception while fetching all indices from ES cluster {}: {}", esType,
					CommonsUtility.exceptionFormatter(e));
			// null indicates API error
			return null;
		}
	}

}
