package com.org.com.webapiv2.cstCategoryCode;;

import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
public class CategoryParticipant {

	@NonNull
	private long id;

	private String participantType;

	private String status;

	private String category;

	private String type;

	private String businessType; // find business category

	private String merchantSubCategory;

	private String initiator;

	private String messageType;

	private long narrationId;

	private String timeLineId;

	private String importantNoteId;

	private String createdOn;

	private String updatedOn;

	private String updatedBy;

	private String responseConstant;

	private String responseCtasMappingId;

	private String responseMappersMappingId;

	private String paymentInstrument;

	public String getKey(final String seperator, final String paymentInstrument, final String businessCategory,
			final boolean isPaymentInstrumentUse) {
		StringBuilder sb = new StringBuilder();
		if (isPaymentInstrumentUse) {
			sb.append(this.status)
				.append(seperator)
				.append(this.category)
				.append(seperator)
				.append(this.type)
				.append(seperator)
				.append(participantType)
				.append(seperator)
				.append(businessCategory)
				.append(seperator)
				.append(this.merchantSubCategory)
				.append(seperator)
				.append(initiator)
				.append(seperator)
				.append(responseConstant)
				.append(seperator)
				.append(messageType)
				.append(seperator)
				.append(paymentInstrument);
		}
		else {
			sb.append(this.status)
				.append(seperator)
				.append(this.category)
				.append(seperator)
				.append(this.type)
				.append(seperator)
				.append(participantType)
				.append(seperator)
				.append(businessCategory)
				.append(seperator)
				.append(this.merchantSubCategory)
				.append(seperator)
				.append(initiator)
				.append(seperator)
				.append(responseConstant)
				.append(seperator)
				.append(messageType);
		}
		return sb.toString();

	}

}
