package com.org.com.webapiv2.cstCategoryCode;;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

@Builder
@Getter
@ToString
public class SearchKeyDto {

	private String status;

	private String category;

	private String type;

	private String participantType;

	private String businessCategory;

	private String merchantSubCategory;

	private String initiator;

	private String responseConstant;

	private String timelineType;

	// private Enums.PAYMENT_INSTRUMENT paymentInstrument;

	private String paymentInstrument;

	public SearchKeyDto(final String status, final String category, final String type, final String participantType,
			final String businessCategory, final String merchantSubCategory, final String initiator,
			final String responseConstant, final String timelineType, final String paymentInstrument) {
		this.status = status;
		this.category = category;
		this.type = type;
		this.participantType = participantType;
		this.businessCategory = businessCategory;
		this.merchantSubCategory = merchantSubCategory;
		this.initiator = initiator;
		this.timelineType = timelineType;
		this.responseConstant = responseConstant;
		this.paymentInstrument = paymentInstrument;
	}

}
