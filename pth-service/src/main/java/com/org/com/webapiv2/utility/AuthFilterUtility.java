package com.org.com.webapiv2.utility;

import static com.org.com.webapiv2.constants.Constants.APPLICATION_JSON;
import static com.org.com.webapiv2.constants.Constants.CONTENT_TYPE;
import static com.org.com.webapiv2.constants.Constants.GET_LANGUAGES_API_NAME;
import static com.org.com.webapiv2.constants.Constants.GET_MESSAGES_API_NAME;
import static com.org.com.webapiv2.constants.Constants.IVR_TXN_LIST_URL;
import static com.org.com.webapiv2.constants.Constants.MANDATE_HISTORY_INT_API;
import static com.org.com.webapiv2.constants.Constants.UPI_CST_PANEL_API;
import static com.org.com.webapiv2.constants.api.StatementConstants.CLAIMS_HASH_KEY;
import static com.org.com.webapiv2.constants.api.StatementConstants.STATEMENT_API;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.INVALID_TOKEN_EXCEPTION_REQUIRED;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.REWIND_API_AUTH_SKIPPING_ENABLED;
import static com.org.panaroma.commons.constants.Constants.PTH;
import static com.org.panaroma.commons.constants.WebConstants.ANY_TXN_API;
import static com.org.panaroma.commons.constants.WebConstants.AUTHORIZATION;
import static com.org.panaroma.commons.constants.WebConstants.CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.CST;
import static com.org.panaroma.commons.constants.WebConstants.CST_DETAILS_API;
import static com.org.panaroma.commons.constants.WebConstants.CST_HOME;
import static com.org.panaroma.commons.constants.WebConstants.CST_PANEL_API;
import static com.org.panaroma.commons.constants.WebConstants.CST_SEARCH_API;
import static com.org.panaroma.commons.constants.WebConstants.JWT_TOKEN;
import static com.org.panaroma.commons.constants.WebConstants.LATEST_TXN_API;
import static com.org.panaroma.commons.constants.WebConstants.OPEN_SOURCE;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_INTERNAL_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PASSBOOK_API;
import static com.org.panaroma.commons.constants.WebConstants.REQUEST_ID;
import static com.org.panaroma.commons.constants.WebConstants.TXN_DETAIL_API;
import static com.org.panaroma.commons.constants.WebConstants.TXN_LIST;
import static com.org.panaroma.commons.constants.WebConstants.UPI_SWITCH_V2;
import static com.org.panaroma.commons.constants.WebConstants.USERID;
import static com.org.panaroma.commons.constants.WebConstants.USER_ID;
import static com.org.panaroma.commons.constants.WebConstants.USER_TOKEN;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INVALID_JWT_TOKEN;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.MAPPER_EXCEPTION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.org.com.webapiv2.constants.enums.ApiDetailsEnum;
import com.org.com.webapiv2.filter.wrappers.RequestWrapper;
import com.org.com.webapiv2.monitoring.MonitoringUtility;
import com.org.com.webapiv2.parentResponse.ParentResponse;
import com.org.com.webapiv2.service.impl.utility.WebUtilityServiceUnified;
import com.org.com.webapiv2.utility.configurablePropertyUtility.ConfigurablePropertiesHolderUnified;
import com.org.panaroma.commons.dto.ValidationResult;
import com.org.panaroma.commons.enums.Client;
import com.org.panaroma.commons.exceptionhandler.ExceptionBuilder;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.HashUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.web.utility.RequestFilterUtility;
import com.org.panaroma.web.utility.TokenValidatorUtility;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

@Log4j2
@Component
public class AuthFilterUtility {

	private static Map<String, Boolean> apiMapToValidateUserId;

	private static List<String> authRequiredUrl;

	private final WebUtilityServiceUnified webUtilityService;

	private final ErrorResponseUtilityUnified errorResponseUtility;

	private final String secret;

	private final String secretForPms;

	private final String ppblPassbookSecret;

	private final String uthCstSecret;

	private final String cstPthTxnListApiSecret;

	private final String secretForTimeline;

	private final String pthConsumerServiceSecret;

	private final String pthTpapPanelSecret;

	private final String mandateHistoryCstSecret;

	private final String pthUpiSwitchSecretKey;

	private ObjectMapper objectMapper = new ObjectMapper();

	private ConfigurablePropertiesHolderUnified configurablePropertiesHolderUnified;

	private Gson gson = new Gson();

	static {
		apiMapToValidateUserId = new HashMap<>();
		for (ApiDetailsEnum apiDetailsEnum : ApiDetailsEnum.values()) {
			apiMapToValidateUserId.put(apiDetailsEnum.getApiUrl(), apiDetailsEnum.getIsAuthValidationRequired());
		}

		authRequiredUrl = new ArrayList<>();
		authRequiredUrl.add(IVR_TXN_LIST_URL);
	}

	@Autowired
	public AuthFilterUtility(final WebUtilityServiceUnified webUtilityService,
			final ErrorResponseUtilityUnified errorResponseUtility,
			@Value("${uth.upi.txn.history.secret.key}") final String secret,
			@Value("${pth.upi.switch.secret.key}") final String pthUpiSwitchSecretKey,
			@Value("${uth.pms.txn.history.secret.key}") final String secretForPms,
			@Value("${uth.ppbl.txn.history.secret.key}") final String ppblPassbookSecret,
			@Value("${cst.pth.txnList.api.secret}") final String uthCstSecret,
			@Value("${upi.timeline.secret.key}") final String secretForTimeline,
			@Value("${pth-pth.consumer.service-secret}") final String pthConsumerServiceSecret,
			@Value("${cst.pth.txnList.api.secret}") final String cstPthTxnListApiSecret,
			@Value("${cst.pth.cstPanel.api.secret}") final String pthTpapPanelSecret,
			@Value("${mandate.history.cst.secret}") final String mandateHistoryCstSecret,
			final ConfigurablePropertiesHolderUnified configurablePropertiesHolderUnified) {
		this.webUtilityService = webUtilityService;
		this.errorResponseUtility = errorResponseUtility;
		this.secret = secret;
		this.secretForPms = secretForPms;
		this.ppblPassbookSecret = ppblPassbookSecret;
		this.uthCstSecret = uthCstSecret;
		this.secretForTimeline = secretForTimeline;
		this.pthConsumerServiceSecret = pthConsumerServiceSecret;
		this.cstPthTxnListApiSecret = cstPthTxnListApiSecret;
		this.pthTpapPanelSecret = pthTpapPanelSecret;
		this.mandateHistoryCstSecret = mandateHistoryCstSecret;
		this.configurablePropertiesHolderUnified = configurablePropertiesHolderUnified;
		this.pthUpiSwitchSecretKey = pthUpiSwitchSecretKey;
	}

	public static Boolean isUserTokenValidationEnabled(final String apiName) {
		return Boolean.TRUE.equals(apiMapToValidateUserId.get(apiName));
	}

	public static Boolean isAuthValidationRequired(final String apiUri) {
		String apiUrl = apiUri.substring(StringUtils.ordinalIndexOf(apiUri, "/", 2) + 1);
		return authRequiredUrl.contains(apiUrl);
	}

	public void setUserIdAttributeIfRequired(final HttpServletRequest request, final HttpServletResponse response)
			throws IOException {
		String apiUri = request.getRequestURI();
		String apiUrl = apiUri.substring(StringUtils.ordinalIndexOf(apiUri, "/", 2) + 1);
		String headerToken = request.getHeader(AUTHORIZATION);

		if (this.isAuthTokenValidationRequiredForApi(apiUrl)) {
			try {
				String client = request.getHeader(CLIENT);
				String userIdFromRequest = request.getHeader("userid");
				String userId = null;

				if (StringUtils.equalsIgnoreCase(client, Client.uthv2testing.name())
						&& ApiDetailsEnum.REWIND_DATA.getApiUrl().equalsIgnoreCase(apiUrl)
						&& configurablePropertiesHolderUnified.getProperty(REWIND_API_AUTH_SKIPPING_ENABLED,
								Boolean.class)) {
					userId = TokenValidatorUtility.getTokenFromMap(headerToken, USER_TOKEN);
				}
				else {
					Boolean isUnAuthErrorReq = checkIfUnAuthErrorRequired(request);
					ValidationResult oauthResponse = StringUtils.isNotBlank(userIdFromRequest)
							? webUtilityService.validateAndGetUserId(headerToken, ThreadContext.get("requestId"), "",
									userIdFromRequest, isUnAuthErrorReq)
							: webUtilityService.validateAndGetUserId(headerToken, ThreadContext.get("requestId"), "",
									isUnAuthErrorReq);
					userId = oauthResponse.getUserId();
				}
				request.setAttribute(USER_ID, userId);
				MdcUtility.populateConstant(USER_ID, userId);

			}
			catch (Exception e) {
				log.error("Exception while fetching user token, Exception : {}", CommonsUtility.exceptionFormatter(e));
				ParentResponse parentResponse = errorResponseUtility.handleError((Throwable) e, apiUrl,
						request.getHeader(CLIENT));

				// Setting custom response in HttpServletResponse
				errorResponseUtility.setCustomResponse(response, parentResponse);
			}
		}
	}

	public boolean isAuthTokenValidationRequiredForApi(final String apiName) {
		return AuthFilterUtility.isUserTokenValidationEnabled(apiName);
	}

	public void validateJwtToken(final Map<String, Object> requestBody, final RequestWrapper requestWrapper) {
		String jwtToken = TokenValidatorUtilityUnified.getTokenFromMap(requestWrapper.getHeader(AUTHORIZATION),
				JWT_TOKEN);
		try {
			String api = MonitoringUtility.getApiIdentifier(requestWrapper);
			String client = this.getClient(api, requestWrapper);
			Map<String, String> claims = this.setJwtClaims(requestBody, api, client);
			String finalSecretValue = this.getSecretValue(api, client);
			boolean isValidated = TokenValidatorUtilityUnified.verifyJwtToken(client, claims, jwtToken,
					finalSecretValue);

			if (!isValidated) {
				log.info("Invalid Jwt token for request ID:{}", ThreadContext.get(REQUEST_ID));
				throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_JWT_TOKEN);
			}
		}
		catch (Exception e) {
			log.error("Exception : {} while validating jwt token for request ID:{}",
					CommonsUtility.exceptionFormatter(e), ThreadContext.get(REQUEST_ID));
			throw e;
		}
	}

	private String getSecretValue(final String api, final String client) {
		String finalSecretValue = "";
		switch (api) {
			case PASSBOOK_API:
			case TXN_DETAIL_API:
				finalSecretValue = this.ppblPassbookSecret;
				break;
			case CST_DETAILS_API:
				finalSecretValue = this.uthCstSecret;
				break;
			case LATEST_TXN_API:
				finalSecretValue = this.secretForPms;
				break;
			case ANY_TXN_API:
			case GET_MESSAGES_API_NAME:
			case GET_LANGUAGES_API_NAME:
				finalSecretValue = this.secretForTimeline;
				break;
			case STATEMENT_API:
				finalSecretValue = this.pthConsumerServiceSecret;
				break;
			case CST_PANEL_API:
				if (UPI_SWITCH_V2.equals(client)) {
					finalSecretValue = this.pthUpiSwitchSecretKey;
				}
				else {
					finalSecretValue = this.pthTpapPanelSecret;
				}
				break;
			case TXN_LIST:
			case CST_SEARCH_API:
				// if client == CST then use this cstPthTxnListApiSecret secret,
				// otherwise for other client like UPI let the default handling run
				if (CST.equals(client)) {
					finalSecretValue = this.cstPthTxnListApiSecret;
					break;
				}
			case MANDATE_HISTORY_INT_API:
				finalSecretValue = mandateHistoryCstSecret;
				break;
			case "history":
				if (client.equals(CST)) {
					finalSecretValue = uthCstSecret;
					break;
				}
			default:
				finalSecretValue = this.secret;
		}
		return finalSecretValue;
	}

	private String getClient(final String api, final RequestWrapper requestWrapper) {
		if (CST_DETAILS_API.equals(api)) {
			return requestWrapper.getHeader(OPEN_SOURCE);
		}
		if (STATEMENT_API.equals(api)) {
			return PTH;
		}
		String client = requestWrapper.getHeader(CLIENT);
		if (client == null) {
			client = requestWrapper.getParameter(CLIENT);
		}
		return client;
	}

	public String getClient(final HttpServletRequest request) {
		String api = MonitoringUtility.getApiIdentifier(request);
		if (CST_DETAILS_API.equals(api)) {
			return request.getHeader(OPEN_SOURCE);
		}
		String client = request.getHeader(CLIENT);
		if (client == null) {
			client = request.getParameter(CLIENT);
		}
		return client;
	}

	@Nullable public Map<String, Object> getRequestBody(final RequestWrapper requestWrapper) throws IOException {
		Map<String, Object> jsonRequest = null;
		if (!CST_HOME.equals(requestWrapper.getHeader(OPEN_SOURCE))) {
			byte[] body = StreamUtils.copyToByteArray(requestWrapper.getInputStream());
			jsonRequest = this.objectMapper.readValue(body, Map.class);
		}
		return jsonRequest;
	}

	public void validateToken(final Map<String, String> paramMap, final RequestWrapper requestWrapper) {
		String api = MonitoringUtility.getApiIdentifier(requestWrapper);
		String jwtToken = null;
		if (GET_LANGUAGES_API_NAME.equalsIgnoreCase(api) || GET_MESSAGES_API_NAME.equalsIgnoreCase(api)) {
			jwtToken = requestWrapper.getHeader(AUTHORIZATION);
		}
		else {
			jwtToken = TokenValidatorUtilityUnified.getTokenFromMap(requestWrapper.getHeader(AUTHORIZATION), JWT_TOKEN);
		}
		if (Objects.isNull(jwtToken)) {
			throw ExceptionBuilder.getExceptionForInternalServiceRequest(PANAROMA_INTERNAL_SERVICE, MAPPER_EXCEPTION);
		}
		try {
			String secret = getSecretValue(api, null);
			boolean isValidated = TokenValidatorUtilityUnified.verifyJwtToken(paramMap.get(CLIENT), new HashMap<>(),
					jwtToken, secret);
			if (!isValidated) {
				log.info("Invalid Jwt token for request ID:{}", ThreadContext.get(REQUEST_ID));
				throw ExceptionBuilder.getExceptionForInternalServiceRequest(PANAROMA_INTERNAL_SERVICE,
						INVALID_JWT_TOKEN);
			}
		}
		catch (Exception e) {
			log.error("Exception : {} while validating jwt token for request ID:{}",
					CommonsUtility.exceptionFormatter(e), ThreadContext.get(REQUEST_ID));
			throw e;
		}
	}

	private Map<String, String> setJwtClaims(final Map<String, Object> requestBody, final String api,
			final String passedClient) {
		switch (api) {
			case CST_DETAILS_API:
			case PASSBOOK_API:
			case TXN_DETAIL_API:
				Map<String, String> ppblPassbookClaims = new HashMap<>();
				return ppblPassbookClaims;
			case STATEMENT_API:
			case CST_SEARCH_API:
				Map<String, String> apiClaimsMap = new HashMap<>();
				try {
					String requestBodyAsString = gson.toJson(requestBody);
					apiClaimsMap.put(CLAIMS_HASH_KEY, HashUtility.generateSha256Hash(requestBodyAsString));
				}
				catch (NoSuchAlgorithmException e) {
					log.error("Error while converting claims to string {}", e);
				}
				return apiClaimsMap;
			case MANDATE_HISTORY_INT_API:
				Map<String, String> mandateHistoryIntMap = new HashMap<>();
				try {
					String requestBodyAsString = gson.toJson(requestBody);
					mandateHistoryIntMap.put(CLAIMS_HASH_KEY, HashUtility.generateSha256Hash(requestBodyAsString));
				}
				catch (NoSuchAlgorithmException e) {
					log.error("Error while converting claims to string {}", e);
				}
				return mandateHistoryIntMap;
			case TXN_LIST:
				// if passedClient == CST then use whole request body's hash as
				// PayloadHash,
				// otherwise for other passedClient like UPI let the default handling run
				if (CST.equals(passedClient)) {
					Map<String, String> txnListApiCstClaimsMap = new HashMap<>();
					try {
						String requestBodyAsString = gson.toJson(requestBody);
						txnListApiCstClaimsMap.put(CLAIMS_HASH_KEY,
								HashUtility.generateSha256Hash(requestBodyAsString));
					}
					catch (NoSuchAlgorithmException e) {
						log.error("Error while converting claims to string {}", e);
					}
					return txnListApiCstClaimsMap;
				}
			case "history":
				if (CST.equals(passedClient)) {
					Map<String, String> txnListApiCstClaimsMap = new HashMap<>();
					try {
						String requestBodyAsString = gson.toJson(requestBody);
						txnListApiCstClaimsMap.put(CLAIMS_HASH_KEY,
								HashUtility.generateSha256Hash(requestBodyAsString));
					}
					catch (NoSuchAlgorithmException e) {
						log.error("Error while converting claims to string {}", e);
					}
					return txnListApiCstClaimsMap;
				}
			default:
				if (!StringUtils.equals(api, UPI_CST_PANEL_API) && Objects.isNull(requestBody.get(USERID))) {
					log.error("UserId is null for Uth request ID:{} and Upi request ID:{}",
							ThreadContext.get(REQUEST_ID), requestBody.get("requestId"));
					throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				}

				// If API is CST Panel and Client is UPI_SWITCH then userId would be
				// mandatory in request body
				if (StringUtils.equals(api, UPI_CST_PANEL_API) && StringUtils.equals(passedClient, UPI_SWITCH_V2)
						&& Objects.isNull(requestBody.get(USERID))) {
					log.error("UserId is null for Uth request ID:{} and Upi request ID:{}, client: {}, requestBody: {}",
							ThreadContext.get(REQUEST_ID), requestBody.get("requestId"), passedClient, requestBody);
					throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				}

				if (Objects.isNull(requestBody.get(CLIENT))) {
					log.error("Client is null for request ID:{}", ThreadContext.get(REQUEST_ID));
					throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				}

				if (Objects.isNull(requestBody.get(REQUEST_ID))) {
					log.error("Response body's requestId is null for request ID:{}", ThreadContext.get(REQUEST_ID));
					throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				}

				Map<String, String> claims = new HashMap<>();
				String requestId = (String) (requestBody.get(REQUEST_ID));
				String client = (String) (requestBody.get(CLIENT));
				String userId = null;

				if (Objects.nonNull(requestBody.get(USERID))) {
					userId = (String) (requestBody.get(USERID));
				}
				claims.put(REQUEST_ID, requestId);
				claims.put(CLIENT, client);

				if (Objects.nonNull(userId)) {
					claims.put(USERID, userId);
				}

				return claims;
		}
	}

	public Boolean checkIfUnAuthErrorRequired(final HttpServletRequest request) {
		if (!configurablePropertiesHolderUnified.getProperty(INVALID_TOKEN_EXCEPTION_REQUIRED, Boolean.class)) {
			return false;
		}

		return RequestFilterUtility.checkIfUnAuthErrorRequired(request);
	}

}