package com.org.com.webapiv2.dto.metaData.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.com.webapiv2.dto.metaData.MetaDataResponse;
import lombok.Data;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetaDataResponseV4 extends MetaDataResponse {

}
