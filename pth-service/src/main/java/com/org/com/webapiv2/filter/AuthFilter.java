package com.org.com.webapiv2.filter;

import static com.org.com.webapiv2.constants.MonitoringConstants.API_NAME;
import static com.org.com.webapiv2.constants.MonitoringConstants.AUTH_TYPE;
import static com.org.com.webapiv2.constants.api.StatementConstants.PTH_CONSUMER_SERVICE_CLIENT;
import static com.org.com.webapiv2.constants.enums.ApiDetailsEnum.META_DATA_API;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.BLACKLISTED_APIS_FOR_FILTERS;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.JWT_VERFICATION_SUPPORTED_CLIENTS;
import static com.org.panaroma.commons.constants.Constants.ENTITY_ID;
import static com.org.panaroma.commons.constants.Constants.UTH_DC_CLIENT_FOR_INTERNAL_LOCALIZATION_APIS;
import static com.org.panaroma.commons.constants.WebConstants.CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.CST;
import static com.org.panaroma.commons.constants.WebConstants.CST_HOME;
import static com.org.panaroma.commons.constants.WebConstants.NON_PAYLOAD_HASH_JWT_AUTHORIZATION_CLIENTS;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PMS;
import static com.org.panaroma.commons.constants.WebConstants.PPBL;
import static com.org.panaroma.commons.constants.WebConstants.REQUEST_ID;
import static com.org.panaroma.commons.constants.WebConstants.UPI;
import static com.org.panaroma.commons.constants.WebConstants.UPI_SWITCH;
import static com.org.panaroma.commons.constants.WebConstants.UPI_SWITCH_V2;
import static com.org.panaroma.commons.constants.WebConstants.USER_ID;
import static com.org.panaroma.commons.constants.WebConstants.TPAP_PANEL;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.MAPPER_EXCEPTION;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AUTH_TYPE_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.com.webapiv2.filter.wrappers.RequestWrapper;
import com.org.com.webapiv2.parentResponse.ParentResponse;
import com.org.com.webapiv2.service.impl.utility.WebUtilityServiceUnified;
import com.org.com.webapiv2.utility.AuthFilterUtility;
import com.org.com.webapiv2.utility.ErrorResponseUtilityUnified;
import com.org.panaroma.commons.enums.Client;
import com.org.panaroma.commons.exceptionhandler.ExceptionBuilder;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.org.panaroma.web.monitoring.MetricsAgent;
import java.util.Set;

import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
@Order(15)
@Log4j2
public class AuthFilter extends OncePerRequestFilter {

	private final AuthFilterUtility authFilterUtility;

	private final ErrorResponseUtilityUnified errorResponseUtility;

	private final MetricsAgent metricsAgent;

	private ObjectMapper objectMapper = new ObjectMapper();

	private final Boolean isValidationRequiredForMetaData;

	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	private List<String> jwtVerificationClients = Arrays.asList(UPI, PPBL, CST_HOME, PTH_CONSUMER_SERVICE_CLIENT, CST,
			TPAP_PANEL, UPI_SWITCH_V2);

	@Autowired
	public AuthFilter(final AuthFilterUtility authFilterUtility,
			@Value("${auth-validation-required-for-meta-data}") final Boolean isValidationRequiredForMetaData,
			final ErrorResponseUtilityUnified errorResponseUtility, final MetricsAgent metricsAgent,
			final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.authFilterUtility = authFilterUtility;
		this.errorResponseUtility = errorResponseUtility;
		this.isValidationRequiredForMetaData = isValidationRequiredForMetaData;
		this.metricsAgent = metricsAgent;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	@Override
	protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
		try {
			Set<String> blackListedApisForRateLimit = configurablePropertiesHolder
				.getPropertyWithDefaultValue(BLACKLISTED_APIS_FOR_FILTERS, Set.class, Collections.emptySet());

			String api = MdcUtility.getConstantValue(API_NAME);
			if (blackListedApisForRateLimit != null && blackListedApisForRateLimit.contains(api)) {
				return true;
			}
		}
		catch (Exception ex) {
			log.warn("Exception while checking if filter should be applied or not, Exception : {}",
					CommonsUtility.exceptionFormatter(ex));
		}
		return false;
	}

	@Override
	protected void doFilterInternal(final HttpServletRequest request, final HttpServletResponse response,
			final FilterChain filterChain) throws IOException, ServletException {
		String apiUri = request.getRequestURI();
		String apiUrl = apiUri.substring(StringUtils.ordinalIndexOf(apiUri, "/", 2) + 1);
		String client = authFilterUtility.getClient(request);
		if (apiUri.equals("/pth/int/v1/mandate-backFill")) {
			filterChain.doFilter(request, response);
			return;
		}
		if (!UtilityExtension.isCurrentFilterNotRequired(apiUri)) {
			filterChain.doFilter(request, response);
			return;
		}
		else {

			// fetching JWT verification clients from configurable properties
			List<String> jwtVerificationClients = configurablePropertiesHolder
				.getProperty(JWT_VERFICATION_SUPPORTED_CLIENTS, List.class);

			if (AuthFilterUtility.isAuthValidationRequired(request.getRequestURI())
					|| (Objects.nonNull(client) && (StringUtils.equals(client, Client.androidapp.name())
							|| StringUtils.equals(client, Client.iosapp.name())
							|| StringUtils.equalsIgnoreCase(client, Client.uthv2testing.name())))) {
				metricsAgent.incrementCount(AUTH_TYPE_COUNT, CLIENT + COLON + client, API_NAME + COLON + apiUri,
						AUTH_TYPE + COLON + "AuthToken");
				if (apiUrl.equals(META_DATA_API.getApiUrl()) && !isValidationRequiredForMetaData) {
					filterChain.doFilter(request, response);
				}
				else {
					authFilterUtility.setUserIdAttributeIfRequired(request, response);

					if (authFilterUtility.isAuthTokenValidationRequiredForApi(apiUrl)
							&& Objects.isNull(request.getAttribute(USER_ID))) {
						return;
					}
					filterChain.doFilter(request, response);
				}
			}
			else if (Objects.nonNull(client) && jwtVerificationClients.contains(client)) {
				metricsAgent.incrementCount(AUTH_TYPE_COUNT, CLIENT + COLON + client, API_NAME + COLON + apiUri,
						AUTH_TYPE + COLON + "JWTToken");
				this.validateJwtToken(request, response, filterChain);
			}
			else if (Objects.nonNull(client)
					&& NON_PAYLOAD_HASH_JWT_AUTHORIZATION_CLIENTS.contains(request.getHeader(CLIENT))) {
				metricsAgent.incrementCount(AUTH_TYPE_COUNT, CLIENT + COLON + client, API_NAME + COLON + apiUri,
						AUTH_TYPE + COLON + "JWTIssuerToken");
				this.validateJwtTokenIssuer(request, response, filterChain);
			}
			else {
				Exception exp = ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				this.hanldeException(request, response, exp);
			}
		}
	}

	private void validateJwtToken(final HttpServletRequest request, final HttpServletResponse response,
			final FilterChain filterChain) throws IOException {
		RequestWrapper requestWrapper = new RequestWrapper((HttpServletRequest) request);

		try {
			Map<String, Object> jsonRequest = authFilterUtility.getRequestBody(requestWrapper);
			authFilterUtility.validateJwtToken(jsonRequest, requestWrapper);
			filterChain.doFilter(requestWrapper, response);

		}
		catch (JsonParseException e) {
			Exception exp = ExceptionBuilder.getException(PANAROMA_SERVICE, MAPPER_EXCEPTION);
			this.hanldeException(request, response, exp);
		}
		catch (Exception e) {
			this.hanldeException(request, response, e);
		}
	}

	private void hanldeException(final HttpServletRequest request, final HttpServletResponse response,
			final Exception e) throws IOException {
		log.warn("Exception while validating jwt token for RequestID : {} , Exception : {}",
				ThreadContext.get(REQUEST_ID), CommonsUtility.exceptionFormatter(e));
		String apiUri = request.getRequestURI();
		String apiUrl = WebUtilityServiceUnified.getUrlFromUri(apiUri);
		ParentResponse parentResponse = errorResponseUtility.handleError((Throwable) e, apiUrl,
				authFilterUtility.getClient(request));
		switch (authFilterUtility.getClient(request)) {
			case PPBL:
				response.setStatus(parentResponse.getHttpCode());
				break;
			default:
				// do nothing
		}

		// Setting custom response in HttpServletResponse
		errorResponseUtility.setCustomResponse(response, parentResponse);
	}

	private void validateJwtTokenIssuer(final HttpServletRequest request, final HttpServletResponse response,
			final FilterChain filterChain) throws IOException {

		RequestWrapper requestWrapper = new RequestWrapper((HttpServletRequest) request);
		try {
			// putting header value in paramMap
			Map<String, String> paramMap = new HashMap<>();
			paramMap.put(CLIENT, request.getHeader(CLIENT));
			paramMap.put(ENTITY_ID, request.getHeader(ENTITY_ID));
			authFilterUtility.validateToken(paramMap, requestWrapper);
			filterChain.doFilter(requestWrapper, response);

		}
		catch (Exception e) {
			log.error("Exception while validating jwt token, Exception : {}", CommonsUtility.exceptionFormatter(e));
			String apiUri = request.getRequestURI();
			String apiUrl = apiUri.substring(StringUtils.ordinalIndexOf(apiUri, "/", 2) + 1);
			ParentResponse parentResponse = errorResponseUtility.handleError((Throwable) e, apiUrl,
					request.getHeader(CLIENT));
			switch (request.getHeader(CLIENT)) {
				case PMS:
				case UPI_SWITCH:
				case UTH_DC_CLIENT_FOR_INTERNAL_LOCALIZATION_APIS:
					response.setStatus(parentResponse.getHttpCode());
					break;
				default:
					// do nothing
			}

			// Setting custom response in HttpServletResponse
			errorResponseUtility.setCustomResponse(response, parentResponse);
		}
	}

}
