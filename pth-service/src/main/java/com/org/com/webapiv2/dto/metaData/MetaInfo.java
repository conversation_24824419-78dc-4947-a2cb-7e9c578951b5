package com.org.com.webapiv2.dto.metaData;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetaInfo {

	@JsonIgnore
	private Long cacheTtl;

	@JsonIgnore
	private String fromDate;

	@JsonIgnore
	private String fromDateFormat;

	private Long fromDateInEpochMillis;

	@JsonIgnore
	private Integer currentMonth;

	@JsonIgnore
	private Integer currentYear;

	@JsonProperty("dateRangeFilter_fromDate_millis")
	private Long dateRangeFilterFromDateMillis;

	private Long fromDateForUserTagsSummaryInMillis;

	private Map<String, String> txnTypeToAccountLabelMap;

}
