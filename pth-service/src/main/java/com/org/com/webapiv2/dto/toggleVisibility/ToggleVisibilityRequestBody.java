package com.org.com.webapiv2.dto.toggleVisibility;

import static com.org.com.webapiv2.constants.MonitoringConstants.COLON;
import static com.org.panaroma.commons.constants.CommonConstants.PAGINATION_STREAM_SOURCE;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.enums.ToggleVisibilityAction;
import com.org.panaroma.commons.utils.JsonUtils;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ToggleVisibilityRequestBody {

	@JsonProperty("action")
	@NotNull
	private ToggleVisibilityAction action;

	@JsonProperty("streamSource")
	@NotBlank
	private String streamSource;

	@JsonProperty("txnDate")
	@NotBlank
	private String txnDate;

	@JsonProperty("txnId")
	@NotBlank
	private String txnId;

	public String getTxnId() {
		return StringUtils.substringBefore(txnId, COLON);
	}

	public String getStreamSource() {
		// As in search v4 we started sending actual streamSource value (i.e :- UPI) in
		// pagination instead of key.
		// So converting actual value back to key before proceeding further.
		TransactionSource transactionSource = TransactionSource.getTransactionSourceEnumByName(streamSource);
		if (Objects.nonNull(transactionSource)) {
			streamSource = transactionSource.getTransactionSourceKey().toString();
		}
		return streamSource;
	}

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
