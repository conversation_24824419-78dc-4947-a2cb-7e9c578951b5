package com.org.com.webapiv2.dao.impl;

import static com.org.com.webapiv2.constants.Constants.ZERO;
import static com.org.panaroma.commons.config.DateRange.DATE_FORMAT;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.TAGGING_USE_SECONDARY_ES_FOR_USER_TAGS;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.USE_SECONDARY_ES_FOR_TAGS_USER_SUMMARY;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.USE_SECONDARY_ES_FOR_TAG_SUMMARY;
import static com.org.panaroma.commons.constants.Constants.FUTURE_LOG_REMOVER_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.ENTITY_ID;
import static com.org.panaroma.commons.constants.WebConstants.GET_DETAILS_FUNC;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_INTERNAL_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.SECOND_ES_FROM_DATE;
import static com.org.panaroma.commons.constants.WebConstants.STREAM_SOURCE;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_ID;
import static com.org.panaroma.commons.constants.WebConstants.TXN_DATE;
import static com.org.panaroma.commons.constants.WebConstants.yyyy_MM_dd_HH_mm_ss;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.ES_EXCEPTION;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.NO_TRANSACTION_FOUND_FOR_LISTING;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.TAGS_ES_EXCEPTION;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.TAGS_INTERNAL_SERVER_ERROR;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.TOGGLE_VISIBILITY_ES_EXCEPTION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.com.webapiv2.dao.IEsDao;
import com.org.com.webapiv2.dto.tags.TagSummaryResponse;
import com.org.com.webapiv2.dto.toggleVisibility.ToggleVisibilityRequest;
import com.org.com.webapiv2.dto.tags.UserTagsSummaryResponse;
import com.org.com.webapiv2.dto.upiSearch.EsRepoResponseDto;
import com.org.com.webapiv2.model.UserTagData;
import com.org.com.webapiv2.monitoring.MetricAgent;
import com.org.com.webapiv2.repository.IEsRepo;
import com.org.com.webapiv2.utility.EsUtil;
import com.org.com.webapiv2.utility.ExceptionMsgBuilder;
import com.org.com.webapiv2.utility.ExceptionMsgBuilder;
import com.org.com.webapiv2.utility.LogExectutionTime;
import com.org.com.webapiv2.utility.tag.TagHelper;
import com.org.panaroma.commons.config.DateRange;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.bankPassbook.DetailRequest;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.exceptionhandler.ExceptionBuilder;
import com.org.panaroma.commons.exceptionhandler.ExceptionHandlerUtil;
import com.org.panaroma.commons.exceptionhandler.PanaromaException;
import com.org.panaroma.commons.repository.es.EsUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.IndexUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.web.utility.ResultsMapperExtended;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

@Log4j2
@Repository
public class EsDaoImpl implements IEsDao {

	private final String indexAliasName;

	private final String indexNamePrefix;

	private final List<String> updatedIndexList;

	private final IEsRepo esRepo;

	private final ResultsMapperExtended resultMapper;

	private ObjectMapper objectMapper;

	private final MetricAgent metricAgent;

	private Integer esSocketTimeout;

	private final ExceptionMsgBuilder exceptionMsgBuilder;

	private final IEsRepo esV2Repo;

	private final IEsRepo tertiaryEsRepo;

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public EsDaoImpl(@Value("${elastic-search-index}") final String indexAliasName,
			@Value("${index.name.prefix}") final String indexNamePrefix,
			@Value("#{'${updated.index.month.list}'.split(',')}") final List<String> updatedIndexList,
			@Value("${es-socket-timeout}") final Integer esSocketTimeout, final IEsRepo esRepo,
			final ResultsMapperExtended resultMapper, final MetricAgent metricAgent, final ObjectMapper objectMapper,
			final ExceptionMsgBuilder exceptionMsgBuilder, @Qualifier("esV2Repo") final IEsRepo esV2Repo,
			final ConfigurablePropertiesHolder configurablePropertiesHolder,
			@Qualifier("tertiaryEsRepo") final IEsRepo tertiaryEsRepo) {
		this.indexAliasName = indexAliasName;
		this.indexNamePrefix = indexNamePrefix;
		this.updatedIndexList = updatedIndexList;
		this.esRepo = esRepo;
		this.resultMapper = resultMapper;
		this.metricAgent = metricAgent;
		this.esSocketTimeout = esSocketTimeout;
		this.objectMapper = objectMapper;
		this.esV2Repo = esV2Repo;
		this.tertiaryEsRepo = tertiaryEsRepo;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.exceptionMsgBuilder = exceptionMsgBuilder;
	}

	@Override
	@LogExectutionTime
	public TransformedTransactionHistoryDetail getTxnFromEs(final SearchContext searchContext, final String txnDate) {
		String docId = EsUtil.getDocIdForPaymentTxnHistoryEsIndex(searchContext);
		return getDataUsingDocId(docId, searchContext.getEntityId(), txnDate);
	}

	@Override
	public TransformedTransactionHistoryDetail getDataUsingDocId(final String docId, final String entityId,
			final String txnDate) {
		if (StringUtils.isBlank(docId) || StringUtils.isBlank(entityId) || StringUtils.isBlank(txnDate)) {
			log.error("Invalid input parameters for get Data Using DocId, docId : {}, entityId : {}, txnDate : {}",
					docId, entityId, txnDate);
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, INVALID_PARAMETER);

		}
		try {
			String indexName = Utility.getUpdatedIndexName(this.indexNamePrefix, txnDate, this.updatedIndexList);

			GetRequest getRequest = new GetRequest().id(docId).routing(entityId).index(indexName);

			GetResponse response;
			try {
				response = esRepo.esGetResponse(getRequest);
			}
			catch (Exception e) {
				log.error("Exception while fetching record from ES for DocId : {} ,with exception : {}", docId,
						CommonsUtility.exceptionFormatter((Exception) e));
				throw ExceptionBuilder.getException(PANAROMA_SERVICE, ES_EXCEPTION);
			}

			if (!response.isExists()) {
				log.error("No Txn found in ES with docId : {}", docId);
				return null;
			}

			String sourceAsString = response.getSourceAsString();
			TransformedTransactionHistoryDetail storedObj = null;

			if (sourceAsString != null) {
				storedObj = objectMapper.readValue(sourceAsString, TransformedTransactionHistoryDetail.class);
				log.debug("Found Data from Es, docId : {}", docId);
			}

			return storedObj;
		}
		catch (Exception e) {
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, ES_EXCEPTION, "");
		}
	}

	// This method is for getting user tags from ES
	@Override
	public List<UserTagData> getUserTagData(final String userId, final SearchContext searchContext) {
		SearchSourceBuilder searchSourceBuilder = EsUtil.getSourceBuilderForUserTags(searchContext);

		// Getting indices between fromDate to toDate
		List<String> indices = IndexUtility.getIndexListInRange(indexNamePrefix, searchContext.getFromDate(),
				searchContext.getToDate());

		SearchRequest searchRequest = new SearchRequest(indices.toArray(new String[0]));
		searchRequest.source(searchSourceBuilder);
		searchRequest.routing(searchContext.getEntityId());

		log.info(FUTURE_LOG_REMOVER_IDENTIFIER + "search request for user tags searchRequest: {}", searchRequest);

		// Check if secondary ES should be used for user tags
		boolean useSecondaryEsForUserTags = configurablePropertiesHolder
			.getPropertyWithDefaultValue(TAGGING_USE_SECONDARY_ES_FOR_USER_TAGS, Boolean.class, true);

		SearchResponse searchResponse;
		try {
			// Fetch the search response from the appropriate ES repository
			if (useSecondaryEsForUserTags) {
				searchResponse = esV2Repo.getSearchResponse(searchRequest);
			}
			else {
				searchResponse = esRepo.getSearchResponse(searchRequest);
			}
		}
		catch (Exception ex) {
			log.error("Exception while fetching record from ES for UserId : {}, with exception : {}",
					searchContext.getEntityId(), CommonsUtility.exceptionFormatter(ex));
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, ES_EXCEPTION);
		}

		// Getting the hits from the search response
		List<SearchHit> hits = Arrays.asList(searchResponse.getHits().getHits());
		if (hits.isEmpty()) {
			log.info("No user tags found for this users in ES for userId : {}", searchContext.getEntityId());
			return null;
		}

		// Getting the txns from the hits
		List<TransformedTransactionHistoryDetail> tagTxnsDetailsList = getHistoryDetailsList(hits);
		log.info(FUTURE_LOG_REMOVER_IDENTIFIER + "txns for user tags: {}", tagTxnsDetailsList);

		// Creating the user tag data from the txns
		return TagHelper.getUserTagDataFromTxns(tagTxnsDetailsList);
	}

	@Override
	public TransformedTransactionHistoryDetail getStoredDataFromEs(final SearchContext searchContext,
			final String txnDate) {
		try {
			SearchSourceBuilder searchSourceBuilder = EsUtility.createSearchSourceBuilder(searchContext, null, false);
			String indexName = Utility.getUpdatedIndexName(this.indexNamePrefix, txnDate, this.updatedIndexList);

			SearchRequest searchRequest = new SearchRequest(indexName);
			searchRequest.source(searchSourceBuilder);
			searchRequest.routing(searchContext.getEntityId());

			SearchResponse searchResponse;
			// long apiStartTime = Instant.now().toEpochMilli();
			try {
				searchResponse = esRepo.getSearchResponse(searchRequest);
			}
			catch (Exception e) {
				log.error("Exception while fetching record from ES for UserId : {} ,with exception : {}",
						searchContext.getEntityId(), CommonsUtility.exceptionFormatter((Exception) e));
				throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_ES_EXCEPTION);
			}
			// long timeTaken = Instant.now().toEpochMilli() - apiStartTime;
			// String tag = MonitoringConstants.ES_REST_CLIENT_TAGS;
			// metricAgent.recordApiExecutionTime(tag, timeTaken);

			List<SearchHit> hits = Arrays.asList(searchResponse.getHits().getHits());
			if (hits.size() > 1) {
				throw ExceptionBuilder.getException(PANAROMA_SERVICE, INTERNAL_SERVER_ERROR);
			}
			if (hits.isEmpty()) {
				log.error("No Txn found in ES for UserId : {} and txnId : {}", searchContext.getEntityId(),
						searchContext.getTxnId());
				throw ExceptionBuilder.getException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING);
			}
			TransformedTransactionHistoryDetail detail = resultMapper.mapSearchHit(hits.get(0),
					TransformedTransactionHistoryDetail.class);

			if (!Objects.isNull(detail)) {
				return detail;
			}
			else {
				log.error("Error while mapping");
			}
			return null;
		}
		catch (Exception e) {
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, TAGS_INTERNAL_SERVER_ERROR,
					"");
		}
	}

	@Override
	@LogExectutionTime
	public EsRepoResponseDto getResponseTxn(final SearchContext searchContext, final PaginationParams params)
			throws ParseException {
		// If request is for statement API, we call separate method i.e.
		// getResponseForStatement
		if (Objects.nonNull(searchContext.getStatementFilter())) {
			return getResponseForStatement(searchContext, params);
		}
		SearchSourceBuilder sourceBuilder = EsUtility.createSearchSourceBuilder(searchContext, params, false);
		// TODO: we need to remove alias hit for all apis.
		SearchRequest searchRequest = new SearchRequest(indexAliasName);
		searchRequest.source(sourceBuilder);
		searchRequest.routing(searchContext.getEntityId());

		SearchResponse searchResponse;
		// long apiStartTime = Instant.now().toEpochMilli();
		try {
			searchResponse = esRepo.getSearchResponse(searchRequest);
			log.debug("searchResponse {}", searchResponse);
		}
		catch (Exception e) {
			log.error("Exception while fetching record from ES for UserId : {} ,with exception : {}",
					searchContext.getEntityId(), CommonsUtility.exceptionFormatter((Exception) e));
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, ES_EXCEPTION,
					"getResponseTxn");
		}

		List<SearchHit> hits = Arrays.asList(searchResponse.getHits().getHits());
		return getResponseFromSearchHits(hits, searchContext, searchResponse.getHits().getTotalHits());
	}

	private EsRepoResponseDto getResponseFromSearchHits(final List<SearchHit> hits, final SearchContext searchContext,
			final long totalHits) {
		if (hits.isEmpty()) {
			log.error("No transaction fetched for userId {} from {} to today", searchContext.getEntityId(),
					searchContext.getFromDate());
			throw ExceptionBuilder.getCustomizedFromDateException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING,
					Utility.getDateTime(searchContext.getFromDate(), yyyy_MM_dd_HH_mm_ss));
		}

		PaginationParams paginationParams = setPaginationParams(hits, searchContext);
		List<TransformedTransactionHistoryDetail> historyDetailsList = null;

		if (hits.size() == searchContext.getPageSize() + 1) {
			historyDetailsList = getHistoryDetailsList(hits.subList(0, hits.size() - 1));
		}
		else {
			historyDetailsList = getHistoryDetailsList(hits);
		}

		List<TransformedTransactionHistoryDetail> dedupedTxns = UtilityExtension
			.removeDuplicateDocuments(historyDetailsList);
		EsRepoResponseDto response = new EsRepoResponseDto(dedupedTxns, totalHits, paginationParams);
		log.info("Final response from repo has txns size : {}, total hits in ES : {}, paginationParams : {}",
				dedupedTxns.size(), totalHits, paginationParams);
		return response;
	}

	private EsRepoResponseDto getResponseForStatement(final SearchContext searchContext, final PaginationParams params)
			throws ParseException {

		// Fetch fromDate in epoch of secondary es
		String secondaryEsFromDate = configurablePropertiesHolder.getProperty(SECOND_ES_FROM_DATE, String.class);
		/*
		 * The value in above property is string in 2024-06-01 00:00:00.000 +0530 format.
		 * Once we add second-es-from-date-in-days property on BO Panel, the value in
		 * above variable will be some days i.e. int value instead of date format. We will
		 * have to handle this.
		 */
		long secondaryEsFromDateInEpoch = DateRange.getTimeFromStringFormattedDate(secondaryEsFromDate, DATE_FORMAT);

		Pair<Pair<Long, Long>, Pair<Long, Long>> secondaryAndTertiaryEsFromDateAndToDateRanges = getSecondaryAndTertiaryEsFromAndToDateRangesForStatement(
				searchContext, secondaryEsFromDateInEpoch);

		List<SearchHit> allHits = new ArrayList<>();
		long totalHits = 0;

		PaginationParams paginationParamsAfterSecondaryEsCall = params;

		// Process Secondary ES Range
		if (Objects.nonNull(secondaryAndTertiaryEsFromDateAndToDateRanges.getKey())
				&& Objects.nonNull(secondaryAndTertiaryEsFromDateAndToDateRanges.getKey().getKey())
				&& Objects.nonNull(secondaryAndTertiaryEsFromDateAndToDateRanges.getKey().getValue())
				&& (Objects.isNull(params)
						|| Long.valueOf(params.getTransactionDateEpoch()) >= secondaryEsFromDateInEpoch)) {
			SearchResponse secondaryEsResponse = fetchSearchResponseForStatement(searchContext, params,
					secondaryAndTertiaryEsFromDateAndToDateRanges.getKey(), esV2Repo, searchContext.getPageSize());
			allHits.addAll(Arrays.asList(secondaryEsResponse.getHits().getHits()));
			totalHits += secondaryEsResponse.getHits().getTotalHits();
			if (!allHits.isEmpty()) {
				paginationParamsAfterSecondaryEsCall = null;
			}
		}

		// Process Tertiary ES Range
		if (Objects.nonNull(secondaryAndTertiaryEsFromDateAndToDateRanges.getValue())
				&& Objects.nonNull(secondaryAndTertiaryEsFromDateAndToDateRanges.getValue().getKey())
				&& Objects.nonNull(secondaryAndTertiaryEsFromDateAndToDateRanges.getValue().getValue())
				&& allHits.size() < searchContext.getPageSize() + 1) {
			SearchResponse tertiaryEsResponse = fetchSearchResponseForStatement(searchContext,
					paginationParamsAfterSecondaryEsCall, secondaryAndTertiaryEsFromDateAndToDateRanges.getValue(),
					tertiaryEsRepo, searchContext.getPageSize() - allHits.size());
			allHits.addAll(Arrays.asList(tertiaryEsResponse.getHits().getHits()));
			totalHits += tertiaryEsResponse.getHits().getTotalHits();
		}

		return getResponseFromSearchHits(allHits, searchContext, totalHits);
	}

	private SearchResponse fetchSearchResponseForStatement(final SearchContext searchContext,
			final PaginationParams params, final Pair<Long, Long> dateRange, final IEsRepo esRepo,
			final int updatedPageSize) {
		SearchContext updatedSearchContext = updateSearchContextWithDateRangeForStatement(searchContext, dateRange,
				updatedPageSize);
		log.info("updated searchContext for statement request is : {}", updatedSearchContext);
		SearchSourceBuilder sourceBuilder = EsUtility.createSearchSourceBuilder(updatedSearchContext, params, false);
		SearchRequest searchRequest = new SearchRequest(
				IndexUtility.getIndexListInRange(indexNamePrefix, dateRange.getKey(), dateRange.getValue())
					.toArray(new String[] {}));
		searchRequest.source(sourceBuilder);
		searchRequest.routing(searchContext.getEntityId());

		try {
			return esRepo.getSearchResponse(searchRequest);
		}
		catch (Exception e) {
			log.error(
					"Exception while fetching record from ES for statement API using : {} repo"
							+ " for UserId: {}, with exception: {}",
					esRepo.getClass().getSimpleName(), searchContext.getEntityId(),
					CommonsUtility.exceptionFormatter(e));
			throw ExceptionHandlerUtil.getTaggedException(e, PANAROMA_SERVICE, ES_EXCEPTION,
					"fetchSearchResponseForStatement");
		}
	}

	private SearchContext updateSearchContextWithDateRangeForStatement(final SearchContext searchContext,
			final Pair<Long, Long> dateRange, final int updatedPageSize) {
		SearchContext updatedSearchContext = new SearchContext();
		BeanUtils.copyProperties(searchContext, updatedSearchContext);
		updatedSearchContext.setFromDate(dateRange.getKey());
		updatedSearchContext.setToDate(dateRange.getValue());
		updatedSearchContext.setPageSize(updatedPageSize);

		return updatedSearchContext;
	}

	private Pair<Pair<Long, Long>, Pair<Long, Long>> getSecondaryAndTertiaryEsFromAndToDateRangesForStatement(
			final SearchContext searchContext, final long secondaryEsFromDateInEpoch) {

		// Create 2 ranges of from & to depending on secondary es from date & what actual
		// from & to date is received in request
		Pair<Long, Long> fromAndToDateInSecondaryEsDateServingRange = new Pair<>();
		Pair<Long, Long> fromAndToDateInTertiaryEsDateServingRange = new Pair<>();

		if (secondaryEsFromDateInEpoch > searchContext.getFromDate()
				&& secondaryEsFromDateInEpoch < searchContext.getToDate()) {
			fromAndToDateInTertiaryEsDateServingRange.setKey(searchContext.getFromDate());
			fromAndToDateInTertiaryEsDateServingRange.setValue(secondaryEsFromDateInEpoch - 1);

			fromAndToDateInSecondaryEsDateServingRange.setKey(secondaryEsFromDateInEpoch);
			fromAndToDateInSecondaryEsDateServingRange.setValue(searchContext.getToDate());
		}
		else if (secondaryEsFromDateInEpoch <= searchContext.getFromDate()) {
			fromAndToDateInSecondaryEsDateServingRange.setKey(searchContext.getFromDate());
			fromAndToDateInSecondaryEsDateServingRange.setValue(searchContext.getToDate());
		}
		else {
			fromAndToDateInTertiaryEsDateServingRange.setKey(searchContext.getFromDate());
			fromAndToDateInTertiaryEsDateServingRange.setValue(searchContext.getToDate());
		}

		return new Pair<>(fromAndToDateInSecondaryEsDateServingRange, fromAndToDateInTertiaryEsDateServingRange);
	}

	@Override
	@LogExectutionTime
	public TransformedTransactionHistoryDetail getDetails(final DetailRequest detailRequest) {
		QueryBuilder queryBuilder = null;
		queryBuilder = QueryBuilders.boolQuery()
			.filter(QueryBuilders.termQuery(ENTITY_ID, detailRequest.getEntityId()))
			.filter(QueryBuilders.termQuery(TRANSACTION_ID, detailRequest.getTxnId()))
			.filter(QueryBuilders.termQuery(STREAM_SOURCE,
					TransactionSource.getTransactionSourceEnumByName(detailRequest.getClient().toString())
						.getTransactionSourceKey()));

		SearchSourceBuilder searchSourceBuilder = EsUtil.getEsSearchSourceBuilder(esSocketTimeout, queryBuilder);
		// exclude searchfields fields from ES response
		searchSourceBuilder.fetchSource(null, "searchFields");

		// need to implement index calculation later
		String esIndexName = Utility.getUpdatedIndexName(this.indexNamePrefix, detailRequest.getTxnDate().toString(),
				this.updatedIndexList);
		SearchRequest searchRequest = new SearchRequest(esIndexName);
		searchRequest.source(searchSourceBuilder);
		searchRequest.routing(detailRequest.getEntityId());

		SearchResponse searchResponse = null;

		try {
			searchResponse = esRepo.getSearchResponse(searchRequest);
		}
		catch (Exception e) {
			log.error("Exception while fetching record from ES for UserId : {} ,with exception : {}",
					detailRequest.getEntityId(), CommonsUtility.exceptionFormatter((Exception) e));
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, ES_EXCEPTION,
					GET_DETAILS_FUNC);
		}

		List<SearchHit> hits = Arrays.asList(searchResponse.getHits().getHits());
		if (EsUtil.isNullOrEnptyList(hits)) {
			log.error("No transaction fetched for userId {}", detailRequest.getEntityId());
			PanaromaException e = ExceptionBuilder.getException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING);
			throw ExceptionHandlerUtil.getTaggedException(e, PANAROMA_SERVICE, ES_EXCEPTION, GET_DETAILS_FUNC);
		}

		TransformedTransactionHistoryDetail detail = resultMapper.mapSearchHit(hits.get(0),
				TransformedTransactionHistoryDetail.class);
		log.info("Returned response from ES for txnId : {}", detailRequest.getTxnId());

		return detail;
	}

	@Override
	@LogExectutionTime
	public EsRepoResponseDto getUpiCstPanelResponse(final SearchContext searchContext, final PaginationParams params,
			final Map<String, String> metaInfo) {
		log.debug("getUpiCstPanel Request params searchContext :{} params :{} metaInfo:{} ", searchContext, params,
				metaInfo);
		SearchSourceBuilder sourceBuilder = EsUtility.createSearchSourceBuilder(searchContext, params, false);

		String[] indices = null;
		if (metaInfo != null && metaInfo.containsKey(TXN_DATE)) {
			indices = IndexUtility.getIndexNames(Long.parseLong(metaInfo.get(TXN_DATE)), indexNamePrefix, null);
			IndexUtility.convertToRegexOfGivenIndices(indices);
		}
		else if (StringUtils.isNotBlank(searchContext.getEntityId())) {
			List<String> indices1 = IndexUtility.getIndexListInRange(indexNamePrefix, searchContext.getFromDate(),
					searchContext.getToDate());
			indices = Arrays.copyOf(indices1.toArray(), indices1.size(), String[].class);
		}
		SearchRequest searchRequest = null;
		if (indices == null || indices.length == 0) {
			searchRequest = new SearchRequest(indexAliasName);
		}
		else {
			searchRequest = new SearchRequest(indices);
		}
		searchRequest.source(sourceBuilder);

		if (StringUtils.isNotBlank(searchContext.getEntityId())) {
			searchRequest.routing(searchContext.getEntityId());
		}

		SearchResponse searchResponse;
		log.info("ES SearchRequest for UpiCstPanel Api : {} ", searchRequest);
		// long apiStartTime = Instant.now().toEpochMilli();
		try {
			searchResponse = esRepo.getSearchResponse(searchRequest);
		}
		catch (Exception e) {
			log.error("Exception while fetching record from ES for UserId : {} ,with exception : {}",
					searchContext.getEntityId(), CommonsUtility.exceptionFormatter((Exception) e));
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, ES_EXCEPTION, "");
		}
		/*
		 * long timeTaken = Instant.now().toEpochMilli() - apiStartTime; String tag =
		 * MonitoringConstants.ES_REST_CLIENT_UPI_CST_PANEL; tag += COMMA + ROUTING +
		 * COLON + appliedRouting; metricAgent.recordApiExecutionTime(tag, timeTaken);
		 */

		List<SearchHit> hits = Arrays.asList(searchResponse.getHits().getHits());
		if (hits.isEmpty()) {
			log.error("No transaction fetched for userId {} from {} to today", searchContext.getEntityId(),
					searchContext.getFromDate());
			throw ExceptionBuilder.getCustomizedFromDateException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING,
					Utility.getDateTime(searchContext.getFromDate(), yyyy_MM_dd_HH_mm_ss));
		}

		PaginationParams paginationParams = setPaginationParams(hits, searchContext);
		List<TransformedTransactionHistoryDetail> historyDetailsList = null;

		if (hits.size() == searchContext.getPageSize() + 1) {
			historyDetailsList = getHistoryDetailsList(hits.subList(0, hits.size() - 1));
		}
		else {
			historyDetailsList = getHistoryDetailsList(hits);
		}

		List<TransformedTransactionHistoryDetail> dedupedTxns = UtilityExtension
			.removeDuplicateDocuments(historyDetailsList);
		EsRepoResponseDto response = new EsRepoResponseDto(dedupedTxns, searchResponse.getHits().getTotalHits(),
				paginationParams);
		log.info("Returned Response from repo for UPI cstPanel API: {}", response);
		return response;
	}

	private PaginationParams setPaginationParams(final List<SearchHit> hitsList, final SearchContext searchContext) {
		PaginationParams paginationParams = new PaginationParams();
		TransformedTransactionHistoryDetail lastFetchedRecord = null;
		if (hitsList.size() == searchContext.getPageSize() + 1) {
			// removing last record as this was fetched just to check whether next page
			// exists or not.
			// this record need not to be sent in response
			// taking sublist to prevent last element from setting in response

			lastFetchedRecord = resultMapper.mapSearchHit(hitsList.get(hitsList.size() - 2),
					TransformedTransactionHistoryDetail.class);
			paginationParams.setPaginationStreamSource(String.valueOf(lastFetchedRecord.getStreamSource()));
			paginationParams.setPaginationTxnId(lastFetchedRecord.getTxnId());
			paginationParams.setTransactionDateEpoch(String.valueOf(lastFetchedRecord.getTxnDate()));
			paginationParams.setPageNo(searchContext.getPageNo() + 1);
		}
		return paginationParams;
	}

	protected List<TransformedTransactionHistoryDetail> getHistoryDetailsList(final List<SearchHit> searchHits) {
		return searchHits.stream()
			.map(hit -> resultMapper.mapSearchHit(hit, TransformedTransactionHistoryDetail.class))
			.filter(Objects::nonNull)
			.collect(Collectors.toCollection(LinkedList::new));
	}

	@LogExectutionTime
	public TransformedTransactionHistoryDetail getLatestTxn(final SearchContext searchContext) {

		SearchSourceBuilder sourceBuilder = EsUtility.createSearchSourceBuilder(searchContext, null, false);
		// Todo: Add field parameter to fetch specific field from ES.
		SearchRequest searchRequest = new SearchRequest(indexAliasName);
		searchRequest.source(sourceBuilder);
		searchRequest.routing(searchContext.getEntityId());
		SearchResponse searchResponse;
		try {
			searchResponse = esRepo.getSearchResponse(searchRequest);
		}
		catch (Exception e) {
			log.error("Exception while fetching record from ES for UserId : {} ,with exception : {}",
					searchContext.getEntityId(), CommonsUtility.exceptionFormatter((Exception) e));
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_INTERNAL_SERVICE, ES_EXCEPTION, "");
		}
		List<SearchHit> hits = Arrays.asList(searchResponse.getHits().getHits());
		if (Objects.isNull(hits) || hits.isEmpty()) {
			log.error("No transaction fetched for userId {} from {} to today", searchContext.getEntityId(),
					searchContext.getFromDate());
			return null;
		}
		TransformedTransactionHistoryDetail historyDetails = resultMapper.mapSearchHit(hits.get(ZERO),
				TransformedTransactionHistoryDetail.class);
		return historyDetails;
	}

	@LogExectutionTime
	public TransformedTransactionHistoryDetail getAnyTxn(final SearchContext searchContext) {

		SearchSourceBuilder sourceBuilder = EsUtility.createSearchSourceBuilderForAnyTxn(searchContext, null, false);
		// Todo: Add field parameter to fetch specific field from ES.
		SearchRequest searchRequest = new SearchRequest(indexAliasName);
		searchRequest.source(sourceBuilder);
		searchRequest.routing(searchContext.getEntityId());
		SearchResponse searchResponse;
		try {
			searchResponse = esRepo.getSearchResponse(searchRequest);
		}
		catch (Exception e) {
			log.error("Exception while fetching record from ES for UserId : {} ,with exception : {}",
					searchContext.getEntityId(), CommonsUtility.exceptionFormatter((Exception) e));
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_INTERNAL_SERVICE, ES_EXCEPTION, "");
		}
		List<SearchHit> hits = Arrays.asList(searchResponse.getHits().getHits());
		if (Objects.isNull(hits) || hits.isEmpty()) {
			log.error("No transaction fetched for userId {} from {} to today", searchContext.getEntityId(),
					searchContext.getFromDate());
			return null;
		}
		TransformedTransactionHistoryDetail historyDetails = resultMapper.mapSearchHit(hits.get(ZERO),
				TransformedTransactionHistoryDetail.class);
		return historyDetails;
	}

	@Override
	@LogExectutionTime
	public TransformedTransactionHistoryDetail fetchDataFromEs(final ToggleVisibilityRequest toggleVisibilityRequest) {
		try {
			String docId = toggleVisibilityRequest.getUserId() + "|" + toggleVisibilityRequest.getStreamSource() + "|"
					+ toggleVisibilityRequest.getTxnId();
			String indexName = Utility.getUpdatedIndexName(this.indexNamePrefix, toggleVisibilityRequest.getTxnDate(),
					this.updatedIndexList);

			GetRequest getRequest = new GetRequest().id(docId)
				.routing(toggleVisibilityRequest.getUserId())
				.index(indexName);

			GetResponse response;
			try {
				response = esRepo.esGetResponse(getRequest);
			}
			catch (Exception e) {
				log.error("Exception while fetching record from ES for UserId : {} ,with exception : {}",
						toggleVisibilityRequest.getUserId(), CommonsUtility.exceptionFormatter((Exception) e));
				throw exceptionMsgBuilder.getEsToggleRequestException(toggleVisibilityRequest);
			}

			if (!response.isExists()) {
				log.error("No Txn found in ES with docId : {}", docId);
				throw exceptionMsgBuilder.getNoDataFoundToggleVisibilityException(toggleVisibilityRequest);
			}

			String sourceAsString = response.getSourceAsString();
			TransformedTransactionHistoryDetail storedObj = null;

			if (sourceAsString != null) {
				storedObj = objectMapper.readValue(sourceAsString, TransformedTransactionHistoryDetail.class);
				log.debug("Found Data from Es, docId : {}", docId);
			}

			return storedObj;
		}
		catch (Exception e) {
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE,
					TOGGLE_VISIBILITY_ES_EXCEPTION, "");
		}
	}

	@Override
	public UserTagsSummaryResponse getUserTagsSummary(final String userId, final String month,
			final SearchContext searchContext) {

		// Build the search source builder for user tags summary
		SearchSourceBuilder searchSourceBuilder = EsUtil.getSourceBuilderForUserTagsSummary(searchContext);

		// Get the list of indices in the specified date range
		List<String> indices = IndexUtility.getIndexListInRange(indexNamePrefix, searchContext.getFromDate(),
				searchContext.getToDate());

		// Create a search request with the indices and search source builder
		SearchRequest searchRequest = new SearchRequest(indices.toArray(new String[] {}));
		searchRequest.source(searchSourceBuilder);
		searchRequest.routing(searchContext.getEntityId());
		searchRequest.allowPartialSearchResults(false);

		// Check if secondary ES should be used for tags user summary
		boolean useSecondaryEsForTagsUserSummary = configurablePropertiesHolder
			.getPropertyWithDefaultValue(USE_SECONDARY_ES_FOR_TAGS_USER_SUMMARY, Boolean.class, true);

		SearchResponse searchResponse;
		try {
			// Fetch the search response from the appropriate ES repository
			if (useSecondaryEsForTagsUserSummary) {
				searchResponse = esV2Repo.getSearchResponse(searchRequest);
			}
			else {
				searchResponse = esRepo.getSearchResponse(searchRequest);
			}
		}
		catch (Exception e) {
			log.error("Exception while fetching record from ES for UserId : {}, with exception : {}",
					searchContext.getEntityId(), CommonsUtility.exceptionFormatter((Exception) e));
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, ES_EXCEPTION);
		}

		return TagHelper.getUserTagsSummaryResponse(searchResponse, searchContext, userId, month);
	}

	@Override
	public TagSummaryResponse getTagSummary(final String userId, final String month, final String tag,
			SearchContext searchContext) {
		// build searchSource builder
		SearchSourceBuilder searchSourceBuilder = EsUtil.getSourceBuilderForTagSummary(searchContext);
		// get list of indices [fromDate, toDate]
		List<String> indices = IndexUtility.getIndexListInRange(indexNamePrefix, searchContext.getFromDate(),
				searchContext.getToDate());
		// create search request
		SearchRequest searchRequest = new SearchRequest(indices.toArray(new String[] {}));
		searchRequest.source(searchSourceBuilder);
		searchRequest.routing(searchContext.getEntityId());
		searchRequest.allowPartialSearchResults(false);
		// check if secondary ES is required or not
		boolean useSecondaryESForTagSummary = configurablePropertiesHolder
			.getPropertyWithDefaultValue(USE_SECONDARY_ES_FOR_TAG_SUMMARY, Boolean.class, true);

		SearchResponse searchResponse;
		try {
			// Fetch the search response from the appropriate ES repository
			if (useSecondaryESForTagSummary) {
				searchResponse = esV2Repo.getSearchResponse(searchRequest); // get
																			// searchResponse
																			// from
																			// secondary
																			// ES
			}
			else {
				searchResponse = esRepo.getSearchResponse(searchRequest); // get
																			// searchResponse
																			// from
																			// primary ES
			}
		}
		catch (Exception e) {
			log.error("Exception while fetching record from ES for UserId : {}, with exception : {}",
					searchContext.getEntityId(), CommonsUtility.exceptionFormatter((Exception) e));
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, ES_EXCEPTION);
		}
		return TagHelper.getTagSummaryResponse(searchResponse, searchContext, userId, month, tag);
	}

}
