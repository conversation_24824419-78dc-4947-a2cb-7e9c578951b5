package com.org.com.webapiv2.filter;

import static com.org.com.webapiv2.constants.MonitoringConstants.API_NAME;
import static com.org.com.webapiv2.constants.MonitoringConstants.CLIENT;
import static com.org.com.webapiv2.constants.MonitoringConstants.COLON;
import static com.org.com.webapiv2.constants.MonitoringConstants.RATE_LIMIT_COUNT;
import static com.org.com.webapiv2.constants.MonitoringConstants.RATE_LIMIT_ERROR_COUNT;
import static com.org.com.webapiv2.constants.MonitoringConstants.USERID;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.BLACKLISTED_APIS_FOR_RATE_LIMIT;
import static com.org.panaroma.commons.constants.WebConstants.API;
import static com.org.panaroma.commons.constants.WebConstants.AUTHORIZATION;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PPBL;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.TRAFFIC_DIVERSION_CUSTOM_ERROR;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COMMA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ConfigurablePropertiesConstants.IS_TRAFFIC_DIVERSION_ENABLED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ConfigurablePropertiesConstants.WHITELISTED_APIS_FOR_TRAFFIC_DIVERSION_ON_RATE_LIMIT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RATE_LIMITING;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SOURCE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TRAFFIC_DIVERSION;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.com.webapiv2.constants.enums.ApiDetailsEnum;
import com.org.com.webapiv2.constants.enums.RateLimiterApiDetailEnum;
import com.org.com.webapiv2.monitoring.MetricAgent;
import com.org.com.webapiv2.monitoring.MonitoringUtility;
import com.org.com.webapiv2.parentResponse.ParentResponse;
import com.org.com.webapiv2.utility.ErrorResponseUtilityUnified;
import com.org.com.webapiv2.utility.ServletRequestRedirectionUtility;
import com.org.middlewaretools.rateLimit.dto.RateLimiterDto;
import com.org.middlewaretools.rateLimit.impl.DefaultRateLimiterService;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.exceptionhandler.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.web.analytics.dto.ErrorInfo;
import com.org.panaroma.web.analytics.service.UthAnalyticsService;
import com.org.panaroma.web.exceptionhandler.ErrorCodeConstants;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import com.org.panaroma.web.rateLimit.ITokenRateLimiterService;
import com.org.panaroma.web.utility.TransactionHistoryMonitoringUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Log4j2
@Order(7)
@Component
public class RateLimitFilterUnified extends OncePerRequestFilter {

	private final DefaultRateLimiterService rateLimiterService;

	private final MetricAgent metricAgent;

	private final ErrorResponseUtilityUnified errorResponseUtility;

	private ObjectMapper objectMapper = new ObjectMapper();

	private ITokenRateLimiterService tokenRateLimiterService;

	private UthAnalyticsService uthAnalyticsService;

	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public RateLimitFilterUnified(final DefaultRateLimiterService rateLimiterService, final MetricAgent metricAgent,
			final ErrorResponseUtilityUnified errorResponseUtility,
			final ITokenRateLimiterService tokenRateLimiterService, final UthAnalyticsService uthAnalyticsService,
			final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.rateLimiterService = rateLimiterService;
		this.metricAgent = metricAgent;
		this.errorResponseUtility = errorResponseUtility;
		this.tokenRateLimiterService = tokenRateLimiterService;
		this.uthAnalyticsService = uthAnalyticsService;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	@Override
	protected void doFilterInternal(final HttpServletRequest request, final HttpServletResponse response,
			final FilterChain filterChain) throws IOException, ServletException {

		Set<String> blackListedApisForRateLimit = configurablePropertiesHolder
			.getPropertyWithDefaultValue(BLACKLISTED_APIS_FOR_RATE_LIMIT, Set.class, Collections.emptySet());

		String api = MdcUtility.getConstantValue(API_NAME);
		if (blackListedApisForRateLimit != null && blackListedApisForRateLimit.contains(api)) {
			filterChain.doFilter(request, response);
			return;
		}

		String apiUri = request.getRequestURI();
		String apiUrl = apiUri.substring(StringUtils.ordinalIndexOf(apiUri, "/", 2) + 1);
		String apiName = MonitoringUtility.getApiName(apiUrl);
		// adding check for running web api filter
		/*
		 * if (!UtilityExtension.isCurrentFilterNotRequired(apiUri)) {
		 * filterChain.doFilter(request, response); return; }
		 */
		// String client = MonitoringUtility.fetchFromParamsOrHeader(request, CLIENT);
		String client = TransactionHistoryMonitoringUtility.fetchCustomClient(request);

		if (ApiDetailsEnum.REWIND_DATA.getApiUrl().equals(apiUrl)) {
			client = "all";
		}
		String userToken = null;
		String custId = null;
		try {

			String rateLimiterApiName = RateLimiterApiDetailEnum.getRateLimiterApiNameByUrl(apiUrl);
			if (StringUtils.isBlank(rateLimiterApiName)) {
				rateLimiterApiName = apiUrl;
			}
			String finalRateLimiterApiName = rateLimiterApiName;
			String finalClient = client;

			Map<String, String> paramMap = new HashMap<String, String>() {
				{
					put(API, finalRateLimiterApiName);
					put(CLIENT, finalClient);
				}
			};

			RateLimiterDto rateLimiterDto = new RateLimiterDto(paramMap);
			boolean acquirePermission = rateLimiterService.acquirePermission(rateLimiterDto);

			String apiNameTag = MdcUtility.getConstantValue(API_NAME);

			String tags = MonitoringUtility.getTagForRateLimiter(client, apiNameTag, acquirePermission);
			metricAgent.incrementCount(RATE_LIMIT_COUNT, tags);
			userToken = TransactionHistoryMonitoringUtility.fetchAuthTokenFromRequest(request, AUTHORIZATION);
			custId = MonitoringUtility.fetchFromParamsOrHeader(request, USERID);
			if (acquirePermission) {
				String openSource = TransactionHistoryMonitoringUtility.fetchFromParamsOrHeader(request,
						WebConstants.OPEN_SOURCE);

				// if request allowed then check token level rate limiting if openSource
				// is not bgAppSync
				if (!checkIfOpenSourceIsBgAppSync(openSource)) {
					tokenRateLimiterService.isUserDailyRateLimit(userToken, client, custId, apiUri);
					tokenRateLimiterService.isUserRateLimited(userToken, client, custId, apiUri);
				}

				filterChain.doFilter(request, response);
			}
			else {
				metricAgent.incrementCount(RATE_LIMIT_ERROR_COUNT, API_NAME + COLON + apiName);

				/**
				 * If api is whiteListed and traffic diversion is enable. Then Instead of
				 * rateLimit throw CUSTOM_RETRY(507,4024).
				 */
				List<String> Apis = configurablePropertiesHolder
					.getProperty(WHITELISTED_APIS_FOR_TRAFFIC_DIVERSION_ON_RATE_LIMIT, List.class);
				Boolean trafficDiversion = configurablePropertiesHolder.getProperty(IS_TRAFFIC_DIVERSION_ENABLED,
						Boolean.class);

				if (configurablePropertiesHolder
					.getProperty(WHITELISTED_APIS_FOR_TRAFFIC_DIVERSION_ON_RATE_LIMIT, List.class)
					.contains(apiNameTag)
						&& configurablePropertiesHolder.getProperty(IS_TRAFFIC_DIVERSION_ENABLED, Boolean.class)) {
					metricAgent.incrementCount(TRAFFIC_DIVERSION, SOURCE + MonitoringConstants.COLON + RATE_LIMITING
							+ COMMA + MonitoringConstants.API_NAME + MonitoringConstants.COLON + apiNameTag);
					throw ExceptionFactory.getException(PANAROMA_SERVICE, TRAFFIC_DIVERSION_CUSTOM_ERROR);
				}
				else {
					// if request not allowed throw rate limit exception
					throw ExceptionFactory.getException(PANAROMA_SERVICE, ErrorCodeConstants.RATE_LIMITER_ERROR);
				}
			}
			// filterChain.doFilter(request,response);
		}
		catch (com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException e) {
			ErrorInfo errorInfo = ErrorInfo.builder()
				.responseCode(e.getResponseCode())
				.httpCode(e.getHttpCode())
				.tag(e.getTag())
				.message(e.getMessage())
				.build();
			uthAnalyticsService.dumpResponseForException(null, request, errorInfo, userToken);

			// ParentResponse parentResponse =
			// ParentResponse.builder().responseCode(e.getResponseCode()).httpCode(e.getHttpCode())
			// .message(e.getMessage()).status(FAILURE).retryable(false).build();
			ParentResponse parentResponse = errorResponseUtility.handleError((Throwable) e, apiUrl, client);
			if (!ServletRequestRedirectionUtility.isEligibleForRedirection()) {

				// Setting custom response in HttpServletResponse
				errorResponseUtility.setCustomResponse(response, parentResponse);
			}

		}
		catch (PanaromaException e) {
			ErrorInfo errorInfo = ErrorInfo.builder()
				.responseCode(e.getResponseCode())
				.httpCode(e.getHttpCode())
				.tag(e.getTag())
				.message(e.getMessage())
				.build();
			uthAnalyticsService.dumpResponseForException(null, request, errorInfo, userToken);

			ParentResponse parentResponse = errorResponseUtility.handleError((Throwable) e, apiUrl, client);
			setCustomResponse(request, response, parentResponse);
		}
		catch (Exception e) {
			log.error("inside RateLimitFilter error : {} {}", e.getMessage(), CommonsUtility.exceptionFormatter(e));
			filterChain.doFilter(request, response);
		}
	}

	private boolean checkIfOpenSourceIsBgAppSync(final String openSource) {
		return WebConstants.BG_APP_SYNC.equalsIgnoreCase(openSource);
	}

	public void setCustomResponse(final HttpServletRequest request, final HttpServletResponse response,
			final ParentResponse errorResponse) throws IOException {
		switch (request.getHeader(WebConstants.CLIENT)) {
			case PPBL:
				response.setStatus(errorResponse.getHttpCode());
				break;
			default:
				// do nothing
		}

		// Setting custom response in HttpServletResponse
		errorResponseUtility.setCustomResponse(response, errorResponse);
	}

}
