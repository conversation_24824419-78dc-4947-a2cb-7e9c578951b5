package com.org.com.webapiv2.dto.upiSearch;

import com.org.com.webapiv2.dto.upiCstPanel.FwdTxnDetails;
import com.org.com.webapiv2.dto.upiCstPanel.RefundDetails;
import com.org.panaroma.commons.utils.JsonUtils;
import lombok.Data;

import java.util.List;

@Data
public class ResponseTxn {

	String txnId;

	String status;

	String amount;

	Integer txnIndicator;

	String date;

	String updatedDate;

	String note;

	String initiationType;

	String initiationMode;

	String rrn;

	String errorCode;

	String errorMessage;

	String refCategory;

	String merchantId;

	Boolean isOnusMerchant;

	String orderId;

	String merchantOrderId;

	String businessType;

	String category;

	String orgTxnId;

	String refUrl;

	String merchantGenre;

	String txnNarration;

	String umn;

	FwdTxnDetails fwdTxnDetails; // PTH-918 --> CST Bot Flow Requirement

	List<RefundDetails> refundDetails; // PTH-918 --> CST Bot Flow Requirement

	boolean isHiddenTxn;

	String otherUpiAppName; // PTH-1170, PSP Names

	/*
	 * Below field is added for https://jira.mypaytm.com/browse/PTH-919 Keeping this field
	 * as string instead of boolean because we will be just passing the value whatever
	 * comes in contextMap.isDeemed field. Ideally this should just be true / false, in
	 * case of any other value we won't be responsible
	 */
	String isDeemed;

	UserDetails payer;

	UserDetails payee;

	String paymentCategory;

	Boolean isUpiLiteTxn; // PTH-1171 --> send upi lite identifier to CST

	// internationalTxnDetails was added for https://jira.mypaytm.com/browse/PTH-733
	InternationalTxnDetails internationalTxnDetails;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}