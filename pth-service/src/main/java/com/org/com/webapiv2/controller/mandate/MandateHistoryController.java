package com.org.com.webapiv2.controller.mandate;

import static com.org.panaroma.commons.constants.WebConstants.CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.USER_ID;
import static com.org.panaroma.commons.constants.WebConstants.USER_ID_2;

import com.org.com.webapiv2.controller.validator.MandateHistoryRequestValidator;
import com.org.com.webapiv2.dto.mandate.MandateHistoryExtReqBody;
import com.org.com.webapiv2.dto.mandate.MandateHistoryExtResp;
import com.org.com.webapiv2.dto.mandate.MandateHistoryIntReqBody;
import com.org.com.webapiv2.dto.mandate.MandateHistoryIntResp;
import com.org.com.webapiv2.dto.mandate.MandateHistoryReq;
import com.org.com.webapiv2.service.mandate.IMandateHistoryService;
import com.org.panaroma.commons.enums.Client;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pth/")
@Log4j2
public class MandateHistoryController {

	private IMandateHistoryService mandateHistoryService;

	private MandateHistoryRequestValidator mandateHistoryRequestValidator;

	@Autowired
	public MandateHistoryController(final IMandateHistoryService mandateHistoryService,
			final MandateHistoryRequestValidator mandateHistoryRequestValidator) {
		this.mandateHistoryService = mandateHistoryService;
		this.mandateHistoryRequestValidator = mandateHistoryRequestValidator;
	}

	@PostMapping(value = { "ext/v1/mandate/history" }, produces = "application/json; charset=UTF-8")
	public ResponseEntity<MandateHistoryExtResp> getMandateHistoryExternal(
			@RequestHeader(value = CLIENT, required = true) final Client client,
			@RequestAttribute(value = USER_ID) final String userId,
			@Valid @RequestBody final MandateHistoryExtReqBody reqBody) throws Exception {

		log.info("Request received to fetch mandate-history. client: {}, reqBody: {}", client.toString(), reqBody);
		mandateHistoryRequestValidator.validate(reqBody);
		// As response logs are getting printed using Logbook. search "Logbook" &&
		// "request" to check these logs.
		// log.info("Request received to fetch mandate-history. client: {}, reqBody: {}",
		// client.toString(), reqBody);
		MandateHistoryReq req = MandateHistoryReq.builder()
			.umn(reqBody.getUmn())
			.mandateCreationDate(reqBody.getMandateCreationDate())
			.client(client.toString())
			.entityId(userId)
			.paginationParams(reqBody.getPaginationParams())
			.build();
		MandateHistoryExtResp resp = mandateHistoryService.getMandateHistoryExt(req);

		return ResponseEntity.ok().body(resp);
	}

	@PostMapping(value = { "int/v1/mandate/history" }, produces = "application/json; charset=UTF-8")
	public ResponseEntity<MandateHistoryExtResp> getMandateHistoryInternal(
			@RequestHeader(value = CLIENT) final Client client, @RequestHeader(value = USER_ID_2) final String userId,
			@Valid @RequestBody final MandateHistoryIntReqBody reqBody) throws Exception {

		log.info("Request received to fetch mandate-history. client: {}, reqBody: {}", client.toString(), reqBody);

		MandateHistoryReq req = MandateHistoryReq.builder()
			.umn(reqBody.getUmn())
			.mandateCreationDate(reqBody.getMandateCreationDate())
			.client(client.toString())
			.entityId(userId)
			.build();

		MandateHistoryExtResp resp = mandateHistoryService.getMandateHistoryExt(req);

		return ResponseEntity.ok().body(resp);
	}

}
