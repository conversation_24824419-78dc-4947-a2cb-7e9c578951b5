package com.org.com.webapiv2.dto.upiSearch;

import com.org.panaroma.commons.utils.JsonUtils;
import lombok.Data;

@Data
public class UserDetails {

	String name;

	String vpa;

	String bankAccount;

	String bankName;

	String ifsc;

	String mobile;

	String oauthCustId;

	String reversalRespCode;

	// Added for PTH-1318
	String accType;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
