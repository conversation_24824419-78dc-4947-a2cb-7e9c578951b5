package com.org.com.webapiv2.cstCategoryCode;;

/**
 * Please create new Enums in {@link com.paytm.bank.psp.common.enums}
 * This class will remain ever growing if we do not start dividing enums in seperate file inside above mentioned package
 */

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

public class Enums {

	public static Enums.ResponseCodeClients ResponseCodeClients;

	public enum TxnInfoType {

		PAY, COLLECT, DEBIT, CREDIT, DEBIT_REVERSAL, CREDIT_REVERSAL, CREATE, UPDATE, REVOKE, EXPIRE, REFUND, PAUSE,
		UNPAUSE

	}

	public enum MandateType {

		RECURRING, ONETIME

	}

	public enum MandateState {

		ACTIVE, PENDING_CREATE_AUTH, PENDING_CREATE, PENDING_UPDATE, EXPIRED, CANCELLED, DECLINED, COMPLETED, DEEMED,
		PENDING_CANCELLATION, PENDING_DECLINE, PENDING_EXECUTE, FAILED_EXECUTION_ACTIVE, FAILED_EXECUTION_EXPIRED,
		FAILED_UPDATE_ACTIVE, SUCCESS_UPDATE_ACTIVE, FAILED_CREATE, FAILED_REVOKE_ACTIVE, PENDING_PAUSED, PAUSED,
		PENDING_UNPAUSED, PENDING_REVOKED, UNPAUSED, PENDING_EXPIRED

	}

	// inform addition of new category MERCHANT2VPA to recon,panel,refund todo
	public enum TxnInfoCategory {

		VPA2VPA, VPA2MERCHANT, VPA2AADHAR, VPA2AADHARIIN, VPA2ACCOUNT, VPA2MOBILE, VPA2CARD, MERCHANT2VPA,
		MERCHANT2ACCOUNT, VPA2P2PM

	}

	/*
	 * AMBIGUOUS - this is populated in status of txn phase for cbs terminating code
	 * errors
	 */
	public enum TxnInfoStatus {

		DEEMED, SUCCESS, FAILURE, PENDING, CREATED_AT_CREDIT, CREATED_AT_DEBIT, AMBIGUOUS

	}

	public enum TxnParticipantType {

		PAYEE, PAYER

	}

	@Getter
	public enum Purpose {

		DEFAULT_OR_P2P("00"), SEBI("01"), AMC("02"), TRAVEL("03"), HOSPITALITY("04"), HOSPITAL("05"), TELECOM("06"),
		INSUREANCE("07"), EDUCATION("08"), GIFTING("09"), OTHERS("10"), SUBSCRIPTION("14");

		private static final Map<String, Purpose> stateMap = new HashMap<>();

		static {
			Purpose[] mandatePurposes = values();

			for (Purpose purpose : mandatePurposes) {
				stateMap.put(purpose.getValue(), purpose);
			}
		}

		private final String value;

		Purpose(final String value) {
			this.value = value;
		}

		public static Purpose getMandatePurpose(final String mode) {
			return stateMap.get(mode);
		}

	}

	public enum UserType {

		ENTITY, PERSON, VEHICLE

	}

	public enum TxnInfoBusinessType {

		CASHBACK_PREAPPROVED, REFUND_PREAPPROVED, PAY_PREAPPROVED, BANK_TRANSFER_PREAPPROVED, EXT_REFUND_PREAPPROVED,
		OFFLINE_CASHBACK_PREAPPROVED, OMS_REFUND_PREAPPROVED, PPBL_CORPORATE_PREAPPROVED, PG_PAYOUT_PREAPPROVED,
		PROMO_TIERED_CASHBACK_PREAPPROVED, ONLINE_INTENT, OFFLINE_INTENT, FSM_INTENT, UPI_INTENT_V2,
		GRATIFICATION_PREAPPROVED, PAYOUT_PREAPPROVED, CORPORATE_PAYOUT, MANDATE, FASTAG_TOPUP, WALLET_TOPUP,
		/**
		 * refund which are initiated by other PSP and we are involved as bank only
		 */
		THIRD_PARTY_REFUND, USER_PREAPPROVED, UPI_INTENT_V2_USER_PREAPPROVED, DIRECT_SETTLEMENT_USER_PREAPPROVED,
		FASTAG_TOPUP_USER_PREAPPROVED, WALLET_TOPUP_USER_PREAPPROVED, FSM_INTENT_USER_PREAPPROVED,
		ONLINE_INTENT_USER_PREAPPROVED, OFFLINE_INTENT_USER_PREAPPROVED,

	}

	public enum ResponseCodeClients {

		APP, NPCI, CBS, PG, PS, RISK, SWITCH, TELCO, UTH, UPI_CSTBOT, PREAPPROVED, UPI_CC, UPI_LITE_PAY, UPI_LITE_TOPUP,
		UPI_GLOBAL, UPI_LITE_DEREGISTER

	}

	public enum PaymentInstrument {

		// Not using UNDERSCORE here since the PassbookUtilsCache uses UNDERSCORE in the
		// key string
		// which can then possible interfere with cache key creation logic
		PPBL, PPBLWALLET, NONPPBL

	}

}
