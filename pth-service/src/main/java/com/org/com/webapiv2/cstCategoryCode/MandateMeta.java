package com.org.com.webapiv2.cstCategoryCode;;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/***
 * Author: sahil.singhal Date: 1/5/19
 ***/

@Data
public class MandateMeta {

	@JsonProperty("validity_start_date")
	private Date validityStartDate;

	@JsonProperty("validity_end_date")
	private Date validityEndDate;

	@JsonProperty("mandate_state")
	private String mandateState;

	@JsonProperty("acc_ref_id")
	private String accountRefId;

	@JsonProperty("recurrence")
	private String recurrence;

	@JsonProperty("recurrence_rule")
	private String recurrenceRule;

	@JsonProperty("recurrence_type")
	private String recurrenceType;

	@JsonProperty("amount_rule")
	private String amountRule;

	@JsonProperty("max_debit_amount")
	private BigDecimal maxDebitAmount;

	private String type;

}