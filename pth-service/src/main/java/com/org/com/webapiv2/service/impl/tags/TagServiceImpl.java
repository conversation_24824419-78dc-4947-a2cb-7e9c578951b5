package com.org.com.webapiv2.service.impl.tags;

import static com.org.com.webapiv2.constants.Constants.TAG_SUCCESSFULLY_SAVED;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.AUTO_TAGGING_FEATURE_ROLLOUT;
import static com.org.panaroma.commons.constants.Constants.FUTURE_LOG_REMOVER_IDENTIFIER;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.USER_TAGS_SUMMARY_FROM_DATE_IN_DAYS;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.MONTH_YEAR_FORMAT;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.MAX_TAG_COUNT;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.TAG_MAX_LENGTH;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.TAG_MIN_LENGTH;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.SEARCH_SUPPORTED_TAGS_MAX_COUNT;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.USER_TAG_COUNT_IN_SUGGESTED_TAGS;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.SUGGESTED_TAGS_MAX_COUNT;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.USER_TAG_LIMIT;
import static com.org.panaroma.commons.constants.TagsConstants.TAG_SERVICE_FUNC;
import static com.org.panaroma.commons.constants.TagsConstants.TAG_SERVICE_INTERNAL_FUNC;
import static com.org.panaroma.commons.constants.WebConstants.CURRENT_TXN_TAGS;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT_TAGS;
import static com.org.panaroma.commons.constants.WebConstants.OK_200;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_SUPPORTED_TAGS;
import static com.org.panaroma.commons.constants.WebConstants.SUCCESS;
import static com.org.panaroma.commons.constants.WebConstants.SUGGESTED_TAGS;
import static com.org.panaroma.commons.constants.WebConstants.SYSTEM_TAGS;
import static com.org.panaroma.commons.constants.WebConstants.TXN_TAGS;
import static com.org.panaroma.commons.constants.WebConstants.USER_TAGS;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.KAFKA_EXCEPTION;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.MAX_NO_OF_TAG_ERROR;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.NO_TAG_FOUND;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.TAGS_INTERNAL_SERVER_ERROR;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.TAGS_INVALID_PARAMETER;

import com.org.com.webapiv2.constants.enums.KafkaClientEnum;
import com.org.com.webapiv2.dao.IEsDao;
import com.org.com.webapiv2.dao.ITagDao;
import com.org.com.webapiv2.dto.tags.GetTagRequest;
import com.org.com.webapiv2.dto.tags.GetTagsResponse;
import com.org.com.webapiv2.dto.tags.GetTagsV2Response;
import com.org.com.webapiv2.dto.tags.TagDto;
import com.org.com.webapiv2.dto.tags.TagInfo;
import com.org.com.webapiv2.dto.tags.TagRequestBody;
import com.org.com.webapiv2.dto.tags.TagsRequest;
import com.org.com.webapiv2.dto.tags.UserTagsSummaryRequest;
import com.org.com.webapiv2.dto.tags.UserTagsSummaryResponse;
import com.org.com.webapiv2.dto.tags.TagSummaryRequest;
import com.org.com.webapiv2.dto.tags.TagSummaryResponse;
import com.org.com.webapiv2.model.SystemTagData;
import com.org.com.webapiv2.model.TagsData;
import com.org.com.webapiv2.model.TxnTags;
import com.org.com.webapiv2.model.UserTagData;
import com.org.com.webapiv2.parentResponse.ParentResponse;
import com.org.com.webapiv2.service.tags.ITagservice;
import com.org.com.webapiv2.utility.TagSearchContext;
import com.org.com.webapiv2.utility.tag.TagHelper;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.TagType;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.entity.AutoTaggingDetails;
import com.org.panaroma.commons.exceptionhandler.ExceptionBuilder;
import com.org.panaroma.commons.exceptionhandler.ExceptionHandlerUtil;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.tags.IntelligentTagSuggestionService;
import com.org.panaroma.commons.utils.AutoTaggingUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Log4j2
public class TagServiceImpl implements ITagservice {

	private final ITagDao tagDao;

	private final IEsDao esDao;

	private Integer maxTagCount;

	private Integer tagMaxLength;

	private Integer tagMinLength;

	private Integer userTagLimit;

	private Integer userTagCountInSuggestedTags;

	private Integer suggestedTagsMaxCount;

	private Integer searchSupportedTagsMaxCount;

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	private final IKafkaClient kafkaClient;

	private final RolloutStrategyHelper rolloutStrategyHelper;

	private final IntelligentTagSuggestionService intelligentTagSuggestionService;

	@Autowired
	public TagServiceImpl(final ITagDao tagDao, final IEsDao esTagDao,
			@Value("${tagging.max.tags.count.per.txn}") final Integer maxTagCount,
			@Value("${tagging.tag.max.length}") final Integer tagMaxLength,
			@Value("${tagging.tag.min.length}") final Integer tagMinLength,
			@Value("${tagging.user.tags.limit}") final Integer userTagLimit,
			@Value("${tagging.user.tags.count.in.suggested.tags}") final Integer userTagCountInSuggestedTags,
			@Value("${tagging.suggested.tags.max.count}") final Integer suggestedTagsMaxCount,
			@Value("${tagging.search.supported.tags.max.count}") final Integer searchSupportedTagsMaxCount,
			final IKafkaClient kafkaClient, final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final RolloutStrategyHelper rolloutStrategyHelper,
			final IntelligentTagSuggestionService intelligentTagSuggestionService) {
		this.tagDao = tagDao;
		this.esDao = esTagDao;
		this.maxTagCount = maxTagCount;
		this.tagMaxLength = tagMaxLength;
		this.tagMinLength = tagMinLength;
		this.userTagLimit = userTagLimit;
		this.userTagCountInSuggestedTags = userTagCountInSuggestedTags;
		this.suggestedTagsMaxCount = suggestedTagsMaxCount;
		this.searchSupportedTagsMaxCount = searchSupportedTagsMaxCount;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.kafkaClient = kafkaClient;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.intelligentTagSuggestionService = intelligentTagSuggestionService;
	}

	@Override
	public ParentResponse getTags(final GetTagRequest request) {
		List<UserTagData> userTagDataList = null;
		SystemTagData systemTagData = null;
		List<TagsData> txnTags = null;
		try {
			SearchContext searchContext = TagSearchContext.getSearchContextForUserTags(request.getUserId());
			log.info(FUTURE_LOG_REMOVER_IDENTIFIER + "search context for user tags: {}", searchContext);

			userTagDataList = tagDao.getUserTagData(request.getUserId(), searchContext);

			log.info(FUTURE_LOG_REMOVER_IDENTIFIER + "final user tags list : {}", userTagDataList);

			if (StringUtils.isNotBlank(request.getRequestBody().getTxnId())) {
				TransformedTransactionHistoryDetail txn = this.fetchTxn(request);
				txnTags = convertTransformedTagsToTagsData(txn.getTags());
				// Fetch system tag data based on txn
				systemTagData = tagDao.getSystemTagData(txn);
			}
		}
		catch (Exception e) {
			throw ExceptionHandlerUtil.getTaggedException(e, PANAROMA_SERVICE, TAGS_INTERNAL_SERVER_ERROR, "");
		}
		TagDto tagDto = dedupeAndCreateDto(userTagDataList, systemTagData, txnTags);
		GetTagsResponse tagsResponse = buildGetTagsResponse(tagDto);
		ParentResponse response = ParentResponse.builder()
			.response(tagDto)
			.status(SUCCESS)
			.message(SUCCESS)
			.httpCode(OK_200)
			.response(tagsResponse)
			.build();
		log.info("Sending response of GetTag api : {}", response);
		return response;
	}

	/**
	 * This method retrieves tags for a given request, including user tags, system tags,
	 * and transaction tags. It fetches user tag data and system tag data based on the
	 * user ID and category ID from the request. If a transaction ID is provided, it also
	 * fetches transaction tags. It then categorizes the user tags into user category tags
	 * and other user category tags. Finally, it builds and returns a response containing
	 * suggested tags and search supported tags.
	 * @param request The request object containing user ID, category ID, and optionally
	 * transaction ID and other details.
	 * @return A ParentResponse object containing the tags response.
	 */
	@Override
	public ParentResponse getTagsV2(final GetTagRequest request) {
		List<UserTagData> userTagDataList = null;
		SystemTagData systemTagData = null;
		List<TagsData> currentTxnTags = null;

		// Determine the category ID, defaulting to DEFAULT_TAGS if not provided
		String categoryId = StringUtils.isBlank(request.getRequestBody().getCategoryId()) ? DEFAULT_TAGS
				: request.getRequestBody().getCategoryId();

		try {
			SearchContext searchContext = TagSearchContext.getSearchContextForUserTags(request.getUserId());
			log.info(FUTURE_LOG_REMOVER_IDENTIFIER + "search context for user tags: {}", searchContext);

			// Fetch user tag data based on user ID and search context
			userTagDataList = tagDao.getUserTagData(request.getUserId(), searchContext);

			// If a transaction ID is provided, fetch transaction tags
			if (StringUtils.isNotBlank(request.getRequestBody().getTxnId())) {
				TransformedTransactionHistoryDetail txn = this.fetchTxn(request);
				currentTxnTags = convertTransformedTagsToTagsData(txn.getTags());
				// Fetch system tag data based on txn
				systemTagData = tagDao.getSystemTagData(txn);
			}
		}
		catch (Exception e) {
			log.error("Exception while fetching tags for request : {}", request);
			// Handle exceptions by throwing a tagged exception
			throw ExceptionHandlerUtil.getTaggedException(e, PANAROMA_SERVICE, TAGS_INTERNAL_SERVER_ERROR, "");
		}

		// Categorize user tags into user category tags and other user category tags
		List<TagInfo> userCategoryTags = getUserCategoryTags(userTagDataList, categoryId);
		List<TagInfo> userOtherCategoryTags = getUserOtherCategoryTags(userTagDataList, categoryId);

		// Get transaction tags and system tags
		List<TagInfo> userCurrentTxnTags = getTxnTags(userTagDataList, currentTxnTags);
		List<TagInfo> systemTags = getSystemTags(systemTagData, userTagDataList);

		// remove userCurrentTxnTags from UserCategoryTags and UserOtherCategoryTags
		List<String> currentTxnTagNames = userCurrentTxnTags.stream().map(TagInfo::getTag).collect(Collectors.toList());
		userCategoryTags = userCategoryTags.stream()
			.filter(tagInfo -> !currentTxnTagNames.contains(tagInfo.getTag()))
			.collect(Collectors.toList());

		userOtherCategoryTags = userOtherCategoryTags.stream()
			.filter(tagInfo -> !currentTxnTagNames.contains(tagInfo.getTag()))
			.collect(Collectors.toList());

		// deduping system tags from user tags
		systemTags = systemTags.stream()
			.filter(tagInfo -> !currentTxnTagNames.contains(tagInfo.getTag()))
			.collect(Collectors.toList());

		// Build the response containing suggested tags and search supported tags
		GetTagsV2Response getTagsV2Response = buildGetTagV2Response(userCategoryTags, userOtherCategoryTags,
				userCurrentTxnTags, systemTags, request);

		// Create and return the parent response
		ParentResponse response = ParentResponse.builder()
			.status(SUCCESS)
			.message(SUCCESS)
			.httpCode(OK_200)
			.response(getTagsV2Response)
			.build();
		log.info("Sending response of GetTag V2 api : {}", response);
		return response;
	}

	/**
	 * This method builds the response for the GetTagsV2 API by generating suggested tags
	 * and search supported tags. It combines tags from user category, other user
	 * categories, and system tags, and removes any tags that match with user transaction
	 * tags. It ensures the total number of tags does not exceed the specified limits.
	 * @param userCategoryTags List of tags from the user used tags in older same category
	 * txns (Same category as current txn's category).
	 * @param userOtherCategoryTags List of tags from the user used tags in older other
	 * category txns (other category as current txn's category).
	 * @param userCurrentTxnTags List of tags from user current transactions.
	 * @param systemTags List of system tags.
	 * @return A GetTagsV2Response object containing the suggested tags and search
	 * supported tags.
	 */
	private GetTagsV2Response buildGetTagV2Response(final List<TagInfo> userCategoryTags,
			final List<TagInfo> userOtherCategoryTags, final List<TagInfo> userCurrentTxnTags,
			final List<TagInfo> systemTags, final GetTagRequest request) {

		maxTagCount = configurablePropertiesHolder.getPropertyWithDefaultValue(MAX_TAG_COUNT, Integer.class,
				maxTagCount);
		tagMaxLength = configurablePropertiesHolder.getPropertyWithDefaultValue(TAG_MAX_LENGTH, Integer.class,
				tagMaxLength);
		tagMinLength = configurablePropertiesHolder.getPropertyWithDefaultValue(TAG_MIN_LENGTH, Integer.class,
				tagMinLength);

		boolean autoTaggingEnabledForThisUser = rolloutStrategyHelper.isUserWhiteListed(AUTO_TAGGING_FEATURE_ROLLOUT,
				request.getUserId());

		List<TagInfo> suggestedTags = getSuggestedTags(userCategoryTags, userOtherCategoryTags, userCurrentTxnTags,
				systemTags);
		List<TagInfo> searchSupportedTags = getSearchSupportedTags(userCategoryTags, userOtherCategoryTags,
				userCurrentTxnTags, systemTags, suggestedTags);

		Map<String, List<TagInfo>> tags = new HashMap<>();
		tags.put(CURRENT_TXN_TAGS, userCurrentTxnTags);
		tags.put(SUGGESTED_TAGS, suggestedTags);
		tags.put(SEARCH_SUPPORTED_TAGS, searchSupportedTags);

		// doing bucketing of tags used in txn count in 1, 1+, 5+, 10+
		TagHelper.updateBucketingInTagUsedCount(userCurrentTxnTags);
		TagHelper.updateBucketingInTagUsedCount(suggestedTags);
		TagHelper.updateBucketingInTagUsedCount(searchSupportedTags);

		return GetTagsV2Response.builder()
			.maxTagCount(maxTagCount)
			.maxTagLength(tagMaxLength)
			.minTagLength(tagMinLength)
			// AutoTagging feature - 0: disabled, 1: enabled
			.autoTaggingEnabled(autoTaggingEnabledForThisUser ? 1 : 0)
			.tags(tags)
			.build();
	}

	/**
	 * This method generates a list of suggested tags by combining tags from user
	 * category, other user categories, and system tags. It prioritizes adding up to
	 * USER_TAG_COUNT_IN_SUGGESTED_TAGS (BO property) tags from the user category, then
	 * fills up to USER_TAG_COUNT_IN_SUGGESTED_TAGS tags from other user categories if
	 * needed, and finally adds up to (SUGGESTED_TAGS_MAX_COUNT -
	 * USER_TAG_COUNT_IN_SUGGESTED_TAGS) (BO property) tags from system tags. It ensures
	 * the total number of tags does not exceed SUGGESTED_TAGS_MAX_COUNT and removes any
	 * tags that match with user transaction tags.
	 * @param userCategoryTags List of tags from the user used tags in older same category
	 * txns (Same category as current txn's category).
	 * @param userOtherCategoryTags List of tags from the user used tags in older other
	 * category txns (other category as current txn's category).
	 * @param userCurrentTxnTags List of tags from user current transactions.
	 * @param systemTags List of system tags.
	 * @return A list of suggested tags, with a maximum length of 4 after removing tags
	 * that match with user transaction tags.
	 */
	private List<TagInfo> getSuggestedTags(final List<TagInfo> userCategoryTags,
			final List<TagInfo> userOtherCategoryTags, final List<TagInfo> userCurrentTxnTags,
			final List<TagInfo> systemTags) {
		List<TagInfo> suggestedTags = new ArrayList<>();

		userTagCountInSuggestedTags = configurablePropertiesHolder
			.getPropertyWithDefaultValue(USER_TAG_COUNT_IN_SUGGESTED_TAGS, Integer.class, userTagCountInSuggestedTags);
		suggestedTagsMaxCount = configurablePropertiesHolder.getPropertyWithDefaultValue(SUGGESTED_TAGS_MAX_COUNT,
				Integer.class, suggestedTagsMaxCount);

		// Add up to userTagCountInSuggestedTags tags from userCategoryTags
		int userCategoryCount = Math.min(userCategoryTags.size(), userTagCountInSuggestedTags);
		suggestedTags.addAll(userCategoryTags.subList(0, userCategoryCount));

		// If less than userTagCountInSuggestedTags tags were added from userCategoryTags,
		// add the remaining from userOtherCategoryTags
		if (userCategoryCount < userTagCountInSuggestedTags) {
			int remainingCount = userTagCountInSuggestedTags - userCategoryCount;
			int userOtherCategoryCount = Math.min(userOtherCategoryTags.size(), remainingCount);
			suggestedTags.addAll(userOtherCategoryTags.subList(0, userOtherCategoryCount));
		}

		// System Tags which is not used in suggested tags yet
		Set<String> suggestedTagNames = suggestedTags.stream().map(TagInfo::getTag).collect(Collectors.toSet());

		List<TagInfo> systemTagsNotUsedInSuggestedTag = systemTags.stream()
			.filter(systemTag -> !suggestedTagNames.contains(systemTag.getTag()))
			.toList();

		// Add System Tags in suggested Tags
		suggestedTags.addAll(systemTagsNotUsedInSuggestedTag);

		// Ensure the total number of tags does not exceed
		// tagging.suggested.tags.max.count
		if (suggestedTags.size() > suggestedTagsMaxCount) {
			suggestedTags = suggestedTags.subList(0, suggestedTagsMaxCount);
		}

		return suggestedTags;
	}

	/**
	 * This method generates a list of search supported tags by combining tags from user
	 * category, system, and other categories. It removes any tags that match with user
	 * transaction tags and suggested tags, deduplicates the list, and ensures the total
	 * number of tags does not exceed the search supported tag max length.
	 * @param userCategoryTags List of tags from the user used tags in older same category
	 * txns (Same category as current txn's category).
	 * @param userOtherCategoryTags List of tags from the user used tags in older other
	 * category txns (other category as current txn's category).
	 * @param userCurrentTxnTags List of tags from user current transactions.
	 * @param systemTags List of system tags.
	 * @param suggestedTags List of suggested tags.
	 * @return A list of search supported tags, with a maximum length of userTagLimit
	 * after deduplication.
	 */
	private List<TagInfo> getSearchSupportedTags(final List<TagInfo> userCategoryTags,
			final List<TagInfo> userOtherCategoryTags, final List<TagInfo> userCurrentTxnTags,
			final List<TagInfo> systemTags, final List<TagInfo> suggestedTags) {

		searchSupportedTagsMaxCount = configurablePropertiesHolder
			.getPropertyWithDefaultValue(SEARCH_SUPPORTED_TAGS_MAX_COUNT, Integer.class, searchSupportedTagsMaxCount);

		List<TagInfo> searchSupportedTags = new ArrayList<>();

		// Add all tags from userCategoryTags
		searchSupportedTags.addAll(userCategoryTags);

		// Add all tags from systemTags
		searchSupportedTags.addAll(systemTags);

		// Add all tags from userOtherCategoryTags
		searchSupportedTags.addAll(userOtherCategoryTags);

		// Remove tags from search supported list that match any tags in userTxnTags and
		// suggestedTags
		List<String> txnTagNames = userCurrentTxnTags.stream().map(TagInfo::getTag).collect(Collectors.toList());
		List<String> suggestedTagNames = suggestedTags.stream().map(TagInfo::getTag).collect(Collectors.toList());
		searchSupportedTags = searchSupportedTags.stream()
			.filter(tagInfo -> !txnTagNames.contains(tagInfo.getTag()) && !suggestedTagNames.contains(tagInfo.getTag()))
			.collect(Collectors.toList());

		// Deduplicate the search supported tags list
		searchSupportedTags = searchSupportedTags.stream().distinct().collect(Collectors.toList());

		// Ensure the total number of tags does not exceed searchSupportedTagsMaxCount
		return searchSupportedTags.size() > searchSupportedTagsMaxCount
				? searchSupportedTags.subList(0, searchSupportedTagsMaxCount) : searchSupportedTags;
	}

	// Method to get system tags which is not used by user(means those system tags is not
	// present in user tags)
	private List<TagInfo> getSystemTags(final SystemTagData systemTagData, final List<UserTagData> userTagDataList) {
		List<TagInfo> systemTags = new ArrayList<>();
		if (Objects.nonNull(systemTagData)) {
			for (String tag : systemTagData.getTags()) {
				UserTagData userTagData = getUserTagData(userTagDataList, tag);
				// if userTag is null for given system tag then add this tag in systemTags
				// final list
				if (Objects.isNull(userTagData)) {
					TagInfo tagInfo = TagInfo.builder().tag(tag).tagType(TagType.SYSTEM_TAGS.getTagTypeValue()).build();
					systemTags.add(tagInfo);
				}
				else {
					TagInfo tagInfo = TagInfo.builder()
						.tag(tag)
						.tagType(TagType.SYSTEM_TAGS.getTagTypeValue())
						.count(String.valueOf(userTagData.getCount()))
						.build();
					systemTags.add(tagInfo);
				}
			}
		}
		return systemTags;
	}

	private List<TagInfo> getUserCategoryTags(final List<UserTagData> userTagDataList, final String categoryId) {
		List<TagInfo> userCategoryTags = new ArrayList<>();
		if (!CollectionUtils.isEmpty(userTagDataList)) {
			userCategoryTags = userTagDataList.stream()
				.filter(userTagData -> userTagData.getCategoryId() != null
						&& userTagData.getCategoryId().contains(categoryId))
				.sorted(Comparator.comparing(UserTagData::getUpdatedTimeStamp).reversed())
				.map(userTagData -> TagInfo.builder()
					.tag(userTagData.getTag())
					.count(String.valueOf(userTagData.getCount()))
					.tagType(TagType.SAME_CATEGORY_USER_TAGS.getTagTypeValue())
					.build())
				.collect(Collectors.toList());
		}
		return userCategoryTags;
	}

	private List<TagInfo> getUserOtherCategoryTags(final List<UserTagData> userTagDataList, final String categoryId) {
		List<TagInfo> userOtherCategoryTags = new ArrayList<>();
		if (!CollectionUtils.isEmpty(userTagDataList)) {
			userOtherCategoryTags = userTagDataList.stream()
				.filter(userTagData -> userTagData.getCategoryId() == null
						|| !userTagData.getCategoryId().contains(categoryId))
				.sorted(Comparator.comparing(UserTagData::getUpdatedTimeStamp).reversed())
				.map(userTagData -> TagInfo.builder()
					.tag(userTagData.getTag())
					.count(String.valueOf(userTagData.getCount()))
					.tagType(TagType.OTHER_CATEGORY_USER_TAGS.getTagTypeValue())
					.build())
				.collect(Collectors.toList());
		}
		return userOtherCategoryTags;
	}

	private List<TagInfo> getTxnTags(final List<UserTagData> userTagDataList, final List<TagsData> currentTxnTagsList) {
		List<TagInfo> txnTags = new ArrayList<>();
		if (!CollectionUtils.isEmpty(currentTxnTagsList)) {
			for (TagsData tagsData : currentTxnTagsList) {
				UserTagData userTagData = getUserTagData(userTagDataList, tagsData.getTagName());
				if (Objects.nonNull(userTagData)) {
					TagInfo tagInfo = TagInfo.builder()
						.tag(tagsData.getTagName())
						.count(String.valueOf(userTagData.getCount()))
						.tagType(TagType.USER_TXN_TAGS.getTagTypeValue())
						.build();
					txnTags.add(tagInfo);
				}
				else {
					TagInfo tagInfo = TagInfo.builder()
						.tag(tagsData.getTagName())
						.count("1")
						.tagType(TagType.USER_TXN_TAGS.getTagTypeValue())
						.build();
					txnTags.add(tagInfo);
				}
			}
		}
		return txnTags;
	}

	private UserTagData getUserTagData(final List<UserTagData> userTagDataList, final String tag) {
		if (!CollectionUtils.isEmpty(userTagDataList)) {
			for (UserTagData userTagData : userTagDataList) {
				if (StringUtils.equals(userTagData.getTag(), tag)) {
					return userTagData;
				}
			}
		}
		return null;
	}

	private TransformedTransactionHistoryDetail fetchTxn(final GetTagRequest request) {
		if (StringUtils.isNotBlank(request.getRequestBody().getStreamSource())
				&& StringUtils.isNotBlank(request.getRequestBody().getTxnDate())
				&& StringUtils.isNotBlank(request.getRequestBody().getTxnId())) {

			SearchContext searchContext = TagSearchContext.getSearchContext(request.getUserId(),
					request.getRequestBody().getTxnId(), request.getRequestBody().getStreamSource());

			TransformedTransactionHistoryDetail txn = esDao.getTxnFromEs(searchContext,
					Utility.getEpochTime(request.getRequestBody().getTxnDate()));

			return txn;
		}

		log.error("StreamSource or txnDate or txnId is blank for txnId : {}", request.getRequestBody().getTxnId());
		throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
	}

	private TagDto dedupeAndCreateDto(final List<UserTagData> userTagDataList, final SystemTagData systemTagData,
			final List<TagsData> txnTagsList) {

		// Fetch the user tag limit from the properties file
		userTagLimit = configurablePropertiesHolder.getPropertyWithDefaultValue(USER_TAG_LIMIT, Integer.class,
				userTagLimit);

		List<String> systemTagList = null;
		List<String> userTagList = null;
		List<String> txnTags = null;

		if (Objects.nonNull(systemTagData)) {
			systemTagList = new ArrayList<>(systemTagData.getTags());
		}

		if (!CollectionUtils.isEmpty(userTagDataList)) {
			userTagList = userTagDataList.stream()
				.sorted(Comparator.comparing(UserTagData::getUpdatedTimeStamp).reversed())
				.map(UserTagData::getTag)
				.limit(userTagLimit)
				.collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(systemTagList)) {
				systemTagList.removeAll(new HashSet<>(userTagList));
			}
		}

		if (!CollectionUtils.isEmpty(txnTagsList)) {
			txnTags = txnTagsList.stream().map(TagsData::getTagName).collect(Collectors.toList());

			if (!CollectionUtils.isEmpty(userTagList)) {
				userTagList.removeAll(txnTags);
			}
		}

		return TagDto.builder().txnTags(txnTags).systemTags(systemTagList).userTags(userTagList).build();
	}

	private GetTagsResponse buildGetTagsResponse(final TagDto tagDto) {
		Map<String, List<TagInfo>> tags = new HashMap<>();

		tags.put(TXN_TAGS, null);
		tags.put(USER_TAGS, null);
		tags.put(SYSTEM_TAGS, null);

		if (!CollectionUtils.isEmpty(tagDto.getTxnTags())) {
			List<TagInfo> txnTagsList = new ArrayList<>();
			tags.put(TXN_TAGS, txnTagsList);

			tagDto.getTxnTags().forEach(tag -> {
				txnTagsList.add(TagInfo.builder().tag(tag).used(true).build());
			});
		}

		if (!CollectionUtils.isEmpty(tagDto.getUserTags())) {
			List<TagInfo> userTagsList = new ArrayList<>();
			tags.put(USER_TAGS, userTagsList);

			tagDto.getUserTags().forEach(tag -> {
				userTagsList.add(TagInfo.builder().tag(tag).used(false).build());
			});
		}

		if (!CollectionUtils.isEmpty(tagDto.getSystemTags())) {
			List<TagInfo> systemTagsList = new ArrayList<>();
			tags.put(SYSTEM_TAGS, systemTagsList);

			tagDto.getSystemTags().forEach(tag -> {
				systemTagsList.add(TagInfo.builder().tag(tag).used(false).build());
			});
		}

		List<String> sequence = new ArrayList<>();
		sequence.add(TXN_TAGS);
		sequence.add(USER_TAGS);
		sequence.add(SYSTEM_TAGS);

		// Fetch the configurable properties of tag feature
		maxTagCount = configurablePropertiesHolder.getPropertyWithDefaultValue(MAX_TAG_COUNT, Integer.class,
				maxTagCount);
		tagMaxLength = configurablePropertiesHolder.getPropertyWithDefaultValue(TAG_MAX_LENGTH, Integer.class,
				tagMaxLength);
		tagMinLength = configurablePropertiesHolder.getPropertyWithDefaultValue(TAG_MIN_LENGTH, Integer.class,
				tagMinLength);

		return GetTagsResponse.builder()
			.maxTagCount(maxTagCount)
			.maxTagLength(tagMaxLength)
			.minTagLength(tagMinLength)
			.sequence(sequence)
			.tags(tags)
			.build();
	}

	@Override
	public ParentResponse tag(final TagsRequest tagsRequest) {
		try {
			this.tagServiceInternal(tagsRequest);
			return ParentResponse.builder().status(SUCCESS).httpCode(OK_200).message(TAG_SUCCESSFULLY_SAVED).build();

		}
		catch (Exception e) {
			log.error("Exception while add or delete a tag, Excepetion : {}", CommonsUtility.exceptionFormatter(e));
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, TAGS_INTERNAL_SERVER_ERROR,
					TAG_SERVICE_FUNC);
		}
	}

	private void tagServiceInternal(final TagsRequest request) {
		List<String> requestTags = request.getTagRequestBody().getTags();

		// Fetch max tag count from properties
		maxTagCount = configurablePropertiesHolder.getPropertyWithDefaultValue(MAX_TAG_COUNT, Integer.class,
				maxTagCount);

		if (requestTags.size() > maxTagCount) {
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, MAX_NO_OF_TAG_ERROR);
		}

		List<TagsData> tags = null;
		Long txnAmount = null;

		// Creating search context for ES to get txnTags
		SearchContext searchContext = TagSearchContext.getSearchContext(request.getUserId(),
				request.getTagRequestBody().getTxnId(), request.getTagRequestBody().getSourceContext());

		TransformedTransactionHistoryDetail txn = esDao.getStoredDataFromEs(searchContext,
				Utility.getEpochTime(request.getTagRequestBody().getTxnDate()));
		txnAmount = txn.getAmount();
		tags = convertTransformedTagsToTagsData(txn.getTags());

		try {
			final List<String> requestTagsCopy = new ArrayList<>(requestTags);
			List<TagsData> addedTags = new ArrayList<>();
			List<TagsData> deletedTags = new ArrayList<>(tags);

			this.validateTags(requestTags, tags);
			addedTags = requestTags.stream()
				.map(tag -> new TagsData(tag, Instant.now().toEpochMilli(), Instant.now().toEpochMilli()))
				.collect(Collectors.toList());

			deletedTags = deletedTags.stream()
				.filter(tagsData -> !requestTagsCopy.contains(tagsData.getTagName()))
				.map(tagsData -> {
					tagsData.setUpdatedTime(Instant.now().toEpochMilli());
					return tagsData;
				})
				.collect(Collectors.toList());

			List<TagsData> txnTagsList = new ArrayList<>(tags);
			txnTagsList.removeAll(deletedTags);
			txnTagsList.addAll(addedTags);

			this.checkAndUpdateAutoTaggingDetails(request, txnTagsList, addedTags, deletedTags, txn);

			this.buildAndUpdateTxnTags(request, txnTagsList, addedTags, deletedTags, txnAmount);

		}
		catch (Exception e) {
			log.error("Exception while validation or updating tag data for TagRequest : {} " + "with Exception : {}",
					request.getTagRequestBody(), CommonsUtility.exceptionFormatter(e));
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, TAGS_INTERNAL_SERVER_ERROR,
					TAG_SERVICE_INTERNAL_FUNC);
		}

	}

	private void validateTagRequest(final String requestSource, final String streamSourceInDb,
			final String requestTxnDate, final String txnDateInDb) {
		if (!StringUtils.equals(requestSource, streamSourceInDb)) {
			log.error("Stream source : {} found in request but in DB it was : {}", requestSource, streamSourceInDb);
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
		}

		String epochDate = Utility.getEpochTime(requestTxnDate);
		if (!StringUtils.equals(txnDateInDb, epochDate)) {
			log.error("Txn date : {} found in request but in DB it was : {}", epochDate, txnDateInDb);
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
		}
	}

	private void validateTags(final List<String> requestTags, final List<TagsData> tags) {
		if (CollectionUtils.isEmpty(tags) && CollectionUtils.isEmpty(requestTags)) {
			log.error("Request came to delete all tags but no tags found in DB");
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, NO_TAG_FOUND);
		}

		// fetching max tag length from properties
		tagMaxLength = configurablePropertiesHolder.getPropertyWithDefaultValue(TAG_MAX_LENGTH, Integer.class,
				tagMaxLength);
		tagMinLength = configurablePropertiesHolder.getPropertyWithDefaultValue(TAG_MIN_LENGTH, Integer.class,
				tagMinLength);

		for (String tag : requestTags) {
			if (StringUtils.isBlank(tag) || tag.length() > tagMaxLength) {
				log.error("Tag : {} is either blank or longer than max tag length", tag);
				throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
			}

			if (StringUtils.isBlank(tag) || tag.length() < tagMinLength) {
				log.error("Tag : {} is either blank or less than min tag length", tag);
				throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
			}
		}

		List<String> distinctRequestedTags = new ArrayList<>(requestTags);
		distinctRequestedTags = distinctRequestedTags.stream()
			.map(String::toLowerCase)
			.distinct()
			.collect(Collectors.toList());

		if (requestTags.size() != distinctRequestedTags.size()) {
			log.error("Duplicate tags found tags: {}, requestTags: {}", tags, requestTags);
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
		}

		List<String> dbTags = tags.stream().map(TagsData::getTagName).collect(Collectors.toList());
		List<String> duplicateTags = new ArrayList<>();
		for (String tag : requestTags) {
			boolean found = false;
			for (String dbTag : dbTags) {
				if (tag.equalsIgnoreCase(dbTag)) {
					found = true;
					break;
				}
			}
			if (found) {
				duplicateTags.add(tag);
			}
		}
		requestTags.removeAll(duplicateTags);
	}

	private void buildAndUpdateTxnTags(final TagsRequest request, final List<TagsData> tagsList,
			final List<TagsData> addedTags, final List<TagsData> deletedTags, final Long txnAmount) {
		TagRequestBody tagRequestBody = request.getTagRequestBody();

		TxnTags txnTags = TxnTags.builder()
			.txnId(tagRequestBody.getTxnId())
			.txnDate(Utility.getEpochTime(tagRequestBody.getTxnDate()))
			.updatedDate(DateTimeUtility.currentTimeEpoch())
			.amount(txnAmount)
			.streamSource(TransactionSource.getTransactionSourceEnumByName(tagRequestBody.getSourceContext())
				.getTransactionSourceKey())
			.tags(tagsList)
			.userId(request.getUserId())
			.addedTags(addedTags)
			.deletedTags(deletedTags)
			.categoryId(tagRequestBody.getCategoryId())
			.build();
		try {
			// Pushing txnTags to kafka
			kafkaClient.pushIntoKafkaInSync(KafkaClientEnum.TXN_TAG_KAFKA_CLIENT.getKafkaClientName(),
					txnTags.getUserId(), txnTags);

		}
		catch (Exception e) {
			log.error("Exception while pushing txnTags to kafka for txnId : {} with Exception : {}",
					tagRequestBody.getTxnId(), CommonsUtility.exceptionFormatter(e));
			throw ExceptionHandlerUtil.getTaggedException(e, PANAROMA_SERVICE, KAFKA_EXCEPTION, "");
		}
	}

	/**
	 * Checks and updates auto-tagging details for a transaction based on feature flag. If
	 * auto-tagging is enabled, updates the auto-tagging details for the second party with
	 * the current set of tags.
	 * @param request TagsRequest containing transaction and tag information
	 * @param tagsList List of all tags after the update
	 * @param addedTags List of newly added tags
	 * @param deletedTags List of removed tags
	 * @param txn Transaction details from ES
	 * @throws Exception if there's an error while updating auto-tagging details
	 *
	 * see PTH-228 Auto-tagging implementation
	 */
	private void checkAndUpdateAutoTaggingDetails(final TagsRequest request, final List<TagsData> tagsList,
			final List<TagsData> addedTags, final List<TagsData> deletedTags,
			final TransformedTransactionHistoryDetail txn) throws Exception {

		boolean autoTaggingEnabledForThisUser = rolloutStrategyHelper.isUserWhiteListed(AUTO_TAGGING_FEATURE_ROLLOUT,
				txn.getEntityId());

		// Auto tagging is enabled or not
		if (!autoTaggingEnabledForThisUser) {
			log.info("Auto Tagging feature is disabled for this user for txnId: {}, userId: {}",
					request.getTagRequestBody().getTxnId(), txn.getEntityId());
			return;
		}

		// Auto Tagging enabled is given or not for the request autoTagging - 0: false, 1:
		// true
		if (request != null && request.getTagRequestBody().getAutoTaggingEnabled() == 0) {
			log.info(FUTURE_LOG_REMOVER_IDENTIFIER + "Auto Tagging enabled flag is false for txnId: {}",
					request.getTagRequestBody().getTxnId());
			return;
		}

		// If no tags are being added or deleted, then return
		if ((CollectionUtils.isEmpty(addedTags) && CollectionUtils.isEmpty(deletedTags))
				|| CollectionUtils.isEmpty(tagsList)) {
			log.info("No Tags are being added or deleted txnId: {}", request.getTagRequestBody().getTxnId());
			return;
		}

		String userId = txn.getEntityId();
		String secPartyId = AutoTaggingUtility.getSecPartyIdForAutoTagging(txn);
		Integer secPartyType = AutoTaggingUtility.getSecPartyTypeKeyForAutoTagging(txn);

		log.info("Auto Tagging details is being updated for userId: {}, secPartyId: {}, secPartyType: {}, tags: {}",
				userId, secPartyId, secPartyType, tagsList);

		// Get the auto tagging details for the user and sec party
		AutoTaggingDetails autoTaggingDetails = tagDao.getAutoTaggingDetails(userId, secPartyId, secPartyType);
		Set<String> tags = tagsList.stream().map(TagsData::getTagName).collect(Collectors.toSet());

		// If no auto tagging details found, then create a new one
		if (Objects.isNull(autoTaggingDetails)) {
			autoTaggingDetails = AutoTaggingDetails.builder()
				.userId(userId)
				.secPartyId(secPartyId)
				.secPartyType(secPartyType)
				.tags(tags)
				.build();
		}
		else {
			// If auto tagging details found, then update the tags
			autoTaggingDetails.setTags(tags);
		}

		// Update the auto tagging details in the database
		tagDao.updateAutoTaggingDetails(userId, secPartyId, autoTaggingDetails);

		log.info("Auto Tagging details is updated for userId: {}, autoTaggingDetails: {}", userId, autoTaggingDetails);
	}

	private List<TagsData> convertTransformedTagsToTagsData(final List<TransformedTag> esTagList) {
		List<TagsData> tagsDataList = new ArrayList<TagsData>();
		if (Objects.isNull(esTagList)) {
			return tagsDataList;
		}
		for (TransformedTag transformedTag : esTagList) {
			tagsDataList.add(TagsData.builder()
				.tagName(transformedTag.getTag())
				.updatedTime(transformedTag.getCreatedDate())
				.createdTime(transformedTag.getCreatedDate())
				.build());
		}
		return tagsDataList;
	}

	/**
	 * Retrieves the user tags summary for a given user and month.
	 * @param userTagsSummaryRequest the request containing user ID and month for which
	 * the summary is to be fetched
	 * @return ParentResponse containing the user tags summary response
	 * @throws Exception if there is an error during the process
	 */
	@Override
	public ParentResponse getUserTagsSummary(UserTagsSummaryRequest userTagsSummaryRequest) {

		// Validating the user tags summary request
		validateUserTagSummaryRequest(userTagsSummaryRequest);

		// getting search context for user tags summary
		SearchContext searchContext = TagSearchContext.getSearchContextUserTagsSummary(userTagsSummaryRequest);

		// Fetch the user tags summary from the DAO
		UserTagsSummaryResponse userTagsSummaryResponse = tagDao.getUserTagsSummary(userTagsSummaryRequest.getUserId(),
				userTagsSummaryRequest.getMonth(), searchContext);

		// Sort the list of tags
		if (userTagsSummaryResponse != null && userTagsSummaryResponse.getUserTagsSummary() != null) {
			Collections.sort(userTagsSummaryResponse.getUserTagsSummary());
		}

		// Setting the page number in the response
		userTagsSummaryResponse.setPageNo(userTagsSummaryRequest.getPageNo());

		// Build and return the response
		ParentResponse response = ParentResponse.builder()
			.status(SUCCESS)
			.message(SUCCESS)
			.httpCode(OK_200)
			.response(userTagsSummaryResponse)
			.build();

		return response;
	}

	@Override
	public ParentResponse getTagSummary(TagSummaryRequest tagSummaryRequest) {
		// validate tag summary request
		validateTagSummaryRequest(tagSummaryRequest);

		// get searchContext for tag Summary
		SearchContext searchContext = TagSearchContext.getSearchContextForTagSummary(tagSummaryRequest);

		// fetch tag summary (SPENT/RECEIVED Response) from DAO
		TagSummaryResponse tagSummaryResponse = tagDao.getTagSummary(tagSummaryRequest.getUserId(),
				tagSummaryRequest.getMonth(), tagSummaryRequest.getTag(), searchContext);
		ParentResponse response = ParentResponse.builder()
			.status(SUCCESS)
			.message(SUCCESS)
			.httpCode(OK_200)
			.response(tagSummaryResponse)
			.retryable(Boolean.FALSE)
			.build();
		return response;
	}

	private void validateTagSummaryRequest(final TagSummaryRequest tagSummaryRequest) {

		// Retrieve the default value for tags summary from date in days from the
		// configuration
		Integer userTagsSummaryFromDateInDays = configurablePropertiesHolder
			.getPropertyWithDefaultValue(USER_TAGS_SUMMARY_FROM_DATE_IN_DAYS, Integer.class, 180);
		// Calculate the epoch milliseconds for the first date of the month based on the
		// retrieved days
		long userTagsSummaryFromDateEpochMillis = DateTimeUtility
			.getEpochMillisOfFirstDateOfMonth(userTagsSummaryFromDateInDays);
		// Check if the month in the request is within the supported range
		if (DateTimeUtility.getEpochMillis(tagSummaryRequest.getMonth(),
				MONTH_YEAR_FORMAT) < userTagsSummaryFromDateEpochMillis) {
			log.error("Month : {} is smaller than tag supported range, supported Range: {}",
					tagSummaryRequest.getMonth(), userTagsSummaryFromDateEpochMillis);
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
		}
	}

	private void validateUserTagSummaryRequest(final UserTagsSummaryRequest userTagsSummaryRequest) {
		// Check if the user ID or month is blank in the request
		if (StringUtils.isBlank(userTagsSummaryRequest.getUserId())
				|| StringUtils.isBlank(userTagsSummaryRequest.getMonth())) {
			log.error("UserId or Month is blank in UserTagsSummaryRequest : {}", userTagsSummaryRequest);
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
		}

		// Retrieve the default value for user tags summary from date in days from the
		// configuration
		Integer userTagsSummaryFromDateInDays = configurablePropertiesHolder
			.getPropertyWithDefaultValue(USER_TAGS_SUMMARY_FROM_DATE_IN_DAYS, Integer.class, 180);
		// Calculate the epoch milliseconds for the first date of the month based on the
		// retrieved days
		long userTagsSummaryFromDateEpochMillis = DateTimeUtility
			.getEpochMillisOfFirstDateOfMonth(userTagsSummaryFromDateInDays);

		// Check if the month in the request is within the supported range
		if (DateTimeUtility.getEpochMillis(userTagsSummaryRequest.getMonth(),
				MONTH_YEAR_FORMAT) < userTagsSummaryFromDateEpochMillis) {
			log.error("Month : {} is smaller than tag supported range, supported Range: {}",
					userTagsSummaryRequest.getMonth(), userTagsSummaryFromDateEpochMillis);
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, TAGS_INVALID_PARAMETER);
		}
	}

}