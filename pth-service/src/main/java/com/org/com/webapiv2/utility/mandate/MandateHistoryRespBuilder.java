package com.org.com.webapiv2.utility.mandate;

import com.org.com.webapiv2.dto.mandate.MandateHistoryExtResp;
import com.org.com.webapiv2.dto.mandate.MandateHistoryExtRespNode;
import com.org.com.webapiv2.dto.mandate.MandateHistoryIntResp;
import com.org.com.webapiv2.dto.mandate.MandateHistoryIntRespNode;
import com.org.com.webapiv2.dto.mandate.MandateHistoryUpiApiResponse;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.commons.dto.mandate.MandateInfoData;
import com.org.panaroma.commons.enums.MandateActionEnum;
import com.org.panaroma.commons.enums.MandateTypeEnum;
import com.org.panaroma.commons.utils.LogoUtility;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.ObjectUtils;

public class MandateHistoryRespBuilder {

	public static MandateHistoryIntResp createMandateHistoryIntResp(final MandateInfoData mandateDataParent,
			final List<MandateActivityData> mandateDataChild) {

		MandateHistoryIntResp mandateHistoryIntResp = new MandateHistoryIntResp();
		List<MandateHistoryIntRespNode> mandateHistoryIntRespNodeList = new ArrayList<>();

		if (Objects.isNull(mandateDataParent) || ObjectUtils.isEmpty(mandateDataChild)) {
			mandateHistoryIntResp.setCreateEntryPresent(false);
			mandateHistoryIntResp.setTimeline(mandateHistoryIntRespNodeList);
			return mandateHistoryIntResp;
		}

		for (MandateActivityData data : mandateDataChild) {
			MandateHistoryIntRespNode mandateHistoryIntRespNode = createMandateHistoryIntRespNode(data,
					mandateDataParent);
			mandateHistoryIntRespNodeList.add(mandateHistoryIntRespNode);
			// Only CREATE events are treated as create entries for history display
			// IN_PORT events are excluded because there could be older actions in Paytm
			// ecosystem
			if (MandateActionEnum.CREATE.getMandateActionKey().equals(data.getAction())) {
				mandateHistoryIntResp.setCreateEntryPresent(true);
			}
		}
		mandateHistoryIntResp.setTimeline(mandateHistoryIntRespNodeList);
		return mandateHistoryIntResp;
	}

	public static MandateHistoryExtResp createMandateHistoryExtResp(final MandateHistoryIntResp mandateHistoryIntResp) {
		MandateHistoryExtResp mandateHistoryExtResp = new MandateHistoryExtResp();
		List<MandateHistoryExtRespNode> mandateHistoryExtRespNodeList = new ArrayList<>();

		for (MandateHistoryIntRespNode node : mandateHistoryIntResp.getTimeline()) {
			MandateHistoryExtRespNode mandateHistoryExtRespNode = createMandateHistoryExtRespNode(node);
			mandateHistoryExtRespNodeList.add(mandateHistoryExtRespNode);
		}

		mandateHistoryExtResp.setTimeline(mandateHistoryExtRespNodeList);
		return mandateHistoryExtResp;
	}

	public static MandateHistoryExtResp createMandateHistoryExtResp(final MandateHistoryIntResp mandateHistoryIntResp,
			PaginationParams paginationParams) {
		MandateHistoryExtResp mandateHistoryExtResp = createMandateHistoryExtResp(mandateHistoryIntResp);
		mandateHistoryExtResp.setPaginationParams(paginationParams);
		return mandateHistoryExtResp;
	}

	private static MandateHistoryExtRespNode createMandateHistoryExtRespNode(
			final MandateHistoryIntRespNode intRespNode) {
		MandateHistoryExtRespNode mandateHistoryExtRespNode = new MandateHistoryExtRespNode();

		mandateHistoryExtRespNode.setMessage(intRespNode.getMessage());
		mandateHistoryExtRespNode.setStatus(intRespNode.getStatus());
		mandateHistoryExtRespNode.setTxnDate(intRespNode.getTxnDate());
		mandateHistoryExtRespNode.setRrn(intRespNode.getRrn());
		mandateHistoryExtRespNode.setTxnId(intRespNode.getTxnId());
		mandateHistoryExtRespNode.setTxnAmount(intRespNode.getTxnAmount());
		mandateHistoryExtRespNode.setMaxDebitAmount(intRespNode.getMaxDebitAmount());
		mandateHistoryExtRespNode.setFrequency(intRespNode.getFrequency());
		mandateHistoryExtRespNode.setUmn(intRespNode.getUmn());
		mandateHistoryExtRespNode.setValidityStartDate(intRespNode.getValidityStartDate());
		mandateHistoryExtRespNode.setValidityEndDate(intRespNode.getValidityEndDate());
		mandateHistoryExtRespNode.setPayerBank(intRespNode.getPayerBank());
		mandateHistoryExtRespNode.setPayerBankAccount(intRespNode.getPayerBankAccount());
		mandateHistoryExtRespNode.setPayeeVpa(intRespNode.getPayeeVpa());
		mandateHistoryExtRespNode.setPayeeName(intRespNode.getPayeeName());
		mandateHistoryExtRespNode.setTitle(intRespNode.getTitle());
		mandateHistoryExtRespNode.setInfoLine(intRespNode.getInfoLine());
		mandateHistoryExtRespNode.setMandateType(intRespNode.getMandateType());
		mandateHistoryExtRespNode.setPayeeLogo(intRespNode.getPayeeLogo());
		mandateHistoryExtRespNode.setPayerBankLogo(intRespNode.getPayerBankLogo());

		return mandateHistoryExtRespNode;
	}

	private static MandateHistoryIntRespNode createMandateHistoryIntRespNode(final MandateActivityData child,
			final MandateInfoData parent) {

		MandateHistoryIntRespNode mandateHistoryIntRespNode = new MandateHistoryIntRespNode();

		mandateHistoryIntRespNode.setMessage(MandateHistoryMessageHelper.getMandateHistoryCustomMsg(parent, child));
		mandateHistoryIntRespNode.setStatus(ClientStatusEnum.getStatusEnumByKey(child.getViewStatus()).name());
		mandateHistoryIntRespNode.setTxnDate(child.getTxnDate());
		mandateHistoryIntRespNode.setRrn(child.getRrn());
		mandateHistoryIntRespNode.setTxnId(child.getTxnId());
		/*
		 * For old non-financial recurring docs child.amount field had mandateAmount & for
		 * newer non-financial recurring docs this field will be null For financial
		 * recurring docs child.amount field will always have the debit amount value
		 */
		mandateHistoryIntRespNode
			.setTxnAmount(Objects.nonNull(child.getAmount()) ? child.getAmount().toString() : null);
		/*
		 * There is a requirement to send mandate amount in txnAmount field for
		 * non-financial recurring txns. For old docs this will be handled by above line &
		 * for new docs the below if condition is added
		 */
		if (Objects.isNull(mandateHistoryIntRespNode.getTxnAmount())) {
			mandateHistoryIntRespNode
				.setTxnAmount(Objects.nonNull(child.getMandateAmount()) ? child.getMandateAmount().toString() : null);
		}
		mandateHistoryIntRespNode.setInfoLine(MandateHistoryMessageHelper.getMandateHistoryInfoLine(child, parent));
		mandateHistoryIntRespNode.setTitle(MandateHistoryMessageHelper.getMandateHistoryTitle(child, parent));
		if (MandateTypeEnum.RECURRING_MANDATE.getMandateTypeKey().equals(parent.getType())) {
			mandateHistoryIntRespNode.setMaxDebitAmount(
					Objects.nonNull(child.getMandateAmount()) ? child.getMandateAmount().toString() : null);
			/*
			 * For old recurring mandate docs child.mandateAmount field didn't exist.
			 * Hence the below if block
			 */
			if (Objects.isNull(mandateHistoryIntRespNode.getMaxDebitAmount())) {
				mandateHistoryIntRespNode
					.setMaxDebitAmount(Objects.nonNull(parent.getAmount()) ? parent.getAmount().toString() : null);
			}
		}

		mandateHistoryIntRespNode.setMandateType(MandateTypeEnum.getMandateTypeView(parent.getType()));
		mandateHistoryIntRespNode.setUmn(parent.getUmn());
		if (Objects.nonNull(child.getActionMetaData())
				&& Objects.nonNull(child.getActionMetaData().getValidityEndDate())) {
			mandateHistoryIntRespNode.setValidityEndDate(child.getActionMetaData().getValidityEndDate());
		}
		else if (Objects.nonNull(parent.getMandateMetaData())
				&& Objects.nonNull(parent.getMandateMetaData().getValidityEndDate())) {
			mandateHistoryIntRespNode.setValidityEndDate(parent.getMandateMetaData().getValidityEndDate());
		}
		if (Objects.nonNull(parent.getMandateMetaData())) {
			mandateHistoryIntRespNode.setFrequency(parent.getMandateMetaData().getFrequency());
			mandateHistoryIntRespNode.setValidityStartDate(parent.getMandateMetaData().getValidityStartDate());
		}
		if (Objects.nonNull(parent.getPayerData())) {
			if (Objects.nonNull(parent.getPayerData().getAccountData())) {
				mandateHistoryIntRespNode.setPayerBank(parent.getPayerData().getAccountData().getBankName());
				mandateHistoryIntRespNode.setPayerBankAccount(parent.getPayerData().getAccountData().getAccNum());
				mandateHistoryIntRespNode
					.setPayerBankLogo(LogoUtility.getBankLogo(parent.getPayerData().getAccountData().getIfsc(),
							parent.getPayerData().getAccountData().getBankName()));
			}
		}

		if (Objects.nonNull(parent.getPayeeData())) {
			mandateHistoryIntRespNode.setPayeeName(parent.getPayeeData().getName());
			if (Objects.nonNull(parent.getPayeeData().getAccountData())) {
				mandateHistoryIntRespNode.setPayeeVpa(parent.getPayeeData().getAccountData().getVpa());
				mandateHistoryIntRespNode.setPayeeLogo(
						LogoUtility.mandateHistoryMerchantVpaLogo(parent.getPayeeData().getAccountData().getVpa()));
			}
		}

		return mandateHistoryIntRespNode;
	}

	public static void createMandateHistoryCombineResponse(final MandateHistoryIntResp mandateHistoryIntResp,
			final MandateHistoryUpiApiResponse upiResp) {

		if (ObjectUtils.isEmpty(mandateHistoryIntResp.getTimeline()) && Objects.nonNull(upiResp)) {
			mandateHistoryIntResp.setTimeline(upiResp.getTimeline());
		}
		else if (ObjectUtils.isNotEmpty(mandateHistoryIntResp.getTimeline()) && Objects.nonNull(upiResp)
				&& ObjectUtils.isNotEmpty(upiResp.getTimeline())) {
			mandateHistoryIntResp.setTimeline(processCombineResponse(mandateHistoryIntResp, upiResp));
		}
	}

	private static List<MandateHistoryIntRespNode> processCombineResponse(
			final MandateHistoryIntResp mandateHistoryIntResp, final MandateHistoryUpiApiResponse upiResp) {
		// Combine both lists
		List<MandateHistoryIntRespNode> combinedTimeline = new ArrayList<>();
		// Set to track txnIds that have been added
		Set<String> seenTxnIds = new HashSet<>();

		// Add all elements from mandateHistoryIntResp (more recent transactions)
		for (MandateHistoryIntRespNode node : mandateHistoryIntResp.getTimeline()) {
			if (seenTxnIds.add(node.getTxnId())) {
				combinedTimeline.add(node); // Add the node if txnId is unique
			}
		}

		// Add elements from upiResp (older transactions) only if txnId is not already
		// present
		for (MandateHistoryIntRespNode node : upiResp.getTimeline()) {
			if (seenTxnIds.add(node.getTxnId())) {
				combinedTimeline.add(node); // Add the node if txnId is unique
			}
		}

		return combinedTimeline;
	}

}