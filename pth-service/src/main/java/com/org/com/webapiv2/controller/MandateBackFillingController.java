package com.org.com.webapiv2.controller;

import com.org.com.webapiv2.parentResponse.ParentResponse;
import com.org.panaroma.commons.dto.MandateBackFillingKafkaObject;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.utils.CommonsUtility;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.org.panaroma.commons.constants.CommonConstants.MANDATE_BACKFILLING_KAFKA_CLIENT_NAME;

@RestController
@RequestMapping("/pth/int/v1/mandate-backFill")
@Log4j2
public class MandateBackFillingController {

	private IKafkaClient kafkaClient;

	@Autowired
	public MandateBackFillingController(final IKafkaClient kafkaClient) {

		this.kafkaClient = kafkaClient;
	}

	@PostMapping
	public ParentResponse processMandateBackFilling(@RequestParam("fromDate") final Long fromDate,
			@RequestParam("toDate") final Long toDate, @RequestParam("pageSize") final Integer pageSize) {
		try {
			log.info(
					"Mandate backfilling request received with these params. fromDate : {}, toDate : {}, pageSize : {}",
					fromDate, toDate, pageSize);
			SearchContext searchContext = new SearchContext();
			searchContext.setFromDate(fromDate);
			searchContext.setToDate(toDate);
			searchContext.setPageSize(pageSize);
			MandateBackFillingKafkaObject mandateBackFillingKafkaObject = MandateBackFillingKafkaObject.builder()
				.paginationParams(null)
				.searchContext(searchContext)
				.build();
			kafkaClient.pushIntoKafkaInSync(MANDATE_BACKFILLING_KAFKA_CLIENT_NAME, "MANDATE_BACKFILLING",
					mandateBackFillingKafkaObject);
			return ParentResponse.builder()
				.status("Success")
				.httpCode(200)
				.message("Backfilling process initiated successfully.")
				.build();
		}
		catch (Exception exception) {
			log.error(
					"Exception : {} occurred while processing mandate backfilling request for these params. fromDate : {}, toDate : {}, pageSize : {}.",
					CommonsUtility.exceptionFormatter(exception), fromDate, toDate, pageSize);
		}
		return ParentResponse.builder()
			.status("Failure")
			.message("Failed to initiate backfilling process.")
			.httpCode(200)
			.build();

	}

}
