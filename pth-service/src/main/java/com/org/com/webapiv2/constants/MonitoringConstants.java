package com.org.com.webapiv2.constants;

public class MonitoringConstants {

	public static final String API_HIT_COUNT = "API_HIT_COUNT";

	public static final String UNIFIED_TXN_HISTORY_APP = "application:UnifiedTransactionHistory";

	public static final String UNIFIED_TRANSACTION_HISTORY_API = "UNIFIED_TRANSACTION_HISTORY_API";

	public static final String API_EXECUTION_TIME = "API_EXECUTION_TIME";

	public static final String API_NAME = "api_name";

	public static final String CLIENT = "client";

	public static final String INVALID_CLIENT = "INVALID_CLIENT";

	public static final String API_STATUS_COUNT = "API_STATUS_COUNT";

	public static final String API_RESPONSE_CODE_COUNT = "API_RESPONSE_CODE_COUNT";

	public static final String RATE_LIMIT_COUNT = "RATE_LIMIT_COUNT";

	public static final String SOURCE = "source";

	public static final String STATUS = "status";

	public static final String RESPONSE_CODE = "responseCode";

	public static final String EXP_MESSAGE = "expMessage:";

	public static final String EXP_CLASS = "expClass:";

	public static final String CONTAINER_HOST = "containerHost";

	public static final String COLON = ":";

	public static final String COMMA = ",";

	public static final String ROUTING = "routing";

	public static final String TXN_TYPE = "txnType";

	public static final String ADAPTOR_EVENTS_DELAY = "ADAPTOR_EVENTS_DELAY";

	public static final String IS_BACKFILL = "isBackfill";

	public static final String KAFKA_SINK_TIME = "KAFKA_SINK_TIME";

	public static final String HTTP_CODE_STR = "httpCodeStr";

	public static final String ES_REST_CLIENT_TAGS = "ES_REST_CLIENT_TAGS";

	public static final String ES_REST_CLIENT_UPI_TXNLIST = "ES_REST_CLIENT_UPI_TXNLIST";

	public static final String ES_REST_CLIENT_UPI_CST_PANEL = "ES_REST_CLIENT_UPI_CST_PANEL";

	public static final String RATE_LIMIT_ERROR_COUNT = "RATE_LIMIT_ERROR_COUNT";

	public static final String LATEST_READ_CACHE_HIT_COUNT = "LatestTxn_Read_Cache_Hit_Count";

	public static final String LATEST_WRITE_CACHE_HIT_COUNT = "LatestTxn_Write_Cache_Hit_Count";

	public static final String USERID = "userId";

	public static final String REDIRECTION = "REDIRECTION";

	public static final String LATEST_ES_HIT_COUNT = "LatestTxn_Es_Hit_Count";

	public static final String MANDATE_HISTORY_UPI_API_HIT = "MANDATE_HISTORY_UPI_API_HIT";

	public static final String MANDATE_HISTORY_EXCEPTION = "MANDATE_HISTORY_EXCEPTION";

	public static final String MANDATE_HISTORY_UPI_API_HIT_EXCEPTION = "MANDATE_HISTORY_UPI_API_HIT_EXCEPTION";

	public static final String INDEX_AVAILABILITY_VERIFICATION_COUNT = "INDEX_AVAILABILITY_VERIFICATION_COUNT";

	public static final String RESULT = "result";

	public static final String ES_CLUSTER = "es_cluster";

	public static final String INDEX = "index";

	public static class ConfigurablePropertiesConstants {

		public static final String BMS_API_TIME = "BMS_API_TIME";

	}

	public enum Traffic {

		Allowed, Blocked

	}

}
