package com.org.com.webapiv2.service.impl.metaData;

import com.google.gson.Gson;
import com.org.com.webapiv2.dto.metaData.MetaDataRequest;
import com.org.com.webapiv2.dto.metaData.MetaDataResponse;
import com.org.com.webapiv2.dto.metaData.v4.MetaDataResponseV4;
import com.org.com.webapiv2.service.metaData.IMetaDataService;
import com.org.com.webapiv2.utility.MetaDataUtility;
import com.org.panaroma.commons.exceptionhandler.ExceptionHandlerUtil;
import com.org.panaroma.commons.exceptionhandler.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.commons.utils.PthVersionUtility;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.org.panaroma.commons.constants.WebConstants.PTH_VERSION;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.MAPPER_EXCEPTION;

@Component
@Log4j2
public class MetaDataServiceV4 implements IMetaDataService {

	private final MetaDataServiceUnified metaDataServiceUnified;

	private final MetaDataUtility metaDataUtility;

	private final Gson gson;

	@Autowired
	public MetaDataServiceV4(final MetaDataServiceUnified metaDataServiceUnified, final MetaDataUtility metaDataUtility,
			final Gson gson) {
		this.metaDataServiceUnified = metaDataServiceUnified;
		this.metaDataUtility = metaDataUtility;
		this.gson = gson;
	}

	@Override
	public MetaDataResponse getMetaData(final MetaDataRequest request, final Map<String, String> tokenMap) {
		try {

			// pth_version less than 6 is not valid for this api.
			if (!PthVersionUtility.isRequestValidForListingV4()) {
				log.info("Invalid pth_version for metaData api, pth_version: {}, request: {}",
						MdcUtility.getConstantValue(PTH_VERSION), request);
				throw ExceptionFactory.getException(PANAROMA_SERVICE, MAPPER_EXCEPTION);
			}

			// Get the complete response from the unified service
			MetaDataResponse metaDataResponse = metaDataServiceUnified.getMetaData(request, tokenMap);

			// Convert to V4 response
			MetaDataResponseV4 responseV4 = gson.fromJson(gson.toJson(metaDataResponse), MetaDataResponseV4.class);

			// Override only the V4 specific fields
			responseV4.setRoutingInfo(metaDataUtility.getRoutingInfoV4());
			responseV4.setApiUrlsInfo(metaDataUtility.getApiUrlsInfoV4());

			return responseV4;
		}
		catch (Exception e) {
			if (!(e instanceof PanaromaException)) {
				log.error("Unhandled exception while creating meta data response V4 : {}",
						CommonsUtility.exceptionFormatter(e));
			}
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, INTERNAL_SERVER_ERROR, "");
		}
	}

}
