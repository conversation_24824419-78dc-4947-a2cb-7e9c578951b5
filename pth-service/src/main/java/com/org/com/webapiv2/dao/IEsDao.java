package com.org.com.webapiv2.dao;

import com.org.com.webapiv2.dto.tags.TagSummaryResponse;
import com.org.com.webapiv2.dto.tags.UserTagsSummaryResponse;
import com.org.com.webapiv2.dto.toggleVisibility.ToggleVisibilityRequest;
import com.org.com.webapiv2.dto.upiSearch.EsRepoResponseDto;
import com.org.com.webapiv2.model.UserTagData;
import com.org.com.webapiv2.utility.LogExectutionTime;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.bankPassbook.DetailRequest;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface IEsDao {

	@LogExectutionTime
	TransformedTransactionHistoryDetail getTxnFromEs(SearchContext searchContext, String txnDate);

	TransformedTransactionHistoryDetail getDataUsingDocId(final String docId, final String entityId,
			final String txnDate);

	TransformedTransactionHistoryDetail getStoredDataFromEs(SearchContext searchContext, String txnDate);

	EsRepoResponseDto getResponseTxn(SearchContext searchContext, PaginationParams params) throws ParseException;

	TransformedTransactionHistoryDetail getDetails(DetailRequest detailRequest);

	EsRepoResponseDto getUpiCstPanelResponse(SearchContext searchContext, PaginationParams params,
			Map<String, String> metaInfo);

	TransformedTransactionHistoryDetail getLatestTxn(SearchContext searchContext);

	TransformedTransactionHistoryDetail getAnyTxn(SearchContext searchContext);

	TransformedTransactionHistoryDetail fetchDataFromEs(ToggleVisibilityRequest toggleVisibilityRequest);

	List<UserTagData> getUserTagData(final String userId, final SearchContext searchContext);

	UserTagsSummaryResponse getUserTagsSummary(String userId, String month, SearchContext searchContext);

	TagSummaryResponse getTagSummary(String userId, String month, String tag, SearchContext searchContext);

}
