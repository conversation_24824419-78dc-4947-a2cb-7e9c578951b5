package com.org.com.webapiv2.service.impl.statement;

import static com.org.panaroma.commons.constants.WebConstants.DEFAULT_BANK;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.HYPHEN;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.UPI_LITE;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.UPI_LITE_LOGO;
import static com.org.panaroma.commons.enums.LogoType.UPI_LITE_ICON;

import com.org.com.webapiv2.dto.statement.StatementTxnDetail;
import com.org.panaroma.commons.dto.es.TransformedBankData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.IfscUtility;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

public class StatementResponseAccountDetailsSetter {

	public static void setBankData(final TransformedTransactionHistoryDetail tthd,
			final StatementTxnDetail statementTxnDetail) {
		if (UpiLiteUtility.isTxnUsingUpiLiteInstrumentForStmt(tthd)) {
			setPaymentInstrumentDataForLiteTxns(tthd, statementTxnDetail);
			return;
		}

		if (Utility.isUpiViaCcTxn(tthd)) {
			setBankDataForUpiCc(tthd, statementTxnDetail);
			return;
		}

		List<TransformedParticipant> selfParticipants = getSelfParticiapnt(tthd);
		for (TransformedParticipant selfParticipant : selfParticipants) {
			if (ObjectUtils.isNotEmpty(selfParticipant.getBankData())) {
				TransformedBankData userBankData = selfParticipant.getBankData();
				String bankName = StringUtils.isNotBlank(userBankData.getBankName()) ? userBankData.getBankName()
						: IfscUtility.getBankName(userBankData.getIfsc());
				if (StringUtils.isNotBlank(userBankData.getAccNumber())) {
					String accNum = SPACE + HYPHEN + SPACE + StringUtils.right(userBankData.getAccNumber(), 4);
					// this selfBankDetails field is deprecated and will be removed in
					// future
					statementTxnDetail.getSelfInstrumentDetails().setBankDetails(bankName + accNum);// this
				}
				else {
					statementTxnDetail.getSelfInstrumentDetails().setBankDetails(bankName);
					// this selfBankDetails field is deprecated and will be removed in
					// future
				}
				statementTxnDetail.getSelfInstrumentDetails().setBankName(bankName);
				statementTxnDetail.getSelfInstrumentDetails().setBankAccountNumber(userBankData.getAccNumber());
				statementTxnDetail.getSelfInstrumentDetails()
					.setBankLogo(LogoUtility.getBankLogo(userBankData.getIfsc(), bankName));
				return;
			}
		}

		// this selfBankDetails field is deprecated and will be removed in future
		statementTxnDetail.getSelfInstrumentDetails().setBankDetails(DEFAULT_BANK_NAME);
		statementTxnDetail.getSelfInstrumentDetails().setBankName(DEFAULT_BANK_NAME);
		statementTxnDetail.getSelfInstrumentDetails().setBankLogo(LogoUtility.getBankLogo(null, DEFAULT_BANK));
	}

	private static void setPaymentInstrumentDataForLiteTxns(final TransformedTransactionHistoryDetail tthd,
			final StatementTxnDetail statementTxnDetail) {
		// this selfBankDetails field is deprecated and will be removed in future
		statementTxnDetail.getSelfInstrumentDetails().setBankDetails(UPI_LITE);
		statementTxnDetail.getSelfInstrumentDetails().setBankName(UPI_LITE);
		statementTxnDetail.getSelfInstrumentDetails().setBankLogo(LogoUtility.getLogo(UPI_LITE_LOGO, UPI_LITE_ICON));
	}

	private static void setBankDataForUpiCc(final TransformedTransactionHistoryDetail tthd,
			final StatementTxnDetail statementTxnDetail) {
		List<TransformedParticipant> selfParticipants = getSelfParticiapnt(tthd);
		for (TransformedParticipant selfParticipant : selfParticipants) {
			if (ObjectUtils.isNotEmpty(selfParticipant.getBankData())) {
				TransformedBankData payeeBankData = selfParticipant.getBankData();
				String bankName = StringUtils.isNotBlank(payeeBankData.getBankName()) ? payeeBankData.getBankName()
						: IfscUtility.getBankName(payeeBankData.getIfsc());
				String cardNum = ObjectUtils.isNotEmpty(selfParticipant.getCardData())
						? selfParticipant.getCardData().getCardNum() : "";
				String bankDetail = StringUtils.isNotBlank(cardNum)
						? bankName + SPACE + HYPHEN + SPACE + cardNum.substring(cardNum.length() - 2) : bankName;
				// this selfBankDetails field is deprecated and will be removed in future
				statementTxnDetail.getSelfInstrumentDetails().setBankDetails(bankDetail);
				statementTxnDetail.getSelfInstrumentDetails().setBankName(bankName);
				statementTxnDetail.getSelfInstrumentDetails().setBankAccountNumber(cardNum);

				// Add null check for cardData and cardNetwork
				String cardLogo = null;
				if (ObjectUtils.isNotEmpty(selfParticipant.getCardData())
						&& StringUtils.isNotBlank(selfParticipant.getCardData().getCardNetwork())) {
					cardLogo = LogoUtility.getCardLogo(selfParticipant.getCardData().getCardNetwork());
				}
				statementTxnDetail.getSelfInstrumentDetails().setBankLogo(cardLogo);
				return;
			}
		}
	}

	private static List<TransformedParticipant> getSelfParticiapnt(final TransformedTransactionHistoryDetail tthd) {
		return tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId().equals(participant.getEntityId()))
			.collect(Collectors.toList());
	}

}
