package com.org.com.webapiv2.scheduler;

import com.org.com.webapiv2.scheduler.helper.IndexAvailabilityCheckerSchedulerHelper;
import com.org.com.webapiv2.scheduler.service.SchedulerService;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

import static com.org.com.webapiv2.constants.SchedulerConstants.SchedulerType.INDEX_AVAILABILITY_CHECKER_SCHEDULER;
import static com.org.panaroma.commons.constants.WebConstants.REQUEST_ID;

@Component
@Log4j2
public class IndexAvailabilityCheckerScheduler implements SchedulerService {

	private final IndexAvailabilityCheckerSchedulerHelper indexAvailabilityCheckerSchedulerHelper;

	@Autowired
	IndexAvailabilityCheckerScheduler(
			final IndexAvailabilityCheckerSchedulerHelper indexAvailabilityCheckerSchedulerHelper) {
		this.indexAvailabilityCheckerSchedulerHelper = indexAvailabilityCheckerSchedulerHelper;
	}

	@Override
	public void scheduledTrigger() {
		ThreadContext.put(REQUEST_ID, UUID.randomUUID().toString());
		log.info("Running index availability scheduler");
		indexAvailabilityCheckerSchedulerHelper.checkIndexAvailability();
	}

	@Override
	public String getSchedulerType() {
		return INDEX_AVAILABILITY_CHECKER_SCHEDULER;
	}

}
