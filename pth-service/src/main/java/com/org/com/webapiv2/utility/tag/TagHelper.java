package com.org.com.webapiv2.utility.tag;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.TAG_SUMMARY_MONTH_FROM_USING_UTH_VISIBLE_TXN_INDICATOR;
import static com.org.panaroma.commons.constants.Constants.FUTURE_LOG_REMOVER_IDENTIFIER;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.MONTH_YEAR_FORMAT;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.TOTAL_AMOUNT;
import static com.org.panaroma.commons.constants.TagsConstants.TAGS;
import static com.org.panaroma.commons.constants.TagsConstants.TXN_INDICATORS;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.NO_TRANSACTION_FOUND_FOR_LISTING;

import com.org.com.webapiv2.constants.enums.TagSummaryResponseEnum;
import com.org.com.webapiv2.dto.tags.TagSummaryResponse;
import com.org.com.webapiv2.dto.tags.TagSummaryResponseNode;
import com.org.com.webapiv2.dto.tags.UserTagsSummaryResponse;
import com.org.com.webapiv2.dto.tags.UserTagsSummaryResponseNode;
import com.org.com.webapiv2.dto.tags.TagInfo;
import com.org.com.webapiv2.model.UserTagData;
import com.org.com.webapiv2.utility.AnalyticsUtility;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.exceptionhandler.ExceptionBuilder;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.math.BigDecimal;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

//This class is for helper methods for tag related operations
@Log4j2
@Component
public class TagHelper {

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public TagHelper(final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	// This method is for creating user tag data from es txns
	public static List<UserTagData> getUserTagDataFromTxns(
			final List<TransformedTransactionHistoryDetail> tagTxnsDetailsList) {

		Map<String, UserTagData> userTagDataMap = new HashMap<>();

		// creating user tag data by traversing each txn
		for (TransformedTransactionHistoryDetail txn : tagTxnsDetailsList) {
			for (String tagName : txn.getSearchFields().getSearchTags()) {
				UserTagData userTagData = userTagDataMap.get(tagName);

				// creating tag if already not present in map
				if (userTagData == null) {
					userTagData = new UserTagData();
					userTagData.setTag(tagName);
					userTagData.setCount(1);
					userTagData.setUpdatedTimeStamp(txn.getDocUpdatedDate());
					userTagDataMap.put(tagName, userTagData);
				}
				else {
					// increase the count of tag if already present
					userTagData.setCount(userTagData.getCount() + 1);
				}

				// Setting Uth Category of txn in user Tag Data for serving uth category
				// specific tags in get tags api PTH-227
				if (ObjectUtils.isNotEmpty(txn.getSearchFields().getSearchUthMerchantCategory())) {
					for (String category : txn.getSearchFields().getSearchUthMerchantCategory()) {
						UthCategoryEnum categoryEnum = UthCategoryEnum.getEnumFromUthCategoryId(category);
						if (categoryEnum != null) {
							Set<String> categorySet = userTagData.getCategoryId() == null ? new HashSet<>()
									: userTagData.getCategoryId();
							categorySet.add(categoryEnum.getUthCategoryId());
							userTagData.setCategoryId(categorySet);
						}
					}
				}
			}
		}
		log.info(FUTURE_LOG_REMOVER_IDENTIFIER + "final user tags : {}", userTagDataMap);

		return new ArrayList<>(userTagDataMap.values());
	}

	/**
	 * Extracts the text part from a tag, removing emojis.
	 * @param tag The tag possibly containing emoji and text
	 * @return The text part without emojis
	 */
	public static String extractTextFromTag(String tag) {
		if (tag == null) {
			return "";
		}

		// everything after the first space is the text
		int spaceIndex = tag.indexOf(' ');
		if (spaceIndex >= 0 && spaceIndex < tag.length() - 1) {
			return tag.substring(spaceIndex + 1).trim();
		}

		// If no space is found, return the original tag --> handles cases where there's
		// no emoji
		return tag;
	}

	/**
	 * This method updates the bucketing in tag used count. Bucketing is done in 1, 1+,
	 * 5+, 10+
	 * @param tags List of TagInfo objects to process
	 */
	public static void updateBucketingInTagUsedCount(final List<TagInfo> tags) {
		if (CollectionUtils.isEmpty(tags)) {
			return;
		}

		for (TagInfo tag : tags) {
			String count = tag.getCount();
			if (StringUtils.isNotBlank(count)) {
				try {
					int usageCount = Integer.parseInt(count);
					if (usageCount > 10) {
						tag.setCount("10+");
					}
					else if (usageCount > 5) {
						tag.setCount("5+");
					}
					else if (usageCount > 1) {
						tag.setCount("1+");
					}
					else {
						tag.setCount("1");
					}
				}
				catch (NumberFormatException e) {
					log.error("exception while bucketing tag count Invalid tag count: {}, Exception: {}", count,
							CommonsUtility.exceptionFormatter(e));
					// If count is not a valid number, default to null
					tag.setCount(null);
				}
			}
		}
	}

	// Method to get User Tags Summary Response from ES Search Response
	public static UserTagsSummaryResponse getUserTagsSummaryResponse(final SearchResponse searchResponse,
			final SearchContext searchContext, final String userId, final String month) {
		// Extract tags aggregation from the search response
		Terms tags = extractTagsFromResponse(searchResponse);

		UserTagsSummaryResponse userTagsSummaryResponse = UserTagsSummaryResponse.builder()
			.userId(userId)
			.month(month)
			.build();

		// Handle pagination for next page
		handlePagination(tags, searchContext, userTagsSummaryResponse);

		// get Tags List from Tags bucket aggregation
		List<UserTagsSummaryResponseNode> userTagsSummaryResponseNodes = getUserTagsSummaryResponseNodes(tags);

		userTagsSummaryResponse.setUserTagsSummary(userTagsSummaryResponseNodes);

		// Return the list of UserTagsSummaryResponseNodes
		return userTagsSummaryResponse;
	}

	private static void handlePagination(Terms tags, SearchContext searchContext,
			UserTagsSummaryResponse userTagsSummaryResponse) {
		if (tags.getBuckets().size() == searchContext.getPageSize() + 1) {
			PaginationParams paginationParams = PaginationParams.builder()
				.pageNo(searchContext.getPageNo() + 1)
				.pageSize(searchContext.getPageSize())
				.build();
			userTagsSummaryResponse.setPaginationParams(paginationParams);

			// Remove the last tag bucket as it was fetched just to check whether next
			// page exists or not
			tags.getBuckets().remove(tags.getBuckets().size() - 1);
		}
	}

	private static List<UserTagsSummaryResponseNode> getUserTagsSummaryResponseNodes(Terms tags) {
		List<UserTagsSummaryResponseNode> userTagsSummaryResponseNodes = new ArrayList<>();

		// Iterate through each tag bucket in the tags aggregation
		for (Terms.Bucket tagBucket : tags.getBuckets()) {
			// Extract transaction indicators aggregation for the current tag
			Terms txnIndicator = tagBucket.getAggregations().get(TXN_INDICATORS);

			// Create a new UserTagsSummaryResponseNode for the current tag
			UserTagsSummaryResponseNode userTagsSummaryResponseNode = UserTagsSummaryResponseNode.builder()
				.tag(tagBucket.getKeyAsString())
				.build();

			// get Map of txnIndicator and Pair<> amount and txnCount for given
			// txnIndicator aggregation
			Map<TagSummaryResponseEnum, Pair<Double, Integer>> tagSummaryResponseEnumMap = getTagSummaryResponseEnumMap(
					txnIndicator);
			for (Map.Entry<TagSummaryResponseEnum, Pair<Double, Integer>> entry : tagSummaryResponseEnumMap
				.entrySet()) {
				TagSummaryResponseEnum tagSummaryResponseEnum = entry.getKey();

				Pair<Double, Integer> amountTxnCountPair = tagSummaryResponseEnumMap.get(tagSummaryResponseEnum);

				// Get the amount and transaction count for the current tag
				String amount = AnalyticsUtility.getAmount(BigDecimal.valueOf(amountTxnCountPair.getKey()));
				Integer txnCount = amountTxnCountPair.getValue();

				// Setting amount and txnCount for spent and received
				userTagsSummaryResponseNode.setAmount(amount, tagSummaryResponseEnum);
				userTagsSummaryResponseNode.setTxnCount(txnCount, tagSummaryResponseEnum);
			}

			// Add the UserTagsSummaryResponseNode to the list
			userTagsSummaryResponseNodes.add(userTagsSummaryResponseNode);
		}

		return userTagsSummaryResponseNodes;
	}

	// Method to get Map of TagSummaryResponseEnum and Pair<> amount and txnCount for
	// given txnIndicator aggregation
	private static Map<TagSummaryResponseEnum, Pair<Double, Integer>> getTagSummaryResponseEnumMap(
			final Terms txnIndicatorAggregation) {
		if (Objects.isNull(txnIndicatorAggregation)) {
			return null;
		}

		// get Map of txnIndicator and Pair<> amount and txnCount for given txnIndicator
		// aggregation
		Map<TransactionIndicator, Pair<Double, Integer>> txnIndicatorMap = getTxnIndicatorMap(txnIndicatorAggregation);

		// Map to store TagSummaryResponseEnum and Pair<> amount and txnCount
		Map<TagSummaryResponseEnum, Pair<Double, Integer>> tagSummaryResponseEnumMap = new HashMap<>();

		for (Map.Entry<TransactionIndicator, Pair<Double, Integer>> entry : txnIndicatorMap.entrySet()) {
			TagSummaryResponseEnum tagSummaryResponseEnum = TagSummaryResponseEnum
				.getTagSummaryResponseEnumByTxnIndicator((TransactionIndicator) entry.getKey());

			if (tagSummaryResponseEnumMap.containsKey(tagSummaryResponseEnum)) {
				Pair<Double, Integer> amountTxnCountExistingPair = tagSummaryResponseEnumMap
					.get(tagSummaryResponseEnum);
				amountTxnCountExistingPair.setKey(amountTxnCountExistingPair.getKey() + entry.getValue().getKey());
				amountTxnCountExistingPair
					.setValue(amountTxnCountExistingPair.getValue() + entry.getValue().getValue());
				tagSummaryResponseEnumMap.put(tagSummaryResponseEnum, amountTxnCountExistingPair);
			}
			else {
				tagSummaryResponseEnumMap.put(tagSummaryResponseEnum,
						new Pair<>(entry.getValue().getKey(), entry.getValue().getValue()));
			}
		}

		return tagSummaryResponseEnumMap;
	}

	// Method to get Map of txnIndicator and Pair<> amount and txnCount for given
	// txnIndicator aggregation
	private static Map<TransactionIndicator, Pair<Double, Integer>> getTxnIndicatorMap(
			final Terms txnIndicatorAggregation) {
		if (Objects.isNull(txnIndicatorAggregation)) {
			return null;
		}

		Map<TransactionIndicator, Pair<Double, Integer>> txnIndicatorMap = new HashMap<>();

		// Iterate through each transaction indicator bucket
		for (Terms.Bucket txnIndicatorBucket : txnIndicatorAggregation.getBuckets()) {
			// Extract the total amount aggregation for the current transaction indicator
			Sum totalAmount = txnIndicatorBucket.getAggregations().get(TOTAL_AMOUNT);

			// Get amount and document count
			Double amount = totalAmount.getValue();
			Long docCount = txnIndicatorBucket.getDocCount();

			TransactionIndicator transactionIndicator = TransactionIndicator
				.getTransactionIndicatorEnumByKey(Integer.parseInt(txnIndicatorBucket.getKeyAsString()));

			if (Objects.nonNull(transactionIndicator)) {
				if (txnIndicatorMap.containsKey(transactionIndicator)) {
					// update to map Transaction Indicator (Credit, Debit, NoIndicator)
					// and Pair<> amount and txnCount. sum existing amount and txnCount
					// with new amount and sum
					Pair<Double, Integer> amountTxnCountExistingPair = txnIndicatorMap.get(transactionIndicator);
					amountTxnCountExistingPair.setKey(amountTxnCountExistingPair.getKey() + amount);
					amountTxnCountExistingPair.setValue(amountTxnCountExistingPair.getValue() + docCount.intValue());
					txnIndicatorMap.put(transactionIndicator, amountTxnCountExistingPair);

				}
				else {
					// Add to map
					txnIndicatorMap.put(transactionIndicator, new Pair<>(amount, docCount.intValue()));
				}
			}

		}

		return txnIndicatorMap;
	}

	private static Terms extractTagsFromResponse(final SearchResponse searchResponse) {
		if (Objects.isNull(searchResponse) || Objects.isNull(searchResponse.getAggregations())
				|| Objects.isNull(searchResponse.getAggregations().get(TAGS))) {
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING);
		}

		Terms tags = searchResponse.getAggregations().get(TAGS);
		if (ObjectUtils.isEmpty(tags.getBuckets())) {
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING);
		}

		return tags;
	}

	public static TagSummaryResponse getTagSummaryResponse(final SearchResponse searchResponse,
			final SearchContext searchContext, final String userId, final String month, final String tag) {

		// Getting txn indicators bucket of tag from search response
		Terms tagTxnIndicators = extractTransactionIndicatorsTermsFromResponse(searchResponse);

		// Build TagSummaryResponse
		List<TagSummaryResponseNode> tagSummaryResponseNodes = getTagSummaryResponseNodes(tagTxnIndicators);

		return TagSummaryResponse.builder()
			.userId(userId)
			.month(month)
			.tag(tag)
			.tagSummary(tagSummaryResponseNodes)
			.build();
	}

	private static Terms extractTransactionIndicatorsTermsFromResponse(final SearchResponse searchResponse) {
		if (Objects.isNull(searchResponse) || Objects.isNull(searchResponse.getAggregations())
				|| Objects.isNull(searchResponse.getAggregations().get(TXN_INDICATORS))) {
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING);
		}

		Terms txnIndicators = searchResponse.getAggregations().get(TXN_INDICATORS);

		if (ObjectUtils.isEmpty(txnIndicators.getBuckets())) {
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING);
		}

		return txnIndicators;
	}

	private static List<TagSummaryResponseNode> getTagSummaryResponseNodes(final Terms tagTxnIndicators) {
		List<TagSummaryResponseNode> tagSummaryResponseNodes = new ArrayList<>();

		// get Map of TagSummaryResponseEnum and Pair<> amount and txnCount for given
		// txnIndicator aggregation
		Map<TagSummaryResponseEnum, Pair<Double, Integer>> tagSummaryResponseEnumMap = getTagSummaryResponseEnumMap(
				tagTxnIndicators);

		// Add entry in correct order mentioned in OrderOfTagSummaryResponseNode
		for (TagSummaryResponseEnum tagSummaryResponseEnum : TagSummaryResponseEnum.OrderOfTagSummaryResponseNode) {
			Double totalAmount = 0.0;
			int txnCount = 0;

			// get amount and txnCount for given tagSummaryResponseEnum from
			// tagSummaryResponseEnumMap
			if (tagSummaryResponseEnumMap.containsKey(tagSummaryResponseEnum)) {
				Pair<Double, Integer> amountTxnCountPair = tagSummaryResponseEnumMap.get(tagSummaryResponseEnum);
				totalAmount = amountTxnCountPair.getKey();
				txnCount = amountTxnCountPair.getValue();
			}

			String amount = AnalyticsUtility.getAmount(BigDecimal.valueOf(totalAmount));

			// add to tagSummaryResponseNodes
			tagSummaryResponseNodes.add(TagSummaryResponseNode.builder()
				.title(LocalizedDataCacheService.getLocalizedValue(tagSummaryResponseEnum.getTitle()))
				.amount(amount)
				.txnCount(txnCount)
				.type(tagSummaryResponseEnum.name())
				.build());
		}

		return tagSummaryResponseNodes;
	}

	// method to check if UTH based txn indicator field should be used for tag aggregation
	public static boolean useUthBasedTxnIndicatorFieldForTagAggregation(final SearchContext searchContext) {
		if (Objects.isNull(searchContext) || Objects.isNull(searchContext.getFromDate())) {
			return false;
		}

		// Getting month from configurable properties
		String monthFromUsingSearchTxnIndicator = configurablePropertiesHolder
			.getProperty(TAG_SUMMARY_MONTH_FROM_USING_UTH_VISIBLE_TXN_INDICATOR, String.class);
		// Convert the month from the request to epoch milliseconds
		Long monthEpochMillis = DateTimeUtility.getEpochMillis(monthFromUsingSearchTxnIndicator, MONTH_YEAR_FORMAT);
		// Get the start and end epoch times for the month
		Long monthStartEpoch = DateTimeUtility.getMonthStartEpoch(monthEpochMillis);

		return searchContext.getFromDate() != null && searchContext.getFromDate() >= monthStartEpoch;
	}

}
