package com.org.com.webapiv2.cstCategoryCode;

import com.org.com.webapiv2.dto.upiSearch.ResponseTxn;
import java.text.SimpleDateFormat;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ConvertUthReponseToUpi {

	static String TRANLOG_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

	public static UserTransactionHistory convert(final ResponseTxn responseTxn) {
		log.info("Preparing auditList from uth response -{}: ", responseTxn);
		SimpleDateFormat dateFormat = new SimpleDateFormat(TRANLOG_DATE_FORMAT);
		dateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"));

		try {
			UserTransactionHistory userTransactionHistory = new UserTransactionHistory();
			userTransactionHistory.setId(responseTxn.getTxnId());
			userTransactionHistory.setStatus(responseTxn.getStatus());
			userTransactionHistory.setAmount(responseTxn.getAmount());
			userTransactionHistory.setNote(responseTxn.getNote());
			userTransactionHistory.setRrn(responseTxn.getRrn());
			userTransactionHistory.setRefCategory(responseTxn.getRefCategory());
			userTransactionHistory.setMerchantId(responseTxn.getMerchantId());
			userTransactionHistory.setMerchantOrderId(responseTxn.getMerchantOrderId());
			userTransactionHistory.setBusinessType(responseTxn.getBusinessType());
			userTransactionHistory.setCategory(responseTxn.getCategory());
			userTransactionHistory.setOrgTxnId(responseTxn.getOrgTxnId());
			userTransactionHistory.setSource("SWITCHV2");

			userTransactionHistory.setPayeeCustId(responseTxn.getPayee().getOauthCustId());
			/*
			 * if (!ObjectUtils.isEmpty(responseTxn.getPayee().getOauthCustId())) { TODO:
			 * need to discuss how to payeer and payeer custId
			 * userTransactionHistory.setPayeeUserId(Long.parseLong(responseTxn.getPayee()
			 * .getOauthCustId())); TODO: need to verify payer and payee Type User payee =
			 * null; payee = userSlaveRepository.findByScopeCustIdAndTypeAndStatus(
			 * transaction.getPayee().getOauthCustId(), Enums.USER_TYPE.PERSON.name(),
			 * Enums.DB_STATUS.ACTIVE.name()); if (!ObjectUtils.isEmpty(payee)) {
			 * userTransactionHistory.setPayeeUserId(payee.getId());
			 * userTransactionHistory.setPayeeType(payee.getType()); } }
			 */
			userTransactionHistory.setPayeeName(responseTxn.getPayee().getName());
			userTransactionHistory.setPayeeAccount(responseTxn.getPayee().getBankAccount());
			userTransactionHistory.setPayeeAccountIfsc(responseTxn.getPayee().getIfsc());
			userTransactionHistory.setPayeeVa(responseTxn.getPayee().getVpa());
			userTransactionHistory.setPayeeAccountIfsc(responseTxn.getPayee().getIfsc());
			userTransactionHistory.setPayeeMobile(responseTxn.getPayee().getMobile());

			userTransactionHistory.setPayerCustId(responseTxn.getPayer().getOauthCustId());
			/*
			 * if (!ObjectUtils.isEmpty(responseTxn.getPayer().getOauthCustId())) { TODO:
			 * we have not payer user id thats why using paysercustid
			 * userTransactionHistory.setPayerUserId(responseTxn.getPayer().getOauthCustId
			 * ()); User payer = null; payer =
			 * userSlaveRepository.findByScopeCustIdAndTypeAndStatus(
			 * transaction.getPayer().getOauthCustId(), Enums.USER_TYPE.PERSON.name(),
			 * Enums.DB_STATUS.ACTIVE.name()); if (!ObjectUtils.isEmpty(payer)) {
			 * userTransactionHistory.setPayerUserId(payer.getId());
			 * userTransactionHistory.setPayerType(payer.getType()); } }
			 */
			userTransactionHistory.setPayerName(responseTxn.getPayer().getName());
			userTransactionHistory.setPayerAccount(responseTxn.getPayer().getBankAccount());
			userTransactionHistory.setPayerAccountIfsc(responseTxn.getPayer().getIfsc());
			userTransactionHistory.setPayerVa(responseTxn.getPayer().getVpa());
			userTransactionHistory.setPayerAccountIfsc(responseTxn.getPayer().getIfsc());
			userTransactionHistory.setPayerMobile(responseTxn.getPayer().getMobile());
			// before used : responseTxn.getInitiationType()
			// after user :
			userTransactionHistory.setType(responseTxn.getInitiationType());
			log.info("Response successfully prepared from uth response : {}", userTransactionHistory);
			return userTransactionHistory;
		}
		catch (Exception e) {
			log.error("Exception occured while parsing uth response  {}", e.getMessage());
		}

		return null;
	}

}
