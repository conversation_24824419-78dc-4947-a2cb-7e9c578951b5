package com.org.com.webapiv2.cstCategoryCode;

import com.google.common.base.Strings;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import jakarta.annotation.PostConstruct;

import com.org.panaroma.commons.utils.CommonsUtility;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class CategoryConfigurationUtil {

	public static String seperator = "_";

	public static Map<String, CategoryParticipant> categoryMap = new LinkedHashMap<>();

	public static Map<Long, CategoryNarration> categoryNarrationMap = new LinkedHashMap<>();

	// @Value("${cst.category.is.payment.instrument.use:false}")
	private static boolean cstIsCategoryPaymentInstrumentUse;

	public static Map<String, CategoryParticipant> readFileCategoryParticipant(final String filePath)
			throws IOException {
		Map<String, CategoryParticipant> categoryParticipantHashMap = new LinkedHashMap<>();
		try {
			InputStream inputStream = CategoryConfigurationUtil.class.getClassLoader().getResourceAsStream(filePath);
			BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
			String data;
			StringBuilder content = new StringBuilder();
			while ((data = br.readLine()) != null) {
				try {
					String[] field = data.split(",");
					log.info("Narration Id :{}", field[9]);

					if (StringUtils.isNotEmpty(field[9])) {
						CategoryParticipant cp = CategoryParticipant.builder()
							.id(Long.parseLong(field[0]))
							.participantType(field[1])
							.status(field[2])
							.category(field[3])
							.type(field[4])
							.businessType(field[5])
							.merchantSubCategory(field[6])
							.initiator(field[7])
							.messageType(field[8])
							.narrationId(Long.parseLong(field[9]))
							.timeLineId(field[10])
							.importantNoteId(field[11])
							.createdOn(field[12])
							.updatedOn(field[13])
							.updatedBy(field[14])
							.responseConstant(field[15])
							.build();

						String paymentInstrument = field[18];
						if (paymentInstrument != null) {
							String[] paymentInstruments = paymentInstrument.split(":");
							for (int i = 0; i < paymentInstruments.length; i++) {
								categoryParticipantHashMap.put(cp.getKey(seperator, paymentInstruments[i],
										getBusinessCategory(cp.getBusinessType()), true), cp);

							}
						}
						categoryParticipantHashMap.put(cp.getKey(seperator, null,
								getBusinessCategory(cp.getBusinessType()), cstIsCategoryPaymentInstrumentUse), cp);
					}
					else {
						log.info("Ignoring this record because narration id is blank :{}", data);
					}
				}
				catch (Exception ex) {
					log.info("Inner Exception :{},  Exception:{}", data, CommonsUtility.exceptionFormatter(ex));
				}

			}
		}
		catch (Exception ex) {
			ex.printStackTrace();
			log.error("readFileCategoryParticipant: Unable to read file :{}", CommonsUtility.exceptionFormatter(ex));
		}
		return categoryParticipantHashMap;
	}

	@PostConstruct
	public static void init() {
		try {
			categoryMap = readFileCategoryParticipant("narration.csv");
			categoryNarrationMap = readFileCategoryNarration("category.csv");
		}
		catch (Exception ex) {
			log.error("please check unable to initialise category configuration :{}", ex);
			ex.printStackTrace();
		}
	}

	@Value("${cst.category.is.payment.instrument.use:false}")
	public void setEnableCstPaymentInstrument(final boolean cstIsCategoryPaymentInstrumentUse) {
		this.cstIsCategoryPaymentInstrumentUse = cstIsCategoryPaymentInstrumentUse;
	}

	public static Map<Long, CategoryNarration> readFileCategoryNarration(final String filePath) throws IOException {
		Map<Long, CategoryNarration> categoryNarrationHashMap = new LinkedHashMap<>();
		try {
			InputStream inputStream = CategoryConfigurationUtil.class.getClassLoader().getResourceAsStream(filePath);
			BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));

			String data;
			StringBuilder content = new StringBuilder();
			while ((data = br.readLine()) != null) {
				try {
					String[] field = data.split(",");
					long id = Long.parseLong(field[0]);
					String narration = field[1];
					String narrationDirection = field[2];
					String detailNarration = field[3];
					String passbookCategory = field[4];
					String displayName = field[5];
					String createdOn = field[6];
					String updatedOn = field[7];

					CategoryNarration cp = CategoryNarration.builder()
						.narrationId(id)
						.narration(narration)
						.narrationDirection(narrationDirection)
						.detailNarration(detailNarration)
						.passbookCategory(passbookCategory)
						.displayName(displayName)
						.createdOn(createdOn)
						.updatedOn(updatedOn)
						.build();
					log.info("Narration Id add in the log :{} CategoryNarration:{} ", id, cp);
					categoryNarrationHashMap.put(cp.getNarrationId(), cp);
				}
				catch (Exception ex) {
					log.error("readFileCategoryNarration : Inner Exception :{} Exception:{}", data, ex);
				}
			}
		}
		catch (Exception ex) {
			ex.printStackTrace();
			log.error("Unable to read file :{}", ex);
		}
		return categoryNarrationHashMap;
	}

	public static String getBusinessCategory(final String businessType) {

		Enums.TxnInfoBusinessType businessTypeEnum = com.google.common.base.Enums
			.getIfPresent(Enums.TxnInfoBusinessType.class, Strings.nullToEmpty(businessType))
			.orNull();

		return Objects.nonNull(businessTypeEnum) ? businessTypeEnum.name() : TransactionHistoryConstants.DEFAULT;
	}

}
