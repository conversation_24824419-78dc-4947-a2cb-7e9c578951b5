package com.org.com.webapiv2.cstCategoryCode;

;

public interface TransactionHistoryConstants {

	String DEFAULT = "DEFAULT";

	String COLLECT = "COLLECT";

	String MANDATE = "MANDATE";

	String ERROR_CODE_U69 = "U69";

	String NO_RECORD_FOUND = "no records found for given request";

	String USER_NOT_EXIST = "upi user does not exist";

	String DEBIT = "DEBIT";

	String PAY = "PAY";

	String CREDIT = "CREDIT";

	String PERSON = "PERSON";

	String ENTITY = "ENTITY";

	String P2P = "P2P";

	String P2M = "P2M";

	String TRANLOG_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

	String MANDATE_DATE_FORMAT = "ddMMyyyy";

	String SUCCESS = "SUCCESS";

	String PENDING = "PENDING";

	String FAILURE = "FAILURE";

	String DEEMED = "DEEMED";

	String REJECT = "REJECT";

	String ASC = "asc";

	String DESC = "desc";

	String DECLINED_RESP_CODE = "U19-ZA";

	String DECLINED_RESP_CODE_OTHER_PSP = "U29-ZA";

	String NPCI_SUCCESS_RESP_CODE = "00";

	String DEBIT_REVERSAL_TIMEOUT_RESP_CODE = "RR";

	String DEBIT_REVERSAL_FAILURE_RESP_CODE = "96";

	String DEBIT_REVERSAL_PENDING = "91";

	String DEBIT_NOT_FOUND_RESP_CODE = "OD";

	String DEBIT_NOT_DONE_RESP_CODE = "ND";

	// millis
	long TIMEMILLIS_IN_6MONTHS = 15552000000L;

	String PAYER_BANK_NAME = "PAYER_BANK_NAME";

	String PAYEE_BANK_NAME = "PAYEE_BANK_NAME";

	String PAYEE_NAME = "PAYEE_NAME";

	String PAYER_NAME = "PAYER_NAME";

	String DEFAULT_CATEGORY = "DEFAULT";

	String DEFAULT_BUSINESS_TYPE = "DEFAULT";

	String DRC = "DRC";

	String DRC_PENDING = "DRC_PENDING";

	String DEBIT_NOT_DONE = "DEBIT_NOT_DONE";

	String DECLINED = "DECLINED";

	String DEFAULT_TYPE = "DEFAULT";

	String kEY_NOT_FOUND_ERROR = "TRANSACTION_HISTORY_DATA_NOT_FOUND";

	String DATA_NOT_FOUND_IN_DATABASE = "DATA_NOT_FOUND_FOR_PASSBOOK_IN_DATABASE";

	String POST_TXN_SCREEN_TIMELINE_DATA_NOT_FOUND_IN_DATABASE = "POST_TXN_SCREEN_TIMELINE_DATA_NOT_FOUND_IN_DATABASE";

	String DATA_NOT_FOUND_IN_DATABASE_MANDATE_JOURNEY_NARRATION = "DATA_NOT_FOUND_FOR_MANDATE_JOURNEY_NARRATION_IN_DATABASE";

	String DATA_NOT_FOUND_IN_DATABASE_MANDATE_IMP_INFO = "DATA_NOT_FOUND_IN_DATABASE_MANDATE_IMP_INFO";

	String DEFAULT_SUB_CATEGORY = "DEFAULT";

	String DEFAULT_INITIATOR = "DEFAULT";

	String NON_PAYTM_MERCHANT = "NON_PAYTM_MERCHANT";

	String PAYTM_OFFLINE_MERCHANT = "PAYTM_OFFLINE_MERCHANT";

	String MERCHANT_DEFAULT_SUB_CATEGORY = "DEFAULT_MERCHANT";

	String BUSINESS_TYPE = "business_type";

	String DEFAULT_VALUE = "DEFAULT";

	String TXN_TYPE = "type";

	String VALIDITY_END = "VALIDITY_END";

	String VALIDITY_START = "VALIDITY_START";

	String DRC_SUCCESS = "DRC_SUCCESS";

	String BUSINESS_FAILURE = "BUSINESS_FAILURE";

	String DEBIT_TIMEOUT_FAILURE = "DEBIT_TIMEOUT_FAILURE";

	String TXN_AMOUNT = "AMOUNT";

	String DEBIT_TIMEOUT_AND_BUSINESS_FAILURE_FLAG = "DEBIT_TIMEOUT_AND_BUSINESS_FAILURE_FLAG";

	String EXCEPTION_IN_BANK_HEALTH_METADATA_API = "EXCEPTION_IN_BANK_HEALTH_METADATA_API";

	String SUCCESS_RESPONSE_COUNT_IN_BANK_HEALTH_METADATA_API = "SUCCESS_RESPONSE_COUNT_IN_BANK_HEALTH_METADATA_API";

	String MERCHANT_ORDER_STATUS_IN_PENDING_STATE = "MERCHANT_ORDER_STATUS_IN_PENDING_STATE";

	String MERCHANT_ORDER_STATUS_IN_FAILURE_STATE = "MERCHANT_ORDER_STATUS_IN_FAILURE_STATE";

	String DC = "DC";

	String DR = "DR";

	String DC_HYSTRIX_FALLBACK = "DC_HYSTRIX_FALLBACK";

	String DR_HYSTRIX_FALLBACK = "DR_HYSTRIX_FALLBACK";

	String IS_ES_FALLBACK_ALLOWED = "IS_ES_FALLBACK_ALLOWED";

	String ES_FALLBACK_EXCEPTION = "ES_FALLBACK_EXCEPTION";

	String POST_TXN_SCREEN_TIMELINE = "POST_TXN_SCREEN";

	String MASKED_ACCOUNT_NUMBER_PAYER = "MASKED_ACCOUNT_NUMBER_PAYER";

	String PAYEE_MASKED_ACCOUNT_NUM = "PAYEE_MASKED_ACCOUNT_NUM";

	// In Minutes (24Hrs)
	String DEFAULT_MANDATE_RENEWAL_NOTIFICATION_START_OFFSET = "1440";

	String IPO_BUSINESS_TYPE_PREFIX = "IPO_";

	String OTM_BUSINESS_TYPE_PREFIX = "OTM_";

	String RM_BUSINESS_TYPE_PREFIX = "RM_";

	String MANDATE_DATE_FORMAT_BO_PANEL = "dd/MM/yyyy";

	String NEED_VERIFY = "NEED_VERIFY";

	String USER_PREAPPROVED = "USER_PREAPPROVED";

}