package com.org.com.webapiv2.cstCategoryCode;;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;

@Setter
@Getter
class UthDto {

	Long userId;

	// not coming in request so added here to pass
	String scopeCustId;

	Integer pageNo;

	Integer pageSize;

	Filter filter;

	Sort sort;

	public Integer getPageNo() {

		if (ObjectUtils.isEmpty(pageNo)) {
			return 0;
		}
		return pageNo;
	}

	public Integer getPageSize() {

		if (ObjectUtils.isEmpty(pageSize)) {
			return 20;
		}

		return pageSize;
	}

}