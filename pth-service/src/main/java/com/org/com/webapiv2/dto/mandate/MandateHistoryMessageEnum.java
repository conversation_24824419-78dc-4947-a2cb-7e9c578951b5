package com.org.com.webapiv2.dto.mandate;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MandateHistoryMessageEnum {

	RECURRING_MANDATE_CREATE_SUCCESS(
			"Automatic Payment successfully created for {PayeeName} for ₹{MandateAmount}, {Frequency}, starting from {StartDate} till {EndDate}",
			"Automatic Payment has been successfully created", "Automatic Payment Created", null),

	RECURRING_MANDATE_CREATE_PENDING("Automatic Payment request for {PayeeName} of ₹{MandateAmount} is being processed",
			"Automatic Payment request is being processed", "Setup Pending", "Setup is under process"),

	RECURRING_MANDATE_CREATE_FAILURE("Request to create an Automatic Payment of ₹{MandateAmount} has failed",
			"Request to create an Automatic Payment has failed", "Setup Failed", null),

	RECURRING_MANDATE_COLLECT_FAILURE("Payment of ₹{Amount} has failed", "Payment has failed", "Payment Failed", null),

	RECURRING_MANDATE_COLLECT_SUCCESS("Payment of ₹{Amount} is successful", "Payment is successful",
			"Payment Completed", null),

	RECURRING_MANDATE_COLLECT_PENDING("Payment of ₹{Amount} is being processed", "Payment under process",
			"Payment Pending", "Payment is under process"),

	RECURRING_MANDATE_PAUSE_SUCCESS("Automatic Payment paused from {PauseStartDate} till {PauseEndDate}",
			"Automatic Payment successfully Paused", "Automatic Payment Paused", null),

	RECURRING_MANDATE_PAUSE_PENDING("Request to pause Automatic Payment for {PayeeName} is under process",
			"Request to pause Automatic Payment is under process", "Pause Request Pending", "Pause is under process"),

	RECURRING_MANDATE_PAUSE_FAILURE("Attempt to pause automatic payment for {PayeeName} is failed",
			"Request to pause Automatic Payment has failed", "Pause Request Failed", null),

	// need to ask UnpauseStartDate and ExpiryDate here
	RECURRING_MANDATE_UNPAUSE_SUCCESS("Automatic Payment Resumed from {UnpauseStartDate} till {ExpiryDate}",
			"Automatic Payment has been resumed successfully", "Automatic Payment Resumed", null),

	RECURRING_MANDATE_UNPAUSE_PENDING("Request to resume Automatic Payment for {PayeeName} is being processed",
			"Request to resume Automatic Payment is being processed", "Resume Request Pending",
			"Resume is under process"),

	RECURRING_MANDATE_UNPAUSE_FAILURE("Attempt to resume automatic payment for {PayeeName} is failed",
			"Attempt to resume Automatic Payment has failed", "Resume Request Failed", null),

	RECURRING_MANDATE_REVOKE_SUCCESS("Automatic Payment successfully cancelled for {PayeeName}.",
			"Automatic Payment successfully cancelled", "Automatic Payment Cancelled", null),

	RECURRING_MANDATE_REVOKE_PENDING("Request to cancel Automatic Payment for {PayeeName} is being processed",
			"Request to cancel Automatic Payment is being processed", "Cancel Request Pending",
			"Cancellation is in process"),

	RECURRING_MANDATE_REVOKE_FAILURE("Attempt to cancel automatic payment for {PayeeName} is failed",
			"Attempt to cancel automatic payment has failed", "Cancel Request Failed", null),

	RECURRING_MANDATE_UPDATE_SUCCESS(
			"Automatic Payment successfully updated for {PayeeName} for ₹{MandateAmount}, "
					+ "{Frequency}, starting from {StartDate} till {EndDate}",
			"Automatic Payment successfully updated", "Automatic Payment Updated", null),

	RECURRING_MANDATE_UPDATE_PENDING(
			"Automatic Payment update request for {PayeeName} of ₹{MandateAmount} is being processed",
			"Request to update Automatic Payment is being processed", "Update Request Pending",
			"Update is under process"),

	RECURRING_MANDATE_UPDATE_FAILURE("Request to update Automatic Payment has failed",
			"Attempt to update automatic payment has failed", "Update Request Failed", null),

	RECURRING_MANDATE_EXPIRE_SUCCESS("Automatic Payment for {PayeeName} has expired", "Automatic Payment expired",
			"Automatic Payment Expired", null),

	// Below are the values for IPO_MANDATE

	IPO_MANDATE_CREATE_SUCCESS(
			"IPO Bid of ₹{Amount} is successfully completed for {PayeeName}. Money has been blocked in your {PayerBank} A/c {PayerMaskedAccountNo} till {ExpiryDate} "
					+ "and shall be deducted automatically as per the IPO allotment process. In case of non allotment, the entire amount will be unblocked from your {PayerBank} account and will be available for use",
			null, "IPO Bid Request successful", null),

	IPO_MANDATE_CREATE_PENDING("IPO Bid application of ₹{Amount} for {PayeeName} is under process", null,
			"IPO Bid under process", "Application Pending"),

	IPO_MANDATE_CREATE_FAILURE(
			"IPO Application request has failed. Money did not get blocked from your {PayerBank} account", null,
			"IPO Bid Request failed", null),

	IPO_MANDATE_COLLECT_FAILURE("Payment of ₹{Amount} has failed from your {PayerBank} A/c {PayerMaskedAccountNo}",
			null, "Payment failed", null),

	IPO_MANDATE_COLLECT_SUCCESS(
			"Congratulations! You have been allotted shares of {PayeeName} IPO. Shares allotted will be credited in your demat account on the IPO listing day. "
					+ "Payment of ₹{Amount} is been deducted from your {PayerBank} A/c {PayerMaskedAccountNo}",
			null, "IPO allotted successfully", "Money has been deducted from your bank account"),

	IPO_MANDATE_COLLECT_PENDING(
			"Payment of ₹{Amount} is under process from your {PayerBank} A/c {PayerMaskedAccountNo}", null,
			"Payment pending", "Payment is under process"),

	IPO_MANDATE_REVOKE_SUCCESS(
			"Successfully cancelled IPO application for {PayeeName} IPO. Amount of ₹{Amount} shall be "
					+ "released from your {PayerBank} A/c {PayerMaskedAccountNo} within 1-4 business days",
			null, "IPO Bid Request cancelled", null),

	IPO_MANDATE_REVOKE_PENDING("Request to cancel IPO application for {PayeeName} IPO is under process", null,
			"IPO Bid cancellation pending", "Cancellation is in process"),

	IPO_MANDATE_REVOKE_FAILURE("Failed to cancel IPO application for {PayeeName} IPO", null,
			"IPO Bid cancellation failed", null),

	IPO_MANDATE_UPDATE_SUCCESS("IPO Bid for {PayeeName} IPO has been successfully updated.", null,
			"IPO Bid Request updated successfully", null),

	IPO_MANDATE_UPDATE_PENDING("Update request for the IPO Bid for {PayeeName} IPO is under process", null,
			"IPO Bid update under process", "Update is under process"),

	IPO_MANDATE_UPDATE_FAILURE("Request to update the IPO Bid for {PayeeName} IPO has failed", null,
			"IPO Bid update request failed", null),

	IPO_MANDATE_EXPIRE_SUCCESS("IPO Bid Request for {PayeeName} has expired", "IPO Bid Request expired",
			"IPO Bid Request Expired", null),

	// For SBMD_MANDATE
	SBMD_MANDATE_CREATE_SUCCESS(
			"Trading Block for {PayeeName} is successfully setup for ₹{Amount}, starting from {StartDate} till {EndDate}",
			"Trading Block has been successfully setup", "Trading Block Created", null),

	SBMD_MANDATE_CREATE_PENDING("Trading Block setup request for {PayeeName} of ₹{Amount} is under process",
			"Trading Block setup request is under process", "Setup Pending", "Setup is under process"),

	SBMD_MANDATE_CREATE_FAILURE("Trading Block setup request for {PayeeName} is failed",
			"Trading Block setup has been failed", "Setup Failed", null),

	SBMD_MANDATE_COLLECT_SUCCESS("Payment of ₹{Amount} is successful", "Paid Successfully", "Payment Completed", null),

	SBMD_MANDATE_COLLECT_PENDING("Payment of ₹{Amount} is under process", "Payment under process", "Payment Pending",
			"Payment is under process"),

	SBMD_MANDATE_COLLECT_FAILURE("Payment of ₹{Amount} has failed", "Payment Failed", "Payment Failed", null),

	SBMD_MANDATE_REVOKE_SUCCESS("Trading Block for {PayeeName} is successfully cancelled", null,
			"Trading Block Cancelled", null),

	SBMD_MANDATE_REVOKE_FAILURE("Request to cancel your Trading Block for {PayeeName} is failed",
			"Cancellation request failed", "Trading Block cancellation request failed", null),

	SBMD_MANDATE_EXPIRE_SUCCESS("Trading Block for {PayeeName} has expired", "Trading Block Expired",
			"Trading Block Expired", null),

	// For ONE_TIME_MANDATE
	ONE_TIME_MANDATE_CREATE_SUCCESS(
			"Automatic Payment for {PayeeName} is successfully setup for ₹{Amount}, starting from {StartDate} till {EndDate}",
			"Automatic Payment is successfully setup", "Automatic Payment Created", null),

	ONE_TIME_MANDATE_CREATE_PENDING("Automatic Payment setup request for {PayeeName} of ₹{Amount} is under process",
			"Automatic Payment setup request is under process", "Setup Pending", "Application Pending"),

	ONE_TIME_MANDATE_CREATE_FAILURE("Automatic Payment setup for {PayeeName} is failed",
			"Automatic Payment setup is failed", "Setup Failed", null),

	ONE_TIME_MANDATE_COLLECT_SUCCESS("Payment of ₹{Amount} is successful", "Payment successful", "Payment Completed",
			"Money has been deducted from your bank account"),

	ONE_TIME_MANDATE_COLLECT_PENDING("Payment of ₹{Amount} is under process", "Payment under process",
			"Payment Pending", "Payment is under process"),

	ONE_TIME_MANDATE_COLLECT_FAILURE("Payment of ₹{Amount} has failed", "Payment has failed", "Payment Failed", null),

	ONE_TIME_MANDATE_REVOKE_SUCCESS(
			"Automatic Payment is successfully cancelled for {PayeeName}. No further debits will happen on your bank account",
			"Automatic Payment is successfully cancelled. No further debits will happen on your bank account",
			"Automatic Payment Cancelled", null),

	ONE_TIME_MANDATE_REVOKE_PENDING("Request to cancel Automatic Payment for {PayeeName} is under process",
			"Request to cancel Automatic Payment is under process", "Cancel Request Pending",
			"Cancellation is in process"),

	ONE_TIME_MANDATE_REVOKE_FAILURE("Request to cancel your automatic payment for {PayeeName} is failed",
			"Request to cancel your automatic payment is failed", "Cancel Request Failed", null),

	ONE_TIME_MANDATE_EXPIRE_SUCCESS("Automatic Payment for {PayeeName} has expired", "Automatic Payment has expired",
			"Automatic Payment Expired", null),

	// Port-In Events
	RECURRING_MANDATE_IN_PORT_SUCCESS("Automatic Payment for {PayeeName} is now on Paytm App",
			"Automatic Payment is now on Paytm App", "Automatic Payment Moved", null),

	// Port-Out Events
	RECURRING_MANDATE_OUT_PORT_SUCCESS("Automatic Payment for {PayeeName} is no longer on Paytm App",
			"Automatic Payment is no longer on Paytm App", "Automatic Payment Moved out of Paytm", null),

	RECURRING_MANDATE_OUT_PORT_FAILURE("Request to move your automatic payment for {PayeeName} is failed",
			"Transfer request for Automatic Payment failed", "Automatic Payment transfer failed", null);

	private final String message;

	private final String defaultMsg;

	private final String title;

	private final String infoLine;

	public static MandateHistoryMessageEnum getMessageByKey(final String key) {
		for (MandateHistoryMessageEnum messageEnum : MandateHistoryMessageEnum.values()) {
			if (messageEnum.name().equalsIgnoreCase(key)) {
				return messageEnum;
			}
		}
		return null;
	}

	public static String getTitleByKey(final String key) {
		for (MandateHistoryMessageEnum messageEnum : MandateHistoryMessageEnum.values()) {
			if (messageEnum.name().equalsIgnoreCase(key)) {
				return messageEnum.getTitle();
			}
		}
		return null;
	}

	public static String getInfoLineByKey(final String key) {
		for (MandateHistoryMessageEnum messageEnum : MandateHistoryMessageEnum.values()) {
			if (messageEnum.name().equalsIgnoreCase(key)) {
				return messageEnum.getInfoLine();
			}
		}
		return null;
	}

}
