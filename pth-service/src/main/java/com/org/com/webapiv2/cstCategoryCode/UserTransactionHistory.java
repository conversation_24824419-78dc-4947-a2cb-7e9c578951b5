package com.org.com.webapiv2.cstCategoryCode;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import jakarta.persistence.Id;
import lombok.Data;

@Data
public class UserTransactionHistory {

	@Id
	@JsonProperty("txn_id")
	private String id;

	@JsonProperty("org_txn_id")
	private String orgTxnId;

	private String channel;

	@JsonProperty("payee_user_id")
	private Long payeeUserId;

	@JsonProperty("payee_cust_id")
	private String payeeCustId;

	@JsonProperty("payer_user_id")
	private Long payerUserId;

	@JsonProperty("payer_cust_id")
	private String payerCustId;

	@JsonProperty("category")
	private String category;

	@JsonProperty("npci_resp_code")
	private String npciRespCode;

	private String amount;

	private Date tdate;

	@JsonProperty("exp_after_min")
	private Long expAfterMin;

	@JsonProperty("merchant_id")
	private String merchantId;

	@JsonProperty("merchant_txn_id")
	private String merchantTxnId;

	private String note;

	@JsonProperty("payee_aadhar")
	private String payeeAadhar;

	@JsonProperty("payee_aadhar_iin")
	private String payeeAadharIin;

	@JsonProperty("payee_account")
	private String payeeAccount;

	@JsonProperty("payee_account_type")
	private String payeeAccountType;

	@JsonProperty("payee_account_ifsc")
	private String payeeAccountIfsc;

	@JsonProperty("payee_mcc")
	private String payeeMcc;

	@JsonProperty("payee_mobile")
	private String payeeMobile;

	@JsonProperty("payee_name")
	private String payeeName;

	@JsonProperty("payee_type")
	private String payeeType;

	@JsonProperty("payee_va")
	private String payeeVa;

	@JsonProperty("payer_aadhar")
	private String payerAadhar;

	@JsonProperty("payer_aadhar_iin")
	private String payerAadharIin;

	@JsonProperty("payer_account")
	private String payerAccount;

	@JsonProperty("payer_account_type")
	private String payerAccountType;

	@JsonProperty("payer_account_ifsc")
	private String payerAccountIfsc;

	@JsonProperty("payer_mcc")
	private String payerMcc;

	@JsonProperty("payer_mobile")
	private String payerMobile;

	@JsonProperty("payer_name")
	private String payerName;

	@JsonProperty("payer_type")
	private String payerType;

	@JsonProperty("payer_va")
	private String payerVa;

	@JsonProperty("ref_id")
	private String refId;

	@JsonProperty("ref_url")
	private String refUrl;

	@JsonProperty("rrn")
	private String rrn;

	private String status;

	private String type;

	@JsonProperty("payee_rev_resp_code")
	private String payeeRevRespCode;

	@JsonProperty("payer_rev_resp_code")
	private String payerRevRespCode;

	@JsonProperty("payee_resp_code")
	private String payeeRespCode;

	@JsonProperty("payer_resp_code")
	private String payerRespCode;

	@JsonProperty("irc")
	private String irc;

	@JsonProperty("collect_identifier")
	private String collectIdentifier;

	@JsonProperty("update_tdate")
	private Date updateTdate;

	@JsonProperty("source")
	private String source;

	@JsonProperty("business_type")
	private String businessType;

	@JsonProperty("first_phase")
	private String firstPhase;

	@JsonProperty("npci_ts")
	private Date npciTs;

	@JsonProperty("expire_on")
	private Date expireOn;

	@JsonProperty("application_updated_on")
	private Date applicationUpdatedOn;

	@JsonProperty("ref_category")
	private String refCategory;

	@JsonProperty("initiation_mode")
	private String initiationMode;

	@JsonProperty("purpose")
	private String purpose;

	private String umn;

	@JsonProperty("mandate_meta")
	private MandateMeta mandateMeta;

	@JsonProperty("merchant_sub_category")
	private String merchantSubCategory;

	@JsonProperty("theme_id")
	private String themeId;

	@JsonProperty("external_status")
	private String externalStatus;

	@JsonProperty("merchant_order_id")
	private String merchantOrderId;

	@JsonProperty("merchant_order_status_sync_flag")
	private String merchantOrderStatusSyncFlag;

	@JsonProperty("on_us_merchant")
	private String onUsMerchant;

	@JsonProperty("is_verified_merchant")
	private String isVerifiedMerchant;

	@JsonProperty("verification_state")
	private String verificationState;

	@JsonProperty("is_user_payer")
	private boolean isUserPayer;

	@JsonProperty("is_old_app_for_cta")
	private boolean isOldAppForCta;

	@JsonProperty("response_code")
	private String responseCode;

	@JsonProperty("response_message")
	private String responseMessage;

	@JsonProperty("app_name")
	private String appName;

	@JsonProperty("app_os")
	private String appOs;

	@JsonProperty("app_version")
	private String appVersion;

	@JsonProperty("post_txn_service_type")
	private String postTxnServiceType;

	@JsonProperty("credit_card_last_two_digits")
	private String creditCardLastTwoDigits;

}
