package com.org.com.webapiv2.cstCategoryCode;

import com.google.common.base.Strings;
import java.util.Objects;

import com.org.com.webapiv2.dto.upiSearch.ResponseTxn;
import com.org.com.webapiv2.dto.upiSearch.UpiSearchRequest;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import static com.org.com.webapiv2.cstCategoryCode.Constants.PAYTM_PPI_WALLET_IFSC;
import static com.org.com.webapiv2.cstCategoryCode.Constants.PPBL_IFSC;

@Component
@Slf4j
public class CategoryInformation {

	private static final String DEFAULT_KEY_FOR_CACHE_WITH_PAYMENT_INSTRUMENT_SUPPORT = "DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT";

	private static final String DEFAULT_KEY_FOR_CACHE = "DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT_DEFAULT";

	private static final String DEFAULT = "DEFAULT";

	private static final String MESSAGE_TYPE = "PASSBOOK";

	private static boolean cstIsCategoryPaymentInstrumentUse;

	@Value("${cst.category.is.payment.instrument.use:false}")
	public void setEnableCstPaymentInstrument(final boolean cstIsCategoryPaymentInstrumentUse) {
		this.cstIsCategoryPaymentInstrumentUse = cstIsCategoryPaymentInstrumentUse;
	}

	public static Enums.PaymentInstrument getPaymentInstrument(final UserTransactionHistory transaction,
			final boolean isPayer) {
		String participantIfsc = getParticipantIfsc(transaction, isPayer);
		if (PAYTM_PPI_WALLET_IFSC.equalsIgnoreCase(participantIfsc)) {
			return Enums.PaymentInstrument.PPBLWALLET;
		}
		else if (PPBL_IFSC.equalsIgnoreCase(participantIfsc)) {
			return Enums.PaymentInstrument.PPBL;
		}
		else {
			return Enums.PaymentInstrument.NONPPBL;
		}
	}

	public static String getParticipantIfsc(final UserTransactionHistory transaction, final boolean isPayer) {
		return isPayer ? transaction.getPayerAccountIfsc() : transaction.getPayeeAccountIfsc();
	}

	private static String getCategory(final String category) {

		Enums.TxnInfoCategory txninfoCategoryEnum = com.google.common.base.Enums
			.getIfPresent(Enums.TxnInfoCategory.class, Strings.nullToEmpty(category))
			.orNull();

		return Objects.nonNull(txninfoCategoryEnum) ? txninfoCategoryEnum.name() : DEFAULT;
	}

	public static String getCategory(final UpiSearchRequest request, final ResponseTxn responseTxn) {
		UserTransactionHistory userTxnHistory = ConvertUthReponseToUpi.convert(responseTxn);
		return getCategory(request, userTxnHistory);
	}

	public static String getCategory(final UpiSearchRequest request, final UserTransactionHistory txnHistory) {
		boolean isPayer = isPayer(request, txnHistory);
		return getDataFromCache(isPayer, txnHistory);
	}

	private static boolean isPayer(final UpiSearchRequest uthDto, final UserTransactionHistory transaction) {

		Boolean isUserPayer = false;

		// private String payerCustId;
		String payerUserId = transaction.getPayerCustId();
		String payeeUserId = transaction.getPayeeCustId();

		/**
		 * if (ObjectUtils.nullSafeEquals(payeeUserId, payerUserId) && uthDto.getFilter()
		 * != null && uthDto.getFilter().getTransactionType() != null) {
		 *
		 * if ("DEBIT".equalsIgnoreCase(uthDto.getFilter().getTransactionType())) {
		 * isUserPayer = true; }
		 *
		 * } else
		 **/

		if (ObjectUtils.nullSafeEquals(uthDto.getRequestBody().getUserId(), payerUserId)) {
			isUserPayer = true;
		}

		log.info("user is payer {}", isUserPayer);
		return isUserPayer;
	}

	private static String getDataFromCache(final boolean isPayer, final UserTransactionHistory transaction) {
		String businessCategory = CategoryConfigurationUtil.getBusinessCategory(transaction.getBusinessType());
		CategoryParticipant searchKeyDto = CategoryParticipant.builder()
			.participantType(getPayerOrPayee(isPayer))
			.category(getCategory(transaction.getCategory()))
			.status(checkStatus(transaction, isPayer))
			.type(getType(transaction.getType()))
			.merchantSubCategory(getMerchantSubCategory(transaction.getPayeeVa(), transaction.getMerchantSubCategory(),
					transaction.getPayeeType()))
			.initiator(getInitiator(transaction))
			.messageType(MESSAGE_TYPE)
			.paymentInstrument(getPaymentInstrument(transaction, isPayer).name())
			.responseConstant("DEFAULT")
			.build();

		String key = searchKeyDto.getKey("_", searchKeyDto.getPaymentInstrument(), businessCategory,
				cstIsCategoryPaymentInstrumentUse);

		log.info("Search Key DTO is {}", key);
		String category = getCategoryFromCache(key);

		if (StringUtils.isEmpty(category)) {
			category = getCategoryFromCache(DEFAULT_KEY_FOR_CACHE);
		}
		return category;
	}

	private static String getCategoryFromCache(final String key) {
		CategoryParticipant categoryParticipant = CategoryConfigurationUtil.categoryMap.get(key);
		if (categoryParticipant != null) {
			CategoryNarration categoryNarration = CategoryConfigurationUtil.categoryNarrationMap
				.get(categoryParticipant.getNarrationId());
			return categoryNarration.getPassbookCategory();
		}
		return null;
	}

	// Get status of the transaction.
	public static String checkStatus(final UserTransactionHistory transaction, final boolean isPayer) {
		String status = transaction.getStatus();
		if (Enums.TxnInfoStatus.PENDING.name().equalsIgnoreCase(status)
				&& isDeemedTransaction(transaction, getCurrentTxnStatus(transaction))) {

			status = Enums.TxnInfoStatus.DEEMED.name();
		}
		else if (Enums.TxnInfoStatus.FAILURE.name().equalsIgnoreCase(getCurrentTxnStatus(transaction))) {
			// getFailedTxnState(passbookTransactions, transaction, isPayer);
			status = Enums.TxnInfoStatus.FAILURE.name();
		}
		else if (Enums.TxnInfoStatus.SUCCESS.name().equalsIgnoreCase(status)
				&& Enums.TxnInfoBusinessType.MANDATE.name().equalsIgnoreCase(transaction.getBusinessType())
				&& transaction.getPurpose() != null
				&& Enums.Purpose.SUBSCRIPTION.getValue().equals(transaction.getPurpose())) {
			return status;
		}
		else if (Enums.TxnInfoStatus.SUCCESS.name().equalsIgnoreCase(status)
				&& Enums.TxnInfoBusinessType.MANDATE.name().equalsIgnoreCase(transaction.getBusinessType())) {

			MandateMeta mandateMeta = getMandateDetails(transaction);
			if (!ObjectUtils.isEmpty(mandateMeta) && StringUtils.isNotEmpty(mandateMeta.getMandateState())) {
				switch (Enums.MandateState.valueOf(mandateMeta.getMandateState())) {

					case FAILED_UPDATE_ACTIVE:
						transaction.setType(Enums.TxnInfoType.UPDATE.name());
						return Enums.TxnInfoStatus.FAILURE.name();

					case FAILED_REVOKE_ACTIVE:
						transaction.setType(Enums.TxnInfoType.REVOKE.name());
						return Enums.TxnInfoStatus.FAILURE.name();

					case FAILED_EXECUTION_ACTIVE:
						transaction.setType(Enums.TxnInfoType.COLLECT.name());
						return Enums.TxnInfoStatus.FAILURE.name();

					case PENDING_EXECUTE:
						transaction.setType(Enums.TxnInfoType.COLLECT.name());
						return Enums.TxnInfoStatus.PENDING.name();

					default:
						break;
				}
			}
		}

		return status.toUpperCase();
	}

	protected static String getCurrentTxnStatus(final UserTransactionHistory transaction) {
		String status = transaction.getStatus();
		String requiredStatus = status;

		if ("REJECT".equalsIgnoreCase(status) || "FAILURE".equalsIgnoreCase(status)) {
			requiredStatus = "FAILURE";
		}
		return requiredStatus;
	}

	private static boolean isDeemedTransaction(final UserTransactionHistory transaction,
			final String passbookTransStatus) {

		final String txnStatus = transaction.getStatus();
		final String passBookStatus = passbookTransStatus;

		return StringUtils.isNotBlank(txnStatus) && StringUtils.isNotBlank(passBookStatus)
				&& Enums.TxnInfoStatus.DEEMED.name().equalsIgnoreCase(txnStatus)
				&& Enums.TxnInfoStatus.PENDING.name().equalsIgnoreCase(passBookStatus);
	}

	private static String getInitiator(final UserTransactionHistory transaction) {

		if (Enums.TxnInfoBusinessType.MANDATE.name().equalsIgnoreCase(transaction.getBusinessType())) {

			if (!ObjectUtils.isEmpty(transaction.getMandateMeta())) {
				if (Enums.MandateState.DECLINED.name()
					.equalsIgnoreCase(transaction.getMandateMeta().getMandateState())) {
					return Enums.TxnParticipantType.PAYEE.name();
				}
				else if (Enums.MandateState.CANCELLED.name()
					.equalsIgnoreCase(transaction.getMandateMeta().getMandateState())) {
					return Enums.TxnParticipantType.PAYER.name();
				}
			}
		}

		return TransactionHistoryConstants.DEFAULT_INITIATOR;
	}

	private static String getType(final String type) {
		log.info("Type :{}", type);
		Enums.TxnInfoType txnTypeEnum = com.google.common.base.Enums
			.getIfPresent(Enums.TxnInfoType.class, Strings.nullToEmpty(type))
			.orNull();

		return Objects.nonNull(txnTypeEnum) ? txnTypeEnum.name() : TransactionHistoryConstants.DEFAULT;
	}

	/*
	 * FINDING PAYTM OFFLINE , PAYTM ONLINE (those have merchant subcategory), NON_PAYTM ,
	 * DEFAULT MERCHANT (only ends with @paytm)
	 */
	private static String getMerchantSubCategory(final String payeeVa, final String merchantSubCategory,
			final String payeeType) {

		String category = TransactionHistoryConstants.DEFAULT_SUB_CATEGORY;

		if (Enums.UserType.ENTITY.name().equalsIgnoreCase(payeeType)) {

			category = TransactionHistoryConstants.MERCHANT_DEFAULT_SUB_CATEGORY;

			if (StringUtils.isNotBlank(merchantSubCategory)) {

				category = merchantSubCategory;

			}
			else if (PassbookUtil.isPaytmOfflineMerchant(payeeVa)) {

				category = TransactionHistoryConstants.PAYTM_OFFLINE_MERCHANT;

			}
			else if (PassbookUtil.isNonPaytmMerchant(payeeVa)) {

				category = TransactionHistoryConstants.NON_PAYTM_MERCHANT;

			}
		}

		return category;
	}

	private static String getPayerOrPayee(final boolean isPayer) {
		return isPayer ? Enums.TxnParticipantType.PAYER.name() : Enums.TxnParticipantType.PAYEE.name();
	}

	public static MandateMeta getMandateDetails(final UserTransactionHistory transaction) {
		if (Enums.TxnInfoBusinessType.MANDATE.name().equalsIgnoreCase(transaction.getBusinessType())) {

			String businessType = Enums.TxnInfoBusinessType.MANDATE.name();

			MandateMeta esMandateMeta = transaction.getMandateMeta();

			MandateMeta mandateMeta = new MandateMeta();

			if (esMandateMeta != null) {

				log.info("mandate data is present in UTH index: {}", esMandateMeta);

				mandateMeta.setMandateState(transaction.getMandateMeta().getMandateState());
				mandateMeta.setValidityStartDate(transaction.getMandateMeta().getValidityStartDate());
				mandateMeta.setValidityEndDate(transaction.getMandateMeta().getValidityEndDate());
				setTypeForMandateTxns(mandateMeta, transaction.getPurpose(), businessType);
			}

			return mandateMeta;
		}
		return null;
	}

	private static void setTypeForMandateTxns(final MandateMeta mandateMeta, final String purpose,
			final String businessType) {
		// any mandate type is one time by default
		if (Enums.TxnInfoBusinessType.MANDATE.name().equals(businessType)) {
			mandateMeta.setType(Enums.MandateType.ONETIME.name());
		}
		// if mandate and purpose code is 14 then its a recurring mandate.
		if (Enums.TxnInfoBusinessType.MANDATE.name().equals(businessType)
				&& Enums.Purpose.SUBSCRIPTION.getValue().equals(purpose)) {
			mandateMeta.setType(Enums.MandateType.RECURRING.name());
		}
	}

}
