package com.org.com.webapiv2.service.impl.upiApis;

import static com.org.panaroma.commons.constants.WebConstants.OK_200;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.SUCCESS;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.INTERNAL_SERVER_ERROR;

import com.org.com.webapiv2.dao.IEsDao;
import com.org.com.webapiv2.dto.upiSearch.UpiSearchRequest;
import com.org.com.webapiv2.dto.upiSearch.UpiSearchResponse;
import com.org.com.webapiv2.parentResponse.ParentResponse;
import com.org.com.webapiv2.utility.ConversionUtilityForProgressiveSearch;
import com.org.com.webapiv2.utility.ResponseBuilderUnified;
import com.org.com.webapiv2.utility.UpiMiniStatementSearchContext;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.exceptionhandler.ExceptionHandlerUtil;
import com.org.panaroma.commons.exceptionhandler.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.web.repo.EsProgressiveRecordsSearchingRepoNonMono;
import java.util.HashMap;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class UpiMiniStatementService {

	private final IEsDao esDao;

	private final UpiMiniStatementSearchContext miniStatementSearchContext;

	private final EsProgressiveRecordsSearchingRepoNonMono repoNonMono;

	@Autowired
	public UpiMiniStatementService(final IEsDao esDao, final UpiMiniStatementSearchContext miniStatementSearchContext,
			@Qualifier("EsProgressiveSearchingHistoryRepoNonMono") final EsProgressiveRecordsSearchingRepoNonMono repoNonMono) {
		this.esDao = esDao;
		this.miniStatementSearchContext = miniStatementSearchContext;
		this.repoNonMono = repoNonMono;
	}

	public ParentResponse getTxnHistory(final UpiSearchRequest request) {
		try {
			SearchContext searchContext = miniStatementSearchContext.setSearchContext(request.getRequestBody());
			com.org.panaroma.web.SearchContext webSearchContext = ConversionUtilityForProgressiveSearch
				.convertSearchcontext(searchContext);
			RepoResponseSearchApiDto responseSearchApiDto = repoNonMono.searchWithoutMono(webSearchContext,
					new HashMap<>(), null);
			// EsRepoResponseDto repoResponse = esDao.getResponseTxn(searchContext,
			// request.getRequestBody().getPaginationParams());
			UpiSearchResponse searchResponse = ResponseBuilderUnified.buildUpiSearchResponse(request,
					responseSearchApiDto, searchContext);
			ParentResponse response = ParentResponse.builder()
				.status(SUCCESS)
				.httpCode(OK_200)
				.response(searchResponse)
				.build();
			// As response logs are getting printed using Logbook. search "Logbook" &&
			// "response" to check these logs.
			// log.info("Sending response for txnList api: {}", response);
			return response;

		}
		catch (Exception e) {
			if (!(e instanceof PanaromaException
					|| e instanceof com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException)) {
				log.error("Unhandled exception : {}", CommonsUtility.exceptionFormatter(e));
			}

			if (e instanceof com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException) {
				com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException panaromaException = (com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException) e;
				throw panaromaException;
			}
			throw ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE, INTERNAL_SERVER_ERROR, "");
		}
	}

}
