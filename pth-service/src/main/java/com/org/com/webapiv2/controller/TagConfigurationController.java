package com.org.com.webapiv2.controller;

import com.org.com.webapiv2.dto.tagConfiguration.TagConfigurationDeleteRequestDto;
import com.org.com.webapiv2.dto.tagConfiguration.TagConfigurationDeleteResponseDto;
import com.org.com.webapiv2.dto.tagConfiguration.TagConfigurationRequestDto;
import com.org.com.webapiv2.dto.tagConfiguration.TagConfigurationResponseDto;
import com.org.com.webapiv2.service.TagConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/pth/ext/tag/config/v1")
public class TagConfigurationController {

	private final TagConfigurationService tagConfigurationService;

	@Autowired
	public TagConfigurationController(TagConfigurationService tagConfigurationService) {
		this.tagConfigurationService = tagConfigurationService;
	}

	@PostMapping("/update")
	public TagConfigurationResponseDto createUpdateTagConfiguration(
			@RequestBody TagConfigurationRequestDto requestDto) {
		return tagConfigurationService.createUpdateTagConfiguration(requestDto);
	}

	@PostMapping("/delete")
	public TagConfigurationDeleteResponseDto deleteTagConfiguration(
			@RequestBody TagConfigurationDeleteRequestDto requestDto) {
		return tagConfigurationService.deleteTagConfiguration(requestDto);
	}

}
