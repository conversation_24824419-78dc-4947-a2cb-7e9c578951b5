package com.org.com.webapiv2.service;

import com.org.com.webapiv2.dto.tagConfiguration.TagConfigurationDeleteRequestDto;
import com.org.com.webapiv2.dto.tagConfiguration.TagConfigurationDeleteResponseDto;
import com.org.com.webapiv2.dto.tagConfiguration.TagConfigurationRequestDto;
import com.org.com.webapiv2.dto.tagConfiguration.TagConfigurationResponseDto;
import com.org.panaroma.commons.entity.TagConfigurationEntity;
import com.org.panaroma.commons.enums.TagConfigurationStatusEnum;
import com.org.panaroma.commons.repository.TagConfigurationRepo;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class TagConfigurationService {

	private final TagConfigurationRepo tagConfigurationRepo;

	@Autowired
	public TagConfigurationService(TagConfigurationRepo tagConfigurationRepo) {
		this.tagConfigurationRepo = tagConfigurationRepo;
	}

	public TagConfigurationResponseDto createUpdateTagConfiguration(TagConfigurationRequestDto requestDto) {
		log.info("Received request to create/update tag configuration: {}", requestDto);
		TagConfigurationEntity entity = null;
		if (requestDto.getId() != null) {
			entity = tagConfigurationRepo.findById(requestDto.getId()).orElseThrow();
		}

		TagConfigurationEntity entityToSave = TagConfigurationRequestDto.toEntity(requestDto, entity);
		entityToSave = tagConfigurationRepo.save(entityToSave);
		log.info("Tag configuration saved with id: {}", entityToSave.getId());
		return TagConfigurationResponseDto.builder()
			.message("Tag configuration enabled successfully with id: " + entityToSave.getId())
			.build();
	}

	public TagConfigurationDeleteResponseDto deleteTagConfiguration(TagConfigurationDeleteRequestDto requestDto) {
		log.info("Received request to delete tag configuration with id: {}", requestDto.getId());

		TagConfigurationEntity entity = tagConfigurationRepo.findById(requestDto.getId()).orElseThrow();

		entity.setStatus(TagConfigurationStatusEnum.DISABLE);
		tagConfigurationRepo.save(entity);

		return TagConfigurationDeleteResponseDto.builder()
			.message("Tag configuration disabled successfully of id : " + requestDto.getId())
			.build();
	}

}
