package com.org.com.webapiv2.dto.upiCstPanel;

import com.org.panaroma.commons.dto.kafkaPushAPI.UpiDataDto;
import com.org.panaroma.commons.utils.JsonUtils;
import java.util.Map;
import lombok.Data;

@Data
public class UserInfo {

	String name;

	String vpa;

	String bankAccount;

	String ifsc;

	String mobile;

	String mcc;

	String entityType;

	String oauthCustId;

	String reversalRespCode;

	String respCode;

	Map<String, String> contextMap;

	UpiDataDto upiData;

	Long upiAccRefId;

	Long txnDateEpoch;

	Long updatedDateEpoch;

	// Added for PTH-1318
	String accType;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
