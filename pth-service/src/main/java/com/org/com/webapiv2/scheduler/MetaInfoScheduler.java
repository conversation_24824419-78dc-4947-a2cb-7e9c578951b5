package com.org.com.webapiv2.scheduler;

import static com.org.com.webapiv2.constants.SchedulerConstants.SchedulerType.META_INFO_SCHEDULER;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.USER_TAGS_SUMMARY_FROM_DATE_IN_DAYS;
import static com.org.panaroma.commons.constants.WebConstants.AWS_ES_FROM_DATE;
import static com.org.panaroma.commons.constants.WebConstants.DATE_RANGE_FROM_DATE_LISTING_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.FROM_DATE_LISTING_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_FROM_DATE_FORMAT;

import com.org.com.webapiv2.dto.metaData.MetaInfo;
import com.org.com.webapiv2.scheduler.service.SchedulerService;
import com.org.com.webapiv2.utility.MetaDataUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import java.time.LocalDate;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class MetaInfoScheduler implements SchedulerService {

	@Getter
	MetaInfo metaInfo;

	@Value("${cache.ttl.for.metadata.at.app.side}")
	Long cacheExpiryTimeAtApp;

	@Autowired
	MetaDataUtility metaDataUtility;

	@Autowired
	ConfigurablePropertiesHolder configurablePropertiesHolder;

	@PostConstruct
	public void init() {
		metaInfo = new MetaInfo();
		setMetaInfo();
	}

	public void setMetaInfo() {
		try {
			metaInfo.setCacheTtl(cacheExpiryTimeAtApp);
			metaInfo.setFromDateFormat(LISTING_FROM_DATE_FORMAT);
			metaInfo.setTxnTypeToAccountLabelMap(metaDataUtility.getSourceAccountLabelMapBasedOnTxnType());
			// Changing it as we don't have dc-from-date-listing-filter.
			// String filterFromDateDays = configurablePropertiesHolder
			// .getProperty(DC_FROM_DATE_LISTING_FILTER, String.class);
			String dateRangeFilterFromDateDays = configurablePropertiesHolder
				.getProperty(DATE_RANGE_FROM_DATE_LISTING_FILTER, String.class);
			Long dateRangeFromDateForFilter = DateTimeUtility
				.getTimeFromStringFormattedDate(dateRangeFilterFromDateDays);
			metaInfo.setDateRangeFilterFromDateMillis(dateRangeFromDateForFilter);
			String listingFromDateString = configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER,
					String.class);
			metaInfo.setFromDate(listingFromDateString);
			metaInfo.setCurrentMonth(LocalDate.now().getMonthValue());
			metaInfo.setCurrentYear(LocalDate.now().getYear());
			Long fromDateInEpoch = DateTimeUtility.getTimeFromStringFormattedDate(listingFromDateString);
			metaInfo.setFromDateInEpochMillis(fromDateInEpoch);

			// Set User Tags Summary from date
			Integer userTagsSummaryFromDateInDays = configurablePropertiesHolder
				.getPropertyWithDefaultValue(USER_TAGS_SUMMARY_FROM_DATE_IN_DAYS, Integer.class, 180);
			long userTagsSummaryFromDateEpochMillis = DateTimeUtility
				.getEpochMillisOfFirstDateOfMonth(userTagsSummaryFromDateInDays);

			metaInfo.setFromDateForUserTagsSummaryInMillis(userTagsSummaryFromDateEpochMillis);
		}
		catch (Exception e) {
			log.error("Exception while setting metaInfo Exception: {}", CommonsUtility.exceptionFormatter(e));
		}
	}

	private void updateMetaDataInfo() throws Exception {
		String listingFromDateString = configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class);
		String listingAwsFromDateString = configurablePropertiesHolder.getProperty(AWS_ES_FROM_DATE, String.class);

		String fromDate = listingAwsFromDateString.compareTo(listingFromDateString) > 0 ? listingAwsFromDateString
				: listingFromDateString;

		if (StringUtils.isBlank(metaInfo.getFromDate()) || !metaInfo.getFromDate().equals(fromDate)) {
			setMetaInfo();
		}
	}

	@Override
	public void scheduledTrigger() {
		try {
			updateMetaDataInfo();
		}
		catch (Exception e) {
			log.error("Error while refreshing meta info scheduler: {}", CommonsUtility.exceptionFormatter(e));
		}
	}

	@Override
	public String getSchedulerType() {
		return META_INFO_SCHEDULER;
	}

}
