package com.org.com.webapiv2.cstCategoryCode;;

import java.math.BigDecimal;

public interface CommonConstants {

	String SUCCESS = "SUCCESS";

	String PENDING = "PENDING";

	String DEEMED_FAILURE = "DEEMED_FAILURE";

	String FAILURE = "FAILURE";

	String DEEMED = "DEEMED";

	String REJECT = "REJECT";

	String P2P = "P2P";

	String P2M = "P2M";

	String VPA2MERCHANT = "VPA2MERCHANT";

	String DEFAULT = "DEFAULT";

	BigDecimal MAX_AMOUNT_LIMIT_FOR_RECURRING = new BigDecimal(2000);

	String IMP_INFO_AMOUNT = "<Amount>";

	String IMP_INFO_AMOUNT_RULE = "<AmountRule>";

	String IMP_INFO_RECURRENCE = "<Recurrence>";

	String IMP_INFO_VALIDITY_START_DATE = "<ValidityStartDate>";

	String IMP_INFO_VALIDITY_END_DATE = "<ValidityEndDate>";

	String IMP_INFO_LAST_VALIDITY_END_DATE = "<LastValidityEndDate>";

	String IMP_INFO_LAST_AMOUNT = "<LastAmount>";

	String IMP_INFO_PAUSE_END_DATE = "<pauseEndDate>";

	Integer DAYS_IN_WEEK = 7;

	String ASPRESENTED_RECURRENCE = "As and When Presented";

	String ES_SUCCESS_STATUS = "0";

	String ECOSYSTEM_STATIC_MESSAGE = "STATIC";

	String UPI_CSTBOT = "UPI_CSTBOT";

	String UPI = "UPI";

}
