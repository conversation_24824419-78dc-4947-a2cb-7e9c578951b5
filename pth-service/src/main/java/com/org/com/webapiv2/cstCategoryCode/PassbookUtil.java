package com.org.com.webapiv2.cstCategoryCode;;

import org.apache.commons.lang3.StringUtils;

public class PassbookUtil {

	public static final String PAYER_NAME = "PAYER_NAME";

	public static final String PAYER_BANK_NAME = "PAYER_BANK_NAME";

	public static final String PAYEE_NAME = "PAYEE_NAME";

	public static final String PAYEE_BANK_NAME = "PAYEE_BANK_NAME";

	public static final String PAYTM_VPA_HANDLE = "@paytm";

	public static final String PAYTM_QR_VPA = "paytmqr";

	private PassbookUtil() {

	}

	public static boolean isPaytmOfflineMerchant(final String payeeVa) {
		return StringUtils.isNotBlank(payeeVa) && payeeVa.toLowerCase().trim().startsWith(PAYTM_QR_VPA)
				&& payeeVa.trim().endsWith(PAYTM_VPA_HANDLE);
	}

	public static boolean isNonPaytmMerchant(final String payeeVa) {
		return StringUtils.isNotBlank(payeeVa) && !payeeVa.trim().endsWith(PAYTM_VPA_HANDLE);
	}

}
