package com.org.com.webapiv2.controller.v4;

import com.org.com.webapiv2.dto.metaData.MetaDataRequest;
import com.org.com.webapiv2.dto.metaData.MetaDataResponse;
import com.org.com.webapiv2.service.impl.metaData.MetaDataServiceV4;
import com.org.com.webapiv2.service.metaData.IMetaDataService;
import com.org.panaroma.commons.enums.Client;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.org.com.webapiv2.constants.Constants.APPLICATION_JSON_CHARSET_UTF_8;
import static com.org.panaroma.commons.constants.WebConstants.AUTHORIZATION;
import static com.org.panaroma.commons.constants.WebConstants.ENTITY_ID;

@RestController
@RequestMapping({ "/pth/ext/v4" })
@Log4j2
public class MetaDataControllerV4 {

	private MetaDataServiceV4 metaDataService;

	@Autowired
	public MetaDataControllerV4(final MetaDataServiceV4 metaDataService) {
		this.metaDataService = metaDataService;
	}

	@GetMapping(value = { "/metaData" }, produces = APPLICATION_JSON_CHARSET_UTF_8)
	public ResponseEntity getMetaData(@RequestHeader(AUTHORIZATION) final Map<String, String> tokenMap,
			@RequestHeader(value = ENTITY_ID, required = false) final String entityId,
			@RequestParam(value = "client", required = false) final Client client,
			@RequestParam(value = "activeFilters", defaultValue = "true") final String activeFilters,
			@RequestParam(value = "activeSearch", defaultValue = "true") final String activeSearch,
			@RequestParam(value = "type", required = false) final String type,
			@RequestParam(value = "version", required = false) final String appVersion,
			@RequestParam(value = "metadataApiVersion", required = false) final String metadataApiVersion)
			throws Exception {

		// As response logs are getting printed using Logbook. search "Logbook" &&
		// "response" to check these logs.
		// log.info("request received to fetch metaData with types {}", type);

		MetaDataRequest request = MetaDataRequest.builder()
			.entityId(entityId)
			.client(client)
			.activeFilters(activeFilters)
			.activeSearch(activeSearch)
			.type(type)
			.appVersion(appVersion)
			.metadataApiVersion(metadataApiVersion)
			.build();

		MetaDataResponse response = metaDataService.getMetaData(request, tokenMap);

		// As response logs are getting printed using Logbook. search "Logbook" &&
		// "response" to check these logs.
		/*
		 * log.
		 * info("MetaData Response : {}, type : {}, activeFilters : {}, activeSearch : {} "
		 * , response, type, activeFilters, activeSearch);
		 */

		return ResponseEntity.ok().body(response);
	}

}
