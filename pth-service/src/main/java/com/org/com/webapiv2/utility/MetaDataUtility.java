package com.org.com.webapiv2.utility;

import static com.org.panaroma.commons.constants.LocalizationConstants.LOCALE;
import static com.org.panaroma.commons.constants.WebConstants.FROM_DATE_LISTING_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_FROM_DATE_FORMAT;
import static com.org.panaroma.commons.constants.WebConstants.TO;

import com.org.com.webapiv2.dto.metaData.FilterNode;
import com.org.com.webapiv2.dto.metaData.FilterValueNode;
import com.org.com.webapiv2.dto.metaData.RoutingInfo;
import com.org.com.webapiv2.dto.metaData.Validation;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.NumberFormatUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.commons.constants.ConfigurationPropertiesConstants;
import com.org.com.webapiv2.dto.metaData.ApiUrlsInfo;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class MetaDataUtility {

	private final List<String> whiteListedMetaDataApiUserId;

	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	private Integer minAmountLimit;

	private Integer maxAmountLimit;

	private final RolloutStrategyHelper rolloutStrategyHelper;

	@Autowired
	public MetaDataUtility(@Value("${metaDataApi.whitelisted.users}") final List<String> whiteListedMetaDataApiUserId,
			@Value("${min-amount-limit-for-amount-range-filter}") final Integer minAmountLimit,
			@Value("${max-amount-limit-for-amount-range-filter}") final Integer maxAmountLimit,
			final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final RolloutStrategyHelper rolloutStrategyHelper) {
		this.whiteListedMetaDataApiUserId = whiteListedMetaDataApiUserId;
		this.minAmountLimit = minAmountLimit;
		this.maxAmountLimit = maxAmountLimit;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
	}

	public boolean isNodeWhitelistedForUser(final Integer whitelistedUsersCustIds, final Double whitelistingPercentage,
			final String userId) {
		return Utility.isUserWhitelistedFromPercentageOrUserList(whitelistingPercentage, whitelistedUsersCustIds,
				whiteListedMetaDataApiUserId, userId);
	}

	/*
	 * this method sets the narration to be shown in listing based on txnType for
	 * P2P_OUTWARD (1) -> show "From" for P2P_INWARD (3) -> show "In"
	 *
	 * If we send the txnType in the response then for that type of txn, FE will use the
	 * narration sent by us in response otherwise it will use the logic based on
	 * txnIndicator
	 */
	public Map<String, String> getSourceAccountLabelMapBasedOnTxnType() {
		return Map.of(TransactionTypeEnum.P2P_INWARD_3P_APP.getTransactionTypeKey().toString(), TO);
	}

	public List<Pair<String, String>> getMonthDisplayNamesAndRequestParamValues(final String listingFromDate)
			throws ParseException {
		long toDate = Instant.now().toEpochMilli();
		long fromDate = new SimpleDateFormat(LISTING_FROM_DATE_FORMAT).parse(listingFromDate).getTime();

		DateFormat displayNameFormat = new SimpleDateFormat("MMMM''yy");
		return getMonthDisplayNamesAndRequestParamPairList(fromDate, toDate, displayNameFormat);
	}

	// method for creating months display name and request param value for date range
	// filter
	private List<Pair<String, String>> getMonthDisplayNamesAndRequestParamPairList(final long fromDate,
			final long toDate, final DateFormat displayNameDateFormat) {

		LocalDateTime toLocalDateTime = Instant.ofEpochMilli(toDate).atZone(ZoneId.systemDefault()).toLocalDateTime();
		toLocalDateTime = toLocalDateTime.withDayOfMonth(toLocalDateTime.toLocalDate().lengthOfMonth());
		toLocalDateTime = toLocalDateTime.withHour(23);
		toLocalDateTime = toLocalDateTime.withMinute(59);
		toLocalDateTime = toLocalDateTime.withSecond(59);
		toLocalDateTime = toLocalDateTime.withNano(999);

		LocalDateTime fromLocalDateTime = Instant.ofEpochMilli(fromDate)
			.atZone(ZoneId.systemDefault())
			.toLocalDateTime();
		List<Pair<String, String>> monthsDisplayAndRequestNameList = new ArrayList<>();

		String displayName;

		// loop for traversing all months from date to current date
		while (fromLocalDateTime.isBefore(toLocalDateTime) || fromLocalDateTime.isEqual(toLocalDateTime)) {
			LocalDate firstDayOfMonth = fromLocalDateTime.toLocalDate();
			LocalDate lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth());
			displayName = displayNameDateFormat
				.format(Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant()));

			// Start day of month epoch
			LocalDateTime startLocalDateTime = LocalDateTime.of(firstDayOfMonth, LocalTime.MIN);

			StringBuilder requestParamValue = new StringBuilder();
			requestParamValue.append(startLocalDateTime.atZone(ZoneId.of("Asia/Kolkata")).toInstant().toEpochMilli());

			// Ending day of month epoch
			LocalDateTime endLocalDateTime = LocalDateTime.of(lastDayOfMonth, LocalTime.MAX);

			requestParamValue.append("-")
				.append(endLocalDateTime.atZone(ZoneId.of("Asia/Kolkata")).toInstant().toEpochMilli());
			monthsDisplayAndRequestNameList.add(new Pair<>(displayName, requestParamValue.toString()));

			fromLocalDateTime = fromLocalDateTime.plusMonths(1);
		}
		return monthsDisplayAndRequestNameList;
	}

	/*
	 * This method returns the FilterNode after replacing all the strings to be displayed
	 * to the customer on his/her app with the values configured on CLS panel for those
	 * strings.
	 *
	 * This method is also called in case of locale = en-IN in metadata api request to
	 * provide the functionality of changing the english values too for these strings
	 * without any BE deployment
	 */
	public void localizeFilterNode(final FilterNode node) {
		if (Objects.nonNull(node)) {
			node.setDisplayName(LocalizedDataCacheService.getLocalizedValue(node.getDisplayNameLocaleKey(),
					node.getDisplayName(), ThreadContext.get(LOCALE)));
			if (Objects.nonNull(node.getValues())) {
				for (FilterValueNode filterValueNode : node.getValues()) {
					if (StringUtils.isNotBlank(filterValueNode.getDisplayNameLocaleKey())) {
						filterValueNode.setDisplayName(
								LocalizedDataCacheService.getLocalizedValue(filterValueNode.getDisplayNameLocaleKey(),
										filterValueNode.getDisplayName(), ThreadContext.get(LOCALE)));
					}
					// Localise filter value node validation message
					Validation filterValueNodeValidation = filterValueNode.getValidation();
					if (Objects.nonNull(filterValueNodeValidation)
							&& StringUtils.isNotBlank(filterValueNodeValidation.getMessageLocaleKey())) {
						String localizedMessage = LocalizedDataCacheService.getLocalizedValue(
								filterValueNodeValidation.getMessageLocaleKey(), filterValueNodeValidation.getMessage(),
								ThreadContext.get(LOCALE));
						if ("APP_MIN_AMOUNT".equals(filterValueNode.getRequestParamValue())) {
							filterValueNodeValidation.setMessage(String.format(localizedMessage, NumberFormatUtility
								.numberInIndianAmountFormatForAmountRangeFilter(minAmountLimit)));
						}
						else if ("APP_MAX_AMOUNT".equals(filterValueNode.getRequestParamValue())) {
							filterValueNodeValidation.setMessage(String.format(localizedMessage, NumberFormatUtility
								.numberInIndianAmountFormatForAmountRangeFilter(maxAmountLimit)));
						}
					}
				}
			}
			// Localise filter node validation message
			Validation filterNodeValidation = node.getValidation();
			if (Objects.nonNull(filterNodeValidation)
					&& StringUtils.isNotBlank(filterNodeValidation.getMessageLocaleKey())) {
				filterNodeValidation
					.setMessage(LocalizedDataCacheService.getLocalizedValue(filterNodeValidation.getMessageLocaleKey(),
							filterNodeValidation.getMessage(), ThreadContext.get(LOCALE)));
			}
		}
	}

	/**
	 * Will return url's of listingV1,detailV1,listingV2,detailV2.
	 */
	public RoutingInfo getRoutingInfo(final String userId) {
		try {
			Long fromDate = new SimpleDateFormat(LISTING_FROM_DATE_FORMAT)
				.parse(configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class))
				.getTime();
			return RoutingInfo.builder()
				.uthV1ListingUrl(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.UTH_V1_LISTING_URL, String.class))
				.uthV1DetailUrl(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.UTH_V1_DETAIL_URL, String.class))
				.uthV2ListingUrl(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.UTH_V2_LISTING_URL, String.class))
				.uthV2DetailUrl(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.UTH_V2_DETAIL_URL, String.class))
				.uthBgAppSyncListingUrl(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.UTH_BG_APP_SYNC_LISTING_URL, String.class))
				.uthV1FromDate(fromDate)
				.pthListingUrlV1(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_URL_V1, String.class))
				.pthListingFilterUrlV1(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_FILTER_URL_V1, String.class))
				.pthListingUpiPassbookUrlV1(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_UPI_PASSBOOK_URL_V1, String.class))
				.pthListingUpiLitePassbookUrlV1(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_UPI_LITE_PASSBOOK_URL_V1, String.class))
				.pthListingUpiCcPassbookUrlV1(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_UPI_CC_PASSBOOK_URL_V1, String.class))
				.pthListingFilterUpiPassbookUrlV1(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V1, String.class))
				.pthListingFilterUpiLitePassbookUrlV1(configurablePropertiesHolder.getProperty(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V1, String.class))
				.pthListingFilterUpiCcPassbookUrlV1(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V1, String.class))
				.pthListingUrlV2(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_URL_V2, String.class))
				.pthListingFilterUrlV2(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_FILTER_URL_V2, String.class))
				.pthListingUpiPassbookUrlV2(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_UPI_PASSBOOK_URL_V2, String.class))
				.pthListingUpiLitePassbookUrlV2(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_UPI_LITE_PASSBOOK_URL_V2, String.class))
				.pthListingUpiCcPassbookUrlV2(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_UPI_CC_PASSBOOK_URL_V2, String.class))
				.pthListingFilterUpiPassbookUrlV2(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V2, String.class))
				.pthListingFilterUpiLitePassbookUrlV2(configurablePropertiesHolder.getProperty(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V2, String.class))
				.pthListingFilterUpiCcPassbookUrlV2(configurablePropertiesHolder
					.getProperty(WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V2, String.class))
				.build();
		}
		catch (Exception e) {
			log.error("Exception while getting fromDate for routing info in metaApi : {}",
					CommonsUtility.exceptionFormatter(e));
		}
		return null;

	}

	public RoutingInfo getRoutingInfoV4() {
		try {
			Long fromDate = new SimpleDateFormat(LISTING_FROM_DATE_FORMAT)
				.parse(configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class))
				.getTime();
			return RoutingInfo.builder()
				.uthV1ListingUrl(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.UTH_V1_LISTING_URL_V4, String.class,
						"https://upi.paytm.com/pth-v2/pth/ext/v4/search"))
				.uthV1DetailUrl(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.UTH_V1_DETAIL_URL_V4, String.class,
						"https://upi.paytm.com/pth/ext/v3"))
				.uthV2ListingUrl(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.UTH_V2_LISTING_URL_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/search"))
				.uthV2DetailUrl(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.UTH_V2_DETAIL_URL_V4, String.class,
						"https://upi.paytm.com/pth/ext/v3"))
				.uthBgAppSyncListingUrl(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.UTH_BG_APP_SYNC_LISTING_URL_V4, String.class,
						"https://upi.paytm.com/pth-v2/pth/ext/v4/bg/listing"))
				.uthV1FromDate(fromDate)
				.pthListingUrlV1(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_URL_V1_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/listing"))
				.pthListingFilterUrlV1(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_URL_V1_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/listing/filter"))
				.pthListingUpiPassbookUrlV1(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_UPI_PASSBOOK_URL_V1_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi/listing"))
				.pthListingUpiLitePassbookUrlV1(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_UPI_LITE_PASSBOOK_URL_V1_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi_lite/listing"))
				.pthListingUpiCcPassbookUrlV1(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_UPI_CC_PASSBOOK_URL_V1_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi_cc/listing"))
				.pthListingFilterUpiPassbookUrlV1(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V1_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi/listing/filter"))
				.pthListingFilterUpiLitePassbookUrlV1(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V1_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi_lite/listing/filter"))
				.pthListingFilterUpiCcPassbookUrlV1(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V1_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi_cc/listing/filter"))
				.pthListingUrlV2(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_URL_V2_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/listing"))
				.pthListingFilterUrlV2(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_URL_V2_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/listing/filter"))
				.pthListingUpiPassbookUrlV2(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_UPI_PASSBOOK_URL_V2_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi/listing"))
				.pthListingUpiLitePassbookUrlV2(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_UPI_LITE_PASSBOOK_URL_V2_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi_lite/listing"))
				.pthListingUpiCcPassbookUrlV2(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_UPI_CC_PASSBOOK_URL_V2_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi_cc/listing"))
				.pthListingFilterUpiPassbookUrlV2(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_PASSBOOK_URL_V2_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi/listing/filter"))
				.pthListingFilterUpiLitePassbookUrlV2(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_LITE_PASSBOOK_URL_V2_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi_lite/listing/filter"))
				.pthListingFilterUpiCcPassbookUrlV2(configurablePropertiesHolder.getPropertyWithDefaultValue(
						WebConstants.RoutingConstants.PTH_LISTING_FILTER_UPI_CC_PASSBOOK_URL_V2_V4, String.class,
						"https://upi.paytm.com/pth/ext/v4/upi_cc/listing/filter"))
				.build();
		}
		catch (Exception e) {
			log.error("Exception while getting fromDate for routing info V4 in metaApi : {}",
					CommonsUtility.exceptionFormatter(e));
		}
		return null;

	}

	public ApiUrlsInfo getApiUrlsInfo() {
		return ApiUrlsInfo.builder()
			.updatesApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPDATES_API_URL, String.class))
			.listingApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_LISTING_URL, String.class))
			.detailApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_DETAIL_URL, String.class))
			.reconApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_RECON_API_URL, String.class))
			.getTagsApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_GET_TAGS_API_URL, String.class))
			.updateTagsApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPDATE_TAGS_API_URL, String.class))
			.bgListingApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_BG_LISTING_API_URL, String.class))
			.mandateHistoryApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_MANDATE_HISTORY_API_URL, String.class))
			.toggleVisibilityApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_TOGGLE_VISIBILITY_API_URL, String.class))
			.searchAndFilterApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_SEARCH_FILTER_API_URL, String.class))
			.upiPassbookApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPI_PASSBOOK_API_URL, String.class))
			.upiLitePassbookApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPI_LITE_PASSBOOK_API_URL, String.class))
			.upiCcPassbookApi(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPI_CC_PASSBOOK_API_URL, String.class))
			.userTagsSummaryApiUrl(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_USER_TAGS_SUMMARY_API_URL, String.class))
			.tagSummaryApiUrl(configurablePropertiesHolder
				.getProperty(ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_TAG_SUMMARY_API_URL, String.class))
			.build();
	}

	public ApiUrlsInfo getApiUrlsInfoV4() {
		return ApiUrlsInfo.builder()
			.updatesApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPDATES_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v4/updates"))
			.listingApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_LISTING_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v4/listing"))
			.detailApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_DETAIL_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v3"))
			.reconApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_RECON_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v1/recon"))
			.getTagsApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_GET_TAGS_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v1/tag/getTags"))
			.updateTagsApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPDATE_TAGS_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v1/tag/updateTags"))
			.bgListingApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_BG_LISTING_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v4/bg/listing"))
			.mandateHistoryApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_MANDATE_HISTORY_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v1/mandateHistory"))
			.toggleVisibilityApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_TOGGLE_VISIBILITY_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v3/toggleVisibility"))
			.searchAndFilterApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_SEARCH_FILTER_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v4/listing/filter"))
			.upiPassbookApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPI_PASSBOOK_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v4/upi/listing"))
			.upiLitePassbookApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPI_LITE_PASSBOOK_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v4/upi_lite/listing"))
			.upiCcPassbookApi(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_UPI_CC_PASSBOOK_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v4/upi_cc/listing"))
			.userTagsSummaryApiUrl(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_USER_TAGS_SUMMARY_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v1/tag/getUserTagsSummary"))
			.tagSummaryApiUrl(configurablePropertiesHolder.getPropertyWithDefaultValue(
					ConfigurationPropertiesConstants.ApiUrlsInfo.PTH_TAG_SUMMARY_API_URL_V4, String.class,
					"https://upi.paytm.com/pth/ext/v1/tag/tagSummary"))
			.build();
	}

}