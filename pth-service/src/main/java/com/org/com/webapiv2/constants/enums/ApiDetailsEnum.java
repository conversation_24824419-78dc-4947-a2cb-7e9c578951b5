package com.org.com.webapiv2.constants.enums;

import static com.org.com.webapiv2.constants.Constants.*;
import static com.org.com.webapiv2.constants.Constants.MANDATE_HISTORY_INT_API;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.ServiceDisableConstants;
import static com.org.com.webapiv2.constants.Constants.MANDATE_HISTORY_EXT_API;
import static com.org.com.webapiv2.constants.Constants.MANDATE_HISTORY_INT_API;
import static com.org.panaroma.commons.constants.WebConstants.CST_SEARCH_API;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum ApiDetailsEnum {

	PING("ext/v1/ping", "ping", Boolean.FALSE), HEALTH("ext/v1/health", "health", Boolean.FALSE),
	UPDATE_TAGS("ext/v1/tag/updateTags", "updateTags", Boolean.TRUE),
	GET_TAGS("ext/v1/tag/getTags", "getTags", Boolean.TRUE),
	GET_TAGS_V2("ext/v2/tag/getTags", "getTagsV2", Boolean.TRUE),
	GET_USER_TAGS_SUMMARY("ext/v1/tag/getUserTagsSummary", "getUserTagsSummary", Boolean.TRUE,
			ServiceDisableConstants.TAGGING_USER_TAGS_SUMMARY_DISABLED),
	MONTHLY_SPENT_RECEIVED_ON_TAG_API("ext/v1/tag/tagSummary", "tagSummary", Boolean.TRUE,
			ServiceDisableConstants.TAGGING_TAG_SUMMARY_DISABLED),
	TXN_LIST("int/v1/txnList", "txnList", Boolean.FALSE),
	CST_PANEL("int/v1/txnList/cstPanel", "cstPanel", Boolean.FALSE),
	MONTHLY_ANALYTICS_AGG("ext/v1/spend-analytics/monthly", "monthly-analytics", Boolean.TRUE),
	MONTHS_SPENT_DATA("ext/v1/spend-analytics/data", "month-spent-data", Boolean.TRUE),
	CST_BOT_DETAIL("ext/v1/cstBotDetail", "cstBotDetail", Boolean.TRUE),
	ANALYTICS_AGG_DATA_BREAK_UP("ext/v1/spend-analytics/dataBreakup", "dataBreakup", Boolean.TRUE),
	ANALYTICS_AGG_DATA("ext/v1/spend-analytics/aggregatedData", "aggregatedData", Boolean.TRUE),
	ANALYTICS_CONFIG_PUSH("ext/v1/utility/pushConfig", "pushKafkaConfig", Boolean.FALSE),
	PASSBOOK("int/v1/source-passbook/passbook", "passbook", Boolean.FALSE),
	TXN_DETAIL("int/v1/source-passbook/txnDetail", "txnDetail", Boolean.FALSE),
	REWIND_DATA("ext/v1/spend-analytics/rewindData", "rewindData", Boolean.TRUE),
	// ANALYTICS_PROCESS_FILE("ext/v1/utility/processFile", "processFile", Boolean.FALSE),
	LATEST_TXN("int/v1/latestTxn", "latestTxn", Boolean.FALSE),
	META_DATA_API("ext/v1/metaData", "metaData", Boolean.TRUE),
	IVR_TXN_LIST("ext/v1/ivr/txnList", "ivrTxnList", Boolean.TRUE),
	ANY_TXN("int/v1/fetch/anyTxn", "anyTxn", Boolean.FALSE), STATEMENT("int/v1/statement", "statement", Boolean.FALSE),
	RECENT_TXN("ext/v1/recent-txn", "recentTxns", Boolean.TRUE, ServiceDisableConstants.RECENT_TXNS_DISABLED),
	GET_LANGUAGES("int/v1/getLanguages", GET_LANGUAGES_API_NAME, Boolean.FALSE),
	GET_MESSAGES("int/v1/getMessages", GET_MESSAGES_API_NAME, Boolean.FALSE),
	DETAIL_API_UPI_V3("ext/v3/UPI/detail", "DETAIL_API_UPI_V3", Boolean.FALSE),
	DETAIL_API_PG_V3("ext/v3/PG/detail", "DETAIL_API_PG_V3", Boolean.FALSE),
	DETAIL_API_PPBL_V3("ext/v3/PPBL/detail", "DETAIL_API_PPBL_V3", Boolean.FALSE),
	DETAIL_API_WALLET_V3("ext/v3/WALLET/detail", "DETAIL_API_WALLET_V3", Boolean.FALSE),
	DETAIL_API_OMS_V3("ext/v3/OMS/detail", "DETAIL_API_OMS_V3", Boolean.FALSE),
	SEARCH_API_V3("ext/v3/search", "SEARCH_API", Boolean.FALSE),
	BG_LISTING("ext/v3/bg/listing", "BG_LISTING", Boolean.FALSE), LISTING("ext/v3/listing", "LISTING", Boolean.FALSE),
	UPI_PASSBOOK("ext/v3/upi/listing", "UPI_PASSBOOK", Boolean.FALSE),
	UPI_LITE_PASSBOOK("ext/v3/upi_lite/listing", "UPI_LITE_PASSBOOK", Boolean.FALSE),
	LISTING_FILTER("ext/v3/listing/filter", "LISTING_FILTER", Boolean.FALSE),
	UPI_PASSBOOK_FILTER("ext/v3/upi/listing/filter", "UPI_PASSBOOK_FILTER", Boolean.FALSE),
	UPI_LITE_PASSBOOK_FILTER("ext/v3/upi_lite/listing/filter", "UPI_LITE_PASSBOOK_FILTER", Boolean.FALSE),
	AUTOCOMPLETE_API("ext/v1/search/autocomplete", "AUTOCOMPLETE_API", Boolean.FALSE),
	UPDATES_API("ext/v1/updates", "UPDATES_API", Boolean.FALSE), RECON_API("ext/v1/recon", "RECON_API", Boolean.FALSE),
	MANDATE_HISTORY_EXT("ext/v1/mandate/history", MANDATE_HISTORY_EXT_API, Boolean.TRUE),
	MANDATE_HISTORY_INT("int/v1/mandate/history", MANDATE_HISTORY_INT_API, Boolean.FALSE),
	CST_SEARCH("int/v3/cstSearch", CST_SEARCH_API, Boolean.FALSE),
	TOGGLE_VISIBILITY("ext/v1/toggleVisibility", "toggleVisibility", Boolean.TRUE);

	private String apiUrl;

	private String apiName;

	private Boolean isAuthValidationRequired;

	private String serviceDisabledControl;

	ApiDetailsEnum(final String apiUrl, final String apiName, final Boolean isAuthValidationRequired) {
		this.apiUrl = apiUrl;
		this.apiName = apiName;
		this.isAuthValidationRequired = isAuthValidationRequired;
	}

	public static ApiDetailsEnum getApiDetailsEnumByUrl(final String apiUrl) {
		if (StringUtils.isBlank(apiUrl)) {
			return null;
		}
		for (ApiDetailsEnum apiDetailsEnum : ApiDetailsEnum.values()) {
			if (apiUrl.equals(apiDetailsEnum.apiUrl)) {
				return apiDetailsEnum;
			}
		}
		return null;
	}

}
