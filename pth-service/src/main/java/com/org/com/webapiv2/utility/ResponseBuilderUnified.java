package com.org.com.webapiv2.utility;

import static com.org.cst.view.constants.CstConstants.IS_DEEMED;
import static com.org.panaroma.commons.constants.Constants.MOBILE_NUMBER;
import static com.org.panaroma.commons.constants.WebConstants.CART_DATA_LIST_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.CHANNEL_CODE;
import static com.org.panaroma.commons.constants.WebConstants.EEE_MMM_dd_HH_mm_ss_z_yyyy;
import static com.org.panaroma.commons.constants.WebConstants.ERROR_CODE;
import static com.org.panaroma.commons.constants.WebConstants.ERROR_MSG;
import static com.org.panaroma.commons.constants.WebConstants.EXPIRE_AFTER;
import static com.org.panaroma.commons.constants.WebConstants.FIRST_PHASE;
import static com.org.panaroma.commons.constants.WebConstants.INITIATION_MODE;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_GENRE;
import static com.org.panaroma.commons.constants.WebConstants.NPCI_RESP_CODE;
import static com.org.panaroma.commons.constants.WebConstants.ONUS_MERCHANT_FLAG_KEY_FROM_UPI;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PARTICIPANTS_LIST_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.REF_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.REF_URL;
import static com.org.panaroma.commons.constants.WebConstants.RESP_CODE;
import static com.org.panaroma.commons.constants.WebConstants.REV_RESP_CODE;
import static com.org.panaroma.commons.constants.WebConstants.RRN;
import static com.org.panaroma.commons.constants.WebConstants.TAGS_LIST_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.TXNPURPOSE;
import static com.org.panaroma.commons.constants.WebConstants.TXNTYPE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_NARRATION;
import static com.org.panaroma.commons.constants.WebConstants.UPI_TXN_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.UpiInternationalConstant.CONVERSION_RATE;
import static com.org.panaroma.commons.constants.WebConstants.UpiInternationalConstant.FOREIGN_AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.UpiInternationalConstant.FOREIGN_CURRENCY;
import static com.org.panaroma.commons.constants.WebConstants.UpiInternationalConstant.MARK_UP_FEE;
import static com.org.panaroma.commons.constants.WebConstants.yyyy_MM_dd_HH_mm_ss;
import static com.org.panaroma.commons.exceptionhandler.ErrorCodeConstants.TRANSFORMATION_ERROR;
import static com.org.panaroma.commons.utils.Utility.getSelfParticipants;
import static com.org.panaroma.web.utility.GenericUtilityExtension.fetchPayeeVpa;
import static com.org.panaroma.web.utility.GenericUtilityExtension.isOnusMerchant;
import static com.org.panaroma.commons.constants.CommonConstants.UpiLiteConstants.IS_UPI_LITE_TXN;

import com.google.gson.Gson;
import com.org.com.webapiv2.dto.cstSearch.CstSearchAdditionalFields;
import com.org.com.webapiv2.dto.cstSearch.CstSearchResponse;
import com.org.com.webapiv2.dto.cstSearch.CstSearchResponseNode;
import com.org.com.webapiv2.dto.latestTxn.LatestTxnEsRepoResponseDto;
import com.org.com.webapiv2.dto.latestTxn.LatestTxnResDto;
import com.org.com.webapiv2.dto.upiCstPanel.CstPanelResponseTxn;
import com.org.com.webapiv2.dto.upiCstPanel.UpiCstPanelResponse;
import com.org.com.webapiv2.dto.upiCstPanel.UserInfo;
import com.org.com.webapiv2.dto.upiSearch.EsRepoResponseDto;
import com.org.com.webapiv2.dto.upiSearch.InternationalTxnDetails;
import com.org.com.webapiv2.dto.upiSearch.ResponseTxn;
import com.org.com.webapiv2.dto.upiSearch.UpiSearchResponse;
import com.org.com.webapiv2.dto.upiSearch.UserDetails;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.AccountTypeEnum;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.PspInfo;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.StatusEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.UpiChannelTypeEnum;
import com.org.panaroma.commons.dto.bankPassbook.response.MirrorCartItems;
import com.org.panaroma.commons.dto.bankPassbook.response.MirrorTransformedParticipant;
import com.org.panaroma.commons.dto.bankPassbook.response.MirrorTransformedTag;
import com.org.panaroma.commons.dto.bankPassbook.response.MirrorTthd;
import com.org.panaroma.commons.dto.bankPassbook.response.PassbookListingResponse;
import com.org.panaroma.commons.dto.cart.CartItems;
import com.org.panaroma.commons.dto.es.ParentDetails;
import com.org.com.webapiv2.dto.upiCstPanel.FwdTxnDetails;
import com.org.panaroma.commons.dto.es.RefundDetails;
import com.org.panaroma.commons.dto.es.TransformedCardData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.kafkaPushAPI.UpiDataDto;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.exceptionhandler.ExceptionBuilder;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.cache.ICacheClient;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.exceptionhandler.ErrorCodeConstants;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.utility.ListingUtility;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.org.panaroma.web.utility.configurablePropertyUtility.InternalCache;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Log4j2
@Component("respBuilderUnified")
public class ResponseBuilderUnified {

	private static final Gson gson = new Gson();

	List<Class> classes = Arrays.asList(String.class, Integer.class, Byte.class, Short.class, Long.class, Float.class,
			Double.class, Boolean.class, Character.class, HashSet.class, Set.class);

	public static UpiSearchResponse buildUpiSearchResponse(final RepoResponseSearchApiDto repoResponseDto,
			final SearchContext searchContext) {
		UpiSearchResponse response = new UpiSearchResponse();
		PaginationParams paginationParams = ConversionUtilityForProgressiveSearch
			.convertPaginationParams(repoResponseDto.getPaginationParams());
		response.setFromDate(Utility.getDateTime(searchContext.getFromDate(), yyyy_MM_dd_HH_mm_ss));
		response.setToDate(Utility.getDateTime(searchContext.getToDate(), yyyy_MM_dd_HH_mm_ss));
		response.setCurrentPage(searchContext.getPageNo());
		response
			.setTotalPages((int) Utility.getTotalPages(repoResponseDto.getTotalHits(), searchContext.getPageSize()));
		response.setUserId(searchContext.getEntityId());
		response.setPaginationParams(paginationParams);
		response.setTxns(getResponseTxnIterable(repoResponseDto.getTransformedTransactionHistoryDetailsIterable()));
		return response;
	}

	public static UpiCstPanelResponse buildUpiCstPanelResponse(final EsRepoResponseDto repoResponseDto,
			final SearchContext searchContext) {
		UpiCstPanelResponse response = new UpiCstPanelResponse();
		if (StringUtils.isNotBlank(searchContext.getEntityId())) {
			response.setUserId(searchContext.getEntityId());
		}
		response.setFromDate(Utility.getDateTime(searchContext.getFromDate(), EEE_MMM_dd_HH_mm_ss_z_yyyy));
		response.setToDate(Utility.getDateTime(searchContext.getToDate(), EEE_MMM_dd_HH_mm_ss_z_yyyy));
		response.setCurrentPage(searchContext.getPageNo());
		response
			.setTotalPages((int) Utility.getTotalPages(repoResponseDto.getTotalHits(), searchContext.getPageSize()));
		response.setPaginationParams(repoResponseDto.getPaginationParams());
		response.setTxns(getCstResponseTxnIterable(repoResponseDto.getTransformedTransactionHistoryDetailsIterable()));
		return response;
	}

	private static Iterable<ResponseTxn> getResponseTxnIterable(
			final Iterable<TransformedTransactionHistoryDetail> tthdIterable) {
		Iterator<TransformedTransactionHistoryDetail> tthdIterator = tthdIterable.iterator();

		return () -> new Iterator<ResponseTxn>() {
			@Override
			public boolean hasNext() {
				return tthdIterator.hasNext();
			}

			@Override
			public ResponseTxn next() {
				return convertToResponseTxn(tthdIterator.next());
			}
		};
	}

	private static Iterable<CstPanelResponseTxn> getCstResponseTxnIterable(
			final Iterable<TransformedTransactionHistoryDetail> tthdIterable) {
		Iterator<TransformedTransactionHistoryDetail> tthdIterator = tthdIterable.iterator();

		return () -> new Iterator<CstPanelResponseTxn>() {
			@Override
			public boolean hasNext() {
				return tthdIterator.hasNext();
			}

			@Override
			public CstPanelResponseTxn next() {
				return convertToCstResponseTxn(tthdIterator.next());
			}
		};
	}

	private static ResponseTxn convertToResponseTxn(final TransformedTransactionHistoryDetail tthd) {
		ResponseTxn responseTxn = new ResponseTxn();

		responseTxn.setOrderId(tthd.getSourceTxnId());
		responseTxn.setMerchantOrderId(tthd.getOrderId());
		responseTxn.setTxnId(tthd.getTxnId());
		responseTxn.setAmount(Currency.getCurrencyAmountInHigherDenomination(tthd.getAmount(), 1));
		responseTxn.setTxnIndicator(tthd.getTxnIndicator());
		responseTxn.setDate(tthd.getTxnDate().toString());
		responseTxn.setUpdatedDate(tthd.getUpdatedDate().toString());
		responseTxn.setOrgTxnId(tthd.getParentTxnId());
		responseTxn.setStatus(ClientStatusEnum.getStatusEnumByKey(tthd.getOriginalStatus()).toString());
		responseTxn.setUmn(tthd.getUmn());
		responseTxn.setHiddenTxn(Objects.isNull(tthd.getIsHiddenTxn()) ? false : tthd.getIsHiddenTxn());
		responseTxn
			.setFwdTxnDetails(isParentDetailsValidated(tthd.getParentDetails()) ? convertToFwdTxnDetails(tthd) : null);
		responseTxn.setRefundDetails(
				isRefundDetailsValidated(tthd.getRefundDetails()) ? convertToCstPanelRefundDetails(tthd) : null);
		responseTxn
			.setFwdTxnDetails(isParentDetailsValidated(tthd.getParentDetails()) ? convertToFwdTxnDetails(tthd) : null);
		responseTxn.setRefundDetails(
				isRefundDetailsValidated(tthd.getRefundDetails()) ? convertToCstPanelRefundDetails(tthd) : null);
		responseTxn.setOtherUpiAppName(getAppNameFromVpa(tthd));

		if (!CollectionUtils.isEmpty(tthd.getContextMap())) {
			responseTxn.setCategory(tthd.getContextMap().getOrDefault(UPI_TXN_CATEGORY, null));
			responseTxn.setInitiationMode(tthd.getContextMap().getOrDefault(INITIATION_MODE, null));
			responseTxn.setInitiationType(tthd.getContextMap().get(TXNTYPE));
			responseTxn.setRrn(tthd.getContextMap().getOrDefault(RRN, null));
			responseTxn.setRefCategory(tthd.getContextMap().getOrDefault(REF_CATEGORY, null));
			responseTxn.setErrorCode(tthd.getContextMap().getOrDefault(ERROR_CODE, null));
			responseTxn.setErrorMessage(tthd.getContextMap().getOrDefault(ERROR_MSG, null));
			responseTxn.setMerchantGenre(tthd.getContextMap().getOrDefault(MERCHANT_GENRE, null));
			responseTxn.setBusinessType(tthd.getContextMap().getOrDefault(TXNPURPOSE, null));
			responseTxn.setRefUrl(tthd.getContextMap().getOrDefault(REF_URL, null));
			responseTxn.setTxnNarration(tthd.getContextMap().getOrDefault(TXN_NARRATION, null));
			responseTxn.setIsOnusMerchant(StringUtils.isBlank(tthd.getContextMap().get(ONUS_MERCHANT_FLAG_KEY_FROM_UPI))
					? null : TRUE.equalsIgnoreCase(tthd.getContextMap().get(ONUS_MERCHANT_FLAG_KEY_FROM_UPI)));
			responseTxn.setIsDeemed(tthd.getContextMap().get(IS_DEEMED));
			responseTxn.setIsUpiLiteTxn(Boolean.valueOf(tthd.getContextMap().get(IS_UPI_LITE_TXN)));
		}
		setUserDetails(responseTxn, tthd);
		if (TransactionTypeEnum.P2M_INTERNATIONAL.getTransactionTypeKey().equals(tthd.getTxnType())
				|| TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL.getTransactionTypeKey().equals(tthd.getTxnType())) {
			setInternationalTxnDetails(responseTxn, tthd);
		}
		return responseTxn;
	}

	private static CstPanelResponseTxn convertToCstResponseTxn(final TransformedTransactionHistoryDetail tthd) {
		CstPanelResponseTxn responseTxn = new CstPanelResponseTxn();

		responseTxn.setOrderId(tthd.getSourceTxnId());
		responseTxn.setTxnId(tthd.getTxnId());
		responseTxn.setAmount(Currency.getCurrencyAmountInHigherDenomination(tthd.getAmount(), 1));
		responseTxn.setDate(Utility.getDateTime(tthd.getTxnDate(), EEE_MMM_dd_HH_mm_ss_z_yyyy));
		responseTxn.setUpdatedDate(Utility.getDateTime(tthd.getUpdatedDate(), EEE_MMM_dd_HH_mm_ss_z_yyyy));
		responseTxn.setOrgTxnId(tthd.getParentTxnId());
		responseTxn.setStatus(ClientStatusEnum.getStatusEnumByKey(tthd.getOriginalStatus()).getStatusValue());
		responseTxn.setMerchantTxnId(tthd.getSourceTxnId());
		responseTxn.setNpciTs(Utility.getDateTime(tthd.getUpdatedDate(), EEE_MMM_dd_HH_mm_ss_z_yyyy));
		responseTxn.setApplicationUpdatedOn(Utility.getDateTime(tthd.getUpdatedDate(), EEE_MMM_dd_HH_mm_ss_z_yyyy));
		responseTxn.setUmn(tthd.getUmn());
		responseTxn.setHiddenTxn(Objects.isNull(tthd.getIsHiddenTxn()) ? false : tthd.getIsHiddenTxn());
		responseTxn
			.setFwdTxnDetails(isParentDetailsValidated(tthd.getParentDetails()) ? convertToFwdTxnDetails(tthd) : null);
		responseTxn.setRefundDetails(
				isRefundDetailsValidated(tthd.getRefundDetails()) ? convertToCstPanelRefundDetails(tthd) : null);
		responseTxn
			.setFwdTxnDetails(isParentDetailsValidated(tthd.getParentDetails()) ? convertToFwdTxnDetails(tthd) : null);
		responseTxn.setRefundDetails(
				isRefundDetailsValidated(tthd.getRefundDetails()) ? convertToCstPanelRefundDetails(tthd) : null);
		responseTxn.setOtherUpiAppName(getAppNameFromVpa(tthd));

		if (!CollectionUtils.isEmpty(tthd.getContextMap())) {
			responseTxn.setCategory(tthd.getContextMap().getOrDefault(UPI_TXN_CATEGORY, null));
			responseTxn.setInitiationType(tthd.getContextMap().getOrDefault(INITIATION_MODE, null));
			responseTxn.setRrn(tthd.getContextMap().getOrDefault(RRN, null));
			responseTxn.setRefCategory(tthd.getContextMap().getOrDefault(REF_CATEGORY, null));
			responseTxn.setErrorCode(tthd.getContextMap().getOrDefault(ERROR_CODE, null));
			responseTxn.setBusinessType(tthd.getContextMap().getOrDefault(TXNPURPOSE, null));
			responseTxn.setChannel(tthd.getContextMap().getOrDefault(CHANNEL_CODE, null));
			responseTxn.setNpciRespCode(tthd.getContextMap().getOrDefault(NPCI_RESP_CODE, null));
			responseTxn.setExpAfterMin(tthd.getContextMap().getOrDefault(EXPIRE_AFTER, null));
			responseTxn.setRefUrl(tthd.getContextMap().getOrDefault(REF_URL, null));
			responseTxn.setTxnType(tthd.getContextMap().getOrDefault(TXNTYPE, null));
			responseTxn.setIrc(tthd.getContextMap().getOrDefault(NPCI_RESP_CODE, null));
			responseTxn.setFirstPhase(tthd.getContextMap().getOrDefault(FIRST_PHASE, null));

		}
		responseTxn.setContextMap(tthd.getContextMap());
		responseTxn.setTxnDateEpoch(tthd.getTxnDate());
		responseTxn.setUpdatedDateEpoch(tthd.getUpdatedDate());
		setUserInfo(responseTxn, tthd);
		return responseTxn;
	}

	private static boolean isParentDetailsValidated(final ParentDetails parentDetails) {
		return !Objects.isNull(parentDetails) && Boolean.TRUE.equals(parentDetails.isParentExistsInDb());
	}

	private static boolean isRefundDetailsValidated(final List<RefundDetails> refundDetails) {
		return !Objects.isNull(refundDetails);
	}

	// this method is used to set FwdTxnDetails (PTH-918) for the responseTxn
	// takes the necessary fields from tthd and sets it to FwdTxnDetails
	private static FwdTxnDetails convertToFwdTxnDetails(final TransformedTransactionHistoryDetail tthd) {

		return FwdTxnDetails.builder()
			.txnId(tthd.getParentDetails().getTxnId())
			.txnSource(tthd.getParentDetails().getTxnSource())
			.txnDate(tthd.getParentDetails().getTxnDate())
			.amount(tthd.getParentDetails().getAmount())
			.rrn(tthd.getParentDetails().getRrn())
			.build();

	}

	// this method is user to convert RefundDetails from tthd (pth-commons -> dto -> es)
	// to com.org.com.webapiv2.dto.upiCstPanel.RefundDetails (PTH-918)
	private static List<com.org.com.webapiv2.dto.upiCstPanel.RefundDetails> convertToCstPanelRefundDetails(
			final TransformedTransactionHistoryDetail tthd) {
		List<com.org.com.webapiv2.dto.upiCstPanel.RefundDetails> refundDetails = new ArrayList<>();
		for (RefundDetails refundDetailsItem : tthd.getRefundDetails()) {
			com.org.com.webapiv2.dto.upiCstPanel.RefundDetails refund = com.org.com.webapiv2.dto.upiCstPanel.RefundDetails
				.builder()
				.txnId(refundDetailsItem.getTxnId())
				.orderId(refundDetailsItem.getOrderId())
				.status(Objects.requireNonNull(StatusEnum.getStatusEnumByKey(refundDetailsItem.getStatus()))
					.getStatusValue())
				.date(String.valueOf(refundDetailsItem.getDate()))
				.amount(String.valueOf(refundDetailsItem.getAmount()))
				.build();
			refundDetails.add(refund);
		}
		return refundDetails;
	}

	private static void setUserDetails(final ResponseTxn responseTxn, final TransformedTransactionHistoryDetail tthd) {

		for (TransformedParticipant participant : tthd.getParticipants()) {
			UserDetails userDetails = getUserDetails(participant);

			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				responseTxn.setPayee(userDetails);

			}
			else {
				responseTxn.setPayer(userDetails);
				responseTxn.setNote(participant.getRemarks());
			}

			if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
				if (Objects.nonNull(participant.getMerchantData())) {
					responseTxn.setMerchantId(participant.getMerchantData().getMerchantId());
				}
			}
		}
	}

	public static String getAppNameFromVpa(TransformedTransactionHistoryDetail tthd) {
		String otherAppParticipantPspHandle = fetchOtherPartyPspHandle(tthd);
		PspInfo pspInfo = InternalCache.getPspInfo(otherAppParticipantPspHandle);
		String pspDisplayText = null;
		if (ObjectUtils.isNotEmpty(pspInfo) && StringUtils.isNotBlank(pspInfo.getPspDisplayText())) {
			pspDisplayText = pspInfo.getPspDisplayText();
			return pspDisplayText.replaceFirst("^on\\s+", "");
		}
		return null;
	}

	/*
	 * Populates foreign currency and conversion to INR related details for P2M
	 * international txns
	 */
	private static void setInternationalTxnDetails(final ResponseTxn responseTxn,
			final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd) || Objects.isNull(tthd.getContextMap())) {
			return;
		}
		Map<String, String> contextMap = tthd.getContextMap();
		InternationalTxnDetails internationalTxnDetails = new InternationalTxnDetails();
		internationalTxnDetails.setForeignAmount(contextMap.get(FOREIGN_AMOUNT));
		internationalTxnDetails.setForeignCurrency(contextMap.get(FOREIGN_CURRENCY));
		internationalTxnDetails.setMarkupFee(contextMap.get(MARK_UP_FEE));
		internationalTxnDetails.setConversionRate(contextMap.get(CONVERSION_RATE));
		responseTxn.setInternationalTxnDetails(internationalTxnDetails);
	}

	private static String fetchOtherPartyPspHandle(TransformedTransactionHistoryDetail tthd) {
		if (tthd == null) {
			return null;
		}

		String otherPartyEntityId = tthd.getOtherPartyEntityId();

		if (ObjectUtils.isNotEmpty(tthd.getParticipants())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				// Safe null handling for both values
				boolean isOtherParty = Objects.equals(participant.getEntityId(), otherPartyEntityId);

				if (isOtherParty && ObjectUtils.isNotEmpty(participant.getUpiData())
						&& ObjectUtils.isNotEmpty(participant.getUpiData().getVpa())) {
					return Utility.getPspHandle(participant.getUpiData().getVpa());
				}
			}
		}
		return null;
	}

	private static void setUserInfo(final CstPanelResponseTxn responseTxn,
			final TransformedTransactionHistoryDetail tthd) {

		for (TransformedParticipant participant : tthd.getParticipants()) {
			UserInfo userInfo = getUserInfo(participant);

			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				responseTxn.setPayee(userInfo);

			}
			else {
				responseTxn.setPayer(userInfo);
				responseTxn.setNote(participant.getRemarks());
			}

			if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
				if (Objects.nonNull(participant.getMerchantData())) {
					responseTxn.setMerchantId(participant.getMerchantData().getMerchantId());
				}
			}
		}
	}

	private static UserDetails getUserDetails(final TransformedParticipant participant) {
		UserDetails userDetails = new UserDetails();

		if (Objects.nonNull(participant.getBankData())) {
			userDetails.setBankAccount(participant.getBankData().getAccNumber());
			userDetails.setBankName(participant.getBankData().getBankName());
			userDetails.setIfsc(participant.getBankData().getIfsc());

			// PTH-1318 -> Setting accType for all account types
			AccountTypeEnum accountTypeEnum = AccountTypeEnum
				.getAccountTypeEnumByKey(participant.getBankData().getAccountType());
			if (accountTypeEnum != null) {
				userDetails.setAccType(accountTypeEnum.getAccountType());
			}
		}

		if (Objects.nonNull(participant.getContextMap())) {
			userDetails.setMobile(participant.getContextMap().getOrDefault(MOBILE_NUMBER, null));
			userDetails.setReversalRespCode(participant.getContextMap().getOrDefault(REV_RESP_CODE, null));
		}

		if (Objects.nonNull(participant.getUpiData())) {
			userDetails.setVpa(participant.getUpiData().getVpa());
		}

		userDetails.setName(participant.getName());
		userDetails.setOauthCustId(participant.getEntityId());
		return userDetails;
	}

	private static UserInfo getUserInfo(final TransformedParticipant participant) {
		UserInfo userInfo = new UserInfo();

		if (Objects.isNull(participant)) {
			return null;
		}

		if (Objects.nonNull(participant.getBankData())) {
			userInfo.setBankAccount(participant.getBankData().getAccNumber());
			userInfo.setIfsc(participant.getBankData().getIfsc());
			userInfo.setUpiAccRefId(participant.getBankData().getUpiAccRefId());
			// PTH-1318 -> Setting accType for all account types
			AccountTypeEnum accountTypeEnum = AccountTypeEnum
				.getAccountTypeEnumByKey(participant.getBankData().getAccountType());
			if (accountTypeEnum != null) {
				userInfo.setAccType(accountTypeEnum.getAccountType());
			}
		}

		if (Objects.nonNull(participant.getMobileData())) {
			userInfo.setMobile(participant.getMobileData().getMobileNumber());
		}

		if (Objects.nonNull(participant.getUpiData())) {
			userInfo.setVpa(participant.getUpiData().getVpa());

			UpiDataDto upiDataDto = new UpiDataDto();
			upiDataDto.setVpa(participant.getUpiData().getVpa());
			upiDataDto.setChannel(
					UpiChannelTypeEnum.getUpiChannelTypeValueByKey(participant.getUpiData().getChannel()) == null ? null
							: UpiChannelTypeEnum.getUpiChannelTypeValueByKey(participant.getUpiData().getChannel())
								.getChannelName());
			upiDataDto.setChannelId(participant.getUpiData().getChannelId());
			upiDataDto.setChannelMode(participant.getUpiData().getChannelMode());
			upiDataDto.setUpiCcInfo(participant.getUpiData().getUpiCcInfo());
			upiDataDto.setPmtInstMetaInfo(participant.getUpiData().getPmtInstMetaInfo());

			userInfo.setUpiData(upiDataDto);
		}

		if (Objects.nonNull(participant.getMerchantData())) {
			userInfo.setMcc(participant.getMerchantData().getMccCode());
		}

		if (!CollectionUtils.isEmpty(participant.getContextMap())) {
			userInfo.setRespCode(participant.getContextMap().getOrDefault(RESP_CODE, null));
			userInfo.setReversalRespCode(participant.getContextMap().getOrDefault(REV_RESP_CODE, null));
		}

		if (Objects.nonNull(participant.getEntityType())) {
			userInfo.setEntityType(participant.getEntityType() == 1 ? "PERSON" : "ENTITY");
		}

		userInfo.setName(participant.getName());
		userInfo.setOauthCustId(participant.getEntityId());
		userInfo.setContextMap(participant.getContextMap());
		userInfo.setTxnDateEpoch(Long.valueOf(participant.getTxnDate()));
		userInfo.setUpdatedDateEpoch(Long.valueOf(participant.getUpdatedDate()));
		return userInfo;
	}

	public MirrorTthd getPassbookDetailResponse(final TransformedTransactionHistoryDetail tthd) {
		MirrorTthd mirrorTthd = new MirrorTthd();
		deepCloning(tthd, mirrorTthd);
		return mirrorTthd;
	}

	public PassbookListingResponse getPassbookListingResponse(final EsRepoResponseDto esRepoResponseDto) {
		PassbookListingResponse response = new PassbookListingResponse();
		List<MirrorTthd> list = new ArrayList<>();
		for (TransformedTransactionHistoryDetail tthd : esRepoResponseDto
			.getTransformedTransactionHistoryDetailsIterable()) {
			MirrorTthd mirrorTthd = new MirrorTthd();
			deepCloning(tthd, mirrorTthd);
			list.add(mirrorTthd);
		}
		response.setTxns(list);
		response.setPaginationParams(esRepoResponseDto.getPaginationParams());
		return response;
	}

	public void deepCloning(final Object fromClassObject, final Object toClassObject) {
		try {
			Field[] fromClassObjFields = fromClassObject.getClass().getDeclaredFields();
			Class toClass = toClassObject.getClass();

			for (Field field : fromClassObjFields) {
				field.setAccessible(true);
				// if data is null no need to process furthur for this field
				if (field.get(fromClassObject) == null) {
					continue;
				}
				// if field datatype is primitive just set data
				if (field.getType().isPrimitive()) {
					setValueInToObj(field, field.get(fromClassObject), toClassObject, toClass);
					continue;
				}

				// check if field is present in Mirror objects or not
				try {
					toClass.getDeclaredField(field.getName());
				}
				catch (NoSuchFieldException e) {
					continue;
				}

				// if classType is String or hashSet or PRimitive datatypes wrapper class
				// no need of special handling just assign the value to toClass
				if (classes.contains(field.getType())) {
					setValueInToObj(field, field.get(fromClassObject), toClassObject, toClass);
					continue;
				}

				// handles List Data Types
				if (field.getType() == List.class || field.getType() == ArrayList.class) {

					List finalClonnedList = handleClonningInListDataType(field, fromClassObject);
					setValueInToObj(field, finalClonnedList, toClassObject, toClass);
					continue;
				}

				// Handles cloning of Map dataTypes
				if (field.getType() == Map.class || field.getType() == HashMap.class) {
					Map map = new HashMap(((Map) field.get(fromClassObject)));
					setValueInToObj(field, map, toClassObject, toClass);
					continue;
				}

				// Handles cloning of Objects which are not handled above;
				Field toField = toClass.getDeclaredField(field.getName());
				toField.setAccessible(true);
				Object mirrorObj = toField.getType().getDeclaredConstructor().newInstance();
				deepCloning(field.get(fromClassObject), mirrorObj);
				toField.set(toClassObject, mirrorObj);
			}
		}
		catch (Exception e) {
			log.error("Error while clonning Tthd into mirror tthd, Exception : {}",
					CommonsUtility.exceptionFormatter(e));
			throw ExceptionBuilder.getException(PANAROMA_SERVICE, TRANSFORMATION_ERROR);
		}
	}

	private void setValueInToObj(final Field classField, final Object valueToBeSet, final Object toClassObject,
			final Class toClass) throws NoSuchFieldException, IllegalAccessException {
		Field toField = toClass.getDeclaredField(classField.getName());
		toField.setAccessible(true);
		toField.set(toClassObject, valueToBeSet);
	}

	private List handleClonningInListDataType(final Field field, final Object fromClassObject)
			throws IllegalAccessException {
		switch (field.getName()) {
			case PARTICIPANTS_LIST_IDENTIFIER:
				List<TransformedParticipant> participantsList = (List<TransformedParticipant>) field
					.get(fromClassObject);
				List<MirrorTransformedParticipant> mirrorParticipantList = new ArrayList<>();
				for (TransformedParticipant participant : participantsList) {
					MirrorTransformedParticipant mirrorParticipant = new MirrorTransformedParticipant();
					deepCloning(participant, mirrorParticipant);
					mirrorParticipantList.add(mirrorParticipant);
				}
				return mirrorParticipantList;
			case TAGS_LIST_IDENTIFIER:
				List<TransformedTag> tagsList = (List<TransformedTag>) field.get(fromClassObject);
				List<MirrorTransformedTag> mirrorTagsList = new ArrayList<>();
				for (TransformedTag tag : tagsList) {
					MirrorTransformedTag mirrorTag = new MirrorTransformedTag();
					deepCloning(tag, mirrorTag);
					mirrorTagsList.add(mirrorTag);
				}
				return mirrorTagsList;
			case CART_DATA_LIST_IDENTIFIER:
				var cartItemsList = (List<CartItems>) field.get(fromClassObject);
				List<MirrorCartItems> mirrorCartItemsList = new ArrayList<>();
				for (CartItems cartItems : cartItemsList) {
					MirrorCartItems mirrorCartItem = new MirrorCartItems();
					deepCloning(cartItems, mirrorCartItem);
					mirrorCartItemsList.add(mirrorCartItem);
				}
				return mirrorCartItemsList;
			default:
				// no default handling
		}
		return null;
	}

	public static LatestTxnResDto buildLatestTxnResponse(final LatestTxnEsRepoResponseDto repoResponse,
			final SearchContext searchContext) {

		LatestTxnResDto resDto = new LatestTxnResDto();
		resDto.setFromDate(searchContext.getFromDate().toString());
		resDto.setEntityId(searchContext.getEntityId());
		// if no data found , then set txnDate null
		if (Objects.nonNull(repoResponse)) {
			resDto.setTxnDate(repoResponse.getTransactionDate().toString());
		}
		else {
			resDto.setTxnDate(null);
		}
		return resDto;
	}

	public CstSearchResponse buildCstSearchResponse(final RepoResponseSearchApiDto repoResponse,
			final com.org.panaroma.web.SearchContext searchContext, final Map<String, String> paramMap,
			final List<String> salaryReportCodes, final String langId, final ICacheClient cacheClient,
			final IKafkaClient kafkaClient) {
		try {
			Map<String, com.org.panaroma.commons.dto.UserDetails> userIdImageUrlMapFromCache = ListingUtility
				.getStringUserDetailsMap(false, true, cacheClient, kafkaClient, repoResponse);

			CstSearchResponse cstSearchResponse = CstSearchResponse.builder()
				.txns(getCstSearchResponseList(repoResponse.getTransformedTransactionHistoryDetailsIterable(), langId,
						salaryReportCodes, userIdImageUrlMapFromCache, paramMap))
				.build();
			if (repoResponse.getPaginationParams() != null
					&& repoResponse.getPaginationParams().getPaginationTxnId() != null) {
				cstSearchResponse.setPaginationParams(repoResponse.getPaginationParams());
			}
			return cstSearchResponse;

		}
		catch (Exception e) {
			log.error("Exception while creating cst response. Exception : {}", CommonsUtility.exceptionFormatter(e));
			if (e instanceof PanaromaException) {
				throw e;
			}
			else {
				log.error("Unhandled Exception while creating cst response. Exception : {}",
						CommonsUtility.exceptionFormatter(e));
				throw ExceptionFactory.getException(PANAROMA_SERVICE, ErrorCodeConstants.TRANSFORMATION_ERROR);
			}
		}
	}

	public static List<CstSearchResponseNode> getCstSearchResponseList(
			final List<TransformedTransactionHistoryDetail> esDtoList, final String langId,
			final List<String> salaryReportCodes,
			final Map<String, com.org.panaroma.commons.dto.UserDetails> userIdImageUrlMapFromCache,
			final Map<String, String> paramMap) {

		List<CstSearchResponseNode> txns = new ArrayList<>();
		for (TransformedTransactionHistoryDetail esDto : esDtoList) {
			EsResponseTxn viewFields = ListingUtility.convertToEsResponseTxn(esDto, langId, salaryReportCodes,
					userIdImageUrlMapFromCache, paramMap);

			CstSearchResponseNode cstSearchResponseNode = gson.fromJson(gson.toJson(viewFields),
					CstSearchResponseNode.class);

			CstSearchAdditionalFields additionalFields = new CstSearchAdditionalFields();
			additionalFields.setIsOnusMerchant(isOnusMerchant(esDto));
			additionalFields.setOrderId(esDto.getOrderId());
			additionalFields.setPayeeVpa(fetchPayeeVpa(esDto));
			additionalFields.setChannelCode(MapUtils.getString(esDto.getContextMap(), CHANNEL_CODE));
			TransformedCardData userCardData = null;
			List<TransformedParticipant> selfParticipants = getSelfParticipants(esDto);
			for (TransformedParticipant selfParticipant : selfParticipants) {
				userCardData = selfParticipant.getCardData();
				if (Objects.nonNull(userCardData)) {
					break;
				}
			}
			additionalFields.setUserCardData(userCardData);

			cstSearchResponseNode.setAdditionalFields(additionalFields);

			txns.add(cstSearchResponseNode);
		}
		return txns;
	}

}
