package com.org.com.webapiv2.dao;

import com.org.com.webapiv2.dto.tags.TagSummaryResponse;
import com.org.com.webapiv2.dto.tags.UserTagsSummaryResponse;
import com.org.com.webapiv2.model.SystemTagData;
import com.org.com.webapiv2.model.TxnTags;
import com.org.com.webapiv2.model.UserTagData;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.entity.AutoTaggingDetails;
import java.util.List;

public interface ITagDao {

	List<UserTagData> getUserTagData(String userId, SearchContext searchContext);

	SystemTagData getSystemTagData(TransformedTransactionHistoryDetail txn);

	void updateAutoTaggingDetails(String userId, String secPartyId, AutoTaggingDetails autoTaggingDetails)
			throws Exception;

	AutoTaggingDetails getAutoTaggingDetails(final String userId, final String secPartyId, final Integer secPartyType);

	UserTagsSummaryResponse getUserTagsSummary(String userId, String month, SearchContext searchContext);

	TagSummaryResponse getTagSummary(String userId, String month, String tag, SearchContext searchContext);

}
