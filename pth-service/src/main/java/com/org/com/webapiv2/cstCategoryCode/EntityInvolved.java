package com.org.com.webapiv2.cstCategoryCode;;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
  Entity i.e. other party involved in transaction
  This will be used for user to user transaction history filter
   */
@Data
public class EntityInvolved {

	@Schema(description = "field like vpa")
	private String field;

	@Schema(description = "value of the field")
	private String value;

}