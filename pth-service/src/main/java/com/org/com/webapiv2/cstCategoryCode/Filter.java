package com.org.com.webapiv2.cstCategoryCode;;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
Filter to be applied
* */
@Data
public class Filter {

	@Schema(description = "transaction type")
	private String transactionType;

	@Schema(description = "entity involved")
	private EntityInvolved entityInvolved;

	@Schema(description = "status")
	private String status;

	@Schema(description = "transaction category")
	private String transactionCategory;

	@Schema(description = "date range")
	private Range date;

	@Schema(description = "amount range")
	private Range amount;

	@Schema(description = "transaction id")
	private String transactionId;

}