import com.org.panaroma.commons.constants.BankDataConstants;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.es.SearchFields;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import org.apache.commons.lang3.StringUtils;
import com.org.panaroma.commons.dto.WalletType;
import java.util.List;
import java.util.ArrayList
import com.org.panaroma.commons.dto.CardType
import com.org.panaroma.commons.dto.UthCategoryEnum
import com.org.panaroma.commons.dto.ClientStatusEnum
import org.apache.commons.lang3.ObjectUtils
import com.org.panaroma.commons.utils.IfscUtility;
import java.util.Set
import com.org.panaroma.commons.dto.TransactionIndicator
import com.org.panaroma.commons.dto.TxnCategoryEnum;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import static com.org.panaroma.ingester.utils.DroolsUtility.*

global Object response;

dialect "mvel"

rule "set_order_level_details" salience 11
    when
        tthd : TransformedTransactionHistoryDetail()
    then
        tthd.searchFields.setSearchOrderId(tthd.getOrderId());
        //txnIndicator filter support
        tthd.getSearchFields().setSearchTxnIndication(
                        TransactionIndicator.getTransactionIndicatorEnumByKey(tthd.getTxnIndicator()).getTransactionIndicatorUserViewValue());
        //txnStatus filter support
        tthd.getSearchFields().setSearchTxnStatus(ClientStatusEnum.getStatusEnumByKey(tthd.getStatus()).getStatusUserViewValue());
        //txnCategory filter support
        setSearchTxnCategory(tthd);
        updateSearchTagField(tthd);
end

rule "wallet_self" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)
        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals(ClientStatusEnum.FAILURE.getStatusKey() == status && ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus()))
        from participants
    then
        tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
        tthd.searchFields.setSearchRemarks(selfParticipant.getRemarks());
        tthd.searchFields.setSearchReferenceId(tthd.getTxnId());
        if (selfParticipant.getWalletData() != null && WalletType.getWalletTypeByKey(selfParticipant.getWalletData().getWalletType()) != null) {
                tthd.searchFields.getSearchWalletType().add(String.valueOf(WalletType.getEnumFromWalletTypeKey(selfParticipant.getWalletData().getWalletType()).getWalletTypeKey()));
                tthd.searchFields.getSearchWalletType().add(String.valueOf(WalletType.getEnumFromWalletTypeKey(selfParticipant.getWalletData().getWalletType()).getWalletTypeUserViewValue()));
        }
end

rule "wallet_Other_User" salience 9
    when
        tthd: TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant((tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.USER.entityTypeKey == entityType)
            && (PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then

        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if (otherParticipant.getMobileData() != null) {
            tthd.searchFields.setSearchOtherMobileNo(otherParticipant.getMobileData().getMobileNumber());
        }
end

rule "wallet_Other_Merchant" salience 8
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.MERCHANT.entityTypeKey == entityType)
            && (PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then

        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if (otherParticipant.getMerchantData() != null
            && UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()) != null) {

            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryId());
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryUserView());
        }
end

rule "upi_self" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant((tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals((ClientStatusEnum.FAILURE.getStatusKey() == status) && (ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus())))
        from participants
    then
        tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
        tthd.searchFields.setSearchRemarks(selfParticipant.getRemarks());
        if (selfParticipant.getBankData() != null) {
            tthd.searchFields.setSearchSelfBankName(selfParticipant.getBankData().getBankName());
            tthd.searchFields.setSearchSelfBankIfsc(selfParticipant.getBankData().getIfsc());
            tthd.searchFields.setSearchSelfAccountNo(selfParticipant.getBankData().getAccNumber());
            updateSelfAccountType(tthd,selfParticipant);
        }
        if (tthd.getContextMap() != null && tthd.getContextMap().containsKey("rrn")) {
            tthd.searchFields.setSearchReferenceId(tthd.getContextMap().get("rrn"));
        }
end

rule "upi_Other_User" salience 9
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant((tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.USER.entityTypeKey == entityType)
            && (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then
        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if(otherParticipant.getUpiData() != null) {
            tthd.searchFields.setSearchOtherVpa(otherParticipant.getUpiData().getVpa());
        }

        if(otherParticipant.getBankData()!= null) {
            tthd.searchFields.setSearchOtherBankName(otherParticipant.getBankData().getBankName());
            tthd.searchFields.setSearchOtherBankIfsc(otherParticipant.getBankData().getIfsc());
            tthd.searchFields.setSearchOtherAccountNo(otherParticipant.getBankData().getAccNumber());
        }
        if (otherParticipant.getMobileData() != null) {
            tthd.searchFields.setSearchOtherMobileNo(otherParticipant.getMobileData().getMobileNumber());
        }
end

rule "upi_Other_Merchant" salience 8
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.MERCHANT.entityTypeKey == entityType)
            && (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then
        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if (otherParticipant.getUpiData() != null) {
            tthd.searchFields.setSearchOtherVpa(otherParticipant.getUpiData().getVpa());
        }
        if (otherParticipant.getMerchantData() != null
                && UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()) != null) {
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryId());
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryUserView());        }
end

rule "bank_self" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.BANK.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals(ClientStatusEnum.FAILURE.getStatusKey() == status && ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus()))
        from participants
    then
        tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
        tthd.searchFields.setSearchRemarks(selfParticipant.getRemarks());
        if (selfParticipant.getBankData() != null) {
            tthd.searchFields.setSearchSelfBankName(selfParticipant.getBankData().getBankName());
            tthd.searchFields.setSearchSelfBankIfsc(selfParticipant.getBankData().getIfsc());
            tthd.searchFields.setSearchSelfAccountNo(selfParticipant.getBankData().getAccNumber());
            tthd.searchFields.setSearchReferenceId(selfParticipant.getBankData().getBankTxnId());
            updateSelfAccountType(tthd,selfParticipant);
        }
        String phoneNumber = Utility
                            .getValidPhoneNumber(selfParticipant, TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()), true);
        tthd.getSearchFields().setSearchOtherMobileNo(phoneNumber);
end

rule "bank_Other_User" salience 9
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.USER.entityTypeKey == entityType)
            && (PaymentSystemEnum.BANK.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then
        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if(otherParticipant.getBankData()!= null) {
            tthd.searchFields.setSearchOtherBankName(otherParticipant.getBankData().getBankName());
            tthd.searchFields.setSearchOtherBankIfsc(otherParticipant.getBankData().getIfsc());
            tthd.searchFields.setSearchOtherAccountNo(otherParticipant.getBankData().getAccNumber());
        }
end

rule "bank_Other_Merchant" salience 8
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.MERCHANT.entityTypeKey == entityType)
            && (PaymentSystemEnum.BANK.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then
        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if (otherParticipant.getMerchantData() != null
            && UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()) != null) {

            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryId());
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryUserView());
        }
end

rule "pg_self" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.PG.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals(ClientStatusEnum.FAILURE.getStatusKey() == status && ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus()))
        from participants
    then
        tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
        tthd.searchFields.setSearchRemarks(selfParticipant.getRemarks());
        if (selfParticipant.getBankData() != null) {
            tthd.searchFields.setSearchSelfBankName(selfParticipant.getBankData().getBankName());
            tthd.searchFields.setSearchSelfBankIfsc(selfParticipant.getBankData().getIfsc());
            tthd.searchFields.setSearchSelfAccountNo(selfParticipant.getBankData().getAccNumber());
            tthd.searchFields.setSearchReferenceId(selfParticipant.getBankData().getBankTxnId());
            updateSelfAccountType(tthd,selfParticipant);
        }
        if (selfParticipant.getCardData() != null && CardType.getCardTypeByKey(selfParticipant.getCardData().getCardType()) != null) {
            tthd.searchFields.setSearchSelfCardType(CardType.getCardTypeByKey(selfParticipant.getCardData().getCardType()).getCardTypeValue());
            tthd.searchFields.setSearchSelfCardNetwork(selfParticipant.getCardData().getCardNetwork());
        }
end

rule "pg_Other_User" salience 9
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.USER.entityTypeKey == entityType)
            && (PaymentSystemEnum.PG.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then
        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if(otherParticipant.getUpiData() != null) {
            tthd.searchFields.setSearchOtherVpa(otherParticipant.getUpiData().getVpa());
        }
        if(otherParticipant.getBankData()!= null) {
                tthd.searchFields.setSearchOtherBankName(otherParticipant.getBankData().getBankName());
                tthd.searchFields.setSearchOtherBankIfsc(otherParticipant.getBankData().getIfsc());
                tthd.searchFields.setSearchOtherAccountNo(otherParticipant.getBankData().getAccNumber());
        }
        if (otherParticipant.getMobileData() != null) {
                tthd.searchFields.setSearchOtherMobileNo(otherParticipant.getMobileData().getMobileNumber());
        }
end

rule "pg_Other_Merchant" salience 8
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.MERCHANT.entityTypeKey == entityType)
            && (PaymentSystemEnum.PG.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then
        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if (otherParticipant.getMerchantData() != null
            && UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()) != null) {
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryId());
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryUserView());        }
end

rule "postpaid_self" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.PAYTM_POSTPAID.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals(ClientStatusEnum.FAILURE.getStatusKey() == status
            && ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus()))
        from participants
    then
        tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
        tthd.searchFields.setSearchRemarks(selfParticipant.getRemarks());
        if (selfParticipant != null
                && selfParticipant.getContextMap() != null
                && selfParticipant.getContextMap().containsKey("postpaid_txn_id")) {
            tthd.searchFields.setSearchReferenceId(selfParticipant.getContextMap().get("postpaid_txn_id"));
        }
end

rule "mgv_self" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)
        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.MGV.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals(ClientStatusEnum.FAILURE.getStatusKey() == status && ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus()))
        from participants
    then
        tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
        tthd.searchFields.setSearchRemarks(selfParticipant.getRemarks());
        if (selfParticipant.getOtherData() != null) {
            tthd.searchFields.setSearchVoucherName(selfParticipant.getOtherData().getName());
        }
end

rule "ppbl_self" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals((ClientStatusEnum.FAILURE.getStatusKey() == status) && (ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus())))
        from participants
    then
        tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
        tthd.searchFields.setSearchRemarks(selfParticipant.getRemarks());
        if (selfParticipant.getBankData() != null) {
            tthd.searchFields.setSearchSelfBankName(selfParticipant.getBankData().getBankName());
            tthd.searchFields.setSearchSelfBankIfsc(selfParticipant.getBankData().getIfsc());
            tthd.searchFields.setSearchSelfAccountNo(selfParticipant.getBankData().getAccNumber());
            tthd.searchFields.setSearchReferenceId(selfParticipant.getBankData().getRrn());
            updateSelfAccountType(tthd,selfParticipant);
        }
        String phoneNumber = Utility
                            .getValidPhoneNumber(selfParticipant, TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()), true);
        tthd.getSearchFields().setSearchOtherMobileNo(phoneNumber);
end

rule "ppbl_Other_User" salience 9
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.USER.entityTypeKey == entityType)
            && (PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then
        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if(otherParticipant.getBankData()!= null) {
                tthd.searchFields.setSearchOtherBankName(otherParticipant.getBankData().getBankName());
                tthd.searchFields.setSearchOtherBankIfsc(otherParticipant.getBankData().getIfsc());
                tthd.searchFields.setSearchOtherAccountNo(otherParticipant.getBankData().getAccNumber());
        }
        if (otherParticipant.getMobileData() != null) {
                tthd.searchFields.setSearchOtherMobileNo(otherParticipant.getMobileData().getMobileNumber());
        }
end

rule "ppbl_Other_Merchant" salience 8
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && (EntityTypesEnum.USER.entityTypeKey != entityType)
            && (PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(paymentSystem)))
        from participants
    then
        tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
        tthd.searchFields.setSearchOtherName(otherParticipant.getName());
        if (otherParticipant.getMerchantData() != null
                && UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()) != null) {
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryId());
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryUserView());
        }
end

rule "ts_self" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (tthd.getTxnIndicator().equals(txnIndicator))
            && (PaymentSystemEnum.TS.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals((ClientStatusEnum.FAILURE.getStatusKey() == status) && (ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus())))
        from participants
   then
       tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
       tthd.searchFields.setSearchRemarks(selfParticipant.getRemarks());
       if (selfParticipant.getBankData() != null) {
           tthd.searchFields.setSearchSelfBankName(selfParticipant.getBankData().getBankName());
           tthd.searchFields.setSearchSelfBankIfsc(selfParticipant.getBankData().getIfsc());
            updateSelfAccountType(tthd,selfParticipant);
       }
   end

rule "ts_Other_User" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId))
            && (tthd.getTxnIndicator().equals(txnIndicator)))
            && (PaymentSystemEnum.TS.getPaymentSystemKey().equals(paymentSystem))
            && Boolean.FALSE.equals((ClientStatusEnum.FAILURE.getStatusKey() == status) && (ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus())))
        from participants
   then
       tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
       tthd.searchFields.setSearchRemarks(otherParticipant.getRemarks());
       if (otherParticipant.getBankData() != null) {
           tthd.searchFields.setSearchOtherBankName(otherParticipant.getBankData().getBankName());
           tthd.searchFields.setSearchOtherBankIfsc(otherParticipant.getBankData().getIfsc());
           tthd.searchFields.setSearchOtherAccountNo(otherParticipant.getBankData().getAccNumber());
       }
   end

rule "entityTypeNull" salience 7
   when
       tthd:TransformedTransactionHistoryDetail (participants: participants)

       otherParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId)))
            && entityType == null)
       from participants
   then
       tthd.searchFields.setOtherDataUpdatedDate(otherParticipant.getTxnDate());
       tthd.searchFields.setSearchOtherName(otherParticipant.getName());
       if (otherParticipant.getBankData() != null) {
           tthd.getSearchFields().setSearchOtherBankName(otherParticipant.getBankData().getBankName());
           tthd.getSearchFields().setSearchOtherAccountNo(otherParticipant.getBankData().getAccNumber());
           tthd.getSearchFields().setSearchOtherBankIfsc(otherParticipant.getBankData().getIfsc());
       }

       if (otherParticipant.getUpiData() != null) {
           tthd.getSearchFields().setSearchOtherVpa(otherParticipant.getUpiData().getVpa());
       }

        if (otherParticipant.getMobileData() != null) {
            tthd.searchFields.setSearchOtherMobileNo(otherParticipant.getMobileData().getMobileNumber());
        }

        if (otherParticipant.getMerchantData() != null
                && UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()) != null) {
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryId());
            tthd.searchFields.getSearchUthMerchantCategory().add(UthCategoryEnum.getEnumFromUthCategoryId(otherParticipant.getMerchantData().getUthCategory()).getUthCategoryUserView());        }
end

rule "paymentSystemToOrderLevel" salience 6
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)
        participant: TransformedParticipant((tthd.getEntityId() != null)
            && Boolean.FALSE.equals((TransactionTypeEnum.ADD_AND_PAY.getTransactionTypeKey().equals(tthd.getMainTxnType())))
            && (tthd.getEntityId().equals(entityId))) from participants
    then
        if (participant.getPaymentSystem() != null
            && PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem()) != null) {
            if (PaymentSystemEnum.BANK.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
                tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.PPBL.getPaymentSystemKey()));
                tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.PPBL.getPaymentSystemUserViewValue());
            }
            else if (UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(participant)) {
                 tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.UPI_LITE.getPaymentSystemKey()));
                 tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.UPI_LITE.getPaymentSystemUserViewValue());
            }
            else {
                tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.getEnumFromPaymentSystemKey(participant.getPaymentSystem()).getPaymentSystemKey()));
                tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.getEnumFromPaymentSystemKey(participant.getPaymentSystem()).getPaymentSystemUserViewValue());
            }
        }
end

rule "upi_lite_top_up_and_de_register_handling" salience 1
    when
        tthd:TransformedTransactionHistoryDetail(
            getMainTxnType() in (TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.getTransactionTypeKey(),
                                 TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.getTransactionTypeKey()));
    then
        tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.UPI.getPaymentSystemKey()));
        tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.UPI.getPaymentSystemUserViewValue());
end

rule "setBankAccId" salience 6
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId)))
        from participants
    then

        tthd.searchFields.setSelfDataUpdatedDate(selfParticipant.getTxnDate());
        setBankAcctId(tthd,selfParticipant);
end

rule "special_Add_And_Pay_handling" salience -1
    when
        tthd:TransformedTransactionHistoryDetail(
            getMainTxnType() == TransactionTypeEnum.ADD_AND_PAY.getTransactionTypeKey());
    then
        tthd.searchFields.getSearchPaymentSystem().clear();
        tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.WALLET.getPaymentSystemKey()));
        tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.WALLET.getPaymentSystemUserViewValue());
end

rule "paytm_postpaid_repayment_special_handling"
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        otherParticipant: TransformedParticipant((tthd.getEntityId != null)
            && TransactionTypeEnum.P2M.getTransactionTypeKey().equals(tthd.getMainTxnType())
            && Boolean.FALSE.equals((tthd.getEntityId().equals(entityId))
            && (tthd.getTxnIndicator() == txnIndicator))
            && (EntityTypesEnum.MERCHANT.entityTypeKey == entityType)
            && (entityId in ("PTMLXC07967440869609", "ClixCa39481323827004", "PTMPDU31912254318620", "PTMPPP06705714907201",
                             "OCLABF96068099251394", "OCLFul28991506618582", "OCLCLI86115359468128", "OCLHap97016760097639",
                             "OCLABF87587253727381", "OCLFul09433261208044", "OCLCLI08031899190868", "OCLHap02932088630701")))
        from participants
    then
       tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.PAYTM_POSTPAID.getPaymentSystemKey()));
       tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.PAYTM_POSTPAID.getPaymentSystemUserViewValue());
end

rule "pg_debit_card_txns_handling"
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.PG.getPaymentSystemKey().equals(paymentSystem))
            && (cardData != null)
            && (StringUtils.equalsIgnoreCase(IfscUtility.getBankNameWithCamelCase(BankDataConstants.PAYTM_BANK_IFSC), cardData.getCardIssuer())))
        from participants
    then
       tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.PPBL.getPaymentSystemKey()));
       tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.PPBL.getPaymentSystemUserViewValue());
end

rule "upi_linked_ppbl_accounts_handling"
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)

        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(paymentSystem))
            && (Boolean.TRUE.equals(Utility.isTxnApplicableForUpiLinkedPpblHandling(tthd, selfParticipant)))
            && (bankData != null)
            && (BankDataConstants.PAYTM_BANK_IFSC.equalsIgnoreCase(bankData.getIfsc())))
        from participants
    then
       tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.PPBL.getPaymentSystemKey()));
       tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.PPBL.getPaymentSystemUserViewValue());
end

rule "special_Add_Money_handling" salience 1
    when
        tthd:TransformedTransactionHistoryDetail(
            getMainTxnType() == TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey());
    then
        tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.WALLET.getPaymentSystemKey()));
        tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.WALLET.getPaymentSystemUserViewValue());
end
rule "fd_specific_rules" salience 6
    when
        tthd : TransformedTransactionHistoryDetail((contextFilterMap != null)
        && (contextFilterMap.get(WebConstants.REPORT_CODE) != null)
        && WebConstants.FD_REPORT_CODES.contains(contextFilterMap.get(WebConstants.REPORT_CODE)))
    then
        tthd.searchFields.getSearchPaymentSystem().add(WebConstants.FIXED_DEPOSIT);
end

rule "gv_specific_rules" salience 0
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants)
        selfParticipant: TransformedParticipant( (tthd.getEntityId() != null)
            && (tthd.getEntityId().equals(entityId))
            && Boolean.FALSE.equals(ClientStatusEnum.FAILURE.getStatusKey() == status && ClientStatusEnum.FAILURE.getStatusKey() != tthd.getStatus())
            && (contextMap != null) && Boolean.TRUE.equals(WebConstants.GV_PURCHASED.equals(contextMap.get(WebConstants.TXN_PURPOSE))))
        from participants
    then
        tthd.searchFields.getSearchPaymentSystem().add(WebConstants.PAYTM_GIFT_VOUCHER);
        if (selfParticipant.getUpiData() != null) {
            tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.UPI.getPaymentSystemKey()));
            tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.UPI.getPaymentSystemUserViewValue());
        }
end

rule "self_upi_transfer_search"
    when
        tthd:TransformedTransactionHistoryDetail(
                getContextMap() != null && contextMap.get("isSelfTransfer") == "true");
    then
        updateSearchKeywordsForSelfTransfer(tthd);
end

rule "set_channel_code"
    when
        tthd:TransformedTransactionHistoryDetail(
                getContextMap() != null && contextMap.get("channelCode") != null
                && WebConstants.IVR.equalsIgnoreCase(contextMap.get("channelCode")));
    then
        tthd.searchFields.setSearchChannelCode(WebConstants.IVR);
end

// adding special handling because in this case the participant is not of paymentSystem wallet, but will have the wallet data in it.
// this is for addMoneyRefund, where we are storing PG doc with wallet search fields
rule "wallet_add_money_refund" salience 10
    when
        tthd:TransformedTransactionHistoryDetail (participants: participants,
            getTxnType() == TransactionTypeEnum.ADD_MONEY_REFUND.getTransactionTypeKey())
        pgParticipant: TransformedParticipant (PaymentSystemEnum.PG.getPaymentSystemKey().equals(paymentSystem)) from participants;
    then
        if (pgParticipant != null
                && pgParticipant.getWalletData() != null
                && WalletType.getWalletTypeByKey(pgParticipant.getWalletData().getWalletType()) != null) {
            tthd.searchFields.getSearchWalletType().add(String.valueOf(WalletType.getEnumFromWalletTypeKey(pgParticipant.getWalletData().getWalletType()).getWalletTypeKey()));
            tthd.searchFields.getSearchWalletType().add(String.valueOf(WalletType.getEnumFromWalletTypeKey(pgParticipant.getWalletData().getWalletType()).getWalletTypeUserViewValue()));
            tthd.searchFields.getSearchPaymentSystem().add(String.valueOf(PaymentSystemEnum.WALLET.getPaymentSystemKey()));
            tthd.searchFields.getSearchPaymentSystem().add(PaymentSystemEnum.WALLET.getPaymentSystemUserViewValue());
        }
end


rule "set_emi_status"
    when
        tthd:TransformedTransactionHistoryDetail(contextMap != null && Boolean.TRUE.equals(Utility.isUpiViaCcTxn(tthd)));
    then
        updateEmiStatus(tthd);
end