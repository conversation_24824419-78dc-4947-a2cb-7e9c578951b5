#Kafka and ES properties
source.kafka.topic                       = transformed-data-topic
bootstrap.servers                        = localhost:9092
group.id                                 = upi-consumer-group
#zookeeper.connect    = localhost:2181
#
#name                 = upi-stream-consumer
#instance_count       = 1
#poll_time            = 100
#consumer_class_name  = org.apache.kafka.clients.consumer.KafkaConsumer
#message_class        = com.paytm.bank.objects.request.KafkaConsumerEntity
#enable.auto.commit   = false
#value.deserializer   = org.apache.kafka.common.serialization.StringDeserializer
#key.deserializer     = org.apache.kafka.common.serialization.StringDeserializer
#max.poll.interval.ms = 3000000
#max.poll.records     = 20

kafka.bootstrap-servers            = *************:9092,*************:9092,*************:9092
kafka.confluent-kafka-registry-url = http://kafka-schema-registry.default:80/

es-host-list=*************
es-port = 9200
es-v2-host-list                                         = *************
es-v2-port                                              = 9200

es-dc-host-list                                      = *************
es-dc-port                                           = 9200

mandate-es-host-list                                 = *************

es-max-retry-timeout = 2000
es-connect-timeout = 1000
elastic-search-index = payment_history_alias
elastic-search-index-prefix = payment-history-
es-socket-timeout                                = 1000
es-connect-request-timeout            = 1000

es.read.sourceMap.dc-main-pipeline.hostList = *************
es.read.sourceMap.dc-uth-enricher-pipeline.hostList = *************
es.read.sourceMap.es-insert-update-executor-pipeline.hostList = *************
es.read.sourceMap.data-audit-pipeline.hostList = *************



es.target[0].indexName                            = payment-history
es.target[0].port                                 = 9200
es.target[0].hosts                                = 127.0.0.1
es.target[0].pipeline-name                        = upi

es.target[13].indexName                            = payment-history
es.target[13].port                                 = 9200
es.target[13].hosts                                = 127.0.0.1
es.target[13].pipeline-name                        = upiV2


es.target[1].indexName                            = payment-history
es.target[1].port                                 = 9200
es.target[1].hosts                                = 127.0.0.1
es.target[1].pipeline-name                        = pg

es.target[2].indexName                            = payment-history
es.target[2].port                                 = 9200
es.target[2].hosts                                = 127.0.0.1
es.target[2].pipeline-name                        = wallet

es.target[3].indexName                            = payment-history
es.target[3].port                                 = 9200
es.target[3].hosts                                = 127.0.0.1
es.target[3].pipeline-name                        = retry-pipeline

es.target[4].indexName                            = payment-history
es.target[4].port                                 = 9200
es.target[4].hosts                                = *************
es.target[4].pipeline-name                        = cbs

es.target[5].indexName                            = payment-history
es.target[5].port                                 = 9200
es.target[5].hosts                                = 127.0.0.1
es.target[5].pipeline-name                        = pg-retry-v2

es.target[6].indexName                            = payment-history
es.target[6].port                                 = 9200
es.target[6].hosts                                = 127.0.0.1
es.target[6].pipeline-name                        = uth

es.target[7].indexName                            = spend-history
es.target[7].port                                 = 9200
es.target[7].hosts                                = 127.0.0.1
es.target[7].pipeline-name                        = user-spend-docs-creator
es.target[7].requestType                          = index
es.target[7].switch-adaptors-name                 = SpendEsAndCacheAdaptor

es.target[8].indexName                            = udir-es
es.target[8].port                                 = 9200
es.target[8].hosts                                = 127.0.0.1
es.target[8].pipeline-name                        = udir

es.target[9].indexName                            = payment-history
es.target[9].port                                 = 9200
es.target[9].hosts                                = 127.0.0.1
es.target[9].pipeline-name                        = dc-main-pipeline

es.target[10].indexName                            = payment-history
es.target[10].port                                 = 9200
es.target[10].hosts                                = 127.0.0.1
es.target[10].pipeline-name                        = dc-uth-enricher-pipeline

es.target[11].indexName                            = payment-history
es.target[11].port                                 = 9200
es.target[11].hosts                                = 127.0.0.1
es.target[11].pipeline-name                        = es-insert-update-executor-pipeline
es.target[11].switch-adaptors-name                 = insertExecutor

es.target[12].indexName                            = payment-history
es.target[12].port                                 = 9200
es.target[12].hosts                                = 127.0.0.1
es.target[12].pipeline-name                        = es-insert-update-executor-pipeline
es.target[12].switch-adaptors-name                 = updateExecutor

es.target[14].indexName                            = payment-history
es.target[14].port                                 = 9200
es.target[14].hosts                                = 127.0.0.1
es.target[14].pipeline-name                        = upi-relay-pipeline


#Kakfa Source config
kafka.source-list[0].topic                        = middleware_transaction_history_upi_data
kafka.source-list[0].bootstrap-servers            = localhost:9092
kafka.source-list[0].consumer-group-name          = upi-consumer-group
kafka.source-list[0].pipeline-name                = upi
kafka.source-list[0].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[0].max-poll-records             = 50

#Kakfa Source config
kafka.source-list[20].topic                        = middleware_transaction_history_upi_data_v2
kafka.source-list[20].bootstrap-servers            = localhost:9092
kafka.source-list[20].consumer-group-name          = upi-consumer-group-v2
kafka.source-list[20].pipeline-name                = upiV2
kafka.source-list[20].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[20].max-poll-records             = 50

#Kakfa Source config
kafka.source-list[1].topic                        = middleware_transaction_history_pg_data
kafka.source-list[1].bootstrap-servers            = localhost:9092
kafka.source-list[1].consumer-group-name          = pg-consumer-group
kafka.source-list[1].pipeline-name                = pg
kafka.source-list[1].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[1].max-poll-records             = 50

kafka.source-list[2].topic                        = middleware_transaction_history_wallet_data
kafka.source-list[2].bootstrap-servers            = localhost:9092
kafka.source-list[2].consumer-group-name          = wallet-consumer-group
kafka.source-list[2].pipeline-name                = wallet
kafka.source-list[2].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[2].max-poll-records             = 50

kafka.source-list[3].topic                        = middleware_transaction_history_retry_data
kafka.source-list[3].bootstrap-servers            = localhost:9092
kafka.source-list[3].consumer-group-name          = retry-consumer-group
kafka.source-list[3].pipeline-name                = retry-pipeline
kafka.source-list[3].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[3].max-poll-records             = 50

#TODO: Need to create topic at all env
kafka.source-list[4].topic                        = middleware_transaction_history_ppbl_data
kafka.source-list[4].bootstrap-servers            = localhost:9092
kafka.source-list[4].consumer-group-name          = cbs-consumer-group
kafka.source-list[4].pipeline-name                = cbs
kafka.source-list[4].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[4].max-poll-records             = 50

kafka.source-list[5].topic                        = gg-transaction-stich_data
kafka.source-list[5].bootstrap-servers            = localhost:9092
kafka.source-list[5].consumer-group-name          = gg-transaction-consumer-group
kafka.source-list[5].pipeline-name                = cbs-converter
kafka.source-list[5].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[5].max-poll-records             = 50

kafka.source-list[6].topic                        = uth_localisation_data
kafka.source-list[6].bootstrap-servers            = localhost:9092
kafka.source-list[6].consumer-group-name          = marketplace-consumer-group
kafka.source-list[6].pipeline-name                = marketplace
kafka.source-list[6].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[6].max-poll-records             = 50

kafka.source-list[7].topic                        = middleware_transaction_history_pg_retry_data
kafka.source-list[7].bootstrap-servers            = localhost:9092
kafka.source-list[7].consumer-group-name          = pg-retry-v2-consumer-group
kafka.source-list[7].pipeline-name                = pg-retry-v2
kafka.source-list[7].confluent-kafka-registry-url = http://localhost:8081/
kafka.source-list[7].max-poll-records             = 50

kafka.source-list[8].topic                        = middleware_transaction_history_recon_config_data
kafka.source-list[8].bootstrap-servers            = localhost:9092
kafka.source-list[8].consumer-group-name          = recon-consumer-group
kafka.source-list[8].pipeline-name                = recon
kafka.source-list[8].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[8].max-poll-records             = 50

kafka.source-list[9].topic                        = middleware_transaction_history_ppbl_data
kafka.source-list[9].bootstrap-servers            = localhost:9092
kafka.source-list[9].consumer-group-name          = cbs-stream-consumer-group
kafka.source-list[9].pipeline-name                = cbs-stream
kafka.source-list[9].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[9].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[9].retry-target.retry-topic                  = middleware_transaction_history_ppbl_data
kafka.source-list[9].retry-target.bootstrap-servers            = localhost:9092
kafka.source-list[9].retry-target.batch-size-bytes             = 3000
kafka.source-list[9].retry-target.linger-ms                    = 15
kafka.source-list[9].retry-target.request-timeout-ms           = 5000
kafka.source-list[9].retry-target.producers-pool-size          = 5
kafka.source-list[9].retry-target.transaction-timeout          = 60000
kafka.source-list[9].serialization-type                        = String

kafka.source-list[10].topic                        = promo-internal-topic
kafka.source-list[10].bootstrap-servers            = localhost:9092
kafka.source-list[10].consumer-group-name          = promo-consumer-group
kafka.source-list[10].pipeline-name                = promo
kafka.source-list[10].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[10].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[10].retry-target.retry-topic                  = promo-internal-topic
kafka.source-list[10].retry-target.bootstrap-servers            = localhost:9092
kafka.source-list[10].retry-target.batch-size-bytes             = 3000
kafka.source-list[10].retry-target.linger-ms                    = 15
kafka.source-list[10].retry-target.request-timeout-ms           = 5000
kafka.source-list[10].retry-target.producers-pool-size          = 5
kafka.source-list[10].retry-target.transaction-timeout          = 60000
kafka.source-list[10].serialization-type                        = String

kafka.source-list[11].topic                        = middleware_transaction_history_cbs_gg_stitch_retry_data
kafka.source-list[11].bootstrap-servers            = localhost:9092
kafka.source-list[11].consumer-group-name          = gg-stitch-retry-consumer-group
kafka.source-list[11].pipeline-name                = cbs-retry-pipeline
kafka.source-list[11].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[11].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[11].retry-target.retry-topic                  = middleware_transaction_history_cbs_gg_stitch_retry_data
kafka.source-list[11].retry-target.bootstrap-servers            = localhost:9092
kafka.source-list[11].retry-target.batch-size-bytes             = 3000
kafka.source-list[11].retry-target.linger-ms                    = 15
kafka.source-list[11].retry-target.request-timeout-ms           = 5000
kafka.source-list[11].retry-target.producers-pool-size          = 5
kafka.source-list[11].retry-target.transaction-timeout          = 60000
kafka.source-list[11].serialization-type                        = String

kafka.source-list[12].topic                        = uth_spend_user_kafka_config_data
kafka.source-list[12].bootstrap-servers            = localhost:9092
kafka.source-list[12].consumer-group-name          = spend_user_kafka_config_consumer_group
kafka.source-list[12].pipeline-name                = user-spend-docs-creator
kafka.source-list[12].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[12].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[12].retry-target.retry-topic                  = uth_spend_user_kafka_config_data
kafka.source-list[12].retry-target.bootstrap-servers            = localhost:9092
kafka.source-list[12].retry-target.batch-size-bytes             = 3000
kafka.source-list[12].retry-target.linger-ms                    = 15
kafka.source-list[12].retry-target.request-timeout-ms           = 5000
kafka.source-list[12].retry-target.producers-pool-size          = 5
kafka.source-list[12].retry-target.transaction-timeout          = 60000
kafka.source-list[12].serialization-type                        = String

kafka.source-list[13].topic                        = uth_spend_user_month_agg_kafka_config_data
kafka.source-list[13].bootstrap-servers            = localhost:9092
kafka.source-list[13].consumer-group-name          = user-analytics-month-agg_consumer_group
kafka.source-list[13].pipeline-name                = user-analytics-month-agg-creator
kafka.source-list[13].confluent-kafka-registry-url = http://localhost:8081
kafka.source-list[13].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[13].retry-target.retry-topic                  = uth_spend_user_month_agg_kafka_config_data
kafka.source-list[13].retry-target.bootstrap-servers            = localhost:9092
kafka.source-list[13].retry-target.batch-size-bytes             = 3000
kafka.source-list[13].retry-target.linger-ms                    = 15
kafka.source-list[13].retry-target.request-timeout-ms           = 5000
kafka.source-list[13].retry-target.producers-pool-size          = 5
kafka.source-list[13].retry-target.transaction-timeout          = 60000
kafka.source-list[13].serialization-type                        = String

kafka.source-list[14].topic                        = uth_tag_source_config_data
kafka.source-list[14].bootstrap-servers            = localhost:9092
kafka.source-list[14].consumer-group-name          = tag-agg-retry-consumer-group
kafka.source-list[14].pipeline-name                = tag-agg-retry-pipeline
kafka.source-list[14].max-poll-records             = 50

kafka.source-list[15].topic                        = uth_tag_source_config_data
kafka.source-list[15].bootstrap-servers            = localhost:9092
kafka.source-list[15].consumer-group-name          = tag-agg-retry-consumer-group
kafka.source-list[15].pipeline-name                = udir
kafka.source-list[15].max-poll-records             = 50
kafka.source-list[15].retry-target.retry-topic                  = uth_tag_source_config_data
kafka.source-list[15].retry-target.bootstrap-servers            = localhost:9092
kafka.source-list[15].retry-target.batch-size-bytes             = 3000
kafka.source-list[15].retry-target.linger-ms                    = 15
kafka.source-list[15].retry-target.request-timeout-ms           = 5000
kafka.source-list[15].retry-target.producers-pool-size          = 5
kafka.source-list[15].retry-target.transaction-timeout          = 60000
kafka.source-list[15].serialization-type                        = String

kafka.source-list[16].topic                        = uth_all_sources_dc_data_mwite1
kafka.source-list[16].bootstrap-servers            = localhost:9092
kafka.source-list[16].consumer-group-name          = uth-dc-main-consumer
kafka.source-list[16].pipeline-name                = dc-main-pipeline
kafka.source-list[16].max-poll-records             = 50

kafka.source-list[17].topic                        = uth_enricher_dc_data_mwite1
kafka.source-list[17].bootstrap-servers            = localhost:9092
kafka.source-list[17].consumer-group-name          = uth-enricher-dc-consumer
kafka.source-list[17].pipeline-name                = dc-uth-enricher-pipeline
kafka.source-list[17].max-poll-records             = 50

kafka.source-list[18].topic                        = uth_es_insert_update_data
kafka.source-list[18].bootstrap-servers            = localhost:9092
kafka.source-list[18].consumer-group-name          = uth-es-insert-update-consumer
kafka.source-list[18].pipeline-name                = es-insert-update-executor-pipeline
kafka.source-list[18].max-poll-records             = 50

kafka.source-list[19].topic                        = uth_audit_data
kafka.source-list[19].bootstrap-servers            = localhost:9092
kafka.source-list[19].consumer-group-name          = uth-audit-consumer
kafka.source-list[19].pipeline-name                = data-audit-pipeline
kafka.source-list[19].max-poll-records             = 50

kafka.source-list[21].topic                        = middleware_transaction_history_upi_data
kafka.source-list[21].bootstrap-servers            = localhost:9092
kafka.source-list[21].consumer-group-name          = upi-relay-consumer-group
kafka.source-list[21].pipeline-name                = upi-relay-pipeline
kafka.source-list[21].confluent-kafka-registry-url = http://localhost:8081/
kafka.source-list[21].max-poll-records             = 50

#Kafka Target Config
kafka.target-list[0].topic                        = middleware_transaction_history_ppbl_data
kafka.target-list[0].bootstrap-servers            = localhost:9092
kafka.target-list[0].pipeline-name                = cbs-converter
kafka.target-list[0].confluent-kafka-registry-url = http://localhost:8081
kafka.target-list[0].batch-size-bytes             = 3000
kafka.target-list[0].linger-ms                    = 15
kafka.target-list[0].request-timeout-ms           = 5000
kafka.target-list[0].producers-pool-size           = 5

kafka.target-list[1].topic                        = promo-internal-topic
kafka.target-list[1].bootstrap-servers            = localhost:9092
kafka.target-list[1].pipeline-name                = cbs-stream
kafka.target-list[1].confluent-kafka-registry-url = http://localhost:8081
kafka.target-list[1].batch-size-bytes             = 3000
kafka.target-list[1].linger-ms                    = 15
kafka.target-list[1].request-timeout-ms           = 5000
kafka.target-list[1].producers-pool-size          = 5
kafka.target-list[1].transaction-timeout          = 60000
kafka.target-list[1].switch-adaptors-name         = PromoServiceInternalAdaptor

kafka.target-list[2].topic                        = promo-external-topic
kafka.target-list[2].bootstrap-servers            = localhost:9092
kafka.target-list[2].pipeline-name                = promo
kafka.target-list[2].confluent-kafka-registry-url = http://localhost:8081
kafka.target-list[2].batch-size-bytes             = 3000
kafka.target-list[2].linger-ms                    = 15
kafka.target-list[2].request-timeout-ms           = 5000
kafka.target-list[2].producers-pool-size          = 5
kafka.target-list[2].transaction-timeout          = 60000
kafka.target-list[2].switch-adaptors-name         = PromoServiceExternalAdaptor

kafka.target-list[3].topic                        = middleware_transaction_history_promo_internal_data
kafka.target-list[3].bootstrap-servers            = localhost:9092
kafka.target-list[3].pipeline-name                = cbs-retry-pipeline
kafka.target-list[3].confluent-kafka-registry-url = http://localhost:8081
kafka.target-list[3].batch-size-bytes             = 3000
kafka.target-list[3].linger-ms                    = 15
kafka.target-list[3].request-timeout-ms           = 5000
kafka.target-list[3].producers-pool-size          = 5
kafka.target-list[3].transaction-timeout          = 60000
kafka.target-list[3].switch-adaptors-name         = PromoServiceInternalAdaptor

kafka.target-list[4].topic                        = uth_spend_user_kafka_config_data
kafka.target-list[4].bootstrap-servers            = localhost:9092
kafka.target-list[4].pipeline-name                = user-spend-docs-creator
kafka.target-list[4].confluent-kafka-registry-url = http://localhost:8081
kafka.target-list[4].batch-size-bytes             = 3000
kafka.target-list[4].linger-ms                    = 15
kafka.target-list[4].request-timeout-ms           = 5000
kafka.target-list[4].producers-pool-size          = 5
kafka.target-list[4].transaction-timeout          = 60000
kafka.target-list[4].switch-adaptors-name         = UserSpendConfigKafkaAdaptor

kafka.target-list[5].topic                        = uth_spend_user_month_agg_kafka_config_data
kafka.target-list[5].bootstrap-servers            = localhost:9092
kafka.target-list[5].pipeline-name                = user-spend-docs-creator
kafka.target-list[5].confluent-kafka-registry-url = http://localhost:8081
kafka.target-list[5].batch-size-bytes             = 3000
kafka.target-list[5].linger-ms                    = 15
kafka.target-list[5].request-timeout-ms           = 5000
kafka.target-list[5].producers-pool-size          = 5
kafka.target-list[5].transaction-timeout          = 60000
kafka.target-list[5].switch-adaptors-name         = UserMonthSpendAggKafkaAdaptor

kafka.target-list[6].topic                        = uth_enricher_dc_data
kafka.target-list[6].bootstrap-servers            = localhost:9092
kafka.target-list[6].pipeline-name                = sink2DcUth
kafka.target-list[6].confluent-kafka-registry-url = http://localhost:8081
kafka.target-list[6].batch-size-bytes             = 3000
kafka.target-list[6].linger-ms                    = 15
kafka.target-list[6].request-timeout-ms           = 5000
kafka.target-list[6].producers-pool-size          = 5
kafka.target-list[6].transaction-timeout          = 60000

bankData.blackListed.reportCodes =
reportcodes.blocked.for.ShowInListing = 60202,20701,80203,20702,60301
reportcodes.blocked.for.grouping = 60202,20701,20702
bankPassbook.Rptcodes.whilelisted.userslist = 1
bankPassbook.Rptcodes.whilelisteding.percent= 100

#Kafka properties
kafka.target-v2-list[0].kafka-client-name               = cart
kafka.target-v2-list[0].kafka-producer-key              = CART_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_cart_data
kafka.target-v2-list[0].bootstrap-servers               = localhost:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://localhost:8081
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000


kafka.target-v2-list[1].kafka-client-name               = userImageUrl
kafka.target-v2-list[1].kafka-producer-key              = USER_IMAGE_URL_DATA_PRODUCER
kafka.target-v2-list[1].topic                           = user_image_url_data
kafka.target-v2-list[1].bootstrap-servers               = localhost:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].confluent-kafka-registry-url    = http://localhost:8081
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 5000

kafka.target-v2-list[2].kafka-client-name               = TAG_AGG_SOURCE_CONFIG_KAFKA_CLIENT
kafka.target-v2-list[2].kafka-producer-key              = TAG_AGG_SOURCE_CONFIG_KAFKA_CLIENT
kafka.target-v2-list[2].topic                           = uth_tag_source_config_data
kafka.target-v2-list[2].bootstrap-servers               = http://localhost:8081
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].linger-ms                       = 15
kafka.target-v2-list[2].request-timeout-ms              = 5000


#es.target[0].indexName = payment-history
#es.target[0].port = 9200
#es.target[0].hosts = *************,***********
#
#
##Kakfa Source config
#kafka.source-list[0].topic                        = middleware_payments_transaction_history_transformed_data
#kafka.source-list[0].bootstrap-servers            = *************:9092,*************:9092,*************:9092
#kafka.source-list[0].consumer-group-name          = transaction-history-group


check.pointing.enable                    = false

retry.kafka-list[0].pipelineName = upi
retry.kafka-list[0].topic = middleware_transaction_history_upi_data

retry.kafka-list[4].pipelineName                  = upiV2
retry.kafka-list[4].topic                         = middleware_transaction_history_upi_data_v2

retry.kafka-list[1].pipelineName = wallet
retry.kafka-list[1].topic = middleware_transaction_history_wallet_data

retry.kafka-list[2].pipelineName = pg
retry.kafka-list[2].topic = middleware_transaction_history_pg_data

retry.kafka-list[3].pipelineName                  = dc-retry-pipeline
retry.kafka-list[3].topic                         = uth_retry_dc_data

while.listed.users.list                            = 1234,12345
enable.whitelisting                                = false
es.bulk.flush.action.value                         = 1

flink.parallelism.enabled                          = true

mrkplace.sectionId                                 = 10
mrkplace.subSectionId                              = 420
mrkplace.priority                                  = 2
mrkplace.quality                                   = 2
bank.utility.service.scheme= http
bank.utility.service.port=9091
bank.utility.service.host= localhost
bank.utility.service.path= /test2
utility.client.tcp.read.timeout          = 2000
utility.client.tcp.connection.timeout    = 2000
utility.client.request.timeout = 8000
utility.client.socket.timeout = 2000
marketplace.retryTime=5

marketplace.localise.cache.time =-1
marketplace.unlocalise.cache.time=86400
marketplace.cache.socket.timeout=500
marketplace.cache.sleep.retries= 50
marketplace.cache.total.timeout=900

test.controller.enabled=false
batch.size=50
localisation.asyc.corePool =5
localisation.asyc.maxPool=7
localisation.asyc.queueCapacity=10
kafka.localisation.target.client =marketplace
kafka.localisation.target.topic =uth_localisation_data
kafka.localisation.target.bootstrapServers =localhost:9092
kafka.localisation.target.batchSizeBytes =30000
kafka.localisation.target.lingerMs =15
kafka.localisation.target.requestTimeoutMs =5000
kafka.localisation.target.confluentKafkaRegistryUrl=http://localhost:8081
pth.retry.exhausted.topic.name                  = pth_retry_exhausted_data


#15 minutes
pg.p2m.refund.cache.time.in.seconds           = 900
pg.p2m.refund.cache.socket.timeout.in.seconds = 500
pg.p2m.refund.cache.total.timeout.in.seconds  = 900
pg.p2m.refund.cache.sleep.retries             = 50

flink.checkpoint.url                               = flink-checkpoints-middleware
flink.enableCheckpointing.interval                 = 100
flink.minPauseBetweenCheckpoints.interval          = 10000
flink.pg.parallelism.value                         = 84
flink.oms.parallelism.value                       = 1
flink.ppbl-pg.parallelism.value                   = 1
flink.upi.parallelism.value                        = 20
flink.upiV2.parallelism.value                     = 20
flink.retry-pipeline.parallelism.value             = 1
flink.default.parallelism.value                    = 1
flink.wallet.parallelism.value                     = 20
flink.cbs.parallelism.value                       = 20
flink.ts.parallelism.value                       = 20
flink.pg.retry.v2.parallelism.value                = 20
flink.cart.parallelism.value                       = 20
flink.cbs-converter.parallelism.value             = 20
flink.chat.parallelism.value                       = 20
flink.retry-pipeline-v2.parallelism.value         = 1
flink.chat-back-filling-pipeline.parallelism.value = 1
flink.upi-back-filling-pipeline.parallelism.value      = 1
flink.retry-back-filling-pipeline.parallelism.value      = 1
flink.recon.parallelism.value             = 1
flink.status-resolver.parallelism.value             = 1
flink.user_image_url.parallelism.value             = 20
flink.uth.parallelism.value = 1
flink.promo.parallelism.value                     = 1
flink.cbs-stream.parallelism.value                = 1
flink.cbs-retry-pipeline.parallelism.value        = 1
flink.cache-updater-pipeline.parallelism.value =1
flink.van-pipeline.parallelism.value = 1
flink.upi-recon-pipeline.parallelism.value = 1
flink.user-spend-docs-creator.parallelism.value = 1
flink.user-analytics-month-agg-creator.parallelism.value = 1
flink.promo-upi-pipeline.parallelism.value = 1
userId-fetcher-pipeline.parallelism.value = 1
flink.dc-main-pipeline.parallelism.value = 1
flink.dc-uth-enricher-pipeline.parallelism.value = 1
flink.es-insert-update-executor-pipeline.parallelism.value = 1
flink.data-audit-pipeline.parallelism.value = 1
flink.chat.data.publish.api.pipeline.parallelism.value = 1
flink.api-response-cache-population.parallelism.value  = 1
flink.pg.data.publish.api.pipeline.parallelism.value = 1
flink.upi-relay-pipeline.parallelism.value = 10

# the below pms api values will be removed when PmsServiceForPromo class will get removed
# pms api
pms.service.base.url                 = http://localhost:1234
pms.service.accrefservice.url        = /pms/admin/int/v1/product/mapping
pms.service.read.timeout             = 1000
pms.client.secret                    = 1234
pms.service.socket.timeout           = 1000
pms.service.connection.timeout       = 1000

beneficiary.client.secret                    = 1234

bankData.blackListed.dccId = 1
bankData.blackListed.key.deviceId.value.acquirerId = {1:1}
bankData.whiteListed.SchemeCodes = SAINF,SAIND,SALRF,SALRY,WMNSA,WMNSF,SRCIT,SRCIF,BSBDF,BSBDA,MIIND,MIINF,CAIND,CAINF

payment-history-alias = payment_history_alias

index.name.prefix = payment-history

updateElasticSearchTemplate                      = false
updateSchemaRegistry                             = false
mapOf.KafkaTopic.Schema                          = {\
                                                      'middleware_transaction_history_wallet_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails' \
                                                    }
mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'payment_history_template': 'esTemplate.json', \
                                                      'udir_dump_template': 'udirDumpTemplate.json', \
                                                      'uth_cst_template': 'udirTemplate.json' \
                                                   }
env.based.prefix =

updateElasticSearchV2Template                      = false
mapOf.ElasticSearchV2Template.templateFile          = {\
                                                      'spend_history_template': 'spendDocEsTemplate.json' \
                                                   }

updateElasticSearchDcTemplate                      = false
mapOf.ElasticSearchDcTemplate.templateFile          = {\
                                                      'payment_history_template': 'esTemplate.json,es2Template.json', \
                                                      'udir_dump_template': 'udirDumpTemplate.json', \
                                                      'uth_cst_template': 'udirTemplate.json',\
                                                      'spend_history_template': 'spendDocEsTemplate.json' \
                                                   }

updateMandateElasticSearchTemplate                      = true
mapOf.MandateElasticSearchTemplate.templateFile          = {\
                                                      'mandate_info_template': 'mandateInfoEsTemplate.json', \
                                                      'mandate_activity_template': 'mandateActivityEsTemplate.json' \
                                                   }

thd.cache.expiry.in.seconds=7200
white.listed.visible.reportCode.list = 20213
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
aerospike.namespace                               = test
aerospike.host-name                               = localhost
aerospike.port                                    = 3000
pg.custId.cache.ttl = 7200

# 15 Days in Sec.
account.ref.no.cache.expiryTime = 1296000

updated.index.month.list = payment-history-08-2020
ts.pending.blacklisted.reportCodes = 20261,20263,20264,20265,20275,20276,20277

spring.datasource.url=jdbc:h2:mem:pth_pc;
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.configuration.maximum-pool-size=30
spring.datasource.driver-class-name=org.h2.Driver
hibernate.hbm2ddl.auto=create
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=create
hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.show-sql=true

cache.scheduler.initial.delay.string = ${random.int(1800000)}
cache.scheduler.fixed.rate.string    = 3600000

fd.whitelisted.users = -1

white.listed.users.list = 124,3436,PayeeId1,PayeeId2,**********,********,********,12345,**********,*********,**********,**********,**********

#isWhiteListingRequired = 1 :: whiteListing required for some users
#if whiteListing required for new user, add on the list above instead of changing flag to -1
#isWhiteListingRequired = -1 :: whiteListing not required or open for all
#isWhiteListingRequired = 0 :: close for all
is.white.listing.required.for.chat = 1
is.white.listing.required.for.single.doc = 1

upi.whitelisted.users = 1234
#Prometheus Monitoring Constants
prometheus.explorer                                         = INGESTER
prometheus.hostname                                         = localhost
prometheus.port                                             = 9200

cart.whiteListedUsers = 123,-1
cart.baseUrl = localgost:8080
cart.uri = /v3/order/fetch/bulk
cart.timeout =1000
cart.connection.timeout = 1000
cart.socket.timeout = 1000
cart.whitelistedMerchantVerticalIds = 1,2,204,174,173,199,131,176
ondc.verticalId = 204
cart.whitelisted.userIds.list.for.narration = -1
cart.http.max.connection = 100
cart.http.max.connection.per.route = 100

token.baseUrl = localgost:8080
token.uri = /v1/authorize
token.timeout =1000
token.connection.timeout = 1000
token.socket.timeout = 1000
token.clientKey = abc
token.clientSecret =def
#23hr 55 minutes
token.cache.time = 86100
#Merchant logo Base url
base.url.for.merchant.logo = https://catalog-staging.paytm.com/images/
url.for.ondc.logo = https://assetscdn1.paytm.com/images/catalog/view_item/1519347/*************.png

envBased.jacocoSupport=false

#bank-oauth
bank.oauth.service.base.url                        = http://localhost:9091
bank.oauth.service.url.category                    = /bank-oauth/ext
bank.oauth.client.id                               = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET
bank.oauthClient.connectionProvider.maxConnections      = 500
bank.oauthClient.connectionProvider.acquireTimeout      = 45000
bank.oauthClient.connectionProvider.maxIdleTime         = 5
bank.oauth.client.connection.timeout                    = 2000
bank.oauth.read.timeout                                 = 2000
bank.oauth.socket.timeout                               = 2000

callOauthForUserImageUrl = true
pushUserInUserImageUrlKafka = true
userImagePushModifiedObjectToEs = false
updateUserImage = false

es.bulk.flush.interval.value = 100

upi.udir.es.bulk.flush.interval.value = 100
upi.udir.es.bulk.flush.action.value = 10

#upi check status
upi.status.check.connection.timeout = 1000
upi.secret.key = 1234567
upi.baseUrl = http://upisecure-staging.orgk.com
upi.status.check.url = /upi/int/txn/uth/view
upi.mandate.status.check.url = /upi/int/mandate/txn/uth/view
upi.relay.secret.key = jewaih345FEDjfk
upi.relay.baseUrl = https://upi-proxy-staging-internal.paytm.com

countOfTxns.metric.for.status.and.source.required = true

static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/

search.ingestion.whilelisted.users = -1

uthCategory.setter.map = {PG:{'P2M','P2M_REFUND'},PPBL_PG:{'P2M','P2M_REFUND'},UPI:{'P2M','P2M_REFUND','RECURRING_MANDATE','IPO_MANDATE','LITE_TOPUP_MANDATE','SBMD_MANDATE','ONE_TIME_MANDATE'},PPBL:{'P2M_REFUND','P2M'}}

#change the below value to 0 when we require to block all users
is.whitelisting.required.for.update.via.uth.pipeline = 1
p2mAndAddAndPay.whitelisting.required.for.chat = 1

rollOut.percent.for.update.via.uth.pipeline = 100
p2mAndAddAndPay.rollOut.percent.for.chat = 10.50

p2p.whitelisting.required.for.chat = -1
p2p.rollOut.percent.for.chat = 100

userImageNullImageTtlInSec = 172800
userImageIgnoreNullImageInCacheForOauthCall = true

uth.filter.applied = true
cart.filter.applied = true
merged.filter.applied = true
prometheus.aspect.prefix = V2_
pipeline.name.suffix =
cache.key.prefix =
cache.key.prefix.dc.pipelines = DC_

#DynamoClient Configs
dynamo.secret-key = ********************
dynamo.access-key = uXxSMypuD1TWWUQbEePgNaw2RaYgqly7c702nf5H
dynamo.connection-timeout = 1000
dynamo.client-execution-timeout = 1000
dynamo.request-timeout = 1000
dynamo.socket-timeout = 1000

dynamo.max-error-retries = 10

flink.tags-enricher.parallelism.value = 1

# 3 * 24 * 60 * 60 * 1000 (3 days).
nonTransactingUserCache.refresh.time = 259200000
# TTL 7Days
nonTransactingUser.cache.expiry.in.seconds = 604800
nonTransactingUserCacheService.enable = true
nonTransactingUserCache.rollOut.percentage = 0
nonTransactingUser.white.listed.users.list = 413345538,243463250,1750211,18738814,18286455,245178554,347748179,957604,1242662593,58627652,191733975,286665205,600182887,342090935,197176405,255971089,231017873,108980782,25335912,600283353,1185507635,1172512886,244889640,203130745,134590356,29783428,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,599605539,230084261,272527877,123368804,9661621,204402215,131639348,54517546,324512031,221106539,262721253,423215038,97964488,183417583,2556302,16211563,399697124,418569378,117523406,20511073,3770465,256599059,108768010,132686264,24971888,31143459,24396310,243152226,3131732,235647667,1261698840,301350106,587272468,175355059,17357855,186617689,337851179,18232972,20122075,31857089,221254683,7202709,152008896,1187000305,3583761,114432758,149649162,1064619405,126712218,195741439,5892884,311026020,290923433,1407103,3253901,12646069,10857212,223555563,1284033,1080853,2953086,25612800,6236193,217145117,4478554,231701817,1025968896,188952843,11395214,417711068,190077351,*********,2164859,330511075,308804658,285892061,********,7881770,29486281,55772598,125262162,11201492,506042876,144435616,109464264,99392518,9178274,285908411,18011324,22237406,78349274,269241912,259788295,293383009,27044578,24534571,57437884,16386605,22116696,738496,1089807822,2844400,298028227,183503419,1132104848,243009112,22551101,1210439670,1112511404,196578757,110036284,50831904,255453517,298394047,7202709,56612796,437535,217145117,413345538,29155048,10647321,186488641,114324348,5301036,221443355,44195192
is.nonTransactingUser.white.listed.users.list.enabled = 1
#TTL 5min
listingApi.cache.expiry.in.seconds = 300
listingUserCache.enable = true
listingUserCache.rollOut.percentage = 100
is.listingUserCache.white.listed.users.list.enabled = 1
listingUserCache.white.listed.users.list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,*********,********,1422994344,1428969782,243463250

listingCache.delay.between.deleteAndSave.milliseconds = 4000

retry-threshold-for-chat-pipeline = 2


# no of grouping days buffer
# 30 min
dc.txn.grouping.fromDate = 1800000
# 30 min
dc.txn.grouping.toDate = 1800000
# 5 days
dc.refund.txn.grouping.fromDate = 432000000
# 7 days
dc.refund.txn.grouping.toDate = 604800000

domesticDc.rollOut.percentage = 100
domesticDc.whitelisting.required = 1
domesticDc.white.listed.users.list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,*********,********,1422994344,1428969782,243463250
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration
retryPipelinePushLimit = 1

#Spend Whitelisting Properties
spend.white.listed.users.list = 413345538,243463250,1750211,18738814,18286455,245178554,347748179,957604,1242662593,58627652,191733975,286665205,600182887,342090935,197176405,255971089,231017873,108980782,25335912,600283353,1185507635,1172512886,244889640,203130745,134590356,29783428,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,599605539,230084261,272527877,123368804,9661621,204402215,131639348,54517546,324512031,221106539,262721253,423215038,97964488,183417583,2556302,16211563,399697124,418569378,117523406,20511073,3770465,256599059,108768010,132686264,24971888,31143459,24396310,243152226,3131732,235647667,1261698840,301350106,587272468,175355059,17357855,186617689,337851179,18232972,20122075,31857089,221254683,7202709,152008896,1187000305,3583761,114432758,149649162,1064619405,126712218,195741439,5892884,311026020,290923433,1407103,3253901,12646069,10857212,223555563,1284033,1080853,2953086,25612800,6236193,217145117,4478554,231701817,1025968896,188952843,11395214,417711068,190077351,*********,2164859,330511075,308804658,285892061,********,7881770,29486281,55772598,125262162,11201492,506042876,144435616,109464264,99392518,9178274,285908411,18011324,22237406,78349274,269241912,259788295,293383009,27044578,24534571,57437884,16386605,22116696,738496,1089807822,2844400,298028227,183503419,1132104848,243009112,22551101,1210439670,1112511404,196578757,110036284,50831904,255453517,298394047,7202709,56612796,437535,217145117,413345538,29155048,10647321,186488641,114324348,5301036,221443355,44195192
is.white.listing.required.for.spend = 1
rollOut.percent.for.spend = 0

spend.index.name.prefix = spend-history
spend-history-alias = spend_history_alias

#10 min expiry time
spend.doc.set.cache.expiry.time = 600
spend.es.bulk.flush.action.value                    = 1
spend.es.bulk.flush.interval.value                  = 10

upi-udir-Index-alias = payment_history_alias
upi-udir-index.name.prefix = payment-history
upi-udir-dump-Index-alias = udir_dump_alias
upi-udir-dump-index.name.prefix = udir-dump

# 1 Sec delay for month Doc creation
delay.for.month.agg.creation                        = 1000

#tag aggregation retry count threshold
retry.count.limit.for.tag.agg                        = 10

rollout.config-list[0].percentage = 0
rollout.config-list[0].whitelisting-required = -1
rollout.config-list[0].user-list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,*********,********,1428969782,243463250,1000400001
rollout.config-list[0].whitelisting-for = imps

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = -1
rollout.config-list[1].whitelisting-for = uthNtuCache

rollout.config-list[2].percentage = 100
rollout.config-list[2].whitelisting-required = -1
rollout.config-list[2].whitelisting-for = upiPassbookListingCache

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = -1
rollout.config-list[3].whitelisting-for = p2p_inward_failure_pending_events_filter_rollout

rollout.config-list[4].percentage = 0
rollout.config-list[4].whitelisting-required = -1
rollout.config-list[4].user-list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,*********,********,1428969782,243463250,18323360
rollout.config-list[4].whitelisting-for = stop1stEventEsCall

rollout.config-list[5].percentage = 0
rollout.config-list[5].whitelisting-required = 1
rollout.config-list[5].user-list = 315936344,144435616,417711068,506042876,109464264,413345538,190077351,7881770,*********,********,1428969782,243463250,1000400001,**********
rollout.config-list[5].whitelisting-for = omsIntegration

isNtuCacheEnabled = true
isUpiPassbookCacheEnabled = true

txn.event.max.days.diff.from.current.to.process.txn = 60

mandate-flow-event-days-limit = 180

#For limiting the updates range on aws and dc.
ingestion.end.date.dc = 2023-04-01 00:00:00.000 +0530
ingestion.start.date.aws = 2023-03-01 00:00:00.000 +0530

partialEmiEventRetryPipelinePushLimit = 2

# need to analyse these below properties: stop.firstEvent.esCallFor, stop.firstEvent.esCallFor.timediff whenever new source added.
stop.firstEvent.esCallFor = UPI,WALLET,PPBL,TS
stop.firstEvent.esCallFor.timediff = 60000

isKafkaPushForParallelWriteEnabled = true

lag.cache.update.interval = 1000
lag.cache.expiry.time     = 10

updatesApi.oldestTxn.cache.expiry.seconds = 604800
updatesApi.oldestTxn.cache.minTimeGap.days = 5
updatesApi.oldestTxn.cache.maxTimeGap.days = 60

chatApi.baseUrl = https://chat-external-txn-nonprod.paytmdgt.io
chatApi.uri = /pcchat/v1/chat/uth/notification
chatApi.connect.timeout = 100
chatApi.socket.timeout = 100
chatApi.connection.request.timeout = 100
chatApi.secret.key = paytm-chat-uth-jwt-notification-********-5
chatApi.client.id =  uth_user_1

#Need to add details of API Properly
pgDataApi.baseUrl = https://api.paytmbank.com
pgDataApi.uri = /payments-history-v2/ext/v1/fetchPgData
pgDataApi.connect.timeout = 1000
pgDataApi.socket.timeout = 1000
pgDataApi.connection.request.timeout = 1000
pgDataApi.secret.key = dummy
pgDataApi.client.id =  OCL_HOME
pgData.cut.off.date.in.epoch =  *************

recap.baseUrl = http://localhost:10030
recap.url = /payments-history-v2/ext/v1/spend-analytics/rewindData

online.deals.verticalId  = 66
gv.verticalId            = 66
offline.deals.verticalId = 174
online.deals.logoUrl     = https://assetscdn1.paytm.com/images/catalog/view_item/2283581/*************.png
gv.logoUrl               = https://consumergv.paytm.com/gv-custom-files/images/gift-cards.png
offline.deals.logoUrl    = https://assetscdn1.paytm.com/images/catalog/view_item/1799274/*************.png

recentTxns.cache.expiry.time.seconds = 300

flinkBoPanelSource.enabled = false
paytm.tpap.handles.list = paytm,pthdfc,ptyes,ptaxis,ptsbi
min.diff.to.process.relay.events = 0

default-retry-count-for-events-enabled = false

recurring-mandate-history-go-live-date = 15-06-2024 00:00:00
ipo-mandate-history-go-live-date = 08-11-2024 00:00:00

#PMS Cust Api properties
pms.custId.api.baseUrl = https://tpappms-staging1.paytm.com
pms.custId.api.uri = /upi/ext/meta/v2/fetch/custId
pms.custId.api.timeout = 1000
pms.custId.api.connection.timeout = 1000
pms.custId.api.socket.timeout = 1000
pms.custId.api.http.max.connection = 100
pms.custId.api.http.max.connection.per.route = 20
pms.custId.api.secret.key = pmsservice

autoTagging.details.aerospike.set.name = auto_tagging_details_set
autoTagging.aerospike.namespace                = pth
autoTagging.aerospike.host-name                = localhost
autoTagging.aerospike.port                     = 3000