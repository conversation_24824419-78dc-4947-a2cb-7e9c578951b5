package com.org.panaroma.ingester.streamPipeline.CacheUpdater;

import static com.org.panaroma.ingester.constants.PipelineConstants.INGESTOR;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.aerospike.client.AerospikeException;
import com.org.panaroma.commons.cache.dto.ListingSpecificCacheData;
import com.org.panaroma.commons.cache.dto.NonTransactingUserCacheData;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.ingester.IngesterApplication;
import com.org.panaroma.ingester.dto.streamDTO.BaseStreamDto;
import com.org.panaroma.ingester.streamPipeline.MockCommonBeans;
import com.org.panaroma.ingester.streamingframework.testUtils.FlinkCustomCollectingSink;
import com.org.panaroma.ingester.streamingframework.testUtils.FlinkCustomJobTester;
import com.org.panaroma.ingester.streamingframework.testUtils.FlinkCustomManualSource;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import lombok.extern.log4j.Log4j2;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = IngesterApplication.class)
@ActiveProfiles(value = "test")
@Log4j2
public class cacheUpdaterPipelineTest extends MockCommonBeans {

	@Autowired
	cacheUpdaterPipelineWithCustomSource cacheUpdaterPipelineWithCustomSource;

	FlinkCustomManualSource<CacheUpdaterKafkaDto> cacheUpdaterManualSource;

	FlinkCustomCollectingSink<CacheUpdaterKafkaDto> cacheUpdaterTargetSink;

	FlinkCustomCollectingSink<CacheUpdaterKafkaDto> cacheUpdaterRetrySink;
	static Map<String, ListingSpecificCacheData> mockuthNtuCacheSet;
	static Map<String, ListingSpecificCacheData> mockUpiListingCacheSet;

	private static Map<String, Map<String, ListingSpecificCacheData>> cacheNameToCacheMapping;

	Map<CacheInfo, String> cacheMetaInfo = new HashMap<>();

	static class AnswerGetAndUpdateCall implements Answer, Serializable {

		Boolean isGet;

		AnswerGetAndUpdateCall(Boolean isGet) {
			this.isGet = isGet;
		}

		@Override
		public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
			Object[] args = invocationOnMock.getArguments();
			if (isGet) {
				return cacheNameToCacheMapping.get(((CacheInfo) args[1]).getCacheName()).get((String) args[0]);
			}
			else {
				cacheNameToCacheMapping.get(((CacheInfo) args[2]).getCacheName())
					.put((String) args[0], (ListingSpecificCacheData) args[1]);
				return null;
			}
		}

	}

	private void populateCache() {
		mockuthNtuCacheSet = new HashMap<>();
		mockuthNtuCacheSet.put("123456", new ListingSpecificCacheData(null, true, 1L, 1L, null));
		mockuthNtuCacheSet.put("nonTransactingUser", new ListingSpecificCacheData(null, true, 1L, 1L, null));
		mockuthNtuCacheSet.put("transactingUser", new ListingSpecificCacheData(null, false, 2L, 2L, null));
		Map<String, Map<String, ListingSpecificCacheData>> map = new HashMap<>();
		map.put(CacheInfo.UTH_NTU_CACHE.getCacheName(), mockuthNtuCacheSet);
		map.put(CacheInfo.UPI_PASSBOOK_LISTING_CACHE.getCacheName(), mockUpiListingCacheSet);
		cacheNameToCacheMapping = map;
	}

	private void setMockResponses() throws Exception {
		Mockito.doAnswer(new AnswerGetAndUpdateCall(true)).when(aerospikeCacheClient).getListingCacheData(any(), any());
		Mockito.doAnswer(new AnswerGetAndUpdateCall(false))
			.when(aerospikeCacheClient)
			.saveNtuCacheData(any(), any(), any());
		Mockito.doThrow(new AerospikeException("Testing Connection Timeout"))
			.when(aerospikeCacheClient)
			.saveNtuCacheData(eq("retry"), any(ListingSpecificCacheData.class), any(CacheInfo.class));
	}

	@Test
	public void testNonTransactingUserAdaptor() throws Exception {
		populateCache();
		log.warn("uth cache : {}", mockuthNtuCacheSet);
		log.warn("upi cache : {}", mockUpiListingCacheSet);
		setMockResponses();
		startCacheUpdaterPipeline();

		// All test cases where sinks are empty should be tested here.
		try {

			// testNewNonTransactingUserEventFromHistory();
			testFirstTransactionEventFromMainPipeline();
			testOtherThanFirstEventFromMainPipeline();
			// testFromDateChangesEventFromHistory();
			testEmptyTargetAndRetrySink();

		}
		catch (TimeoutException e) {
			e.printStackTrace();
		}

		// All test cases where sinks must have data should be tested here.
		try {
			commonRetryCases();
		}
		catch (TimeoutException e) {
			e.printStackTrace();
		}

		FlinkCustomJobTester.stopTest();
	}

	private void testFirstTransactionEventFromMainPipeline() throws TimeoutException {
		// In this case there are two scenarios

		// 1st One :- When there is no entry to this user in cache.

		CacheUpdaterKafkaDto newUser = new CacheUpdaterKafkaDto("abc_123", cacheMetaInfo, "newUser", null, false, null,
				null, INGESTOR, 11L, null);
		cacheUpdaterManualSource.sendRecord(newUser);
		CacheUpdaterKafkaDto targetData = cacheUpdaterTargetSink.poll();
		Assert.assertEquals(new NonTransactingUserCacheData(false, 3L),
				mockuthNtuCacheSet.get(newUser.getEntityId()).getIsNonTransactingUser());
		Assert.assertNull(targetData);

		// 2nd One :- When the user is already marked nonTransacting in cache
		// (isNonTransactingUser = true).
		// :- In this case cache will be updated as event's isNonTransactingUser flag is
		// false.
		CacheUpdaterKafkaDto newTransaction = new CacheUpdaterKafkaDto("abcd_123", cacheMetaInfo, "nonTransactingUser",
				null, false, null, null, INGESTOR, 12L, null);
		cacheMetaInfo.put(CacheInfo.UTH_NTU_CACHE, newTransaction.getEntityId());
		cacheUpdaterManualSource.sendRecord(newTransaction);

		targetData = cacheUpdaterTargetSink.poll();

		Assert.assertEquals(new NonTransactingUserCacheData(false, 3L),
				mockuthNtuCacheSet.get(newTransaction.getEntityId()).getIsNonTransactingUser());
		Assert.assertNull(targetData);

		// If we get data for any older txn (older than our from date) than cache will be
		// updated.
		CacheUpdaterKafkaDto olderTransaction = new CacheUpdaterKafkaDto("abcd_123", cacheMetaInfo,
				"nonTransactingUser", null, false, null, null, INGESTOR, 13L, null);
		cacheMetaInfo.put(CacheInfo.UTH_NTU_CACHE, olderTransaction.getEntityId());
		cacheUpdaterManualSource.sendRecord(olderTransaction);
		targetData = cacheUpdaterTargetSink.poll();
		Assert.assertEquals(new NonTransactingUserCacheData(false, 0L),
				mockuthNtuCacheSet.get(olderTransaction.getEntityId()).getIsNonTransactingUser());
		Assert.assertNull(targetData);

	}

	private void testOtherThanFirstEventFromMainPipeline() throws TimeoutException {

		// When the user is already marked transacting in cache (isNonTransactingUser =
		// false).
		// :- In this case there are two sub scenarios
		// First one :- We get an event of new transaction.(In this case cache will be
		// updated).
		CacheUpdaterKafkaDto transactingUser = new CacheUpdaterKafkaDto("abcde_123", cacheMetaInfo, "transactingUser",
				null, false, null, null, INGESTOR, 14L, null);
		cacheMetaInfo.put(CacheInfo.UTH_NTU_CACHE, transactingUser.getEntityId());
		cacheUpdaterManualSource.sendRecord(transactingUser);
		CacheUpdaterKafkaDto targetData = cacheUpdaterTargetSink.poll();
		Assert.assertEquals(new NonTransactingUserCacheData(false, 3L),
				mockuthNtuCacheSet.get(transactingUser.getEntityId()).getIsNonTransactingUser());
		Assert.assertNull(targetData);

		// Second one :- We get an event for old txn (Older than one month).(In this case
		// cache will be updated).
		CacheUpdaterKafkaDto transactingUserData = new CacheUpdaterKafkaDto("abcde_123", cacheMetaInfo,
				"transactingUser", null, false, null, null, INGESTOR, 15L, null);
		cacheMetaInfo.put(CacheInfo.UTH_NTU_CACHE, transactingUser.getEntityId());
		cacheUpdaterManualSource.sendRecord(transactingUserData);
		targetData = cacheUpdaterTargetSink.poll();
		Assert.assertEquals(new NonTransactingUserCacheData(false, 0L),
				mockuthNtuCacheSet.get(transactingUserData.getEntityId()).getIsNonTransactingUser());
		Assert.assertNull(targetData);

	}

	private void testEmptyTargetAndRetrySink() throws TimeoutException {

		CacheUpdaterKafkaDto cacheUpdaterCommonData = new CacheUpdaterKafkaDto("abcde_123", cacheMetaInfo, "123456",
				null, false, null, null, INGESTOR, 18L, null);
		cacheMetaInfo.put(CacheInfo.UTH_NTU_CACHE, cacheUpdaterCommonData.getEntityId());
		cacheUpdaterManualSource.sendRecord(cacheUpdaterCommonData);
		CacheUpdaterKafkaDto targetData = cacheUpdaterTargetSink.poll();
		Assert.assertNull(targetData);
		targetData = cacheUpdaterRetrySink.poll();
		Assert.assertNull(targetData);
	}

	private void commonRetryCases() throws TimeoutException {

		// If some exception occurred in main pipeline while dealing with cache.
		// Than that event will not be updated in cache directly by main pipeline.
		// And it will be pushed to nonTransactingUser kafka topic.
		Map<String, String> metaInfo = new HashMap<>();
		CacheUpdaterKafkaDto cacheUpdaterCommonData = new CacheUpdaterKafkaDto("forRetry", cacheMetaInfo, "retry", null,
				false, null, null, INGESTOR, 19L, metaInfo);
		cacheMetaInfo.put(CacheInfo.UTH_NTU_CACHE, cacheUpdaterCommonData.getEntityId());
		CacheUpdaterKafkaDto retryData;
		cacheUpdaterManualSource.sendRecord(cacheUpdaterCommonData);
		retryData = cacheUpdaterRetrySink.poll();

		Assert.assertEquals(cacheUpdaterCommonData, retryData);
		Assert.assertEquals("1", retryData.getMetaInfo().get(BaseStreamDto.RETRY_KEY));

		cacheUpdaterCommonData.getMetaInfo().put(BaseStreamDto.RETRY_KEY, "2");

		cacheUpdaterManualSource.sendRecord(cacheUpdaterCommonData);
		retryData = cacheUpdaterRetrySink.poll();

		Assert.assertEquals(cacheUpdaterCommonData, retryData);
		Assert.assertEquals("3", retryData.getMetaInfo().get(BaseStreamDto.RETRY_KEY));

		// Testing more than 5 (Default) retries.

		cacheUpdaterCommonData.getMetaInfo().put(BaseStreamDto.RETRY_KEY, "6");

		cacheUpdaterManualSource.sendRecord(cacheUpdaterCommonData);
		retryData = cacheUpdaterRetrySink.poll();
		Assert.assertEquals("7", retryData.getMetaInfo().get(BaseStreamDto.RETRY_KEY));

	}

	private void startCacheUpdaterPipeline() throws Exception {
		StreamExecutionEnvironment exe = cacheUpdaterPipelineWithCustomSource
			.buildTestPipeline(cacheUpdaterPipelineWithCustomSource.PIPELINE_NAME)
			.getExecutionEnvironment();
		FlinkCustomJobTester.startTest(exe);
		cacheUpdaterManualSource = cacheUpdaterPipelineWithCustomSource.getManualSource();
		cacheUpdaterTargetSink = cacheUpdaterPipelineWithCustomSource.getTargetSink();
		cacheUpdaterRetrySink = cacheUpdaterPipelineWithCustomSource.getRetrySink();
	}

}
