server.port = 9091

#Kafka and ES properties
source.kafka.topic                                 = transformed-data-topic
bootstrap.servers                                  = localhost:9092
group.id                                           = upi-consumer-group
#zookeeper.connect    = localhost:2181
#
#name                 = upi-stream-consumer
#instance_count       = 1
#poll_time            = 100
#consumer_class_name  = org.apache.kafka.clients.consumer.KafkaConsumer
#message_class        = com.paytm.bank.objects.request.KafkaConsumerEntity
#enable.auto.commit   = false
#value.deserializer   = org.apache.kafka.common.serialization.StringDeserializer
#key.deserializer     = org.apache.kafka.common.serialization.StringDeserializer
#max.poll.interval.ms = 3000000
#max.poll.records     = 20

kafka.bootstrap-servers                            = *************:9092,*************:9092,*************:9092
kafka.confluent-kafka-registry-url                 = http://kafka-schema-registry.default:80/
es-socket-timeout                                = 1000

es.target[0].indexName                             = payment-history
es.target[0].port                                  = 9200
es.target[0].hosts                                 = 127.0.0.1
es.target[0].pipeline-name                         = upi
es.target[0].requestType                          = insert


es.target[1].indexName                             = payment-history
es.target[1].port                                  = 9200
es.target[1].hosts                                 = 127.0.0.1
es.target[1].pipeline-name                         = pg
es.target[1].requestType                           = insert

es.target[2].indexName                             = payment-history
es.target[2].port                                  = 9200
es.target[2].hosts                                 = 127.0.0.1
es.target[2].pipeline-name                         = userImageUrl
es.target[2].requestType                           = update

es.target[3].indexName                            = uth-cst
es.target[3].port                                 = 9200
es.target[3].hosts                                = 127.0.0.1
es.target[3].pipeline-name                        = udir
es.target[3].requestType                          = generic-insert

es-host-list                                        = localhost
es-port                                             = 9200
es-connect-request-timeout            = 1000



#Kakfa Source config
kafka.source-list[0].topic                         = upi-data
kafka.source-list[0].bootstrap-servers             = localhost:9092
kafka.source-list[0].consumer-group-name           = upi-consumer-group
kafka.source-list[0].pipeline-name                 = upi
kafka.source-list[0].max-poll-records              = 50

#Kakfa Source config
kafka.source-list[1].topic                         = pg-data
kafka.source-list[1].bootstrap-servers             = localhost:9092
kafka.source-list[1].consumer-group-name           = pg-consumer-group
kafka.source-list[1].pipeline-name                 = pg
kafka.source-list[1].max-poll-records              = 50

kafka.source-list[4].topic                        = uth_localisation_data
kafka.source-list[4].bootstrap-servers            = **************:9092,**************:9092,**************:9092
kafka.source-list[4].consumer-group-name          = marketplace-consumer-group
kafka.source-list[4].pipeline-name                = marketplace
kafka.source-list[4].confluent-kafka-registry-url = http://kafka-schema-registry:80/
kafka.source-list[4].max-poll-records             = 50

kafka.source-list[5].topic                        = user_image_url_data
kafka.source-list[5].bootstrap-servers            = **************:9092,**************:9092,**************:9092
kafka.source-list[5].consumer-group-name          = user-image-url-group
kafka.source-list[5].pipeline-name                = userImageUrl
kafka.source-list[5].confluent-kafka-registry-url = http://kafka-schema-registry:80/
kafka.source-list[5].max-poll-records             = 50

kafka.source-list[6].topic                        = uth_upi_udir_cst_complaint_data
kafka.source-list[6].bootstrap-servers            = localhost:9092
kafka.source-list[6].consumer-group-name          = uth-udir-consumer-group
kafka.source-list[6].pipeline-name                = udir
kafka.source-list[6].max-poll-records             = 50
kafka.source-list[6].retry-target.retry-topic                  = uth_upi_udir_cst_complaint_data
kafka.source-list[6].retry-target.bootstrap-servers            = localhost:9092
kafka.source-list[6].retry-target.batch-size-bytes             = 3000
kafka.source-list[6].retry-target.linger-ms                    = 15
kafka.source-list[6].retry-target.request-timeout-ms           = 5000
kafka.source-list[6].retry-target.producers-pool-size          = 5
kafka.source-list[6].retry-target.transaction-timeout          = 60000
kafka.source-list[6].serialization-type                        = String


#es.target[0].indexName = payment-history
#es.target[0].port = 9200
#es.target[0].hosts = *************,***********
#
#
##Kakfa Source config
#kafka.source-list[0].topic                        = middleware_payments_transaction_history_transformed_data
#kafka.source-list[0].bootstrap-servers            = *************:9092,*************:9092,*************:9092
#kafka.source-list[0].consumer-group-name          = transaction-history-group


check.pointing.enable                              = false

retry.kafka-list[0].pipelineName                   = upi
retry.kafka-list[0].topic                          = middleware_transaction_history_upi_data

retry.kafka-list[1].pipelineName                   = wallet
retry.kafka-list[1].topic                          = middleware_transaction_history_wallet_data

retry.kafka-list[2].pipelineName                   = pg
retry.kafka-list[2].topic                          = middleware_transaction_history_pg_data

flink.checkpoint.url                               = s3://flink-checkpoints-middleware
flink.enableCheckpointing.interval                 = 100
flink.minPauseBetweenCheckpoints.interval          = 10000
flink.pg.parallelism.value                         = 84
flink.ppbl-pg.parallelism.value                   = 1
flink.upi.parallelism.value                        = 20
flink.retry-pipeline.parallelism.value             = 1
flink.default.parallelism.value                    = 1
flink.wallet.parallelism.value                     = 20
flink.cbs.parallelism.value                       = 20
flink.ts.parallelism.value                       = 20
flink.cbs-converter.parallelism.value             = 20
flink.chat.parallelism.value                       = 20
flink.user_image_url.parallelism.value             = 20
flink.promo-upi-pipeline.parallelism.value = 1
flink.api-response-cache-population.parallelism.value  = 1
flink.upi-relay-pipeline.parallelism.value = 10
flink.toggle-visibility-pipeline.parallelism.value = 1


while.listed.users.list                            = 1234,12345
enable.whitelisting                                = true
es.bulk.flush.action.value                         = 1

flink.parallelism.enabled                          = true

pth.retry.exhausted.topic.name                  = pth_retry_exhausted_data

bank.utility.service.scheme= http
bank.utility.service.port=9091
bank.utility.service.host= localhost
bank.utility.service.path= /test2
utility.client.socket.timeout          = 2000
utility.client.tcp.connection.timeout    = 2000
utility.client.request.timeout=8000
marketplace.retryTime=150000
marketplace.localise.cache.time =-1
marketplace.unlocalise.cache.time=86400
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
test.controller.enabled=false
batch.size=50
payment-history-alias = payment_history_alias
index.name.prefix = payment-history
elastic-search-index-prefix = payment-history-

#15 minutes
pg.p2m.refund.cache.time.in.seconds                = 900
pg.p2m.refund.cache.socket.timeout.in.seconds      = 500
pg.p2m.refund.cache.total.timeout.in.seconds       = 900
pg.p2m.refund.cache.sleep.retries                  = 50

# the below pms api values will be removed when PmsServiceForPromo class will get removed
# pms api
pms.service.base.url                 = https://pms-ite.orgk.com
pms.service.accrefservice.url        = /pms/admin/int/v1/product/mapping
pms.service.read.timeout             = 1000
pms.client.secret                    = M4lAh62VbnZkBGGciUtb
pms.service.socket.timeout           = 1000
pms.service.connection.timeout       = 1000

aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
aerospike.namespace                = banksvc
aerospike.host-name                = middlewareaerospike-service
aerospike.port                     = 3000

# 15 Days in Sec.
account.ref.no.cache.expiryTime = 1296000

updateElasticSearchTemplate                      = true
updateSchemaRegistry                             = true
mapOf.KafkaTopic.Schema                          = {\
                                                      'middleware_transaction_history_wallet_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_retry_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_retry_exhausted_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_retry_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_backfill_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_upi_backfill_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_wallet_backfill_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_upi_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_upi_relay_data' : 'com.org.panaroma.commons.dto.TransactionHistoryDetails' \
                                                    }
mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'payment_history_template': 'esTemplate.json', \
                                                      'udir_dump_template': 'udirDumpTemplate.json', \
                                                      'uth_cst_template': 'udirTemplate.json' \
                                                   }

updateElasticSearchDcTemplate                      = true
mapOf.ElasticSearchDcTemplate.templateFile          = {\
                                                      'payment_history_template': 'esTemplate.json', \
                                                      'udir_dump_template': 'udirDumpTemplate.json', \
                                                      'uth_cst_template': 'udirTemplate.json',\
                                                      'spend_history_template': 'spendDocEsTemplate.json' \
                                                   }

updateMandateElasticSearchTemplate                      = true
mapOf.MandateElasticSearchTemplate.templateFile          = {\
                                                      'mandate_info_template': 'mandateInfoEsTemplate.json', \
                                                      'mandate_activity_template': 'mandateActivityEsTemplate.json' \
                                                   }
env.based.prefix =
thd.cache.expiry.in.seconds=7200


bankData.blackListed.reportCodes = 1
reportcodes.blocked.for.ShowInListing = 60202,20701,80203,20702,60301
reportcodes.blocked.for.grouping = 60202,20701,20702
bankPassbook.Rptcodes.whilelisted.userslist = 1
bankPassbook.Rptcodes.whilelisteding.percent= 100
bankData.blackListed.dccId = 1
bankData.blackListed.key.deviceId.value.acquirerId = {1:1}
bankData.whiteListed.SchemeCodes = SAINF,SAIND,SALRF,SALRY,WMNSA,WMNSF,SRCIT,SRCIF,BSBDF,BSBDA,MIIND,MIINF,CAIND,CAINF

#Prometheus Monitoring Constants
prometheus.explorer                                         = INGESTER
prometheus.hostname                                         = ${CUSTOM_MONITORING_SERVICE_HOST}
prometheus.port                                             = ${CUSTOM_MONITORING_SERVICE_PORT}

cache.scheduler.initial.delay.string = ${random.int(1800000)}
cache.scheduler.fixed.rate.string    = 3600000
cart.whiteListedUsers = 123

#bank-oauth
bank.oauth.service.base.url                        = http://localhost:9091
bank.oauth.service.url.category                    = /bank-oauth/ext
bank.oauth.client.id                               = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET
bank.oauthClient.connectionProvider.maxConnections      = 500
bank.oauthClient.connectionProvider.acquireTimeout      = 45000
bank.oauthClient.connectionProvider.maxIdleTime         = 5
bank.oauth.client.connection.timeout                    = 2000
bank.oauth.read.timeout                                = 2000
bank.oauth.socket.timeout                               = 2000

callOauthForUserImageUrl = true
pushUserInUserImageUrlKafka = true
userImagePushModifiedObjectToEs = true
updateUserImage = true
whitelistedMerchantVerticalIds = -1

static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/

spring.jpa.hibernate.ddl-auto                    = none
spring.datasource.url                            = **********************************
spring.datasource.username                       = root
spring.datasource.password                       = ${PTH_MYSQL_PASSWORD}
spring.datasource.driver-class-name              = com.mysql.jdbc.Driver
spring.jpa.show-sql                              = true
spring.datasource.configuration.maximum-pool-size=30
search.ingestion.whilelisted.users = -1

uthCategory.setter.map = {PG:{'P2M','P2M_REFUND','ADD_AND_PAY'},UPI:{'P2M','P2M_REFUND','RECURRING_MANDATE','IPO_MANDATE','LITE_TOPUP_MANDATE','SBMD_MANDATE','ONE_TIME_MANDATE'},PPBL:{'P2M_REFUND','P2M'}}

#change the below value to 0 when we require to block all users
is.whitelisting.required.for.update.via.uth.pipeline = 1
p2mAndAddAndPay.whitelisting.required.for.chat = -1
p2p.whitelisting.required.for.chat = -1

p2p.rollOut.percent.for.chat = 100
rollOut.percent.for.update.via.uth.pipeline = 100
p2mAndAddAndPay.rollOut.percent.for.chat = 100
#keeping ttl as 2 days
userImageNullImageTtlInSec = 172800
userImageIgnoreNullImageInCacheForOauthCall = true

cache.key.prefix.dc.pipelines = DC_

retryPipelinePushLimit = 1

domesticDc.rollOut.percentage = 100
domesticDc.whitelisting.required = 1
domesticDc.white.listed.users.list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1422994344,1428969782,243463250


spend.index.name.prefix = spend-history
spend-history-alias = spend_history_alias
spend.es.bulk.flush.action.value                    = 1
spend.es.bulk.flush.interval.value                  = 10

# 1 Sec delay for month Doc creation
delay.for.month.agg.creation                        = 1000

#tag aggregation retry count threshold
retry.count.limit.for.tag.agg                        = 10

rollout.config-list[0].percentage = 10
rollout.config-list[0].whitelisting-required = 1
rollout.config-list[0].user-list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1428969782,243463250
rollout.config-list[0].whitelisting-for = imps

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = 1
rollout.config-list[1].user-list= 417711068,506042876,413345538,57893554,315936344,243463250,1314078462,1404082587,1027880890,1361857062,600435113,366922136,490717868,255212505,1407581691,197999747,22003214,324830717,22376586
rollout.config-list[1].whitelisting-for = p2p_inward_failure_pending_events_filter_rollout

rollout.config-list[2].percentage = 100
rollout.config-list[2].whitelisting-required = 1
rollout.config-list[2].user-list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1428969782,243463250,1000400001,1000783608
rollout.config-list[2].whitelisting-for = chatDataApi

rollout.config-list[3].percentage = 0
rollout.config-list[3].whitelisting-required = 1
rollout.config-list[3].user-list = 315936344,144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1428969782,243463250,1000400001,1000783608
rollout.config-list[3].whitelisting-for = omsIntegration

rollout.config-list[4].percentage = 0
rollout.config-list[4].whitelisting-required = 0
rollout.config-list[4].user-list = 417711068,506042876,413345538,57893554,315936344,243463250,1314078462,1404082587,1027880890,1361857062,600435113,366922136,490717868,255212505,1407581691,22376586,22003214,197999747
rollout.config-list[4].whitelisting-for = autoTaggingFeature

rollout.config-list[5].percentage = 0
rollout.config-list[5].whitelisting-required = 1
rollout.config-list[5].user-list = 413345538,125262162,243463250,57893554,506042876,1361857062,22338243,44040170,1027880890,12231789
rollout.config-list[5].whitelisting-for = droolsRemoval

rollout.config-list[6].percentage = 0
rollout.config-list[6].whitelisting-required = 1
rollout.config-list[6].user-list = 413345538,125262162,243463250,57893554,506042876,1361857062,22338243,44040170,1027880890,12231789
rollout.config-list[6].whitelisting-for = fwdTxnDetailsRollOut

# 4 minutes
retry-config.maxSleep = 240000 
retry-config.retryLimitsMap[chat]maxRetryCount=5
retry-config.retryLimitsMap[chat].minRetryDelay=60000
retry-config.retryLimitsMap[chat].retryGap=exponential
retry-config.retryLimitsMap[chat].multiplier=5

upi-udir-Index-alias = uth_cst_alias
upi-udir-index.name.prefix = uth-cst
upi-udir-dump-Index-alias = udir_dump_alias
upi-udir-dump-index.name.prefix = udir-dump
upi.udir.es.bulk.flush.interval.value = 100
upi.udir.es.bulk.flush.action.value = 10

#For limiting the updates range on aws and dc.
ingestion.end.date.dc = 2023-04-01 00:00:00.000 +0530
ingestion.start.date.aws = 2023-03-01 00:00:00.000 +0530

configurableConstants.addMoneyRefundMerchantId = scwpay09224240900570

partialEmiEventRetryPipelinePushLimit = 2

# need to analyse these below properties: stop.firstEvent.esCallFor, stop.firstEvent.esCallFor.timediff whenever new source added.
stop.firstEvent.esCallFor = UPI,WALLET,PPBL,TS
stop.firstEvent.esCallFor.timediff = 60000

updatesApi.zeroDelta.cache.expiry.seconds = 604800
updatesApi.zeroDelta.deletedFlag.expiry.seconds = 30

isKafkaPushForParallelWriteEnabled = true
updatesApi.oldestTxn.cache.expiry.seconds = 604800
updatesApi.oldestTxn.cache.minTimeGap.days = 5
updatesApi.oldestTxn.cache.maxTimeGap.days = 60

lag.cache.update.interval = 1000
lag.cache.expiry.time     = 10

chatApi.baseUrl = https://chat-external-txn-nonprod.paytmdgt.io
chatApi.uri = /pcchat/v1/chat/uth/notification
chatApi.connect.timeout = 100
chatApi.socket.timeout = 100
chatApi.connection.request.timeout = 100
chatApi.secret.key = ${UTH_CHAT_SECRET_KEY}
chatApi.client.id =  ${UTH_CHAT_PUBLISH_API_CLIENT_ID}
chatApi.rollOut.percent.for.chatData = 10
is.whitelisting.required.for.chatApi = 1


configurable_properties.source.bo_panel = BOPanel
configurable_properties.source.bo_panel.prefixCheck = true
configurable_properties.source.bo_panel.prefixValue = flink.
configurable_properties-index-alias = configurable_properties_alias
configurable_properties.source.elastic_search = configurable_properties_alias

bootWithoutConfigs.boPanelConfigs = true

flinkBoPanelSource.emissionGap.seconds = 60
flinkBoPanelSource.loopSleepTime.seconds = 1
flinkBoPanelSource.enabled = true

bop.testing=123

#stage -> https://phs-staging-internal.paytm.com (old)
#K8s -> http://payment-helper-service.stage.svc.cluster.local
external.client.properties[BMSService].baseUrl: https://phs-staging-internal.paytm.com
external.client.properties[BMSService].secretKey: aB9htNYgOa
external.client.properties[BMSService].socketTimeout: 1000
external.client.properties[BMSService].connectionTimeout: 1000

recap.baseUrl = http://localhost:10030
recap.url = /payments-history-v2/ext/v1/spend-analytics/rewindData

online.deals.verticalId  = 66
gv.verticalId            = 66
offline.deals.verticalId = 174
online.deals.logoUrl     = https://assetscdn1.paytm.com/images/catalog/view_item/2283581/1699599295957.png
gv.logoUrl               = https://consumergv.paytm.com/gv-custom-files/images/gift-cards.png
offline.deals.logoUrl    = https://assetscdn1.paytm.com/images/catalog/view_item/1799274/1699276411188.png

recentTxns.cache.expiry.time.seconds = 300

cart.http.max.connection = 100
cart.http.max.connection.per.route = 100

internal-localization-service.base-url: http://localhost:10030
internal-localization-service.messages-path: /payments-history-v2/int/v1/getMessages
internal-localization-service.languages-path: /payments-history-v2/int/v1/getLanguages
internal-localization-service.connection-request-timeout: 1000
internal-localization-service.connect-timeout: 1000
internal-localization-service.socket-timeout: 1000
internal-localization-service.secret: xyz

paytm.tpap.handles.list = paytm,pthdfc,ptyes,ptaxis,ptsbi
min.diff.to.process.relay.events = 0

mandate-info-index-alias = mandate_info_alias
mandate-activity-index-alias = mandate_activity_alias

default-retry-count-for-events-enabled = false

static-mandate-history-merchant-vpa-logo-base-url = https://pwebassets.paytm.com/ocl-upi/upi/images/merchant-logo/

recurring-mandate-history-go-live-date = 15-06-2024 00:00:00
ipo-mandate-history-go-live-date = 08-11-2024 00:00:00
sbmd-mandate-history-go-live-date = 29-01-2025 00:00:00
one-time-mandate-history-go-live-date = 24-03-2025 00:00:00

autoTagging.details.aerospike.set.name = auto_tagging_details_set
autoTagging.aerospike.namespace                = banksvc
autoTagging.aerospike.host-name                = middlewareaerospike-service
autoTagging.aerospike.port                     = 3000

third-es-host-list                    = uth-es-app3-nlb-81e82784bb8b158a.elb.ap-south-1.amazonaws.com

bop.flink.oms.whitelisted.payment.method.list = [\"UPI\",\"CC\",\"DC\",\"NB\"]

max.days.diff.for.fwd.refund.txns.linking = 45