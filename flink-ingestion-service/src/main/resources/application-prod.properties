#Kafka and ES properties
es-host-list                                         = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es-port                                              = 9200

es-v2-host-list                                      = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es-v2-port                                           = 9200

es-dc-host-list                                      = uth-es-app2-nlb-85972d6b09d6e362.elb.ap-south-1.amazonaws.com
es-dc-port                                           = 9200

mandate-es-host-list                                 = uth-es-app-mandate-nlb-ba8f1f4864d65bfe.elb.ap-south-1.amazonaws.com

es-socket-timeout = 2000
es-max-retry-timeout = 2000
es-connect-timeout = 1000
elastic-search-index = payment_history_alias
es-connect-request-timeout = 1000

es.read.sourceMap.dc-main-pipeline.hostList = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.read.sourceMap.dc-main-pipeline.indexNamePrefix = payment-history

es.read.sourceMap.dc-uth-enricher-pipeline.hostList = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.read.sourceMap.dc-uth-enricher-pipeline.indexNamePrefix = payment-history

#TODO :- Need to change IPs of new ES cluster for prod.
es.read.sourceMap.es-insert-update-executor-pipeline.hostList = uth-es-app2-nlb-85972d6b09d6e362.elb.ap-south-1.amazonaws.com
es.read.sourceMap.es-insert-update-executor-pipeline.indexNamePrefix = payment-history

es.read.sourceMap.data-audit-pipeline.hostList = uth-es-app2-nlb-85972d6b09d6e362.elb.ap-south-1.amazonaws.com
es.read.sourceMap.data-audit-pipeline.indexNamePrefix = payment-history

es.read.sourceMap.mandate.hostList = uth-es-app-mandate-nlb-ba8f1f4864d65bfe.elb.ap-south-1.amazonaws.com
es.read.sourceMap.mandate.indexNamePrefix = mandate

es.read.sourceMap.mandate-retry.hostList = uth-es-app-mandate-nlb-ba8f1f4864d65bfe.elb.ap-south-1.amazonaws.com
es.read.sourceMap.mandate-retry.indexNamePrefix = mandate


es.target[0].indexName                            = payment-history
es.target[0].port                                 = 9200
es.target[0].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[0].pipeline-name                        = upi
es.target[0].requestType                          = insert

es.target[1].indexName                            = payment-history
es.target[1].port                                 = 9200
es.target[1].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[1].pipeline-name                        = pg
es.target[1].requestType                          = insert

es.target[2].indexName                            = payment-history
es.target[2].port                                 = 9200
es.target[2].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[2].pipeline-name                        = wallet
es.target[2].requestType                          = insert

es.target[3].indexName                            = payment-history
es.target[3].port                                 = 9200
es.target[3].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[3].pipeline-name                        = retry-pipeline
es.target[3].requestType                          = insert

es.target[4].indexName                            = payment-history
es.target[4].port                                 = 9200
es.target[4].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[4].pipeline-name                        = cbs
es.target[4].requestType                          = insert

es.target[5].indexName                            = payment-history
es.target[5].port                                 = 9200
es.target[5].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[5].pipeline-name                        = ts
es.target[5].requestType                          = insert

es.target[6].indexName                            = payment-history
es.target[6].port                                 = 9200
es.target[6].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[6].pipeline-name                        = pg-retry-v2
es.target[6].requestType                          = insert

es.target[7].indexName                            = payment-history
es.target[7].port                                 = 9200
es.target[7].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[7].pipeline-name                        = userImageUrl
es.target[7].requestType                          = update

es.target[8].indexName                            = payment-history
es.target[8].port                                 = 9200
es.target[8].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[8].pipeline-name                        = uth
es.target[8].requestType                          = update


es.target[9].indexName                            = payment-history
es.target[9].port                                 = 9200
es.target[9].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[9].pipeline-name                        = retry-back-filling
es.target[9].requestType                          = insert

es.target[10].indexName                            = payment-history
es.target[10].port                                 = 9200
es.target[10].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[10].pipeline-name                        = tags-enricher
es.target[10].requestType                          = update

es.target[11].indexName                            = spend-history
es.target[11].port                                 = 9200
es.target[11].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[11].pipeline-name                        = user-spend-docs-creator
es.target[11].requestType                          = insert
es.target[11].switch-adaptors-name                 = SpendEsAndCacheAdaptor

es.target[12].indexName                            = uth-cst
es.target[12].port                                 = 9200
es.target[12].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[12].pipeline-name                        = udir
es.target[12].requestType                          = generic-insert

es.target[13].indexName                            = payment-history
es.target[13].port                                 = 9200
es.target[13].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[13].pipeline-name                        = dc-main-pipeline
es.target[13].requestType                          = insert

es.target[14].indexName                            = payment-history
es.target[14].port                                 = 9200
es.target[14].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[14].pipeline-name                        = dc-uth-enricher-pipeline
es.target[14].requestType                          = update

es.target[15].indexName                            = payment-history
es.target[15].port                                 = 9200
es.target[15].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[15].pipeline-name                        = ppbl-pg
es.target[15].requestType                          = insert

#TODO :- Need to change IPs of new ES cluster for prod.
es.target[16].indexName                            = payment-history
es.target[16].port                                 = 9200
es.target[16].hosts                                = uth-es-app2-nlb-85972d6b09d6e362.elb.ap-south-1.amazonaws.com
es.target[16].pipeline-name                        = es-insert-update-executor-pipeline
es.target[16].requestType                          = insert
es.target[16].switch-adaptors-name                 = insertExecutor

#TODO :- Need to change IPs of new ES cluster for prod.
es.target[17].indexName                            = payment-history
es.target[17].port                                 = 9200
es.target[17].hosts                                = uth-es-app2-nlb-85972d6b09d6e362.elb.ap-south-1.amazonaws.com
es.target[17].pipeline-name                        = es-insert-update-executor-pipeline
es.target[17].requestType                          = update
es.target[17].switch-adaptors-name                 = updateExecutor

es.target[18].indexName                            = payment-history
es.target[18].port                                 = 9200
es.target[18].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[18].pipeline-name                        = oms
es.target[18].requestType                          = insert

es.target[19].indexName                            = payment-history
es.target[19].port                                 = 9200
es.target[19].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[19].pipeline-name                        = upi-relay-pipeline
es.target[19].requestType                          = insert

es.target[20].indexName                            = mandate
es.target[20].port                                 = 9200
es.target[20].hosts                                = uth-es-app-mandate-nlb-ba8f1f4864d65bfe.elb.ap-south-1.amazonaws.com
es.target[20].pipeline-name                        = mandate
es.target[20].requestType                          = insert

es.target[21].indexName                            = mandate
es.target[21].port                                 = 9200
es.target[21].hosts                                = uth-es-app-mandate-nlb-ba8f1f4864d65bfe.elb.ap-south-1.amazonaws.com
es.target[21].pipeline-name                        = mandate-retry
es.target[21].requestType                          = insert

es.target[22].indexName                            = payment-history
es.target[22].port                                 = 9200
es.target[22].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[22].pipeline-name                        = oms-refund
es.target[22].requestType                          = insert

es.target[23].indexName                            = payment-history
es.target[23].port                                 = 9200
es.target[23].hosts                                = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es.target[23].pipeline-name                        = toggle-visibility
es.target[23].requestType                          = insert

# Date: 14th Sept 2023 Migrate MSK to Self Managed Kafka with below IPs
# b-1.prod-v1-mid-uthmsk.9he4ww.c4.kafka.ap-south-1.amazonaws.com:9092,b-2.prod-v1-mid-uthmsk.9he4ww.c4.kafka.ap-south-1.amazonaws.com:9092,b-3.prod-v1-mid-uthmsk.9he4ww.c4.kafka.ap-south-1.amazonaws.com:9092,b-4.prod-v1-mid-uthmsk.9he4ww.c4.kafka.ap-south-1.amazonaws.com:9092,b-5.prod-v1-mid-uthmsk.9he4ww.c4.kafka.ap-south-1.amazonaws.com:9092,b-6.prod-v1-mid-uthmsk.9he4ww.c4.kafka.ap-south-1.amazonaws.com:9092

#Kakfa Source config
kafka.source-list[0].topic                        = pth_upi_data_v1
kafka.source-list[0].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[0].consumer-group-name          = upi-consumer-group-latest-17thMarch
kafka.source-list[0].pipeline-name                = upi
kafka.source-list[0].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[0].max-poll-records             = 300

#Kakfa Source config
kafka.source-list[1].topic                        = pth_pg_data_v2
kafka.source-list[1].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[1].consumer-group-name          = pg-consumer-group-dummy-latest-28ThFeb
kafka.source-list[1].pipeline-name                = pg
kafka.source-list[1].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[1].max-poll-records             = 500

kafka.source-list[39].topic                        = pth_pg_data
kafka.source-list[39].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[39].consumer-group-name          = pg-data-publish-consumer-group
kafka.source-list[39].pipeline-name                = pg-data-publish-api-pipeline
kafka.source-list[39].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[39].max-poll-records             = 500

kafka.source-list[2].topic                        = middleware_transaction_history_wallet_data
kafka.source-list[2].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[2].consumer-group-name          = wallet-consumer-group
kafka.source-list[2].pipeline-name                = wallet
kafka.source-list[2].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[2].max-poll-records             = 300

kafka.source-list[3].topic                        = pth_retry_data
kafka.source-list[3].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[3].consumer-group-name          = retry-consumer-group-latest-17thMarch
kafka.source-list[3].pipeline-name                = retry-pipeline
kafka.source-list[3].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[3].max-poll-records             = 50

kafka.source-list[4].topic                        = cbs-gg-bo-stiched-transaction-v2
kafka.source-list[4].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[4].consumer-group-name          = uth-gg-transaction-consumer-group
kafka.source-list[4].pipeline-name                = cbs
kafka.source-list[4].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[4].max-poll-records             = 300

kafka.source-list[5].topic                        = cbs-gg-bo-stiched-transaction-v2
kafka.source-list[5].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[5].consumer-group-name          = gg-transaction-consumer-group
kafka.source-list[5].pipeline-name                = cbs-converter
kafka.source-list[5].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[5].max-poll-records             = 100


kafka.source-list[6].topic                        = uth_localisation_data
kafka.source-list[6].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[6].consumer-group-name          = marketplace-consumer-group
kafka.source-list[6].pipeline-name                = marketplace
kafka.source-list[6].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[6].max-poll-records             = 50

kafka.source-list[7].topic                        = middleware_transaction_history_ts_data
kafka.source-list[7].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[7].consumer-group-name          = ts-transaction-consumer-group
kafka.source-list[7].pipeline-name                = ts
kafka.source-list[7].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[7].max-poll-records             = 50

kafka.source-list[8].topic                        = pth_merged_data
kafka.source-list[8].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[8].consumer-group-name          = chat-consumer-group_v2
kafka.source-list[8].pipeline-name                = chat
kafka.source-list[8].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[8].max-poll-records             = 100

kafka.source-list[9].topic                        = middleware_transaction_history_cart_data
kafka.source-list[9].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[9].consumer-group-name          = cart-consumer-group_v2
kafka.source-list[9].pipeline-name                = cart
kafka.source-list[9].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[9].max-poll-records             = 50

kafka.source-list[10].topic                        = middleware_transaction_history_pg_retry-v2_data
kafka.source-list[10].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[10].consumer-group-name          = pg-retry-v2-consumer-group
kafka.source-list[10].pipeline-name                = pg-retry-v2
kafka.source-list[10].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[10].max-poll-records             = 50

kafka.source-list[11].topic                        = middleware_transaction_history_chat_back_fill_data
kafka.source-list[11].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[11].consumer-group-name          = chat-back-filling-consumer-group
kafka.source-list[11].pipeline-name                = chat-back-filling
kafka.source-list[11].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[11].max-poll-records             = 50

kafka.source-list[12].topic                        = middleware_transaction_history_retry_v2_data
kafka.source-list[12].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[12].consumer-group-name          = retry-consumer-group-v2
kafka.source-list[12].pipeline-name                = retry-pipeline-v2
kafka.source-list[12].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[12].max-poll-records             = 200

kafka.source-list[13].topic                        = pth_recon_config_data
kafka.source-list[13].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[13].consumer-group-name          = recon-config-consumer-group_v2
kafka.source-list[13].pipeline-name                = recon
kafka.source-list[13].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[13].max-poll-records             = 50

kafka.source-list[14].topic                        = pth_pending_txn_data
kafka.source-list[14].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[14].consumer-group-name          = status-resolver-consumer-group_v2
kafka.source-list[14].pipeline-name                = status-resolver
kafka.source-list[14].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[14].max-poll-records             = 50

kafka.source-list[15].topic                        = cbs_gg_data_replicate
kafka.source-list[15].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[15].consumer-group-name          = cbs_golden_gate_consumer
kafka.source-list[15].pipeline-name                = van-pipeline
kafka.source-list[15].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[15].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[15].retry-target.retry-topic                  = cbs_gg_data_replicate
kafka.source-list[15].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[15].retry-target.batch-size-bytes             = 3000
kafka.source-list[15].retry-target.linger-ms                    = 15
kafka.source-list[15].retry-target.request-timeout-ms           = 5000
kafka.source-list[15].retry-target.producers-pool-size          = 5
kafka.source-list[15].retry-target.transaction-timeout          = 60000
kafka.source-list[15].serialization-type                        = String

kafka.source-list[16].topic                        = pth_user_image_url_data
kafka.source-list[16].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[16].consumer-group-name          = user-image-url-group_v2
kafka.source-list[16].pipeline-name                = userImageUrl
kafka.source-list[16].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[16].max-poll-records             = 50

#TODO : Need to change below topic. This will be diff from current using topic.
kafka.source-list[17].topic                        = pth_uth_enricher_data
kafka.source-list[17].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[17].consumer-group-name          = uth-transaction-enricher-consumer-group_v2
kafka.source-list[17].pipeline-name                = uth
kafka.source-list[17].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[17].max-poll-records             = 100

kafka.source-list[18].topic                        = middleware_transaction_history_back_fill_data
kafka.source-list[18].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[18].consumer-group-name          = upi-back-filling-consumer-group
kafka.source-list[18].pipeline-name                = upi-back-filling
kafka.source-list[18].max-poll-records             = 50

kafka.source-list[19].topic                        = middleware_transaction_history_retry_back_fill_data
kafka.source-list[19].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[19].consumer-group-name          = retry-back-fill-consumer-group
kafka.source-list[19].pipeline-name                = retry-back-filling
kafka.source-list[19].max-poll-records             = 50

kafka.source-list[20].topic                        = cbs-gg-bo-stiched-transaction-v2
kafka.source-list[20].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[20].consumer-group-name          = cbs-stream-consumer-group
kafka.source-list[20].pipeline-name                = cbs-stream
kafka.source-list[20].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[20].max-poll-records             = 300
#Retry Kafka Properties for this Source
kafka.source-list[20].retry-target.retry-topic                  = middleware_transaction_history_cbs_gg_stitch_retry_data
kafka.source-list[20].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[20].retry-target.batch-size-bytes             = 3000
kafka.source-list[20].retry-target.linger-ms                    = 15
kafka.source-list[20].retry-target.request-timeout-ms           = 5000
kafka.source-list[20].retry-target.producers-pool-size          = 5
kafka.source-list[20].retry-target.transaction-timeout          = 60000
kafka.source-list[20].serialization-type                        = String

kafka.source-list[21].topic                        = middleware_transaction_history_promo_internal_data
kafka.source-list[21].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[21].consumer-group-name          = promo-internal-consumer-group
kafka.source-list[21].pipeline-name                = promo
kafka.source-list[21].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[21].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[21].retry-target.retry-topic                  = middleware_transaction_history_promo_internal_data
kafka.source-list[21].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[21].retry-target.batch-size-bytes             = 3000
kafka.source-list[21].retry-target.linger-ms                    = 15
kafka.source-list[21].retry-target.request-timeout-ms           = 5000
kafka.source-list[21].retry-target.producers-pool-size          = 5
kafka.source-list[21].retry-target.transaction-timeout          = 60000
kafka.source-list[21].serialization-type                        = String

kafka.source-list[22].topic                        = middleware_transaction_history_cbs_gg_stitch_retry_data
kafka.source-list[22].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[22].consumer-group-name          = gg-stitch-retry-consumer-group
kafka.source-list[22].pipeline-name                = cbs-retry-pipeline
kafka.source-list[22].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[22].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[22].retry-target.retry-topic                  = middleware_transaction_history_cbs_gg_stitch_retry_data
kafka.source-list[22].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[22].retry-target.batch-size-bytes             = 3000
kafka.source-list[22].retry-target.linger-ms                    = 15
kafka.source-list[22].retry-target.request-timeout-ms           = 5000
kafka.source-list[22].retry-target.producers-pool-size          = 5
kafka.source-list[22].retry-target.transaction-timeout          = 60000
kafka.source-list[22].serialization-type                        = String

kafka.source-list[23].topic                        = pth_cache_updater_data
kafka.source-list[23].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[23].consumer-group-name          = pth-ntud-consumer-group-latest-28ThFeb
kafka.source-list[23].pipeline-name                = cache-updater-pipeline
kafka.source-list[23].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[23].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[23].retry-target.retry-topic                  = pth_cache_updater_data
kafka.source-list[23].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[23].retry-target.batch-size-bytes             = 3000
kafka.source-list[23].retry-target.linger-ms                    = 15
kafka.source-list[23].retry-target.request-timeout-ms           = 5000
kafka.source-list[23].retry-target.producers-pool-size          = 5
kafka.source-list[23].retry-target.transaction-timeout          = 60000
kafka.source-list[23].serialization-type                        = String

kafka.source-list[24].topic                        = cbs-gg-transaction
kafka.source-list[24].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[24].consumer-group-name          = cbs-gg-upi-recon-consumer
kafka.source-list[24].pipeline-name                = upi-recon-pipeline
kafka.source-list[24].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[24].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[24].retry-target.retry-topic                  = cbs-gg-transaction
kafka.source-list[24].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[24].retry-target.batch-size-bytes             = 3000
kafka.source-list[24].retry-target.linger-ms                    = 15
kafka.source-list[24].retry-target.request-timeout-ms           = 5000
kafka.source-list[24].retry-target.producers-pool-size          = 5
kafka.source-list[24].retry-target.transaction-timeout          = 60000
kafka.source-list[24].serialization-type                        = String

kafka.source-list[25].topic                        = uth_spend_user_kafka_config_data
kafka.source-list[25].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[25].consumer-group-name          = spend_user_kafka_config_consumer_group
kafka.source-list[25].pipeline-name                = user-spend-docs-creator
kafka.source-list[25].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[25].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[25].retry-target.retry-topic                  = uth_spend_user_kafka_config_data
kafka.source-list[25].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[25].retry-target.batch-size-bytes             = 3000
kafka.source-list[25].retry-target.linger-ms                    = 15
kafka.source-list[25].retry-target.request-timeout-ms           = 5000
kafka.source-list[25].retry-target.producers-pool-size          = 5
kafka.source-list[25].retry-target.transaction-timeout          = 60000
kafka.source-list[25].serialization-type                        = String

kafka.source-list[26].topic                        = uth_spend_user_month_agg_kafka_config_data
kafka.source-list[26].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[26].consumer-group-name          = user-analytics-month-agg_consumer_group
kafka.source-list[26].pipeline-name                = user-analytics-month-agg-creator
kafka.source-list[26].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[26].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[26].retry-target.retry-topic                  = uth_spend_user_month_agg_kafka_config_data
kafka.source-list[26].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[26].retry-target.batch-size-bytes             = 3000
kafka.source-list[26].retry-target.linger-ms                    = 15
kafka.source-list[26].retry-target.request-timeout-ms           = 5000
kafka.source-list[26].retry-target.producers-pool-size          = 5
kafka.source-list[26].retry-target.transaction-timeout          = 60000
kafka.source-list[26].serialization-type                        = String

kafka.source-list[27].topic                        = middleware_transaction_history_userid_fetcher_data
kafka.source-list[27].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[27].consumer-group-name          = uth-userid_fetcher-consumer-group
kafka.source-list[27].pipeline-name                = userId-fetcher-pipeline
kafka.source-list[27].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[27].max-poll-records             = 50
kafka.source-list[27].retry-target.retry-topic                  = middleware_transaction_history_userid_fetcher_data
kafka.source-list[27].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[27].retry-target.batch-size-bytes             = 3000
kafka.source-list[27].retry-target.linger-ms                    = 15
kafka.source-list[27].retry-target.request-timeout-ms           = 5000
kafka.source-list[27].retry-target.producers-pool-size          = 5
kafka.source-list[27].retry-target.transaction-timeout          = 60000
kafka.source-list[27].serialization-type                        = String

kafka.source-list[28].topic                        = pth_tag_source_config_data
kafka.source-list[28].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[28].consumer-group-name          = tag-agg-retry-consumer-group
kafka.source-list[28].pipeline-name                = tag-agg-retry-pipeline
kafka.source-list[28].max-poll-records             = 50

kafka.source-list[29].topic                        = pth_merged_data
kafka.source-list[29].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[29].consumer-group-name          = uth-promo-upi-consumer-group
kafka.source-list[29].pipeline-name                = promo-upi-pipeline
kafka.source-list[29].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[29].max-poll-records             = 50
kafka.source-list[29].retry-target.retry-topic                  = pth_merged_data
kafka.source-list[29].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[29].retry-target.batch-size-bytes             = 3000
kafka.source-list[29].retry-target.linger-ms                    = 15
kafka.source-list[29].retry-target.request-timeout-ms           = 5000
kafka.source-list[29].retry-target.producers-pool-size          = 5
kafka.source-list[29].retry-target.transaction-timeout          = 60000


kafka.source-list[30].topic                        = middleware_transaction_history_generic_retry_data
kafka.source-list[30].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[30].consumer-group-name          = uth-generic-retry-consumer-group
kafka.source-list[30].pipeline-name                = generic-retry-pipeline
kafka.source-list[30].poll-interval                = 300000
kafka.source-list[30].session-timeout              = 300000
kafka.source-list[30].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[30].max-poll-records             = 1
kafka.source-list[30].retry-target.retry-topic                  = middleware_transaction_history_generic_retry_data
kafka.source-list[30].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[30].retry-target.batch-size-bytes             = 3000
kafka.source-list[30].retry-target.linger-ms                    = 15
kafka.source-list[30].retry-target.request-timeout-ms           = 5000
kafka.source-list[30].retry-target.producers-pool-size          = 5
kafka.source-list[30].retry-target.transaction-timeout          = 60000
kafka.source-list[30].serialization-type                        = String



kafka.source-list[31].topic                        = uth_upi_udir_cst_complaint_data
kafka.source-list[31].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[31].consumer-group-name          = uth-udir-consumer-group
kafka.source-list[31].pipeline-name                = udir
kafka.source-list[31].max-poll-records             = 50
kafka.source-list[31].retry-target.retry-topic                  = uth_upi_udir_cst_complaint_data
kafka.source-list[31].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[31].retry-target.batch-size-bytes             = 3000
kafka.source-list[31].retry-target.linger-ms                    = 15
kafka.source-list[31].retry-target.request-timeout-ms           = 5000
kafka.source-list[31].retry-target.producers-pool-size          = 5
kafka.source-list[31].retry-target.transaction-timeout          = 60000
kafka.source-list[31].serialization-type                        = String

kafka.source-list[32].topic                        = uth_all_sources_dc_data
kafka.source-list[32].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[32].consumer-group-name          = uth-dc-main-consumer
kafka.source-list[32].pipeline-name                = dc-main-pipeline
kafka.source-list[32].max-poll-records             = 50

kafka.source-list[33].topic                        = uth_enricher_dc_data
kafka.source-list[33].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[33].consumer-group-name          = uth-enricher-dc-consumer
kafka.source-list[33].pipeline-name                = dc-uth-enricher-pipeline
kafka.source-list[33].max-poll-records             = 50

kafka.source-list[34].topic                        = middleware_transaction_history_ppbl_pg_data
kafka.source-list[34].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[34].consumer-group-name          = ppbl-pg-consumer-group
kafka.source-list[34].pipeline-name                = ppbl-pg
kafka.source-list[34].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[34].max-poll-records             = 500

kafka.source-list[35].topic                        = pth_es_insert_update_data
kafka.source-list[35].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[35].consumer-group-name          = uth-es-insert-update-consumer
kafka.source-list[35].pipeline-name                = es-insert-update-executor-pipeline
kafka.source-list[35].max-poll-records             = 50

kafka.source-list[36].topic                        = pth_audit_data
kafka.source-list[36].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[36].consumer-group-name          = uth-audit-consumer
kafka.source-list[36].pipeline-name                = data-audit-pipeline
kafka.source-list[36].max-poll-records             = 50

kafka.source-list[37].topic                        = pth_chat_data
kafka.source-list[37].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[37].consumer-group-name          = chat-data-publish-api-consumer
kafka.source-list[37].pipeline-name                = chat-data-publish-api-pipeline
kafka.source-list[37].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[37].max-poll-records             = 1000

kafka.source-list[38].topic                        = uth_year_in_rewind_cache_v1
kafka.source-list[38].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[38].consumer-group-name          = spend_consumer_group_recap_backfill
kafka.source-list[38].pipeline-name                = api-response-cache-population-pipeline
kafka.source-list[38].max-poll-records             = 50
kafka.source-list[38].retry-target.retry-topic                  = uth_year_in_rewind_cache_v1
kafka.source-list[38].retry-target.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[38].retry-target.batch-size-bytes             = 3000
kafka.source-list[38].retry-target.linger-ms                    = 15
kafka.source-list[38].retry-target.request-timeout-ms           = 5000
kafka.source-list[38].retry-target.producers-pool-size          = 5
kafka.source-list[38].retry-target.transaction-timeout          = 60000
kafka.source-list[38].serialization-type                        = String

kafka.source-list[40].topic                        = order_update
kafka.source-list[40].bootstrap-servers            = ordkafka-v35-internal.paytm.com:9092
kafka.source-list[40].consumer-group-name          = oms-consumer-latest-28ThFeb
kafka.source-list[40].pipeline-name                = oms
kafka.source-list[40].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[40].max-poll-records             = 1000

kafka.source-list[41].topic                        = pth_upi_relay_data
kafka.source-list[41].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[41].consumer-group-name          = upi-relay-consumer-group-latest-28ThFeb
kafka.source-list[41].pipeline-name                = upi-relay-pipeline
kafka.source-list[41].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[41].max-poll-records             = 300

kafka.source-list[42].topic                        = pth_mandate_data
kafka.source-list[42].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[42].consumer-group-name          = mandate-consumer-group-v3-latest-28ThFeb
kafka.source-list[42].pipeline-name                = mandate
kafka.source-list[42].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[42].max-poll-records             = 300

kafka.source-list[43].topic                        = pth_mandate_retry_data
kafka.source-list[43].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[43].consumer-group-name          = mandate-retry-consumer-group-v3-latest-28ThFeb
kafka.source-list[43].pipeline-name                = mandate-retry
kafka.source-list[43].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[43].max-poll-records             = 50

kafka.source-list[44].topic                        = pth_merged_data
kafka.source-list[44].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[44].consumer-group-name          = recent-txn-consumer-group
kafka.source-list[44].pipeline-name                = recent-txn
kafka.source-list[44].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[44].max-poll-records             = 100

kafka.source-list[45].topic                        = order_refund_update
kafka.source-list[45].bootstrap-servers            = ordkafka-v35-internal.paytm.com:9092
kafka.source-list[45].consumer-group-name          = pth-oms-refund-consumer-latest-28ThFeb
kafka.source-list[45].pipeline-name                = oms-refund
kafka.source-list[45].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[45].max-poll-records             = 1000

kafka.source-list[46].topic                        = pth_toggle_visibility_data
kafka.source-list[46].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[46].consumer-group-name          = toggle-visibility-consumer-group
kafka.source-list[46].pipeline-name                = toggle-visibility
kafka.source-list[46].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[46].max-poll-records             = 50

kafka.source-list[47].topic                        = pth_txn_tags_data
kafka.source-list[47].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.source-list[47].consumer-group-name          = tag-enricher-consumer-group
kafka.source-list[47].pipeline-name                = tags-enricher
kafka.source-list[47].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.source-list[47].max-poll-records             = 100


#Kafka Target Config
kafka.target-list[0].topic                        = middleware_transaction_history_ppbl_data
kafka.target-list[0].bootstrap-servers            = *************:9092,************:9092,*************:9092,*************:9092
kafka.target-list[0].pipeline-name                = cbs-converter
kafka.target-list[0].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[0].batch-size-bytes             = 3000
kafka.target-list[0].linger-ms                    = 15
kafka.target-list[0].request-timeout-ms           = 5000
kafka.target-list[0].producers-pool-size           = 5
kafka.target-list[0].transaction-timeout           = 60000

kafka.target-list[1].topic                        = pth_merged_data
kafka.target-list[1].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[1].pipeline-name                = upi
kafka.target-list[1].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[1].batch-size-bytes             = 80000
kafka.target-list[1].linger-ms                    = 5
kafka.target-list[1].request-timeout-ms           = 5000
kafka.target-list[1].producers-pool-size          = 5
kafka.target-list[1].transaction-timeout          = 60000

kafka.target-list[2].topic                        = pth_merged_data
kafka.target-list[2].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[2].pipeline-name                = wallet
kafka.target-list[2].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[2].batch-size-bytes             = 80000
kafka.target-list[2].linger-ms                    = 5
kafka.target-list[2].request-timeout-ms           = 5000
kafka.target-list[2].producers-pool-size          = 5
kafka.target-list[2].transaction-timeout          = 60000

kafka.target-list[3].topic                        = pth_merged_data
kafka.target-list[3].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[3].pipeline-name                = cbs
kafka.target-list[3].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[3].batch-size-bytes             = 80000
kafka.target-list[3].linger-ms                    = 5
kafka.target-list[3].request-timeout-ms           = 5000
kafka.target-list[3].producers-pool-size          = 5
kafka.target-list[3].transaction-timeout          = 60000

kafka.target-list[4].topic                        = pth_chat_data
kafka.target-list[4].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[4].pipeline-name                = chat
kafka.target-list[4].batch-size-bytes             = 80000
kafka.target-list[4].linger-ms                    = 5
kafka.target-list[4].request-timeout-ms           = 5000
kafka.target-list[4].producers-pool-size          = 5
kafka.target-list[4].transaction-timeout          = 60000

kafka.target-list[5].topic                        = middleware_transaction_history_pg_retry-v2_data
kafka.target-list[5].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[5].pipeline-name                = cart
kafka.target-list[5].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[5].batch-size-bytes             = 3000
kafka.target-list[5].linger-ms                    = 15
kafka.target-list[5].request-timeout-ms           = 5000
kafka.target-list[5].producers-pool-size           = 5
kafka.target-list[5].transaction-timeout           = 60000

kafka.target-list[6].topic                        = middleware_transaction_history_cart_data
kafka.target-list[6].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[6].pipeline-name                = sink2cart
kafka.target-list[6].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[6].batch-size-bytes             = 3000
kafka.target-list[6].linger-ms                    = 15
kafka.target-list[6].request-timeout-ms           = 5000
kafka.target-list[6].producers-pool-size          = 5
kafka.target-list[6].transaction-timeout          = 60000

kafka.target-list[7].topic                        = middleware_transaction_history_retry_v2_data
kafka.target-list[7].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[7].pipeline-name                = chat-back-filling
kafka.target-list[7].batch-size-bytes             = 3000
kafka.target-list[7].linger-ms                    = 15
kafka.target-list[7].request-timeout-ms           = 5000
kafka.target-list[7].producers-pool-size          = 5
kafka.target-list[7].transaction-timeout          = 60000

kafka.target-list[8].topic                        = pth_merged_data
kafka.target-list[8].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[8].pipeline-name                = retry-pipeline-v2
kafka.target-list[8].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[8].batch-size-bytes             = 3000
kafka.target-list[8].linger-ms                    = 15
kafka.target-list[8].request-timeout-ms           = 5000
kafka.target-list[8].producers-pool-size          = 5
kafka.target-list[8].transaction-timeout          = 60000

kafka.target-list[9].topic                        = pth_pending_txn_data
kafka.target-list[9].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[9].pipeline-name                = recon
kafka.target-list[9].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[9].batch-size-bytes             = 3000
kafka.target-list[9].linger-ms                    = 5
kafka.target-list[9].request-timeout-ms           = 5000
kafka.target-list[9].producers-pool-size          = 5
kafka.target-list[9].transaction-timeout          = 60000

kafka.target-list[10].topic                        = pth_upi_data_v1
kafka.target-list[10].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[10].pipeline-name                = status-resolver
kafka.target-list[10].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[10].batch-size-bytes             = 3000
kafka.target-list[10].linger-ms                    = 5
kafka.target-list[10].request-timeout-ms           = 5000
kafka.target-list[10].producers-pool-size          = 5
kafka.target-list[10].transaction-timeout          = 60000

#TODO : Need to change below topic. This will be diff from current using topic.
kafka.target-list[11].topic                        = pth_uth_enricher_data
kafka.target-list[11].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[11].pipeline-name                = sink2uth
kafka.target-list[11].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[11].batch-size-bytes             = 3000
kafka.target-list[11].linger-ms                    = 5
kafka.target-list[11].request-timeout-ms           = 5000
kafka.target-list[11].producers-pool-size          = 5
kafka.target-list[11].transaction-timeout          = 60000

kafka.target-list[12].topic                        = middleware_transaction_history_retry_back_fill_data
kafka.target-list[12].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[12].pipeline-name                = upi-back-filling
kafka.target-list[12].batch-size-bytes             = 3000
kafka.target-list[12].linger-ms                    = 15
kafka.target-list[12].request-timeout-ms           = 5000
kafka.target-list[12].producers-pool-size          = 5
kafka.target-list[12].transaction-timeout          = 60000

kafka.target-list[13].topic                        = pth_merged_data
kafka.target-list[13].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[13].pipeline-name                = uth
kafka.target-list[13].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[13].batch-size-bytes             = 80000
kafka.target-list[13].linger-ms                    = 5
kafka.target-list[13].request-timeout-ms           = 5000
kafka.target-list[13].producers-pool-size          = 5
kafka.target-list[13].transaction-timeout          = 60000

kafka.target-list[14].topic                        = middleware_transaction_history_promo_internal_data
kafka.target-list[14].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[14].pipeline-name                = cbs-stream
kafka.target-list[14].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[14].batch-size-bytes             = 3000
kafka.target-list[14].linger-ms                    = 15
kafka.target-list[14].request-timeout-ms           = 5000
kafka.target-list[14].producers-pool-size          = 5
kafka.target-list[14].transaction-timeout          = 60000
kafka.target-list[14].switch-adaptors-name         = PromoServiceInternalAdaptor

kafka.target-list[15].topic                        = pth_merged_data
kafka.target-list[15].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[15].pipeline-name                = retry-pipeline
kafka.target-list[15].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[15].batch-size-bytes             = 80000
kafka.target-list[15].linger-ms                    = 5
kafka.target-list[15].request-timeout-ms           = 5000
kafka.target-list[15].producers-pool-size          = 5
kafka.target-list[15].transaction-timeout          = 60000

kafka.target-list[20].topic                        = pth_pg_data
kafka.target-list[20].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[20].pipeline-name                = pg-data-publish-api-pipeline
kafka.target-list[20].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[20].batch-size-bytes             = 80000
kafka.target-list[20].linger-ms                    = 5
kafka.target-list[20].request-timeout-ms           = 5000
kafka.target-list[20].producers-pool-size          = 5
kafka.target-list[20].transaction-timeout          = 60000

kafka.target-list[21].topic                        = pth_merged_data
kafka.target-list[21].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[21].pipeline-name                = upi-relay-pipeline
kafka.target-list[21].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[21].batch-size-bytes             = 80000
kafka.target-list[21].linger-ms                    = 5
kafka.target-list[21].request-timeout-ms           = 5000
kafka.target-list[21].producers-pool-size          = 5
kafka.target-list[21].transaction-timeout          = 60000

#kafka.target-list[16].topic                        = ppbl_transactional
#kafka.target-list[16].bootstrap-servers            = ************:9092,***********:9092,************:9092,************:9092
#kafka.target-list[16].pipeline-name                = promo
#kafka.target-list[16].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[16].batch-size-bytes             = 3000
#kafka.target-list[16].linger-ms                    = 15
#kafka.target-list[16].request-timeout-ms           = 5000
#kafka.target-list[16].producers-pool-size          = 5
#kafka.target-list[16].transaction-timeout          = 60000
#kafka.target-list[16].switch-adaptors-name         = PromoServiceExternalAdaptor
#kafka.target-list[16].security-protocol            = SASL_PLAINTEXT
#kafka.target-list[16].security-sasl-mechanism      = PLAIN
#kafka.target-list[16].security-sasl-jaas-config    = org.apache.kafka.common.security.plain.PlainLoginModule required username="uth" password="${PROMO_KAFKA_SECURITY_PASSWORD}";

#kafka.target-list[17].topic                        = cbs-gg-van
#kafka.target-list[17].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[17].pipeline-name                = van-pipeline
#kafka.target-list[17].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[17].batch-size-bytes             = 3000
#kafka.target-list[17].linger-ms                    = 15
#kafka.target-list[17].request-timeout-ms           = 5000
#kafka.target-list[17].producers-pool-size          = 5
#kafka.target-list[17].transaction-timeout          = 60000
#kafka.target-list[17].switch-adaptors-name         = VanServiceAdaptor

#kafka.target-list[18].topic                        = middleware_transaction_history_promo_internal_data
#kafka.target-list[18].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[18].pipeline-name                = cbs-retry-pipeline
#kafka.target-list[18].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[18].batch-size-bytes             = 3000
#kafka.target-list[18].linger-ms                    = 15
#kafka.target-list[18].request-timeout-ms           = 5000
#kafka.target-list[18].producers-pool-size          = 5
#kafka.target-list[18].transaction-timeout          = 60000
#kafka.target-list[18].switch-adaptors-name         = PromoServiceInternalAdaptor

#kafka.target-list[19].topic                        = upi_consumers_promoengine
#kafka.target-list[19].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[19].pipeline-name                = promo
#kafka.target-list[19].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[19].batch-size-bytes             = 3000
#kafka.target-list[19].linger-ms                    = 15
#kafka.target-list[19].request-timeout-ms           = 5000
#kafka.target-list[19].producers-pool-size          = 5
#kafka.target-list[19].transaction-timeout          = 60000
#kafka.target-list[19].switch-adaptors-name         = PromoUpiServiceExternalAdaptor

#kafka.target-list[20].topic                        = uth_lien_recovery
##DR kafka Broker list:
##kafka.target-list[20].bootstrap-servers            = ************:9092,*************:9092,*************:9092,************:9092
##DC kafka Broker list:
#kafka.target-list[20].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[20].pipeline-name                = cbs-retry-pipeline
#kafka.target-list[20].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[20].batch-size-bytes             = 3000
#kafka.target-list[20].linger-ms                    = 15
#kafka.target-list[20].request-timeout-ms           = 5000
#kafka.target-list[20].producers-pool-size          = 5
#kafka.target-list[20].transaction-timeout          = 60000
#kafka.target-list[20].switch-adaptors-name         = LienServiceAdaptor

#kafka.target-list[21].topic                        = uth_lien_recovery
##DR kafka Broker list:
##kafka.target-list[20].bootstrap-servers            = ************:9092,*************:9092,*************:9092,************:9092
##DC kafka Broker list:
#kafka.target-list[21].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[21].pipeline-name                = cbs-stream
#kafka.target-list[21].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[21].batch-size-bytes             = 3000
#kafka.target-list[21].linger-ms                    = 15
#kafka.target-list[21].request-timeout-ms           = 5000
#kafka.target-list[21].producers-pool-size          = 5
#kafka.target-list[21].transaction-timeout          = 60000
#kafka.target-list[21].switch-adaptors-name         = LienServiceAdaptor

kafka.target-list[16].topic                        = pth_cache_updater_data
kafka.target-list[16].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[16].pipeline-name                = cache-updater-pipeline
kafka.target-list[16].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[16].batch-size-bytes             = 3000
kafka.target-list[16].linger-ms                    = 15
kafka.target-list[16].request-timeout-ms           = 5000
kafka.target-list[16].producers-pool-size          = 5
kafka.target-list[16].transaction-timeout          = 60000
kafka.target-list[16].switch-adaptors-name         = CommonCacheAdaptor,DetailCacheInvalidatorAdaptor,AppSideCacheDataAdaptor

kafka.target-list[17].topic                        = pth_cache_updater_data
kafka.target-list[17].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[17].pipeline-name                = sink2CacheUpdater
kafka.target-list[17].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[17].batch-size-bytes             = 3000
kafka.target-list[17].linger-ms                    = 15
kafka.target-list[17].request-timeout-ms           = 5000
kafka.target-list[17].producers-pool-size          = 5
kafka.target-list[17].transaction-timeout          = 60000
#
kafka.target-list[18].topic                        = pth_merged_data
kafka.target-list[18].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[18].pipeline-name                = sink2mergedDataForRetryFromChat
kafka.target-list[18].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[18].batch-size-bytes             = 3000
kafka.target-list[18].linger-ms                    = 5
kafka.target-list[18].request-timeout-ms           = 5000
kafka.target-list[18].producers-pool-size          = 5
kafka.target-list[18].transaction-timeout          = 60000

kafka.target-list[19].topic                        = pth_cache_updater_data
kafka.target-list[19].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[19].pipeline-name                = detail_cache_invalidator_sink
kafka.target-list[19].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[19].batch-size-bytes             = 3000
kafka.target-list[19].linger-ms                    = 15
kafka.target-list[19].request-timeout-ms           = 5000
kafka.target-list[19].producers-pool-size          = 5
kafka.target-list[19].transaction-timeout          = 60000

kafka.target-list[22].topic                        = pth_merged_data
kafka.target-list[22].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[22].pipeline-name                = oms-refund
kafka.target-list[22].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[22].batch-size-bytes             = 3000
kafka.target-list[22].linger-ms                    = 5
kafka.target-list[22].request-timeout-ms           = 5000
kafka.target-list[22].producers-pool-size          = 5
kafka.target-list[22].transaction-timeout          = 60000

kafka.target-list[23].topic                        = pth_merged_data
kafka.target-list[23].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-list[23].pipeline-name                = toggle-visibility
kafka.target-list[23].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-list[23].batch-size-bytes             = 80000
kafka.target-list[23].linger-ms                    = 5
kafka.target-list[23].request-timeout-ms           = 5000
kafka.target-list[23].producers-pool-size          = 5
kafka.target-list[23].transaction-timeout          = 60000

#
#kafka.target-list[25].topic                        = cbs-gg-bo-transaction
#kafka.target-list[25].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[25].pipeline-name                = upi-recon-pipeline
#kafka.target-list[25].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[25].batch-size-bytes             = 3000
#kafka.target-list[25].linger-ms                    = 15
#kafka.target-list[25].request-timeout-ms           = 5000
#kafka.target-list[25].producers-pool-size          = 5
#kafka.target-list[25].transaction-timeout          = 60000
#kafka.target-list[25].switch-adaptors-name         = UpiReconServiceAdaptor
#
#kafka.target-list[26].topic                        = uth_spend_user_kafka_config_data
#kafka.target-list[26].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[26].pipeline-name                = user-spend-docs-creator
#kafka.target-list[26].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[26].batch-size-bytes             = 3000
#kafka.target-list[26].linger-ms                    = 15
#kafka.target-list[26].request-timeout-ms           = 5000
#kafka.target-list[26].producers-pool-size          = 5
#kafka.target-list[26].transaction-timeout          = 60000
#kafka.target-list[26].switch-adaptors-name         = UserSpendConfigKafkaAdaptor
#
#kafka.target-list[27].topic                        = uth_spend_user_month_agg_kafka_config_data
#kafka.target-list[27].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[27].pipeline-name                = user-spend-docs-creator
#kafka.target-list[27].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[27].batch-size-bytes             = 3000
#kafka.target-list[27].linger-ms                    = 15
#kafka.target-list[27].request-timeout-ms           = 5000
#kafka.target-list[27].producers-pool-size          = 5
#kafka.target-list[27].transaction-timeout          = 60000
#kafka.target-list[27].switch-adaptors-name         = UserMonthSpendAggKafkaAdaptor
#
#kafka.target-list[28].topic                        = uth_spend_user_kafka_config_data
#kafka.target-list[28].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[28].pipeline-name                = userId-fetcher-pipeline
#kafka.target-list[28].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[28].batch-size-bytes             = 3000
#kafka.target-list[28].linger-ms                    = 15
#kafka.target-list[28].request-timeout-ms           = 5000
#kafka.target-list[28].producers-pool-size          = 5
#kafka.target-list[28].transaction-timeout          = 60000
#kafka.target-list[28].switch-adaptors-name         = UserIdFetcherAdaptor
#
#kafka.target-list[29].topic                        = uth_promo_upi_txn
#kafka.target-list[29].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[29].pipeline-name                = promo-upi-pipeline
#kafka.target-list[29].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[29].batch-size-bytes             = 3000
#kafka.target-list[29].linger-ms                    = 15
#kafka.target-list[29].request-timeout-ms           = 5000
#kafka.target-list[29].producers-pool-size          = 5
#kafka.target-list[29].transaction-timeout          = 60000
#kafka.target-list[29].switch-adaptors-name         = PromoUthAdaptor
#
#kafka.target-list[30].topic                        = middleware_transaction_history_merged_data
#kafka.target-list[30].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[30].pipeline-name                = ts
#kafka.target-list[30].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[30].batch-size-bytes             = 3000
#kafka.target-list[30].linger-ms                    = 15
#kafka.target-list[30].request-timeout-ms           = 5000
#kafka.target-list[30].producers-pool-size          = 5
#kafka.target-list[30].transaction-timeout          = 60000
#
#kafka.target-list[31].topic                        = uth_cache_updater_data
#kafka.target-list[31].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[31].pipeline-name                = detail_cache_invalidator_sink
#kafka.target-list[31].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[31].batch-size-bytes             = 3000
#kafka.target-list[31].linger-ms                    = 15
#kafka.target-list[31].request-timeout-ms           = 5000
#kafka.target-list[31].producers-pool-size          = 5
#kafka.target-list[31].transaction-timeout          = 60000
#
#kafka.target-list[32].topic                        = middleware_transaction_history_chat_data
#kafka.target-list[32].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[32].pipeline-name                = generic-retry-pipeline
#kafka.target-list[32].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[32].batch-size-bytes             = 3000
#kafka.target-list[32].linger-ms                    = 15
#kafka.target-list[32].request-timeout-ms           = 5000
#kafka.target-list[32].producers-pool-size          = 5
#kafka.target-list[32].transaction-timeout          = 60000
#kafka.target-list[32].switch-adaptors-name         = ChatAdaptor
#
#kafka.target-list[33].topic                        = uth_enricher_dc_data
#kafka.target-list[33].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[33].pipeline-name                = sink2DcUth
#kafka.target-list[33].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[33].batch-size-bytes             = 3000
#kafka.target-list[33].linger-ms                    = 15
#kafka.target-list[33].request-timeout-ms           = 5000
#kafka.target-list[33].producers-pool-size          = 5
#kafka.target-list[33].transaction-timeout          = 60000
#
#kafka.target-list[34].topic                        = middleware_transaction_history_chat_data
#kafka.target-list[34].bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
#kafka.target-list[34].pipeline-name                = chat-data-publish-api-pipeline
#kafka.target-list[34].confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/
#kafka.target-list[34].batch-size-bytes             = 3000
#kafka.target-list[34].linger-ms                    = 15
#kafka.target-list[34].request-timeout-ms           = 5000
#kafka.target-list[34].producers-pool-size          = 5
#kafka.target-list[34].transaction-timeout          = 60000


kafka.target-v2-list[0].kafka-client-name               = cart
kafka.target-v2-list[0].kafka-producer-key              = CART_DATA_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_cart_data
kafka.target-v2-list[0].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = chat_back_filling
kafka.target-v2-list[1].kafka-producer-key              = CHAT_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[1].topic                           = middleware_transaction_history_chat_back_fill_data
kafka.target-v2-list[1].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].confluent-kafka-registry-url    = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 5000

kafka.target-v2-list[2].kafka-client-name               = recon_config
kafka.target-v2-list[2].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[2].topic                           = pth_recon_config_data
kafka.target-v2-list[2].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].confluent-kafka-registry-url    = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-v2-list[2].linger-ms                       = 5
kafka.target-v2-list[2].request-timeout-ms              = 5000

kafka.target-v2-list[3].kafka-client-name               = status-resolver
kafka.target-v2-list[3].kafka-producer-key              = STATUS_RESOLVER_PRODUCER
kafka.target-v2-list[3].topic                           = pth_pending_txn_data
kafka.target-v2-list[3].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[3].batch-size-bytes                = 3000
kafka.target-v2-list[3].confluent-kafka-registry-url    = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-v2-list[3].linger-ms                       = 5
kafka.target-v2-list[3].request-timeout-ms              = 5000

kafka.target-v2-list[4].kafka-client-name               = userImageUrl
kafka.target-v2-list[4].kafka-producer-key              = USER_IMAGE_URL_DATA_PRODUCER
kafka.target-v2-list[4].topic                           = pth_user_image_url_data
kafka.target-v2-list[4].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[4].batch-size-bytes                = 3000
kafka.target-v2-list[4].confluent-kafka-registry-url    = http://schema-registry.uth-prod.paytm.local:8081/
kafka.target-v2-list[4].linger-ms                       = 15
kafka.target-v2-list[4].request-timeout-ms              = 5000

kafka.target-v2-list[5].kafka-client-name               = upi_back_filling
kafka.target-v2-list[5].kafka-producer-key              = UPI_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[5].topic                           = middleware_transaction_history_back_fill_data
kafka.target-v2-list[5].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[5].batch-size-bytes                = 3000
kafka.target-v2-list[5].linger-ms                       = 15
kafka.target-v2-list[5].request-timeout-ms              = 5000

kafka.target-v2-list[6].kafka-client-name               = search_back_filling
kafka.target-v2-list[6].kafka-producer-key              = UPI_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[6].topic                           = middleware_transaction_history_back_fill_data
kafka.target-v2-list[6].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[6].batch-size-bytes                = 3000
kafka.target-v2-list[6].linger-ms                       = 15
kafka.target-v2-list[6].request-timeout-ms              = 5000

kafka.target-v2-list[7].kafka-client-name               = USERID_FETCHER_KAFKA_CLIENT
kafka.target-v2-list[7].kafka-producer-key              = USERID_FETCHER_KAFKA_CLIENT_PRODUCER
kafka.target-v2-list[7].topic                           = middleware_transaction_history_userid_fetcher_data
kafka.target-v2-list[7].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[7].batch-size-bytes                = 3000
kafka.target-v2-list[7].linger-ms                       = 15
kafka.target-v2-list[7].request-timeout-ms              = 5000

kafka.target-v2-list[8].kafka-client-name               = TAG_AGG_SOURCE_CONFIG_KAFKA_CLIENT
kafka.target-v2-list[8].kafka-producer-key              = TAG_AGG_SOURCE_CONFIG_KAFKA_CLIENT
kafka.target-v2-list[8].topic                           = pth_tag_source_config_data
kafka.target-v2-list[8].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[8].batch-size-bytes                = 3000
kafka.target-v2-list[8].linger-ms                       = 15
kafka.target-v2-list[8].request-timeout-ms              = 5000


flink.tags-enricher.parallelism.value = 1


# These report codes were removed from blacklist during bank passbook migration task but now these are not required to be ingested, so adding them back to blacklist.
bankData.blackListed.reportCodes = 60202,20701,20702
#we get data for these reportcodes from other sources
reportcodes.blocked.for.ShowInListing = 60202,20701,80203,20702,60301
reportcodes.blocked.for.grouping = 60202,20701,20702
bankPassbook.Rptcodes.whilelisted.userslist = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********,*********,********,957604
bankPassbook.Rptcodes.whilelisteding.percent= 100
bankData.blackListed.dccId = 1
bankData.blackListed.key.deviceId.value.acquirerId = {1:1}
bankData.whiteListed.SchemeCodes = SAINF,SAIND,SALRF,SALRY,WMNSA,WMNSF,SRCIT,SRCIF,BSBDF,BSBDA,MIIND,MIINF,CAIND,CAINF,CAOTH,SBSHG,ECASH,RUPAY,SBCOM

kafka.bootstrap-servers            = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.confluent-kafka-registry-url = http://schema-registry.uth-prod.paytm.local:8081/

retry.kafka-list[0].pipelineName                  = upi
retry.kafka-list[0].topic                         = pth_upi_data_v1

retry.kafka-list[1].pipelineName                  = wallet
retry.kafka-list[1].topic                         = middleware_transaction_history_wallet_data

retry.kafka-list[2].pipelineName                  = pg
retry.kafka-list[2].topic                         = middleware_transaction_history_pg_data

retry.kafka-list[3].pipelineName                  = dc-retry-pipeline
retry.kafka-list[3].topic                         = uth_retry_dc_data

while.listed.users.list                           = 221106539,*********,2164859,22960051,25345284,123368804,9246658,330511075,308804658,1284033,98872,193080039,126753966,5506688,324512031,199511893,247871050,278530725,230084261,2891646,1020773145,429232688,131765382,341860463,31546363,132474812,55772598,191742149,183417583,104052480,244889640,6296725,304009358,1306150,3508338,1024806998,1284347,5445595,1286021,3567739,124257864,145792030,316472990,22615627,262721253,1647833,23188999,7082329,306171964,223191845,349664741,134052452,514469132,272527877,658562377,341860463,13534320,1590188,5459956,11707830,358192068,599605539,250432756
flink.checkpoint.url                              = s3://pth-flink-checkpoint-prod-381491835802/prod/
flink.enableCheckpointing.interval                = 100
flink.minPauseBetweenCheckpoints.interval         = 10000
flink.pg.parallelism.value                        = 10
flink.oms.parallelism.value                       = 5
flink.ppbl-pg.parallelism.value                   = 10
flink.upi.parallelism.value                       = 60
flink.retry-pipeline.parallelism.value            = 2
flink.default.parallelism.value                   = 1
flink.wallet.parallelism.value                    = 30
flink.cbs.parallelism.value                       = 60
flink.ts.parallelism.value                       = 2
flink.cbs-converter.parallelism.value             = 5
flink.pg.retry.v2.parallelism.value               = 7
flink.cart.parallelism.value                      = 2
flink.chat.parallelism.value                      = 1
flink.recent.txn.parallelism.value                = 5
flink.retry-pipeline-v2.parallelism.value         = 20
flink.chat-back-filling-pipeline.parallelism.value = 1
flink.recon.parallelism.value             = 1
flink.upiV2.parallelism.value                     = 1
flink.status-resolver.parallelism.value             = 5
flink.user_image_url.parallelism.value             = 1
flink.uth.parallelism.value=10
flink.upi-back-filling-pipeline.parallelism.value      = 1
flink.retry-back-filling-pipeline.parallelism.value      = 20
flink.promo.parallelism.value                     = 4
flink.cbs-stream.parallelism.value                = 10
flink.cbs-retry-pipeline.parallelism.value        = 1
flink.cache-updater-pipeline.parallelism.value = 10
flink.van-pipeline.parallelism.value = 1
flink.upi-recon-pipeline.parallelism.value        = 25
flink.user-spend-docs-creator.parallelism.value = 2
flink.user-analytics-month-agg-creator.parallelism.value = 1
flink.promo-upi-pipeline.parallelism.value = 20
userId-fetcher-pipeline.parallelism.value = 1
flink.generic-retry-pipeline.parallelism.value = 1
flink.dc-main-pipeline.parallelism.value = 1
flink.dc-uth-enricher-pipeline.parallelism.value = 1
flink.es-insert-update-executor-pipeline.parallelism.value = 60
flink.data-audit-pipeline.parallelism.value = 2
flink.chat.data.publish.api.pipeline.parallelism.value = 150
flink.pg.data.publish.api.pipeline.parallelism.value = 10
flink.api-response-cache-population.parallelism.value  = 60
flink.upi-relay-pipeline.parallelism.value = 10
flink.mandate-pipeline.parallelism.value = 10
flink.mandate-retry-pipeline.parallelism.value = 1
flink.oms.refund.parallelism.value             = 1
flink.backfilling-pipeline.parallelism.value = 1
flink.toggle-visibility-pipeline.parallelism.value = 1

enable.whitelisting                               = false
flink.parallelism.enabled                         = true
es.bulk.flush.action.value                        = 100

aerospike.namespace                               = pth
aerospike.host-name                               = uth-aerospike1-new.uth-prod.paytm.local
aerospike.port                                    = 3000
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 150
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
pg.custId.cache.ttl = 7200

# 19 Days in Sec.
account.ref.no.cache.expiryTime = 1641600

# the below pms api values will be removed when PmsServiceForPromo class will get removed
# pms api
pms.service.base.url                 = https://pms.orgk.com
pms.service.accrefservice.url        = /pms/admin/int/v1/product/mapping
pms.service.read.timeout             = 500
#pms.client.secret                    = ${MIDDLEWARE_TXN_PMS_CLIENT_SECRET}
pms.client.secret                    = MIDDLEWARE_TXN_PMS_CLIENT_SECRET
pms.service.socket.timeout           = 1000
pms.service.connection.timeout       = 1000

beneficiary.client.secret            = ${PTH_BENEFICIARY_SECRET}

pth.retry.exhausted.topic.name                  = pth_retry_exhausted_data

#MarketPlace-Utility
mrkplace.sectionId= 10
mrkplace.subSectionId=420
mrkplace.priority= 2
mrkplace.quality= 2
bank.utility.service.scheme=https
bank.utility.service.port=443
bank.utility.service.host=utilitiesv2.paytm.com
bank.utility.service.path=/v1/language/translation
utility.client.request.timeout=6000
utility.client.socket.timeout= 2000
utility.client.tcp.connection.timeout= 2000

marketplace.retryTime=150000
marketplace.localise.cache.time =-1
marketplace.unlocalise.cache.time=86400
test.controller.enabled=false
batch.size=50
#15 minutes
pg.p2m.refund.cache.time.in.seconds                = 900
pg.p2m.refund.cache.socket.timeout.in.seconds      = 500
pg.p2m.refund.cache.total.timeout.in.seconds       = 900
pg.p2m.refund.cache.sleep.retries                  = 50

payment-history-alias = payment_history_alias
index.name.prefix = payment-history


updateElasticSearchTemplate                      = true
updateSchemaRegistry                             = true
mapOf.KafkaTopic.Schema                          = {\
                                                      'middleware_transaction_history_wallet_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_pg_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_pg_data_v2': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_ppbl_pg_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_retry_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_retry_exhausted_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_retry_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_backfill_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_upi_backfill_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_wallet_backfill_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_upi_data_v1': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_ppbl_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_ts_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'uth_localisation_data':'com.org.panaroma.commons.dto.MarketplaceKafkaRecord', \
                                                      'pth_merged_data': 'com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail', \
                                                      'pth_chat_data': 'com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail', \
                                                      'pth_upi_relay_data' : 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_mandate_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_mandate_retry_data': 'com.org.panaroma.commons.dto.mandate.MandateBaseDto' \
                                                    }
mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'payment_history_template': 'esTemplate.json', \
                                                      'udir_dump_template': 'udirDumpTemplate.json', \
                                                      'uth_cst_template': 'udirTemplate.json', \
                                                      'mandate_info_template': 'mandateInfoEsTemplate.json', \
                                                      'mandate_activity_template': 'mandateActivityEsTemplate.json' \
                                                   }

updateElasticSearchV2Template                      = true
mapOf.ElasticSearchV2Template.templateFile          = {\
                                                      'spend_history_template': 'spendDocEsTemplate.json' \
                                                   }

updateElasticSearchDcTemplate                      = true
mapOf.ElasticSearchDcTemplate.templateFile          = {\
                                                      'payment_history_template': 'esTemplate.json,es2Template.json', \
                                                      'udir_dump_template': 'udirDumpTemplate.json', \
                                                      'uth_cst_template': 'udirTemplate.json', \
                                                      'spend_history_template': 'spendDocEsTemplate.json', \
                                                      'mandate_info_template': 'mandateInfoEsTemplate.json', \
                                                      'mandate_activity_template': 'mandateActivityEsTemplate.json' \
                                                   }

updateMandateElasticSearchTemplate                      = true
mapOf.MandateElasticSearchTemplate.templateFile          = {\
                                                      'mandate_info_template': 'mandateInfoEsTemplate.json', \
                                                      'mandate_activity_template': 'mandateActivityEsTemplate.json' \
                                                   }

white.listed.visible.reportCode.list = 20212,20211,20213,20221,20222,40107,20295,20270,20271,20215,20216,20218,30103,40103,40108,20273
thd.cache.expiry.in.seconds=3600

updated.index.month.list = payment-history-07-2020,payment-history-08-2020,payment-history-09-2020,payment-history-10-2020,payment-history-11-2020
ts.pending.blacklisted.reportCodes = 20261,20263,20264,20265,20275,20276,20277

cache.scheduler.initial.delay.string = ${random.int(1800000)}
cache.scheduler.fixed.rate.string    = 72000000

spring.jpa.hibernate.ddl-auto                    = none
spring.datasource.url                            = *****************************************************
spring.datasource.username                       = pth_pc_w
spring.datasource.password                       = ${MW_PTH_DB_PASSWORD}
spring.datasource.driver-class-name              = com.mysql.jdbc.Driver
spring.jpa.show-sql                              = true
spring.datasource.configuration.maximum-pool-size=30

fd.whitelisted.users = -1

cart.whiteListedUsers = -1

#23hr 55 minutes
token.cache.time = 86100

white.listed.users.list = 18286455,245178554,347748179,957604,1242662593,58627652,191733975,286665205,600182887,342090935,197176405,255971089,231017873,108980782,25335912,600283353,1185507635,1172512886,244889640,203130745,134590356,29783428,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,599605539,230084261,272527877,123368804,9661621,204402215,131639348,54517546,324512031,221106539,262721253,423215038,97964488,183417583,2556302,16211563,399697124,418569378,117523406,20511073,3770465,256599059,108768010,132686264,24971888,31143459,24396310,243152226,3131732,235647667,1261698840,301350106,587272468,175355059,17357855,186617689,337851179,18232972,20122075,31857089,221254683,7202709,152008896,1187000305,3583761,114432758,149649162,1064619405,126712218,195741439,5892884,311026020,290923433,1407103,3253901,12646069,10857212,223555563,1284033,1080853,2953086,25612800,6236193,217145117,4478554,231701817,1025968896,188952843,11395214,*********,*********,*********,2164859,330511075,308804658,285892061,********,7881770,29486281,55772598,125262162,11201492,*********,*********,*********,99392518,9178274,285908411,18011324,22237406,78349274,269241912,259788295,293383009,27044578,24534571,57437884,16386605,22116696,738496,1089807822,2844400,298028227,183503419,1132104848,243009112,22551101,1210439670,1112511404,196578757,110036284,50831904,255453517,298394047,7202709,********,437535,217145117,*********
#isWhiteListingRequired = 1 :: whiteListing required for some users
#isWhiteListingRequired = -1 :: whiteListing not required or open for all
#isWhiteListingRequired = 0 :: close for all
is.white.listing.required.for.chat = -1
is.white.listing.required.for.single.doc = 1

upi.whitelisted.users = -1

#Prometheus Monitoring Constants
prometheus.explorer                                         = INGESTER
#prometheus.hostname                                         = ${CUSTOM_MONITORING_SERVICE_HOST}
#prometheus.port                                             = ${CUSTOM_MONITORING_SERVICE_PORT}
prometheus.hostname                                         = localhost
prometheus.port                                             = 8130

cart.baseUrl = https://order-internal.paytm.com
cart.uri = /v3/order/fetch/bulk
cart.whitelistedMerchantVerticalIds =4,76,56,187,86,17,71,87,84,90,105,155,94,204,174,173,199,131,176
#cart.whitelisted.userIds.list.for.narration = *********,*********,*********,221106539,2164859,330511075,308804658,97964488,285892061,********,7881770,134590356,29486281,16211563,418569378,*********,*********,9246658
cart.whitelisted.userIds.list.for.narration = -1

#connection polling configuration
cart.timeout =500
cart.connection.timeout = 1000
cart.socket.timeout = 500
cart.http.max.connection = 30
cart.http.max.connection.per.route = 30

token.baseUrl = https://checkout-internal.paytm.com
token.uri = /v1/authorize
token.timeout =1000
token.connection.timeout = 1000
token.socket.timeout = 1000
#token.clientKey = ${clientKey}
#token.clientSecret =${clientSecret}
token.clientKey = clientKey
token.clientSecret = clientSecret
#Merchant logo Base url
base.url.for.merchant.logo = https://catalog.paytm.com/images/
url.for.ondc.logo = https://assetscdn1.paytm.com/images/catalog/view_item/1519347/*************.png
ondc.verticalId = 204
envBased.jacocoSupport=false

#PMS Cust Api properties
pms.custId.api.baseUrl = https://tpappms-internal.paytm.com
pms.custId.api.uri = /upi/ext/meta/v2/fetch/custId
pms.custId.api.timeout = 1000
pms.custId.api.connection.timeout = 1000
pms.custId.api.socket.timeout = 1000
pms.custId.api.http.max.connection = 100
pms.custId.api.http.max.connection.per.route = 20
pms.custId.api.secret.key = ${UPI_PMS_PTH_SECRET_KEY}


#bank-oauth
bank.oauth.service.base.url                        = https://oauth.paytm.com
bank.oauth.service.url.category                    = /bank-oauth/ext
#bank.oauth.client.id                               = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID}
#bank.oauth.client.secret                           = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET}
bank.oauth.client.id                               = payment-transaction-history-prod
bank.oauth.client.secret                           = ${PTH_OAUTHCONFIG_CLIENTSECRET}
bank.oauthClient.connectionProvider.maxConnections      = 500
bank.oauthClient.connectionProvider.acquireTimeout      = 10000
bank.oauthClient.connectionProvider.maxIdleTime         = 5
bank.oauth.client.connection.timeout                    = 800
bank.oauth.read.timeout                                 = 1000
bank.oauth.socket.timeout                               = 1000

callOauthForUserImageUrl = true
pushUserInUserImageUrlKafka = true
userImagePushModifiedObjectToEs = true
updateUserImage = true

es.bulk.flush.interval.value = 100

#upi check status
upi.status.check.connection.timeout = 1000
#upi.secret.key = ${UTH_UPI_SECRET_KEY}
upi.secret.key = ${UPI_PTH_SECRET_KEY}
upi.relay.secret.key = ${UPI_PTH_SECRET_KEY}
upi.baseUrl.for.mandate.txns = https://upisecure-internal.paytm.com
upi.baseUrl.for.non.mandate.txns = https://tpaptransactionalswitch-internal.paytm.com
upi.relay.baseUrl = https://upi-tpap-ocil-yes-gateway-switch.paytm.com
upi.status.check.url = /upi/int/txn/uth/view
upi.relay.status.check.url = /upi/int/tpap/gateway/mandate/payer/txn/uth/view
upi.mandate.status.check.url = /upi/int/mandate/txn/uth/view

countOfTxns.metric.for.status.and.source.required = true


static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/

uthCategory.setter.map = {PG:{'P2M','P2M_REFUND','ADD_AND_PAY'},PPBL_PG:{'P2M','P2M_REFUND','ADD_AND_PAY'},UPI:{'P2M','P2M_REFUND','RECURRING_MANDATE','IPO_MANDATE','LITE_TOPUP_MANDATE','SBMD_MANDATE','ONE_TIME_MANDATE'},PPBL:{'P2M_REFUND','P2M'}}

search.ingestion.whilelisted.users = -1
#search.ingestion.whilelisted.users = *********,*********,*********,221106539,2164859,330511075,308804658,97964488,285892061,********,7881770,134590356,29486281,16211563,418569378,*********,*********,118501134,3770465,9268107,99392518,*********,*********,11201492,599605539,9178274,957604,131765382,2953086

#change the below value to 0 when we require to block all users
is.whitelisting.required.for.update.via.uth.pipeline = 1
p2mAndAddAndPay.whitelisting.required.for.chat = 1
p2p.whitelisting.required.for.chat = 1

p2p.rollOut.percent.for.chat = 100
rollOut.percent.for.update.via.uth.pipeline = 100
p2mAndAddAndPay.rollOut.percent.for.chat = 100

#keeping ttl as 2 days
userImageNullImageTtlInSec = 172800
userImageIgnoreNullImageInCacheForOauthCall = true


uth.filter.applied = true
cart.filter.applied = true
merged.filter.applied = true
prometheus.aspect.prefix = V3_
pipeline.name.suffix =
cache.key.prefix = V3_
cache.key.prefix.dc.pipelines = DC_

# 1 * 60 * 60 * 1000 (1 hour).
nonTransactingUserCache.refresh.time = 3600000

nonTransactingUser.cache.expiry.in.mins = 10
nonTransactingUserCacheService.enable = true
nonTransactingUserCache.rollOut.percentage = 100
nonTransactingUser.white.listed.users.list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********
is.nonTransactingUser.white.listed.users.list.enabled = 1

promo.white.listed.users.list = *********,*********,1750211,18738814,18286455,245178554,347748179,957604,1242662593,58627652,191733975,286665205,600182887,342090935,197176405,255971089,231017873,108980782,25335912,600283353,1185507635,1172512886,244889640,203130745,134590356,29783428,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,599605539,230084261,272527877,123368804,9661621,204402215,131639348,54517546,324512031,221106539,262721253,423215038,97964488,183417583,2556302,16211563,399697124,418569378,117523406,20511073,3770465,256599059,108768010,132686264,24971888,31143459,24396310,243152226,3131732,235647667,1261698840,301350106,587272468,175355059,17357855,186617689,337851179,18232972,20122075,31857089,221254683,7202709,152008896,1187000305,3583761,114432758,149649162,1064619405,126712218,195741439,5892884,311026020,290923433,1407103,3253901,12646069,10857212,223555563,1284033,1080853,2953086,25612800,6236193,217145117,4478554,231701817,1025968896,188952843,11395214,*********,*********,*********,2164859,330511075,308804658,285892061,********,7881770,29486281,55772598,125262162,11201492,*********,*********,*********,99392518,9178274,285908411,18011324,22237406,78349274,269241912,259788295,293383009,27044578,24534571,57437884,16386605,22116696,738496,1089807822,2844400,298028227,183503419,1132104848,243009112,22551101,1210439670,1112511404,196578757,110036284,50831904,255453517,298394047,7202709,********,437535,217145117,*********,29155048,10647321,186488641,114324348,5301036,221443355,44195192,22360277,866976,29747200,223198525
is.promo.white.listed.users.list.enabled = 1
rollOut.percent.for.promo = 1

listingApi.cache.expiry.in.days = 4
listingUserCache.enable = true
listingUserCache.rollOut.percentage = 100
is.listingUserCache.white.listed.users.list.enabled = 1
listingUserCache.white.listed.users.list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********

listingCache.delay.between.deleteAndSave.milliseconds = 2000

retry-threshold-for-chat-pipeline = 2

retryPipelinePushLimit = 5
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration

# no of grouping days buffer
# 30 min
dc.txn.grouping.fromDate = 1800000
# 30 min
dc.txn.grouping.toDate = 1800000
# 5 days
dc.refund.txn.grouping.fromDate = 432000000
# 7 days
dc.refund.txn.grouping.toDate = 604800000

domesticDc.rollOut.percentage = 100
domesticDc.whitelisting.required = 1
domesticDc.white.listed.users.list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********,*********,********,957604

#Spend Whitelisting Properties
spend.white.listed.users.list = 19568374,17948689,1059237905,153618874,310839874,957604,6257441,********,317040440,7842086,191733975,1222490353,1502241615,3199968,420071448,581715142,27210811,893354,29111688,230076407,594493697,191685349,2784010,349174251,6236193,486443710,6529780,29670233,243232292,287665477,219858099,24971888,254133495,518726118,419781662,134590356,9944379,512796856,68336144,47975662,1400206311,342260451,201278171,201791995,230041375,2564256,221254683,132499998,296230701,125262162,*********,*********,********,9268107,3770465,599605539,1490589582,*********,197999747,*********,267806444,1027880890,424941292,240546394,13914273,136465278,42129353,18286455,245178554,347748179,197176405,738496,188952843,24534571,1368664650,16386605,18011324,22237406,78349274,269241912,293383009,27044578,232113247,157021860,18365600,291199149,228146609,550288448,308967198,18480851,1284033,25335912,108768010,1251621668,25209101,147547192,144760378,1237163713,217145117,293275297,3072317,30809950,25188981,1574406,1476173203,5445595,1141567389,585426764,1147729658,315936344,1314078462,1404082587,1361857062,600435113,366922136,490717868,255212505,1407581691,22376586,22003214,*********,1830830837,1383841722,423492510,1479674579,5238511,495910024,55963648,1284347,311680506,217570033,1310394,167391292,21336322,1195317475,291466963,1175315007,330528549,1416564493,227241735,1194270239,30009242,1032714351,1243823346,145831564,294876831,249702262,582637070,1199277624,576599582,245219894,23966029,1185595049,1002577556,303949014,15577609,1350008752,1196189794,488556860,437236362,428213990,1204690964,1136602049,256978359,1184860434,1182369218,1380866871,1170061,1001891860,1297379853,30927120,112525454,1255927086,227776525,88018292,1156189866,679187971,1159929074,1191607769,1197285672,307098214,132990992,1175310601,27667028,1144292352,251450578,1392476388,558457242,138604100,311477762,475154816,1183367693,1174599949,681467605,27530283,1033175770,1185814119,187887579,588620372,1013934927,1153859583,531218630,560527798,1181017027,255979703,612737708,589549310,1275018336,302825736,397257394,517434924,1408189245,189952369,1005222697,1135762328,292903779,24062482,11553558,1179139496,1002467053,1184896575,142227588,433908936,1206861812,1259848719,22106833,605341957,1254938635,345264713,1387361839,350530205,342136775,91656778,1197498674,1269530121,452519806,1140913287,287753231,230782731,316007628,341030201,1215024435,1285163169,1018873698,1354666444,99001044,1607014,1262472564,1174243682,1228823223,209126223,1147121687,224081463,2610876,291742131,462749422,411348640,1009034969,250768708,313714252,306056680,1106536065,45430856,1169372683,1135772011,240892512,1093015375,300808479,548963724,382353050,16814568,564030982,311251718,1033346919,1185314433,347046839,25895865,1054407551,1185615072,346162591,233750937,1184948357,307707404,1206572542,419728806,1214000923,1166952443,505537108,667861349,1304303915,1446911837,304662370,1170286928,1198316914,1274144037,397622530,198430405,520560068,1034842834,555123240,658245107,305399594,329441893,423143734,1153256977,1309887733,2939882,358568536,494463384,1190641946,1106037848,30039003,1166878365,1290008463,1021030524,558108434,40832638,1180949181,1005879564,1183948274,186835455,1183797992,27597456,571293604,1197976720,1304219388,1006274057,385272432,1344008333,1139350955,305890430,554792984,1019546858,244415762,302229186,346274299,1202966225,307361604,456333496,1248214662,231213247,1002333248,1148472693,429871206,335125907,1304578397,1390985303,313223484,673898515,11834554,577223598,1197078765,1296147822,290183857,1371000588,239167006,190286793,1039843354,137439462,219418173,1089761692,323506452,616846934,1035883563,1179132671,1072564282,405776460,180261669,1133154836,292795577,5661338,579303802,1286624471,1167541560,12743422,316783016,1394391095,1016639270,270811376,586721810,307959776,1253167843,327147007,1075529972,155721436,286085187,1188832917,1256880113,288740665,1359281192,246438022,1350068727,1158149065,1202216532,414256040,69126040,566774514,1176632794,1027448990,1128241696,1346843280,18361038,587387770,1283706834,499629994,441714228,1386555435,1180467691,23065016,1176430,1327391190,1060453949,297104087,1149634777,431186084,1723342550,411708466,1188866234,1151560431,239141326,308534450,264668977,596614367,1163062452,1199202148,257583049,22359048,1416220085,1189962751,1136339225,228658379,576009532,1235196000,221468665,1332194140,356124986,1215712422,66620494,517603542,247800034,1042759530,1195673163,503784582,1189215989,441864438,1349670091,1240065286,1034415302,1002084750,1168252368,1285261341,1297433982,1329182044,226156177,1131474568,290771763,25127217,1284905,1255871571,1192540139,91136252,19811232,1017841652,1183925087,1261165563,346360923,185971907,1320582753,421342748,186062185,241580316,300112657,2265174,1011965106,1197369448,1089240642,316860800,1299914537,1188338825,1202981255,1381745935,1276504787,1190360844,10378535,1165490839,1198984845,546081874,319844922,184464229,649916631,1198515034,656670917,1142076438,1056234145,1314194027,330921057,300933593,571031642,1335869282,1171648359,56429924,1095383110,1103095479,1129541703,303148144,1359147978,172835707,646312387,307388562,572410514,1038135113,1178131130,1188327589,1155997326,146839722,1207970111,204015151,1257422428,652021967,6367918,1189305719,1094705799,1414346603,28977770,433373814,29881364,119605092,1146984798,135540388,1274129812,287441765,407116168,1165836907,343525735,1184053893,1240378584,619186868,1191104732,363370332,1065963490,1199967538,1357166668,643783945,1116195295,1129358651,544905860,286327715,1185128014,503383712,1196997754,1230593893,599217903,1262112224,1194653320,312805476,1303065998,1369588242,1209260286,1254766649,1200148202,1249655514,1395652427,404444998,1153761673,1376608160,1151399448,56811708,1150367573,1193501904,523664854,1226425253,1185762014,270903406,1418986124,261315853,672424343,139251098,120435812,1005233029,1021156335,293823621,615647740,1157590759,1304697389,532628764,1411810686,1319499218,1196210110,1109908811,243320654,1122394458,386724604,1293973195,1302630040,394051962,603317763,551619352,1417067710,1270979466,9419235,1068666251,552096950,1186527024,1255705271,650189267,295093861,1341905044,355255872,1042797739,590810886,1204826119,1218030999,231105007,1260434861,664759711,350562963,123611146,294165093,1185867282,1176593679,452407788,1349174361,650409965,251170092,1048590949,39997354,5191493,598780075,670735213,332485043,1167233134,674695143,565875894,603770603,650057389,1120989561,1397910062,1005224491,340683865,1146944848,1246271519,1102097889,448936230,1259883968,1135832679,561687796,457567492,589472010,197154429,252469628,1133227114,1066027513,1166126556,262798297,1385090923,1186225401,1420498312,1108365254,1275451727,18029336,186668233,506075544,1336821011,1108168740,653807215,187706967,1181732811,197884953,95962592,6014735,117089488,1395787207,2461554,22498179,19397336,251240380
is.white.listing.required.for.spend = 1
rollOut.percent.for.spend = 0

#10 min expiry time
spend.doc.set.cache.expiry.time = 600
spend.index.name.prefix = spend-history
spend-history-alias = spend_history_alias
spend.es.bulk.flush.action.value                    = 100
spend.es.bulk.flush.interval.value                  = 100

#2 Sec delay for month Doc creation
delay.for.month.agg.creation                        = 2000

#tag aggregation retry count threshold
retry.count.limit.for.tag.agg                        = 10


rollout.config-list[0].percentage = 100
rollout.config-list[0].whitelisting-required = 1
rollout.config-list[0].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,490717868,********,18323360,1407581691
rollout.config-list[0].whitelisting-for = imps

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = 1
rollout.config-list[1].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,18323360
rollout.config-list[1].whitelisting-for = uthNtuCache

rollout.config-list[2].percentage = 100
rollout.config-list[2].whitelisting-required = 1
rollout.config-list[2].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,18323360
rollout.config-list[2].whitelisting-for = upiPassbookListingCache

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = 1
rollout.config-list[3].user-list= *********,*********,*********,********,315936344,*********,1314078462,1404082587,1027880890,1361857062,600435113,366922136,490717868,255212505,1407581691,197999747,22003214,324830717,22376586
rollout.config-list[3].whitelisting-for = p2p_inward_failure_pending_events_filter_rollout

rollout.config-list[4].percentage = 100
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,18323360,315936344,1404082587,1027880890,1361857062,600435113,366922136,490717868,255212505,1407581691
rollout.config-list[4].whitelisting-for = stop1stEventEsCall

rollout.config-list[5].percentage = 100
rollout.config-list[5].whitelisting-required = 1
rollout.config-list[5].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,1000400001,1000783608,293383009,1025135302
rollout.config-list[5].whitelisting-for = chatDataApi

rollout.config-list[6].percentage = 1
rollout.config-list[6].whitelisting-required = 1
rollout.config-list[6].user-list = *********,*********,1750211,18738814,18286455,245178554,347748179,957604,1242662593,58627652,191733975,286665205,600182887,342090935,197176405,255971089,231017873,108980782,25335912,600283353,1185507635,1172512886,244889640,203130745,134590356,29783428,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,599605539,230084261,272527877,123368804,9661621,204402215,131639348,54517546,324512031,221106539,262721253,423215038,97964488,183417583,2556302,16211563,399697124,418569378,117523406,20511073,3770465,256599059,108768010,132686264,24971888,31143459,24396310,243152226,3131732,235647667,1261698840,301350106,587272468,175355059,17357855,186617689,337851179,18232972,20122075,31857089,221254683,7202709,152008896,1187000305,3583761,114432758,149649162,1064619405,126712218,195741439,5892884,311026020,290923433,1407103,3253901,12646069,10857212,223555563,1284033,1080853,2953086,25612800,6236193,217145117,4478554,231701817,1025968896,188952843,11395214,*********,*********,*********,2164859,330511075,308804658,285892061,********,7881770,29486281,55772598,125262162,11201492,*********,*********,*********,99392518,9178274,285908411,18011324,22237406,78349274,269241912,259788295,293383009,27044578,24534571,57437884,16386605,22116696,738496,1089807822,2844400,298028227,183503419,1132104848,243009112,22551101,1210439670,1112511404,196578757,110036284,50831904,255453517,298394047,7202709,********,437535,217145117,*********,29155048,10647321,186488641,114324348,5301036,221443355,44195192,22360277,866976,29747200,223198525
rollout.config-list[6].whitelisting-for = promoPipelineLogs

rollout.config-list[7].percentage = 0
rollout.config-list[7].whitelisting-required = 1
rollout.config-list[7].user-list = *********,*********,1750211,18738814,18286455,245178554,347748179,957604,1242662593,58627652,191733975,286665205,600182887,342090935,197176405,255971089,231017873,108980782,25335912,600283353,1185507635,1172512886,244889640,203130745,134590356,29783428,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,599605539,230084261,272527877,123368804,9661621,204402215,131639348,54517546,324512031,221106539,262721253,423215038,97964488,183417583,2556302,16211563,399697124,418569378,117523406,20511073,3770465,256599059,108768010,132686264,24971888,31143459,24396310,243152226,3131732,235647667,1261698840,301350106,587272468,175355059,17357855,186617689,337851179,18232972,20122075,31857089,221254683,7202709,152008896,1187000305,3583761,114432758,149649162,1064619405,126712218,195741439,5892884,311026020,290923433,1407103,3253901,12646069,10857212,223555563,1284033,1080853,2953086,25612800,6236193,217145117,4478554,231701817,1025968896,188952843,11395214,*********,*********,*********,2164859,330511075,308804658,285892061,********,7881770,29486281,55772598,125262162,11201492,*********,*********,*********,99392518,9178274,285908411,18011324,22237406,78349274,269241912,259788295,293383009,27044578,24534571,57437884,16386605,22116696,738496,1089807822,2844400,298028227,183503419,1132104848,243009112,22551101,1210439670,1112511404,196578757,110036284,50831904,255453517,298394047,7202709,********,437535,217145117,*********,29155048,10647321,186488641,114324348,5301036,221443355,44195192,22360277,866976,29747200,223198525
rollout.config-list[7].whitelisting-for = thdStreamFilterLog

rollout.config-list[8].percentage = 100
rollout.config-list[8].whitelisting-required = 1
rollout.config-list[8].user-list = 9178274,17789804,12231789,*********,17948689,1059237905,1830830837,310839874,315936344,*********,1361857062,548361564,17789804,125262162,********,*********
rollout.config-list[8].whitelisting-for = appCacheOptimisation

rollout.config-list[9].percentage = 0
rollout.config-list[9].whitelisting-required = -1
rollout.config-list[9].user-list = 9178274,17789804,12231789,*********,17948689,1059237905,1830830837,310839874,315936344,*********,1361857062,548361564,17789804,125262162,********,*********
rollout.config-list[9].whitelisting-for = omsIntegration

rollout.config-list[10].percentage = 0
rollout.config-list[10].whitelisting-required = -1
rollout.config-list[10].user-list = 9178274,17789804,12231789,*********,17948689,1059237905,1830830837,310839874,315936344,*********,1361857062,548361564,17789804,125262162,********,*********,1404082587,230076407,1391078339,243232292,1436626322,1071336715,492771588,1476732725,33138212,17490281,439337006,1728041,156378414,228750709,241548746,10647321,1448708351,230084261,485597634,1000847587
rollout.config-list[10].whitelisting-for = mandatePipeline

rollout.config-list[11].percentage = 0
rollout.config-list[11].whitelisting-required = -1
rollout.config-list[11].user-list = 9178274,17789804,12231789,*********,17948689,1059237905,1830830837,310839874,315936344,*********,1361857062,548361564,17789804,125262162,********,*********
rollout.config-list[11].whitelisting-for = omsRefundIntegration

rollout.config-list[12].percentage = 0
rollout.config-list[12].whitelisting-required = 1
rollout.config-list[12].user-list = *********,*********,*********,********,315936344,*********,1314078462,1404082587,1027880890,1361857062,600435113,366922136,490717868,255212505,1407581691,22376586,22003214,197999747,1059237905,1830830837
rollout.config-list[12].whitelisting-for = autoTaggingFeature

rollout.config-list[13].percentage = 100
rollout.config-list[13].whitelisting-required = 1
rollout.config-list[13].user-list = *********,125262162,*********,********,*********,1361857062,22338243,44040170,1027880890,12231789
rollout.config-list[13].whitelisting-for = droolsRemoval

rollout.config-list[14].percentage = 0
rollout.config-list[14].whitelisting-required = 1
rollout.config-list[14].user-list = *********,125262162,*********,********,*********,1361857062,22338243,44040170,1027880890,12231789
rollout.config-list[14].whitelisting-for = fwdTxnDetailsRollOut

isNtuCacheEnabled = true
isUpiPassbookCacheEnabled = false

txn.event.max.days.diff.from.current.to.process.txn = 60

#10 years
mandate-flow-event-days-limit = 3650

upi-udir-Index-alias = uth_cst_alias
upi-udir-index.name.prefix = uth-cst
upi.udir.es.bulk.flush.interval.value = 100
upi.udir.es.bulk.flush.action.value = 10

#For limiting the updates range on aws and dc.
ingestion.end.date.dc = 2023-11-01 00:00:00.000 +0530
ingestion.start.date.aws = 2023-08-01 00:00:00.000 +0530

partialEmiEventRetryPipelinePushLimit = 2

isKafkaPushForParallelWriteEnabled = true

lag.cache.update.interval = 1000
lag.cache.expiry.time     = 10

chatApi.baseUrl = https://chat-external-txn.paytmdgt.io
chatApi.uri = /pcchat/v1/chat/uth/notification
chatApi.connect.timeout = 1000
chatApi.socket.timeout = 1000
chatApi.connection.request.timeout = 1000
#chatApi.secret.key = ${UTH_CHAT_SECRET_KEY}
#chatApi.client.id =  ${UTH_CHAT_PUBLISH_API_CLIENT_ID}
chatApi.secret.key = UTH_CHAT_SECRET_KEY
chatApi.client.id =  UTH_CHAT_PUBLISH_API_CLIENT_ID

#Need to add details of API Properly
pgDataApi.baseUrl = https://api-beta.paytmbank.com
pgDataApi.uri = /payments-history-v2/ext/v1/fetchPgData
pgDataApi.connect.timeout = 1000
pgDataApi.socket.timeout = 1000
pgDataApi.connection.request.timeout = 1000
pgDataApi.secret.key = dtIzGLZyEfcQbYD
pgDataApi.client.id =  OCL_HOME
pgData.cut.off.date.in.epoch =  *************

#prod -> https://phs-internal.paytm.com (old)
#K8s -> http://payment-helper-service.combprod.svc.cluster.local
external.client.properties[BMSService].baseUrl: https://phs-internal.paytm.com
external.client.properties[BMSService].secretKey: ${PTH_BENEFICIARY_SECRET}
external.client.properties[BMSService].socketTimeout: 1000
external.client.properties[BMSService].connectionTimeout: 1000

#job is not starting when set as false on prod. FIXME
bootWithoutConfigs.boPanelConfigs = true

recap.baseUrl = https://uth-v2-internal.orgk.com
recap.url = /uth-v2/ext/v1/spend-analytics/rewindData

online.deals.verticalId  = 66
gv.verticalId            = 66
offline.deals.verticalId = 174
online.deals.logoUrl     = https://assetscdn1.paytm.com/images/catalog/view_item/2283581/1699599295957.png
gv.logoUrl               = https://consumergv.paytm.com/gv-custom-files/images/gift-cards.png
offline.deals.logoUrl    = https://assetscdn1.paytm.com/images/catalog/view_item/1799274/1699276411188.png

recentTxns.cache.expiry.time.seconds = 300
recentTxns.cacheUpdater.enabled      = false

check.pointing.enable                              = true
paytm.tpap.handles.list = paytm,pthdfc,ptyes,ptaxis,ptsbi

min.diff.to.process.relay.events = 0

mandate.cache.expiry.time = 60
mandate.es.bulk.flush.action.value = 100
mandate.es.bulk.flush.interval.value = 100

autoTagging.details.aerospike.set.name = auto_tagging_details_set
autoTagging.aerospike.namespace                = pth_persistent
autoTagging.aerospike.host-name                = uth-ai-aerospike1.uth-prod.paytm.local,uth-ai-aerospike2.uth-prod.paytm.local,uth-ai-aerospike3.uth-prod.paytm.local,uth-ai-aerospike4.uth-prod.paytm.local
autoTagging.aerospike.port                     = 3000

max.days.diff.for.fwd.refund.txns.linking = 45