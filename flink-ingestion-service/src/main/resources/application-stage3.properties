#Kafka and ES properties
source.kafka.topic                                = transformed-data-topic
bootstrap.servers                                 = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
group.id                                          = upi-consumer-group
#zookeeper.connect    = localhost:2181
#
#name                 = upi-stream-consumer
#instance_count       = 1
#poll_time            = 100
#consumer_class_name  = org.apache.kafka.clients.consumer.KafkaConsumer
#message_class        = com.paytm.bank.objects.request.KafkaConsumerEntity
#enable.auto.commit   = false
#value.deserializer   = org.apache.kafka.common.serialization.StringDeserializer
#key.deserializer     = org.apache.kafka.common.serialization.StringDeserializer
#max.poll.interval.ms = 3000000
#max.poll.records     = 20

es-host-list                                         = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es-port                                              = 9200

es-v2-host-list                                         = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es-v2-port                                              = 9200

es-dc-host-list                                      = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es-dc-port                                           = 9200

mandate-es-host-list                                 = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local

es-socket-timeout = 5000
es-max-retry-timeout = 5000
es-connect-timeout = 5000
elastic-search-index = stage3_payment_history_alias
es-connect-request-timeout            = 5000

es.read.sourceMap.dc-main-pipeline.hostList = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.read.sourceMap.dc-main-pipeline.indexNamePrefix = mwite1-payment-history

es.read.sourceMap.dc-uth-enricher-pipeline.hostList = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.read.sourceMap.dc-uth-enricher-pipeline.indexNamePrefix = mwite1-payment-history

es.read.sourceMap.es-insert-update-executor-pipeline.hostList = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es.read.sourceMap.es-insert-update-executor-pipeline.indexNamePrefix = stage3-payment-history

es.read.sourceMap.data-audit-pipeline.hostList = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es.read.sourceMap.data-audit-pipeline.indexNamePrefix = stage3-payment-history

es.read.sourceMap.mandate.hostList = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es.read.sourceMap.mandate.indexNamePrefix = stage3-mandate

es.read.sourceMap.mandate-retry.hostList = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es.read.sourceMap.mandate-retry.indexNamePrefix = stage3-mandate

es.target[0].indexName                            = stage3-payment-history
es.target[0].port                                 = 9200
es.target[0].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[0].pipeline-name                        = upi
es.target[0].requestType                          = insert

es.target[18].indexName                            = stage3-payment-history
es.target[18].port                                 = 9200
es.target[18].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[18].pipeline-name                        = upiV2
es.target[18].requestType                          = insert

es.target[1].indexName                            = stage3-payment-history
es.target[1].port                                 = 9200
es.target[1].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[1].pipeline-name                        = pg
es.target[1].requestType                          = insert

es.target[2].indexName                            = stage3-payment-history
es.target[2].port                                 = 9200
es.target[2].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[2].pipeline-name                        = wallet
es.target[2].requestType                          = insert

es.target[3].indexName                            = stage3-payment-history
es.target[3].port                                 = 9200
es.target[3].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[3].pipeline-name                        = retry-pipeline
es.target[3].requestType                          = insert

es.target[4].indexName                            = stage3-payment-history
es.target[4].port                                 = 9200
es.target[4].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[4].pipeline-name                        = cbs
es.target[4].requestType                          = insert

es.target[5].indexName                            = stage3-payment-history
es.target[5].port                                 = 9200
es.target[5].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[5].pipeline-name                        = ts
es.target[5].requestType                          = insert

es.target[6].indexName                            = stage3-payment-history
es.target[6].port                                 = 9200
es.target[6].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[6].pipeline-name                        = pg-retry-v2
es.target[6].requestType                          = insert

es.target[7].indexName                            = stage3-payment-history
es.target[7].port                                 = 9200
es.target[7].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[7].pipeline-name                        = userImageUrl
es.target[7].requestType                          = update

es.target[8].indexName                            = stage3-payment-history
es.target[8].port                                 = 9200
es.target[8].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[8].pipeline-name                        = uth
es.target[8].requestType                          = update

es.target[9].indexName                            = stage3-payment-history
es.target[9].port                                 = 9200
es.target[9].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[9].pipeline-name                        = retry-back-filling
es.target[9].requestType                          = insert

es.target[10].indexName                            = stage3-payment-history
es.target[10].port                                 = 9200
es.target[10].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[10].pipeline-name                        = tags-enricher
es.target[10].requestType                          = update

es.target[11].indexName                            = stage3-spend-history
es.target[11].port                                 = 9200
es.target[11].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[11].pipeline-name                        = user-spend-docs-creator
es.target[11].requestType                          = insert
es.target[11].switch-adaptors-name                 = SpendEsAndCacheAdaptor

es.target[12].indexName                            = stage3-uth-cst
es.target[12].port                                 = 9200
es.target[12].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[12].pipeline-name                        = udir
es.target[12].requestType                          = generic-insert

es.target[13].indexName                            = stage3-payment-history
es.target[13].port                                 = 9200
es.target[13].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[13].pipeline-name                        = dc-main-pipeline
es.target[13].requestType                          = insert

es.target[14].indexName                            = stage3-payment-history
es.target[14].port                                 = 9200
es.target[14].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[14].pipeline-name                        = dc-uth-enricher-pipeline
es.target[14].requestType                          = update

es.target[15].indexName                            = stage3-payment-history
es.target[15].port                                 = 9200
es.target[15].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[15].pipeline-name                        = ppbl-pg
es.target[15].requestType                          = insert

es.target[16].indexName                            = stage3-payment-history
es.target[16].port                                 = 9200
es.target[16].hosts                                = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es.target[16].pipeline-name                        = es-insert-update-executor-pipeline
es.target[16].requestType                          = insert
es.target[16].switch-adaptors-name                 = insertExecutor

es.target[17].indexName                            = stage3-payment-history
es.target[17].port                                 = 9200
es.target[17].hosts                                = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es.target[17].pipeline-name                        = es-insert-update-executor-pipeline
es.target[17].requestType                          = update
es.target[17].switch-adaptors-name                 = updateExecutor

es.target[19].indexName                            = stage3-payment-history
es.target[19].port                                 = 9200
es.target[19].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[19].pipeline-name                        = oms
es.target[19].requestType                          = insert

es.target[20].indexName                            = stage3-payment-history
es.target[20].port                                 = 9200
es.target[20].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[20].pipeline-name                        = upi-relay-pipeline
es.target[20].requestType                          = insert

es.target[21].indexName                            = stage3-mandate
es.target[21].port                                 = 9200
es.target[21].hosts                                = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es.target[21].pipeline-name                        = mandate
es.target[21].requestType                          = insert

es.target[22].indexName                            = stage3-mandate
es.target[22].port                                 = 9200
es.target[22].hosts                                = es-app-staging-1-ypt.uth.paytm.local,es-app-staging-2-ypt.uth.paytm.local,es-app-staging-3-ypt.uth.paytm.local
es.target[22].pipeline-name                        = mandate-retry
es.target[22].requestType                          = insert

es.target[23].indexName                            = stage3-payment-history
es.target[23].port                                 = 9200
es.target[23].hosts                                = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es.target[23].pipeline-name                        = toggle-visibility
es.target[23].requestType                          = insert

#Kakfa Source config
kafka.source-list[0].topic                        = middleware_transaction_history_upi_data_stage3
kafka.source-list[0].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[0].consumer-group-name          = upi-consumer-group
kafka.source-list[0].pipeline-name                = upi
kafka.source-list[0].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[0].max-poll-records             = 50

#Kakfa Source config
kafka.source-list[39].topic                        = middleware_transaction_history_upi_data_v2_stage3
kafka.source-list[39].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[39].consumer-group-name          = upi-consumer-group-v2
kafka.source-list[39].pipeline-name                = upiV2
kafka.source-list[39].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[39].max-poll-records             = 50

kafka.source-list[42].topic                        = middleware_transaction_history_pg_data
kafka.source-list[42].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[42].consumer-group-name          = pg-data-publish-consumer-group
kafka.source-list[42].pipeline-name                = pg
kafka.source-list[42].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[42].max-poll-records             = 50

#Kakfa Source config
kafka.source-list[1].topic                        = middleware_transaction_history_pg_data_stage3
kafka.source-list[1].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[1].consumer-group-name          = pg-consumer-group
kafka.source-list[1].pipeline-name                = pg-data-publish-api-pipeline
kafka.source-list[1].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[1].max-poll-records             = 50

kafka.source-list[2].topic                        = middleware_transaction_history_wallet_data_stage3
kafka.source-list[2].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[2].consumer-group-name          = wallet-consumer-group
kafka.source-list[2].pipeline-name                = wallet
kafka.source-list[2].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[2].max-poll-records             = 50

kafka.source-list[3].topic                        = middleware_transaction_history_retry_data_stage3
kafka.source-list[3].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[3].consumer-group-name          = retry-consumer-group
kafka.source-list[3].pipeline-name                = retry-pipeline
kafka.source-list[3].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[3].max-poll-records             = 50

kafka.source-list[4].topic                        = cbs-gg-bo-stiched-transaction-v2_stage3
kafka.source-list[4].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[4].consumer-group-name          = gg-transaction-consumer-group
kafka.source-list[4].pipeline-name                = cbs
kafka.source-list[4].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[4].max-poll-records             = 50

kafka.source-list[5].topic                        = cbs-gg-bo-stiched-transaction-v2_stage3
kafka.source-list[5].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[5].consumer-group-name          = gg-transaction-consumer-group
kafka.source-list[5].pipeline-name                = cbs-converter
kafka.source-list[5].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[5].max-poll-records             = 50

kafka.source-list[6].topic                        = middleware_transaction_history_uth_localisation_data_stage3
kafka.source-list[6].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[6].consumer-group-name          = marketplace-consumer-group
kafka.source-list[6].pipeline-name                = marketplace
kafka.source-list[6].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[6].max-poll-records             = 50

kafka.source-list[7].topic                        = middleware_transaction_history_ts_data_stage3
kafka.source-list[7].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[7].consumer-group-name          = ts-transaction-consumer-group-eksite
kafka.source-list[7].pipeline-name                = ts
kafka.source-list[7].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[7].max-poll-records             = 50

kafka.source-list[8].topic                        = middleware_transaction_history_merged_data_stage3
kafka.source-list[8].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[8].consumer-group-name          = chat-consumer-group-ite
kafka.source-list[8].pipeline-name                = chat
kafka.source-list[8].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[8].max-poll-records             = 50

kafka.source-list[9].topic                        = middleware_transaction_history_cart_data_stage3
kafka.source-list[9].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[9].consumer-group-name          = cart-consumer-group
kafka.source-list[9].pipeline-name                = cart
kafka.source-list[9].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[9].max-poll-records             = 50

kafka.source-list[10].topic                        = middleware_transaction_history_pg_retry_v2_data_stage3
kafka.source-list[10].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[10].consumer-group-name          = pg-retry-v2-consumer-group
kafka.source-list[10].pipeline-name                = pg-retry-v2
kafka.source-list[10].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[10].max-poll-records             = 50

kafka.source-list[11].topic                        = middleware_transaction_history_chat_back_fill_data_stage3
kafka.source-list[11].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[11].consumer-group-name          = chat-back-filling-consumer-group-te
kafka.source-list[11].pipeline-name                = chat-back-filling
kafka.source-list[11].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[11].max-poll-records             = 50

kafka.source-list[12].topic                        = middleware_transaction_history_retry_v2_data_stage3
kafka.source-list[12].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[12].consumer-group-name          = retry-consumer-group-v2-ite
kafka.source-list[12].pipeline-name                = retry-pipeline-v2
kafka.source-list[12].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[12].max-poll-records             = 50


kafka.source-list[13].topic                        = middleware_transaction_history_recon_config_data_stage3
kafka.source-list[13].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[13].consumer-group-name          = recon-consumer-group-ite
kafka.source-list[13].pipeline-name                = recon
kafka.source-list[13].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[13].max-poll-records             = 50

kafka.source-list[14].topic                        = middleware_transaction_history_pending_txn_data_stage3
kafka.source-list[14].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[14].consumer-group-name          = status-resolver-group-ite
kafka.source-list[14].pipeline-name                = status-resolver
kafka.source-list[14].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[14].max-poll-records             = 50

kafka.source-list[15].topic                        = user_image_url_data_stage3
kafka.source-list[15].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[15].consumer-group-name          = user-image-url-group
kafka.source-list[15].pipeline-name                = userImageUrl
kafka.source-list[15].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[15].max-poll-records             = 50

kafka.source-list[16].topic                        = middleware_transaction_history_uth_data_stage3
kafka.source-list[16].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[16].consumer-group-name          = uth-consumer-group
kafka.source-list[16].pipeline-name                = uth
kafka.source-list[16].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[16].max-poll-records             = 100

kafka.source-list[17].topic                        = middleware_transaction_history_back_fill_data_stage3
kafka.source-list[17].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[17].consumer-group-name          = upi-back-filling-consumer-group-ite
kafka.source-list[17].pipeline-name                = upi-back-filling
kafka.source-list[17].max-poll-records             = 50

kafka.source-list[18].topic                        = middleware_transaction_history_retry_back_fill_data_stage3
kafka.source-list[18].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[18].consumer-group-name          = retry-back-fill-consumer-group-ite
kafka.source-list[18].pipeline-name                = retry-back-filling
kafka.source-list[18].max-poll-records             = 50

kafka.source-list[19].topic                        = cbs-gg-bo-stiched-transaction-v2_stage3
kafka.source-list[19].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[19].consumer-group-name          = cbs-stream-consumer-group
kafka.source-list[19].pipeline-name                = cbs-stream
kafka.source-list[19].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[19].max-poll-records             = 300
#Retry Kafka Properties for this Source
kafka.source-list[19].retry-target.retry-topic                  = middleware_transaction_history_cbs_gg_stitch_retry_data_stage3
kafka.source-list[19].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[19].retry-target.batch-size-bytes             = 3000
kafka.source-list[19].retry-target.linger-ms                    = 15
kafka.source-list[19].retry-target.request-timeout-ms           = 5000
kafka.source-list[19].retry-target.producers-pool-size          = 5
kafka.source-list[19].retry-target.transaction-timeout          = 60000
kafka.source-list[19].serialization-type                        = String

kafka.source-list[20].topic                        = middleware_transaction_history_promo_internal_data_stage3
kafka.source-list[20].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[20].consumer-group-name          = promo-internal-consumer-group
kafka.source-list[20].pipeline-name                = promo
kafka.source-list[20].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[20].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[20].retry-target.retry-topic                  = middleware_transaction_history_promo_internal_data_stage3
kafka.source-list[20].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[20].retry-target.batch-size-bytes             = 3000
kafka.source-list[20].retry-target.linger-ms                    = 15
kafka.source-list[20].retry-target.request-timeout-ms           = 5000
kafka.source-list[20].retry-target.producers-pool-size          = 5
kafka.source-list[20].retry-target.transaction-timeout          = 60000
kafka.source-list[20].serialization-type                        = String

kafka.source-list[21].topic                        = middleware_transaction_history_cbs_gg_stitch_retry_data_stage3
kafka.source-list[21].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[21].consumer-group-name          = gg-stitch-retry-consumer-group
kafka.source-list[21].pipeline-name                = cbs-retry-pipeline
kafka.source-list[21].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[21].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[21].retry-target.retry-topic                  = middleware_transaction_history_cbs_gg_stitch_retry_data_stage3
kafka.source-list[21].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[21].retry-target.batch-size-bytes             = 3000
kafka.source-list[21].retry-target.linger-ms                    = 15
kafka.source-list[21].retry-target.request-timeout-ms           = 5000
kafka.source-list[21].retry-target.producers-pool-size          = 5
kafka.source-list[21].retry-target.transaction-timeout          = 60000
kafka.source-list[21].serialization-type                        = String

kafka.source-list[22].topic                        = uth_cache_updater_data_stage3
kafka.source-list[22].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[22].consumer-group-name          = uth-ntud-consumer-group
kafka.source-list[22].pipeline-name                = cache-updater-pipeline
kafka.source-list[22].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[22].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[22].retry-target.retry-topic                  = uth_cache_updater_data_stage3
kafka.source-list[22].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[22].retry-target.batch-size-bytes             = 3000
kafka.source-list[22].retry-target.linger-ms                    = 15
kafka.source-list[22].retry-target.request-timeout-ms           = 5000
kafka.source-list[22].retry-target.producers-pool-size          = 5
kafka.source-list[22].retry-target.transaction-timeout          = 60000
kafka.source-list[22].serialization-type                        = String

kafka.source-list[23].topic                        = cbs_multi_tx_stage3
kafka.source-list[23].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[23].consumer-group-name          = uth_van_consumer
kafka.source-list[23].pipeline-name                = van-pipeline
kafka.source-list[23].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[23].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[23].retry-target.retry-topic                  = cbs_multi_tx_stage3
kafka.source-list[23].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[23].retry-target.batch-size-bytes             = 3000
kafka.source-list[23].retry-target.linger-ms                    = 15
kafka.source-list[23].retry-target.request-timeout-ms           = 5000
kafka.source-list[23].retry-target.producers-pool-size          = 5
kafka.source-list[23].retry-target.transaction-timeout          = 60000
kafka.source-list[23].serialization-type                        = String

kafka.source-list[24].topic                        = cbs-gg-transaction_stage3
kafka.source-list[24].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[24].consumer-group-name          = cbs-gg-upi-recon-consumer
kafka.source-list[24].pipeline-name                = upi-recon-pipeline
kafka.source-list[24].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[24].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[24].retry-target.retry-topic                  = cbs-gg-transaction_stage3
kafka.source-list[24].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[24].retry-target.batch-size-bytes             = 3000
kafka.source-list[24].retry-target.linger-ms                    = 15
kafka.source-list[24].retry-target.request-timeout-ms           = 5000
kafka.source-list[24].retry-target.producers-pool-size          = 5
kafka.source-list[24].retry-target.transaction-timeout          = 60000
kafka.source-list[24].serialization-type                        = String


kafka.source-list[25].topic                        = uth_spend_user_kafka_config_data_stage3
kafka.source-list[25].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[25].consumer-group-name          = spend_user_kafka_config_consumer_group
kafka.source-list[25].pipeline-name                = user-spend-docs-creator
kafka.source-list[25].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[25].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[25].retry-target.retry-topic                  = uth_spend_user_kafka_config_data_stage3
kafka.source-list[25].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[25].retry-target.batch-size-bytes             = 3000
kafka.source-list[25].retry-target.linger-ms                    = 15
kafka.source-list[25].retry-target.request-timeout-ms           = 5000
kafka.source-list[25].retry-target.producers-pool-size          = 5
kafka.source-list[25].retry-target.transaction-timeout          = 60000
kafka.source-list[25].serialization-type                        = String

kafka.source-list[26].topic                        = uth_spend_user_month_agg_kafka_config_data_stage3
kafka.source-list[26].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[26].consumer-group-name          = user-analytics-month-agg_consumer_group
kafka.source-list[26].pipeline-name                = user-analytics-month-agg-creator
kafka.source-list[26].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[26].max-poll-records             = 50
#Retry Kafka Properties for this Source
kafka.source-list[26].retry-target.retry-topic                  = uth_spend_user_month_agg_kafka_config_data_stage3
kafka.source-list[26].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[26].retry-target.batch-size-bytes             = 3000
kafka.source-list[26].retry-target.linger-ms                    = 15
kafka.source-list[26].retry-target.request-timeout-ms           = 5000
kafka.source-list[26].retry-target.producers-pool-size          = 5
kafka.source-list[26].retry-target.transaction-timeout          = 60000
kafka.source-list[26].serialization-type                        = String

kafka.source-list[27].topic                        = middleware_transaction_history_userid_fetcher_data_stage3
kafka.source-list[27].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[27].consumer-group-name          = uth-userid_fetcher-consumer-group
kafka.source-list[27].pipeline-name                = userId-fetcher-pipeline
kafka.source-list[27].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[27].max-poll-records             = 50
kafka.source-list[27].retry-target.retry-topic                  = middleware_transaction_history_userid_fetcher_data_stage3
kafka.source-list[27].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[27].retry-target.batch-size-bytes             = 3000
kafka.source-list[27].retry-target.linger-ms                    = 15
kafka.source-list[27].retry-target.request-timeout-ms           = 5000
kafka.source-list[27].retry-target.producers-pool-size          = 5
kafka.source-list[27].retry-target.transaction-timeout          = 60000
kafka.source-list[27].serialization-type                        = String

kafka.source-list[28].topic                        = uth_tag_source_config_data_stage3
kafka.source-list[28].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[28].consumer-group-name          = tag-agg-retry-consumer-group
kafka.source-list[28].pipeline-name                = tag-agg-retry-pipeline
kafka.source-list[28].max-poll-records             = 50

kafka.source-list[29].topic                        = middleware_transaction_history_merged_data_stage3
kafka.source-list[29].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[29].consumer-group-name          = uth-promo-upi-consumer-group-eksite
kafka.source-list[29].pipeline-name                = promo-upi-pipeline
kafka.source-list[29].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[29].max-poll-records             = 50
kafka.source-list[29].retry-target.retry-topic                  = middleware_transaction_history_merged_data_stage3
kafka.source-list[29].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[29].retry-target.batch-size-bytes             = 3000
kafka.source-list[29].retry-target.linger-ms                    = 15
kafka.source-list[29].retry-target.request-timeout-ms           = 5000
kafka.source-list[29].retry-target.producers-pool-size          = 5
kafka.source-list[29].retry-target.transaction-timeout          = 60000

kafka.source-list[30].topic                        = middleware_transaction_history_generic_retry_data_stage3
kafka.source-list[30].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[30].consumer-group-name          = uth-generic-retry-consumer-group
kafka.source-list[30].pipeline-name                = generic-retry-pipeline
kafka.source-list[30].poll-interval                = 300000
kafka.source-list[30].session-timeout              = 300000
kafka.source-list[30].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[30].max-poll-records             = 1
kafka.source-list[30].retry-target.retry-topic                  = middleware_transaction_history_generic_retry_data_stage3
kafka.source-list[30].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[30].retry-target.batch-size-bytes             = 3000
kafka.source-list[30].retry-target.linger-ms                    = 15
kafka.source-list[30].retry-target.request-timeout-ms           = 5000
kafka.source-list[30].retry-target.producers-pool-size          = 5
kafka.source-list[30].retry-target.transaction-timeout          = 60000
kafka.source-list[30].serialization-type                        = String



kafka.source-list[31].topic                        = uth_upi_udir_cst_complaint_data_stage3
kafka.source-list[31].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[31].consumer-group-name          = uth-udir-consumer-group
kafka.source-list[31].pipeline-name                = udir
kafka.source-list[31].max-poll-records             = 50
kafka.source-list[31].retry-target.retry-topic                  = uth_upi_udir_cst_complaint_data_stage3
kafka.source-list[31].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[31].retry-target.batch-size-bytes             = 3000
kafka.source-list[31].retry-target.linger-ms                    = 15
kafka.source-list[31].retry-target.request-timeout-ms           = 5000
kafka.source-list[31].retry-target.producers-pool-size          = 5
kafka.source-list[31].retry-target.transaction-timeout          = 60000
kafka.source-list[31].serialization-type                        = String

kafka.source-list[32].topic                        = uth_all_sources_dc_data_stage3
kafka.source-list[32].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[32].consumer-group-name          = uth-dc-main-consumer
kafka.source-list[32].pipeline-name                = dc-main-pipeline
kafka.source-list[32].max-poll-records             = 50


kafka.source-list[33].topic                        = uth_enricher_dc_data_stage3
kafka.source-list[33].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[33].consumer-group-name          = uth-enricher-dc-consumer
kafka.source-list[33].pipeline-name                = dc-uth-enricher-pipeline
kafka.source-list[33].max-poll-records             = 50

kafka.source-list[34].topic                        = middleware_transaction_history_ppbl_pg_data_stage3
kafka.source-list[34].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[34].consumer-group-name          = ppbl-pg-consumer-group
kafka.source-list[34].pipeline-name                = ppbl-pg
kafka.source-list[34].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[34].max-poll-records             = 50

kafka.source-list[35].topic                        = pth_es_insert_update_data_stage3
kafka.source-list[35].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[35].consumer-group-name          = uth-es-insert-update-consumer
kafka.source-list[35].pipeline-name                = es-insert-update-executor-pipeline
kafka.source-list[35].max-poll-records             = 50

kafka.source-list[36].topic                        = pth_audit_data_stage3
kafka.source-list[36].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[36].consumer-group-name          = uth-audit-consumer
kafka.source-list[36].pipeline-name                = data-audit-pipeline
kafka.source-list[36].max-poll-records             = 50

kafka.source-list[37].topic                        = middleware_transaction_history_chat_data_stage3
kafka.source-list[37].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[37].consumer-group-name          = chat-data-publish-api-consumer
kafka.source-list[37].pipeline-name                = chat-data-publish-api-pipeline
kafka.source-list[37].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[37].max-poll-records             = 50

kafka.source-list[38].topic                        = middleware_transaction_history_back_fill_data_stage3
kafka.source-list[38].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[38].consumer-group-name          = spend-back-filling-consumer-group
kafka.source-list[38].pipeline-name                = api-response-cache-population-pipeline
kafka.source-list[38].max-poll-records             = 50
kafka.source-list[38].retry-target.retry-topic                  = middleware_transaction_history_back_fill_data_stage3
kafka.source-list[38].retry-target.bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[38].retry-target.batch-size-bytes             = 3000
kafka.source-list[38].retry-target.linger-ms                    = 15
kafka.source-list[38].retry-target.request-timeout-ms           = 5000
kafka.source-list[38].retry-target.producers-pool-size          = 5
kafka.source-list[38].retry-target.transaction-timeout          = 60000
kafka.source-list[38].serialization-type                        = String

#kafka.source-list[40].topic                        = order_update
#kafka.source-list[40].bootstrap-servers            = **********:9092,**********:9092,***********:9092
#kafka.source-list[40].consumer-group-name          = oms-consumer
#kafka.source-list[40].pipeline-name                = oms
#kafka.source-list[40].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
#kafka.source-list[40].max-poll-records             = 1000

kafka.source-list[40].topic                        = order_update
kafka.source-list[40].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[40].consumer-group-name          = oms-consumer-v2
kafka.source-list[40].pipeline-name                = oms
kafka.source-list[40].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[40].max-poll-records             = 1000

kafka.source-list[41].topic                        = pth_upi_relay_data_stage3
kafka.source-list[41].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[41].consumer-group-name          = upi-relay-consumer-group
kafka.source-list[41].pipeline-name                = upi-relay-pipeline
kafka.source-list[41].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[41].max-poll-records             = 50

kafka.source-list[43].topic                        = middleware_transaction_history_merged_data_stage3
kafka.source-list[43].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[43].consumer-group-name          = recent-txn-consumer-group
kafka.source-list[43].pipeline-name                = recent-txn
kafka.source-list[43].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[43].max-poll-records             = 100


kafka.source-list[44].topic                        = pth_mandate_data_stage3
kafka.source-list[44].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[44].consumer-group-name          = mandate-consumer-group
kafka.source-list[44].pipeline-name                = mandate
kafka.source-list[44].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[44].max-poll-records             = 50

kafka.source-list[45].topic                        = pth_mandate_retry_data_stage3
kafka.source-list[45].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[45].consumer-group-name          = mandate-retry-consumer-group
kafka.source-list[45].pipeline-name                = mandate-retry
kafka.source-list[45].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.source-list[45].max-poll-records             = 50

kafka.source-list[46].topic                        = pth_toggle_visibility_data_stage3
kafka.source-list[46].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[46].consumer-group-name          = toggle-visibility-consumer-group
kafka.source-list[46].pipeline-name                = toggle-visibility
kafka.source-list[46].confluent-kafka-registry-url = http://stage-kafka1.uth.paytm.local:8081/
kafka.source-list[46].max-poll-records             = 50

kafka.source-list[47].topic                        = pth_txn_tags_data_stage3
kafka.source-list[47].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.source-list[47].consumer-group-name          = tag-enricher-consumer-group
kafka.source-list[47].pipeline-name                = tags-enricher
kafka.source-list[47].confluent-kafka-registry-url = http://stage-kafka1.uth.paytm.local:8081/
kafka.source-list[47].max-poll-records             = 100

flink.tags-enricher.parallelism.value = 1

#Kafka Target Config
kafka.target-list[0].topic                        = middleware_transaction_history_ppbl_data_stage3
kafka.target-list[0].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[0].pipeline-name                = cbs-converter
kafka.target-list[0].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[0].batch-size-bytes             = 3000
kafka.target-list[0].linger-ms                    = 15
kafka.target-list[0].request-timeout-ms           = 5000
kafka.target-list[0].producers-pool-size           = 5
kafka.target-list[0].transaction-timeout           = 60000

kafka.target-list[1].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[1].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[1].pipeline-name                = upi
kafka.target-list[1].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[1].batch-size-bytes             = 3000
kafka.target-list[1].linger-ms                    = 15
kafka.target-list[1].request-timeout-ms           = 5000
kafka.target-list[1].producers-pool-size          = 5
kafka.target-list[1].transaction-timeout          = 60000

kafka.target-list[35].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[35].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[35].pipeline-name                = upiV2
kafka.target-list[35].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[35].batch-size-bytes             = 3000
kafka.target-list[35].linger-ms                    = 15
kafka.target-list[35].request-timeout-ms           = 5000
kafka.target-list[35].producers-pool-size          = 5
kafka.target-list[35].transaction-timeout          = 60000

kafka.target-list[36].topic                        = middleware_transaction_history_pg_data
kafka.target-list[36].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[36].pipeline-name                = pg-data-publish-api-pipeline
kafka.target-list[36].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[36].batch-size-bytes             = 3000
kafka.target-list[36].linger-ms                    = 15
kafka.target-list[36].request-timeout-ms           = 5000
kafka.target-list[36].producers-pool-size          = 5
kafka.target-list[36].transaction-timeout          = 60000
kafka.target-list[36].switch-adaptors-name         = PgDataPublishAdaptor

kafka.target-list[2].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[2].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[2].pipeline-name                = wallet
kafka.target-list[2].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[2].batch-size-bytes             = 3000
kafka.target-list[2].linger-ms                    = 15
kafka.target-list[2].request-timeout-ms           = 5000
kafka.target-list[2].producers-pool-size          = 5
kafka.target-list[2].transaction-timeout          = 60000

kafka.target-list[3].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[3].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[3].pipeline-name                = cbs
kafka.target-list[3].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[3].batch-size-bytes             = 3000
kafka.target-list[3].linger-ms                    = 15
kafka.target-list[3].request-timeout-ms           = 5000
kafka.target-list[3].producers-pool-size          = 5
kafka.target-list[3].transaction-timeout          = 60000

kafka.target-list[4].topic                        = middleware_transaction_history_chat_data_stage3
kafka.target-list[4].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[4].pipeline-name                = chat
kafka.target-list[4].batch-size-bytes             = 3000
kafka.target-list[4].linger-ms                    = 15
kafka.target-list[4].request-timeout-ms           = 5000
kafka.target-list[4].producers-pool-size          = 5
kafka.target-list[4].transaction-timeout          = 60000

kafka.target-list[5].topic                        = middleware_transaction_history_pg_retry_v2_data_stage3
kafka.target-list[5].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[5].pipeline-name                = cart
kafka.target-list[5].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[5].batch-size-bytes             = 3000
kafka.target-list[5].linger-ms                    = 15
kafka.target-list[5].request-timeout-ms           = 5000
kafka.target-list[5].producers-pool-size           = 5
kafka.target-list[5].transaction-timeout           = 60000

kafka.target-list[6].topic                        = middleware_transaction_history_cart_data_stage3
kafka.target-list[6].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[6].pipeline-name                = sink2cart
kafka.target-list[6].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[6].batch-size-bytes             = 3000
kafka.target-list[6].linger-ms                    = 15
kafka.target-list[6].request-timeout-ms           = 5000
kafka.target-list[6].producers-pool-size          = 5
kafka.target-list[6].transaction-timeout          = 60000

kafka.target-list[7].topic                        = middleware_transaction_history_retry_v2_data_stage3
kafka.target-list[7].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[7].pipeline-name                = chat-back-filling
kafka.target-list[7].batch-size-bytes             = 3000
kafka.target-list[7].linger-ms                    = 15
kafka.target-list[7].request-timeout-ms           = 5000
kafka.target-list[7].producers-pool-size          = 5
kafka.target-list[7].transaction-timeout          = 60000

kafka.target-list[8].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[8].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[8].pipeline-name                = retry-pipeline-v2
kafka.target-list[8].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[8].batch-size-bytes             = 3000
kafka.target-list[8].linger-ms                    = 15
kafka.target-list[8].request-timeout-ms           = 5000
kafka.target-list[8].producers-pool-size          = 5
kafka.target-list[8].transaction-timeout          = 60000

kafka.target-list[9].topic                        = middleware_transaction_history_pending_txn_data_stage3
kafka.target-list[9].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[9].pipeline-name                = recon
kafka.target-list[9].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[9].batch-size-bytes             = 3000
kafka.target-list[9].linger-ms                    = 15
kafka.target-list[9].request-timeout-ms           = 5000
kafka.target-list[9].producers-pool-size          = 5
kafka.target-list[9].transaction-timeout          = 60000

kafka.target-list[10].topic                        = middleware_transaction_history_upi_data_stage3
kafka.target-list[10].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[10].pipeline-name                = status-resolver
kafka.target-list[10].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[10].batch-size-bytes             = 3000
kafka.target-list[10].linger-ms                    = 15
kafka.target-list[10].request-timeout-ms           = 5000
kafka.target-list[10].producers-pool-size          = 5
kafka.target-list[10].transaction-timeout          = 60000


kafka.target-list[11].topic                        = middleware_transaction_history_uth_data_stage3
kafka.target-list[11].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[11].pipeline-name                = sink2uth
kafka.target-list[11].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[11].batch-size-bytes             = 3000
kafka.target-list[11].linger-ms                    = 15
kafka.target-list[11].request-timeout-ms           = 5000
kafka.target-list[11].producers-pool-size          = 5
kafka.target-list[11].transaction-timeout          = 60000

kafka.target-list[12].topic                        = middleware_transaction_history_retry_back_fill_data_stage3
kafka.target-list[12].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[12].pipeline-name                = upi-back-filling
kafka.target-list[12].batch-size-bytes             = 3000
kafka.target-list[12].linger-ms                    = 15
kafka.target-list[12].request-timeout-ms           = 5000
kafka.target-list[12].producers-pool-size          = 5
kafka.target-list[12].transaction-timeout          = 60000

kafka.target-list[13].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[13].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[13].pipeline-name                = uth
kafka.target-list[13].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[13].batch-size-bytes             = 3000
kafka.target-list[13].linger-ms                    = 15
kafka.target-list[13].request-timeout-ms           = 5000
kafka.target-list[13].producers-pool-size          = 5
kafka.target-list[13].transaction-timeout          = 60000

kafka.target-list[14].topic                        = middleware_transaction_history_promo_internal_data_stage3
kafka.target-list[14].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[14].pipeline-name                = cbs-stream
kafka.target-list[14].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[14].batch-size-bytes             = 3000
kafka.target-list[14].linger-ms                    = 15
kafka.target-list[14].request-timeout-ms           = 5000
kafka.target-list[14].producers-pool-size          = 5
kafka.target-list[14].transaction-timeout          = 60000
kafka.target-list[14].switch-adaptors-name         = PromoServiceInternalAdaptor

kafka.target-list[15].topic                        = middleware_transaction_history_promo_external_data_stage3
kafka.target-list[15].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[15].pipeline-name                = promo
kafka.target-list[15].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[15].batch-size-bytes             = 3000
kafka.target-list[15].linger-ms                    = 15
kafka.target-list[15].request-timeout-ms           = 5000
kafka.target-list[15].producers-pool-size          = 5
kafka.target-list[15].transaction-timeout          = 60000
kafka.target-list[15].switch-adaptors-name         = PromoServiceExternalAdaptor

kafka.target-list[16].topic                        = middleware_transaction_history_promo_internal_data_stage3
kafka.target-list[16].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[16].pipeline-name                = cbs-retry-pipeline
kafka.target-list[16].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[16].batch-size-bytes             = 3000
kafka.target-list[16].linger-ms                    = 15
kafka.target-list[16].request-timeout-ms           = 5000
kafka.target-list[16].producers-pool-size          = 5
kafka.target-list[16].transaction-timeout          = 60000
kafka.target-list[16].switch-adaptors-name         = PromoServiceInternalAdaptor

kafka.target-list[17].topic                        = uth_lien_recovery_stage3
kafka.target-list[17].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[17].pipeline-name                = cbs-stream
kafka.target-list[17].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[17].batch-size-bytes             = 3000
kafka.target-list[17].linger-ms                    = 15
kafka.target-list[17].request-timeout-ms           = 5000
kafka.target-list[17].producers-pool-size          = 5
kafka.target-list[17].transaction-timeout          = 60000
kafka.target-list[17].switch-adaptors-name         = LienServiceAdaptor

kafka.target-list[18].topic                        = uth_lien_recovery_stage3
kafka.target-list[18].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[18].pipeline-name                = cbs-retry-pipeline
kafka.target-list[18].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[18].batch-size-bytes             = 3000
kafka.target-list[18].linger-ms                    = 15
kafka.target-list[18].request-timeout-ms           = 5000
kafka.target-list[18].producers-pool-size          = 5
kafka.target-list[18].transaction-timeout          = 60000
kafka.target-list[18].switch-adaptors-name         = LienServiceAdaptor

kafka.target-list[19].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[19].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[19].pipeline-name                = retry-pipeline
kafka.target-list[19].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[19].batch-size-bytes             = 3000
kafka.target-list[19].linger-ms                    = 15
kafka.target-list[19].request-timeout-ms           = 5000
kafka.target-list[19].producers-pool-size          = 5
kafka.target-list[19].transaction-timeout          = 60000

kafka.target-list[20].topic                        = uth_cache_updater_data_stage3
kafka.target-list[20].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[20].pipeline-name                = cache-updater-pipeline
kafka.target-list[20].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[20].batch-size-bytes             = 3000
kafka.target-list[20].linger-ms                    = 15
kafka.target-list[20].request-timeout-ms           = 5000
kafka.target-list[20].producers-pool-size          = 5
kafka.target-list[20].transaction-timeout          = 60000
kafka.target-list[20].switch-adaptors-name         = CommonCacheAdaptor,DetailCacheInvalidatorAdaptor,AppSideCacheDataAdaptor

kafka.target-list[21].topic                        = uth_cache_updater_data_stage3
kafka.target-list[21].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[21].pipeline-name                = sink2CacheUpdater
kafka.target-list[21].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[21].batch-size-bytes             = 3000
kafka.target-list[21].linger-ms                    = 15
kafka.target-list[21].request-timeout-ms           = 5000
kafka.target-list[21].producers-pool-size          = 5
kafka.target-list[21].transaction-timeout          = 60000

kafka.target-list[22].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[22].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[22].pipeline-name                = sink2mergedDataForRetryFromChat
kafka.target-list[22].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[22].batch-size-bytes             = 3000
kafka.target-list[22].linger-ms                    = 5
kafka.target-list[22].request-timeout-ms           = 5000
kafka.target-list[22].producers-pool-size          = 5
kafka.target-list[22].transaction-timeout          = 60000

kafka.target-list[23].topic                        = cbs-gg-van_stage3
kafka.target-list[23].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[23].pipeline-name                = van-pipeline
kafka.target-list[23].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[23].batch-size-bytes             = 3000
kafka.target-list[23].linger-ms                    = 15
kafka.target-list[23].request-timeout-ms           = 5000
kafka.target-list[23].producers-pool-size          = 5
kafka.target-list[23].transaction-timeout          = 60000
kafka.target-list[23].switch-adaptors-name         = VanServiceAdaptor

kafka.target-list[24].topic                        = cbs-gg-bo-transaction_stage3
kafka.target-list[24].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[24].pipeline-name                = upi-recon-pipeline
kafka.target-list[24].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[24].batch-size-bytes             = 3000
kafka.target-list[24].linger-ms                    = 15
kafka.target-list[24].request-timeout-ms           = 5000
kafka.target-list[24].producers-pool-size          = 5
kafka.target-list[24].transaction-timeout          = 60000
kafka.target-list[24].switch-adaptors-name         = UpiReconServiceAdaptor

kafka.target-list[25].topic                        = uth_spend_user_kafka_config_data_stage3
kafka.target-list[25].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[25].pipeline-name                = user-spend-docs-creator
kafka.target-list[25].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[25].batch-size-bytes             = 3000
kafka.target-list[25].linger-ms                    = 15
kafka.target-list[25].request-timeout-ms           = 5000
kafka.target-list[25].producers-pool-size          = 5
kafka.target-list[25].transaction-timeout          = 60000
kafka.target-list[25].switch-adaptors-name         = UserSpendConfigKafkaAdaptor

kafka.target-list[26].topic                        = uth_spend_user_month_agg_kafka_config_data_stage3
kafka.target-list[26].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[26].pipeline-name                = user-spend-docs-creator
kafka.target-list[26].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[26].batch-size-bytes             = 3000
kafka.target-list[26].linger-ms                    = 15
kafka.target-list[26].request-timeout-ms           = 5000
kafka.target-list[26].producers-pool-size          = 5
kafka.target-list[26].transaction-timeout          = 60000
kafka.target-list[26].switch-adaptors-name         = UserMonthSpendAggKafkaAdaptor

kafka.target-list[27].topic                        = uth_spend_user_kafka_config_data_stage3
kafka.target-list[27].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[27].pipeline-name                = userId-fetcher-pipeline
kafka.target-list[27].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[27].batch-size-bytes             = 3000
kafka.target-list[27].linger-ms                    = 15
kafka.target-list[27].request-timeout-ms           = 5000
kafka.target-list[27].producers-pool-size          = 5
kafka.target-list[27].transaction-timeout          = 60000
kafka.target-list[27].switch-adaptors-name         = UserIdFetcherAdaptor

kafka.target-list[28].topic                        = uth_promo_upi_txn_data_stage3
kafka.target-list[28].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[28].pipeline-name                = promo-upi-pipeline
kafka.target-list[28].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[28].batch-size-bytes             = 3000
kafka.target-list[28].linger-ms                    = 15
kafka.target-list[28].request-timeout-ms           = 5000
kafka.target-list[28].producers-pool-size          = 5
kafka.target-list[28].transaction-timeout          = 60000
kafka.target-list[28].switch-adaptors-name         = PromoUthAdaptor

kafka.target-list[29].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[29].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[29].pipeline-name                = ts
kafka.target-list[29].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[29].batch-size-bytes             = 3000
kafka.target-list[29].linger-ms                    = 15
kafka.target-list[29].request-timeout-ms           = 5000
kafka.target-list[29].producers-pool-size          = 5
kafka.target-list[29].transaction-timeout          = 60000

kafka.target-list[30].topic                        = upi_consumers_promoengine_data_stage3
kafka.target-list[30].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[30].pipeline-name                = promo
kafka.target-list[30].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[30].batch-size-bytes             = 3000
kafka.target-list[30].linger-ms                    = 15
kafka.target-list[30].request-timeout-ms           = 5000
kafka.target-list[30].producers-pool-size          = 5
kafka.target-list[30].transaction-timeout          = 60000
kafka.target-list[30].switch-adaptors-name         = PromoUpiServiceExternalAdaptor

kafka.target-list[31].topic                        = middleware_transaction_history_chat_data_stage3
kafka.target-list[31].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[31].pipeline-name                = generic-retry-pipeline
kafka.target-list[31].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[31].batch-size-bytes             = 3000
kafka.target-list[31].linger-ms                    = 15
kafka.target-list[31].request-timeout-ms           = 5000
kafka.target-list[31].producers-pool-size          = 5
kafka.target-list[31].transaction-timeout          = 60000
kafka.target-list[31].switch-adaptors-name         = ChatAdaptor

kafka.target-list[32].topic                        = uth_cache_updater_data_stage3
kafka.target-list[32].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[32].pipeline-name                = detail_cache_invalidator_sink
kafka.target-list[32].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[32].batch-size-bytes             = 3000
kafka.target-list[32].linger-ms                    = 15
kafka.target-list[32].request-timeout-ms           = 5000
kafka.target-list[32].producers-pool-size          = 5
kafka.target-list[32].transaction-timeout          = 60000

kafka.target-list[33].topic                        = uth_enricher_dc_data_stage3
kafka.target-list[33].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[33].pipeline-name                = sink2DcUth
kafka.target-list[33].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[33].batch-size-bytes             = 3000
kafka.target-list[33].linger-ms                    = 15
kafka.target-list[33].request-timeout-ms           = 5000
kafka.target-list[33].producers-pool-size          = 5
kafka.target-list[33].transaction-timeout          = 60000

kafka.target-list[34].topic                        = middleware_transaction_history_chat_data_stage3
kafka.target-list[34].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[34].pipeline-name                = chat-data-publish-api-pipeline
kafka.target-list[34].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[34].batch-size-bytes             = 3000
kafka.target-list[34].linger-ms                    = 15
kafka.target-list[34].request-timeout-ms           = 5000
kafka.target-list[34].producers-pool-size          = 5
kafka.target-list[34].transaction-timeout          = 60000
kafka.target-list[34].switch-adaptors-name         = ChatAdaptor

kafka.target-list[37].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[37].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[37].pipeline-name                = upi-relay-pipeline
kafka.target-list[37].confluent-kafka-registry-url = http://schema-registry.uth.paytm.local:8081/
kafka.target-list[37].batch-size-bytes             = 3000
kafka.target-list[37].linger-ms                    = 15
kafka.target-list[37].request-timeout-ms           = 5000
kafka.target-list[37].producers-pool-size          = 5
kafka.target-list[37].transaction-timeout          = 60000

kafka.target-list[38].topic                        = middleware_transaction_history_merged_data_stage3
kafka.target-list[38].bootstrap-servers            = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-list[38].pipeline-name                = toggle-visibility
kafka.target-list[38].confluent-kafka-registry-url = http://stage-kafka1.uth.paytm.local:8081/
kafka.target-list[38].batch-size-bytes             = 80000
kafka.target-list[38].linger-ms                    = 5
kafka.target-list[38].request-timeout-ms           = 5000
kafka.target-list[38].producers-pool-size          = 5
kafka.target-list[38].transaction-timeout          = 60000


#Kafka properties

kafka.target-v2-list[0].kafka-client-name               = cart
kafka.target-v2-list[0].kafka-producer-key              = CART_DATA_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_cart_data_stage3
kafka.target-v2-list[0].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://schema-registry.uth.paytm.local:8081/
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = chat_back_filling
kafka.target-v2-list[1].kafka-producer-key              = CHAT_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[1].topic                           = middleware_transaction_history_chat_back_fill_data_stage3
kafka.target-v2-list[1].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].confluent-kafka-registry-url    = http://schema-registry.uth.paytm.local:8081/
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 5000

kafka.target-v2-list[2].kafka-client-name               = recon_config
kafka.target-v2-list[2].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[2].topic                           = middleware_transaction_history_recon_config_data_stage3
kafka.target-v2-list[2].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].confluent-kafka-registry-url    = http://schema-registry.uth.paytm.local:8081/
kafka.target-v2-list[2].linger-ms                       = 15
kafka.target-v2-list[2].request-timeout-ms              = 5000

kafka.target-v2-list[3].kafka-client-name               = status-resolver
kafka.target-v2-list[3].kafka-producer-key              = STATUS_RESOLVER_PRODUCER
kafka.target-v2-list[3].topic                           = middleware_transaction_history_pending_txn_data_stage3
kafka.target-v2-list[3].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[3].batch-size-bytes                = 3000
kafka.target-v2-list[3].confluent-kafka-registry-url    = http://schema-registry.uth.paytm.local:8081/
kafka.target-v2-list[3].linger-ms                       = 15
kafka.target-v2-list[3].request-timeout-ms              = 5000

kafka.target-v2-list[4].kafka-client-name               = userImageUrl
kafka.target-v2-list[4].kafka-producer-key              = USER_IMAGE_URL_DATA_PRODUCER
kafka.target-v2-list[4].topic                           = user_image_url_data_stage3
kafka.target-v2-list[4].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[4].batch-size-bytes                = 3000
kafka.target-v2-list[4].confluent-kafka-registry-url    = http://schema-registry.uth.paytm.local:8081/
kafka.target-v2-list[4].linger-ms                       = 15
kafka.target-v2-list[4].request-timeout-ms              = 5000

kafka.target-v2-list[5].kafka-client-name               = upi_back_filling
kafka.target-v2-list[5].kafka-producer-key              = UPI_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[5].topic                           = middleware_transaction_history_back_fill_data_stage3
kafka.target-v2-list[5].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[5].batch-size-bytes                = 3000
kafka.target-v2-list[5].linger-ms                       = 15
kafka.target-v2-list[5].request-timeout-ms              = 5000

kafka.target-v2-list[6].kafka-client-name               = search_back_filling
kafka.target-v2-list[6].kafka-producer-key              = UPI_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[6].topic                           = middleware_transaction_history_back_fill_data_stage3
kafka.target-v2-list[6].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[6].batch-size-bytes                = 3000
kafka.target-v2-list[6].linger-ms                       = 15
kafka.target-v2-list[6].request-timeout-ms              = 5000

kafka.target-v2-list[7].kafka-client-name               = USERID_FETCHER_KAFKA_CLIENT
kafka.target-v2-list[7].kafka-producer-key              = USERID_FETCHER_KAFKA_CLIENT_PRODUCER
kafka.target-v2-list[7].topic                           = middleware_transaction_history_userid_fetcher_data_stage3
kafka.target-v2-list[7].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[7].batch-size-bytes                = 3000
kafka.target-v2-list[7].linger-ms                       = 15
kafka.target-v2-list[7].request-timeout-ms              = 5000

kafka.target-v2-list[8].kafka-client-name               = TAG_AGG_SOURCE_CONFIG_KAFKA_CLIENT
kafka.target-v2-list[8].kafka-producer-key              = TAG_AGG_SOURCE_CONFIG_KAFKA_CLIENT
kafka.target-v2-list[8].topic                           = uth_tag_source_config_data_stage3
kafka.target-v2-list[8].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[8].batch-size-bytes                = 3000
kafka.target-v2-list[8].linger-ms                       = 15
kafka.target-v2-list[8].request-timeout-ms              = 5000


# These report codes were removed from blacklist during bank passbook migration task but now these are not required to be ingested, so adding them back to blacklist.
bankData.blackListed.reportCodes = 60202,20701,20702
reportcodes.blocked.for.ShowInListing = 60202,20701,80203,20702,60301
reportcodes.blocked.for.grouping = 60202,20701,20702
bankPassbook.Rptcodes.whilelisted.userslist = 1
bankPassbook.Rptcodes.whilelisteding.percent= 100
bankData.blackListed.dccId = 1
bankData.blackListed.key.deviceId.value.acquirerId = {1:1}




kafka.bootstrap-servers                           = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.confluent-kafka-registry-url                = http://schema-registry.uth.paytm.local:8081/

retry.kafka-list[0].pipelineName                  = upi
retry.kafka-list[0].topic                         = middleware_transaction_history_upi_data_stage3

retry.kafka-list[4].pipelineName                  = upiV2
retry.kafka-list[4].topic                         = middleware_transaction_history_upi_data_v2_stage3

retry.kafka-list[1].pipelineName                  = wallet
retry.kafka-list[1].topic                         = middleware_transaction_history_wallet_data_stage3

retry.kafka-list[2].pipelineName                  = pg
retry.kafka-list[2].topic                         = middleware_transaction_history_pg_data_stage3

retry.kafka-list[3].pipelineName                  = dc-retry-pipeline
retry.kafka-list[3].topic                         = uth_retry_dc_data_stage3

flink.checkpoint.url                              =  s3://pth-flink-checkpoint-nonprod-339712810712/stage/
flink.enableCheckpointing.interval                = 100
flink.minPauseBetweenCheckpoints.interval         = 10000
flink.pg.parallelism.value                        = 1
flink.oms.parallelism.value                       = 1
flink.ppbl-pg.parallelism.value                   = 1
flink.upi.parallelism.value                       = 1
flink.upiV2.parallelism.value                     = 1
flink.retry-pipeline.parallelism.value            = 1
flink.default.parallelism.value                   = 1
flink.wallet.parallelism.value                    = 1
flink.cbs.parallelism.value                       = 2
flink.ts.parallelism.value                       = 2
flink.cbs-converter.parallelism.value             = 1
flink.retry-pipeline-v2.parallelism.value         = 1
flink.chat-back-filling-pipeline.parallelism.value = 1
flink.upi-back-filling-pipeline.parallelism.value      = 1
flink.retry-back-filling-pipeline.parallelism.value      = 1
flink.pg.retry.v2.parallelism.value               = 1
flink.cart.parallelism.value                      = 1
flink.chat.parallelism.value                      = 1
flink.recent.txn.parallelism.value                = 1
flink.recon.parallelism.value             = 1
flink.status-resolver.parallelism.value             = 1
flink.user_image_url.parallelism.value             = 1
flink.promo.parallelism.value                     = 1
flink.cbs-stream.parallelism.value                = 1
flink.cbs-retry-pipeline.parallelism.value        = 1
flink.cache-updater-pipeline.parallelism.value = 1
flink.van-pipeline.parallelism.value = 1
flink.upi-recon-pipeline.parallelism.value = 1
flink.user-spend-docs-creator.parallelism.value = 1
flink.user-analytics-month-agg-creator.parallelism.value = 1
flink.promo-upi-pipeline.parallelism.value = 1
userId-fetcher-pipeline.parallelism.value = 1
flink.generic-retry-pipeline.parallelism.value = 1
flink.dc-main-pipeline.parallelism.value = 1
flink.dc-uth-enricher-pipeline.parallelism.value = 1
flink.es-insert-update-executor-pipeline.parallelism.value = 1
flink.data-audit-pipeline.parallelism.value = 1
flink.chat.data.publish.api.pipeline.parallelism.value = 1
flink.pg.data.publish.api.pipeline.parallelism.value = 1
flink.upi-relay-pipeline.parallelism.value = 1
flink.mandate-pipeline.parallelism.value = 2
flink.mandate-retry-pipeline.parallelism.value = 2
flink.oms.refund.parallelism.value             = 1
flink.toggle-visibility-pipeline.parallelism.value = 1
flink.backfilling-pipeline.parallelism.value = 1


flink.uth.parallelism.value             = 1

aerospike.namespace                               = pth
aerospike.initial-timeout-millis                  = 10000
aerospike.access-timeout-millis                   = 10000
aerospike.sleep-between-retires-millis            = 100
aerospike.max-retries                             = 2
aerospike.max-threads                             = 300
aerospike.write-client-max-threads                = 5
aerospike.max-socket-idle-secs                    = 14
aerospike.host-name                               = pth-comb-new-aerospike-1.uth.paytm.local
aerospike.port                                    = 3000
aerospike.cache-name                              = pth
aerospike.cache-expiry-time                       = 7200
flink.parallelism.enabled                         = true
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
pg.custId.cache.ttl = 7200

enable.whitelisting                               = false
while.listed.users.list                           = **********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********

#15 minutes
pg.p2m.refund.cache.time.in.seconds                = 900
pg.p2m.refund.cache.socket.timeout.in.seconds      = 500
pg.p2m.refund.cache.total.timeout.in.seconds       = 900
pg.p2m.refund.cache.sleep.retries                  = 50

# 15 Days in Sec.
account.ref.no.cache.expiryTime = 1296000

# the below pms api values will be removed when PmsServiceForPromo class will get removed
# pms api
pms.service.base.url                 = https://pms-ite.orgk.com
pms.service.accrefservice.url        = /pms/admin/int/v1/product/mapping
pms.service.read.timeout             = 1000
#pms.client.secret                    = ${MIDDLEWARE_TXN_PMS_CLIENT_SECRET}
pms.client.secret                    = MIDDLEWARE_TXN_PMS_CLIENT_SECRET
pms.service.socket.timeout           = 1000
pms.service.connection.timeout       = 1000

#beneficiary.client.secret                    = ${PTH_BENEFICIARY_SECRET}
beneficiary.client.secret                    = UTH_BENEFICIARY_SECRET

payment-history-alias = stage3_payment_history_alias
index.name.prefix = stage3-payment-history

updateElasticSearchTemplate                      = true
updateSchemaRegistry                             = true
mapOf.KafkaTopic.Schema                          = {\
                                                      'middleware_transaction_history_wallet_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_ppbl_pg_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_retry_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_retry_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_pg_backfill_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_upi_backfill_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_wallet_backfill_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_upi_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_upi_data_v2_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_ppbl_data_stage3': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'middleware_transaction_history_uth_localisation_data_stage3':'com.org.panaroma.commons.dto.MarketplaceKafkaRecord', \
                                                      'middleware_transaction_history_merged_data_stage3': 'com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail', \
                                                      'middleware_transaction_history_chat_data_stage3': 'com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail', \
                                                      'pth_upi_relay_data_stage3' : 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_mandate_data': 'com.org.panaroma.commons.dto.TransactionHistoryDetails', \
                                                      'pth_mandate_retry_data': 'com.org.panaroma.commons.dto.mandate.MandateBaseDto' \
                                                    }
mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'payment_history_template': 'esTemplate-stage3.json', \
                                                      'udir_dump_template': 'udirDumpTemplate-stage3.json', \
                                                      'uth_cst_template': 'udirTemplate-stage3.json', \
                                                      'mandate_info_template': 'mandateInfoEsTemplate-stage3.json', \
                                                      'mandate_activity_template': 'mandateActivityEsTemplate-stage3.json' \
                                                   }

updateElasticSearchV2Template                      = true
mapOf.ElasticSearchV2Template.templateFile          = {\
                                                      'spend_history_template': 'spendDocEsTemplate-stage3.json' \
                                                   }

updateElasticSearchDcTemplate                      = true
mapOf.ElasticSearchDcTemplate.templateFile          = {\
                                                      'payment_history_template': 'esTemplate-stage3.json,es2Template.json', \
                                                      'udir_dump_template': 'udirDumpTemplate-stage3.json', \
                                                      'uth_cst_template': 'udirTemplate-stage3.json',\
                                                      'spend_history_template': 'spendDocEsTemplate-stage3.json', \
                                                      'mandate_info_template': 'mandateInfoEsTemplate-stage3.json', \
                                                      'mandate_activity_template': 'mandateActivityEsTemplate-stage3.json' \
                                                   }

thd.cache.expiry.in.seconds=7200

white.listed.visible.reportCode.list = 20212,20211,20213,20221,20222,40107,20295,20270,20271,20215,20216,20218,30103,40103,40108,20273
#MarketPlace-Utility
mrkplace.sectionId= 10
mrkplace.subSectionId= 420
mrkplace.priority= 2
mrkplace.quality= 2
bank.utility.service.scheme=https
bank.utility.service.port=443
bank.utility.service.host=utilitiesv2.paytm.com
bank.utility.service.path=/v1/language/translation
utility.client.socket.timeout          = 2000
utility.client.tcp.connection.timeout    = 2000
utility.client.request.timeout=8000
marketplace.retryTime=150000
marketplace.localise.cache.time =-1
marketplace.unlocalise.cache.time=86400
test.controller.enabled=false
batch.size=50

updated.index.month.list = stage3-payment-history-08-2020
ts.pending.blacklisted.reportCodes = 20261,20263,20264,20265,20275,20276,20277

spring.jpa.hibernate.ddl-auto                    = none
spring.datasource.url                            = *********************************************
spring.datasource.username                       = app_pth_pc
spring.datasource.password                       = ${MW_PTH_DB_PASSWORD}
spring.datasource.driver-class-name              = com.mysql.jdbc.Driver
spring.jpa.show-sql                              = true
spring.datasource.configuration.maximum-pool-size=30

cache.scheduler.initial.delay.string = ${random.int(300000)}
cache.scheduler.fixed.rate.string    = 900000

fd.whitelisted.users = -1

white.listed.users.list = 1000012882,1107229601,**********,100000006395,1107236181

#isWhiteListingRequired = 1 :: whiteListing required for some users
#isWhiteListingRequired = -1 :: whiteListing not required or open for all
#isWhiteListingRequired = 0 :: close for all
is.white.listing.required.for.chat = -1
is.white.listing.required.for.single.doc = 1

upi.whitelisted.users = -1

#Prometheus Monitoring Constants
prometheus.explorer                                         = INGESTER_stage3
prometheus.hostname                                         = localhost
prometheus.port                                             = 8130

cart.whiteListedUsers = -1

#cart.baseUrl = https://oms-staging.paytm.com/dev1
#cart.baseUrl = https://bankapiautomation-ite.orgk.com/genericMock2/Svs-OMS/dev1
cart.baseUrl = https://bankapiautomation-ite.orgk.com/genericMock2/Svs-OMS
cart.uri = /v3/order/fetch/bulk
cart.timeout =5000
cart.connection.timeout = 5000
cart.socket.timeout = 5000
cart.whitelistedMerchantVerticalIds = 4,76,56,187,86,17,71,87,84,90,105,155,94,204,1688,174,173,199,131,176
#cart.whitelisted.userIds.list.for.narration = **********,**********,**********,**********,********
cart.whitelisted.userIds.list.for.narration = -1
cart.http.max.connection = 100
cart.http.max.connection.per.route = 100

#token.baseUrl = https://checkout-staging.paytm.com
token.baseUrl = https://bankapiautomation-ite.orgk.com/genericMock2/Svs-CheckoutStaging
token.uri = /v1/authorize
token.timeout =5000
token.connection.timeout = 5000
token.socket.timeout = 5000
token.clientKey = clientKey
token.clientSecret = clientSecret
#23hr 55 minutes
token.cache.time = 86100
#Merchant logo Base url
base.url.for.merchant.logo = https://catalog-staging.paytm.com/images/
url.for.ondc.logo = https://assetscdn1.paytm.com/images/catalog/view_item/1519347/*************.png
ondc.verticalId = 204
envBased.jacocoSupport=true

#PMS Cust Api properties
pms.custId.api.baseUrl = https://tpappms-staging1.paytm.com
pms.custId.api.uri = /upi/ext/meta/v2/fetch/custId
pms.custId.api.timeout = 1000
pms.custId.api.connection.timeout = 1000
pms.custId.api.socket.timeout = 1000
pms.custId.api.http.max.connection = 100
pms.custId.api.http.max.connection.per.route = 20
pms.custId.api.secret.key = pmsservice

#bank-oauth
bank.oauth.service.base.url                        = https://oauth-ite-internal.orgk.com
bank.oauth.service.url.category                    = /bank-oauth/ext
#bank.oauth.client.id                               = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID}
#bank.oauth.client.secret                           = ${MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET}
bank.oauth.client.id                               = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET
bank.oauthClient.connectionProvider.maxConnections      = 500
bank.oauthClient.connectionProvider.acquireTimeout      = 45000
bank.oauthClient.connectionProvider.maxIdleTime         = 5
bank.oauth.client.connection.timeout                    = 5000
bank.oauth.read.timeout                                = 5000
bank.oauth.socket.timeout                               = 5000

callOauthForUserImageUrl = true
pushUserInUserImageUrlKafka = true
userImagePushModifiedObjectToEs = true
updateUserImage = true

es.bulk.flush.interval.value = 100

#upi check status
upi.status.check.connection.timeout = 1000
upi.secret.key = ${UPI_PTH_SECRET_KEY}
#upi.baseUrl = https://upisecurev4-staging.orgk.com
upi.baseUrl.for.mandate.txns = https://upisecure-internal-staging.paytm.com
upi.baseUrl.for.non.mandate.txns = https://tpaptransactionalswitch-internal-staging.paytm.com
upi.status.check.url = /upi/int/txn/uth/view
upi.relay.status.check.url = /upi/int/tpap/gateway/mandate/payer/txn/uth/view
upi.mandate.status.check.url = /upi/int/mandate/txn/uth/view
upi.relay.secret.key = ${UPI_PTH_SECRET_KEY}
upi.relay.baseUrl =  https://upi-tpap-ocil-yes-gateway-switch-stage.paytm.com

countOfTxns.metric.for.status.and.source.required = true


static-bank-logo-base-url = https://tpap-logo.paytm.com/uth/images/bank-logo/
static-category-logo-base-url = https://tpap-logo.paytm.com/uth/images/category-logo/
static-status-logo-base-url = https://tpap-logo.paytm.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://tpap-logo.paytm.com/uth/images/wallet-logo/
static-paytm-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/paytm-logo/
static-upi-merchant-logo-base-url  =  https://tpap-logo.paytm.com/upi/images/merchant-logo/
static-merchant-logo-base-url  =  https://tpap-logo.paytm.com/uth/images/merchant-logo/
static-paymentMode-logo-base-url = https://tpap-logo.paytm.com/uth/images/payment-intiation-mode/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/

search.ingestion.whilelisted.users = -1

uthCategory.setter.map = {PG:{'P2M','P2M_REFUND','ADD_AND_PAY'},PPBL_PG:{'P2M','P2M_REFUND','ADD_AND_PAY'},UPI:{'P2M','P2M_REFUND','RECURRING_MANDATE','IPO_MANDATE','LITE_TOPUP_MANDATE','SBMD_MANDATE','ONE_TIME_MANDATE'},PPBL:{'P2M_REFUND','P2M'}}

#change the below value to 0 when we require to block all users
is.whitelisting.required.for.update.via.uth.pipeline = -1
p2mAndAddAndPay.whitelisting.required.for.chat = -1
p2p.whitelisting.required.for.chat = -1

p2p.rollOut.percent.for.chat = 100
rollOut.percent.for.update.via.uth.pipeline = 30
p2mAndAddAndPay.rollOut.percent.for.chat = 30

uth.filter.applied = true
cart.filter.applied = true
merged.filter.applied = true
prometheus.aspect.prefix =
pipeline.name.suffix =
cache.key.prefix = V3_

#keeping ttl as 5 min
userImageNullImageTtlInSec = 300
userImageIgnoreNullImageInCacheForOauthCall = true

listingApi.cache.expiry.in.days = 4
listingUserCache.enable = true
listingUserCache.rollOut.percentage = 100
is.listingUserCache.white.listed.users.list.enabled = 1
listingUserCache.white.listed.users.list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1422994344,1428969782,243463250

listingCache.delay.between.deleteAndSave.milliseconds = 4000

retry-threshold-for-chat-pipeline = 2
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration

retryPipelinePushLimit = 5


# no of grouping days buffer
# 30 min
dc.txn.grouping.fromDate = 1800000
# 30 min
dc.txn.grouping.toDate = 1800000
# 5 days
dc.refund.txn.grouping.fromDate = 432000000
# 7 days
dc.refund.txn.grouping.toDate = 604800000

domesticDc.rollOut.percentage = 100
domesticDc.whitelisting.required = 1
domesticDc.white.listed.users.list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1422994344,1428969782,243463250

#Spend Whitelisting Properties
spend.white.listed.users.list = 1000012882,1107229601,**********,100000006395,1107236181
is.white.listing.required.for.spend = -1
rollOut.percent.for.spend = 10

#10 min expiry time
spend.doc.set.cache.expiry.time = 600
spend.index.name.prefix = stage3-spend-history
spend-history-alias = stage3_spend_history_alias
spend.es.bulk.flush.action.value                    = 1
spend.es.bulk.flush.interval.value                  = 10

# 1 Sec delay for month Doc creation
delay.for.month.agg.creation                        = 1000

#tag aggregation retry count threshold
retry.count.limit.for.tag.agg                        = 10

rollout.config-list[0].percentage = 100
rollout.config-list[0].whitelisting-required = -1
rollout.config-list[0].whitelisting-for = uthNtuCache

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = -1
rollout.config-list[1].whitelisting-for = upiPassbookListingCache

isNtuCacheEnabled = true
isUpiPassbookCacheEnabled = true

rollout.config-list[2].percentage = 0
rollout.config-list[2].whitelisting-required = 1
rollout.config-list[2].user-list = 144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1428969782,243463250,1000400001,**********
rollout.config-list[2].whitelisting-for = imps

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = -1
rollout.config-list[3].user-list= 417711068,506042876,413345538,57893554,315936344,243463250,1314078462,1404082587,1027880890,1361857062,600435113,366922136,490717868,255212505,1407581691,197999747,22003214,324830717,22376586
rollout.config-list[3].whitelisting-for = p2p_inward_failure_pending_events_filter_rollout

rollout.config-list[4].percentage = 10
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = 315936344,144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1428969782,243463250,1000400001,**********
rollout.config-list[4].whitelisting-for = chatDataApi

rollout.config-list[5].percentage = 0
rollout.config-list[5].whitelisting-required = -1
rollout.config-list[5].user-list = 9178274,17789804,12231789,1002514515,315936344,144435616,417711068,506042876,109464264,413345538,190077351,7881770,134834604,57893554,1428969782,243463250,1000400001,**********
rollout.config-list[5].whitelisting-for = omsIntegration

rollout.config-list[6].percentage = 0
rollout.config-list[6].whitelisting-required = -1
rollout.config-list[6].user-list = 9178274,17789804,12231789,413345538,17948689,1059237905,1830830837,310839874,315936344,506042876,1361857062,548361564,17789804,125262162,57893554,417711068,1404082587,230076407,1391078339,243232292,1436626322,1071336715,492771588,1476732725,33138212,17490281,439337006,1728041,156378414,228750709,241548746,10647321,1448708351,230084261
rollout.config-list[6].whitelisting-for = mandatePipeline

rollout.config-list[7].percentage = 0
rollout.config-list[7].whitelisting-required = 1
rollout.config-list[7].user-list = 417711068,506042876,413345538,57893554,315936344,243463250,1314078462,1404082587,1027880890,1361857062,600435113,366922136,490717868,255212505,1407581691,22376586,22003214,197999747
rollout.config-list[7].whitelisting-for = autoTaggingFeature

rollout.config-list[8].percentage = 0
rollout.config-list[8].whitelisting-required = 1
rollout.config-list[8].user-list = 413345538,125262162,243463250,57893554,506042876,1361857062,22338243,44040170,1027880890,12231789
rollout.config-list[8].whitelisting-for = droolsRemoval

rollout.config-list[9].percentage = 0
rollout.config-list[9].whitelisting-required = 1
rollout.config-list[9].user-list = 413345538,125262162,243463250,57893554,506042876,1361857062,22338243,44040170,1027880890,12231789
rollout.config-list[9].whitelisting-for = fwdTxnDetailsRollOut

filter-event-days-diff = 180
txn.event.max.days.diff.from.current.to.process.txn = 60

#10 years
mandate-flow-event-days-limit = 3650

upi-udir-Index-alias = stage3_uth_cst_alias
upi-udir-index.name.prefix = stage3-uth-cst
upi.udir.es.bulk.flush.interval.value = 100
upi.udir.es.bulk.flush.action.value = 10

partialEmiEventRetryPipelinePushLimit = 2

lag.cache.update.interval = 1000
lag.cache.expiry.time     = 100

chatApi.baseUrl = https://chat-external-txn-nonprod.paytmdgt.io
chatApi.uri = /pcchat/v1/chat/uth/notification
chatApi.connect.timeout = 300
chatApi.socket.timeout = 300
chatApi.connection.request.timeout = 300
chatApi.secret.key = paytm-chat-uth-jwt-notification-********-5
chatApi.client.id =  uth_user_1

#Need to add details of API Properly
pgDataApi.baseUrl = https://unified-transaction-history-ite.paytmbank.com
pgDataApi.uri = /payments-history-v2/ext/v1/fetchPgData
pgDataApi.connect.timeout = 300
pgDataApi.socket.timeout = 300
pgDataApi.connection.request.timeout = 300
pgDataApi.secret.key = dtIzGLZyEfcQbYD
pgDataApi.client.id =  OCL_HOME
pgData.cut.off.date.in.epoch =  *************

recap.baseUrl = https://unified-transaction-history-ite.orgk.com
recap.url = /payments-history-v2/ext/v1/spend-analytics/rewindData

online.deals.verticalId  = 66
gv.verticalId            = 66
offline.deals.verticalId = 174
online.deals.logoUrl     = https://assetscdn1.paytm.com/images/catalog/view_item/2283581/*************.png
gv.logoUrl               = https://consumergv.paytm.com/gv-custom-files/images/gift-cards.png
offline.deals.logoUrl    = https://assetscdn1.paytm.com/images/catalog/view_item/1799274/*************.png

check.pointing.enable                              = true
paytm.tpap.handles.list = paytm,pthdfc,ptyes,ptaxis,ptsbi

mandate.cache.expiry.time = 60
mandate.es.bulk.flush.action.value = 100
mandate.es.bulk.flush.interval.value = 100

mandate-info-index-alias = stage3_mandate_info_alias
mandate-activity-index-alias = stage3_mandate_activity_alias

default-retry-count-for-events-enabled = true

recentTxns.cacheUpdater.enabled    = false

autoTagging.details.aerospike.set.name = auto_tagging_details_set
autoTagging.aerospike.namespace                = pth
autoTagging.aerospike.host-name                = stage3-aerospike.uth.paytm.local
autoTagging.aerospike.port                     = 3000

nonTransactingUser.cache.expiry.in.mins = 10
max.days.diff.for.fwd.refund.txns.linking = 45