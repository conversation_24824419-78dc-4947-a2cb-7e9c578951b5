package com.org.panaroma.ingester;

import com.org.panaroma.commons.config.ElasticSearchIndexRangeConfig;
import com.org.panaroma.commons.config.SearchIndexRangeConfig;
import com.org.panaroma.commons.localization.ILocalizationClient;
import com.org.panaroma.commons.localization.ILocalizedDataCacheService;
import com.org.panaroma.commons.localization.LocalizationConfig;
import com.org.panaroma.commons.localization.LocalizationUtility;
import com.org.panaroma.ingester.objects.RunArgumentsConfigObject;
import com.org.panaroma.ingester.services.IngesterService;
import com.org.panaroma.ingester.utils.Utility;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@Log4j2
@ComponentScan(basePackages = { "com.org.panaroma", "com.org.cst.commons" }, excludeFilters = @ComponentScan.Filter(
		type = FilterType.ASSIGNABLE_TYPE,
		classes = { ElasticSearchIndexRangeConfig.class, SearchIndexRangeConfig.class, ILocalizationClient.class,
				ILocalizedDataCacheService.class, LocalizationConfig.class, LocalizationUtility.class }))
@EntityScan(basePackages = { "com.org.panaroma.commons" })
@EnableJpaRepositories(basePackages = { "com.org.panaroma.commons.repository", "com.org.panaroma.ingester.repository" })
@EnableScheduling
public class IngesterApplication {

	public static void main(final String[] args) throws Exception {
		log.info("Starting application.");
		RunArgumentsConfigObject runArgumentsConfigObject = Utility.getRunArguments(args);
		System.setProperty(AbstractEnvironment.ACTIVE_PROFILES_PROPERTY_NAME, runArgumentsConfigObject.getProfile());
		SpringApplication.run(IngesterApplication.class, args);
		IngesterService.startExecution(runArgumentsConfigObject.getPipelineName());
		log.info("Application started.");
	}

}
