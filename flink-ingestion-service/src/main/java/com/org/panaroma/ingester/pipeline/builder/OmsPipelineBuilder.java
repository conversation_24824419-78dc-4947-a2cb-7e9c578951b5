package com.org.panaroma.ingester.pipeline.builder;

import static com.org.panaroma.ingester.constants.Constants.CACHEUPDATER_KAFKASINK;
import static com.org.panaroma.ingester.constants.Constants.SINK_TO_CACHE_UPDATER;
import static com.org.panaroma.ingester.constants.Constants.SINK_TO_UTH;

import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.ingester.AutoTagsEnricher;
import com.org.panaroma.ingester.CacheUpdaterKafkaDtoGenerator;
import com.org.panaroma.ingester.SearchFieldEnricher;
import com.org.panaroma.ingester.StreamEnricher;
import com.org.panaroma.ingester.StreamSplitter;
import com.org.panaroma.ingester.StreamValidator;
import com.org.panaroma.ingester.ThdStreamFilter;
import com.org.panaroma.ingester.UthStreamEnricher;
import com.org.panaroma.ingester.enums.FlinkPipelineNameEnum;
import com.org.panaroma.ingester.sinks.objects.FlinkSink;
import com.org.panaroma.ingester.sources.customSources.BoPanelSource;
import com.org.panaroma.ingester.transformer.ESFieldFilterTransformer;
import com.org.panaroma.ingester.transformer.KafkaFieldFilterTransformer;
import com.org.panaroma.ingester.transformer.OmsDataTransformer;
import com.org.panaroma.ingester.configuration.flink.FlinkProperties;
import com.org.panaroma.ingester.dto.OmsDto;
import com.org.panaroma.ingester.pipeline.FlinkPipeline;
import com.org.panaroma.ingester.pipeline.interfaces.IPipeline;
import com.org.panaroma.ingester.pipeline.interfaces.IPipelineBuilder;
import com.org.panaroma.ingester.sinks.factory.FlinkSinksFactory;
import com.org.panaroma.ingester.sources.factory.FlinkSourcesFactory;
import com.org.panaroma.ingester.sources.objects.FlinkSource;
import com.org.panaroma.ingester.stats.SimpleCounter;
import com.org.panaroma.ingester.utils.Utility;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.runtime.state.StateBackend;
import org.apache.flink.runtime.state.filesystem.FsStateBackend;
import org.apache.flink.streaming.api.datastream.BroadcastConnectedStream;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig.ExternalizedCheckpointCleanup;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class OmsPipelineBuilder implements IPipelineBuilder {

	private FlinkSourcesFactory sourceFunctionFactory;

	private OmsDataTransformer omsDataTransformer;

	private StreamSplitter streamSplitter;

	private StreamEnricher streamEnricher;

	private ThdStreamFilter thdStreamFilter;

	private StreamValidator streamValidator;

	private SearchFieldEnricher searchFieldEnricher;

	private CacheUpdaterKafkaDtoGenerator cacheUpdaterKafkaDtoGenerator;

	private FlinkProperties flinkProperties;

	private FlinkSinksFactory sinkFunctionFactory;

	public static final String SINK = "-sink";

	private final ESFieldFilterTransformer esFieldFilterTransformer;

	private final KafkaFieldFilterTransformer kafkaFieldFilterTransformer;

	public static final String UTHKAFKASINK = "-uth-kafka-sink";

	private UthStreamEnricher uthStreamEnricher;

	private AutoTagsEnricher autoTagsEnricher;

	MapStateDescriptor<String, Object> ruleStateDescriptor = new MapStateDescriptor<>("configBroadcastState",
			BasicTypeInfo.STRING_TYPE_INFO, TypeInformation.of(new TypeHint<>() {
			}));

	private final BoPanelSource configSource;

	@Autowired
	public OmsPipelineBuilder(final FlinkSourcesFactory flinkSourcesFactory, final UthStreamEnricher uthStreamEnricher,
			final OmsDataTransformer omsDataTransformer, final StreamSplitter streamSplitter,
			final StreamEnricher streamEnricher, final ThdStreamFilter thdStreamFilter,
			final StreamValidator streamValidator, final SearchFieldEnricher searchFieldEnricher,
			final CacheUpdaterKafkaDtoGenerator cacheUpdaterKafkaDtoGenerator, final FlinkProperties flinkProperties,
			final FlinkSinksFactory sinkFunctionFactory, final ESFieldFilterTransformer esFieldFilterTransformer,
			final KafkaFieldFilterTransformer kafkaFieldFilterTransformer, final BoPanelSource configSource,
			final AutoTagsEnricher autoTagsEnricher) {
		this.sourceFunctionFactory = flinkSourcesFactory;
		this.uthStreamEnricher = uthStreamEnricher;
		this.omsDataTransformer = omsDataTransformer;
		this.streamSplitter = streamSplitter;
		this.streamEnricher = streamEnricher;
		this.thdStreamFilter = thdStreamFilter;
		this.streamValidator = streamValidator;
		this.searchFieldEnricher = searchFieldEnricher;
		this.cacheUpdaterKafkaDtoGenerator = cacheUpdaterKafkaDtoGenerator;
		this.flinkProperties = flinkProperties;
		this.sinkFunctionFactory = sinkFunctionFactory;
		this.esFieldFilterTransformer = esFieldFilterTransformer;
		this.kafkaFieldFilterTransformer = kafkaFieldFilterTransformer;
		this.configSource = configSource;
		this.autoTagsEnricher = autoTagsEnricher;
	}

	@Override
	public IPipeline build(final String pipelineName) {
		final StreamExecutionEnvironment executionEnvironment = StreamExecutionEnvironment.getExecutionEnvironment();
		this.setParallelism(pipelineName, executionEnvironment);
		this.setCheckPointing(executionEnvironment);
		final List<SourceFunction> sf = sourceFunctionFactory.getSourceFunctionsByPipeline(pipelineName)
			.stream()
			.map(FlinkSource::getSource)
			.collect(Collectors.toList());
		DataStream<OmsDto> inputStream = null;
		for (final SourceFunction sourceFunction : sf) {
			final DataStream<OmsDto> tempStream = executionEnvironment.addSource(sourceFunction)
				.uid(pipelineName + "-source")
				.name(pipelineName + "-source");
			if (inputStream == null) {
				inputStream = tempStream;
			}
			else {
				inputStream.union(tempStream);
			}
		}

		List<SinkFunction> sinkFunctions = sinkFunctionFactory.getSinksByPipeline(pipelineName)
			.stream()
			.map(FlinkSink::getSink)
			.collect(Collectors.toList());

		List<SinkFunction> cacheUpdaterSinkFunctions = sinkFunctionFactory.getSinksByPipeline(SINK_TO_CACHE_UPDATER)
			.stream()
			.map(FlinkSink::getSink)
			.collect(Collectors.toList());

		final List<SinkFunction> uthSinkFunctions = sinkFunctionFactory.getSinksByPipeline(SINK_TO_UTH)
			.stream()
			.map(FlinkSink::getSink)
			.collect(Collectors.toList());

		inputStream.map(new SimpleCounter(pipelineName + "-inputCount"));

		BroadcastStream<Map<String, Object>> configStream = executionEnvironment.addSource(configSource)
			.broadcast(ruleStateDescriptor);

		BroadcastConnectedStream inputConnectedStream = inputStream.connect(configStream);

		DataStream convertedStream = inputConnectedStream.process(omsDataTransformer)
			.uid(pipelineName + "-omsProcessor")
			.name(pipelineName + "-omsProcessor");

		// DataStream convertedStream = inputStream.flatMap(this.omsDataTransformer)
		// .uid(pipelineName + "-omsProcessor")
		// .name(pipelineName + "-omsProcessor");

		DataStream filteredStream = convertedStream.flatMap(this.thdStreamFilter)
			.uid(pipelineName + "-filter")
			.name(pipelineName + "-filter");

		DataStream validatedStream = filteredStream.flatMap(this.streamValidator)
			.uid(pipelineName + "-validator")
			.name(pipelineName + "-validator");

		DataStream splittedStream = validatedStream.flatMap(this.streamSplitter)
			.uid(pipelineName + "-splitter")
			.name(pipelineName + "-splitter");

		DataStream enrichedStream = splittedStream.flatMap(this.streamEnricher)
			.uid(pipelineName + "-enricher")
			.name(pipelineName + "-enricher");

		DataStream cacheUpdaterKafkaStream = enrichedStream.flatMap(this.cacheUpdaterKafkaDtoGenerator)
			.uid(pipelineName + "-cacheUpdateUserUpdater")
			.name(pipelineName + "-cacheUpdateUserUpdater");

		DataStream uthStream = enrichedStream.flatMap(this.uthStreamEnricher)
			.uid(pipelineName + "-uthStreamEnricher")
			.name(pipelineName + "-uthStreamEnricher");

		DataStream searchFieldsEnricherStream = uthStream.flatMap(this.searchFieldEnricher)
			.uid(pipelineName + "-search")
			.name(pipelineName + "-search");

		DataStream uthSinkStream = uthStream.filter(new UthStreamFilter(this.flinkProperties.getIsUthFilterApplied()));

		BroadcastConnectedStream<TransformedTransactionHistoryDetail, Map<String, Object>> searchFieldsEnricherConnectedStream = searchFieldsEnricherStream
			.connect(configStream);

		// Auto Tags applied on stream PTH-228
		DataStream<TransformedTransactionHistoryDetail> autoTagsEnricherStream = searchFieldsEnricherConnectedStream
			.process(this.autoTagsEnricher)
			.returns(TransformedTransactionHistoryDetail.class)
			.uid(pipelineName + "-autoTagging")
			.name(pipelineName + "-autoTagging");

		BroadcastConnectedStream autoTagsEnricherStreamrConnectedStream = autoTagsEnricherStream.connect(configStream);

		BroadcastConnectedStream uthSinkConnectedStream = uthSinkStream.connect(configStream);

		DataStream searchFieldsEnricherStreamFiltered = autoTagsEnricherStreamrConnectedStream
			.process(esFieldFilterTransformer)
			.uid(pipelineName + "-searchFieldFilter")
			.name(pipelineName + "-searchFieldFilter");

		DataStream uthSinkStreamFiltered = uthSinkConnectedStream.process(kafkaFieldFilterTransformer)
			.uid(pipelineName + "-uthFieldFilter")
			.name(pipelineName + "-uthFieldFilter");

		for (SinkFunction sinkFunction : cacheUpdaterSinkFunctions) {
			if (sinkFunction instanceof FlinkKafkaProducer) {
				cacheUpdaterKafkaStream.addSink(sinkFunction)
					.uid(pipelineName + CACHEUPDATER_KAFKASINK)
					.name(pipelineName + SINK);
			}
		}

		for (SinkFunction sinkFunction : uthSinkFunctions) {
			if (sinkFunction instanceof FlinkKafkaProducer) {
				uthSinkStreamFiltered.addSink(sinkFunction).uid(pipelineName + UTHKAFKASINK).name(pipelineName + SINK);
			}
		}

		for (SinkFunction sinkFunction : sinkFunctions) {
			searchFieldsEnricherStreamFiltered.addSink(sinkFunction).uid(pipelineName + SINK).name(pipelineName + SINK);
		}

		return new FlinkPipeline(executionEnvironment, pipelineName);
	}

	@Override
	public List<String> appliesTo() {
		return Arrays.asList(FlinkPipelineNameEnum.OMS.getPipelineName());
	}

	private void setParallelism(final String pipelineName, final StreamExecutionEnvironment env) {
		if (!this.flinkProperties.getParallelismEnabled()) {
			return;
		}
		env.setParallelism(Utility.getParallelismValue(pipelineName, flinkProperties));
	}

	private void setCheckPointing(final StreamExecutionEnvironment env) {
		if (!this.flinkProperties.getIsCheckPointingEnabled()) {
			log.info("Checkpointing is disabled now.");
			return;
		}
		final StateBackend stateBackend = new FsStateBackend(flinkProperties.getCheckPointingUrl());
		env.setStateBackend(stateBackend);
		env.enableCheckpointing(flinkProperties.getEnableCheckpointingInterval());
		env.getCheckpointConfig()
			.setMinPauseBetweenCheckpoints(flinkProperties.getMinPauseBetweenCheckpointsInterval());
		env.getCheckpointConfig().enableExternalizedCheckpoints(ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
	}

}