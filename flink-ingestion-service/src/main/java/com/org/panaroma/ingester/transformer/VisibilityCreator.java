package com.org.panaroma.ingester.transformer;

import static com.org.panaroma.commons.constants.BankDataConstants.BANK_PASSBOOK_MISSING_REPORTCODES;
import static com.org.panaroma.commons.constants.BankDataConstants.IS_VIRTUAL_PARTICIPANT_ADDED;
import static com.org.panaroma.commons.constants.BankDataConstants.PPBL_TS_IMPS_REPORT_CODES;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_CREATE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_PAY;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_REVOKE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_UPDATE;
import static com.org.panaroma.commons.constants.Constants.IMPS;
import static com.org.panaroma.commons.dto.ClientStatusEnum.PENDING;
import static com.org.panaroma.commons.dto.ClientStatusEnum.SUCCESS;
import static com.org.panaroma.commons.utils.UtilityExtension.isDebitParticipantBankDetailsPresent;
import static com.org.panaroma.ingester.constants.Constants.BANK_TRANSFER_PREAPPROVED;
import static com.org.panaroma.ingester.constants.Constants.COLLECT;
import static com.org.panaroma.ingester.constants.Constants.COLLECT_STATUS;
import static com.org.panaroma.ingester.constants.Constants.COLON;
import static com.org.panaroma.ingester.constants.Constants.COMMA;
import static com.org.panaroma.ingester.constants.Constants.INTEGRATION_ISSUE_WITH_SOURCE;
import static com.org.panaroma.ingester.constants.Constants.REASON;
import static com.org.panaroma.ingester.constants.Constants.REASON_FOR_NOT_VISIBLE;
import static com.org.panaroma.ingester.constants.Constants.REQUEST_PENDING;
import static com.org.panaroma.ingester.constants.Constants.TRANSACTION_PURPOSE;
import static com.org.panaroma.ingester.constants.Constants.TXN_TYPE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.PG_OMS_TXN_EVENT_COUNT;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.StatusEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.CartUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.rollout.strategy.IRolloutStrategyHelper;
import com.org.panaroma.ingester.enums.VisibilityFalseReasonEnum;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.transformer.interfaces.IVisibilityCreator;
import com.org.panaroma.ingester.utils.Utility;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/***
 * Take care of visibility criteria for user. Here we check given values are not null or
 * not. If key has null/empty value, isVisible flag will be false. list of not null keys -
 * this.keysForNullCheck;
 */
@Component
@Log4j2
public class VisibilityCreator implements IVisibilityCreator {

	private List<String> keysForNullCheck;

	/*
	 * @Value("${domesticDc.rollOut.percentage}") private Double dcRollOutPercentage;
	 *
	 * @Value("${domesticDc.whitelisting.required}") private Integer
	 * dcWhitelistingRequired;
	 *
	 * @Value("${domesticDc.white.listed.users.list}") private List<String>
	 * dcWhitelistedUsers;
	 */

	@Value("${bankPassbook.Rptcodes.whilelisted.userslist}")
	private List<String> whiteListeUserIdsForBankPasssbook;

	@Value("${paytm.tpap.handles.list}")
	private List<String> paytmTpapVpaHandlesList;

	@Value("${bankPassbook.Rptcodes.whilelisteding.percent}")
	private Double whiteListePercentageForBankPasssbook;

	// UPI P2M Collect txns with contextMap.collect_status in any of these states will
	// have isVisible flag true.
	private static List<String> visibleUpiP2mCollectStates = Arrays.asList("SUCCESS", "DEEMED", "FAILURE", "DECLINED");

	private static List<String> whiteListedReportCodeList = Arrays.asList(
			// Phase1: Imps and Xfer report codes
			"20212", "20211", "20213", "20221", "20222", "40107", "20295", "20270", "20271", "20215", "20216", "20218",
			"30103", "40103", "40108", "20273",
			// Phase2: neft and rtgs report codes
			"20410", "20420", "20411", "20421", "20430", "20440", "40110", "20293",
			// Phase3:
			/* "20210", */"90250", "90230", "90220", "60400", "90240", "60225", "60226", "70220", "70215", "72206",
			"70218", "70203", "72203", "70211", "70216", "70206", "70217", "70225",
			// phase4:
			"70205", "70210", "70236", "71203", "72205", "71205", "72210", "71210", "72216", "71216", "71217", "72217",
			"72236", "71236", "74203", "73203", "73205", "74205", "74210", "73210", "74216", "73216", "74217", "73217",
			"74236", "73236", "70266", "71206", "71211", "71215", "71218", "71220", "71225", "71266", "73206", "73211",
			"73215", "73218", "73220", "73225", "73266", "72211", "72215", "72218", "72220", "72225", "72266", "74206",
			"74211", "74215", "74218", "74220", "74225", "74266", "20200", "20110", "60201", "70201", "70204", "71201",
			"71204", "73201", "73204", "72201", "72204", "74201", "74204", "70202", "71202", "72202", "73202", "74202",
			"20100", "60200", "70233", "90100", "90110", "90120", "40105", "70285", "71285", "73285", "72285", "74285",
			"20304", "20400", "60205", "60230", "70207", "70232", "70286", "70287", "20220", "70219", "70230", "71219",
			"73219", "72219", "74219",
			// phase5:
			"20300", "20301", "60215", "60221", "70280", "91014", "91016", "91018", "91020", "91022", "91026", "91034",
			"91036", "60206", "60224", "60231", "70221", "70222", "70223", "72260", "72261", "20240", "20257", "20290",
			"20350", "20601", "23000", "23001", "40104", "60229", "60240", "60403", "60612", "60615", "60616", "60712",
			"60714", "70226", "70290", "70291", "92009",
			// UPI Report Codes
			"20501", "20502", "20503", "20504", "20505", "20506", "20507", "20508", "20509", "20510", "20512", "20513",
			"20525", "20526",
			// phase6:
			"71207", "71232", "71286", "71287", "72207", "72232", "72360", "73207", "73232", "73286", "73287", "74207",
			"74232", "74260", "74360", "72286", "72287", "74286", "74287", "70224", "71221", "71222", "71223", "71224",
			"72361", "73221", "73222", "73223", "73224", "74261", "74361", "74221", "74224", "74223", "74222", "72221",
			"72222", "72223", "72224", "92003", "92005", "92003", "92006", "20302", "91028", "91024", "91015", "91017",
			"91021", "91023", "91027", "91029", "91025", "91019", "91035", "91037", "60223", "60232", "60233", "60220",
			"60219", "60222", "91053", "10102", "20101", "20258", "20259", "20272", "20274", "20280", "20604", "40109",
			"60214", "60216", "60217", "60250", "60300", "60410", "70208", "70209", "70212", "70228", "70229", "70234",
			"70235", "70240", "70241", "70242", "70243", "70282", "70300", "70301", "70400", "80501", "91042", "91043",
			"91054", "91056", "91057", "92002", "92004", "92010", "92011",
			// Fd report codes
			"20261", "20265", "20263", "20264", "20250", "20260", "20205", "20275", "20276", "23002", "20277", "20267",
			// International DC ReportCode
			"22254", "22255", "22252", "22261", "22256", "22210", "22260", "22214", "22234", "22251", "22253", "22266",
			"24210", "24214", "24234", "24251", "24252", "24253", "24254", "24255", "24256", "24260", "24261", "24266",
			"72288", "74288",
			// Domestic DC net banking report codes
			"20210", "20234", "20214", "20253", "20256", "20252", "20254", "20255", "20251", "20266", "70289", "70288",
			"21210", "21234", "21214", "21253", "21256", "21252", "21254", "21255", "21251", "21266", "71288",
			// remaining reportcodes not handled in UTH but handled in AS : made
			// IS_VISIBLE true but would make SHOW_IN_LISTING false
			// because we get these events from pg or upi
			"60202", /* "20701",(This reportcode is not live on prod) */ "80203",
			/* "20702",(This reportcode is not live on prod) */
			"60301", "60711", "91058", "60890", "54193", "71291", "20602", "60721",
			// phase 1 reportcodes
			"60625", /* "60626",(This reportcode is not live on prod) */"80207", "54192", "60718", "54194", "60723",
			"60717", /* Loan Repayment reportCode */ "20281",
			/* Loan Repayment reportCode refund */ "20282", /* Visa OCT report code */ "25001", "25002", "25101",
			"25102", /* nach dishonour code */ "91060", /* VISA_RECURRING_REPORT_CODE */ "21208", "21209", "22208",
			"22209", /* nach dishonour refund code */ "91061");

	private static Map<TransactionTypeEnum, Function<TransformedTransactionHistoryDetail, Boolean>> txnTypeVisibilityCheckMap;

	private final IRolloutStrategyHelper rolloutStrategyHelper;

	private final MetricsAgent metricsAgent;

	@Autowired
	public VisibilityCreator(
			@Value("#{'${white.listed.visible.reportCode.list}'.split(',')}") final List<String> whiteListedReportCodeList,
			final IRolloutStrategyHelper rolloutStrategyHelper, final MetricsAgent metricsAgent) {
		this.keysForNullCheck = Arrays.asList("EntityId", "EntityType", "StreamSource", "Amount", "TxnType",
				"TxnIndicator", "Status", "TxnId", "TxnDate", "UpdatedDate", "Participants");
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.metricsAgent = metricsAgent;
		// this.rolloutStrategy = rolloutStrategy;
		// todo: need to revisit later, property was not getting picked up on ite env, so
		// making it static

		/*
		 * this.whiteListedReportCodeList = whiteListedReportCodeList; if
		 * (whiteListedReportCodeList == null || (whiteListedReportCodeList.size() == 1 &&
		 * StringUtils.isBlank(whiteListedReportCodeList.get(0)))) {
		 * this.whiteListedReportCodeList = new ArrayList<>(); }
		 */
	}

	/*
	 * Always assign txnTypeVisibilityCheckMap in last other wise it has double checked
	 * locking problem
	 */
	private Map<TransactionTypeEnum, Function<TransformedTransactionHistoryDetail, Boolean>> getTxnTypeVisibleKeyCheckMap() {
		if (txnTypeVisibilityCheckMap != null) {
			return txnTypeVisibilityCheckMap;
		}

		synchronized (this) {
			if (txnTypeVisibilityCheckMap == null) {
				Map<TransactionTypeEnum, Function<TransformedTransactionHistoryDetail, Boolean>> tempMap;
				tempMap = new HashMap<>();

				tempMap.put(TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD, this::doForP2pUpiToWallet);
				tempMap.put(TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD, this::doForP2P);
				tempMap.put(TransactionTypeEnum.P2P_OUTWARD, this::doForP2P);
				tempMap.put(TransactionTypeEnum.P2P_INWARD, this::doForP2P);
				tempMap.put(TransactionTypeEnum.P2P2M, this::doForP2P);
				tempMap.put(TransactionTypeEnum.P2P2M_INWARD, this::doForP2P);
				tempMap.put(TransactionTypeEnum.P2P2M_OUTWARD, this::doForP2P);
				tempMap.put(TransactionTypeEnum.P2P_INWARD_REMITTANCE, this::doForP2P);

				// in case of p2m, same checks will be applied like p2p.
				tempMap.put(TransactionTypeEnum.P2M, this::doForP2M);
				tempMap.put(TransactionTypeEnum.ADD_MONEY_TO_BANK, this::doForP2M);
				tempMap.put(TransactionTypeEnum.P2P_OUTWARD_REMITTANCE, this::doForP2M);
				tempMap.put(TransactionTypeEnum.P2M_INTERNATIONAL, this::doForP2M);

				tempMap.put(TransactionTypeEnum.P2M_REFUND, this::doForRefund);
				tempMap.put(TransactionTypeEnum.P2P2M_REFUND, this::doForRefund);
				tempMap.put(TransactionTypeEnum.P2P_OUTWARD_REMITTANCE_REFUND, this::doForRefund);
				tempMap.put(TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL, this::doForRefund);

				tempMap.put(TransactionTypeEnum.CASHBACK_RECEIVED, this::doForCashback);
				tempMap.put(TransactionTypeEnum.OTHER_CREDIT, this::doForCashback);
				tempMap.put(TransactionTypeEnum.OTHER_DEBIT, this::doForCashback);
				tempMap.put(TransactionTypeEnum.ADD_MONEY, this::doForCashback);
				tempMap.put(TransactionTypeEnum.WALLET_SETTLEMENT, this::doForWalletSettlement);

				tempMap.put(TransactionTypeEnum.ON_HOLD, this::doForOauthAndCapture);
				tempMap.put(TransactionTypeEnum.RELEASED, this::doForOauthAndCapture);

				tempMap.put(TransactionTypeEnum.PPBL_TRANSACTION, this::doForPpblTransaction);

				// Wallet Interoperability txnTypes visibility Checks
				tempMap.put(TransactionTypeEnum.WALLET_UPI_DEBIT_P2P, this::doForP2P);
				tempMap.put(TransactionTypeEnum.UPI_WALLET_CREDIT, this::doForP2P);
				tempMap.put(TransactionTypeEnum.WALLET_UPI_DEBIT_P2M, this::doForP2M);
				// Add P2P checks for reversal too since checks are same
				// Todo: Need to check name nullity condition should be there or not after
				// wallet sample events
				tempMap.put(TransactionTypeEnum.WALLET_UPI_DEBIT_REVERSAL, this::doForP2P);
				tempMap.put(TransactionTypeEnum.UPI_WALLET_CREDIT_REVERSAL, this::doForP2P);
				tempMap.put(TransactionTypeEnum.IPO_MANDATE, this::doForIpoMandate);
				tempMap.put(TransactionTypeEnum.RECURRING_MANDATE, this::doForRecurringMandate);
				tempMap.put(TransactionTypeEnum.LITE_TOPUP_MANDATE, this::doForLiteTopUpMandate);
				tempMap.put(TransactionTypeEnum.SBMD_MANDATE, this::doForSbmdMandate);
				tempMap.put(TransactionTypeEnum.ONE_TIME_MANDATE, this::doForOneTimeMandate);

				// Add deactivation Event check on amt
				tempMap.put(TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE, this::doForUpiLite);

				tempMap.put(TransactionTypeEnum.SYSTEM_DEBIT, this::doForSystemDebit);

				txnTypeVisibilityCheckMap = tempMap;
			}
		}
		return txnTypeVisibilityCheckMap;
	}

	private Boolean doForPpblTransaction(final TransformedTransactionHistoryDetail detail) {
		String inputReportCode = Utility.getInputRptCodeWithTxnIndicator(detail);

		// only show success txns for p2m refund txns
		if (TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(detail.getTxnType())) {
			if (!SUCCESS.getStatusKey().equals(detail.getStatus())) {
				return false;
			}
		}
		if (TransactionSource.TS.getTransactionSourceKey().equals(detail.getStreamSource())) {
			if (detail.getParticipants().size() != 2) {
				log.warn("TS Event : setting visibility false as no of participants : {} for txn id : {}",
						detail.getParticipants().size(), detail.getTxnId());
				this.pushReasonInContextMap(detail,
						VisibilityFalseReasonEnum.PARTICIPANTS_NOT_TWO.getVisibilityFalseReasonKey());
				return false;
			}
			// in case of non-terminal state, we are setting the visibility false for TS
			// event.
			// coz there may be a case where ts gets time out from cbs and txn gets
			// success.
			// Not setting visibility false for IMPS events.
			if (ClientStatusEnum.PENDING.getStatusKey().equals(detail.getStatus())
					&& !(PPBL_TS_IMPS_REPORT_CODES.contains(inputReportCode)
							&& rolloutStrategyHelper.isUserWhiteListed(IMPS, detail.getEntityId()))) {
				this.pushReasonInContextMap(detail,
						VisibilityFalseReasonEnum.PENDING_STATUS.getVisibilityFalseReasonKey());
				return false;
			}
			for (TransformedParticipant participant : detail.getParticipants()) {
				if (checkForParticipant(detail, participant)) {
					log.warn("TS Event : setting visibility false as checkForParticipant failed for txn id : {}",
							detail.getTxnId());
					return false;
				}
			}
		}
		else if (TransactionSource.PPBL.getTransactionSourceKey().equals(detail.getStreamSource())) {

			int allowedParticipantValue = 1;
			if (Utility.isXferTxn(detail)) {
				allowedParticipantValue = 2;
			}
			// changed allowed participant value for virtual participants txns
			if (detail.getContextMap() != null && detail.getContextMap().containsKey(IS_VIRTUAL_PARTICIPANT_ADDED)) {
				allowedParticipantValue = 2;
			}
			if (detail.getParticipants().size() > allowedParticipantValue) {
				/*
				 * log.
				 * warn("PPBL Event : setting visibility false as no of participants : {} for txn id : {}, allowedParticipantValue : {}"
				 * , detail.getParticipants().size(), detail.getTxnId(),
				 * allowedParticipantValue);
				 */
				this.pushReasonInContextMap(detail,
						VisibilityFalseReasonEnum.PARTICIPANT_COUNT_MISMATCH.getVisibilityFalseReasonKey());
				return false;
			}

			for (TransformedParticipant participant : detail.getParticipants()) {
				if (checkForParticipant(detail, participant)) {
					/*
					 * log.
					 * warn("PPBL Event : setting visibility false as checkForParticipant failed for txn id : {}"
					 * , detail.getTxnId());
					 */
					return false;
				}
			}
			// whitelisting ppbl reportcodes for visibility, if empty list, whitelist all
			// reportcode
			if (whiteListedReportCodeList.size() == 0 || (StringUtils.isNotBlank(inputReportCode)
					&& whiteListedReportCodeList.contains(inputReportCode))) {
				/*
				 * if (DOMESTIC_DC_REPORT_CODE.contains(inputReportCode) &&
				 * !com.org.panaroma.commons.utils.Utility.
				 * isUserWhitelistedFromPercentageOrUserList(dcRollOutPercentage,
				 * dcWhitelistingRequired, dcWhitelistedUsers, detail.getEntityId())) {
				 * return false; }
				 */
				if (BANK_PASSBOOK_MISSING_REPORTCODES.contains(inputReportCode)
						&& !com.org.panaroma.commons.utils.Utility.isUserWhitelistedFromPercentageOrUserList(
								whiteListePercentageForBankPasssbook, 1, whiteListeUserIdsForBankPasssbook,
								detail.getEntityId())) {
					return false;
				}
				return true;
			}
			return false;
		}
		return true;
	}

	private Boolean doForWalletSettlement(final TransformedTransactionHistoryDetail detail) {
		// minimum one participant
		if (detail.getParticipants().size() != 1) {
			this.pushReasonInContextMap(detail,
					VisibilityFalseReasonEnum.PARTICIPANTS_NOT_ONE.getVisibilityFalseReasonKey());
			return false;
		}

		for (TransformedParticipant participant : detail.getParticipants()) {
			if (checkForParticipant(detail, participant)) {
				return false;
			}
		}
		return true;
	}

	private Boolean doForOauthAndCapture(final TransformedTransactionHistoryDetail detail) {
		// minimum one participant
		// visibility of released will be off till the on hold is received.
		if (detail.getParticipants().size() < 1) {
			this.pushReasonInContextMap(detail,
					VisibilityFalseReasonEnum.LESS_THAN_ONE_PARTICIPANTS.getVisibilityFalseReasonKey());
			return false;
		}

		for (TransformedParticipant participant : detail.getParticipants()) {
			if (checkForParticipant(detail, participant)) {
				return false;
			}
		}

		return true;
	}

	private Boolean doForCashback(final TransformedTransactionHistoryDetail detail) {

		if (TransactionTypeEnum.CASHBACK_RECEIVED.getTransactionTypeKey().equals(detail.getMainTxnType())
				&& !StatusEnum.SUCCESS.getStatusKey().equals(detail.getStatus())) {
			this.pushReasonInContextMap(detail,
					VisibilityFalseReasonEnum.CASHBACK_NOT_SUCCESSFUL_TXN.getVisibilityFalseReasonKey());
			return false;
		}

		if (detail.getParticipants().size() < 1) {
			this.pushReasonInContextMap(detail,
					VisibilityFalseReasonEnum.LESS_THAN_ONE_PARTICIPANTS.getVisibilityFalseReasonKey());
			return false;
		}

		for (TransformedParticipant participant : detail.getParticipants()) {
			if (checkForParticipant(detail, participant)) {
				return false;
			}
		}
		return true;

	}

	private Boolean doForP2M(final TransformedTransactionHistoryDetail detail) {
		// if less than 2 participants, then isVisible will be false.
		if (detail.getParticipants().size() < 2) {
			log.warn(
					"pipeline: {}, Number of participants is less than 2. So marking visibility flag as false for systemId: {}",
					Utility.getActivePipelineName(), detail.getTxnId());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.LESS_THAN_TWO_PARTICIPANTS.name());
			this.pushReasonInContextMap(detail,
					VisibilityFalseReasonEnum.LESS_THAN_TWO_PARTICIPANTS.getVisibilityFalseReasonKey());
			return false;
		}

		for (TransformedParticipant participant : detail.getParticipants()) {
			if (checkForParticipant(detail, participant)) {
				return false;
			}
		}

		// P2M transactions should never have txnIndicator as CREDIT, so mark all such
		// transactions as not visible
		if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(detail.getTxnIndicator())) {
			this.pushReasonInContextMap(detail,
					VisibilityFalseReasonEnum.P2M_TXN_WITH_CREDIT_INDICATOR.getVisibilityFalseReasonKey());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.P2M_TXN_WITH_CREDIT_INDICATOR.name() + COMMA
							+ INTEGRATION_ISSUE_WITH_SOURCE + COLON
							+ VisibilityFalseReasonEnum.P2M_TXN_WITH_CREDIT_INDICATOR.isIntegrationIssueWithSource());
			return false;
		}

		return true;
	}

	private boolean checkForParticipant(final TransformedTransactionHistoryDetail detail,
			final TransformedParticipant participant) {
		if (participant.getAmount() == null) {
			log.debug("Dont have amount in participant :{} sp marking isVisible false for txnId : {}", participant,
					detail.getTxnId());
			this.pushReasonInContextMap(detail,
					VisibilityFalseReasonEnum.PARTICIPANT_AMOUNT_NULL.getVisibilityFalseReasonKey());
			return true;
		}
		if (participant.getPaymentSystem() == null) {
			log.debug("Dont have paymenySystem in participant :{} sp marking isVisible false for txnId : {}",
					participant, detail.getTxnId());
			this.pushReasonInContextMap(detail,
					VisibilityFalseReasonEnum.PARTICIPANT_PAYMENT_SYSTEM_NULL.getVisibilityFalseReasonKey());
			return true;
		}

		return false;
	}

	// in case of refund, we will show transaction as pending to user. will check only for
	// name, amount and paymentSystem.
	private Boolean doForRefund(final TransformedTransactionHistoryDetail transactionHistoryDetails) {
		// only show success txns for p2m refund txns
		if (TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(transactionHistoryDetails.getMainTxnType())) {
			if (!SUCCESS.getStatusKey().equals(transactionHistoryDetails.getStatus())) {
				return false;
			}
		}
		for (TransformedParticipant participant : transactionHistoryDetails.getParticipants()) {
			if (checkForParticipant(transactionHistoryDetails, participant)) {
				return false;
			}
		}
		return true;
	}

	private Boolean doForP2pUpiToWallet(final TransformedTransactionHistoryDetail transactionHistoryDetails) {
		if (transactionHistoryDetails.getParticipants().size() < 2) {
			// Don't have credit and debit legs, so simply marking it to false.
			log.warn(
					"pipeline: {}, Number of participant is less than 2. So marking visibility flag as false for systemId: {}",
					Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.LESS_THAN_TWO_PARTICIPANTS.name());
			this.pushReasonInContextMap(transactionHistoryDetails,
					VisibilityFalseReasonEnum.LESS_THAN_TWO_PARTICIPANTS.getVisibilityFalseReasonKey());
			return false;
		}

		for (TransformedParticipant participant : transactionHistoryDetails.getParticipants()) {
			if (participant.getAmount() == null) {
				log.info("pipeline: {}, systemId: {}, Dont have amount in participant :{} so marking isVisible false.",
						Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId(), participant);
				this.pushReasonInContextMap(transactionHistoryDetails,
						VisibilityFalseReasonEnum.PARTICIPANT_AMOUNT_NULL.getVisibilityFalseReasonKey());
				return false;
			}
			if (participant.getPaymentSystem() == null) {
				log.info(
						"pipeline: {}, systemId: {}, Dont have paymentSystem in participant :{} so marking isVisible false.",
						Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId(), participant);
				this.pushReasonInContextMap(transactionHistoryDetails,
						VisibilityFalseReasonEnum.PARTICIPANT_PAYMENT_SYSTEM_NULL.getVisibilityFalseReasonKey());
				return false;
			}
			// if name empty but walletMobileNumber notNull them do nothing
			// else return false
			if (StringUtils.isBlank(participant.getName())) {
				if (participant.getWalletData() == null
						|| participant.getWalletData().getWalletMobileNumber() == null) {
					log.info(
							"pipeline: {}, systemId: {}, Dont have name in participant :{} so marking isVisible false.",
							Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId(), participant);
					this.pushReasonInContextMap(transactionHistoryDetails,
							VisibilityFalseReasonEnum.PARTICIPANT_NAME_AND_WALLET_MOBILE_NUMBER_NULL
								.getVisibilityFalseReasonKey());
					return false;
				}
			}
		}
		return true;
	}

	/***
	 * Not null field for visible = name, amount, paymentSystem. Flag will be false when
	 * transaction is in pending and for credit leg.
	 */
	private Boolean doForP2P(final TransformedTransactionHistoryDetail transactionHistoryDetails) {

		if (transactionHistoryDetails.getParticipants().size() < 2) {
			// Don't have credit and debit legs, so simply marking it to false.
			log.warn(
					"pipeline: {}, Number of participant is less than 2. So marking visibility flag as false for systemId: {}",
					Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.LESS_THAN_TWO_PARTICIPANTS.name());
			this.pushReasonInContextMap(transactionHistoryDetails,
					VisibilityFalseReasonEnum.LESS_THAN_TWO_PARTICIPANTS.getVisibilityFalseReasonKey());
			return false;
		}

		for (TransformedParticipant participant : transactionHistoryDetails.getParticipants()) {
			if (participant.getAmount() == null) {
				log.debug("pipeline: {}, systemId: {}, Dont have amount in participant :{} sp marking isVisible false.",
						Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId(), participant);
				this.pushReasonInContextMap(transactionHistoryDetails,
						VisibilityFalseReasonEnum.PARTICIPANT_AMOUNT_NULL.getVisibilityFalseReasonKey());
				return false;
			}
			if (participant.getPaymentSystem() == null) {
				log.debug(
						"pipeline: {}, systemId: {}, Dont have paymentSystem in participant :{} sp marking isVisible false.",
						Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId(), participant);
				this.pushReasonInContextMap(transactionHistoryDetails,
						VisibilityFalseReasonEnum.PARTICIPANT_PAYMENT_SYSTEM_NULL.getVisibilityFalseReasonKey());
				return false;
			}
		}
		// when stream is for credit and overall it is not success
		if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(transactionHistoryDetails.getTxnIndicator())
				&& !StatusEnum.SUCCESS.getStatusKey().equals(transactionHistoryDetails.getStatus())) {
			this.pushReasonInContextMap(transactionHistoryDetails,
					VisibilityFalseReasonEnum.TXN_FOR_CREDIT_AND_STATUS_NOT_SUCCESS.getVisibilityFalseReasonKey());
			return false;
		}

		// adding conditions for wallet->bank via UPI
		String txnPurpose = transactionHistoryDetails.getContextMap() == null ? null
				: transactionHistoryDetails.getContextMap().get(TRANSACTION_PURPOSE);
		if (BANK_TRANSFER_PREAPPROVED.equalsIgnoreCase(txnPurpose) && TransactionSource.UPI.getTransactionSourceKey()
			.equals(transactionHistoryDetails.getStreamSource())) {
			this.pushReasonInContextMap(transactionHistoryDetails,
					VisibilityFalseReasonEnum.WALLET_TO_BANK_VIA_UPI_EVENT.getVisibilityFalseReasonKey());
			return false;
		}
		return true;
	}

	/**
	 * Common validation for all mandate types
	 * @param transactionHistoryDetail Transaction details to validate
	 * @return true if basic validations pass, false otherwise
	 */
	private boolean isBasicMandateCriteriaValid(final TransformedTransactionHistoryDetail transactionHistoryDetail) {
		String purpose = com.org.panaroma.commons.utils.Utility.getPurpose(transactionHistoryDetail);

		// Check if purpose is blank
		if (StringUtils.isBlank(purpose)) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.TXN_PURPOSE_BLANK.getVisibilityFalseReasonKey());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.TXN_PURPOSE_BLANK.name());
			return false;
		}

		// Check if transaction is expired or declined
		if (Utility.isExpiredOrDeclinedTxn(transactionHistoryDetail)) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.TXN_DECLINED_OR_EXPIRED.getVisibilityFalseReasonKey());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.TXN_DECLINED_OR_EXPIRED.name());
			return false;
		}

		return true;
	}

	/**
	 * Validates IPO mandate transactions Rules: 1. Purpose must be present 2. For CREATE
	 * purpose with PENDING status and REQUEST_PENDING collect status - not visible 3. For
	 * REVOKE/UPDATE purpose - must have SUCCESS status 4. Expired/declined transactions
	 * are not visible 5. IPO expired transactions are not visible
	 */
	private boolean doForIpoMandate(final TransformedTransactionHistoryDetail transactionHistoryDetail) {
		if (!isBasicMandateCriteriaValid(transactionHistoryDetail)) {
			return false;
		}

		String purpose = com.org.panaroma.commons.utils.Utility.getPurpose(transactionHistoryDetail);

		// Check CREATE purpose with PENDING status
		if (PURPOSE_CREATE.equals(purpose) && PENDING.getStatusKey().equals(transactionHistoryDetail.getStatus())
				&& REQUEST_PENDING.equals(transactionHistoryDetail.getContextMap().get(COLLECT_STATUS))) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.IPO_MANDATE_PENDING_CREATE_TXN.getVisibilityFalseReasonKey());
			return false;
		}

		// Check REVOKE/UPDATE purpose status
		if ((PURPOSE_REVOKE.equals(purpose) || PURPOSE_UPDATE.equals(purpose))
				&& Boolean.FALSE.equals(SUCCESS.getStatusKey().equals(transactionHistoryDetail.getStatus()))) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.IPO_MANDATE_OTHER_THAN_SUCCESS_REVOKE_TXN.getVisibilityFalseReasonKey());
			return false;
		}

		// Check IPO specific expiration
		if (Utility.isMandateExpiredTxn(transactionHistoryDetail)) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.MANDATE_EXPIRE_EVENT.getVisibilityFalseReasonKey());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.MANDATE_EXPIRE_EVENT.name());
			return false;
		}

		return true;
	}

	/**
	 * Validates Recurring mandate transactions Rules: 1. Purpose must be present 2. Debit
	 * participant bank details must be present 3. Purpose must be COLLECT 4.
	 * Expired/declined transactions are not visible 5. RM expired transactions are not
	 * visible
	 */
	private boolean doForRecurringMandate(final TransformedTransactionHistoryDetail transactionHistoryDetail) {
		if (!isBasicMandateCriteriaValid(transactionHistoryDetail)) {
			return false;
		}

		String purpose = com.org.panaroma.commons.utils.Utility.getPurpose(transactionHistoryDetail);

		// Check debit participant bank details
		if (Boolean.FALSE.equals(isDebitParticipantBankDetailsPresent(transactionHistoryDetail))) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.DEBIT_PARTICIPANT_BANK_DATA_NOT_PRESENT.getVisibilityFalseReasonKey());
			return false;
		}

		// Check purpose is COLLECT
		if (Boolean.FALSE.equals(PURPOSE_COLLECT.equals(purpose))) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.RECURRING_MANDATE_OTHER_THAN_COLLECT.getVisibilityFalseReasonKey());
			return false;
		}
		else if (Utility.isExpiredOrDeclinedTxn(transactionHistoryDetail)) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.TXN_DECLINED_OR_EXPIRED.getVisibilityFalseReasonKey());
			return false;
		}

		return true;
	}

	/**
	 * Validates Lite TopUp mandate transactions Rules: 1. Purpose must be present 2.
	 * Purpose must be PAY
	 */
	private boolean doForLiteTopUpMandate(final TransformedTransactionHistoryDetail transactionHistoryDetail) {
		if (!isBasicMandateCriteriaValid(transactionHistoryDetail)) {
			return false;
		}

		String purpose = com.org.panaroma.commons.utils.Utility.getPurpose(transactionHistoryDetail);

		// Check purpose is PAY
		if (Boolean.FALSE.equals(PURPOSE_PAY.equals(purpose))) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.LITE_TOPUP_MANDATE_OTHER_THAN_PAY.getVisibilityFalseReasonKey());
			return false;
		}

		return true;
	}

	/**
	 * Validates SBMD mandate transactions Rules: 1. Purpose must be present 2. For REVOKE
	 * purpose - must have SUCCESS status 3. Expired/declined transactions are not visible
	 * 4. SBMD expired transactions are not visible
	 */
	private boolean doForSbmdMandate(final TransformedTransactionHistoryDetail transactionHistoryDetail) {
		if (!isBasicMandateCriteriaValid(transactionHistoryDetail)) {
			return false;
		}

		String purpose = com.org.panaroma.commons.utils.Utility.getPurpose(transactionHistoryDetail);

		// Check REVOKE purpose status
		if (PURPOSE_REVOKE.equals(purpose)
				&& Boolean.FALSE.equals(SUCCESS.getStatusKey().equals(transactionHistoryDetail.getStatus()))) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.SBMD_MANDATE_OTHER_THAN_SUCCESS_REVOKE_TXN.getVisibilityFalseReasonKey());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.SBMD_MANDATE_OTHER_THAN_SUCCESS_REVOKE_TXN.name());
			return false;
		}

		// Check SBMD specific expiration
		if (Utility.isMandateExpiredTxn(transactionHistoryDetail)) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.MANDATE_EXPIRE_EVENT.getVisibilityFalseReasonKey());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.MANDATE_EXPIRE_EVENT.name());
			return false;
		}

		return true;
	}

	/**
	 * Validates One Time mandate transactions Rules: 1. Purpose must be present 2. For
	 * REVOKE purpose - must have SUCCESS status 3. Expired/declined transactions are not
	 * visible 4. OTM expired transactions are not visible
	 */
	private boolean doForOneTimeMandate(final TransformedTransactionHistoryDetail transactionHistoryDetail) {
		if (!isBasicMandateCriteriaValid(transactionHistoryDetail)) {
			return false;
		}

		String purpose = com.org.panaroma.commons.utils.Utility.getPurpose(transactionHistoryDetail);

		// Check REVOKE purpose status
		if (PURPOSE_REVOKE.equals(purpose)
				&& Boolean.FALSE.equals(SUCCESS.getStatusKey().equals(transactionHistoryDetail.getStatus()))) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.ONE_TIME_MANDATE_OTHER_THAN_SUCCESS_REVOKE_TXN
						.getVisibilityFalseReasonKey());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.ONE_TIME_MANDATE_OTHER_THAN_SUCCESS_REVOKE_TXN.name());
			return false;
		}

		// Check OTM specific expiration
		if (Utility.isMandateExpiredTxn(transactionHistoryDetail)) {
			this.pushReasonInContextMap(transactionHistoryDetail,
					VisibilityFalseReasonEnum.MANDATE_EXPIRE_EVENT.getVisibilityFalseReasonKey());
			metricsAgent.incrementCount(REASON_FOR_NOT_VISIBLE,
					REASON + COLON + VisibilityFalseReasonEnum.MANDATE_EXPIRE_EVENT.name());
			return false;
		}

		return true;
	}

	private Boolean doForUpiLite(final TransformedTransactionHistoryDetail transactionHistoryDetails) {

		if (transactionHistoryDetails.getParticipants().size() < 1) {
			// Don't have debit legs, so simply marking it to false.
			log.debug(
					"pipeline: {}, Number of participant is less than 1. So marking visibility flag as false for systemId: {}",
					Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId());
			this.pushReasonInContextMap(transactionHistoryDetails,
					VisibilityFalseReasonEnum.LESS_THAN_ONE_PARTICIPANTS.getVisibilityFalseReasonKey());
			return false;
		}

		for (TransformedParticipant participant : transactionHistoryDetails.getParticipants()) {
			if (participant.getAmount() == null) {
				log.debug("pipeline: {}, systemId: {}, Dont have amount participant :{} sp marking isVisible false.",
						Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId(), participant);
				this.pushReasonInContextMap(transactionHistoryDetails,
						VisibilityFalseReasonEnum.PARTICIPANT_AMOUNT_NULL.getVisibilityFalseReasonKey());
				return false;
			}
			if (participant.getPaymentSystem() == null) {
				log.debug(
						"pipeline: {}, systemId: {}, Dont have paymentSystem in participant :{} sp marking isVisible false.",
						Utility.getActivePipelineName(), transactionHistoryDetails.getTxnId(), participant);
				this.pushReasonInContextMap(transactionHistoryDetails,
						VisibilityFalseReasonEnum.PARTICIPANT_PAYMENT_SYSTEM_NULL.getVisibilityFalseReasonKey());
				return false;
			}
		}
		// make event with txnType:DEACTIVATION_OF_UPI_LITE and amt = 0.
		// make this event visibility false
		if (UpiLiteUtility.isDeactivationEventWithAmtZero(transactionHistoryDetails)) {
			return false;
		}
		return true;
	}

	private boolean doForSystemDebit(final TransformedTransactionHistoryDetail txn) {

		if (!SUCCESS.getStatusKey().equals(txn.getStatus())) {
			this.pushReasonInContextMap(txn,
					VisibilityFalseReasonEnum.DEAF_TXN_NOT_SUCCESS.getVisibilityFalseReasonKey());
			return false;
		}
		else if (Objects.isNull(txn.getContextMap()) || Objects.isNull(txn.getContextMap().get("walletTxnType"))
				|| Objects.isNull(txn.getContextMap().get("walletBusinessTxnType"))) {

			this.pushReasonInContextMap(txn,
					VisibilityFalseReasonEnum.REQUIRED_IDENTIFIERS_NULL.getVisibilityFalseReasonKey());
			return false;

		}
		else if (!"118".equalsIgnoreCase(txn.getContextMap().get("walletTxnType"))
				|| !"115".equalsIgnoreCase(txn.getContextMap().get("walletBusinessTxnType"))) {

			this.pushReasonInContextMap(txn,
					VisibilityFalseReasonEnum.REQUIRED_IDENTIFIERS_INCORRECT.getVisibilityFalseReasonKey());
			return false;
		}

		return true;
	}

	@Override
	public void setVisibleCriteria(final TransformedTransactionHistoryDetail details) throws Exception {
		try {
			for (String key : keysForNullCheck) {
				Method method = details.getClass().getMethod("get" + key);
				Object value = method.invoke(details);
				if (value == null) {
					log.debug(
							"pipeline: {}, systemId: {}, value of key :{} is null so marking isVisible flag as false.",
							Utility.getActivePipelineName(), details.getTxnId(), key);
					this.pushReasonInContextMap(details,
							VisibilityFalseReasonEnum.KEY_IS_NULL.getVisibilityFalseReasonKey());
					details.setIsVisible(false);
					return;
				}
			}
		}
		catch (Exception e) {
			log.error("pipeline: {}, systemId {}, Exception : {} while setting null check.",
					Utility.getActivePipelineName(), details.getTxnId(), CommonsUtility.exceptionFormatter(e));
			throw e;
		}

		// Additional condition for PG source :- PG with not P2M Refund txnType doc won't
		// be visible if payment mode is Upi and vpa is of paytm tpap.
		if (TransactionSource.PG.getTransactionSourceKey().equals(details.getStreamSource())
				&& !TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(details.getMainTxnType())
				&& !checkIfPgTxnNeedsToBeVisibleOnTpap(details)) {
			this.pushReasonInContextMap(details,
					VisibilityFalseReasonEnum.PG_UPI_TXN_WITH_TPAP_VPA.getVisibilityFalseReasonKey());
			details.setIsVisible(false);
			return;
		}

		// If PG OMS Txn then marking its visibility as false
		if (TransactionSource.PG.getTransactionSourceKey().equals(details.getStreamSource())
				&& CartUtility.isThisOmsOrder(details)) {
			metricsAgent.incrementCount(PG_OMS_TXN_EVENT_COUNT);
			this.pushReasonInContextMap(details, VisibilityFalseReasonEnum.PG_OMS_TXN.getVisibilityFalseReasonKey());
			details.setIsVisible(false);
			return;
		}

		// adding condition for collect request.
		// when transaction is not success, visible flag will be off for both
		if (TransactionSource.UPI.getTransactionSourceKey().equals(details.getStreamSource())) {
			String upiTxnType = null;
			if (details.getContextMap() != null) {
				upiTxnType = details.getContextMap().get(TXN_TYPE);
			}
			if (COLLECT.equalsIgnoreCase(upiTxnType)
					&& Boolean.FALSE.equals(checkVisibilityForUpiCollectTxn(details))) {
				this.pushReasonInContextMap(details,
						VisibilityFalseReasonEnum.COLLECT_NOT_SUCCESS.getVisibilityFalseReasonKey());
				details.setIsVisible(false);
				return;
			}
		}

		// no mapping will be find for Wallet_settlement, so it will be visible
		if (this.getTxnTypeVisibleKeyCheckMap()
			.get(TransactionTypeEnum.getTransactionTypeEnumByKey(details.getMainTxnType())) == null) {
			details.setIsVisible(true);
		}
		else {
			boolean isVisible = this.getTxnTypeVisibleKeyCheckMap()
				.get(TransactionTypeEnum.getTransactionTypeEnumByKey(details.getMainTxnType()))
				.apply(details);
			details.setIsVisible(isVisible);
		}

		// removing reason for not isVisible is true.
		if (Boolean.TRUE.equals(details.getIsVisible())) {
			if (details.getContextMap() != null && details.getContextMap().get(REASON_FOR_NOT_VISIBLE) != null) {
				log.debug("System ID :{}. Removing reason for isVisible as this is true now.", details.getTxnId());
				details.getContextMap().remove(REASON_FOR_NOT_VISIBLE);
			}
		}

	}

	private boolean checkIfPgTxnNeedsToBeVisibleOnTpap(final TransformedTransactionHistoryDetail txn) {
		TransformedParticipant participant = com.org.panaroma.commons.utils.Utility.getSelfUpiParticipantForPg(txn);

		// participant would be null if upi payment mode is not involved
		if (Objects.isNull(participant)) {
			return true;
		}

		// Payment system is UPI then PG will not be visible
		if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
			return false;
		}
		else {
			// for other payment modes PG event should be visible
			return true;
		}
	}

	private boolean checkVisibilityOfPgIfPaymentModeIsUpi(final TransformedParticipant participant) {
		// upi data or vpa is null so making its visibility false
		if (Objects.isNull(participant.getUpiData()) || StringUtils.isBlank(participant.getUpiData().getVpa())) {
			return false;
		}
		String vpa = participant.getUpiData().getVpa();
		String[] splittedList = vpa.split("@");
		if (splittedList.length < 2) {
			return false;
		}
		String vpaHandle = splittedList[splittedList.length - 1];

		// vpa should not be paytm tpap handle
		return Boolean.FALSE
			.equals(Objects.nonNull(paytmTpapVpaHandlesList) && paytmTpapVpaHandlesList.contains(vpaHandle));
	}

	private Boolean checkVisibilityForUpiCollectTxn(final TransformedTransactionHistoryDetail tthd) {
		if (ClientStatusEnum.SUCCESS.getStatusKey().equals(tthd.getStatus())
				|| com.org.panaroma.commons.utils.Utility.isMandateTransaction(tthd)) {
			return true;
		}
		if (Objects.nonNull(tthd.getContextMap())) {
			String upiCollectStatus = tthd.getContextMap().get(COLLECT_STATUS);
			return TransactionTypeEnum.P2M.getTransactionTypeKey().equals(tthd.getMainTxnType())
					&& visibleUpiP2mCollectStates.contains(upiCollectStatus);
		}
		return false;
	}

	private void pushReasonInContextMap(final TransformedTransactionHistoryDetail details, final String reason) {
		Map<String, String> contextMap = details.getContextMap() == null ? new HashMap<>() : details.getContextMap();
		contextMap.put(REASON_FOR_NOT_VISIBLE, reason);
		details.setContextMap(contextMap);
	}

}
