package com.org.panaroma.ingester.cache;

import static com.org.panaroma.commons.constants.AerospikeConstants.CACHE_UPDATED_TIME_KEY;
import static com.org.panaroma.commons.constants.AerospikeConstants.KAFKA_PROCESSED_RECORD_SET_NAME;
import static com.org.panaroma.commons.constants.AerospikeConstants.LOCALISED_SET_NAME;
import static com.org.panaroma.commons.constants.AerospikeConstants.MANDATE_SET_NAME;
import static com.org.panaroma.commons.constants.AerospikeConstants.OLDEST_TXN_CACHE_KEY;
import static com.org.panaroma.commons.constants.AerospikeConstants.PG_P2M_RECORD_SET_NAME;
import static com.org.panaroma.commons.constants.AerospikeConstants.UNLOCALISED_SET_NAME;
import static com.org.panaroma.commons.constants.AerospikeConstants.UTH_PG_RECORD_SET_NAME;
import static com.org.panaroma.commons.constants.AerospikeConstants.UTH_RECORD_SET;
import static com.org.panaroma.commons.constants.AerospikeConstants.VALUE_STRING;
import static com.org.panaroma.commons.constants.AerospikeConstants.ZERO_DELTA_CACHE_KEY;
import static com.org.panaroma.commons.constants.CommonCacheConstants.ACTION;
import static com.org.panaroma.commons.constants.CommonCacheConstants.AEROSPIKE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.AEROSPIKE_EXCEPTION_COUNT;
import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.GET;
import static com.org.panaroma.commons.constants.CommonCacheConstants.SAVE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.SET;
import static com.org.panaroma.commons.constants.CommonCacheConstants.STATUS;
import static com.org.panaroma.commons.constants.CommonCacheConstants.TOTAL_CACHE_CALLS;
import static com.org.panaroma.commons.constants.CommonCacheConstants.TYPE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE_FAILURE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE_SUCCESS;
import static com.org.panaroma.commons.constants.CommonCacheConstants.monitoringTagFormat;
import static com.org.panaroma.ingester.constants.Constants.COLON;
import static com.org.panaroma.ingester.constants.Constants.DC_PIPELINES;
import static com.org.panaroma.ingester.constants.Constants.DELETE;
import static com.org.panaroma.ingester.constants.Constants.PPBL_SET_NAME;
import static com.org.panaroma.ingester.constants.Constants.TOKEN_CACHE_SET_NAME;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.AEROSPIKE_API_NAME;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EXCEPTION_IN_GETTING_MANDATE_DATA_FROM_AEROSPIKE;

import com.aerospike.client.AerospikeException;
import com.aerospike.client.Bin;
import com.aerospike.client.Key;
import com.aerospike.client.Record;
import com.aerospike.client.policy.CommitLevel;
import com.aerospike.client.policy.RecordExistsAction;
import com.aerospike.client.policy.WritePolicy;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.cache.CacheHelper;
import com.org.panaroma.commons.cache.dto.CacheWrapper;
import com.org.panaroma.commons.cache.dto.ListingSpecificCacheData;
import com.org.panaroma.commons.dto.MerchantData;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.cache.OldestTxnUpdateCache;
import com.org.panaroma.commons.dto.cache.ZeroDeltaCache;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.mandate.MandateBaseDto;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Log4j2
public class AerospikeCacheClient extends IcacheClient<TransformedTransactionHistoryDetail> {

	private Integer marketplaceLocaliseCacheExpiryTime;

	private Integer marketplaceUnlocaliseCacheExpiryTime;

	private Integer tokenCacheSetExpiryTime;

	private WritePolicy tokenCacheWritePolicy;

	private ObjectMapper objectMapper;

	private Integer pgCustIdsCacheTtl;

	private Integer accRefNoCacheExpiryTime;

	/*
	 * This property holds the ttl value of listing pageNo 1 cache both main passbook &
	 * upi passbook
	 */
	private Integer listingApiPageOneCacheExpiryTime;

	/*
	 * This property holds the ttl value of non transacting user cache for both main
	 * passbook & upi passbook
	 */
	private Integer nonTransactingUserCacheExpiryTime;

	private Integer zeroDeltaCacheExpiryTime;

	private Integer zeroDeltaDeletedFlagExpiryTime;

	private Integer oldestTxnCacheExpiryTime;

	private String pipelineName;

	private String cacheKeyForDcPipeline;

	@Autowired
	MetricsAgent metricsAgent;

	private Integer mandateCacheExpiryTime;

	@Autowired
	public AerospikeCacheClient(@Value("${aerospike.host-name}") final String aeroSpikeUrl,
			@Value("${aerospike.namespace}") final String aerospikeNamespace,
			@Value("${aerospike.port}") final Integer aerospikePort,
			@Value("${aerospike.writePolicyDefault.socketTimeout}") final Integer writeDefaultCacheSocketTimeout,
			@Value("${aerospike.writePolicyDefault.totalTimeout}") final Integer writeDefaultCacheTotalTimeout,
			@Value("${aerospike.writePolicyDefault.sleepBetweenRetries}") final Integer writeDefaultSleepBetweenRetries,
			@Value("${aerospike.writePolicyDefault.expiration}") final Integer writeDefaultCacheExpiryTime,
			@Value("${aerospike.readPolicyDefault.sleepBetweenRetries}") final Integer readDefaultSleepBetweenRetries,
			@Value("${aerospike.readPolicyDefault.totalTimeout}") final Integer readDefaultCacheTotalTimeout,
			@Value("${aerospike.readPolicyDefault.socketTimeout}") final Integer readDefaultCacheSocketTimeout,
			@Value("${marketplace.localise.cache.time}") final Integer marketplaceLocaliseCacheExpiryTime,
			@Value("${marketplace.unlocalise.cache.time}") final Integer marketplaceUnlocaliseCacheExpiryTime,
			@Value("${token.cache.time}") final Integer tokenCacheSetExpiryTime,
			@Value("${cache.key.prefix}") final String cacheKeyPrefix,
			@Value("${pg.custId.cache.ttl}") final Integer pgCustIdsCacheTtl,
			@Value("${account.ref.no.cache.expiryTime}") final Integer accRefNoCacheExpiryTime,
			@Value("${listingApi.cache.expiry.in.days}") final Integer listingApiPageOneCacheExpiryTimeInDays,
			@Value("${nonTransactingUser.cache.expiry.in.mins}") final Integer nonTransactingUserCacheExpiryTimeInMins,
			@Value("${updatesApi.zeroDelta.cache.expiry.seconds}") final Integer zeroDeltaCacheExpiryTime,
			@Value("${updatesApi.zeroDelta.deletedFlag.expiry.seconds}") final Integer zeroDeltaDeletedFlagExpiryTime,
			@Value("${updatesApi.oldestTxn.cache.expiry.seconds}") final Integer oldestTxnCacheExpiryTime,
			final ObjectMapper objectMapper,
			@Value("${cache.key.prefix.dc.pipelines}") final String cacheKeyForDcPipeline,
			@Value("${mandate.cache.expiry.time}") final Integer mandateCacheExpiryTime) {
		super(aeroSpikeUrl, aerospikeNamespace, aerospikePort, writeDefaultCacheSocketTimeout,
				writeDefaultCacheTotalTimeout, writeDefaultSleepBetweenRetries, writeDefaultCacheExpiryTime,
				readDefaultSleepBetweenRetries, readDefaultCacheTotalTimeout, readDefaultCacheSocketTimeout,
				cacheKeyPrefix);
		this.marketplaceLocaliseCacheExpiryTime = marketplaceLocaliseCacheExpiryTime;
		this.marketplaceUnlocaliseCacheExpiryTime = marketplaceUnlocaliseCacheExpiryTime;
		this.tokenCacheSetExpiryTime = tokenCacheSetExpiryTime;
		this.objectMapper = objectMapper;
		this.pgCustIdsCacheTtl = pgCustIdsCacheTtl;
		this.accRefNoCacheExpiryTime = accRefNoCacheExpiryTime;
		// Converting below properties in days to seconds
		this.listingApiPageOneCacheExpiryTime = listingApiPageOneCacheExpiryTimeInDays * 24 * 60 * 60;
		this.nonTransactingUserCacheExpiryTime = nonTransactingUserCacheExpiryTimeInMins * 60;
		this.zeroDeltaDeletedFlagExpiryTime = zeroDeltaDeletedFlagExpiryTime;
		this.oldestTxnCacheExpiryTime = oldestTxnCacheExpiryTime;
		this.cacheKeyForDcPipeline = cacheKeyForDcPipeline;
		this.zeroDeltaCacheExpiryTime = zeroDeltaCacheExpiryTime;
		this.mandateCacheExpiryTime = mandateCacheExpiryTime;
	}

	@Override
	public TransformedTransactionHistoryDetail getUpdatedRecord(final String key) {
		ensureInitialized();
		long startTime = System.currentTimeMillis();
		Record record = aerospikeClient.get(null, this.getKey(key, UTH_RECORD_SET),
				new TypeReference<TransformedTransactionHistoryDetail>() {
				});
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, UTH_RECORD_SET);
		TransformedTransactionHistoryDetail finalDoc = record == null ? null
				: (TransformedTransactionHistoryDetail) record.getValue("value");
		if (finalDoc != null) {
			log.warn("Record from aerospike for txnId : {} is {}", finalDoc.getTxnId(), finalDoc);
		}
		return finalDoc;
	}

	@Override
	public List<TransformedTransactionHistoryDetail> getPpblRecord(final String key) {
		ensureInitialized();
		long startTime = System.currentTimeMillis();
		Record record = aerospikeClient.get(null, this.getKey(key, PPBL_SET_NAME),
				new TypeReference<List<TransformedTransactionHistoryDetail>>() {
				});
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, PPBL_SET_NAME);
		return record == null ? null : (List<TransformedTransactionHistoryDetail>) record.getValue("value");
	}

	@Override
	public List<TransformedTransactionHistoryDetail> getUpdatedRecordList(final String key) {
		log.debug("Fetching record from cache for key :{}", key);
		if (StringUtils.isBlank(key)) {
			log.debug("Key is null so returning with null response.");
			return null;
		}
		ensureInitialized();
		long startTime = System.currentTimeMillis();
		Record record = aerospikeClient.get(null, this.getKey(key, UTH_RECORD_SET),
				new TypeReference<List<TransformedTransactionHistoryDetail>>() {
				});
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, UTH_RECORD_SET);
		return record == null ? null : (List<TransformedTransactionHistoryDetail>) record.getValue("value");
	}

	@Override
	public void saveUpdateRecordList(final List<TransformedTransactionHistoryDetail> detailList, final String key) {
		try {
			if (detailList == null || detailList.isEmpty() || StringUtils.isBlank(key)) {
				log.debug("detailList/key is null/empty for key :{}", key);
				log.debug("detailList/key is null/empty:{} for key :{}", detailList, key);
				return;
			}
			ensureInitialized();
			log.debug("Request received to put data into cache with key:{}, data:{}", key, detailList);
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(null, getKey(key, UTH_RECORD_SET), "", new Bin("value", detailList));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, UTH_RECORD_SET);
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache : {}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	@Override
	public void saveUpdatedRecord(final TransformedTransactionHistoryDetail detail, final String key) {
		try {
			if (StringUtils.isBlank(key) || detail == null) {
				log.debug("Null key/data key :{}, data:{}", key, detail);
				return;
			}
			ensureInitialized();
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(null, getKey(key, UTH_RECORD_SET), "", new Bin("value", detail));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, UTH_RECORD_SET);
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache.{}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	@Override
	public void saveUpdatedCustIdsForOrder(final List<String> custIds, final String key) {
		try {
			if (custIds == null || custIds.isEmpty() || StringUtils.isBlank(key)) {
				log.debug("detailList/key is null/empty:{} for key :{}", custIds, key);
				return;
			}
			ensureInitialized();
			log.debug("Request received to put data into cache with key:{}, data:{}", key, custIds);
			WritePolicy writePolicy = new WritePolicy();
			writePolicy.expiration = this.pgCustIdsCacheTtl;
			writePolicy.commitLevel = CommitLevel.COMMIT_ALL;
			writePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
			writePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
			writePolicy.sleepBetweenRetries = this.writeDefaultSleepBetweenRetries;
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(writePolicy, this.getKey(key, UTH_PG_RECORD_SET_NAME), "", new Bin("value", custIds));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, UTH_PG_RECORD_SET_NAME);
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache. {}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	@Override
	public List<String> getUpdatedRecords(final String key) {
		log.debug("Fetching record from cache for key :{}", key);
		if (StringUtils.isBlank(key)) {
			log.debug("Key is null so returning with null response.");
			return null;
		}
		ensureInitialized();
		long startTime = System.currentTimeMillis();
		Record record = aerospikeClient.get(null, this.getKey(key, UTH_PG_RECORD_SET_NAME),
				new TypeReference<List<String>>() {
				});
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, UTH_PG_RECORD_SET_NAME);
		return record == null ? null : (List<String>) record.getValue("value");
	}

	@Override
	public void saveLocalisedRecordsToCache(final Map<String, String> localisedMap, final String langId) {
		try {
			if (Objects.isNull(langId) || localisedMap == null) {
				log.debug("Null key/data key :{}, data:{}", langId, localisedMap);
				return;
			}
			ensureInitialized();
			WritePolicy writePolicy = new WritePolicy();
			writePolicy.expiration = this.marketplaceLocaliseCacheExpiryTime;
			writePolicy.commitLevel = CommitLevel.COMMIT_ALL;
			writePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
			writePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
			writePolicy.sleepBetweenRetries = writeDefaultSleepBetweenRetries;
			for (Map.Entry<String, String> entry : localisedMap.entrySet()) {
				String key = CacheKeyManager.getCacheKey(langId, entry.getKey());
				HashMap<String, String> localisedData = new HashMap<>();
				localisedData.put(entry.getKey(), entry.getValue());
				long startTime = System.currentTimeMillis();
				aerospikeClient.put(writePolicy, getKey(key, LOCALISED_SET_NAME), "", new Bin("value", localisedData));
				metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, LOCALISED_SET_NAME);
			}

		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache {}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}

	}

	@Override
	public void saveUnlocalisedRecordsToCache(final List<String> unLocalisedData, final String langId) {
		try {
			if (unLocalisedData == null || unLocalisedData.size() == 0) {
				log.debug("Null key/data:{}", unLocalisedData);
				return;
			}
			ensureInitialized();
			WritePolicy writePolicy = new WritePolicy();
			writePolicy.expiration = this.marketplaceUnlocaliseCacheExpiryTime;
			writePolicy.commitLevel = CommitLevel.COMMIT_ALL;
			writePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
			writePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
			writePolicy.sleepBetweenRetries = writeDefaultSleepBetweenRetries;
			for (String data : unLocalisedData) {
				String key = CacheKeyManager.getCacheKey(langId, data);
				long startTime = System.currentTimeMillis();
				aerospikeClient.put(writePolicy, getKey(key, UNLOCALISED_SET_NAME), new Bin("value", data));
				metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, UNLOCALISED_SET_NAME);
			}
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache {}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	@Override
	public void saveProcessedRecordsToCache(final String langId, final List<String> processedData) {
		try {
			if (Objects.isNull(processedData) || processedData.size() == 0) {
				log.debug("Null key/data:{}", processedData);
				return;
			}
			ensureInitialized();
			for (String data : processedData) {
				String key = CacheKeyManager.getCacheKey(langId, data);
				long startTime = System.currentTimeMillis();
				aerospikeClient.put(null, getKey(key, KAFKA_PROCESSED_RECORD_SET_NAME), new Bin("value", data));
				metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, KAFKA_PROCESSED_RECORD_SET_NAME);
			}
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache. {}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}

	}

	@Override
	public Map<String, String> getLocalisedBatchRecords(final List<String> cacheBatchKey) {
		if (Objects.isNull(cacheBatchKey)) {
			log.debug("Key is null so returning with null response.");
			return null;
		}
		Map<String, String> localisedData = new HashMap<>();
		ensureInitialized();
		Key[] keys = new Key[cacheBatchKey.size()];
		int count = 0;
		for (String data : cacheBatchKey) {
			keys[count++] = this.getKey(data, LOCALISED_SET_NAME);
		}
		long startTime = System.currentTimeMillis();
		Record[] records = aerospikeClient.get(null, keys, new TypeReference<Map<String, String>>() {
		}, "value");
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, LOCALISED_SET_NAME);
		for (Record record : records) {
			if (record != null) {
				localisedData.putAll((Map<String, String>) record.getValue("value"));
			}
		}
		return localisedData;
	}

	@Override
	public Set<String> getAlreadyConsumedBatchRecordsFromCache(final List<String> cacheBatchKey) {
		if (Objects.isNull(cacheBatchKey)) {
			log.debug("Key is null so returning with null response.");
			return null;
		}
		ensureInitialized();
		Set<String> kafkaProcessedData = new HashSet<>();
		Key[] keys = new Key[cacheBatchKey.size()];
		int count = 0;
		for (String data : cacheBatchKey) {
			keys[count++] = this.getKey(data, KAFKA_PROCESSED_RECORD_SET_NAME);
		}
		long startTime = System.currentTimeMillis();
		Record[] records = aerospikeClient.get(null, keys, "value");
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, KAFKA_PROCESSED_RECORD_SET_NAME);
		for (Record record : records) {
			if (record != null) {
				kafkaProcessedData.add((String) record.getValue("value"));
			}
		}
		return kafkaProcessedData;
	}

	@Override
	public Set<String> getUnlocalisedRecordsfromCache(final List<String> key) {
		if (Objects.isNull(key)) {
			log.debug("Key is null");
			return null;
		}
		log.debug("Fetching unlocalised records from cache for langId :{}", key);
		Set<String> unlocalisedRecords = new HashSet<>();
		Key[] keys = new Key[key.size()];
		int count = 0;
		for (String data : key) {
			keys[count++] = getKey(data, UNLOCALISED_SET_NAME);
		}
		long startTime = System.currentTimeMillis();
		Record[] records = aerospikeClient.get(null, keys, "value");
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, UNLOCALISED_SET_NAME);
		for (Record record : records) {
			if (record != null) {
				unlocalisedRecords.add((String) record.getValue("value"));
			}

		}
		return unlocalisedRecords;

	}

	@Override
	public MerchantData getUpdatedMerchantData(final String key) {
		log.debug("Fetching record from cache for key :{}", key);
		if (StringUtils.isBlank(key)) {
			log.debug("Key is null so returning with null merchant data response.");
			return null;
		}
		ensureInitialized();
		long startTime = System.currentTimeMillis();
		Record record = aerospikeClient.get(null, this.getKey(key, PG_P2M_RECORD_SET_NAME),
				new TypeReference<MerchantData>() {
				});
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, PG_P2M_RECORD_SET_NAME);
		return record == null ? null : (MerchantData) record.getValue("value");
	}

	public MandateBaseDto getStoredMandateData(final String key, final TypeReference type) {
		if (StringUtils.isBlank(key)) {
			log.info("Key is null so returning null mandate data.");
			return null;
		}
		log.info("Fetching mandate data from cache for key : {}", key);
		try {
			long startTime = System.currentTimeMillis();

			ensureInitialized();
			Record record = aerospikeClient.get(null, this.getKey(key, MANDATE_SET_NAME), type);

			metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, MANDATE_SET_NAME);

			return record == null ? null : (MandateBaseDto) record.getValue("value");
		}
		catch (Exception exception) {
			log.error("Some exception occurred while getting mandate data from cache. Exception : {}",
					CommonsUtility.exceptionFormatter(exception));
			metricsAgent.incrementCount(EXCEPTION_IN_GETTING_MANDATE_DATA_FROM_AEROSPIKE);
			throw exception;
		}
	}

	public void saveMandateData(final MandateBaseDto mandateBaseDto, final String key) {
		try {
			if (StringUtils.isBlank(key) || Objects.isNull(mandateBaseDto)) {
				log.info("Null key/mandateData. key : {}, data : {}", key, mandateBaseDto);
				return;
			}
			ensureInitialized();
			WritePolicy writePolicy = new WritePolicy();
			writePolicy.expiration = this.mandateCacheExpiryTime;
			writePolicy.commitLevel = CommitLevel.COMMIT_ALL;
			writePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
			writePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
			writePolicy.sleepBetweenRetries = writeDefaultSleepBetweenRetries;

			long startTime = System.currentTimeMillis();

			aerospikeClient.put(writePolicy, getKey(key, MANDATE_SET_NAME), "", new Bin("value", mandateBaseDto));

			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, MANDATE_SET_NAME);
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache.{}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	@Override
	public void saveUpdateMerchantData(final MerchantData merchantData, final String key) {
		try {
			ensureInitialized();
			if (merchantData == null) {
				log.info("No need to push into cache as merchant data is null for key :{}", key);
				return;
			}
			log.debug("Request received to put data into cache with key:{}, data:{}", key, merchantData);
			WritePolicy writePolicy = new WritePolicy();
			writePolicy.expiration = this.writeDefaultCacheExpiryTime;
			writePolicy.commitLevel = CommitLevel.COMMIT_ALL;
			writePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
			writePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
			writePolicy.sleepBetweenRetries = this.writeDefaultSleepBetweenRetries;
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(writePolicy, this.getKey(key, PG_P2M_RECORD_SET_NAME), "",
					new Bin("value", merchantData));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, PG_P2M_RECORD_SET_NAME);
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache. {}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	@Override
	public void saveUpdateToken(final String tokenCacheKey, final String tokenValue) {
		try {
			this.ensureInitializedForTokenCache();
			ensureInitialized();
			if (StringUtils.isEmpty(tokenCacheKey) || StringUtils.isEmpty(tokenValue)) {
				log.info("No need to push into cache as key/value is null for key :{}", tokenCacheKey);
				return;
			}
			log.debug("Request received to put data into cache with key:{}, data:{}", tokenCacheKey, tokenValue);
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(tokenCacheWritePolicy, this.getKey(tokenCacheKey, TOKEN_CACHE_SET_NAME),
					new Bin("value", tokenValue));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, TOKEN_CACHE_SET_NAME);
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache. {}", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	private void ensureInitializedForTokenCache() {
		ensureInitialized();
		if (tokenCacheWritePolicy != null) {
			return;
		}
		synchronized (this) {
			WritePolicy tokenCacheWritePolicy = new WritePolicy();
			tokenCacheWritePolicy.expiration = this.tokenCacheSetExpiryTime;
			tokenCacheWritePolicy.commitLevel = CommitLevel.COMMIT_ALL;
			tokenCacheWritePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
			tokenCacheWritePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
			tokenCacheWritePolicy.sleepBetweenRetries = this.writeDefaultSleepBetweenRetries;
			this.tokenCacheWritePolicy = tokenCacheWritePolicy;
		}
	}

	@Override
	public String getToken(final String tokenCacheKey) {
		log.debug("Fetching record from cache for key :{}", tokenCacheKey);
		if (StringUtils.isBlank(tokenCacheKey)) {
			log.debug("Key is null so returning with null.");
			return null;
		}
		ensureInitialized();
		long startTime = System.currentTimeMillis();
		Record record = aerospikeClient.get(null, this.getKey(tokenCacheKey, TOKEN_CACHE_SET_NAME));
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, TOKEN_CACHE_SET_NAME);
		return record == null ? null : (String) record.getValue("value");
	}

	private Key getKey(final String key, final String setName) throws AerospikeException {
		try {
			ensureInitialized();
			if (DC_PIPELINES.contains(pipelineName)) {
				log.warn("Setting cache key for dc pipelines, key : {}", this.cacheKeyForDcPipeline + key);
				return new Key(aerospikeNamespace, setName, this.cacheKeyForDcPipeline + key);
			}
			log.warn("Setting cache key for non dc pipelines, key : {}", this.cacheKeyPrefix + key);
			return new Key(aerospikeNamespace, setName, this.cacheKeyPrefix + key);
		}
		catch (AerospikeException ex) {
			log.error("Error initializing key for {} aerospike set with Exception : {}", setName,
					CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	private Key getKeyWithOutPrefix(final String key, final String setName) throws AerospikeException {
		try {
			ensureInitialized();
			return new Key(aerospikeNamespace, setName, key);
		}
		catch (AerospikeException ex) {
			log.error("Error initializing key for {} aerospike set with Exception : {}", setName,
					CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	public void saveRecordInPpblCache(final List<TransformedTransactionHistoryDetail> ppblDocs, final String key,
			final String setName) {
		try {
			if (StringUtils.isBlank(key) || Objects.isNull(ppblDocs) || StringUtils.isBlank(setName)) {
				log.debug("Null key/data/setName");
				return;
			}
			ensureInitialized();
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(null, getKey(key, setName), "", new Bin("value", ppblDocs));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, setName);
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting to Cache {}.", CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	public Record getRecord(final String key, final String setName) {
		ensureInitialized();
		long startTime = System.currentTimeMillis();
		Record record = aerospikeClient.get(null, this.getKey(key, setName));
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, setName);
		return record;
	}

	public Record getRecord(final String key, final String setName, final TypeReference type) {
		ensureInitialized();
		long startTime = System.currentTimeMillis();
		Record record = aerospikeClient.get(null, this.getKey(key, setName), type);
		metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, setName);
		return record;
	}

	@Override
	public Map<String, UserDetails> getUserUrlfromCache(final List<String> key) {
		if (Objects.isNull(key)) {
			log.debug("Key is null");
			return null;
		}
		String userId = getStringFromList(key);
		log.info("Fetching userImage records from cache for userId :{}", userId);
		Map<String, UserDetails> userIdUserImageMap = new HashMap<>();
		try {
			ensureInitialized();
			Key[] keys = new Key[key.size()];
			int count = 0;
			for (String data : key) {
				// cache for user image will be same for both the pipelines. So not adding
				// prefix in this case.
				keys[count++] = new Key(aerospikeNamespace, "userDetailsSet", data);
			}
			long startTime = System.currentTimeMillis();
			Record[] records = aerospikeClient.get(null, keys, new TypeReference<Map<String, UserDetails>>() {
			}, "value");
			metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, "userDetailsSet");
			for (Record record : records) {
				if (record != null) {
					userIdUserImageMap.putAll(objectMapper.convertValue(record.getMap("value"),
							new TypeReference<HashMap<String, UserDetails>>() {
							}));
				}
			}
			if (!userIdUserImageMap.isEmpty()) {
				log.info("Fetched user Image records from cache for userId:{} with response", userId);
			}
			else {
				log.info("User Image records not present in cache for userId:{} with response", userId);
			}
		}
		catch (Exception ex) {
			log.error("Exception occurred while reading userImageUrlData from Cache{}",
					CommonsUtility.exceptionFormatter(ex));
		}
		return userIdUserImageMap;
	}

	@Override
	public void saveUserImageUrlRecordsToCache(final Map<String, UserDetails> userImageUrlData, final Integer ttl) {
		try {
			if (userImageUrlData == null || userImageUrlData.size() == 0) {
				log.debug("userImageUrlData Null key/data:{}", userImageUrlData);
				return;
			}
			ensureInitialized();
			WritePolicy writePolicy = getWritePolicy(ttl);
			for (Entry<String, UserDetails> data : userImageUrlData.entrySet()) {
				HashMap<String, UserDetails> map = new HashMap<>();
				map.put(data.getKey(), data.getValue());
				// cache for user image will be same for both the pipelines. So not adding
				// prefix in this case.
				long startTime = System.currentTimeMillis();
				aerospikeClient.put(writePolicy, new Key(aerospikeNamespace, "userDetailsSet", data.getKey()), "",
						new Bin("value", map));
				metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, "userDetailsSet");
				log.warn("putting userImageUrlRecords to Cache for userid: {}", data.getKey());
			}
		}
		catch (Exception ex) {
			log.error("Exception occurred in putting userImageUrlData to Cache{}",
					CommonsUtility.exceptionFormatter(ex));
		}
	}

	private WritePolicy getWritePolicy(final int i) {
		WritePolicy writePolicy = new WritePolicy();
		writePolicy.expiration = i;
		writePolicy.commitLevel = CommitLevel.COMMIT_ALL;
		writePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
		writePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
		writePolicy.sleepBetweenRetries = writeDefaultSleepBetweenRetries;
		return writePolicy;
	}

	private String getStringFromList(final List<String> message) {
		if (message == null) {
			return null;
		}
		StringBuilder sb = new StringBuilder();
		for (String userId : message) {
			sb.append(", ");
			sb.append(userId);
		}
		String s = sb.toString();
		return s.replaceFirst(", ", "");
	}

	@Override
	public void saveAccRefNumInCache(final String accNo, final String ifsc, final String accRefNo) {
		if (StringUtils.isBlank(ifsc) || StringUtils.isBlank(accNo) || StringUtils.isBlank(accRefNo)) {
			log.warn("accNo/accRefNum/Ifsc is null ifsc: {}, accRefNo: {}", ifsc, accRefNo);
			return;
		}
		try {
			ensureInitialized();
			WritePolicy writePolicy = getWritePolicy(accRefNoCacheExpiryTime);
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(writePolicy, getKey(getKeyForAccRefNum(accNo, ifsc), "accRefNumDetailsSet"),
					new Bin("value", accRefNo));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, "accRefNumDetailsSet");
		}
		catch (AerospikeException e) {
			log.error("Exception putting AccRefNum in Cache ifsc: {}, accRefNo: {}, exception: {}", ifsc, accRefNo,
					CommonsUtility.exceptionFormatter(e));
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, AEROSPIKE_API_NAME + "saveAccRefNumInCache");
		}
	}

	private String getKeyForAccRefNum(final String accNo, final String ifsc) {
		return accNo + "_" + ifsc;
	}

	@Override
	public String getAccRefNumFromCache(final String accNo, final String ifsc) {
		if (StringUtils.isBlank(ifsc) || StringUtils.isBlank(accNo)) {
			log.warn("accNo/Ifsc is null ifsc: {}", ifsc);
		}
		try {
			ensureInitialized();
			String cacheKey = getKeyForAccRefNum(accNo, ifsc);
			Record record = getRecord(cacheKey, "accRefNumDetailsSet");
			String accRefNum = record == null ? null : (String) record.getValue("value");
			return accRefNum;
		}
		catch (AerospikeException e) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, AEROSPIKE_API_NAME + "getAccRefNumFromCache");
			log.error("Exception Getting AccRefNum from Cache ifsc: {}, exception: {}", ifsc,
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public void saveNtuCacheData(final String key, final ListingSpecificCacheData cacheData, final CacheInfo cacheInfo)
			throws Exception {
		String cacheName = cacheInfo.getCacheName();
		try {
			ensureInitialized();
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, CACHE + COLON + cacheName, TYPE + COLON + SAVE);
			WritePolicy writePolicy = getWritePolicy(getExpiryTimeForListingCacheData(cacheData));
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(writePolicy, this.getKey(key, cacheInfo.getCacheSetName()), "",
					new Bin(VALUE_STRING, cacheData.getIsNonTransactingUser().toString()));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, cacheInfo.getCacheSetName());
		}
		catch (Exception e) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName, TYPE + COLON + SAVE);
			log.error(cacheName + " : Some exception while saving data to cache for cache key {}. Exception : {}", key,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	private Integer getExpiryTimeForListingCacheData(final ListingSpecificCacheData cacheData) {
		if (Objects.nonNull(cacheData) && Objects.nonNull(cacheData.getListingData()) && !CollectionUtils
			.isEmpty(cacheData.getListingData().getTransformedTransactionHistoryDetailsIterable())) {
			return listingApiPageOneCacheExpiryTime;
		}
		return nonTransactingUserCacheExpiryTime;
	}

	public ListingSpecificCacheData getListingCacheData(final String key, final CacheInfo cacheInfo) throws Exception {
		String cacheName = cacheInfo.getCacheName();
		try {
			ensureInitialized();
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, CACHE + COLON + cacheName, TYPE + COLON + GET);
			long startTime = System.currentTimeMillis();
			Record record = aerospikeClient.get(null, this.getKey(key, cacheInfo.getCacheSetName()),
					new TypeReference<CacheWrapper>() {
					});
			metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, cacheInfo.getCacheSetName());
			return record == null ? null : new CacheHelper(objectMapper).getUnWrappedData(record.getValue(VALUE_STRING),
					ListingSpecificCacheData.class);
		}
		catch (Exception e) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName, TYPE + COLON + GET);
			log.error(cacheName + " : Some exception while getting data from cache for cache key {}. Exception : {}",
					key, CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	@Override
	public Record getRecordFromCache(final String key, final String set, final TypeReference type) throws Exception {
		if (StringUtils.isBlank(key) || StringUtils.isBlank(set)) {
			log.warn("Key or Set is blank to get Record Set: {}, Key: {}", set, key);
			throw new Exception(String.format("Key or Set is blank to get Record Set: %s, Key: %s", set, key));
		}
		try {
			ensureInitialized();
			long startTime = System.currentTimeMillis();
			Record record = aerospikeClient.get(null, this.getKey(key, set), type);
			metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, set);
			return record;
		}
		catch (AerospikeException e) {
			log.error("Exception while getting Record from Set: {}, with Key: {}, Exception: {}", set, key,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	// This method saves data in cache and also sets cache ttl.
	@Override
	public void saveRecordInCache(final String key, final String set, final Integer expiryTime, final Object record)
			throws Exception {
		if (StringUtils.isBlank(key) || Objects.isNull(record) || StringUtils.isBlank(set)
				|| Objects.isNull(expiryTime)) {
			log.warn("Key: {} or Data: {} or Set: {} or expiryTime: {} to be saved is null.", key, record, set,
					expiryTime);
			throw new Exception(String.format("Key: %s or Data: %s or Set: %s or expiryTime: %s to be saved is null.",
					key, record, set, expiryTime));
		}
		long startTime = System.currentTimeMillis();
		try {
			ensureInitialized();
			WritePolicy writePolicy = getWritePolicy(expiryTime);
			aerospikeClient.put(writePolicy, getKey(key, set), "", new Bin(VALUE_STRING, record));
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, set);
			metricsAgent.incrementCount(AEROSPIKE, SET + COLON + set, STATUS + COLON + WRITE_SUCCESS);
		}
		catch (Exception ex) {
			log.error(
					"Exception occurred while saving record Key: {}, Data: {},  Set: {}, expiryTime: {}, Exception: {}",
					key, record, set, expiryTime, CommonsUtility.exceptionFormatter(ex));
			metricsAgent.incrementCount(AEROSPIKE, SET + COLON + set, STATUS + COLON + WRITE_FAILURE);
			throw ex;
		}
		finally {
			metricsAgent.recordExecutionTime(AEROSPIKE, System.currentTimeMillis() - startTime, SET + COLON + set,
					ACTION + COLON + WRITE);
		}
	}

	@Override
	public void evict(final String key, final String set) {
		try {
			ensureInitialized();
			long startTime = System.currentTimeMillis();
			aerospikeClient.delete((WritePolicy) null, getKeyWithOutPrefix(key, set));
			long endTime = System.currentTimeMillis();
			long latency = endTime - startTime;
			metricsAgent.recordDeleteCountAndLatencyForFlinkAero(startTime, set);
		}
		catch (Exception ex) {
			log.error("Exception occurred while delete cache Key: {}, Set: {}, Exception: {}", key, set,
					CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	public AppSideCacheData getAppSideCacheData(@NonNull final String key) {
		CacheInfo cacheInfo = CacheInfo.UTH_APP_SIDE_CACHE_DATA;
		String cacheName = cacheInfo.getCacheName();
		try {
			ensureInitialized();
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, String.format(monitoringTagFormat, CACHE, cacheName),
					String.format(monitoringTagFormat, TYPE, GET));
			Key cacheKey = getKey(key, cacheInfo.getCacheSetName());
			long startTime = System.currentTimeMillis();
			Record record = aerospikeClient.get(null, cacheKey);
			metricsAgent.recordReadCountAndLatencyForFlinkAero(startTime, cacheInfo.getCacheSetName());
			log.info(cacheName + "Data fetched from cache for cache key {} is {}", key, record);
			if (record == null) {
				log.info("No cache found for key : {}", key);
				return null;
			}
			long zeroDeltaTimeStamp = record.getLong(ZERO_DELTA_CACHE_KEY);
			long oldestTxnDateTimeStamp = record.getLong(OLDEST_TXN_CACHE_KEY);
			long cacheUpdatedDate = record.getLong(CACHE_UPDATED_TIME_KEY);
			AppSideCacheData cacheData = new AppSideCacheData();
			if (zeroDeltaTimeStamp != 0) { // value was actually non-null in cache
				cacheData.setZeroDeltaCacheData(ZeroDeltaCache.builder().fromUpdatedDate(zeroDeltaTimeStamp).build());
			}
			if (oldestTxnDateTimeStamp != 0) {
				cacheData.setOldestTxnUpdateCacheData(
						OldestTxnUpdateCache.builder().txnDate(oldestTxnDateTimeStamp).build());
			}
			if (cacheUpdatedDate != 0) {
				cacheData.setUpdatedOn(cacheUpdatedDate);
			}
			return cacheData;
		}
		catch (AerospikeException e) {
			if (e.toString().contains("Parameter error")) {
				log.error("Invalid parameters for save for key : {}", key);
			}
		}
		catch (Exception e) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName, TYPE + COLON + GET);
			log.error(cacheName + " : Some exception while saving data to cache for cache key {}. Exception : {}", key,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
		return null;
	}

	public void saveZeroDeltaCache(@NonNull final String key, final long timeInEpochMilli) {
		CacheInfo cacheInfo = CacheInfo.UTH_APP_SIDE_CACHE_DATA;
		String cacheName = cacheInfo.getCacheName();
		try {
			ensureInitialized();
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, String.format(monitoringTagFormat, CACHE, cacheName),
					String.format(monitoringTagFormat, TYPE, SAVE));
			int expiryTime;
			expiryTime = zeroDeltaCacheExpiryTime;
			WritePolicy writePolicy = getWritePolicy(expiryTime);
			Key cacheKey = getKey(key, cacheInfo.getCacheSetName());
			Bin timestamp = new Bin(ZERO_DELTA_CACHE_KEY, timeInEpochMilli);
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(writePolicy, cacheKey, timestamp);
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, cacheInfo.getCacheSetName());
			log.warn("Saved in {}, key :: {} , value :: {}", cacheInfo, cacheKey, timestamp);
		}
		catch (AerospikeException e) {
			if (e.toString().contains("Parameter error")) {
				log.error("Invalid parameters for save for key : {}", key);
			}
		}
		catch (Exception e) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName, TYPE + COLON + SAVE);
			log.error(cacheName + " : Some exception while saving data to cache for cache key {}. Exception : {}", key,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	/**
	 * saves txnTimeStamp in the bin "oldestTxnForDeltaTimestamp". Any older bins present
	 * under the same key gets deleted.
	 * @param key key for cache
	 * @param timeInEpochMilli timestamp to be saved
	 */
	public void saveOldestTxnForDeltaCache(@NonNull final String key, final long timeInEpochMilli,
			final long updatedTime) {
		CacheInfo cacheInfo = CacheInfo.UTH_APP_SIDE_CACHE_DATA;
		String cacheName = cacheInfo.getCacheName();
		try {
			ensureInitialized();
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, String.format(monitoringTagFormat, CACHE, cacheName),
					String.format(monitoringTagFormat, TYPE, SAVE));
			int expiryTime;
			expiryTime = oldestTxnCacheExpiryTime;
			WritePolicy writePolicy = getWritePolicy(expiryTime);
			writePolicy.recordExistsAction = RecordExistsAction.REPLACE;
			Key cacheKey = getKey(key, cacheInfo.getCacheSetName());
			Bin timestamp = new Bin(OLDEST_TXN_CACHE_KEY, timeInEpochMilli);
			Bin updatedOn = new Bin(CACHE_UPDATED_TIME_KEY, updatedTime);
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(writePolicy, cacheKey, timestamp, updatedOn);
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, cacheInfo.getCacheSetName());
			log.warn("Saved in {}, key :: {} , value :: {}, {}", cacheInfo, cacheKey, timestamp, updatedOn);
		}
		catch (AerospikeException e) {
			if (e.toString().contains("Parameter error")) {
				log.error("Invalid parameters for save for key : {}", key);
			}
		}
		catch (Exception e) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName, TYPE + COLON + SAVE);
			log.error(cacheName + " : Some exception while saving data to cache for cache key {}. Exception : {}", key,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	/**
	 * Deletes data for zeroDeltaCache by overwriting current data / creating a new data
	 * with a delete flag with short TTL. If there was no data to beging with, still flag
	 * will be saved. Lets any future updates know that there was a recent delete, if
	 * updates arrives within the TTL.
	 * @param key key for which data is to be deleted
	 */
	public void deleteZeroDeltaCache(@NonNull final String key) {
		CacheInfo cacheInfo = CacheInfo.UTH_APP_SIDE_CACHE_DATA;
		String cacheName = cacheInfo.getCacheName();
		try {
			ensureInitialized();
			int expiryTime;
			expiryTime = zeroDeltaDeletedFlagExpiryTime;
			WritePolicy writePolicy = getWritePolicy(expiryTime);
			Key cacheKey = getKey(key, cacheInfo.getCacheSetName());
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, String.format(monitoringTagFormat, CACHE, cacheName),
					String.format(monitoringTagFormat, TYPE, DELETE));
			Bin timestamp = new Bin(ZERO_DELTA_CACHE_KEY, -1);
			long startTime = System.currentTimeMillis();
			aerospikeClient.put(writePolicy, cacheKey, timestamp);
			metricsAgent.recordWriteCountAndLatencyForFlinkAero(startTime, cacheInfo.getCacheSetName());
			log.warn("Saved delete flag in {}, key :: {} , value :: {}", cacheInfo, cacheKey, timestamp);
		}
		catch (Exception e) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName, TYPE + COLON + SAVE);
			log.error(cacheName + " : Some exception while saving data to cache for cache key {}. Exception : {}", key,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	/**
	 * deletes stored cache data for appSideCache.
	 * @param key key for which data is to be deleted
	 */
	public void deleteAppSideCache(@NonNull final String key) {
		CacheInfo cacheInfo = CacheInfo.UTH_APP_SIDE_CACHE_DATA;
		String cacheName = cacheInfo.getCacheName();
		try {
			ensureInitialized();
			Key cacheKey = getKey(key, cacheInfo.getCacheSetName());
			long startTime = System.currentTimeMillis();
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, String.format(monitoringTagFormat, CACHE, cacheName),
					String.format(monitoringTagFormat, TYPE, DELETE));
			aerospikeClient.delete(null, cacheKey);
			long endTime = System.currentTimeMillis();
			long latency = endTime - startTime;
			metricsAgent.recordDeleteCountAndLatencyForFlinkAero(startTime, cacheInfo.getCacheSetName());
			log.warn("Deleted in {}, key :: {}", cacheInfo, cacheKey);
		}
		catch (Exception e) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName, TYPE + COLON + SAVE);
			log.error(cacheName + " : Some exception while saving data to cache for cache key {}. Exception : {}", key,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	public void setPipelineName(final String pipelineName) {
		this.pipelineName = pipelineName;
	}

}
