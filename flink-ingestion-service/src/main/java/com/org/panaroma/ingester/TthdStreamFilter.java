package com.org.panaroma.ingester;

import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.CommonConstants.IS_BACKFILLING_FOR_PTH_DB;
import static com.org.panaroma.ingester.constants.Constants.COLON;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.PUSHED_TO_FILTER_EVENT_PIPELINE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.SOURCE;

import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import com.org.panaroma.commons.utility.configurablePropertyUtility.BopConfigurablePropertiesHolder;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.ingester.enums.SearchAbleIdentifersEnum;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.utils.FilterEventUtility;
import com.org.panaroma.ingester.utils.Utility;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import lombok.extern.log4j.Log4j2;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class TthdStreamFilter extends
		BroadcastProcessFunction<TransformedTransactionHistoryDetail, Map<String, Object>, TransformedTransactionHistoryDetail>
		implements FlatMapFunction<TransformedTransactionHistoryDetail, TransformedTransactionHistoryDetail>,
		Serializable {

	private final Long currentDateAndTxnDateDiffLimit;

	private final MetricsAgent metricsAgent;

	private final FilterEventUtility filterEventUtility;

	private final BopConfigurablePropertiesHolder configurablePropertiesHolder;

	private final RolloutStrategyHelper rolloutStrategyHelper;

	@Autowired
	public TthdStreamFilter(final MetricsAgent metricsAgent,
			@Value("${txn.event.max.days.diff.from.current.to.process.txn}") final Long currentDateAndTxnDateDiffLimit,
			final FilterEventUtility filterEventUtility,
			final BopConfigurablePropertiesHolder configurablePropertiesHolder,
			final RolloutStrategyHelper rolloutStrategyHelper) {
		this.currentDateAndTxnDateDiffLimit = currentDateAndTxnDateDiffLimit;
		this.metricsAgent = metricsAgent;
		this.filterEventUtility = filterEventUtility;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
	}

	@Override
	public void flatMap(final TransformedTransactionHistoryDetail tthd,
			final Collector<TransformedTransactionHistoryDetail> collector) throws Exception {
		try {
			if (tthd == null) {
				return;
			}

			if (Objects.nonNull(tthd.getSearchAbleIdentifiers()) && tthd.getSearchAbleIdentifiers()
				.contains(SearchAbleIdentifersEnum.UPDATED_VIA_UPDATE_TXN_STATUS_API.getSearchAbleIdentifierKey())) {
				collector.collect(tthd);
				return;
			}
			if (TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey().equals(tthd.getTxnType())
					&& Objects.nonNull(tthd.getContextMap())
					&& tthd.getContextMap().containsKey(IS_BACKFILLING_FOR_PTH_DB)) {
				tthd.getContextMap().remove(IS_BACKFILLING_FOR_PTH_DB);
				tthd.getContextMap()
					.put(BACKFILLING_IDENTIFIER,
							BackFillingIdentifierEnum.IPO_MANDATE_TXNS_BACKFILLING.getBackFillingIdentifierKey());
				collector.collect(tthd);
				return;
			}

			// log.warn(">>>>>>> rolling out strategy config map : {}",
			// rolloutStrategyHelper.getRolloutStrategyConfigMap());
			if (rolloutStrategyHelper.isUserWhiteListed("thdStreamFilterLog", tthd.getEntityId())) {
				log.warn("Testing event for too Old txn filter: {}", tthd);
			}
			if (Utility.isEventOlderThanLimit(tthd.getTxnDate(), currentDateAndTxnDateDiffLimit)
					&& com.org.panaroma.commons.utils.Utility.isSkipOldestEvent(tthd)) {
				log.warn("Event received in StreamFilter Tthd : {}, systemId : {}, TimeDiff : {} days", tthd,
						tthd.getTxnId(), (new Date().getTime() - tthd.getTxnDate()) / (1000 * 60 * 60 * 24));
				String tags = SOURCE + COLON + tthd.getStreamSource();
				metricsAgent.incrementCount(PUSHED_TO_FILTER_EVENT_PIPELINE, tags);
				return;
			}
			if (filterEventUtility.isBlackListedEvent(tthd)) {
				return;
			}
			collector.collect(tthd);
		}
		catch (Exception ex) {
			log.warn("Exception in filtering data for systemId in tthdFilter : {}, with Exception : {}",
					tthd.getTxnId(), ex);
			collector.collect(tthd);
		}
	}

	@Override
	public void processElement(final TransformedTransactionHistoryDetail value,
			final BroadcastProcessFunction<TransformedTransactionHistoryDetail, Map<String, Object>, TransformedTransactionHistoryDetail>.ReadOnlyContext ctx,
			final Collector<TransformedTransactionHistoryDetail> out) throws Exception {
		flatMap(value, out);
	}

	@Override
	public void processBroadcastElement(final Map<String, Object> value,
			final BroadcastProcessFunction<TransformedTransactionHistoryDetail, Map<String, Object>, TransformedTransactionHistoryDetail>.Context ctx,
			final Collector<TransformedTransactionHistoryDetail> out) throws Exception {
		log.warn("Received broadcast at : {}", this);
		this.configurablePropertiesHolder.addProperties(value);
	}

}
