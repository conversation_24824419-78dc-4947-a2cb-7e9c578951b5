package com.org.panaroma.ingester.enums;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SearchAbleIdentifersEnum {

	PARTICIPANT_NOT_AVAILABLE("1", "participantNotAvailable", "Participant are not available in upi events"),
	UPDATED_VIA_UPDATE_TXN_STATUS_API("2", "updatedViaUpdateTxnStatusApi",
			"Status of the txn is updated via update-txn-status api"),
	SEARCH_FIELD_EMPTY("3", "searchFieldEmpty", "Search field is empty"),
	FWD_TXN_DATE_OLDER_THAN_LIMIT("4", "fwdTxnDateOlderThanLimit", "Fwd Txn is older than limit"),
	FWD_TXN_NOT_FOUND_USING_TXN_ID("5", "fwdTxnNotFoundUsingTxnId", "Fwd Txn is not found is using txn id"),
	FWD_TXN_SOURCE_OR_TXN_ID_IS_MISSING("6", "fwdTxnSourceOrTxnIdIsMissing", "Fwd Txn Source or Txn Id is missing"),
	ERROR_WHILE_GETTING_FWD_TXN("7", "errorWhileGettingFwdTxn", "Exception while getting fwd txn for refund txn");

	private String searchAbleIdentifierKey;

	private String searchAbleIdentifier;

	private String searchAbleIdentifierDescription;

	private static Map<String, SearchAbleIdentifersEnum> searchAbleIdentifersEnumMap = null;

	static {
		searchAbleIdentifersEnumMap = new HashMap<>();
		for (SearchAbleIdentifersEnum searchAbleIdentifersEnum : SearchAbleIdentifersEnum.values()) {
			searchAbleIdentifersEnumMap.put(searchAbleIdentifersEnum.searchAbleIdentifierKey, searchAbleIdentifersEnum);
		}
	}

	public static SearchAbleIdentifersEnum getSearchAbleIdentifiersEnumByKey(final String searchAbleIdentifierKey) {
		return searchAbleIdentifersEnumMap.get(searchAbleIdentifierKey);
	}

}
