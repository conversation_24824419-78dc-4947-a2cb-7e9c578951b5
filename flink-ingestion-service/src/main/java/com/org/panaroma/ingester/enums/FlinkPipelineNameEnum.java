package com.org.panaroma.ingester.enums;

import com.org.panaroma.commons.dto.MandateBackFillingKafkaObject;
import com.org.panaroma.commons.dto.PostStitchData;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.analytics.UserIdFetcherDto;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.mandate.MandateBaseDto;
import com.org.panaroma.commons.dto.spendDtos.kafka.UserMonthAggKafkaConfig;
import com.org.panaroma.commons.dto.spendDtos.kafka.UserSpendKafkaConfig;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.ingester.constants.PipelineConstants;
import com.org.panaroma.ingester.cst.dto.GenericKafkaDto;
import com.org.panaroma.ingester.dto.BaseRetryDto;
import com.org.panaroma.ingester.dto.OmsDto;
import com.org.panaroma.ingester.dto.OmsRefundDto;
import com.org.panaroma.ingester.dto.UpiReconDto;
import com.org.panaroma.ingester.dto.VanDto;
import com.org.panaroma.ingester.dto.insertUpdateExecutorDtos.GenericUpdateInsertDto;
import com.org.panaroma.ingester.dto.streamDTO.PromoServiceDto;
import com.org.panaroma.ingester.dto.streamDTO.PromoUthDto;
import lombok.Getter;

@Getter
public enum FlinkPipelineNameEnum {

	PG("pg", TransactionHistoryDetails.class), PPBL_PG("ppbl-pg", TransactionHistoryDetails.class),
	WALLET("wallet", TransactionHistoryDetails.class), UPI("upi", TransactionHistoryDetails.class),
	UPI_V2(PipelineConstants.UPI_V2, TransactionHistoryDetails.class), CBS("cbs", PostStitchData.class),
	TS("ts", TransactionHistoryDetails.class),
	RETRY_PIPELINE("retry-pipeline", TransformedTransactionHistoryDetail.class),
	PG_RETRY_V2_PIPELINE("pg-retry-v2", TransformedTransactionHistoryDetail.class),
	CART("cart", TransformedTransactionHistoryDetail.class),
	SINK_TO_CART("sink2cart", TransformedTransactionHistoryDetail.class),
	RETRY_PIPELINE_V2("retry-pipeline-v2", TransformedTransactionHistoryDetail.class),
	CHAT_BACK_FILLING_PIPELINE("chat-back-filling", TransactionHistoryDetails.class),
	MARKETPLACE("marketplace", TransformedTransactionHistoryDetail.class),
	CBS_DATA_CONVERTER_PIPELINE("cbs-converter", PostStitchData.class),
	CHAT_PIPELINE("chat", TransformedTransactionHistoryDetail.class),
	RETRY_PIPELINE_V3("generic-retry-pipeline", BaseRetryDto.class),
	RECON_PIPELINE("recon", TransactionHistoryDetails.class),
	STATUS_RESOLVER("status-resolver", TransactionHistoryDetails.class),
	// From Streaming Framework
	CBS_STREAM_PIPELINE("cbs-stream", PostStitchData.class), PROMO_PIPELINE("promo", PromoServiceDto.class),
	CBS_RETRY_PIPELINE("cbs-retry-pipeline", PostStitchData.class), VAN_PIPELINE("van-pipeline", VanDto.class),
	CACHE_UPDATER_PIPELINE("cache-updater-pipeline", CacheUpdaterKafkaDto.class),
	UPI_RECON_PIPELINE("upi-recon-pipeline", UpiReconDto.class),
	USER_SPEND_DOCS_CREATOR_PIPELINE("user-spend-docs-creator", UserSpendKafkaConfig.class),
	USER_ANALYTICS_MONTH_AGG_CREATOR("user-analytics-month-agg-creator", UserMonthAggKafkaConfig.class),
	USERID_FETHCER_PIPELINE(PipelineConstants.USERID_FETHCER_PIPELINE, UserIdFetcherDto.class),
	PROMO_UPI_PIPELINE(PipelineConstants.PROMO_UPI_PIPELINE, PromoUthDto.class),
	UDIR(PipelineConstants.UDIR, GenericKafkaDto.class),
	DC_MAIN_PIPELINE(PipelineConstants.DC_MAIN_PIPELINE, TransformedTransactionHistoryDetail.class),
	DC_UTH_ENRICHER_PIPELINE(PipelineConstants.DC_UTH_ENRICHER_PIPELINE, TransformedTransactionHistoryDetail.class),
	ES_INSERT_UPDATE_EXECUTOR_PIPELINE(PipelineConstants.ES_INSERT_UPDATE_EXECUTOR_PIPELINE,
			GenericUpdateInsertDto.class),
	AUDIT_PIPELINE(PipelineConstants.DATA_AUDIT_PIPELINE, TransformedTransactionHistoryDetail.class),
	API_RESPONSE_CACHE_POPULATION_PIPELINE(PipelineConstants.API_RESPONSE_CACHE_POPULATION_PIPELINE, String.class),
	CHAT_DATA_PUBLISH_API_PIPELINE(PipelineConstants.CHAT_DATA_PUBLISH_API_PIPELINE,
			TransformedTransactionHistoryDetail.class),
	PG_DATA_PUBLISH_API_PIPELINE(PipelineConstants.PG_DATA_PUBLISH_API_PIPELINE, TransactionHistoryDetails.class),
	OMS(PipelineConstants.OMS, OmsDto.class), OMS_REFUND(PipelineConstants.OMS_REFUND, OmsRefundDto.class),
	UPI_RELAY_PIPELINE(PipelineConstants.UPI_RELAY_PIPELINE, TransactionHistoryDetails.class),
	MANDATE(PipelineConstants.MANDATE, TransactionHistoryDetails.class),
	MANDATE_RETRY(PipelineConstants.MANDATE_RETRY, MandateBaseDto.class),
	BACKFILLING(PipelineConstants.BACKFILLING_PIPELINE_NAME, MandateBackFillingKafkaObject.class);

	private final String pipelineName;

	// TODO Need to check with team for input class for all pipelines
	final Class inputClazz;

	FlinkPipelineNameEnum(final String pipelineName, final Class inputClazz) {
		this.pipelineName = pipelineName;
		this.inputClazz = inputClazz;
	}

	public static FlinkPipelineNameEnum getPipelineNameByName(final String pipelineName) {
		for (FlinkPipelineNameEnum pipelineNameEnum : FlinkPipelineNameEnum.values()) {
			if (pipelineNameEnum.getPipelineName().equals(pipelineName)) {
				return pipelineNameEnum;
			}
		}
		return null;
	}

}
