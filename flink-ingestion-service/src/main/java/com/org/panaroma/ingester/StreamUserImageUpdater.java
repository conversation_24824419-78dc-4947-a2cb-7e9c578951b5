package com.org.panaroma.ingester;

import static com.org.panaroma.ingester.constants.Constants.IS_VIRTUAL_ENTITY_ID;
import static com.org.panaroma.ingester.constants.Constants.USER_IMAGE_URL;

import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.KafkaUrlImageData;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.ingester.cache.CacheKeyManager;
import com.org.panaroma.ingester.cache.IcacheClient;
import com.org.panaroma.ingester.utils.Utility;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class StreamUserImageUpdater implements
		FlatMapFunction<TransformedTransactionHistoryDetail, TransformedTransactionHistoryDetail>, Serializable {

	@Value("${pushUserInUserImageUrlKafka:#{false}}")
	private boolean pushUserInUserImageUrlKafka = false;

	@Value("${updateUserImage:#{false}}")
	private boolean updateUserImage = false;

	private IKafkaClient kafkaClient;

	private IcacheClient<TransformedTransactionHistoryDetail> cacheClient;

	@Autowired
	public StreamUserImageUpdater(final IKafkaClient kafkaClient,
			final IcacheClient<TransformedTransactionHistoryDetail> cacheClient) {
		this.kafkaClient = kafkaClient;
		this.cacheClient = cacheClient;
	}

	@Override
	public void flatMap(final TransformedTransactionHistoryDetail tthd,
			final Collector<TransformedTransactionHistoryDetail> collector) {
		if (tthd == null) {
			return;
		}
		try {
			if (updateUserImage) {
				// log-optimisation
				log.debug("processing for UserImageUrl for source : {}, txnId: {} and entityId : {}",
						Utility.getActivePipelineName(), tthd.getTxnId(), tthd.getEntityId());
				final List<String> customerIdLists = new ArrayList<>();
				if (Utility.isP2pTransaction(tthd) && tthd.getParticipants() != null) {
					for (final TransformedParticipant participant : tthd.getParticipants()) {
						if (needToUpdateUserImage(tthd, participant)) {
							if (StringUtils.isBlank(tthd.getSecondUserImageUrl()) && (Objects
								.isNull(participant.getContextMap())
									|| (Objects.nonNull(participant.getContextMap())
											&& !participant.getContextMap().containsKey(IS_VIRTUAL_ENTITY_ID)))) {
								customerIdLists.add(participant.getEntityId());
							}
						}
					}
				}
				final Map<String, UserDetails> userUrlFromCacheMap = getUserUrlfromCache(customerIdLists);
				if (userUrlFromCacheMap != null && !userUrlFromCacheMap.isEmpty()) {
					boolean needToUpdateCache = false;
					for (final TransformedParticipant participant : tthd.getParticipants()) {
						if (needToUpdateUserImage(tthd, participant)
								&& userUrlFromCacheMap.containsKey(participant.getEntityId())) {
							final UserDetails userDetails = userUrlFromCacheMap.get(participant.getEntityId());
							if (userDetails != null && !StringUtils.isBlank(userDetails.getUserImageUrl())) {
								needToUpdateCache = true;
								tthd.setSecondUserImageUrl(userDetails.getUserImageUrl());
							}
							else {
								userUrlFromCacheMap.remove(participant.getEntityId());
							}
						}
					}
					if (needToUpdateCache) {
						cacheClient.saveUpdatedRecord(tthd, CacheKeyManager.getSelfKey(tthd));
						// log-optimisation
						log.debug("Updating userImageUrl for user id: {} and txnId: {}", tthd.getEntityId(),
								tthd.getTxnId());
					}
				}
				pushUserIdWithOutImageToKafka(customerIdLists, userUrlFromCacheMap, tthd);
			}
		}
		catch (Exception e) {
			log.error("Exception in StreamUserImageUpdater : {}", CommonsUtility.exceptionFormatter(e));
		}
		collector.collect(tthd);
	}

	private boolean needToUpdateUserImage(final TransformedTransactionHistoryDetail tthd,
			final TransformedParticipant participant) {
		return tthd.getEntityId() != null && participant.getEntityId() != null
				&& !(tthd.getEntityId().equals(participant.getEntityId()) && tthd.getTxnIndicator() != null
						&& participant.getTxnIndicator() != null
						&& tthd.getTxnIndicator().equals(participant.getTxnIndicator()))
				&& EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType());
	}

	private Map<String, UserDetails> getUserUrlfromCache(final List<String> customerIdLists) {
		if (customerIdLists == null || customerIdLists.size() == 0) {
			return Collections.EMPTY_MAP;
		}
		return cacheClient.getUserUrlfromCache(customerIdLists);
	}

	private void pushUserIdWithOutImageToKafka(final List<String> customerIdLists,
			final Map<String, UserDetails> userUrlFromCacheMap, final TransformedTransactionHistoryDetail thd) {
		final List<String> userIdsWithoutUrl = customerIdLists != null ? customerIdLists : Collections.EMPTY_LIST;
		final Map<String, UserDetails> userUrlFromCacheMapTemp = userUrlFromCacheMap != null ? userUrlFromCacheMap
				: Collections.EMPTY_MAP;

		userIdsWithoutUrl.removeAll(userUrlFromCacheMapTemp.keySet());
		final Map<String, Object> kafkaUrlImageDataMap = new HashMap<>();
		if (Utility.isP2pTransaction(thd) && thd.getIsSource() && pushUserInUserImageUrlKafka) {
			for (final String userId : userIdsWithoutUrl) {
				final KafkaUrlImageData kafkaUrlImageData = KafkaUrlImageData.builder()
					.userId(userId)
					.docTxnId(thd.getTxnId())
					.docEntityId(thd.getEntityId())
					.docTxnDate(thd.getTxnDate())
					.docStreamSource(thd.getStreamSource())
					.build();
				kafkaUrlImageDataMap.put(userId, kafkaUrlImageData);
			}
			if (!kafkaUrlImageDataMap.isEmpty()) {
				try {
					// log-optimisation
					log.debug("pushing UserImageUrl to kafka txnId: {} for userIds: {}", thd.getTxnId(),
							String.join(", ", userIdsWithoutUrl));
					this.pushInKafka(kafkaUrlImageDataMap);
				}
				catch (final Exception ex) {
					log.error(
							"Exception :{} while pushing data into User Image Url kafka for user image url with systemId : {}",
							CommonsUtility.exceptionFormatter(ex), thd.getTxnId());
					return;
				}
			}
		}
	}

	private void pushInKafka(final Map<String, Object> kafkaUrlImageDataMap) {
		kafkaClient.pushIntoKafka(USER_IMAGE_URL, kafkaUrlImageDataMap);
	}

}
