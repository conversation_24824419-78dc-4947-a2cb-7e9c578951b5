package com.org.panaroma.ingester;

import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2M_REFUND;
import static com.org.panaroma.ingester.constants.Constants.CHILD_TXNS;
import static com.org.panaroma.ingester.constants.Constants.ENTITY_ID;
import static com.org.panaroma.ingester.constants.Constants.PARENT_TXN;
import static com.org.panaroma.ingester.constants.Constants.PPBL_SET_NAME;
import static com.org.panaroma.ingester.constants.Constants.RELATIVE_PRESENT;
import static com.org.panaroma.ingester.constants.Constants.REPORT_CODE;
import static com.org.panaroma.ingester.constants.Constants.SIZE_NOT_ALLOWED_FOR_REGROUPING;
import static com.org.panaroma.ingester.constants.Constants.STREAM_SOURCE;
import static com.org.panaroma.ingester.constants.Constants.TXN_ID;
import static com.org.panaroma.ingester.constants.Constants.XFER_RPT_CODES;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.COUNT_P2M_REFUND_TXNS_WITHOUT_P2M_TXN;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.P2M_AND_P2M_REFUND_DATE_DIFF;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.P2M_DOC_FETCH_COUNT_TO_POPULATE_REFUND_DETAILS;

import com.aerospike.client.Record;
import com.fasterxml.jackson.core.type.TypeReference;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.ParentDetails;
import com.org.panaroma.commons.dto.es.RefundDetails;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.datagroup.GroupingManager;
import com.org.panaroma.ingester.cache.IcacheClient;
import com.org.panaroma.ingester.enums.SearchAbleIdentifersEnum;
import com.org.panaroma.ingester.merger.BaseMerger;
import com.org.panaroma.ingester.merger.interfaces.IMergerFactory;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.repository.EsRepository;
import com.org.panaroma.ingester.services.TransformerHelperService;
import com.org.panaroma.ingester.utils.Utility;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class DataEnricher implements IDataEnricher, Serializable {

	private GroupingManager groupingManager;

	private IMergerFactory iMergerFactory;

	private TransformerHelperService transformerHelperService;

	private IcacheClient<TransformedTransactionHistoryDetail> cacheClient;

	private EsRepository esRepository;

	private MetricsAgent metricsAgent;

	private List<String> whiteListedUserList;

	private Long maxDaysDiffForFwdRefundTxnsLinking;

	@Autowired
	public DataEnricher(final GroupingManager groupingManager, final IMergerFactory mergerFactory,
			final TransformerHelperService transformerHelperService,
			final IcacheClient<TransformedTransactionHistoryDetail> cacheClient, final EsRepository esRepository,
			final MetricsAgent metricsAgent,
			@Value("${white.listed.users.list}") final List<String> whiteListedUserList,
			@Value("${txn.event.max.days.diff.from.current.to.process.txn}") final Long maxDaysDiffForFwdRefundTxnsLinking) {
		this.groupingManager = groupingManager;
		this.iMergerFactory = mergerFactory;
		this.transformerHelperService = transformerHelperService;
		this.cacheClient = cacheClient;
		this.esRepository = esRepository;
		this.metricsAgent = metricsAgent;
		this.whiteListedUserList = whiteListedUserList;
		this.maxDaysDiffForFwdRefundTxnsLinking = maxDaysDiffForFwdRefundTxnsLinking;
	}

	@Override
	public Set<TransformedTransactionHistoryDetail> getMergedDtos(final TransformedTransactionHistoryDetail tthd) {
		Pair<Set<TransformedTransactionHistoryDetail>, Set<TransformedTransactionHistoryDetail>> pairDoc = new Pair<>();
		// Below Metrics states Number of re-groupings saved count
		if (tthd.getContextMap() != null && tthd.getContextMap().containsKey(RELATIVE_PRESENT)
				&& SIZE_NOT_ALLOWED_FOR_REGROUPING.contains(tthd.getContextMap().get(RELATIVE_PRESENT))) {
			metricsAgent.incrementCount("REGROUPED", "NoReGrouping");
		}
		else if (Boolean.TRUE.equals(tthd.getIsVisible())) {
			/*
			 * Added check on isVisible above in case for UPI P2M collect txns,
			 * noOfRelPresent is not populated in contextMap due to some reason otherwise
			 * it would go for regrouping which is not required. For UPI P2M collect
			 * (non-success) events, isVisible and showInListing is false.
			 */
			metricsAgent.incrementCount("REGROUPED");
			pairDoc = groupingManager.group(tthd);
		}
		Utility.removeNonRequiredFields(tthd);
		Set<TransformedTransactionHistoryDetail> groupedDtos;
		groupedDtos = pairDoc.getValue();
		// no functional change, just handled null pointer exception here
		if (groupedDtos == null) {
			groupedDtos = new HashSet<>();
		}
		log.info("size of relative document fetched : {} for txnId : {}. relative doc fetched : {}", groupedDtos.size(),
				tthd.getTxnId(), groupedDtos);
		groupedDtos.add(tthd);
		Set<TransformedTransactionHistoryDetail> mergedDtos = new HashSet<>();
		TransactionTypeEnum currTxnType = com.org.panaroma.commons.utils.Utility.getTxnTypeForSingleDoc(groupedDtos,
				tthd);
		BaseMerger merger = this.iMergerFactory.getMergerByType(currTxnType);
		populatePpblRecordSetCache(tthd);
		if (Objects.nonNull(merger)) {
			if (groupedDtos.size() > 1
					|| (groupedDtos.size() == 1 && TransactionTypeEnum.PPBL_TRANSACTION.equals(currTxnType))) {
				mergedDtos = merger.merge(groupedDtos);
				// Added this method to capture related txn ids in contextmap
				populateParentChildTxnIdsInContextMap(groupedDtos, mergedDtos);
				if (Objects.nonNull(mergedDtos)) {
					log.debug("Number of merged docs in set corresponding to txnId : {} is : {}", tthd.getTxnId(),
							mergedDtos.size());
					if (mergedDtos.size() > 0) {
						// set otherPartyEntityId field in esDto from
						// otherParticipant.entityId
						mergedDtos.forEach(mergedDoc -> {
							if (Objects.nonNull(mergedDoc) && StringUtils.isBlank(mergedDoc.getOtherPartyEntityId())) {
								transformerHelperService.populateOtherPartyEntityId(mergedDoc);
							}
						});
					}
				}
			}
			merger.populateChatFlagInDtosToSendToChat(groupedDtos, mergedDtos, tthd);
		}

		if (P2M_REFUND.equals(currTxnType)) {
			populateRefundAndParentDetails(mergedDtos, groupedDtos);
		}

		Utility.populatePushTimeStampInMergedDoc(mergedDtos, tthd);

		// For Debugging UTH Pipeline issue
		if (ObjectUtils.isNotEmpty(whiteListedUserList) && whiteListedUserList.contains(tthd.getEntityId())) {
			log.warn("merged Dtos : {} for groupId : {}, txnId: {}", mergedDtos, tthd.getGroupId(), tthd.getTxnId());
		}

		log.info("merged Dtos : {} for groupId : {}", mergedDtos, tthd.getGroupId());
		return mergedDtos;
	}

	/**
	 * Derives and populates parentTxnId, childTxnIds in mergedDtos contextMap.
	 * @param groupedDtos = List of all txndIds for this groupId including incoming tthd
	 * @param mergedDtos = List of txnIds that needs to be sent to chat. mergedDtos.size
	 * <= groupedDtos
	 *
	 */
	private void populateParentChildTxnIdsInContextMap(final Set<TransformedTransactionHistoryDetail> groupedDtos,
			final Set<TransformedTransactionHistoryDetail> mergedDtos) {
		List<String> childTxnIds = new ArrayList<>();
		String parentTxnId = groupedDtos.stream()
			.filter(tthd -> tthd.getShowInListing() || tthd.getIsSource())
			.findFirst()
			.map(TransformedTransactionHistoryDetail::getTxnId)
			.orElse(null);
		log.info("parentTxnId-{}", parentTxnId);
		groupedDtos.stream().filter(tthd -> !tthd.getTxnId().equals(parentTxnId)).forEach(tthd -> {
			childTxnIds.add(tthd.getTxnId());
		});
		log.info("childTxnIds-{}", childTxnIds);

		if (childTxnIds.isEmpty()) {
			return;
		}

		// for each mergedTthd, check if it is in parent or child.
		// if tthd.id in parent, populate child
		// if tthd.id in child, populate parent & child.
		// on api side, if child is present, send all of them as list.
		// if it is parent, send list of child. if it is child, send child.

		for (TransformedTransactionHistoryDetail mergedTthd : mergedDtos) {
			if (ObjectUtils.isEmpty(mergedTthd.getContextMap())) {
				mergedTthd.setContextMap(new HashMap<>());
			}
			mergedTthd.getContextMap().put(CHILD_TXNS, String.join(",", childTxnIds));
			if (childTxnIds.contains(mergedTthd.getTxnId())) {
				mergedTthd.getContextMap().put(PARENT_TXN, parentTxnId);
			}
		}
	}

	private void populatePpblRecordSetCache(final TransformedTransactionHistoryDetail enrichedDoc) {
		// required whitelisting boolean here
		if (TransactionTypeEnum.PPBL_TRANSACTION
			.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(enrichedDoc.getMainTxnType()))
				&& enrichedDoc.getContextMap().containsKey(REPORT_CODE)
				&& XFER_RPT_CODES.contains(enrichedDoc.getContextMap().get(REPORT_CODE))) {
			Record storedDoc = null;
			try {
				storedDoc = this.cacheClient.getRecord(enrichedDoc.getTxnId(), PPBL_SET_NAME,
						new TypeReference<List<TransformedTransactionHistoryDetail>>() {
						});
				List<TransformedTransactionHistoryDetail> storedDocsWithSameTxnId;
				String currentEntityId = enrichedDoc.getEntityId();
				if (storedDoc != null) {
					if (!Objects.isNull(storedDoc.bins)) {
						storedDocsWithSameTxnId = (List<TransformedTransactionHistoryDetail>) storedDoc.bins
							.get("value");

						log.debug("Stored docs with same txnId found are :{}", storedDoc);
						// to always keep the latest doc in cache
						for (TransformedTransactionHistoryDetail doc : storedDocsWithSameTxnId) {
							if (currentEntityId.equalsIgnoreCase(doc.getEntityId())) {
								storedDocsWithSameTxnId.remove(doc);
								break;
							}
						}
						storedDocsWithSameTxnId.add(enrichedDoc);
						this.cacheClient.saveRecordInPpblCache(storedDocsWithSameTxnId, enrichedDoc.getTxnId(),
								PPBL_SET_NAME);
					}
					else {
						throw new RuntimeException("bin value null even if Record is present");
					}
				}
				else {
					storedDocsWithSameTxnId = new ArrayList<>();
					storedDocsWithSameTxnId.add(enrichedDoc);
					this.cacheClient.saveRecordInPpblCache(storedDocsWithSameTxnId, enrichedDoc.getTxnId(),
							PPBL_SET_NAME);
				}
			}
			catch (Exception e) {
				log.error("Some exception occurred while putting or getting from cache {}",
						CommonsUtility.exceptionFormatter(e));
				throw e;
			}
		}

	}

	private void populateRefundAndParentDetails(final Set<TransformedTransactionHistoryDetail> mergedDtos,
			final Set<TransformedTransactionHistoryDetail> groupedDtos) {
		Optional<TransformedTransactionHistoryDetail> pgDoc = mergedDtos.stream()
			.filter((doc) -> TransactionSource
				.isPgTypeSource(TransactionSource.getTransactionSourceEnumByKey(doc.getStreamSource())))
			.findFirst();
		TransformedTransactionHistoryDetail pgDocument = null;

		if (pgDoc.isPresent()) {
			pgDocument = pgDoc.get();
		}
		else {
			pgDoc = groupedDtos.stream()
				.filter((doc) -> TransactionSource
					.isPgTypeSource(TransactionSource.getTransactionSourceEnumByKey(doc.getStreamSource())))
				.findFirst();

			if (pgDoc.isPresent()) {
				pgDocument = pgDoc.get();
			}
		}

		// For adding refund txn details on onward txn and vice versa
		// UTH - 211
		if (Objects.nonNull(pgDocument) && P2M_REFUND.getTransactionTypeKey().equals(pgDocument.getTxnType())) {
			List<TransformedTransactionHistoryDetail> data = getP2mDoc(pgDocument);
			TransformedTransactionHistoryDetail p2mDoc = null;

			if (CollectionUtils.isNotEmpty(data) && data.size() == 1) {

				// Get Parent from fetched data
				p2mDoc = data.get(0);

				Long currentDate = new Date().getTime();
				metricsAgent.pushTimeDiff(P2M_AND_P2M_REFUND_DATE_DIFF, p2mDoc.getDocCreatedDate(), currentDate);

				// Get refund Txn details from P2MRefund
				Boolean isP2mDocUpdated = setRefundDetailsOnP2mFromP2mRefundEvent(pgDocument, p2mDoc);

				// Get Parent Txn details from P2M
				setParentDetailsOnP2mRefundFromP2mEvent(pgDocument, p2mDoc);

				// set updatedDate on P2mDoc
				if (isP2mDocUpdated) {
					p2mDoc.setDocUpdatedDate(currentDate);
					mergedDtos.add(p2mDoc);
				}
			}
		}
	}

	/*
	 * Method to get P2M Doc for Refund txn to link Refund and Fwd Txn Details
	 */
	private List<TransformedTransactionHistoryDetail> getP2mDoc(final TransformedTransactionHistoryDetail pgDocument) {
		TransactionSource streamSource = null;
		String parentTxnId = null;

		// If Parent Details is present then fetch parent txn stream source and txnId from
		// parent txn details otherwise fetch from txn itself(Old Logic)
		if (Objects.nonNull(pgDocument.getParentDetails())) {
			streamSource = TransactionSource
				.getTransactionSourceEnumByName(pgDocument.getParentDetails().getTxnSource());
			parentTxnId = pgDocument.getParentDetails().getTxnId();
		}
		else {
			// Old logic of fetching stream source and txnId from txn itself
			streamSource = TransactionSource.getTransactionSourceEnumByKey(pgDocument.getStreamSource());
			parentTxnId = pgDocument.getParentTxnId();
		}

		if (StringUtils.isBlank(parentTxnId) || Objects.isNull(streamSource)) {
			log.warn("Parent txnId or Parent Txn Stream Source is blank for refund txnId : {}", pgDocument.getTxnId());
			com.org.panaroma.commons.utils.Utility.addSearchableIdentifier(pgDocument,
					SearchAbleIdentifersEnum.FWD_TXN_SOURCE_OR_TXN_ID_IS_MISSING.getSearchAbleIdentifierKey());
			return null;
		}
		// removing identifier FWD_TXN_SOURCE_OR_TXN_ID_IS_MISSING if present
		com.org.panaroma.commons.utils.Utility.removeSearchableIdentifier(pgDocument,
				SearchAbleIdentifersEnum.FWD_TXN_SOURCE_OR_TXN_ID_IS_MISSING.getSearchAbleIdentifierKey());

		Long txnDate = null;
		if (Objects.nonNull(pgDocument.getParentDetails())
				&& StringUtils.isNotBlank(pgDocument.getParentDetails().getTxnDate())) {
			txnDate = Long.parseLong(pgDocument.getParentDetails().getTxnDate());
		}

		if (Objects.nonNull(txnDate) && !pgDocument.getParentDetails().isParentExistsInDb()
				&& Utility.isEventOlderThanLimit(txnDate, maxDaysDiffForFwdRefundTxnsLinking)) {
			log.warn("Fwd Txn txn date is older than max allowed older days txnId: {}", pgDocument.getTxnId());

			com.org.panaroma.commons.utils.Utility.addSearchableIdentifier(pgDocument,
					SearchAbleIdentifersEnum.FWD_TXN_DATE_OLDER_THAN_LIMIT.getSearchAbleIdentifierKey());
			return null;
		}
		// removing identifier FWD_TXN_DATE_OLDER_THAN_LIMIT if present
		com.org.panaroma.commons.utils.Utility.removeSearchableIdentifier(pgDocument,
				SearchAbleIdentifersEnum.FWD_TXN_DATE_OLDER_THAN_LIMIT.getSearchAbleIdentifierKey());

		return fetchP2mDocForRefund(pgDocument, parentTxnId, streamSource, txnDate);
	}

	/*
	 * Method to fetch Fwd txn using provided txnId and source for refund txn
	 */
	private List<TransformedTransactionHistoryDetail> fetchP2mDocForRefund(
			final TransformedTransactionHistoryDetail refundTxn, final String fwdTxnId,
			final TransactionSource fwdTxnSource, final Long txnDate) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put(STREAM_SOURCE, fwdTxnSource.getTransactionSourceKey());
		paramMap.put(TXN_ID, fwdTxnId);
		paramMap.put(ENTITY_ID, refundTxn.getEntityId());

		List<TransformedTransactionHistoryDetail> data = null;
		try {
			if (Objects.isNull(txnDate)) {
				data = esRepository.fetchDataOnlyFromAlias(paramMap, refundTxn);
			}
			else {
				data = esRepository.fetchData(paramMap, txnDate, refundTxn.getTxnId());
			}

			if (CollectionUtils.isEmpty(data)) {
				log.warn("No p2m txn found with txnId : {} for p2m_refund txnId : {}", fwdTxnId, refundTxn.getTxnId());

				com.org.panaroma.commons.utils.Utility.addSearchableIdentifier(refundTxn,
						SearchAbleIdentifersEnum.FWD_TXN_NOT_FOUND_USING_TXN_ID.getSearchAbleIdentifierKey());
				metricsAgent.incrementCount(COUNT_P2M_REFUND_TXNS_WITHOUT_P2M_TXN);
			}
			else {
				metricsAgent.incrementCount(P2M_DOC_FETCH_COUNT_TO_POPULATE_REFUND_DETAILS);
			}

			// removing identifier ERROR_WHILE_GETTING_FWD_TXN if present
			com.org.panaroma.commons.utils.Utility.removeSearchableIdentifier(refundTxn,
					SearchAbleIdentifersEnum.ERROR_WHILE_GETTING_FWD_TXN.getSearchAbleIdentifierKey());
		}
		catch (Exception e) {
			log.error("Exception while fetching data from ES : {}, txnId: {}", CommonsUtility.exceptionFormatter(e),
					refundTxn.getTxnId());

			// Adding ERROR_WHILE_GETTING_FWD_TXN identifier to identify such txn whose
			// linking doesn't happened because of this
			com.org.panaroma.commons.utils.Utility.addSearchableIdentifier(refundTxn,
					SearchAbleIdentifersEnum.ERROR_WHILE_GETTING_FWD_TXN.getSearchAbleIdentifierKey());
		}
		return data;
	}

	public boolean setRefundDetailsOnP2mFromP2mRefundEvent(final TransformedTransactionHistoryDetail p2mRefundDoc,
			final TransformedTransactionHistoryDetail p2mDoc) {

		// when p2m doc get updated, it will return true.
		// Get Refund Details
		RefundDetails refundDetails = RefundDetails.builder()
			.orderId(p2mRefundDoc.getOrderId())
			.txnId(p2mRefundDoc.getTxnId())
			.date(p2mRefundDoc.getTxnDate())
			.status(p2mRefundDoc.getStatus())
			.amount(p2mRefundDoc.getAmount())
			.build();

		// Set Refund Detail on P2m Doc
		if (CollectionUtils.isEmpty(p2mDoc.getRefundDetails())) {
			List<RefundDetails> refundDetailsList = new ArrayList<>();
			refundDetailsList.add(refundDetails);
			p2mDoc.setRefundDetails(refundDetailsList);
		}
		else {
			TransformedTransactionHistoryDetail finalPgDocument = p2mRefundDoc;
			if (p2mDoc.getRefundDetails()
				.stream()
				.anyMatch(
						refundDetails1 -> StringUtils.equals(refundDetails1.getTxnId(), finalPgDocument.getTxnId()))) {
				log.info("Refund details is already present in p2m txn : {} for p2m refund txn : {}", p2mDoc.getTxnId(),
						p2mRefundDoc.getTxnId());
				return false;
			}
			p2mDoc.getRefundDetails().add(refundDetails);
		}
		return true;
	}

	public void setParentDetailsOnP2mRefundFromP2mEvent(final TransformedTransactionHistoryDetail p2mRefundDoc,
			final TransformedTransactionHistoryDetail p2mDoc) {

		// Get Parent Details from P2mDoc for P2mRefund and set it on P2mRefund Doc and
		// setting parentExistsInDb as true
		ParentDetails parentDetails = ParentDetails.builder()
			.txnId(p2mDoc.getTxnId())
			.txnDate(p2mDoc.getTxnDate().toString())
			.txnSource(Objects.requireNonNull(TransactionSource.getTransactionSourceEnumByKey(p2mDoc.getStreamSource()))
				.toString())
			.amount(String.valueOf(p2mDoc.getAmount()))
			.rrn(com.org.panaroma.commons.utils.Utility.getRrn(p2mDoc))
			.parentExistsInDb(true)
			.build();

		// Set Parent Detail on P2m_refund Doc, if not present or parentExistsInDb is
		// false
		if (Objects.isNull(p2mRefundDoc.getParentDetails()) || !p2mRefundDoc.getParentDetails().isParentExistsInDb()) {
			p2mRefundDoc.setParentDetails(parentDetails);
		}
	}

}
