package com.org.panaroma.ingester.serializer;

import static com.org.panaroma.ingester.constants.Constants.IS_CHAT_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.IS_FOR_CHAT_BACK_FILLING;
import static com.org.panaroma.ingester.constants.Constants.SINK_TO_UTH;
import static com.org.panaroma.ingester.constants.Constants.TRUE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.ingester.dto.UpiReconDto;
import com.org.panaroma.ingester.dto.VanDto;
import com.org.panaroma.ingester.dto.streamDTO.PromoServiceDto;
import com.org.panaroma.ingester.dto.streamDTO.PromoUpiServiceDto;
import com.org.panaroma.ingester.dto.streamDTO.PromoUthDto;
import com.org.panaroma.ingester.utils.Utility;
import java.util.Objects;
import jakarta.annotation.Nullable;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.avro.reflect.ReflectData;
import org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

@Log4j2
public class StringSerializationSchema<T> implements KafkaSerializationSchema<T> {

	private String topic;

	private transient Schema schema;

	private Class clazz;

	private ObjectMapper objectMapper;

	private String pipeline;

	public StringSerializationSchema(final String topic, final Class clazz, final String pipeline) {
		this.topic = topic;
		this.clazz = clazz;
		this.pipeline = pipeline;
	}

	@SneakyThrows
	@Override
	public ProducerRecord<Object, GenericData.Record> serialize(final Object elem, @Nullable final Long timeStamp) {

		ensureInitialized();
		String key = Utility.getKafkaKey(elem);
		String dataToPush = null;
		try {

			if (elem instanceof TransformedTransactionHistoryDetail) {
				TransformedTransactionHistoryDetail tthd = (TransformedTransactionHistoryDetail) elem;
				if (Objects.nonNull(tthd)) {
					if (tthd.getContextMap() != null
							&& TRUE.equals(tthd.getContextMap().get(IS_FOR_CHAT_BACK_FILLING))) {
						log.warn(
								"Merged doc for back-filling is being pushed to : {} topic for txnId : {}, entityId: {}",
								topic, tthd.getTxnId(), tthd.getEntityId());
					}
					else if (Objects.nonNull(tthd.getContextMap())
							&& TRUE.equals(tthd.getContextMap().get(IS_CHAT_DOCUMENT))) {
						log.warn(
								"Merged doc is being pushed to : {} topic for txnId : {} , mainTxnType : {} , "
										+ "status : {} , originalStatus : {}, entityId: {}",
								topic, tthd.getTxnId(), tthd.getMainTxnType(), tthd.getStatus(),
								tthd.getOriginalStatus(), tthd.getEntityId());

						// Added for Non Prod Chat Testing
						log.debug("Event for Chat System txnId: {}, data: {}", tthd.getTxnId(), tthd);
					}
					else {
						log.warn("TTHD object with txnId : {} is being pushed to : {} topic, entityId :{}",
								tthd.getTxnId(), topic, tthd.getEntityId());
						if (SINK_TO_UTH.equalsIgnoreCase(pipeline)) {
							Utility.setPushTimeStampInContextMap(tthd);
						}
					}
				}
			}
			else if (elem instanceof PromoServiceDto) {
				PromoServiceDto promoServiceDto = (PromoServiceDto) elem;
				log.warn("Promo object with txnId : {} is being pushed to : {} topic, entityId: {}, txnId: {}",
						promoServiceDto.getEventId(), topic, promoServiceDto.getCustId(), promoServiceDto.getTxnId());
			}
			else if (elem instanceof PromoUthDto) {
				PromoUthDto promoUthDto = (PromoUthDto) elem;
				log.warn("PromoUthDto object with receiverCustId: {} and txnId : {} is being pushed to : {} topic",
						promoUthDto.getReceiverCustId(), promoUthDto.getTxnId(), topic);
			}
			else if (elem instanceof PromoUpiServiceDto) {
				PromoUpiServiceDto promoUpiServiceDto = (PromoUpiServiceDto) elem;
				log.warn("PromoUPI object with txnId : {} is being pushed to : {} topic", promoUpiServiceDto.getTxnId(),
						topic);
			}
			else if (elem instanceof CacheUpdaterKafkaDto) {
				CacheUpdaterKafkaDto dto = (CacheUpdaterKafkaDto) elem;
				log.warn("NonTransactingUserData object with txnId : {} is being pushed to : {} topic", dto.getUid(),
						topic);
			}
			else if (elem instanceof VanDto) {
				VanDto dto = (VanDto) elem;
				log.warn("Pushing Data to Van target : {}", dto);
			}
			else if (elem instanceof UpiReconDto) {
				UpiReconDto dto = (UpiReconDto) elem;
				log.warn("Pushing Data to upiRecon target : {}", dto);
			}
			else if (elem instanceof MandateActivityData) {
				MandateActivityData dto = (MandateActivityData) elem;
				log.warn("Pushing Data to mandate-back-filling target : {}", dto);
			}
			else {
				log.warn("Pushing events : {}", elem);
			}
			dataToPush = objectMapper.writeValueAsString(elem);
		}
		catch (Exception e) {
			log.error("Exception while converting record to generic String record. Key: {}, Topic: {}, Exception :{}",
					key, topic, CommonsUtility.exceptionFormatter(e));
		}
		return new ProducerRecord(topic, key, dataToPush);
	}

	private void ensureInitialized() {
		if (objectMapper == null || schema == null) {
			this.schema = ReflectData.get().getSchema(clazz);
			this.objectMapper = new ObjectMapper();
		}
	}

}
