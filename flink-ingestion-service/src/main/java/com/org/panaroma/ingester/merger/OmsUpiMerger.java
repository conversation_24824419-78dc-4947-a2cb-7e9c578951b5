package com.org.panaroma.ingester.merger;

import static com.org.panaroma.commons.constants.DatabaseFieldNameConstants.ElasticSearchFieldNameConstants.SEARCHABLE_IDENTIFIERS;
import static com.org.panaroma.commons.constants.WebConstants.UMN;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2M;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2M_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.RECURRING_MANDATE;
import static com.org.panaroma.ingester.constants.Constants.CONTEXT_MAP;
import static com.org.panaroma.ingester.constants.Constants.CREDIT;
import static com.org.panaroma.ingester.constants.Constants.CREDIT_PAYMENT_SYSTEM;
import static com.org.panaroma.ingester.constants.Constants.DEBIT;
import static com.org.panaroma.ingester.constants.Constants.DEBIT_PAYMENT_SYSTEM;
import static com.org.panaroma.ingester.constants.Constants.DOC_UPDATED_DATE;
import static com.org.panaroma.ingester.constants.Constants.IS_CHAT_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.IS_MERGED_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.LOCATION;
import static com.org.panaroma.ingester.constants.Constants.P2M_STREAM_SOURCES_TO_SEND_TO_CHAT;
import static com.org.panaroma.ingester.constants.Constants.PARENT_DETAILS;
import static com.org.panaroma.ingester.constants.Constants.REFUND_DETAILS;
import static com.org.panaroma.ingester.constants.Constants.SEARCH_FIELDS;
import static com.org.panaroma.ingester.constants.Constants.SPEND_ANALYTICS_FIELDS;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.ingester.repository.EsRepository;
import com.org.panaroma.ingester.utils.MergerUtility;
import com.org.panaroma.ingester.utils.Utility;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class OmsUpiMerger implements UthMerger {

	@Autowired
	EsRepository esRepository;

	@Autowired
	ObjectMapper objectMapper;

	@Value("${white.listed.users.list}")
	private List<String> whiteListedUserList;

	@Value("${p2mAndAddAndPay.whitelisting.required.for.chat}")
	private Integer p2mAndAddAndPayWhitelistingReqForChat;

	@Value("${p2mAndAddAndPay.rollOut.percent.for.chat}")
	private Double p2mAndAddAndPayRollOutPercentageForChat;

	@Override
	public List<TransactionTypeEnum> appliesTo() {
		return Arrays.asList(P2M, P2M_REFUND, RECURRING_MANDATE);
	}

	private static final String DEBIT_UPI_DATA = "debitUpiData";

	private static final String DEBIT_WALLET_DATA = "debitWalletData";

	private static final String DEBIT_BANK_DATA = "debitBankData";

	private static final String DEBIT_USER_CARD_DATA = "debitUserCardData";

	private static final String DEBIT_NAME = "debitName";

	private static final String DEBIT_CONTEXT_MAP = "debitContextMap";

	private static final String CREDIT_UPI_DATA = "creditUpiData";

	private static final String CREDIT_WALLET_DATA = "creditWalletData";

	private static final String CREDIT_NAME = "creditName";

	private static final String CREDIT_BANK_DATA = "creditBankData";

	private static final String CREDIT_USER_CARD_DATA = "creditUserCardData";

	private static final String CREDIT_CONTEXT_MAP = "creditContextMap";

	private static final String MERCHANT_NAME = "merchantName";

	private static final String MERCHANT_UPI_DATA = "merchantUpiData";

	private static final String MERCHANT_BANK_DATA = "merchantBankData";

	private static final String MERCHANT_ENTITY_TYPE = "merchantEntityType";

	private static final String MERCHANT_CONTEXT_MAP = "merchantContextMap";

	@Override
	public Set<TransformedTransactionHistoryDetail> merge(
			final Set<TransformedTransactionHistoryDetail> passedEnrichedDocs) {
		Set<TransformedTransactionHistoryDetail> enrichedDocs = passedEnrichedDocs;

		if (Objects.isNull(enrichedDocs) || enrichedDocs.size() <= 1) {
			return enrichedDocs;
		}

		Optional<TransformedTransactionHistoryDetail> omsDoc = enrichedDocs.stream()
			.filter((doc) -> TransactionSource.OMS.getTransactionSourceKey().equals(doc.getStreamSource()))
			.findFirst();

		TransformedTransactionHistoryDetail omsDocument = null;
		if (omsDoc.isPresent()) {
			omsDocument = omsDoc.get();
		}

		Optional<TransformedTransactionHistoryDetail> upiDoc = enrichedDocs.stream()
			.filter((doc) -> TransactionSource.UPI
				.equals(TransactionSource.getTransactionSourceEnumByKey(doc.getStreamSource())))
			.findFirst();

		TransformedTransactionHistoryDetail upiDocument = null;
		if (upiDoc.isPresent()) {
			upiDocument = upiDoc.get();
		}

		if (Objects.isNull(omsDocument) || Objects.isNull(upiDocument)) {
			return enrichedDocs;
		}

		// Not merging if OMS showInListing is not true
		if (!needDocsMerging(enrichedDocs)) {
			return enrichedDocs;
		}

		Set<TransformedTransactionHistoryDetail> mergeDocSet = new HashSet<>();

		MergerUtility.addUpiDetailsToOmsDoc(omsDocument, upiDocument);
		mergeDocSet.add(omsDocument);
		setMergeFlag(omsDocument);

		return mergeDocSet;
	}

	@Override
	public UpdateRequest createUpdateRequest(final TransformedTransactionHistoryDetail tthd)
			throws JsonProcessingException {
		if (tthd.getContextMap() == null || !tthd.getContextMap().containsKey(IS_MERGED_DOCUMENT)) {
			if (CollectionUtils.isEmpty(tthd.getRefundDetails()) && Objects.isNull(tthd.getParentDetails())) {
				return null;
			}
		}

		UpdateRequest updateRequest = new UpdateRequest();
		updateRequest.index(esRepository.getIndexName(tthd));
		updateRequest.type("_doc");
		updateRequest.id(tthd.docId());
		updateRequest.routing(tthd.getEntityId());
		Map<String, Object> parameters = getParameters(tthd);
		String scriptString = getScript(parameters);

		Script script = new Script(ScriptType.INLINE, "painless", scriptString, parameters);
		log.info("script : {} for docId : {}", script, tthd.docId());
		updateRequest.script(script);

		return updateRequest;
	}

	// Todo: Need to rectify this method
	@Override
	public void populateChatFlagInDtosToSendToChat(final Set<TransformedTransactionHistoryDetail> groupedDtos,
			final Set<TransformedTransactionHistoryDetail> mergedDtos,
			final TransformedTransactionHistoryDetail currentDoc) {
		final Boolean isWhitelistedTxnForChat = com.org.panaroma.commons.utils.Utility
			.isUserWhitelistedFromPercentageOrUserList(p2mAndAddAndPayRollOutPercentageForChat,
					p2mAndAddAndPayWhitelistingReqForChat, whiteListedUserList, currentDoc);
		if (isWhitelistedTxnForChat) {
			TransactionSource streamSource = TransactionSource
				.getTransactionSourceEnumByKey(currentDoc.getStreamSource());
			if (groupedDtos.size() == 1 && P2M_STREAM_SOURCES_TO_SEND_TO_CHAT.contains(streamSource)
					&& !Utility.isUpiP2mRefund(currentDoc)
					&& !com.org.panaroma.commons.utils.Utility.isMandateTransaction(currentDoc)
					&& Boolean.TRUE.equals(currentDoc.getShowInListing())
					&& Boolean.TRUE.equals(currentDoc.getIsVisible())) {
				Utility.populateIsChatDocFlagInContextMap(currentDoc);
				mergedDtos.add(currentDoc);
			}
			else if (Objects.nonNull(mergedDtos) && mergedDtos.size() == 1) {
				TransformedTransactionHistoryDetail mergedDto = mergedDtos.iterator().next();
				if (Boolean.TRUE.equals(mergedDto.getShowInListing())
						&& Boolean.TRUE.equals(mergedDto.getIsVisible())) {
					Utility.populateIsChatDocFlagInContextMap(mergedDto);
				}
			}
			else if (Utility.isUpiP2mCollectTxn(currentDoc)) {
				Utility.populateIsChatDocFlagInContextMap(currentDoc);
				mergedDtos.add(currentDoc);
			}
		}
		else {
			// this else block is only for QA testing & can be removed afterwards
			log.info(
					"Not attempting to populate isChatDocument flag as entityId : {} in event received corresponding to txnId : {} is"
							+ "not whitelisted",
					currentDoc.getEntityId(), currentDoc.getTxnId());
		}
	}

	private String getScript(final Map<String, Object> parameters) {
		String scriptString = "ctx._source.docUpdatedDate = params.docUpdatedDate;";

		if (Objects.isNull(parameters.get(CONTEXT_MAP))) {
			scriptString = scriptString
					+ "if (ctx._source.contextMap == null) { ctx._source.contextMap = ['isMergedDocument' : params.isMergedDocument];} "
					+ "else {ctx._source.contextMap.isMergedDocument = params.isMergedDocument; }";
		}
		else {
			scriptString = scriptString + "ctx._source.contextMap = params.contextMap;";
		}

		scriptString = scriptString + "for(int i=0;i<ctx._source['participants'].size();i++) {";

		String script1 = "";

		if (parameters.get(DEBIT_NAME) != null) {
			script1 = script1 + "ctx._source.participants[i].name = params.debitName; ";
		}
		if (parameters.get(DEBIT_BANK_DATA) != null) {
			script1 = script1 + "ctx._source.participants[i].bankData = params.debitBankData; ";
		}
		if (parameters.get(DEBIT_UPI_DATA) != null) {
			script1 = script1 + "ctx._source.participants[i].upiData = params.debitUpiData; ";
		}
		if (parameters.get(DEBIT_USER_CARD_DATA) != null) {
			script1 = script1 + "ctx._source.participants[i].cardData = params.debitUserCardData; ";
		}
		if (parameters.get(DEBIT_CONTEXT_MAP) != null) {
			script1 = script1 + "ctx._source.participants[i].contextMap = params.debitContextMap; ";
		}

		if (StringUtils.isNotBlank(script1)) {
			scriptString = scriptString + "if(ctx._source.participants[i].txnIndicator == params.debit "
					+ "&& ctx._source.participants[i].paymentSystem == params.debitPaymentSystem) {" + script1 + "}";
		}

		String script2 = "";

		if (parameters.get(CREDIT_NAME) != null) {
			script2 = script2 + " ctx._source.participants[i].name = params.creditName; ";
		}
		if (parameters.get(CREDIT_BANK_DATA) != null) {
			script2 = script2 + " ctx._source.participants[i].bankData = params.creditBankData; ";
		}
		if (parameters.get(CREDIT_UPI_DATA) != null) {
			script2 = script2 + "ctx._source.participants[i].upiData = params.creditUpiData; ";
		}
		if (parameters.get(CREDIT_USER_CARD_DATA) != null) {
			script2 = script2 + "ctx._source.participants[i].cardData = params.creditUserCardData; ";
		}
		if (parameters.get(CREDIT_CONTEXT_MAP) != null) {
			script2 = script2 + "ctx._source.participants[i].contextMap = params.creditContextMap; ";
		}

		if (StringUtils.isNotBlank(script2)) {
			if (StringUtils.isNotBlank(script1)) {
				scriptString = scriptString + "else ";
			}
			scriptString = scriptString + "if (ctx._source.participants[i].txnIndicator == params.credit"
					+ "&& ctx._source.participants[i].paymentSystem == params.creditPaymentSystem) {" + script2 + "}";
		}

		String script3 = "";

		if (parameters.get(MERCHANT_NAME) != null) {
			script3 = script3 + " ctx._source.participants[i].name = params.merchantName; ";
		}
		if (parameters.get(MERCHANT_BANK_DATA) != null) {
			script3 = script3 + " ctx._source.participants[i].bankData = params.merchantBankData; ";
		}
		if (parameters.get(MERCHANT_UPI_DATA) != null) {
			script3 = script3 + "ctx._source.participants[i].upiData = params.merchantUpiData; ";
		}
		if (parameters.get(MERCHANT_CONTEXT_MAP) != null) {
			script3 = script3 + "ctx._source.participants[i].contextMap = params.merchantContextMap; ";
		}

		if (StringUtils.isNotBlank(script3)) {
			scriptString = scriptString + "if (ctx._source.participants[i].entityType == params.merchantEntityType) {"
					+ script3 + "}";
		}

		scriptString = scriptString + "}";

		if (parameters.containsKey(SEARCH_FIELDS) && ObjectUtils.isNotEmpty(parameters.get(SEARCH_FIELDS))) {
			scriptString += " ctx._source.searchFields = params.searchFields; ";
		}
		if (parameters.containsKey(LOCATION) && ObjectUtils.isNotEmpty(parameters.get(LOCATION))) {
			scriptString += " ctx._source.location = params.location; ";
		}

		if (parameters.containsKey(SPEND_ANALYTICS_FIELDS)
				&& ObjectUtils.isNotEmpty(parameters.get(SPEND_ANALYTICS_FIELDS))) {
			scriptString += " ctx._source.spendAnalyticsFields = params.spendAnalyticsFields; ";
		}

		if (parameters.containsKey(REFUND_DETAILS) && Objects.nonNull(parameters.get(REFUND_DETAILS))) {
			scriptString += " ctx._source.refundDetails = params.refundDetails; ";
		}

		if (parameters.containsKey(PARENT_DETAILS) && Objects.nonNull(parameters.get(PARENT_DETAILS))) {
			scriptString += " ctx._source.parentDetails = params.parentDetails; ";
		}

		if (parameters.containsKey(UMN) && Objects.nonNull(parameters.get(UMN))) {
			scriptString += " ctx._source.umn = params.umn; ";
		}

		// Adding searchable Identifiers in Request
		if (parameters.containsKey(SEARCHABLE_IDENTIFIERS) && Objects.nonNull(parameters.get(SEARCHABLE_IDENTIFIERS))) {
			scriptString += " ctx._source.searchAbleIdentifiers = params.searchAbleIdentifiers; ";
		}

		return scriptString;
	}

	private Map<String, Object> getParameters(final TransformedTransactionHistoryDetail tthd)
			throws JsonProcessingException {
		Map<String, Object> parameters = new HashMap<>();
		String isMergeDocument = tthd.getContextMap() == null ? null
				: tthd.getContextMap().getOrDefault(IS_MERGED_DOCUMENT, null);
		parameters.put(IS_MERGED_DOCUMENT, isMergeDocument);

		List<TransformedParticipant> walletTransformedParticipantList = new ArrayList<>();

		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					String upiDataString = objectMapper.writeValueAsString(participant.getUpiData());
					parameters.put(DEBIT_UPI_DATA, objectMapper.readValue(upiDataString, HashMap.class));
					String bankDataString = objectMapper.writeValueAsString(participant.getBankData());
					parameters.put(DEBIT_BANK_DATA, objectMapper.readValue(bankDataString, HashMap.class));
					// Upi via credit card handling
					String cardDataString = objectMapper.writeValueAsString(participant.getCardData());
					parameters.put(DEBIT_USER_CARD_DATA, objectMapper.readValue(cardDataString, HashMap.class));
					parameters.put(DEBIT_PAYMENT_SYSTEM, participant.getPaymentSystem());
					parameters.put(DEBIT_NAME, participant.getName());
				}

				// Upi lite handling
				if (Utility.isUpiLiteTxnAndPaymentInstrument(participant)) {
					parameters.put(DEBIT_CONTEXT_MAP, participant.getContextMap());
				}

				parameters.put(DEBIT, participant.getTxnIndicator());
			}
			else if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					String upiDataString = objectMapper.writeValueAsString(participant.getUpiData());
					parameters.put(CREDIT_UPI_DATA, objectMapper.readValue(upiDataString, HashMap.class));
					String bankDataString = objectMapper.writeValueAsString(participant.getBankData());
					parameters.put(CREDIT_BANK_DATA, objectMapper.readValue(bankDataString, HashMap.class));
					// Upi via credit card handling
					String cardDataString = objectMapper.writeValueAsString(participant.getCardData());
					parameters.put(CREDIT_USER_CARD_DATA, objectMapper.readValue(cardDataString, HashMap.class));
					parameters.put(CREDIT_PAYMENT_SYSTEM, participant.getPaymentSystem());
					parameters.put(CREDIT_NAME, participant.getName());
				}

				// Upi lite handling
				if (Utility.isUpiLiteTxnAndPaymentInstrument(participant)) {
					parameters.put(CREDIT_CONTEXT_MAP, participant.getContextMap());
				}
				parameters.put(CREDIT, participant.getTxnIndicator());
			}

			if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
				parameters.put(MERCHANT_NAME, participant.getName());

				String contextMapString = objectMapper.writeValueAsString(participant.getContextMap());
				parameters.put(MERCHANT_CONTEXT_MAP, objectMapper.readValue(contextMapString, HashMap.class));

				String upiDataString = objectMapper.writeValueAsString(participant.getUpiData());
				parameters.put(MERCHANT_UPI_DATA, objectMapper.readValue(upiDataString, HashMap.class));

				String bankDataString = objectMapper.writeValueAsString(participant.getBankData());
				parameters.put(MERCHANT_BANK_DATA, objectMapper.readValue(bankDataString, HashMap.class));

				parameters.put(MERCHANT_ENTITY_TYPE, EntityTypesEnum.MERCHANT.getEntityTypeKey());
			}
		}

		parameters.put(DOC_UPDATED_DATE, tthd.getDocUpdatedDate());
		if (tthd.getSearchFields() != null) {

			String searchFieldsJson = objectMapper.writeValueAsString(tthd.getSearchFields());
			parameters.put(SEARCH_FIELDS, objectMapper.readValue(searchFieldsJson, HashMap.class));
		}

		if (tthd.getSpendAnalyticsFields() != null) {
			String spendFieldsJson = objectMapper.writeValueAsString(tthd.getSpendAnalyticsFields());
			parameters.put(SPEND_ANALYTICS_FIELDS, objectMapper.readValue(spendFieldsJson, HashMap.class));
		}

		if (CollectionUtils.isNotEmpty(tthd.getRefundDetails())) {
			String refundDetailsJson = objectMapper.writeValueAsString(tthd.getRefundDetails());
			parameters.put(REFUND_DETAILS, objectMapper.readValue(refundDetailsJson, List.class));
		}

		if (Objects.nonNull(tthd.getParentDetails())) {
			String parentDetailsJson = objectMapper.writeValueAsString(tthd.getParentDetails());
			parameters.put(PARENT_DETAILS, objectMapper.readValue(parentDetailsJson, HashMap.class));
		}

		if (Utility.isUpiLiteTxn(tthd) || Utility.isUpiViaCcTxn(tthd) || Utility.isRecurringMandateTxn(tthd)) {
			Map<String, String> contextMap = new HashMap<>(tthd.getContextMap());
			contextMap.remove(IS_CHAT_DOCUMENT);

			parameters.put(CONTEXT_MAP, contextMap);
		}
		if (Utility.isValidLocation(tthd)) {
			String location = objectMapper.writeValueAsString(tthd.getLocation());
			parameters.put(LOCATION, objectMapper.readValue(location, HashMap.class));
		}

		if (StringUtils.isNotBlank(tthd.getUmn())) {
			parameters.put(UMN, tthd.getUmn());
		}

		// Adding searchable Identifiers in parameters for update request in ES
		if (CollectionUtils.isNotEmpty(tthd.getSearchAbleIdentifiers())) {
			parameters.put(SEARCHABLE_IDENTIFIERS, tthd.getSearchAbleIdentifiers());
		}

		return parameters;
	}

	private boolean needDocsMerging(final Set<TransformedTransactionHistoryDetail> enrichedDocs) {
		for (TransformedTransactionHistoryDetail thd : enrichedDocs) {
			if (TransactionSource.OMS.getTransactionSourceKey().equals(thd.getStreamSource()) && thd.getIsSource()
					&& Boolean.TRUE.equals(thd.getIsVisible())) {
				return true;
			}
		}
		return false;
	}

}