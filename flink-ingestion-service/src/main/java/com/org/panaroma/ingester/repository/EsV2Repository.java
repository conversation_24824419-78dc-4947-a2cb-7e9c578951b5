package com.org.panaroma.ingester.repository;

import static com.org.panaroma.ingester.monitoring.MonitoringConstants.ES_V2_TIME_TAKEN;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.RepoUtility;
import com.org.panaroma.ingester.cst.dto.EsGenericsResponse;
import com.org.panaroma.ingester.cst.dto.EsResponseMetadata;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Qualifier("EsV2Repository")
@Log4j2
public class EsV2Repository implements IRepository<Object, Object> {

	static RestHighLevelClient esV2RestHighLevelClient;

	private ObjectMapper objectMapper;

	private String paymentHistoryIndexAliasName;

	private String paymentHistoryIndexNamePrefix;

	private Integer esV2port;

	private List<String> esV2hostList;

	private final int socketTimeout;

	private final int connectTimeOut;

	private final int maxRetryTimeOut;

	private MetricsAgent metricsAgent;

	@Autowired
	public EsV2Repository(final ObjectMapper objectMapper, @Value("${es-port}") final Integer port,
			@Value("#{'${es-host-list}'.split(',')}") final List<String> hostList,
			@Value("${es-v2-port}") final Integer esV2port,
			@Value("#{'${es-v2-host-list}'.split(',')}") final List<String> esV2hostList,
			@Value("${payment-history-alias}") final String paymentHistoryIndexAliasName,
			@Value("${index.name.prefix}") final String paymentHistoryIndexNamePrefix,
			@Value("${es-socket-timeout:#{2000}}") final int socketTimeout,
			@Value("${es-max-retry-timeout:#{2000}}") final int maxRetryTimeOut,
			@Value("${es-connect-timeout:#{2000}}") final int connectTimeOut, final MetricsAgent metricsAgent)
			throws MalformedURLException {
		this.objectMapper = objectMapper;

		this.esV2port = esV2port;
		this.esV2hostList = esV2hostList;

		this.socketTimeout = socketTimeout;
		this.connectTimeOut = connectTimeOut;
		this.maxRetryTimeOut = maxRetryTimeOut;

		esV2RestHighLevelClient = new RestHighLevelClient(RepoUtility.buildRestClient(esV2hostList, esV2port,
				this.maxRetryTimeOut, this.connectTimeOut, this.socketTimeout));

		this.paymentHistoryIndexAliasName = paymentHistoryIndexAliasName;
		this.paymentHistoryIndexNamePrefix = paymentHistoryIndexNamePrefix;
		this.metricsAgent = metricsAgent;
	}

	@Override
	public List<TransformedTransactionHistoryDetail> fetchData(final SearchContext searchContext,
			final PaginationParams paginationParams, final boolean sortInAscending) throws Exception {
		return null;
	}

	@Override
	public List<Object> fetchData(final Object searchObject, final Object object) throws Exception {
		return null;
	}

	@Override
	public List<Object> fetchData(Object searchObject, Long txnDate, String tracingId) throws Exception {
		return null;
	}

	@Override
	public SearchResponse search(final SearchRequest searchRequest) throws Exception {
		ensureInitialized();
		SearchResponse searchResponse = null;
		long startTime = System.currentTimeMillis();
		searchResponse = esV2RestHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
		long endTime = System.currentTimeMillis();
		this.metricsAgent.pushTimeDiff(ES_V2_TIME_TAKEN, startTime, endTime);
		this.metricsAgent.recordReadCountAndLatencyForFlinkEs(startTime);
		return searchResponse;
	}

	@Override
	public Object fetchDataFromAlias(final Object searchObject, final Object object) throws Exception {
		return null;
	}

	@Override
	public List<Object> fetchDataOnlyFromAlias(final Object searchObject, final Object object) throws Exception {
		return null;
	}

	@Override
	public void updateTxnsForGrouping(final Set<Object> detailList, final String groupId) {

	}

	@Override
	public Object getRecordUsingUniqueId(final Object o) throws IOException {
		return null;
	}

	@Override
	public List<Object> fetchRecord(final Object searchObject, final Long txnDate, final String id) throws Exception {
		return null;
	}

	@Override
	public Long fetchCount(final Object searchObject, final SearchContext searchContext) {
		return null;
	}

	@Override
	public UpdateRequest createUserImageUpdateRequest(final Object detail) {
		return null;
	}

	@Override
	public void updateOtherPartyEntityIdRelatedFields(final Object tthd) {

	}

	@Override
	public UpdateRequest createUpdateRequestForTags(final Object tthd) {
		return null;
	}

	@Override
	public Object getStoredUniqueDoc(final String docId, final Long txnDate, final String entityId, final Object detail)
			throws IOException {
		return null;
	}

	private void ensureInitialized() {
		if (esV2RestHighLevelClient == null) {
			try {
				log.error("Es V2 Rest Client is null");
				esV2RestHighLevelClient = new RestHighLevelClient(RepoUtility.buildRestClient(esV2hostList, esV2port,
						this.maxRetryTimeOut, this.connectTimeOut, this.socketTimeout));
			}
			catch (Exception e) {
				log.error("Exception while creating Es V2 rest client. Exception :{}",
						CommonsUtility.exceptionFormatter(e));
			}
		}
	}

	@Override
	public EsGenericsResponse fetchDocUsingDocId(final String docId, final String indexName, final String routing,
			final Class outputClass) throws IOException {
		if (StringUtils.isBlank(docId) || StringUtils.isBlank(indexName) || Objects.isNull(outputClass)) {
			return null;
		}
		try {
			log.info("Getting data from ES for document id:{}", docId);
			ensureInitialized();
			GetRequest getRequest = new GetRequest().id(docId).index(indexName);

			if (StringUtils.isNotBlank(routing)) {
				getRequest.routing(routing);
			}
			long startTime = System.currentTimeMillis();
			GetResponse response = esV2RestHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
			this.metricsAgent.recordReadCountAndLatencyForFlinkEs(startTime);
			if (Boolean.FALSE.equals(response.isExists()) && Boolean.TRUE.equals(response.isSourceEmpty())) {
				log.info("No record found in ES with Doc ID:{} in indexName: {}", docId, indexName);
				return null;
			}
			String sourceAsString = response.getSourceAsString();
			log.info("docID: {}, foundData from Es: {}", docId, sourceAsString);

			EsResponseMetadata esResponseMetadata = new EsResponseMetadata();
			// check when no object found then does it have seqno or not
			esResponseMetadata.setSeqNo(response.getSeqNo());
			esResponseMetadata.setPrimaryTerm(response.getPrimaryTerm());
			EsGenericsResponse esGenericsResponse = new EsGenericsResponse();
			esGenericsResponse.setEsResponseMetadata(esResponseMetadata);
			esGenericsResponse.setObj(objectMapper.readValue(sourceAsString, outputClass));
			return esGenericsResponse;

		}
		catch (ElasticsearchStatusException e) {
			log.info("No index found for DocId: {}, indexName: {}", docId, indexName);
			return null;
		}
		catch (Exception ex) {
			log.error("Exception while fetching data for docId :{}, Exception:{}", docId,
					CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

}
