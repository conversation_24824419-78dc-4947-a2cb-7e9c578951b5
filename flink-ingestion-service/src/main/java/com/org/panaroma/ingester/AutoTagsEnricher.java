package com.org.panaroma.ingester;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.TagFeatureConstants.AUTO_TAGGING_FEATURE_ROLLOUT;
import static com.org.panaroma.commons.constants.Constants.FUTURE_LOG_REMOVER_IDENTIFIER;
import static com.org.panaroma.ingester.constants.PipelineConstants.INTELLIGENT_TAGS_CONFIGURATION_CACHE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.AUTO_TAGGING_FAILURE_COUNT;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.SOURCE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.TXN_TYPE;

import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.kafkaPushAPI.TagTypeEnum;
import com.org.panaroma.commons.entity.AutoTaggingDetails;
import com.org.panaroma.commons.enums.tag.SystemCategoryTagEnum;
import com.org.panaroma.commons.tags.IntelligentTagSuggestionService;
import com.org.panaroma.commons.tags.dto.TagConfigurationCache;
import com.org.panaroma.commons.tags.dto.TagDto;
import com.org.panaroma.commons.tags.dto.TagRule;
import com.org.panaroma.commons.utility.configurablePropertyUtility.BopConfigurablePropertiesHolder;
import com.org.panaroma.commons.utils.AutoTaggingUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.ingester.dao.ITagsDataDao;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.utils.RetryUtility;
import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class AutoTagsEnricher extends
		BroadcastProcessFunction<TransformedTransactionHistoryDetail, Map<String, Object>, TransformedTransactionHistoryDetail>
		implements FlatMapFunction<TransformedTransactionHistoryDetail, TransformedTransactionHistoryDetail>,
		Serializable {

	private final MetricsAgent metricsAgent;

	private RetryUtility retryUtility;

	private final ITagsDataDao tagsDataDao;

	private final RolloutStrategyHelper rolloutStrategyHelper;

	private final BopConfigurablePropertiesHolder configurablePropertiesHolder;

	private final IntelligentTagSuggestionService intelligentTagSuggestionService;

	private final TagConfigurationCache tagConfigurationCache;

	@Autowired
	public AutoTagsEnricher(final MetricsAgent metricsAgent, final RetryUtility retryUtility,
			final ITagsDataDao tagsDataDao, final RolloutStrategyHelper rolloutStrategyHelper,
			final BopConfigurablePropertiesHolder configurablePropertiesHolder,
			final IntelligentTagSuggestionService intelligentTagSuggestionService,
			final TagConfigurationCache tagConfigurationCache) {
		this.metricsAgent = metricsAgent;
		this.retryUtility = retryUtility;
		this.tagsDataDao = tagsDataDao;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.intelligentTagSuggestionService = intelligentTagSuggestionService;
		this.tagConfigurationCache = tagConfigurationCache;
	}

	@Override
	public void flatMap(TransformedTransactionHistoryDetail txn,
			Collector<TransformedTransactionHistoryDetail> collector) throws Exception {
		try {
			if (Objects.isNull(txn)) {
				log.warn("Null transaction received in AutoTagsEnricher");
				return;
			}

			boolean autoTaggingEnabledForThisUser = rolloutStrategyHelper
				.isUserWhiteListed(AUTO_TAGGING_FEATURE_ROLLOUT, txn.getEntityId());

			// If auto tagging feature is disabled, then skip auto tagging
			if (!autoTaggingEnabledForThisUser) {
				log.warn(FUTURE_LOG_REMOVER_IDENTIFIER
						+ "Auto tagging feature is disabled for the user. Skipping auto tagging for txnId: {}, userId: {}",
						txn.getTxnId(), txn.getEntityId());
				collector.collect(txn);
				return;
			}

			// Checking if auto tagging is applicable for the transaction
			if (!AutoTaggingUtility.isAutoTaggingApplicableForTxn(txn)) {
				log.warn(FUTURE_LOG_REMOVER_IDENTIFIER + "Auto tagging is not applicable for the txn txnId: {}",
						txn.getTxnId());
				collector.collect(txn);
				return;
			}

			log.warn("Auto tagging txns txnId: {}, userId: {}", txn.getTxnId(), txn.getEntityId());

			// Enrich the transaction with auto tags
			enrichTransactionWithAutoTags(txn);
		}
		catch (Exception ex) {
			log.error("Exception while adding auto tag on txn txnId: {}, ex: {}", txn.getTxnId(),
					CommonsUtility.exceptionFormatter(ex));

			// pushing to retry if some exception occurs
			pushErrorMetrics(txn);
			retryUtility.pushToRetryKafka(txn);
		}

		// Collect the txn
		collector.collect(txn);
	}

	// Method to enrich the transaction with auto tags PTH-228
	private void enrichTransactionWithAutoTags(final TransformedTransactionHistoryDetail txn) {
		// Get the auto tagging details
		AutoTaggingDetails autoTaggingDetails = tagsDataDao.getAutoTaggingDetails(txn);

		Set<String> autoTags = null;

		// If the auto tagging details are not null, then get the auto tags
		if (Objects.nonNull(autoTaggingDetails)) {
			autoTags = autoTaggingDetails.getTags();
		}
		else {
			// if auto tags from previous txns is null, find it using db configurations
			String autoTag = intelligentTagSuggestionService.getAutoTagSuggestionForTxn(txn);
			autoTags = new HashSet<>(Set.of(autoTag));
		}

		// Create the updated transformed tags
		List<TransformedTag> updatedTransformedTags = autoTags.stream().map(tag -> {
			TransformedTag transformedTag = new TransformedTag();
			transformedTag.setTag(tag);

			// If the auto tagging details are not null, then set the tag type as
			// AUTO_USER_TAG, else set it as System Tag
			if (Objects.nonNull(autoTaggingDetails)) {
				transformedTag.setTagType(TagTypeEnum.AUTO_USER_TAG.getTagTypeKey());
			}
			else {
				transformedTag.setTagType(TagTypeEnum.S.getTagTypeKey());
			}

			transformedTag.setCreatedDate(DateTimeUtility.currentTimeEpoch());
			return transformedTag;
		}).collect(Collectors.toList());

		txn.setTags(updatedTransformedTags);

		// If the search fields are not null, then set the search tags
		if (Objects.nonNull(txn.getSearchFields())) {
			txn.getSearchFields().setSearchTags(autoTags);
		}

		log.warn("Auto tagged txn txnId: {}, autoTag: {}", txn.getTxnId(), autoTags);
	}

	// Method to push error metrics
	private void pushErrorMetrics(final TransformedTransactionHistoryDetail tthd) {
		metricsAgent.incrementCount(AUTO_TAGGING_FAILURE_COUNT, TXN_TYPE + tthd.getMainTxnType(),
				SOURCE + tthd.getStreamSource());
	}

	@Override
	public void processElement(TransformedTransactionHistoryDetail value,
			BroadcastProcessFunction<TransformedTransactionHistoryDetail, Map<String, Object>, TransformedTransactionHistoryDetail>.ReadOnlyContext ctx,
			Collector<TransformedTransactionHistoryDetail> out) throws Exception {
		flatMap(value, out);
	}

	@Override
	public void processBroadcastElement(Map<String, Object> value,
			BroadcastProcessFunction<TransformedTransactionHistoryDetail, Map<String, Object>, TransformedTransactionHistoryDetail>.Context ctx,
			Collector<TransformedTransactionHistoryDetail> out) throws Exception {
		log.warn("Auto tag Enricher Received broadcast at : {}", value);

		Map<String, Map<TagRule, TagDto>> tagCache = (Map<String, Map<TagRule, TagDto>>) value
			.get(INTELLIGENT_TAGS_CONFIGURATION_CACHE);
		if (MapUtils.isNotEmpty(tagCache)) {
			tagConfigurationCache.updateCache(tagCache);
		}

		this.configurablePropertiesHolder.addProperties(value);
	}

}
