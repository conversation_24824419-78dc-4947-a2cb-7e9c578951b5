package com.org.panaroma.ingester.utils;

import static com.org.panaroma.commons.constants.BankDataConstants.PAYTM_BANK_IFSC;
import static com.org.panaroma.commons.constants.Constants.IS_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.SELF_TRANSFER_KEYWORD;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;

import com.org.panaroma.commons.dto.AccountTypeEnum;
import com.org.panaroma.commons.dto.CcEmiStatusEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnCategoryEnum;
import com.org.panaroma.commons.dto.cart.CartItems;
import com.org.panaroma.commons.dto.es.SearchFields;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.UtilityExtension;
import java.util.HashSet;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@Log4j2
public class DroolsUtility {

	public static void updateSearchTagField(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd) && Objects.nonNull(tthd.getTags())) {
			tthd.getSearchFields()
				.setSearchTags(tthd.getTags().stream().map(TransformedTag::getTag).collect(Collectors.toSet()));
		}
	}

	public static void setUthVisibleTxnIndicator(final TransformedTransactionHistoryDetail tthd) {
		TransactionIndicator txnIndicator = Utility.getUthVisibleTransactionIndicator(tthd);

		if (Objects.nonNull(txnIndicator) && Objects.nonNull(tthd) && Objects.nonNull(tthd.getSearchFields())) {
			tthd.getSearchFields().setTxnIndicator(txnIndicator.getTransactionIndicatorKey());
		}
	}

	public static void setSearchTxnCategory(final TransformedTransactionHistoryDetail tthd) {

		if (Objects.nonNull(tthd) && Objects.nonNull(tthd.getContextMap())
				&& tthd.getContextMap().containsKey(IS_SELF_TRANSFER)
				&& TRUE.equalsIgnoreCase(tthd.getContextMap().get(IS_SELF_TRANSFER))) {
			tthd.getSearchFields().setSearchTxnCategory(TxnCategoryEnum.SELF_TRANSFER.getTxnCategoryUserViewValue());
			log.info("Inside self transfer block searchTxnCategory for txnId {} is : {}", tthd.getTxnId(),
					tthd.getSearchFields().getSearchTxnCategory());
		}
		else {
			tthd.getSearchFields()
				.setSearchTxnCategory(TxnCategoryEnum.getTxnCategoryUserViewVauFromTxnType(
						TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType())));
			log.info("SearchTxnCategory for txnId {} is : {}", tthd.getTxnId(),
					tthd.getSearchFields().getSearchTxnCategory());
		}
	}

	public static void updateSelfAccountType(final TransformedTransactionHistoryDetail tthd,
			final TransformedParticipant selfParticipant) {
		if (selfParticipant.getBankData().getAccountType() != null) {
			tthd.getSearchFields()
				.getSearchSelfAccTyp()
				.add(String.valueOf(selfParticipant.getBankData().getAccountType()));

			AccountTypeEnum accountTypeEnum = AccountTypeEnum
				.getAccountTypeEnumByKey(selfParticipant.getBankData().getAccountType());
			if (ObjectUtils.isEmpty(accountTypeEnum)) {
				return;
			}

			if (accountTypeEnum.getViewValue() != null
					&& PAYTM_BANK_IFSC.equals(selfParticipant.getBankData().getIfsc())) {
				tthd.getSearchFields()
					.getSearchSelfAccTyp()
					.addAll(AccountTypeEnum.getAccountTypeEnumByKey(selfParticipant.getBankData().getAccountType())
						.getViewValue());
			}
		}
	}

	public static void updateSearchKeywordsForSelfTransfer(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd.getSearchFields())) {
			tthd.setSearchFields(new SearchFields());
		}
		if (Objects.isNull(tthd.getSearchFields().getSearchKeywords())) {
			tthd.getSearchFields().setSearchKeywords(new HashSet<>());
		}
		tthd.getSearchFields().getSearchKeywords().add(SELF_TRANSFER_KEYWORD);
	}

	public static void updateEmiStatus(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd)) {
			return;
		}

		TransformedParticipant userParticipant = UtilityExtension.getUserParticipant(tthd);

		if (Objects.isNull(userParticipant)) {
			return;
		}

		if (Objects.nonNull(userParticipant.getUpiData())
				&& Objects.nonNull(userParticipant.getUpiData().getUpiCcInfo())
				&& Objects.nonNull(userParticipant.getUpiData().getUpiCcInfo().getEmiInfo())
				&& StringUtils.isNotBlank(userParticipant.getUpiData().getUpiCcInfo().getEmiInfo().getStatus())) {

			CcEmiStatusEnum ccEmiStatusEnum = CcEmiStatusEnum
				.getEmiStatusEnumByValue(userParticipant.getUpiData().getUpiCcInfo().getEmiInfo().getStatus());

			if (Objects.isNull(ccEmiStatusEnum)) {
				tthd.getSearchFields().setEmiStatus(null);
				log.warn("Invalid emi status received for txnId : {}, Invalid value : {}", tthd.getTxnId(),
						userParticipant.getUpiData().getUpiCcInfo().getEmiInfo().getStatus());
			}
			else {
				tthd.getSearchFields()
					.setEmiStatus(userParticipant.getUpiData().getUpiCcInfo().getEmiInfo().getStatus());
			}
		}
	}

	public static void updateVerticalIdToSearchFields(final TransformedTransactionHistoryDetail tthd) {

		if (Objects.nonNull(tthd) && Objects.nonNull(tthd.getSearchFields())) {
			tthd.getSearchFields()
				.setVerticalIds(tthd.getCartDetails()
					.getItems()
					.stream()
					.map(CartItems::getVerticalId)
					.filter(Objects::nonNull)
					.collect(Collectors.toSet()));
		}
	}

}
