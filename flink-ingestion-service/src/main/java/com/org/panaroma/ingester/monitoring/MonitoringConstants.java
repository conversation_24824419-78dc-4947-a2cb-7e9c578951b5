package com.org.panaroma.ingester.monitoring;

public class MonitoringConstants {

	public static final String INGESTER_APP = "application:Ingester";

	public static final String REASON_FOR_NOT_VISIBLE = "reasonForNotVisible";

	public static final String IS_VISIBLE_FALSE_COUNT = "IS_VISIBLE_FALSE_COUNT";

	public static final String TXN_TYPE = "txnType:";

	public static final String SOURCE = "source:";

	public static final String POPULATED_FROM = "populatedFrom:";

	public static final String FILTERED_EVENT = "filteredEvent:";

	public static final String EVENT_TYPE = "eventType:";

	public static final String REQUEST_TYPE = "requestType";

	public static final String STOPPED = "stopped:";

	public static final String PAYMENT_SYSTEM = "paymentSystem:";

	public static final String BUCKET = "bucket:";

	public static final String RETRY_COUNT = "retryCount";

	public static final String RESPONSE_CODE = "responseCode:";

	public static final String STATUS = "status:";

	public static final String CREATED_TXN_DATE_DIFF = "CREATED_TXN_DATE_DIFF";

	public static final String END_STATUS_TXN_DATE_DIFF = "END_STATUS_TXN_DATE_DIFF";

	public static final String UPDATED_DATE_CURRENT_DATE_DIFF = "UPDATED_DATE_CURRENT_DATE_DIFF";

	public static final String UPDATED_DATE_CURRENT_DATE_DIFF_AT_LAG_DATA_PUBLISHER = "UPDATED_DATE_CURRENT_DATE_DIFF_AT_LAG_DATA_PUBLISHER";

	public static final String COUNT_PER_STATUS_SOURCE = "COUNT_PER_STATUS_SOURCE";

	public static final String NUM_OF_LINKED_EVENTS_CAME_FOR_UPDATE = "NUM_OF_LINKED_EVENTS_CAME_FOR_UPDATE";

	public static final String ACTUAL_NUM_OF_LINKED_EVENTS_UPDATED = "ACTUAL_NUM_OF_LINKED_EVENTS_UPDATED";

	public static final String CART_ORDER_DETAIL_FETCH_API_COUNT = "CART_ORDER_DETAIL_FETCH_API_COUNT";

	public static final String CART_ORDER_DETAIL_FETCH_API_FAILURE_COUNT = "CART_ORDER_DETAIL_FETCH_API_FAILURE_COUNT";

	public static final String CART_ORDER_DETAIL_FETCH_API_TIME_TAKEN = "CART_ORDER_DETAIL_FETCH_API_TIME_TAKEN";

	public static final String CART_TOKEN_FETCHER_API_COUNT = "CART_TOKEN_FETCHER_API_COUNT";

	public static final String CART_TOKEN_FETCHER_API_FAILURE_COUNT = "CART_TOKEN_FETCHER_API_FAILURE_COUNT";

	public static final String CART_TOKEN_FETCHER_API_TIME_TAKEN = "CART_TOKEN_FETCHER_API_TIME_TAKEN";

	public static final String FAILURE = "failure";

	public static final String STATUS_CHECK_API_COUNT = "STATUS_CHECK_API_COUNT";

	public static final String STATUS_CHECK_API_EXECUTION_TIME = "STATUS_CHECK_API_EXECUTION_TIME";

	public static final String STATUS_CHECK_API_RESPONSE_CODE_COUNT = "STATUS_CHECK_API_RESPONSE_CODE_COUNT";

	public static final String CLIENT_TAG = "client";

	public static final String SEPARATOR = ":";

	public static final String USER_IMAGE_URL_RECORDS_IN_CACHE = "USER_IMAGE_URL_RECORDS_IN_CACHE";

	public static final String USER_IMAGE_URL_NULL_RECORDS_IN_CACHE = "USER_IMAGE_URL_NULL_RECORDS_IN_CACHE";

	public static final String USER_IMAGE_URL_RECORDS_MISSING_IN_CACHE = "USER_IMAGE_URL_RECORDS_MISSING_IN_CACHE";

	public static final String USER_IMAGE_URL_RECORDS_IN_OAUTH = "USER_IMAGE_URL_RECORDS_IN_OAUTH";

	public static final String USER_IMAGE_URL_RECORDS_NOT_IN_OAUTH = "USER_IMAGE_URL_RECORDS_NOT_IN_OAUTH";

	public static final String TXN_COUNT_TO_REPROCESS_VIA_RECON = "TXN_COUNT_TO_REPROCESS_VIA_RECON";

	public static final String REPROCESSED_NON_MERGED_OMS_EVENTS_COUNT = "REPROCESSED_NON_MERGED_OMS_EVENTS_COUNT";

	public static final String INVISIBLE_MERGED_OMS_EVENTS_COUNT = "INVISIBLE_MERGED_OMS_EVENTS_COUNT";

	public static final String UPI_ONUS_PENDING_EVENTS_COUNT = "UPI_ONUS_PENDING_EVENTS_COUNT";

	public static final String TXN_COUNT_PER_CRON_TAG = "TXN_COUNT_PER_CRON_TAG";

	public static final String REQUEST_TO_FORM_SEARCH_FIELDS_COUNT = "REQUEST_TO_FORM_SEARCH_FIELDS_COUNT";

	public static final String DATA_PUSH_INTO_RETRY_PIPELINE_COUNT = "DATA_PUSH_INTO_RETRY_PIPELINE_COUNT";

	public static final String RETRY_LIMIT_EXCEEDED_COUNT = "RETRY_LIMIT_EXCEEDED_COUNT";

	public static final String SEARCH_FIELDS_FORMATION_FAILURE_COUNT = "SEARCH_FIELDS_FORMATION_FAILURE_COUNT";

	public static final String ES_TIME_TAKEN = "ES_TIME_TAKEN";

	public static final String ES_V2_TIME_TAKEN = "ES_V2_TIME_TAKEN";

	public static final String USER_IMAGE_OAUTH_API_RESPONSE_CODE_COUNT = "USER_IMAGE_OAUTH_API_RESPONSE_CODE_COUNT";

	public static final String SUCCESSFULLY_FETCHED_ACC_REF_NUM_FROM_BMS_COUNT = "SUCCESSFULLY_FETCHED_ACC_REF_NUM_FROM_BMS_COUNT";

	public static final String SUCCESSFULLY_FETCHED_ACC_REF_NUM_FROM_PMS_COUNT = "SUCCESSFULLY_FETCHED_ACC_REF_NUM_FROM_PMS_COUNT";

	public static final String BMS_SERVICE_FAILURE_RESPONSE_COUNT = "BMS_SERVICE_FAILURE_RESPONSE_COUNT";

	public static final String PMS_SERVICE_FAILURE_RESPONSE_COUNT = "PMS_SERVICE_FAILURE_RESPONSE_COUNT";

	public static final String RESP_OBJ_MADE_FROM_RESP_FROM_BMS_SERVICE_IS_NULL = "RESP_OBJ_MADE_FROM_RESP_FROM_BMS_SERVICE_IS_NULL";

	public static final String RESP_OBJ_MADE_FROM_RESP_FROM_PMS_SERVICE_IS_NULL = "RESP_OBJ_MADE_FROM_RESP_FROM_PMS_SERVICE_IS_NULL";

	public static final String BMS_SERVICE_NULL_RESP_COUNT = "BMS_SERVICE_NULL_RESP_COUNT";

	public static final String PMS_SERVICE_NULL_RESP_COUNT = "PMS_SERVICE_NULL_RESP_COUNT";

	public static final String INVALID_THD_FROM_SOURCE = "INVALID_THD_FROM_SOURCE";

	public static final String REPORT_CODE_TAG = "reportCode:";

	public static final String EXCEPTION_WHILE_PUSHING_DATA_TO_PG_KAFKA = "EXCEPTION_WHILE_PUSHING_DATA_TO_PG_KAFKA";

	public static final String EXCEPTION_WHILE_PUSHING_DATA_TO_RETRY_KAFKA = "EXCEPTION_WHILE_PUSHING_DATA_TO_RETRY_KAFKA";

	public static final String NOT_PUSHING_TO_RETRY_TOPIC_AFTER_CERTAIN_RETRIES = "NOT_PUSHING_TO_RETRY_TOPIC_AFTER_CERTAIN_RETRIES";

	public static final String UNSUCCESSFUL_CART_ENRICHER_COUNT = "UNSUCCESSFUL_CART_ENRICHER_COUNT";

	public static final String PARENT_FOUND_FOR_NET_BANKING_RC = "PARENT_FOUND_FOR_NET_BANKING_RC";

	public static final String FETCHING_PARENT_FOR_NET_BANKING_RC = "FETCHING_PARENT_FOR_NET_BANKING_RC";

	public static final String COUNT = "count:";

	public static final String DTOS_ARE_SAME_COUNT = "DTOS_ARE_SAME_COUNT";

	public static final String ES_REJECTED_EXECUTION_EXCEPTION = "ES_REJECTED_EXECUTION_EXCEPTION";

	public static final String NOT_INDEX_REQUEST_OBJECT = "NOT_INDEX_REQUEST_OBJECT";

	public static final String ES_PARSING_EXCEPTION = "ES_PARSING_EXCEPTION";

	public static final String ES_VERSION_CONFLICT_EXCEPTION = "ES_VERSION_CONFLICT_EXCEPTION";

	public static final String GENERIC_ES_EXCEPTION = "GENERIC_ES_EXCEPTION";

	public static final String GENERIC_EXCEPTION_IN_ES_SINK = "GENERIC_EXCEPTION_IN_ES_SINK";

	public static final String RETRY_COUNT_FROM_ES_SINK = "RETRY_COUNT_FROM_ES_SINK";

	public static final String UDIR_RETRY_COUNT_SLAB = "UDIR_RETRY_COUNT_SLAB";

	public static final String ACC_REF_ID_HIT_COUNT = "ACC_REF_ID_HIT_COUNT";

	public static final String AEROSPIKE_API_NAME = "aeApiName:";

	public static final String FOUND_IN_CACHE = "foundInCache:";

	public static final String FOUND_FROM_PMS = "foundFromPms:";

	public static final String NOT_FOUND = "notFound:";

	public static final String TRUE = "true";

	public static final String FALSE = "false";

	public static final String RETRY_EVENTS_COUNT = "RETRY_EVENTS_COUNT";

	public static final String RETRY_COUNT_VALUE = "retryCountValue:";

	public static final String CLASS_NAME = "className:";

	public static final String RETRY_ABLE = "retryAble:";

	public static final String RETRY_WITHOUT_INCREASING_COUNT = "retryWithoutIncreasingCount";

	public static final String ACC_REF_ID_EXCEPTION_COUNT = "ACC_REF_ID_EXCEPTION_COUNT";

	public static final String EVENT_SOURCE = "eventSource:";

	public static final String UNKNOWN_EXCEPTION = "unknownException:";

	public static final String RECEIVED_VAN_EVENT = "RECEIVED_VAN_EVENT";

	public static final String XML_TO_JSON_DESERIALIZER_EXCEPTION = "XML_TO_JSON_DESERIALIZER_EXCEPTION";

	public static final String UPI_RECON_XML_RECEIVED_EVENT = "UPI_RECON_XML_RECEIVED_EVENT";

	public static final String XML_TO_JSON_CONVERTER_EXCEPTION = "XML_TO_JSON_CONVERTER_EXCEPTION";

	public static final String UPI_RECON_MAPPED_OBJECT = "UPI_RECON_MAPPED_OBJECT";

	public static final String PMS_API = "PMS_API";

	public static final String CACHE_SEARCH = "CACHE_SEARCH";

	public static final String UNDERSCORE_TIME_TAKEN = "_TIME_TAKEN";

	public static final String TRANSFORMER_EXECUTION = "TRANSFORMER_EXECUTION";

	public static final String SWITCH_EXECUTION = "SWITCH_EXECUTION";

	public static final String PIPELINE_NAME = "pipelineName:";

	public static final String EXP_MESSAGE = "expMessage:";

	public static final String EXP_CLASS = "expClass:";

	public static final String EXP_LAST_CAUSE = "expLastCause:";

	public static final String EXCEPTION_IN_PIPELINE = "EXCEPTION_IN_PIPELINE";

	public static final String ADAPTOR_NAME = "adaptorName:";

	public static final String TRANSFORMER_NAME = "transformerName:";

	public static final String EXCEPTION_TYPE = "exceptionType";

	public static final String OTHER = "other";

	public static final String ES_SINK_EXCEPTION_COUNT = "ES_SINK_EXCEPTION_COUNT";

	public static final String P2M_DOC_FETCH_COUNT_TO_POPULATE_REFUND_DETAILS = "P2M_DOC_FETCH_COUNT_TO_POPULATE_REFUND_DETAILS";

	public static final String COUNT_P2M_REFUND_TXNS_WITHOUT_P2M_TXN = "COUNT_P2M_REFUND_TXNS_WITHOUT_P2M_TXN";

	public static final String P2M_AND_P2M_REFUND_DATE_DIFF = "P2M_AND_P2M_REFUND_DATE_DIFF";

	public static final String VERSION_CONFLICT = "version_conflict";

	public static final String DOCUMENT_MISSING = "document_missing";

	public static final String DYNAMO_DB_QUERY_TIME = "DYNAMO_DB_QUERY_TIME";

	public static final String TABLE = "table:";

	public static final String DESERIALIZATION_EXCEPTION = "SERIALISATION_EXCEPTION";

	public static final String DESERIALIZER = "serializer:";

	public static final String DYNAMO_DESERIALIZER = "DYNAMO_DESERIALIZER";

	public static final String CHAT_EVENTS_COUNT = "CHAT_EVENTS_COUNT";

	public static final String OTHER_PARTY_ENTITY_ID_RELATED_FIELDS_NOT_UPDATED_IN_CHAT_PIPELINE = "OTHER_PARTY_ENTITY_ID_RELATED_FIELDS_NOT_UPDATED_IN_CHAT_PIPELINE";

	public static final String EXTRACTED_STORED_DOC_FOR_PG_TWICE = "EXTRACTED_STORED_DOC_FOR_PG_TWICE";

	public static final String EXTRACTED_TXN_DATE_FROM_PG_NULL = "EXTRACTED_TXN_DATE_FROM_PG_NULL";

	public static final String SCRIPT_REQUEST = "scriptRequest";

	public static final String REQUEST_RETRY_COUNT = "requestRetryCount";

	public static final String SPEND_FIELDS_FORMATION_FAILURE_COUNT = "SPEND_FIELDS_FORMATION_FAILURE_COUNT";

	public static final String ES_SPEND_DOC_INDEX_FAILURE_COUNT = "ES_SPEND_DOC_INDEX_FAILURE_COUNT";

	public static final String SPEND_INDEX_REQUEST = "spendIndexRequest:";

	public static final String TAG_EXCEPTION_COUNT = "TAG_EXCEPTION_COUNT";

	public static final String TAG_AGG_DELAY = "TAG_AGG_DELAY";

	public static final String DELAY_IN_STORING_TAG_IN_DB = "DELAY_IN_STORING_TAG_IN_DB";

	public static final String PUSHED_TO_FILTER_EVENT_PIPELINE = "PUSHED_TO_FILTER_EVENT_PIPELINE";

	public static final String EXCEPTION_WHILE_APPLYING_PREPROCESSOR = "EXCEPTION_WHILE_APPLYING_PREPROCESSOR";

	public static final String INVALID_STREAM = "INVALID_STREAM";

	public static final String FIELD = "field";

	public static final String PENDING_TXN_UPDATED_DATE_ISSUE = "PENDING_TXN_UPDATED_DATE_ISSUE";

	public static final String PENDING_TXN_UPDATED_DATE_ISSUE_FOR_PART = "PENDING_TXN_UPDATED_DATE_ISSUE_FOR_PART";

	public static final String ERROR_CREATING_INDEX_REQ = "ERROR_CREATING_INDEX_REQ";

	public static final String DC_EVENT_COUNT = "DC_EVENT_COUNT";

	public static final String PUSHED_FOR_RETRY = "PUSHED_FOR_RETRY";

	public static final String INWARD_TXN_EVENTS_FILTER = "INWARD_TXN_EVENTS_FILTER";

	public static final String PARTIAL_EMI_EVENT_PUSH_INTO_RETRY_PIPELINE_COUNT = "PARTIAL_EMI_EVENT_PUSH_INTO_RETRY_PIPELINE_COUNT";

	public static final String GROUPING_GET_REQUESTS_SERVED = "GROUPING_GET_REQUESTS_SERVED";

	public static final String PUSHED_FROM_RETRY_KAFKA_TO_CACHE_UPDATER = "PUSHED_FROM_RETRY_KAFKA_TO_CACHE_UPDATER";

	public static final String MERGING_GET_REQUESTS_SERVED = "MERGING_GET_REQUESTS_SERVED";

	public static final String FROM = "from:";

	public static final String CACHE = "cache";

	public static final String ES = "es";

	public static final String TXN_DATE_CURRENT_DATE_DIFF_DAYS = "TXN_DATE_CURRENT_DATE_DIFF_DAYS";

	public static final String DAYS = "days";

	public static final String ES_SINK_TIME = "ES_SINK_TIME";

	public static final String CHAT_RETRY_COUNT = "CHAT_RETRY_COUNT";

	public static final String CHAT_DATA_API = "chatDataApi";

	public static final String CHAT_DATA_PUBLISH_DELAY = "CHAT_DATA_PUBLISH_DELAY";

	public static final String CHAT_RES_RECEIVED = "chatResReceived:";

	public static final String CHAT_PUBLISH_RETRY_COUNT = "chatPublishRetryCount:";

	public static final String CHAT_DATA_API_TIME_TAKEN = "CHAT_DATA_API_TIME_TAKEN";

	public static final String CHAT_DATA_API_FAILURE_COUNT = "CHAT_DATA_API_FAILURE_COUNT";

	public static final String PG_RETRY_COUNT = "PG_RETRY_COUNT";

	public static final String PG_DATA_API = "pgDataApi";

	public static final String PG_DATA_PUBLISH_DELAY = "PG_DATA_PUBLISH_DELAY";

	public static final String PG_RES_RECEIVED = "pgResReceived:";

	public static final String PG_PUBLISH_RETRY_COUNT = "pgPublishRetryCount:";

	public static final String PG_DATA_API_TIME_TAKEN = "PG_DATA_API_TIME_TAKEN";

	public static final String PG_DATA_API_FAILURE_COUNT = "PG_DATA_API_FAILURE_COUNT";

	public static final String RECAP_API_TIME_TAKEN = "RECAP_API_TIME_TAKEN";

	public static final String RECAP_API_REST_CLIENT_EXCEPTION = "RECAP_API_REST_CLIENT_EXCEPTION";

	public static final String HTTP_CLIENT_CLOSED_EXCEPTION = "HTTP_CLIENT_CLOSED_EXCEPTION";

	public static final String RECAP_RESPONSE_CACHE_POPULATION_EXCEPTION = "RECAP_RESPONSE_CACHE_POPULATION_EXCEPTION";

	public static final String PUSHING_TO_KAFKA_FOR_RETRY = "PUSHING_TO_KAFKA_FOR_RETRY";

	public static final String RECAP_PUSHING_TO_KAFKA_FOR_RETRY_LIMIT_EXCEED = "RECAP_PUSHING_TO_KAFKA_FOR_RETRY_LIMIT_EXCEED";

	public static final String PG_OMS_TXN_EVENT_COUNT = "PG_OMS_TXN_EVENT_COUNT";

	public static final String INCOMING_EVENTS_COUNT = "INCOMING_EVENTS_COUNT";

	public static final String MANDATE_INDEX_REQUEST = "mandateIndexRequest:";

	public static final String MANDATE_DATA_PUSHED_TO_RETRY_KAFKA = "MANDATE_DATA_PUSHED_TO_RETRY_KAFKA";

	public static final String MANDATE_THD_PUSHED_TO_KAFKA_FOR_RETRY = "MANDATE_THD_PUSHED_TO_KAFKA_FOR_RETRY";

	public static final String EXCEPTION_WHILE_PUSHING_MANDATE_DATA_TO_RETRY_KAFKA = "EXCEPTION_WHILE_PUSHING_MANDATE_DATA_TO_RETRY_KAFKA";

	public static final String EXCEPTION_WHILE_PUSHING_MANDATE_THD_TO_KAFKA_FOR_RETRY = "EXCEPTION_WHILE_PUSHING_MANDATE_THD_TO_KAFKA_FOR_RETRY";

	public static final String FLINK_SIDE_ES_HIT_COUNT = "flinkEsCnt";

	public static final String FLINK_SIDE_AEROSPIKE_HIT_COUNT = "flinkAeroCnt";

	public static final String FLINK_SIDE_ES_LATENCY = "flinkEsLatency";

	public static final String FLINK_SIDE_AEROSPIKE_LATENCY = "flinkAeroLatency";

	public static final String FLINK_ES = "flinkEs";

	public static final String FLINK_AEROSPIKE = "flinkAero";

	public static final String READ_COUNT = "readCnt";

	public static final String READ_LATENCY = "readLatency";

	public static final String WRITE_COUNT = "writeCnt";

	public static final String WRITE_LATENCY = "writeLatency";

	public static final String DELETE_COUNT = "deleteCnt";

	public static final String DELETE_LATENCY = "deleteLatency";

	public static final String EXCEPTION_IN_GETTING_MANDATE_DATA_FROM_AEROSPIKE = "EXCEPTION_IN_GETTING_MANDATE_DATA_FROM_AEROSPIKE";

	public static final String EXCEPTION_IN_GETTING_MANDATE_DATA_FROM_ES = "EXCEPTION_IN_GETTING_MANDATE_DATA_FROM_ES";

	public static final String EXCEPTION_WHILE_CREATING_MANDATE_DOC = "EXCEPTION_WHILE_CREATING_MANDATE_DOC";

	public static final String EXCEPTION_WHILE_GETTING_PARENT_MANDATE_DOC = "EXCEPTION_WHILE_GETTING_PARENT_MANDATE_DOC";

	public static final String EXCEPTION_WHILE_ENRICHING_MANDATE_DOC = "EXCEPTION_WHILE_ENRICHING_MANDATE_DOC";

	public static final String PARENT_DOC_NOT_FOUND_FOR_NON_CREATE_EVENT = "PARENT_DOC_NOT_FOUND_FOR_NON_CREATE_EVENT";

	public static final String PARENT_DOC_MISSING_FOR_PORT_OUT_EVENT = "PARENT_DOC_MISSING_FOR_PORT_OUT_EVENT";

	public static final String UPDATED_DATE_OF_STORED_PARENT_DOC_IS_GREATER = "UPDATED_DATE_OF_STORED_PARENT_DOC_IS_GREATER";

	public static final String CREATE_DOC_NOT_FOUND_FOR_FIRST_EXEC_FAIL = "CREATE_DOC_NOT_FOUND_FOR_FIRST_EXEC_FAIL";

	public static final String LAST_FAILED_COLLECT_DOC_NOT_FOUND_WITH_SAME_EXEC_NO = "LAST_FAILED_COLLECT_DOC_NOT_FOUND_WITH_SAME_EXEC_NO";

	public static final String UPI_PART_NOT_AVBL_ERROR = "UPI_PART_NOT_AVBL_ERROR";

	public static final String ES_ERROR = "esError:";

	public static final String UPDATING_DOC_ERROR = "updatingDocError";

	public static final String UPI_PART_NOT_AVBL_COUNT = "UPI_PART_NOT_AVBL_COUNT";

	public static final String RETRY_MORE_THAN_LIMIT_COUNT = "RETRY_MORE_THAN_LIMIT_COUNT";

	public static final String MANDATE_BACK_FILLING_EVENT_COUNT = "MANDATE_BACK_FILLING_EVENT_COUNT";

	public static final String PMS_CUST_ID_API_HIT_COUNT = "PMS_CUST_ID_API_HIT_COUNT";

	public static final String PMS_CUST_ID_API_FAILURE_COUNT = "PMS_CUST_ID_API_FAILURE_COUNT";

	public static final String PMS_CUST_ID_API_RESPONSE_TIME = "PMS_CUST_ID_API_RESPONSE_TIME";

	public static final String AUTO_TAGGING_FAILURE_COUNT = "AUTO_TAGGING_FAILURE_COUNT";

	public static final String SET = "set:";

	public static final String ACTION = "action:";

	public static final String READ = "read";

	public static final String WRITE = "write";

	public static final String DELETE = "delete";

}