package com.org.panaroma.ingester.transformer.esGenericInsertUpdateExecutor;

import static com.org.panaroma.ingester.constants.Constants.COLON;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.PUSHED_TO_FILTER_EVENT_PIPELINE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.SOURCE;

import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.ingester.dto.insertUpdateExecutorDtos.GenericUpdateInsertDto;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.utils.Utility;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class DualWriteMonitoringAndFiltering
		implements FlatMapFunction<GenericUpdateInsertDto, GenericUpdateInsertDto>, Serializable {

	private MetricsAgent metricsAgent;

	private Long currentDateAndTxnDateDiffLimit;

	@Autowired
	DualWriteMonitoringAndFiltering(
			@Value("${txn.event.max.days.diff.from.current.to.process.txn}") final Long currentDateAndTxnDateDiffLimit,
			final MetricsAgent metricsAgent) {
		this.currentDateAndTxnDateDiffLimit = currentDateAndTxnDateDiffLimit;
		this.metricsAgent = metricsAgent;
	}

	@Override
	public void flatMap(final GenericUpdateInsertDto genericUpdateInsertDto,
			final Collector<GenericUpdateInsertDto> collector) throws Exception {
		try {
			if (genericUpdateInsertDto == null || genericUpdateInsertDto.getRequestType() == null) {
				return;
			}
			// TODO :- remove full object from log after QA testing,instead print ids
			// only.
			log.warn("Event received in es-insert-update-executor pipeline : {}", genericUpdateInsertDto);
			TransformedTransactionHistoryDetail tthd = genericUpdateInsertDto.getTthd();
			if (Objects.nonNull(genericUpdateInsertDto.getTthd())) {
				if (Utility.isEventOlderThanLimit(tthd.getTxnDate(), currentDateAndTxnDateDiffLimit, tthd)
						&& com.org.panaroma.commons.utils.Utility.isSkipOldestEvent(tthd)) {
					log.warn("Event received in DualWriteFilter Tthd : {}, systemId : {}, TimeDiff : {} days", tthd,
							tthd.getTxnId(), (new Date().getTime() - tthd.getTxnDate()) / (1000 * 60 * 60 * 24));
					String tags = SOURCE + COLON + tthd.getStreamSource();
					metricsAgent.incrementCount(PUSHED_TO_FILTER_EVENT_PIPELINE, tags);
					return;
				}
			}

			if (Objects.nonNull(genericUpdateInsertDto.getBulkUpdateDto())
					&& Objects.nonNull(genericUpdateInsertDto.getBulkUpdateDto().getCustomUpdateParams())) {
				genericUpdateInsertDto.getBulkUpdateDto()
					.getCustomUpdateParams()
					.removeIf(customUpdateParams -> Utility.isEventOlderThanLimit(customUpdateParams.getTxnDate(),
							currentDateAndTxnDateDiffLimit)
							&& com.org.panaroma.commons.utils.Utility.isSkipOldestEvent(tthd));
			}
			collector.collect(genericUpdateInsertDto);
		}
		catch (Exception e) {
			log.error("Exception while applying filter for dualWrite for : {}, Exception : {}", genericUpdateInsertDto,
					CommonsUtility.exceptionFormatter(e));
			collector.collect(genericUpdateInsertDto);
		}
	}

}
