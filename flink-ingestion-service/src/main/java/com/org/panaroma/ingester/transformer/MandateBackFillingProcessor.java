package com.org.panaroma.ingester.transformer;

import com.org.panaroma.commons.dto.MandateBackFillingKafkaObject;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.commons.enums.MandateBackFillingIdentifierEnum;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.ingester.repository.EsRepository;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.CommonConstants.MANDATE_BACKFILLING_KAFKA_CLIENT_NAME;

@Log4j2
@Component
public class MandateBackFillingProcessor
		implements FlatMapFunction<MandateBackFillingKafkaObject, MandateActivityData>, Serializable {

	private EsRepository repository;

	private IKafkaClient iKafkaClient;

	@Autowired
	public MandateBackFillingProcessor(final EsRepository repository, final IKafkaClient iKafkaClient) {
		this.repository = repository;
		this.iKafkaClient = iKafkaClient;
	}

	@Override
	public void flatMap(final MandateBackFillingKafkaObject mandateBackFillingKafkaObject,
			final Collector<MandateActivityData> collector) throws Exception {
		try {
			log.warn("mandateBackFillingKafkaObject received for processing : {}", mandateBackFillingKafkaObject);
			List<MandateActivityData> mandateActivityDataList;
			mandateActivityDataList = repository.fetchMandateActivityData(
					mandateBackFillingKafkaObject.getSearchContext(),
					mandateBackFillingKafkaObject.getPaginationParams(), true);
			if (!CollectionUtils.isEmpty(mandateActivityDataList)) {
				MandateActivityData lastRecord = null;
				if (mandateBackFillingKafkaObject.getSearchContext().getPageSize() + 1 == mandateActivityDataList
					.size()) {
					lastRecord = mandateActivityDataList.get(mandateActivityDataList.size() - 1);
					mandateActivityDataList = mandateActivityDataList.subList(0,
							mandateBackFillingKafkaObject.getSearchContext().getPageSize());
				}
				mandateActivityDataList.forEach(mandateActivityData -> {
					if (Objects.isNull(mandateActivityData.getMetaDataMap())) {
						mandateActivityData.setMetaDataMap(new HashMap<>());
					}
					mandateActivityData.getMetaDataMap()
						.put(BACKFILLING_IDENTIFIER,
								MandateBackFillingIdentifierEnum.RELATED_DOC_NOT_UPDATED_BACKFILLING
									.getBackFillingIdentifierKey());
				});
				log.warn(
						"mandateActivityDataList created for mandateBackFillingKafkaObject : {} mandateActivityDataList size : {}",
						mandateBackFillingKafkaObject, mandateActivityDataList.size());

				mandateActivityDataList.forEach(collector::collect);

				// update the pagination params and push into kafka if required
				if (Objects.nonNull(lastRecord)) {
					log.warn("last fetched record docId : {}", lastRecord.docId());
					PaginationParams paginationParams = new PaginationParams();
					paginationParams.setPaginationTxnId(lastRecord.getTxnId());
					paginationParams.setTransactionDateEpoch(String.valueOf(lastRecord.getTxnDate()));
					SearchContext searchContext = mandateBackFillingKafkaObject.getSearchContext();
					int currentPageNo = searchContext.getPageNo();
					searchContext.setPageNo(currentPageNo + 1);
					paginationParams.setPageNo(currentPageNo + 1);
					mandateBackFillingKafkaObject.setPaginationParams(paginationParams);
					log.warn(
							"mandateBackFillingKafkaObject : {} is being pushed to kafka topic for next set of records.",
							mandateBackFillingKafkaObject);
					iKafkaClient.pushIntoKafka(MANDATE_BACKFILLING_KAFKA_CLIENT_NAME, "MANDATE_BACKFILLING",
							mandateBackFillingKafkaObject);
				}
			}
			else {
				log.warn("No MandateActivityData object fetched from ES for : {} mandateBackFillingKafkaObject.",
						mandateBackFillingKafkaObject);
			}
		}
		catch (Exception e) {
			log.error(
					"Exception while processing : {} mandateBackFillingKafkaObject. Pushing again to kafka. Exception : {}.",
					mandateBackFillingKafkaObject, CommonsUtility.exceptionFormatter(e));
			try {
				/*
				 * same client name is used as key while pushing data to kafka topic as we
				 * want some constant value so that every time the data goes to same
				 * partition so that all the data is processed sequentially
				 */
				iKafkaClient.pushIntoKafka(MANDATE_BACKFILLING_KAFKA_CLIENT_NAME, MANDATE_BACKFILLING_KAFKA_CLIENT_NAME,
						mandateBackFillingKafkaObject);
			}
			catch (Exception ex) {
				log.error("Exception while re-pushing the mandateBackFillingKafkaObject : {}. Exception : {}",
						mandateBackFillingKafkaObject, CommonsUtility.exceptionFormatter(e));
			}
		}
	}

}
