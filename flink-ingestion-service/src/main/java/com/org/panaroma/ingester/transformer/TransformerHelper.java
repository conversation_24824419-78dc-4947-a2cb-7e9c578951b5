package com.org.panaroma.ingester.transformer;

import static com.org.panaroma.commons.constants.BankDataConstants.DOMESTIC_DC_REPORT_CODE;
import static com.org.panaroma.commons.constants.BankDataConstants.PPBL_TS_IMPS_REPORT_CODES;
import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.Flink.FWD_TXN_DETAILS_ROLLOUT;
import static com.org.panaroma.commons.constants.Constants.IMPS;
import static com.org.panaroma.commons.dto.PaymentSystemEnum.WALLET;
import static com.org.panaroma.commons.dto.TransactionSource.PG;
import static com.org.panaroma.commons.utils.Utility.createParentDetailsFromFwdTxn;
import static com.org.panaroma.commons.utils.Utility.isOnlyForGrouping;
import static com.org.panaroma.constants.Constants.GV_PURCHASED;
import static com.org.panaroma.ingester.constants.Constants.ACTION;
import static com.org.panaroma.ingester.constants.Constants.ADD_AND_PAY_TXN_PURPOSE;
import static com.org.panaroma.ingester.constants.Constants.CHAT_UNIQUE_IDENTIFIER;
import static com.org.panaroma.ingester.constants.Constants.COLON;
import static com.org.panaroma.ingester.constants.Constants.END_STATUS_INT_LIST;
import static com.org.panaroma.ingester.constants.Constants.EVENT_ALREADY_PRESENT_IN_ES;
import static com.org.panaroma.ingester.constants.Constants.FORWARD_TXNDATE;
import static com.org.panaroma.ingester.constants.Constants.IGNORE_DTOS_ARE_SAME;
import static com.org.panaroma.ingester.constants.Constants.IS_FOR_RETRY_FROM_SYSTEM;
import static com.org.panaroma.ingester.constants.Constants.IS_FROM_DC_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.PAYTM;
import static com.org.panaroma.ingester.constants.Constants.PAYTMGIFTVOUCHER;
import static com.org.panaroma.ingester.constants.Constants.PAYTM_GIFT_VOUCHER;
import static com.org.panaroma.ingester.constants.Constants.PAYTM_MID;
import static com.org.panaroma.ingester.constants.Constants.PPBL_SET_NAME;
import static com.org.panaroma.ingester.constants.Constants.REFUND_TYPE;
import static com.org.panaroma.ingester.constants.Constants.RELATIVE_PRESENT;
import static com.org.panaroma.ingester.constants.Constants.REPORT_CODE;
import static com.org.panaroma.ingester.constants.Constants.RETRY_PIPELINE_PUSH_COUNT;
import static com.org.panaroma.ingester.constants.Constants.RelayConstants.DISCARD;
import static com.org.panaroma.ingester.constants.Constants.RelayConstants.OVERRIDE;
import static com.org.panaroma.ingester.constants.Constants.RelayConstants.RELAY_EVENTS;
import static com.org.panaroma.ingester.constants.Constants.TO_SOURCE;
import static com.org.panaroma.ingester.constants.Constants.TRUE;
import static com.org.panaroma.ingester.constants.Constants.UTH_CATEGORY_OTHERS;
import static com.org.panaroma.ingester.constants.Constants.XFER_RPT_CODES;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.ACTUAL_NUM_OF_LINKED_EVENTS_UPDATED;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.CACHE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.CREATED_TXN_DATE_DIFF;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.DTOS_ARE_SAME_COUNT;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.END_STATUS_TXN_DATE_DIFF;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.ES;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.FALSE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.MERGING_GET_REQUESTS_SERVED;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.NUM_OF_LINKED_EVENTS_CAME_FOR_UPDATE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.SOURCE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.STOPPED;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.TXN_TYPE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.UPDATED_DATE_CURRENT_DATE_DIFF;
import static com.org.panaroma.ingester.utils.Utility.getLastFourDigitsOfCard;
import static com.org.panaroma.ingester.utils.Utility.getPurpose;
import static com.org.panaroma.ingester.utils.Utility.getReportCode;
import static com.org.panaroma.ingester.utils.Utility.isImps;

import com.org.panaroma.commons.dto.es.ParentDetails;
import com.aerospike.client.Record;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.config.VerticalIdToUthcategoryMapper;
import com.org.panaroma.commons.dto.CardData;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.MerchantData;
import com.org.panaroma.commons.dto.MergedDocDetails;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.StatusEnum;
import com.org.panaroma.commons.dto.StoredDocDetails;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.commons.dto.es.TransformedBankData;
import com.org.panaroma.commons.dto.es.TransformedCardData;
import com.org.panaroma.commons.dto.es.TransformedMerchantData;
import com.org.panaroma.commons.dto.es.TransformedMobileData;
import com.org.panaroma.commons.dto.es.TransformedOtherData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.es.TransformedUPIData;
import com.org.panaroma.commons.dto.es.TransformedWalletData;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import com.org.panaroma.commons.utils.BankUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.commons.utils.rollout.strategy.IRolloutStrategyHelper;
import com.org.panaroma.datagroup.GroupingManager;
import com.org.panaroma.ingester.cache.CacheKeyManager;
import com.org.panaroma.ingester.cache.IcacheClient;
import com.org.panaroma.ingester.cache.MerchantDetailsCacheHolder;
import com.org.panaroma.ingester.configuration.yaml.ContextFilterMapConfig;
import com.org.panaroma.ingester.dto.ContextFieldData;
import com.org.panaroma.ingester.dto.insertUpdateExecutorDtos.BulkUpdateDto;
import com.org.panaroma.ingester.dto.insertUpdateExecutorDtos.CustomUpdateParams;
import com.org.panaroma.ingester.merger.BaseMerger;
import com.org.panaroma.ingester.merger.interfaces.CMerger;
import com.org.panaroma.ingester.merger.interfaces.IMerger;
import com.org.panaroma.ingester.merger.interfaces.IMergerFactory;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.repository.IRepository;
import com.org.panaroma.ingester.services.TransformerHelperService;
import com.org.panaroma.ingester.transformer.interfaces.ITransformerHelper;
import com.org.panaroma.ingester.transformer.interfaces.IVisibilityCreator;
import com.org.panaroma.ingester.utils.EsInsertUpdateInterceptorUtility;
import com.org.panaroma.ingester.utils.MergerUtility;
import com.org.panaroma.ingester.utils.RelayUtility;
import com.org.panaroma.ingester.utils.Utility;
import java.io.Serializable;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class TransformerHelper implements ITransformerHelper, Serializable {

	private IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> iRepository;

	private IcacheClient<TransformedTransactionHistoryDetail> cacheClient;

	private IVisibilityCreator iVisibilityCreator;

	private IMergerFactory iMergerFactory;

	private GroupingManager groupingManager;

	private ObjectMapper objectMapper;

	private ContextFilterMapConfig contextFilterMapConfig;

	private MerchantDetailsCacheHolder cacheHolder;

	private List<String> whiteListedUserList;

	private Integer isWhilteListingRequiredForChat;

	private Integer isWhilteListingRequiredForSingleDoc;

	private MetricsAgent metricsAgent;

	private TransformerHelperService transformerHelperService;

	private Map<Integer, List<Integer>> uthCategorySetterMap = new HashMap<>();

	private List<String> rptCodesBlockedForShowInList;

	private List<String> rptCodesBlockedForGrouping;

	private final com.org.panaroma.commons.utils.Utility utility;

	private static final List<TransactionTypeEnum> reversalAndRefundTypeList = Arrays.asList(
			TransactionTypeEnum.P2M_REFUND, TransactionTypeEnum.P2P_OUTWARD_REVERSAL, TransactionTypeEnum.P2P2M_REFUND,
			TransactionTypeEnum.RELEASED);

	private final IRolloutStrategyHelper rolloutStrategyHelper;

	private List<TransactionSource> stopFirstEventEsCallFor;

	private long stopFirstEventEsCallForTimeDiff;

	private EsInsertUpdateInterceptorUtility esInsertUpdateInterceptorUtility;

	private RelayUtility relayUtility;

	private final VerticalIdToUthcategoryMapper verticalIdToUthcategoryMapper;

	/***
	 * 20261 : CREATE_FD, 20265 : REDEEM_FD, 20263 : CREATE_FD_IMPULSE, 20264 :
	 * CREATE_FD_IMPULSE_REFUND, 20275 : REDEEM_FD 20276 : REDEEM_FD_INTEREST, 20277 :
	 * REDEEM_FD_ONLINE, 23002: Async FD Recovery, 20267: FD Redeem For Online Payments
	 * Removing 20276, 23002, 20267 since we do not get TS event for this reportCode,
	 * hence making it Source
	 */
	private static final List<String> fdReportCodes = Arrays.asList("20261", "20265", "20263", "20264",
			"20275"/* , "20276" */, "20277");

	@Autowired
	public TransformerHelper(final IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> iRepository,
			final IVisibilityCreator iVisibilityCreator, final IMergerFactory iMergerFactory,
			final GroupingManager groupingManager, final IcacheClient<TransformedTransactionHistoryDetail> cacheClient,
			final ContextFilterMapConfig contextFilterMapConfig, final MerchantDetailsCacheHolder cacheHolder,
			@Value("${white.listed.users.list}") final List<String> whiteListedUserList,
			@Value("${is.white.listing.required.for.chat}") final Integer whiteListingRequiredForChat,
			@Value("${is.white.listing.required.for.single.doc}") final Integer whiteListingRequiredForSingleDoc,
			@Value("${reportcodes.blocked.for.ShowInListing}") final List<String> rptCodesBlockedForShowInList,
			@Value("${reportcodes.blocked.for.grouping}") final List<String> rptCodesBlockedForGrouping,
			@Value("#{${uthCategory.setter.map}}") final Map<String, List<String>> uthCategorySetterMap,
			@Value("${stop.firstEvent.esCallFor}") final List<TransactionSource> stopFirstEventEsCallFor,
			@Value("${stop.firstEvent.esCallFor.timediff}") final long stopFirstEventEsCallForTimeDiff,
			final MetricsAgent metricsAgent, final TransformerHelperService transformerHelperService,
			final com.org.panaroma.commons.utils.Utility utility, final IRolloutStrategyHelper rolloutStrategyHelper,
			final EsInsertUpdateInterceptorUtility esInsertUpdateInterceptorUtility, final RelayUtility relayUtility,
			final VerticalIdToUthcategoryMapper verticalIdToUthcategoryMapper) {
		this.iRepository = iRepository;
		this.iVisibilityCreator = iVisibilityCreator;
		this.iMergerFactory = iMergerFactory;
		this.groupingManager = groupingManager;
		this.cacheClient = cacheClient;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.objectMapper = new ObjectMapper();
		this.contextFilterMapConfig = contextFilterMapConfig;
		this.cacheHolder = cacheHolder;
		this.whiteListedUserList = whiteListedUserList;
		this.isWhilteListingRequiredForChat = whiteListingRequiredForChat;
		this.isWhilteListingRequiredForSingleDoc = whiteListingRequiredForSingleDoc;
		this.metricsAgent = metricsAgent;
		this.transformerHelperService = transformerHelperService;
		this.uthCategorySetterMap = Utility.initUthCategorySetterMap(uthCategorySetterMap);
		this.utility = utility;
		this.rptCodesBlockedForShowInList = rptCodesBlockedForShowInList;
		this.rptCodesBlockedForGrouping = rptCodesBlockedForGrouping;
		this.stopFirstEventEsCallForTimeDiff = stopFirstEventEsCallForTimeDiff;
		this.stopFirstEventEsCallFor = stopFirstEventEsCallFor;
		this.esInsertUpdateInterceptorUtility = esInsertUpdateInterceptorUtility;
		this.relayUtility = relayUtility;
		this.verticalIdToUthcategoryMapper = verticalIdToUthcategoryMapper;
	}

	/**
	 * This method and all methods which used in this are also used for streaming purpose.
	 * please check effect on streaming before doing any change in this method
	 *
	 * Creates transformed history details data for the specified participant.
	 */
	public TransformedTransactionHistoryDetail createTransformedDto(final TransactionHistoryDetails thd,
			final TxnParticipants txnParticipant) {
		// Only creating document for customer and non ignored participant.
		if (txnParticipant == null || StringUtils.isEmpty(txnParticipant.getCustomerId())
				|| com.org.panaroma.commons.utils.Utility.isItIgnoredParticipant(txnParticipant)
				|| Boolean.TRUE.equals(Utility.isaVirtualParticipant(txnParticipant, thd))
				|| transformerHelperService.isSplittingNotRequired(thd, txnParticipant)) {
			return null;
		}

		try {
			String rrn = thd.getContextMap() != null && thd.getContextMap().containsKey("rrn")
					? thd.getContextMap().get("rrn") : null;
			log.debug("pipeline :{}, systemId: {}, rrn: {}, Creating ES document for data : {}",
					Utility.getActivePipelineName(), thd.getSystemId(), rrn, txnParticipant);
			// TODO: need to change below.
			if (participantPartOfPaytmEcosystemPredicate.negate().test(txnParticipant)) {
				log.error("pipeline:{}, systemId:{}, rrn: {}, CustomerId or MerchantData cannot be null",
						Utility.getActivePipelineName(), thd.getSystemId(), rrn);
				return null;
			}
			TransformedTransactionHistoryDetail tDto = new TransformedTransactionHistoryDetail();
			// PTH-397
			// tDto.setIsBankData(thd.getIsBankData() == null ? Boolean.FALSE :
			// thd.getIsBankData());
			tDto.setAmount(this.getAmountInLowerDenomination(thd.getAmount()));
			tDto.setContextMap(thd.getContextMap());
			tDto.setOrderId(this.getOrderId(thd.getContextMap()));
			tDto.setSourceTxnId(thd.getSourceSystemId());
			tDto.setSourceSystem(
					thd.getSourceSystem() == null ? null : thd.getSourceSystem().getTransactionSourceKey());
			// this will be set after getting the merged dto.
			// tDto.setIsSource(this.findIfThisIsParent(thd));
			tDto.setCurrency(
					thd.getCurrency() != null ? thd.getCurrency().getCurrencyKey() : Currency.INR.getCurrencyKey());
			tDto.setStreamSource(
					thd.getStreamSource() == null ? null : thd.getStreamSource().getTransactionSourceKey());
			tDto.setTags(thd.getTags() == null ? new ArrayList<>()
					: thd.getTags()
						.stream()
						.map(t -> new TransformedTag(t.getTag(), t.getType().getTagTypeKey(), t.getCreatedDate()))
						.collect(Collectors.toList()));
			tDto.setTxnId(thd.getSystemId());
			tDto.setUmn(thd.getUmn());
			tDto.setNotesKey(thd.getNotesKey());
			tDto.setChatData(thd.getChatData());
			tDto.setAmountBreakup(thd.getAmountBreakup());
			tDto.setParentTxnId(thd.getParentTxnId());
			this.setEntityDetails(tDto, txnParticipant);
			this.createParticipants(thd, tDto);
			this.createLocation(thd, tDto);
			Utility.populateErrorMessageContextMap(tDto);
			tDto.setRepeatPaymentData(Utility.createRepeatPaymentData(thd));
			tDto.setTxnDate(Long.valueOf(thd.getTxnDate()));
			tDto.setUpdatedDate(StringUtils.isBlank(thd.getUpdatedDate()) ? null : Long.valueOf(thd.getUpdatedDate()));
			tDto.setStatus(thd.getStatus() == null ? null : thd.getStatus().getStatusKey());
			// setting original status same as received status from source system
			tDto.setOriginalStatus(tDto.getStatus());
			tDto.setTxnIndicator(txnParticipant.getTxnIndicator() == null ? null
					: txnParticipant.getTxnIndicator().getTransactionIndicatorKey());
			TransactionTypeEnum transactionTypeEnum = this.getTransactionType(thd, txnParticipant);
			tDto.setTxnType(transactionTypeEnum == null ? null : transactionTypeEnum.getTransactionTypeKey());

			// set contextFilterMap
			// PTH-397
			// tDto.setContextFilterMap(this.getContextFilterMap(thd,
			// thd.getMainTxnType()));

			// setting cart details
			tDto.setCartDetails(thd.getCartDetails());

			boolean fwdTxnDetailsRollout = rolloutStrategyHelper.isUserWhiteListed(FWD_TXN_DETAILS_ROLLOUT,
					txnParticipant.getCustomerId());

			if (fwdTxnDetailsRollout) {
				// Create ParentDetails from FwdTxnDetails
				ParentDetails parentDetails = createParentDetailsFromFwdTxn(thd.getFwdTxnDetails());
				if (parentDetails != null) {
					tDto.setParentDetails(parentDetails);
				}
			}

			return tDto;
		}
		catch (Exception ex) {
			log.error("pipeline: {}, systemId :{}, Exception while converting data into ES document with message : {}.",
					Utility.getActivePipelineName(), thd.getSystemId(), ex.getMessage());
			this.repushDataIntoKafka(thd);
			throw ex;
		}
	}

	private Map<String, String> getContextFilterMap(final TransactionHistoryDetails thd,
			final TransactionTypeEnum transactionTypeEnum) {

		if (contextFilterMapConfig.getContextFilterMap() == null
				|| !contextFilterMapConfig.getContextFilterMap().containsKey(transactionTypeEnum)) {
			return null;
		}
		List<ContextFieldData> contextFieldDataList = contextFilterMapConfig.getContextFilterMap()
			.get(transactionTypeEnum);
		Map<String, String> contextMap = thd.getContextMap();

		if (contextFieldDataList != null && !contextFieldDataList.isEmpty() && contextMap != null) {
			Map<String, String> contextFilterMap = new HashMap<>();

			for (ContextFieldData contextFieldData : contextFieldDataList) {
				if (contextMap.containsKey(contextFieldData.getSource()) && contextFieldData.isEnable()
						&& !StringUtils.isEmpty(contextMap.get(contextFieldData.getSource()))) {
					contextFilterMap.put(contextFieldData.getDestination(),
							contextMap.get(contextFieldData.getSource()));
				}
			}
			if (!contextFilterMap.isEmpty()) {
				return contextFilterMap;
			}
		}
		return null;
	}

	private String getOrderId(final Map<String, String> contextMap) {
		return Utility.getOrderId(contextMap);
	}

	private void createParticipants(final TransactionHistoryDetails thd,
			final TransformedTransactionHistoryDetail tDto) {
		tDto.setParticipants(thd.getParticipants().stream().map(participant -> {
			// in case of PG and we dont have payment transaction id then we will not add
			// participant in this case.
			if (TransactionSource.isPgTypeSource(thd.getStreamSource())
					&& StringUtils.isBlank(participant.getPaymentTxnId())) {
				return null;
			}
			else {
				return this.createTransformedParticipantFromTxnParticipant(participant, thd);
			}
		}).collect(Collectors.toList()));
		tDto.getParticipants().removeAll(Collections.singletonList(null));
	}

	public Long getAmountInLowerDenomination(final String amount) {
		return Utility.getAmountInLowerDenomination(amount);
	}

	private void setEntityDetails(final TransformedTransactionHistoryDetail tDto,
			final TxnParticipants txnParticipant) {
		EntityTypesEnum entityTypes = null;
		String entityId = null;
		if (StringUtils.isNotBlank(txnParticipant.getCustomerId())) {
			entityId = txnParticipant.getCustomerId();
			entityTypes = EntityTypesEnum.USER;
		}
		else if (txnParticipant.getMerchantData() != null) {
			entityId = txnParticipant.getMerchantData().getMerchantId();
			entityTypes = EntityTypesEnum.MERCHANT;
		}
		else {
			log.error(
					"pipeline: {}, systemId: {}, No entity id found for the participant. Txn Participant payment id : {}",
					Utility.getActivePipelineName(), tDto.getTxnId(), txnParticipant.getPaymentTxnId());
			throw new RuntimeException("Error while adding entity id."); // Need to change
																			// this
																			// exception.
		}
		tDto.setEntityId(entityId);
		tDto.setEntityType(entityTypes.getEntityTypeKey());
		txnParticipant.setEntityType(entityTypes);
	}

	// TODO:Shaurya change below duplicate code.
	private void setEntityDetails(final TxnParticipants participant,
			final TransformedParticipant transformedParticipant) {
		EntityTypesEnum entityType = null;
		String entityId = null;
		if (StringUtils.isNotBlank(participant.getCustomerId())) {
			entityId = participant.getCustomerId();
			entityType = EntityTypesEnum.USER;
		}
		else if (participant.getMerchantData() != null) {
			entityId = participant.getMerchantData().getMerchantId();
			entityType = EntityTypesEnum.MERCHANT;
		}
		else {
			log.debug(
					"pipeline: {}, particapant payment txn id :{}, No entity id, entity type found for the participant. Txn Participant : {}",
					Utility.getActivePipelineName(), participant.getPaymentTxnId(), participant);
			// throw new RuntimeException("Error while adding entity id."); //Need to
			// change this exception.
		}
		transformedParticipant.setEntityId(entityId);
		transformedParticipant.setEntityType(entityType == null ? null : entityType.getEntityTypeKey());
	}

	@Override
	public TransformedParticipant createTransformedParticipantFromTxnParticipant(final TxnParticipants participants,
			final TransactionHistoryDetails detail) {
		TransformedParticipant transformedParticipant = new TransformedParticipant();
		transformedParticipant.setAmount(this.getAmountInLowerDenomination(participants.getAmount()));
		transformedParticipant
			.setRemarks(participants.getRemarks() != null ? participants.getRemarks() : detail.getRemarks());
		transformedParticipant.setContextMap(participants.getContextMap());
		transformedParticipant
			.setCurrency(participants.getCurrency() == null ? null : participants.getCurrency().getCurrencyKey());
		this.setEntityDetails(participants, transformedParticipant);
		transformedParticipant.setLogoUrl(participants.getLogoUrl());
		transformedParticipant.setName(participants.getName());
		transformedParticipant.setPaymentSystem(
				participants.getPaymentSystem() == null ? null : participants.getPaymentSystem().getPaymentSystemKey());
		transformedParticipant.setPaymentTxnId(participants.getPaymentTxnId());
		transformedParticipant
			.setStatus(participants.getStatus() == null ? null : participants.getStatus().getStatusKey());
		transformedParticipant.setTxnIndicator(participants.getTxnIndicator() == null ? null
				: participants.getTxnIndicator().getTransactionIndicatorKey());
		transformedParticipant.setTxnDate(participants.getTxnDate());
		transformedParticipant.setUpdatedDate(participants.getUpdatedDate());
		if (participants.getMerchantData() != null) {
			TransformedMerchantData tmd = new TransformedMerchantData();
			tmd.setMccCode(participants.getMerchantData().getMccCode());
			tmd.setMerchantId(participants.getMerchantData().getMerchantId());
			tmd.setMerchantType(participants.getMerchantData().getMerchantType() == null ? null
					: participants.getMerchantData().getMerchantType().getMerchantTypeKey());
			tmd.setMerchantCategory(participants.getMerchantData().getMerchantCategory() == null ? null
					: participants.getMerchantData().getMerchantCategory().getCategoryTagKey());
			tmd.setMerchantPayMode(participants.getMerchantData().getMerchantPayMode() == null ? null
					: participants.getMerchantData().getMerchantPayMode().getPayModeKey());
			tmd.setMerchantSubCategory(participants.getMerchantData().getMerchantSubCategory());
			transformedParticipant.setMerchantData(tmd);
		}
		if (participants.getUpiData() != null) {
			TransformedUPIData transformedUpiData = new TransformedUPIData();
			transformedUpiData.setVpa(participants.getUpiData().getVpa());
			transformedUpiData.setChannel(participants.getUpiData().getChannel() == null ? null
					: participants.getUpiData().getChannel().getChannelKey());
			transformedUpiData.setChannelId(participants.getUpiData().getChannelId());
			transformedUpiData.setChannelMode(participants.getUpiData().getChannelMode());
			transformedUpiData.setUpiCcInfo(participants.getUpiData().getUpiCcInfo());
			transformedUpiData.setPmtInstMetaInfo(participants.getUpiData().getPmtInstMetaInfo());
			transformedParticipant.setUpiData(transformedUpiData);
		}
		CardData cardData = participants.getCardData();
		setCardData(participants, transformedParticipant, cardData);
		if (participants.getWalletData() != null) {
			TransformedWalletData twd = new TransformedWalletData();
			twd.setWalletIssuer(participants.getWalletData().getWalletIssuer() == null ? null
					: participants.getWalletData().getWalletIssuer().getWalletIssuerKey());
			twd.setWalletType(participants.getWalletData().getWalletType() == null ? null
					: participants.getWalletData().getWalletType().getWalletTypeKey());
			twd.setWalletUserId(participants.getWalletData().getWalletUserId());
			twd.setWalletMobileNumber(participants.getWalletData().getWalletMobileNumber());
			twd.setClosingBalance(participants.getWalletData().getClosingBalance());
			transformedParticipant.setWalletData(twd);
		}

		TransformedBankData transformedBankData = new TransformedBankData();

		if (participants.getBankData() != null) {
			transformedBankData.setAccNumber(participants.getBankData().getAccNum());
			transformedBankData.setAccRefNum(participants.getBankData().getAccRefNum());
			transformedBankData.setAccountType(participants.getBankData().getAccType() == null ? null
					: participants.getBankData().getAccType().getAccountTypeEnumKey());
			transformedBankData.setBankName(participants.getBankData().getBankName());
			transformedBankData.setIfsc(participants.getBankData().getIfsc());
			transformedBankData.setUpiAccRefId(participants.getBankData().getUpiAccRefId());
		}
		transformedBankData
			.setBankTxnId(participants.getContextMap() != null && participants.getContextMap().containsKey("bankTxnId")
					? participants.getContextMap().get("bankTxnId") : null);
		transformedBankData.setRrn(detail.getContextMap() != null && detail.getContextMap().containsKey("rrn")
				? detail.getContextMap().get("rrn") : null);
		transformedParticipant.setBankData(transformedBankData);

		if (participants.getOtherData() != null) {
			TransformedOtherData transformedOtherData = new TransformedOtherData();
			BeanUtils.copyProperties(participants.getOtherData(), transformedOtherData);
			transformedParticipant.setOtherData(transformedOtherData);
		}

		if (participants.getMobileData() != null) {
			TransformedMobileData transformedMobileData = new TransformedMobileData();
			BeanUtils.copyProperties(participants.getMobileData(), transformedMobileData);
			if (transformedMobileData.getMobileNumber() == null) {
				String phoneNumber = com.org.panaroma.commons.utils.Utility.getValidPhoneNumber(transformedParticipant,
						detail.getTxnType(), false);
				if (StringUtils.isNotBlank(phoneNumber)) {
					transformedMobileData.setMobileNumber(phoneNumber);
				}
			}
			transformedParticipant.setMobileData(transformedMobileData);
		}
		else {
			TransformedMobileData transformedMobileData = new TransformedMobileData();
			String phoneNumber = com.org.panaroma.commons.utils.Utility.getValidPhoneNumber(transformedParticipant,
					detail.getTxnType(), false);
			if (StringUtils.isNotBlank(phoneNumber)) {
				transformedMobileData.setMobileNumber(phoneNumber);
				transformedParticipant.setMobileData(transformedMobileData);
			}
		}
		return transformedParticipant;
	}

	private void setCardData(final TxnParticipants participants, final TransformedParticipant transformedParticipant,
			final CardData cardData) {
		if (cardData != null) {
			TransformedCardData tcd = new TransformedCardData();
			tcd.setBin(cardData.getBin());
			tcd.setCardNum(cardData.getCardNum());
			tcd.setCardType(cardData.getCardType() == null ? null : cardData.getCardType().getCardTypeKey());
			tcd.setCardNetwork(cardData.getCardNetwork());
			tcd.setCreditAccRefNum(cardData.getCreditAccRefNum());
			if (participants.getBankData() != null) {
				tcd.setCardIssuer(participants.getBankData().getBankName());
			}
			String lastFourDigits = getLastFourDigitsOfCard(cardData);
			if (lastFourDigits != null) {
				tcd.setLastFourDigits(lastFourDigits);
			}
			transformedParticipant.setCardData(tcd);
		}
	}

	private String getUthCategoryId(final TransformedMerchantData merchantData) {
		String uthCategoryId = null;
		if (!Objects.isNull(merchantData)) {
			String mccCode = merchantData.getMccCode();
			String merchantSubCategory = "na";
			if (StringUtils.isNotBlank(merchantData.getMerchantSubCategory())) {
				merchantSubCategory = merchantData.getMerchantSubCategory();
			}
			uthCategoryId = merchantData.getUthCategory();
			if (StringUtils.isNotBlank(mccCode) && StringUtils.isNotBlank(merchantSubCategory)) {
				// if the uthCategory is blank or OTHERS then only we will try to fetch
				// from cache else return the existing uthCategory
				if (StringUtils.isBlank(uthCategoryId) || UTH_CATEGORY_OTHERS.equalsIgnoreCase(uthCategoryId)) {
					Pair<String, String> mccCodeSubCategoryPair = new Pair<>(mccCode, merchantSubCategory);
					uthCategoryId = cacheHolder.getMerchantDetailsCache().get(mccCodeSubCategoryPair);
				}
			}
		}
		return StringUtils.isBlank(uthCategoryId) ? UTH_CATEGORY_OTHERS : uthCategoryId;
	}

	private void setUthCategoryIdForP2M(final TransformedTransactionHistoryDetail enrichedDoc) {
		TransformedParticipant merchantParticipant = null;
		TransformedMerchantData merchantData = null;

		// loop to find merchant participant
		for (TransformedParticipant participant : enrichedDoc.getParticipants()) {
			if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
				merchantParticipant = participant;
			}
		}

		if (!Objects.isNull(merchantParticipant)) {
			log.info("Setting uthCategory in merchantData for doc : {}", enrichedDoc);
			merchantData = merchantParticipant.getMerchantData();
		}

		if (Utility.allowedToFetchUthCategory(uthCategorySetterMap, enrichedDoc)) {
			// log-optimisation
			log.debug("Size of merchantDetails cache is :{}", cacheHolder.getMerchantDetailsCache().size());

			// get uthCategoryId corresponding to mccCode & merchantSubCategory
			if (!Objects.isNull(merchantData)) {
				String uthCategoryId = getUthCategoryId(merchantData);
				merchantData.setUthCategory(uthCategoryId);
				log.info("Updated merchantData after setting uthCategory is : {}", merchantData);
			}
		}

		if (TransactionSource.OMS.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())
				&& !Objects.isNull(merchantData) && Objects.nonNull(enrichedDoc.getCartDetails())) {
			// setting uthCategory for OMS transactions
			merchantData.setUthCategory(verticalIdToUthcategoryMapper
				.getUthCategory(enrichedDoc.getCartDetails().getItems().get(0).getVerticalId()));
		}

	}

	/***
	 * @param sourceDto --> Source dto which was made by a stream.
	 * @return --> final set of DTO which is need to be insert/update in ES.
	 */
	public Set<TransformedTransactionHistoryDetail> mergeStoredAndUpdatedDtos(
			final TransformedTransactionHistoryDetail sourceDto) throws Exception {

		Set<TransformedTransactionHistoryDetail> mergedDtos = new HashSet<>();
		Set<TransformedTransactionHistoryDetail> groupedDtos = new HashSet<>();

		boolean isOnlyForGrouping = isOnlyForGrouping(sourceDto);
		Boolean existingShowInListing = null;
		String existingGroupId = null;
		final Boolean isWhitelistedTxnForChat = com.org.panaroma.commons.utils.Utility
			.checkWhiteListingFlag(this.whiteListedUserList, isWhilteListingRequiredForChat, sourceDto);
		final Boolean isWhitelistedTxnForSingleDoc = com.org.panaroma.commons.utils.Utility
			.checkWhiteListingFlag(this.whiteListedUserList, isWhilteListingRequiredForSingleDoc, sourceDto);

		if (isOnlyForGrouping) {
			log.debug("Re-grouping document for systemId:{}", sourceDto.getTxnId());
		}

		MergedDocDetails mergedDocDetails = this.getFinalDocument(sourceDto);
		TransformedTransactionHistoryDetail enrichedDoc = mergedDocDetails == null ? null
				: mergedDocDetails.getMergedTthd();

		// If enrichedDoc is Null - Dtos are same after merging (fetched from ES)
		if (enrichedDoc == null) {
			return mergedDtos;
		}

		if (Boolean.TRUE.equals(mergedDocDetails.getIsFetchedFromCache())
				&& Boolean.TRUE.equals(mergedDocDetails.getIsRedundantUpdate())) {
			enrichedDoc.setIsForEsSink(Boolean.TRUE);
			mergedDtos.add(enrichedDoc);
			return mergedDtos;
		}
		setUthCategoryIdForP2M(enrichedDoc);

		if (isOnlyForGrouping) {
			// Storing existing groupId and showInListing values of doc which came for
			// only grouping
			existingShowInListing = enrichedDoc.getShowInListing();
			existingGroupId = enrichedDoc.getGroupId();

			mergedDtos.add(enrichedDoc);
		}
		// checkforGroupingRequired will return isVisible flag by default. but is that
		// indicate grouping is required???
		// added this check because PG sends multiple events for an order and there is no
		// need of grouping if we don’t have sufficient data
		if (this.checkForGroupingRequired(enrichedDoc)) {
			Pair<Set<TransformedTransactionHistoryDetail>, Set<TransformedTransactionHistoryDetail>> pairDoc = groupingManager
				.group(enrichedDoc);
			log.info("Doc received after grouping :{}", pairDoc);
			Set<TransformedTransactionHistoryDetail> linkedTxnSet = pairDoc.getKey();
			groupedDtos = pairDoc.getValue();
			groupedDtos.add(utility.cloneObjectData(enrichedDoc));
			// mergedDTOs.addAll(linkedTxnSet);
			if (isOnlyForGrouping) {
				if (linkedTxnSet == null) {
					linkedTxnSet = new HashSet<>();
				}
				// adding in linkedSet only if there is any change in groupId or
				// showInListing
				if (!Objects.equals(existingGroupId, enrichedDoc.getGroupId())
						|| !Objects.equals(existingShowInListing, enrichedDoc.getShowInListing())) {
					log.debug("Re-grouping document is added to update systemId:{}", enrichedDoc.getTxnId());
					linkedTxnSet.add(enrichedDoc);
				}
			}
			// Calling method to set view status in linked txn's TTHD Dto
			setViewStatusInLinkedTxns(linkedTxnSet);
			this.updateTxnsForGrouping(linkedTxnSet, enrichedDoc);
		}
		// Calling for setting created and updated date of ES Doc
		setUthInternalDates(enrichedDoc, isOnlyForGrouping);
		// Calling method to set View Status in enrichDoc
		setViewStatusInTxnTthd(enrichedDoc);

		// save enriched doc in cache for report codes 20270 & 20271 (Xfer i.e. PPBL to
		// PPBL) as merging is required in this case
		if (isWhitelistedTxnForChat || isWhitelistedTxnForSingleDoc) {
			populatePpblRecordSetCache(enrichedDoc);
		}

		TransactionTypeEnum txnType = com.org.panaroma.commons.utils.Utility.getTxnTypeForSingleDoc(groupedDtos,
				enrichedDoc);
		Set<TransformedTransactionHistoryDetail> mergedDocList = null;

		// cloning dtos for comparing with dtos formed after merging
		String nonMergedGroupDtosJson = null;
		Set<TransformedTransactionHistoryDetail> nonMergedGroupDtos = null;
		if (isWhitelistedTxnForSingleDoc) {
			nonMergedGroupDtosJson = objectMapper.writeValueAsString(groupedDtos);
			nonMergedGroupDtos = objectMapper.readValue(nonMergedGroupDtosJson,
					new TypeReference<Set<TransformedTransactionHistoryDetail>>() {
					});
		}

		if (groupedDtos.size() > 0 && (isWhitelistedTxnForChat || isWhitelistedTxnForSingleDoc)) {
			BaseMerger merger = this.iMergerFactory.getMergerByType(txnType);
			if (merger instanceof CMerger) {
				CMerger cMerger = (CMerger) merger;
				log.debug("Sending docs for merging for txnId : {}", enrichedDoc.getTxnId());
				// TODO: reminder to handle mering for txns having
				// showInListing=false/null and isVisible=false/null
				mergedDocList = cMerger.mergeDocuments(groupedDtos, txnType, isWhitelistedTxnForChat);
				if (mergedDocList != null) {
					mergedDocList.forEach(this::removeChatUniqueIdentifierIfRequired);
				}
				if (Objects.nonNull(mergedDocList)) {
					log.debug("Number of merged docs in set corresponding to txnId : {} is : {}",
							enrichedDoc.getTxnId(), mergedDocList.size());
					if (mergedDocList.size() > 0) {
						// we need to set otherPartyEntityId field from otherParticipant
						mergedDocList.forEach(mergedDoc -> {
							if (Objects.nonNull(mergedDoc)) {
								transformerHelperService.populateOtherPartyEntityId(mergedDoc);
							}
							mergedDtos.add(mergedDoc);
						});
					}
				}
			}
		}
		// we need to set otherPartyEntityId field from otherParticipant
		transformerHelperService.populateOtherPartyEntityId(enrichedDoc);
		if (!isOnlyForGrouping) {
			metricsAgent.recordTimeDiffBetweenDatesAsync(UPDATED_DATE_CURRENT_DATE_DIFF, enrichedDoc.getUpdatedDate(),
					new Date().getTime(), enrichedDoc);
			// this variable groupedDtosSize is added in dtos in contextMap sent to UTH
			// pipeline
			// to decide whether to perform grouping again in UTH pipeline or not
			int groupedDtosSize = 0;
			if (groupedDtos != null) {
				groupedDtosSize = groupedDtos.size();
			}
			this.updateDocumentsInCacheAndAddInMergedDtosSet(mergedDtos, enrichedDoc, nonMergedGroupDtos, mergedDocList,
					isWhitelistedTxnForSingleDoc, groupedDtosSize);
		}
		return mergedDtos;
	}

	private void removeChatUniqueIdentifierIfRequired(final TransformedTransactionHistoryDetail doc) {
		if (rolloutStrategyHelper.isUserWhiteListed(IMPS, doc.getEntityId())) {
			return;
		}
		if (doc.getContextMap() == null) {
			return;
		}
		doc.getContextMap().remove(CHAT_UNIQUE_IDENTIFIER);
	}

	// Method to set view status in linked Txns Set
	private void setViewStatusInLinkedTxns(final Set<TransformedTransactionHistoryDetail> linkedTxnSet) {
		for (TransformedTransactionHistoryDetail txn : linkedTxnSet) {
			setViewStatusInTxnTthd(txn);
		}
	}

	// method to set view status of txn in status field of txn's TTHD
	private void setViewStatusInTxnTthd(final TransformedTransactionHistoryDetail txn) {
		if (Boolean.TRUE.equals(txn.getIsVisible()) && Boolean.TRUE.equals(txn.getShowInListing())) {
			/*
			 * if this txn is before deployment and its child or parent change its
			 * showInListing then need to set original status as status before change that
			 * in view status
			 */
			if (Objects.isNull(txn.getOriginalStatus())) {
				txn.setOriginalStatus(txn.getStatus());
			}
			// Setting View in status field
			ClientStatusEnum viewStatus = com.org.panaroma.commons.utils.Utility.getViewStatusFormTthd(txn);

			// TODO remove after prod testing of IMPS
			if (isNonWhiteListedImpsCbs(txn)) {
				viewStatus = ClientStatusEnum.SUCCESS;
			}

			if (Objects.nonNull(viewStatus)) {
				txn.setStatus(viewStatus.getStatusKey());
			}
		}
		// Changing status field to original status when source txn is Visible
		// Case : Upi International using psp - other & Bank - paytm
		// Condition : UPI Pending with status - terminal & originalStatus - non-terminal
		// Do : do not set originalStatus in status for this case
		if (txn.getGroupId() != null && Boolean.FALSE.equals(
				com.org.panaroma.commons.utils.UpiInternationalUtility.isUpiInternationalAndOtherPspUpiEvent(txn))) {

			/*
			 * if this txn is before deployment and its parent change its showListing and
			 * grouping then needs to be set original Status as status
			 */
			if (Objects.isNull(txn.getOriginalStatus())) {
				txn.setOriginalStatus(txn.getStatus());
			}
			txn.setStatus(txn.getOriginalStatus());
		}
	}

	/**
	 * For reverting view status handling for IMPS txns of non whiteListedUsers
	 * @param txn txn entry
	 * @return if entry is of non whiteListed IMPS case
	 */
	private boolean isNonWhiteListedImpsCbs(final TransformedTransactionHistoryDetail txn) {
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(txn.getStreamSource())) {
			return txn.getContextMap() != null
					&& PPBL_TS_IMPS_REPORT_CODES.contains(txn.getContextMap().get(REPORT_CODE))
					&& !rolloutStrategyHelper.isUserWhiteListed(IMPS, txn.getEntityId());
		}
		return false;
	}

	// Method for update Txns for Grouping
	private void updateTxnsForGrouping(final Set<TransformedTransactionHistoryDetail> linkedTxns,
			final TransformedTransactionHistoryDetail enrichedDoc) {
		if (CollectionUtils.isEmpty(linkedTxns)) {
			log.debug("Group Txns is Empty Source: {}, GroupId: {}", enrichedDoc.getStreamSource(),
					enrichedDoc.getGroupId());
			return;
		}
		metricsAgent.countMetricsWithCountTypeStringAndValues(NUM_OF_LINKED_EVENTS_CAME_FOR_UPDATE, linkedTxns.size(),
				TXN_TYPE + enrichedDoc.getMainTxnType(), SOURCE + enrichedDoc.getStreamSource());
		Set<TransformedTransactionHistoryDetail> linkedTxnsToUpdate = new HashSet<>();
		for (TransformedTransactionHistoryDetail detail : linkedTxns) {
			if (detail.getHash() != detail.hashCode()) {
				linkedTxnsToUpdate.add(detail);
			}
		}
		log.info("size txns sent for update : {} , while size of list requested for update : {}",
				linkedTxnsToUpdate.size(), linkedTxns.size());
		metricsAgent.countMetricsWithCountTypeStringAndValues(ACTUAL_NUM_OF_LINKED_EVENTS_UPDATED,
				linkedTxnsToUpdate.size(), TXN_TYPE + enrichedDoc.getMainTxnType(),
				SOURCE + enrichedDoc.getStreamSource());
		for (TransformedTransactionHistoryDetail txn : linkedTxnsToUpdate) {
			this.saveUpdateRecordForRelativeFinder(txn);
		}
		Set<CustomUpdateParams> customUpdateParamsList = new HashSet<>();
		linkedTxnsToUpdate.forEach(tthd -> {
			CustomUpdateParams customUpdateParams = CustomUpdateParams.builder()
				.txnDate(tthd.getTxnDate())
				.docId(tthd.docId())
				.showInListing(tthd.getShowInListing())
				.status(tthd.getStatus())
				.entityId(tthd.getEntityId())
				.txnId(tthd.getTxnId())
				.docUpdatedDate(new Date().getTime())
				.build();

			if (TransactionSource.OMS.getTransactionSourceKey().equals(tthd.getStreamSource())) {
				customUpdateParams.setContextMap(tthd.getContextMap());
			}

			customUpdateParamsList.add(customUpdateParams);
		});
		if (customUpdateParamsList.size() > 0) {
			BulkUpdateDto bulkUpdateDto = BulkUpdateDto.builder()
				.customUpdateParams(customUpdateParamsList)
				.groupId(enrichedDoc.getGroupId())
				.build();
			// TODO :- Remove this call in case of DC_PIPELINES.
			esInsertUpdateInterceptorUtility.pushUpdateOrInsertToKafka(bulkUpdateDto);
			iRepository.updateTxnsForGrouping(bulkUpdateDto);
		}
	}

	private void updateDocumentsInCacheAndAddInMergedDtosSet(final Set<TransformedTransactionHistoryDetail> mergedDtos,
			final TransformedTransactionHistoryDetail enrichedDoc,
			final Set<TransformedTransactionHistoryDetail> nonMergedDtos,
			final Set<TransformedTransactionHistoryDetail> chatDoc, final Boolean isWhiteListingRequiredForSingleDoc,
			final int groupedDtosSize) throws Exception {

		// Need to check for grouped dtos and chat dtos are same or not.
		if (isWhiteListingRequiredForSingleDoc && Utility.needToCreateSingleDoc(enrichedDoc) && chatDoc != null
				&& chatDoc.size() > 0) {
			// For each grouped set, we need to find merged doc from chatDoc and if these
			// two are not same then
			// we have to update this mergedDoc in cache and ES.
			// which changed in singleDoc merger i.e. removing those docs which same as
			// before singleDoc merging
			log.warn("Creating single doc for txnId :{}", enrichedDoc.getTxnId());
			Set<TransformedTransactionHistoryDetail> dtosForEs = Utility
				.extractOnlyUpdatedDtosFromGrpdDtos(nonMergedDtos, chatDoc, enrichedDoc);
			if (dtosForEs != null && dtosForEs.size() > 0) {
				for (TransformedTransactionHistoryDetail doc : dtosForEs) {
					this.updateDocInCacheAndSendToEsSink(mergedDtos, doc, groupedDtosSize);
				}
			}
			else {
				this.updateDocInCacheAndSendToEsSink(mergedDtos, enrichedDoc, groupedDtosSize);
			}
		}
		else {
			this.updateDocInCacheAndSendToEsSink(mergedDtos, enrichedDoc, groupedDtosSize);
		}
	}

	private void updateDocInCacheAndSendToEsSink(final Set<TransformedTransactionHistoryDetail> mergedDtos,
			final TransformedTransactionHistoryDetail doc, final int groupedDtosSize) {
		log.info("Updating doc in chache and sending for ES sink. Doc : {}", doc);
		mergedDtos.add(doc);
		cacheClient.saveUpdatedRecord(doc, CacheKeyManager.getSelfKey(doc));
		// handled below code for setting relative here bcoz every doc added here goes to
		// UTH pipeline.
		if (doc.getContextMap() == null) {
			Map<String, String> contextMap = new HashMap<>();
			doc.setContextMap(contextMap);
		}
		this.saveUpdateRecordForRelativeFinder(doc);
		doc.getContextMap().put(RELATIVE_PRESENT, String.valueOf(groupedDtosSize));
		doc.setIsForEsSink(Boolean.TRUE);
	}

	/***
	 *
	 * store all the related docs with the same txnId in cache.
	 */
	private void populatePpblRecordSetCache(final TransformedTransactionHistoryDetail enrichedDoc) {
		// required whitelisting boolean here
		if (TransactionTypeEnum.PPBL_TRANSACTION
			.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(enrichedDoc.getMainTxnType()))
				&& enrichedDoc.getContextMap().containsKey(REPORT_CODE)
				&& XFER_RPT_CODES.contains(enrichedDoc.getContextMap().get(REPORT_CODE))) {
			Record storedDoc = null;
			try {
				storedDoc = this.cacheClient.getRecord(enrichedDoc.getTxnId(), PPBL_SET_NAME,
						new TypeReference<List<TransformedTransactionHistoryDetail>>() {
						});
				List<TransformedTransactionHistoryDetail> storedDocsWithSameTxnId;
				String currentEntityId = enrichedDoc.getEntityId();
				if (storedDoc != null) {
					if (!Objects.isNull(storedDoc.bins)) {
						storedDocsWithSameTxnId = (List<TransformedTransactionHistoryDetail>) storedDoc.bins
							.get("value");

						log.debug("Stored docs with same txnId found are :{}", storedDoc);
						// to always keep the latest doc in cache
						for (TransformedTransactionHistoryDetail doc : storedDocsWithSameTxnId) {
							if (currentEntityId.equalsIgnoreCase(doc.getEntityId())) {
								storedDocsWithSameTxnId.remove(doc);
								break;
							}
						}
						storedDocsWithSameTxnId.add(enrichedDoc);
						this.cacheClient.saveRecordInPpblCache(storedDocsWithSameTxnId, enrichedDoc.getTxnId(),
								PPBL_SET_NAME);
					}
					else {
						throw new RuntimeException("bin value null even if Record is present");
					}
				}
				else {
					storedDocsWithSameTxnId = new ArrayList<>();
					storedDocsWithSameTxnId.add(enrichedDoc);
					this.cacheClient.saveRecordInPpblCache(storedDocsWithSameTxnId, enrichedDoc.getTxnId(),
							PPBL_SET_NAME);
				}
			}
			catch (Exception e) {
				log.error("Some exception occurred while putting or getting from cache {}",
						CommonsUtility.exceptionFormatter(e));
				throw e;
			}
		}

	}

	private boolean checkForGroupingRequired(final TransformedTransactionHistoryDetail enrichedDoc) {

		if (TransactionSource.UPI.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())
				&& TransactionTypeEnum.P2M.getTransactionTypeKey().equals(enrichedDoc.getMainTxnType())
				&& StatusEnum.PENDING.getStatusKey().equals(enrichedDoc.getStatus())) {
			// will show this event only if we have sufficient info.
			enrichedDoc.setShowInListing(enrichedDoc.getIsVisible());
			return false;
		}

		// todo: temp change not to skip grouping if visibility false in case of bank,
		// as visibility is set to true for some whitelisted reportCodes, bcoz if grouping
		// skipped,
		// showInListing will be null

		// UTH 72 : Temp changes : will do grouping for 80203 and 60203, but showInListing
		// will always be false.
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())) {
			if (this.rptCodesBlockedForShowInList.contains(enrichedDoc.getContextMap().get(REPORT_CODE))) {
				enrichedDoc.setShowInListing(false);
				if (this.rptCodesBlockedForGrouping.contains(enrichedDoc.getContextMap().get(REPORT_CODE))) {
					return false;
				}
			}
			return true;
		}
		return enrichedDoc.getIsVisible();
	}

	private void saveUpdateRecordForRelativeFinder(final TransformedTransactionHistoryDetail enrichedDoc) {

		log.info("Updating cache for relative finder if required for txnID:{}", enrichedDoc.getTxnId());
		String cacheKey;

		TransactionSource transactionSource = TransactionSource
			.getTransactionSourceEnumByKey(enrichedDoc.getStreamSource());
		// Condition to check that txn needs to saved in cache for parent or not
		if (Utility.txnNeedsToSavedForParentOrNot(enrichedDoc, transactionSource)) {
			log.info("SourcetxnID is not null. updating required cache for txnID:{}", enrichedDoc.getTxnId());
			String identifier = this.getIndentifierToSaveRelativeTxn(enrichedDoc);
			cacheKey = CacheKeyManager.createGroupKey(
					TransactionSource.getTransactionSourceEnumByKey(enrichedDoc.getSourceSystem()),
					this.getPaymentSystemFromTransactionSource(transactionSource), transactionSource, identifier);
			log.info("cacheKey :{} for txnID:{} for parent :{} and currentSysem:{}", cacheKey, enrichedDoc.getTxnId(),
					enrichedDoc.getSourceSystem(), enrichedDoc.getStreamSource());
			this.saveUpdateCacheDocList(enrichedDoc, cacheKey);
		}

		for (TransformedParticipant participant : enrichedDoc.getParticipants()) {
			PaymentSystemEnum paymentSystem = CacheKeyManager.getPaymentSystemForCacheKey(enrichedDoc, participant);
			if (paymentSystem != null && this.isGroupRequired(transactionSource, paymentSystem)) {
				log.info("Updating cache for participant:{} for txn id:{}", participant, enrichedDoc.getTxnId());
				String identifier = this.getGroupingIdentifier(enrichedDoc, participant, paymentSystem,
						transactionSource);
				if (StringUtils.isNotBlank(identifier)) {
					cacheKey = CacheKeyManager.createGroupKey(transactionSource, paymentSystem, transactionSource,
							identifier);
					log.info("cacheKey:{} for participant:{} txnID:{}", cacheKey, participant, enrichedDoc.getTxnId());

					// For OMS Parent storing Parent as list in cache
					// Todo: Need to check for Refund case. will this work or not for
					// those cases
					if (TransactionSource.OMS.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())) {
						this.saveUpdateCacheDocList(enrichedDoc, cacheKey);
					}
					else {
						cacheClient.saveUpdatedRecord(enrichedDoc, cacheKey);
					}
				}
			}
		}
		specialHandlingForRelativeFinders(enrichedDoc);
	}

	private void specialHandlingForRelativeFinders(final TransformedTransactionHistoryDetail enrichedDoc) {
		String cacheKey;
		// Adding special cases
		TransactionTypeEnum transactionType = TransactionTypeEnum
			.getTransactionTypeEnumByKey(enrichedDoc.getMainTxnType());
		if (transactionType != null) {
			switch (transactionType) {
				case ADD_MONEY:
					String purpose = getPurpose(enrichedDoc);
					boolean giftVoucherCase = !StringUtils.isEmpty(purpose)
							&& (purpose.equalsIgnoreCase(PAYTM_GIFT_VOUCHER)
									|| purpose.equalsIgnoreCase(PAYTMGIFTVOUCHER));
					// adding special handling for gift voucher purchased for other
					if (giftVoucherCase && TransactionSource.isPgTypeSource(enrichedDoc.getStreamSource())) {
						TransactionSource streamSource = TransactionSource
							.getTransactionSourceEnumByKey(enrichedDoc.getStreamSource());

						cacheKey = CacheKeyManager.createGroupKey(TransactionSource.WALLET, PaymentSystemEnum.PG,
								streamSource, enrichedDoc.getTxnId());
						log.debug("cacheKey:{} for {} add money case. txnId:{}", streamSource, cacheKey,
								enrichedDoc.getTxnId());
						this.saveUpdateCacheDocList(enrichedDoc, cacheKey);
						break;
					}
					break;
				case ADD_AND_PAY:
					// adding for wallet event because PG dont send wallet payment leg in
					// add money and add&pay
					if (TransactionSource.isPgTypeSource(enrichedDoc.getStreamSource())) {
						TransactionSource streamSource = TransactionSource
							.getTransactionSourceEnumByKey(enrichedDoc.getStreamSource());

						cacheKey = CacheKeyManager.createGroupKey(streamSource, PaymentSystemEnum.WALLET, streamSource,
								enrichedDoc.getTxnId());
						log.debug("cacheKey:{} for {} add And Pay case. txnId:{}", streamSource, cacheKey,
								enrichedDoc.getTxnId());
						cacheClient.saveUpdatedRecord(enrichedDoc, cacheKey);
					}
					break;
				default:
			}
		}

		if (TransactionTypeEnum.ADD_MONEY_REFUND.getTransactionTypeKey().equals(enrichedDoc.getTxnType())
				&& PG.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())) {
			cacheKey = CacheKeyManager.getGroupKeyToFindParent(PG, WALLET, enrichedDoc.getTxnId());
			cacheClient.saveUpdatedRecord(enrichedDoc, cacheKey);
		}
	}

	private String getIndentifierToSaveRelativeTxn(final TransformedTransactionHistoryDetail enrichedDoc) {
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())
				&& TransactionSource.TS.getTransactionSourceKey().equals(enrichedDoc.getSourceSystem())
				|| TransactionSource.TS.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())) {
			if (utility.isWhiteListedImpsOutward(enrichedDoc)) {
				return getImpsGroupingCacheKey(enrichedDoc);
			}
			return enrichedDoc.getTxnId();
		}
		else if (TransactionSource.PPBL.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())
				&& TransactionSource.UPI.getTransactionSourceKey().equals(enrichedDoc.getSourceSystem())) {
			return enrichedDoc.getSourceTxnId() + "_" + enrichedDoc.getEntityId();
		}
		else if (TransactionSource.UPI.getTransactionSourceKey().equals(enrichedDoc.getSourceSystem())
				&& Utility.isWalletInterOpTxn(enrichedDoc)) { // For wallet Introp Finder
																// since P2P Issue
			return enrichedDoc.getSourceTxnId() + "_" + enrichedDoc.getEntityId();
		}
		else if (TransactionSource.UPI.getTransactionSourceKey().equals(enrichedDoc.getStreamSource())
				&& TransactionSource.OMS.getTransactionSourceKey().equals(enrichedDoc.getSourceSystem())) { // For
																											// OMS
																											// UPI
																											// Integration
			return enrichedDoc.getSourceTxnId() + "_" + enrichedDoc.getEntityId() + "_" + enrichedDoc.getAmount();
		}
		else {
			return enrichedDoc.getSourceTxnId();
		}
	}

	private String getImpsGroupingCacheKey(final TransformedTransactionHistoryDetail enrichedDoc) {
		return String.format(("%s|%s|%s"), enrichedDoc.getContextMap().get("rrn"),
				enrichedDoc.getContextMap().get("reportCode"), enrichedDoc.getEntityId());
	}

	private void saveUpdateCacheDocList(final TransformedTransactionHistoryDetail enrichedDoc, final String cacheKey) {
		List<TransformedTransactionHistoryDetail> storedDoc = cacheClient.getUpdatedRecordList(cacheKey);
		if (storedDoc == null || storedDoc.isEmpty()) {
			cacheClient.saveUpdateRecordList(Collections.singletonList(enrichedDoc), cacheKey);
		}
		else {
			// deleting stored doc if exists in cache
			storedDoc = storedDoc.stream()
				.filter(doc -> !doc.docId().equalsIgnoreCase(enrichedDoc.docId()))
				.collect(Collectors.toList());
			storedDoc.add(enrichedDoc);
			cacheClient.saveUpdateRecordList(storedDoc, cacheKey);
		}
	}

	private String getGroupingIdentifier(final TransformedTransactionHistoryDetail enrichedDoc,
			final TransformedParticipant participant, final PaymentSystemEnum paymentSystem,
			final TransactionSource transactionSource) {
		String identifier = null;
		switch (transactionSource) {
			case PG:
			case PPBL_PG:
				identifier = this.getCacheIdentifierForPg(enrichedDoc, participant, paymentSystem);
				break;
			case WALLET:
				break;
			case UPI:
				identifier = this.getCacheIdentifierForUpi(enrichedDoc, paymentSystem);
				break;
			case TS:
				identifier = this.getCacheIdentifierForTs(enrichedDoc, participant, paymentSystem);
				break;
			case OMS:
				identifier = this.getCacheIdentifierForOms(enrichedDoc, participant, paymentSystem);
			default:
		}
		return identifier;
	}

	private String getCacheIdentifierForOms(final TransformedTransactionHistoryDetail enrichedDoc,
			final TransformedParticipant participant, final PaymentSystemEnum paymentSystem) {
		switch (paymentSystem) {
			case UPI:
				String rrn = com.org.panaroma.commons.utils.Utility.getRrn(enrichedDoc);
				return rrn + "_" + enrichedDoc.getEntityId() + "_" + participant.getAmount();
			default:
		}
		return null;
	}

	private String getCacheIdentifierForTs(final TransformedTransactionHistoryDetail enrichedDoc,
			final TransformedParticipant participant, final PaymentSystemEnum paymentSystem) {
		switch (paymentSystem) {
			case PPBL:
				if (isImps(enrichedDoc)) {
					return getImpsGroupingCacheKey(enrichedDoc);
				}
				return participant.getPaymentTxnId();
			default:
		}
		return null;
	}

	private String getCacheIdentifierForPg(final TransformedTransactionHistoryDetail enrichedDoc,
			final TransformedParticipant participant, final PaymentSystemEnum paymentSystem) {
		String identifier = null;
		switch (paymentSystem) {
			case WALLET:
				identifier = enrichedDoc.getTxnId();
				break;
			case UPI:
			case PPBL:
				identifier = participant.getPaymentTxnId();
				break;
			default:
		}
		return identifier;
	}

	private String getCacheIdentifierForUpi(final TransformedTransactionHistoryDetail enrichedDoc,
			final PaymentSystemEnum paymentSystem) {
		// This is for wallet Inrop Txn. Need to add entityId since it will cause issue in
		// P2P Txn
		if (paymentSystem.equals(PaymentSystemEnum.WALLET) && Utility.isWalletInterOpTxn(enrichedDoc)) {
			return enrichedDoc.getTxnId() + "_" + enrichedDoc.getEntityId();
		}
		else if (paymentSystem.equals(PaymentSystemEnum.WALLET)) { // This is for normal
																	// Payment System
																	// Wallet
			return enrichedDoc.getTxnId();
		}
		else if (paymentSystem.equals(PaymentSystemEnum.PPBL)) {
			return enrichedDoc.getTxnId() + "_" + enrichedDoc.getEntityId();
		}
		return null;
	}

	private PaymentSystemEnum getPaymentSystemFromTransactionSource(final TransactionSource source) {
		switch (source) {
			case PG:
				return PaymentSystemEnum.PG;
			case UPI:
				return PaymentSystemEnum.UPI;
			case WALLET:
				return PaymentSystemEnum.WALLET;
			case PPBL:
				return PaymentSystemEnum.PPBL;
			default:
		}
		return null;
	}

	private boolean isGroupRequired(final TransactionSource transactionSource, final PaymentSystemEnum childSystem) {

		switch (transactionSource) {
			case PG:
			case PPBL_PG:
				return childSystem.equals(PaymentSystemEnum.UPI) || childSystem.equals(PaymentSystemEnum.WALLET)
						|| childSystem.equals(PaymentSystemEnum.PPBL);
			case UPI:
				return childSystem.equals(PaymentSystemEnum.WALLET) || childSystem.equals(PaymentSystemEnum.PPBL);
			case WALLET:
				return false;
			case TS:
				return childSystem.equals(PaymentSystemEnum.PPBL);
			case OMS:
				return childSystem.equals(PaymentSystemEnum.UPI);
			default:
		}
		return false;
	}

	private boolean findIfThisParent(final TransformedTransactionHistoryDetail transformedTransactionHistoryDetail) {
		if (transformedTransactionHistoryDetail.getMainTxnType() == null) {
			return transformedTransactionHistoryDetail.getIsSource();
		}
		switch (TransactionTypeEnum.getTransactionTypeEnumByKey(transformedTransactionHistoryDetail.getMainTxnType())) {
			case PPBL_TRANSACTION:
				// In case of FD report codes TS will be sourceSystem
				if (isPpblForTsTransactions(transformedTransactionHistoryDetail)) {
					return false;
				}
				if (DOMESTIC_DC_REPORT_CODE.contains(getReportCode(transformedTransactionHistoryDetail))) {
					return false;
				}
				return StringUtils.isBlank(transformedTransactionHistoryDetail.getSourceTxnId());
			// in case of add-money, source will be wallet and we have set it parent and
			// for rest systems, parent will be wallet.
			case ADD_MONEY:
				String purpose = getPurpose(transformedTransactionHistoryDetail);
				if (ADD_AND_PAY_TXN_PURPOSE.equalsIgnoreCase(purpose)) {
					return false;
				}

				boolean setSourceSystemToWalletForPg = true;
				if (PAYTM_GIFT_VOUCHER.equalsIgnoreCase(purpose) || PAYTMGIFTVOUCHER.equalsIgnoreCase(purpose)) {
					setSourceSystemToWalletForPg = false;
				}

				if (TransactionSource.isPgTypeSource(transformedTransactionHistoryDetail.getStreamSource())
						&& setSourceSystemToWalletForPg) {
					transformedTransactionHistoryDetail
						.setSourceSystem(TransactionSource.WALLET.getTransactionSourceKey());
				}
				return TransactionSource.WALLET.getTransactionSourceKey()
					.equals(transformedTransactionHistoryDetail.getStreamSource());
			case P2P_OUTWARD:
				purpose = getPurpose(transformedTransactionHistoryDetail.getParticipants(),
						TransactionIndicator.DEBIT.getTransactionIndicatorKey());
				if (!StringUtils.isEmpty(purpose) && GV_PURCHASED.equalsIgnoreCase(purpose)
						&& TransactionSource.WALLET.getTransactionSourceKey()
							.equals(transformedTransactionHistoryDetail.getStreamSource())) {
					return true;
				}
				return StringUtils.isBlank(transformedTransactionHistoryDetail.getSourceTxnId());
			default:
				return StringUtils.isBlank(transformedTransactionHistoryDetail.getSourceTxnId());
		}
	}

	/**
	 * Checks if the current txn is a child txn for a TS parent. ie. IMPS or FD cases.
	 * @param transformedTransactionHistoryDetail current txn
	 * @return is child PPBL for a parent TS
	 */
	private boolean isPpblForTsTransactions(
			final TransformedTransactionHistoryDetail transformedTransactionHistoryDetail) {
		if (TransactionSource.PPBL.getTransactionSourceKey()
			.equals(transformedTransactionHistoryDetail.getStreamSource())
				&& transformedTransactionHistoryDetail.getParticipants() != null
				&& transformedTransactionHistoryDetail.getParticipants().size() == 1) {
			TransformedParticipant transformedParticipant = transformedTransactionHistoryDetail.getParticipants()
				.get(0);
			// Checking the reportCodes
			if (transformedParticipant != null) {
				String reportCode = transformedParticipant.getContextMap() != null
						? transformedParticipant.getContextMap().get(REPORT_CODE) : null;
				return fdReportCodes.contains(reportCode)
						|| utility.isWhiteListedImpsOutward(transformedTransactionHistoryDetail);
			}
		}
		return false;
	}

	private StoredDocDetails getStoredDtoDetails(final TransformedTransactionHistoryDetail source) throws Exception {
		if (source == null) {
			return null;
		}

		TransformedTransactionHistoryDetail storedRecord = null;

		// variable: retriedAsFirstEventAlreadyPresentInEs = true that means that event is
		// retried
		// after trying to index event with opType = create and ES already has event
		boolean retriedAsFirstEventAlreadyPresentInEs = (source != null && source.getContextMap() != null
				&& TRUE.equals(source.getContextMap().get(EVENT_ALREADY_PRESENT_IN_ES)));

		// if event is retried and with exception, than that means event is already
		// present in ES,
		// remove that flag added to identify this retry case, as this flag is not needed
		// in ES
		if (retriedAsFirstEventAlreadyPresentInEs) {
			metricsAgent.incrementCount("EVENT_ALREADY_PRESENT_IN_ES", SOURCE + source.getStreamSource());
			source.getContextMap().remove(EVENT_ALREADY_PRESENT_IN_ES);
		}
		else {
			storedRecord = cacheClient.getUpdatedRecord(CacheKeyManager.getSelfKey(source));
		}
		log.debug("pipeline :{}, systemId :{}, fetched stored data :{} from cache.", Utility.getActivePipelineName(),
				source.getTxnId(), storedRecord == null ? null : storedRecord.getTxnId());

		Boolean isFetchedFromCache = Boolean.FALSE;
		if (Objects.nonNull(storedRecord)) {
			isFetchedFromCache = Boolean.TRUE;

			String[] tags = Utility.getMetricTagsForGetRequestServedForGroupingAndMerging(storedRecord, CACHE);
			metricsAgent.incrementCount(MERGING_GET_REQUESTS_SERVED, tags);
		}
		// No need to fetch from ES for wallet settlement when not present in cache. High
		// Get TPS at Elasticsearch. (Un-necessary load)
		// for others also, if no data found in Cache, no need to find in ES. for this
		// cache interval needs to be increased.
		if (storedRecord == null
				&& !TransactionTypeEnum.WALLET_SETTLEMENT.getTransactionTypeKey().equals(source.getMainTxnType())
				&& (retriedAsFirstEventAlreadyPresentInEs || shouldQueryEs(source))) {
			log.warn("Fetching stored record from es with unique Id for systemid:{}", source.getTxnId());
			storedRecord = this.iRepository.getRecordUsingUniqueId(source);
			log.debug("pipeline :{}, systemId :{}, fetched stored data :{} from es.", Utility.getActivePipelineName(),
					source.getTxnId(), storedRecord);

			String[] tags = Utility.getMetricTagsForGetRequestServedForGroupingAndMerging(storedRecord, ES);
			metricsAgent.incrementCount(MERGING_GET_REQUESTS_SERVED, tags);
		}

		if (storedRecord != null) {
			// set storedRecord.setIs1stEvent to false to handle case:
			// when 1st event has higher updated date than the update event, in that case
			// during self merging
			// storedRecord will have priority and it will set Is1stEvent field to true
			// but there is already an event present
			// in ES, so marking it explicitly as false
			storedRecord.setIs1stEvent(Boolean.FALSE);
		}
		// Calling for setting isNewDoc Flag value
		setIsNewDocFlagValue(source, storedRecord);
		// Calling for set Original Status in case it is null
		setOriginalStatusInStoredAndSource(source, storedRecord);

		return new StoredDocDetails(storedRecord, isFetchedFromCache);
	}

	// below function: shouldQueryEs is an optimisation over ES,
	// IMPL: created to stop ES Query of the First Event of a txn in case
	// when currentTimeStamp-txnDate < cacheTTL-someBuffer
	private boolean shouldQueryEs(final TransformedTransactionHistoryDetail source) {
		if (source == null) {
			throw new RuntimeException("source is null");
		}
		// For dc-main pipeline we always query es.As snapshot sop is dependent on es get
		// call.
		if (Objects.nonNull(source.getContextMap()) && TRUE.equals(source.getContextMap().get(IS_FROM_DC_PIPELINE))) {
			return true;
		}
		if (!rolloutStrategyHelper.isUserWhiteListed("stop1stEventEsCall", source.getEntityId())) {
			return true;
		}

		Long txnDate = source.getTxnDate();
		Long currentDate = new Date().getTime();
		Long dateDiff = currentDate - txnDate;

		boolean shouldQueryEs = true;

		// should stop query i.e shouldQueryEs=false, for only mentioned sources in
		// variable: stopFirstEventEsCallFor
		shouldQueryEs = !this.stopFirstEventEsCallFor
			.contains(TransactionSource.getTransactionSourceEnumByKey(source.getStreamSource()));

		// should stop query i.e shouldQueryEs=false, when time diff is < 1 minute,
		// when we are sure that event would be present in Aerospike
		shouldQueryEs = shouldQueryEs || dateDiff > this.stopFirstEventEsCallForTimeDiff;

		// if at this point shouldQueryEs=false this mean we do not need to Query ES
		// as event is (supposedly)first event and have came withing 1 min timediff
		if (!shouldQueryEs) {
			log.warn(
					"Not querying ES for txnId : {} and source : {} ,"
							+ " as event is not present in Aeropsike and date diff < {}ms",
					source.getTxnId(), source.getStreamSource(), this.stopFirstEventEsCallForTimeDiff);
			metricsAgent.incrementCount("FIRST_EVENT_ES_CALL_STOPPED", SOURCE + source.getStreamSource(),
					STOPPED + TRUE);
			source.setIs1stEvent(Boolean.TRUE);
		}
		else {
			metricsAgent.incrementCount("FIRST_EVENT_ES_CALL_STOPPED", SOURCE + source.getStreamSource(),
					STOPPED + FALSE);
			source.setIs1stEvent(Boolean.FALSE);
		}

		return shouldQueryEs;
	}

	// Method to set Original Status value in case it is null in source and storedDto
	// This is for handling txns that just initiate before deployment
	private void setOriginalStatusInStoredAndSource(final TransformedTransactionHistoryDetail sourceDto,
			final TransformedTransactionHistoryDetail storedDto) {
		if (Objects.nonNull(storedDto) && Objects.isNull(storedDto.getOriginalStatus())
				&& Objects.nonNull(storedDto.getStatus())) {
			storedDto.setOriginalStatus(storedDto.getStatus());
		}

		if (Objects.nonNull(sourceDto) && Objects.isNull(sourceDto.getOriginalStatus())
				&& Objects.nonNull(sourceDto.getStatus())) {
			sourceDto.setOriginalStatus(sourceDto.getStatus());
		}
	}

	// Method to set isNewDoc flag value
	private void setIsNewDocFlagValue(final TransformedTransactionHistoryDetail sourceDto,
			final TransformedTransactionHistoryDetail storedDto) {
		// setting null for handling retry cases
		sourceDto.setDocCreatedDate(null);
		sourceDto.setDocUpdatedDate(null);
		sourceDto.setDocEndStatusDate(null);
		sourceDto.setIsNewDoc(null);
		if (storedDto == null) {
			sourceDto.setIsNewDoc(true);
		}
	}

	private void handleRefundOrReversalCase(final TransformedTransactionHistoryDetail finalEsDocument,
			final Set<TransformedTransactionHistoryDetail> mergedDtos) throws Exception {
		// note
		if (!reversalAndRefundTypeList
			.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(finalEsDocument.getMainTxnType()))) {
			return;
		}
		log.info(
				"SystemId:{}, This is an reversal or refunded type txn, so finding its parent and updating the status of parent.");
		TransformedTransactionHistoryDetail updatedParent = this.getUpdatedParentTransaction(finalEsDocument);
		if (updatedParent != null) {
			mergedDtos.add(updatedParent);
		}
	}

	/***
	 * This will return the document will need to be insert in ES.
	 * @param source (dataType: tthd)
	 * @return
	 */
	private MergedDocDetails getFinalDocument(final TransformedTransactionHistoryDetail source) throws Exception {
		StoredDocDetails storedDocDetails = this.getStoredDtoDetails(source);
		TransformedTransactionHistoryDetail storedDto = storedDocDetails == null ? null : storedDocDetails.getTthd();

		TransformedTransactionHistoryDetail storedDtoCopy = null;
		Boolean isFetchedFromCache = storedDocDetails == null ? null : storedDocDetails.getIsFetchedFromCache();
		Boolean isRedundantUpdate = Boolean.FALSE;

		if (storedDto != null) {
			String jsonStr = objectMapper.writeValueAsString(storedDto);
			storedDtoCopy = objectMapper.readValue(jsonStr, TransformedTransactionHistoryDetail.class);
		}

		// TODO :- Remove this special check once relay of upi events is complete.
		// Special check just for relay purpose.
		if (Objects.nonNull(storedDto)
				&& TransactionSource.UPI.getTransactionSourceKey().equals(source.getStreamSource())) {
			if (relayUtility.isDocToBeDiscarded(source, storedDto)) {
				metricsAgent.incrementCount(RELAY_EVENTS, ACTION + COLON + DISCARD);
				log.warn("Discarding relay doc as tpap doc is already present for txnId : {}", source.getTxnId());
				return null;
			}
			if (!relayUtility.isStoredDocNeedsToBeUsed(source, storedDto)) {
				metricsAgent.incrementCount(RELAY_EVENTS, ACTION + COLON + OVERRIDE);
				log.warn("Overriding relay doc as new tpap doc is received : {}", source.getTxnId());
				storedDto = null;
			}
		}

		// merging document field by field based on updatedTime
		TransformedTransactionHistoryDetail mergedDto = this.getMergedDto(source, storedDto);
		// rule can be applied based on transactionType. For wallet_settlement this will
		// return true if sourceTxnId blank. But that willn't be blank.
		if (Objects.isNull(mergedDto)) {
			return null;
		}
		mergedDto.setIsSource(this.findIfThisParent(mergedDto));
		this.iVisibilityCreator.setVisibleCriteria(mergedDto);
		enrichTxnDetailsInOmsRefund(mergedDto);
		enrichBankDataFromParentRptCode(mergedDto);
		enrichMerchantData(mergedDto);
		if (com.org.panaroma.commons.utils.Utility.isAddnPayRefundTxn(mergedDto)) {
			Utility.setIgnoredParticipantField(mergedDto);
		}

		/*
		 * Below code specific to IPO_MANDATE is written here for backfilling to be done.
		 * This will be removed from here in future when some generic code is written for
		 * handling of flags which are not to be overridden
		 */

		Boolean isIpoMandateBackfilledTxn = null;
		Boolean backfillingIdentifierFlagPresentInStoredDto = null;

		// remove backfilling identifier & the call checkIfObjectEqual method
		if (TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey().equals(mergedDto.getMainTxnType())) {
			if (Objects.nonNull(mergedDto.getContextMap())
					&& BackFillingIdentifierEnum.IPO_MANDATE_TXNS_BACKFILLING.getBackFillingIdentifierKey()
						.equals(mergedDto.getContextMap().get(BACKFILLING_IDENTIFIER))) {
				isIpoMandateBackfilledTxn = Boolean.TRUE;
				mergedDto.getContextMap().remove(BACKFILLING_IDENTIFIER);
			}
			if (Objects.nonNull(storedDtoCopy) && Objects.nonNull(storedDtoCopy.getContextMap())
					&& BackFillingIdentifierEnum.IPO_MANDATE_TXNS_BACKFILLING.getBackFillingIdentifierKey()
						.equals(storedDtoCopy.getContextMap().get(BACKFILLING_IDENTIFIER))) {
				backfillingIdentifierFlagPresentInStoredDto = Boolean.TRUE;
				storedDtoCopy.getContextMap().remove(BACKFILLING_IDENTIFIER);
			}
		}
		if (Utility.checkIfObjectEqual(storedDtoCopy, mergedDto) && !isOnlyForGrouping(mergedDto)
				&& !isForRetryBySystem(mergedDto) && !isDtosAreSameToBeIgnored(mergedDto)) {
			isRedundantUpdate = Boolean.TRUE;
			metricsAgent.incrementCount(DTOS_ARE_SAME_COUNT,
					SOURCE + TransactionSource.getTransactionSourceEnumByKey(mergedDto.getStreamSource()),
					"isIpoMandateBackfilledTxn:" + isIpoMandateBackfilledTxn);
			if (Boolean.TRUE.equals(isFetchedFromCache)) {
				log.warn(
						"isIpoMandateBackfilledTxn : {} As Dtos are same and fetched from cache so sending it to Flink sink. TxnId : {}",
						isIpoMandateBackfilledTxn, mergedDto.getTxnId());
			}
			else {
				log.warn(
						"isIpoMandateBackfilledTxn : {} SystemId: {} source : {} ,dtos are same, so not pushing into ES/cache again.",
						isIpoMandateBackfilledTxn, mergedDto.getTxnId(), mergedDto.getStreamSource());
				return null;
			}
		}

		// Populate back the identifier so that it can be saved in DB
		if (Boolean.TRUE.equals(isIpoMandateBackfilledTxn)
				|| Boolean.TRUE.equals(backfillingIdentifierFlagPresentInStoredDto)) {
			mergedDto.getContextMap()
				.put(BACKFILLING_IDENTIFIER,
						BackFillingIdentifierEnum.IPO_MANDATE_TXNS_BACKFILLING.getBackFillingIdentifierKey());
		}

		transformerHelperService.setViewTxnIndicatorInDto(mergedDto);

		return new MergedDocDetails(mergedDto, isFetchedFromCache, isRedundantUpdate);
	}

	private void enrichMerchantData(final TransformedTransactionHistoryDetail mergedDto) {
		if (mergedDto == null || Objects.isNull(mergedDto.getParticipants())) {
			return;
		}
		for (TransformedParticipant participants : mergedDto.getParticipants()) {
			if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participants.getEntityType())) {
				TransactionSource transactionSource = TransactionSource
					.getTransactionSourceEnumByKey(mergedDto.getStreamSource());
				MerchantData cacheMerchantData = this.cacheClient.getUpdatedMerchantData(
						CacheKeyManager.getKeyForMerchantDetails(transactionSource, mergedDto.getTxnId()));
				if (cacheMerchantData != null) {
					this.setMerchantData(cacheMerchantData, mergedDto);
				}
			}
			if (Objects.nonNull(participants.getMerchantData()) && mergedDto.getStreamSource() == 2) {
				log.debug("Merchant Data after merging order and merchant leg : {}. SystemId :{}",
						participants.getMerchantData(), mergedDto.getTxnId());
			}
		}
	}

	private void setMerchantData(final MerchantData merchantData, final TransformedTransactionHistoryDetail detail) {
		for (TransformedParticipant participant : detail.getParticipants()) {
			if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
				// update merchant data accordingly.
				// if we are getting notBlank name in merchantData and the participant
				// name is null/paytm, then we will set the name.
				if ((StringUtils.isBlank(participant.getName()) || PAYTM.equalsIgnoreCase(participant.getName()))
						&& StringUtils.isNotBlank(merchantData.getMerchantName())) {
					participant.setName(merchantData.getMerchantName());
				}
				participant.setLogoUrl((StringUtils.isBlank(participant.getLogoUrl()))
						? merchantData.getMerchantLogoUrl() : participant.getLogoUrl());
				TransformedMerchantData transformedMerchantData = participant.getMerchantData();
				transformedMerchantData.setMerchantType(
						(transformedMerchantData.getMerchantType() != null || merchantData.getMerchantType() == null)
								? transformedMerchantData.getMerchantType()
								: merchantData.getMerchantType().getMerchantTypeKey());
				transformedMerchantData.setMerchantId((StringUtils.isBlank(transformedMerchantData.getMerchantId())
						|| PAYTM_MID.equalsIgnoreCase(transformedMerchantData.getMerchantId()))
								? merchantData.getMerchantId() : transformedMerchantData.getMerchantId());
				transformedMerchantData.setMccCode(StringUtils.isBlank(transformedMerchantData.getMccCode())
						? merchantData.getMccCode() : transformedMerchantData.getMccCode());
				transformedMerchantData.setMerchantPayMode((transformedMerchantData.getMerchantPayMode() != null
						|| merchantData.getMerchantPayMode() == null) ? transformedMerchantData.getMerchantPayMode()
								: merchantData.getMerchantPayMode().getPayModeKey());
				transformedMerchantData.setMerchantCategory((transformedMerchantData.getMerchantCategory() != null
						|| merchantData.getMerchantCategory() == null) ? transformedMerchantData.getMerchantCategory()
								: merchantData.getMerchantCategory().getCategoryTagKey());
				participant.setEntityId(transformedMerchantData.getMerchantId());
				transformedMerchantData
					.setMerchantSubCategory(StringUtils.isBlank(transformedMerchantData.getMerchantSubCategory())
							? merchantData.getMerchantSubCategory() : transformedMerchantData.getMerchantSubCategory());
			}
		}
	}

	// fetch parent transaction for reversal reportcodes in bank data, to enrich certain
	// feild in reversla transactions
	// details are fetched using parentReportcode, accountNumber, RRN
	private void enrichBankDataFromParentRptCode(final TransformedTransactionHistoryDetail mergedDto) throws Exception {
		if (mergedDto == null || mergedDto.getIsVisible() == null || !mergedDto.getIsVisible()
				|| mergedDto.getContextFilterMap() == null
				|| StringUtils.isEmpty(mergedDto.getContextFilterMap().get("rrn"))) {
			return;
		}
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(mergedDto.getStreamSource())) {
			log.info("txnId : {}, going to find parent reportCode", mergedDto.getTxnId());
			String parentRptCode = BankUtility.dataNeeded(mergedDto);
			Map<String, Object> paramMap = new HashMap<>();
			if (mergedDto.getContextFilterMap() != null && parentRptCode != null) {
				// log.warn("txnId : {}, Parent ReportCode : {}, found for mapping",
				// mergedDto.getTxnId(), parentRptCode);
				paramMap.put("contextFilterMap.reportCode", parentRptCode.split("_")[0]);
				paramMap.put("contextFilterMap.acctNum", mergedDto.getContextFilterMap().get("acctNum"));
				paramMap.put("contextFilterMap.rrn", mergedDto.getContextFilterMap().get("rrn"));
				TransformedTransactionHistoryDetail parentRptCodeData = this.iRepository.fetchDataFromAlias(paramMap,
						mergedDto);
				if (parentRptCodeData == null) {
					return;
				}
				log.debug("txnId : {}, Parent ReportCode : {} data successfully fetched", mergedDto.getTxnId(),
						parentRptCode);
				Map<String, String> contextMap = new HashMap<>();
				for (TransformedParticipant participant : mergedDto.getParticipants()) {
					if (participant.getTxnIndicator().equals(mergedDto.getTxnIndicator())
							&& participant.getContextMap() != null) {
						contextMap = participant.getContextMap();
					}
				}
				Map<String, String> parentContextMap = new HashMap<>();
				for (TransformedParticipant participant : parentRptCodeData.getParticipants()) {
					if (participant.getTxnIndicator().equals(parentRptCodeData.getTxnIndicator())
							&& participant.getContextMap() != null) {
						parentContextMap = participant.getContextMap();
					}
				}
				if (StringUtils.isEmpty(contextMap.get("merchantName"))) {
					contextMap.put("merchantName", parentContextMap.get("merchantName"));
				}
				if (StringUtils.isEmpty(contextMap.get("dcMerchantNmLoc"))) {
					contextMap.put("dcMerchantNmLoc", parentContextMap.get("dcMerchantNmLoc"));
				}
				if (StringUtils.isEmpty(contextMap.get("remitterName"))) {
					contextMap.put("remitterName", parentContextMap.get("remitterName"));
				}
				if (StringUtils.isEmpty(contextMap.get("remitterAcctNum"))) {
					contextMap.put("remitterAcctNum", parentContextMap.get("remitterAcctNum"));
				}
				if (StringUtils.isEmpty(contextMap.get("benefName"))) {
					contextMap.put("benefName", parentContextMap.get("benefName"));
				}
				if (StringUtils.isEmpty(contextMap.get("benefAcctNum"))) {
					contextMap.put("benefAcctNum", parentContextMap.get("benefAcctNum"));
				}
			}
		}
	}

	// Method for setting Created and last updated date of ES Doc
	private void setUthInternalDates(final TransformedTransactionHistoryDetail mergedDto,
			final boolean isOnlyForGrouping) {
		if (mergedDto != null) {
			Long currentDate = new Date().getTime();
			// if this is newDoc then add both created and updated value
			if (Boolean.TRUE.equals(mergedDto.getIsNewDoc()) && mergedDto.getDocCreatedDate() == null) {
				mergedDto.setDocCreatedDate(currentDate);
				// setting the diff in metrics
				if (!isOnlyForGrouping) {
					metricsAgent.recordTimeDiffBetweenDatesAsync(CREATED_TXN_DATE_DIFF, mergedDto.getTxnDate(),
							mergedDto.getDocCreatedDate(), mergedDto);
				}
			}
			// in every case update DocUpdatedDate value
			mergedDto.setDocUpdatedDate(currentDate);
			// log.warn("Setting docUpdatedDate as {} for txnId : {}",
			// mergedDto.getDocUpdatedDate(), mergedDto.getTxnId());
			// for end status date
			if (mergedDto.getDocEndStatusDate() == null && END_STATUS_INT_LIST.contains(mergedDto.getStatus())) {
				mergedDto.setDocEndStatusDate(currentDate);
				// pushing data into metrics
				metricsAgent.recordTimeDiffBetweenDatesAsync(END_STATUS_TXN_DATE_DIFF, mergedDto.getTxnDate(),
						mergedDto.getDocEndStatusDate(), mergedDto);
			}
		}
	}

	private boolean isForRetryBySystem(final TransformedTransactionHistoryDetail mergedDto) {
		return mergedDto.getContextMap() != null
				&& ("true".equalsIgnoreCase(mergedDto.getContextMap().get(IS_FOR_RETRY_FROM_SYSTEM))
						|| mergedDto.getContextMap().get(RETRY_PIPELINE_PUSH_COUNT) != null);
	}

	private boolean isDtosAreSameToBeIgnored(final TransformedTransactionHistoryDetail tthd) {
		return tthd.getContextMap() != null && (TRUE.equalsIgnoreCase(tthd.getContextMap().get(IGNORE_DTOS_ARE_SAME)));
	}

	private TransformedTransactionHistoryDetail getMergedDto(final TransformedTransactionHistoryDetail source,
			final TransformedTransactionHistoryDetail storedDto) throws Exception {

		IMerger merger = this.iMergerFactory
			.getMergerBySource(TransactionSource.getTransactionSourceEnumByKey(source.getStreamSource()));

		return (TransformedTransactionHistoryDetail) merger.mergeDocuments(source, storedDto);
	}

	/***
	 * Steps- 1 - Will find out all the refund txns for this parent txn. 2 - Calculate
	 * total sum of refund txns and set the current status of the parent txn.
	 * @param source (dataType: tthd)
	 */
	private void updateCurrentDtosBasedOnTheRefundedTransactions(final TransformedTransactionHistoryDetail source)
			throws Exception {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("streamSource", source.getStreamSource());
		paramMap.put("parentTxnId", source.getTxnId());
		paramMap.put("entityId", source.getEntityId());
		paramMap.put("status", StatusEnum.SUCCESS.getStatusKey());
		List<TransformedTransactionHistoryDetail> refundedReversalTxns = this.iRepository.fetchData(paramMap, source);
		if (refundedReversalTxns == null || refundedReversalTxns.size() == 0) {
			log.info("pipeline :{}, systemId :{}, No need to update status because no refunded/reversal txn found.",
					Utility.getActivePipelineName(), source.getTxnId());
			return;
		}
		BigDecimal totalAmount = BigDecimal.ZERO;
		Long maxUpdatedDate = 0L;
		for (TransformedTransactionHistoryDetail transactionHistoryDetails : refundedReversalTxns) {
			totalAmount = totalAmount.add(new BigDecimal(transactionHistoryDetails.getAmount()));
			maxUpdatedDate = transactionHistoryDetails.getUpdatedDate() > maxUpdatedDate
					? transactionHistoryDetails.getUpdatedDate() : maxUpdatedDate;
		}
		if (totalAmount.compareTo(new BigDecimal(source.getAmount())) == 0) {
			source.setStatus(StatusEnum.REFUNDED.getStatusKey());
		}
		else if (totalAmount.compareTo(new BigDecimal(source.getAmount())) < 0) {
			source.setStatus(StatusEnum.PARTIAL_REFUNDED.getStatusKey());
		}
		source.setUpdatedDate(maxUpdatedDate);
	}

	@Override
	public void repushDataIntoKafka(final TransactionHistoryDetails thd) {
	}

	/***
	 * @param stored - Object which will be updated
	 * @param update - Current object stream This method merge the update into stored if
	 * the value of update is not null.
	 */
	private void merge(final Object stored, final Object update) {
		if (!stored.getClass().isAssignableFrom(update.getClass())) {
			return;
		}
		Method[] methods = stored.getClass().getMethods();
		for (Method fromMethod : methods) {
			// Synthetic Check(Support For Jacoco)
			if (com.org.panaroma.commons.utils.Utility.skipMethod(fromMethod)) {
				continue;
			}
			if (fromMethod.getDeclaringClass().equals(stored.getClass()) && fromMethod.getName().startsWith("get")) {
				String fromName = fromMethod.getName();
				String toName = fromName.replace("get", "set");
				try {
					Method toMetod = stored.getClass().getMethod(toName, fromMethod.getReturnType());
					Object value = fromMethod.invoke(update, (Object[]) null);
					if (value != null) {
						toMetod.invoke(stored, value);
					}
				}
				catch (Exception e) {
					log.error("Exception while merging two object Exception: {}", CommonsUtility.exceptionFormatter(e));
					// e.printStackTrace();
				}
			}
		}
	}

	private TransformedTransactionHistoryDetail getUpdatedParentTransaction(
			final TransformedTransactionHistoryDetail thd) throws Exception {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("streamSource", thd.getStreamSource());
		paramMap.put("txnId", thd.getParentTxnId());
		paramMap.put("entityId", thd.getEntityId());
		List<TransformedTransactionHistoryDetail> tthd = this.iRepository.fetchData(paramMap, thd);
		log.debug("SystemID :{} Found parent :{}", thd.getTxnId(), tthd);
		if (tthd == null || tthd.size() == 0) {
			return null;
		}
		TransformedTransactionHistoryDetail parentTxn = tthd.get(0);
		this.updateForPartialOrFullyRefunds(thd, parentTxn);
		return parentTxn;
	}

	/***
	 * @param refundedTransaction (dataType: tthd)
	 * @param parentTransaction (dataType: tthd)
	 */
	private void updateForPartialOrFullyRefunds(final TransformedTransactionHistoryDetail refundedTransaction,
			final TransformedTransactionHistoryDetail parentTransaction) throws Exception {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("streamSource", parentTransaction.getStreamSource());
		paramMap.put("parentTxnId", parentTransaction.getTxnId());
		paramMap.put("entityId", parentTransaction.getEntityId());
		paramMap.put("status", StatusEnum.SUCCESS.getStatusKey());
		List<TransformedTransactionHistoryDetail> allRefundedtxn = this.iRepository.fetchData(paramMap,
				parentTransaction);
		allRefundedtxn = allRefundedtxn == null ? new ArrayList<>() : allRefundedtxn;
		Boolean isCurrentRefundTxnStoredInEs = false;
		BigDecimal totalAmount = BigDecimal.ZERO;
		Long updatedDate = refundedTransaction.getUpdatedDate();
		for (TransformedTransactionHistoryDetail historyDetails : allRefundedtxn) {
			if (historyDetails.docId().equals(refundedTransaction.docId())) {
				isCurrentRefundTxnStoredInEs = true;
			}
			totalAmount = totalAmount.add(new BigDecimal(historyDetails.getAmount()));
			updatedDate = updatedDate < historyDetails.getUpdatedDate() ? historyDetails.getUpdatedDate() : updatedDate;
		}
		if (!isCurrentRefundTxnStoredInEs) {
			totalAmount = totalAmount.add(new BigDecimal(refundedTransaction.getAmount()));
		}
		parentTransaction.setUpdatedDate(updatedDate);
		if (totalAmount.compareTo(new BigDecimal(parentTransaction.getAmount())) == 0) {
			parentTransaction.setStatus(StatusEnum.REFUNDED.getStatusKey());
		}
		else if (totalAmount.compareTo(new BigDecimal(parentTransaction.getAmount())) < 0) {
			parentTransaction.setStatus(StatusEnum.PARTIAL_REFUNDED.getStatusKey());
		}
	}

	public TransactionTypeEnum getTransactionType(final TransactionHistoryDetails thd,
			final TxnParticipants txnParticipants) {
		if (thd.getTxnType() == null) {
			// we are setting null here and this will be handled in merging, when we will
			// get order details in case of pg.
			return null;
		}
		switch (thd.getMainTxnType()) {
			case P2P_INWARD:
			case P2P_OUTWARD:
				if (TransactionIndicator.CREDIT.equals(txnParticipants.getTxnIndicator())) {
					return TransactionTypeEnum.P2P_INWARD;
				}
				else {
					return TransactionTypeEnum.P2P_OUTWARD;
				}
			case P2P2M:
				if (TransactionIndicator.CREDIT.equals(txnParticipants.getTxnIndicator())) {
					return TransactionTypeEnum.P2P2M_INWARD;
				}
				else {
					return TransactionTypeEnum.P2P2M_OUTWARD;
				}
			default:
		}
		return thd.getTxnType();
	}

	/**
	 * Checks if this participant is part of Paytm Ecosystem or not. Either we should get
	 * customer ID or merchant ID to pass this predicate. Return true if Participant is
	 * part of Paytm Ecosystem.
	 */
	private static Predicate<TxnParticipants> participantPartOfPaytmEcosystemPredicate = txnParticipant -> {
		return StringUtils.isNotBlank(txnParticipant.getCustomerId()) || (txnParticipant.getMerchantData() != null
				&& StringUtils.isNotBlank(txnParticipant.getMerchantData().getMerchantId()));
	};

	private void createLocation(final TransactionHistoryDetails thd, final TransformedTransactionHistoryDetail tDto) {
		Utility.createLocation(thd, tDto);
	}

	private void enrichTxnDetailsInOmsRefund(final TransformedTransactionHistoryDetail omsRefundTxn) throws Exception {
		if (Objects.isNull(omsRefundTxn) || Boolean.FALSE.equals(omsRefundTxn.getIsVisible())
				|| !TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(omsRefundTxn.getTxnType())
				|| !TransactionSource.OMS.getTransactionSourceKey().equals(omsRefundTxn.getStreamSource())
				|| Objects.isNull(omsRefundTxn.getContextMap())) {
			return;
		}

		String forwardTxnDate = omsRefundTxn.getContextMap().get(FORWARD_TXNDATE);
		Map<String, Object> paramMap = new HashMap<>();

		paramMap.put("streamSource", TransactionSource.OMS.getTransactionSourceKey());
		paramMap.put("txnId", omsRefundTxn.getParentTxnId());
		paramMap.put("entityId", omsRefundTxn.getEntityId());

		log.info("Fetching Oms Forward txn details for merging in refund txn. Forward txnId: {}, Oms Refund txnId: {}",
				omsRefundTxn.getParentTxnId(), omsRefundTxn.getTxnId());

		List<TransformedTransactionHistoryDetail> forwardTxn = null;

		if (StringUtils.isBlank(forwardTxnDate)) {
			forwardTxn = this.iRepository.fetchDataOnlyFromAlias(paramMap, omsRefundTxn);
		}
		else {
			forwardTxn = this.iRepository.fetchRecord(paramMap, Long.parseLong(forwardTxnDate),
					omsRefundTxn.getParentTxnId());
		}

		if (CollectionUtils.isEmpty(forwardTxn)) {
			log.warn("Oms Forward txn not present in ES for merging in refund txn. Forward txnId: {},"
					+ " Oms Refund txnId: {}", omsRefundTxn.getParentTxnId(), omsRefundTxn.getTxnId());
			return;
		}

		omsRefundTxn.getContextMap().remove(FORWARD_TXNDATE);
		mergeForwardTxnDetailsInRefundTxn(forwardTxn, omsRefundTxn);
	}

	private void mergeForwardTxnDetailsInRefundTxn(final List<TransformedTransactionHistoryDetail> forwardTxn,
			final TransformedTransactionHistoryDetail omsRefundTxn) {
		if (CollectionUtils.isEmpty(forwardTxn) || forwardTxn.size() > 1
				|| Objects.isNull(forwardTxn.get(0).getCartDetails())
				|| Objects.isNull(forwardTxn.get(0).getParticipants())) {
			log.warn("Oms Forward txn details are not available for merging in refund txn. Forward txnId: {},"
					+ " Oms Refund txnId: {}", forwardTxn.get(0).getTxnId(), omsRefundTxn.getTxnId());
			return;
		}

		TransformedParticipant omsForwardMerchantParticipant = MergerUtility.getMerchantParticipant(forwardTxn.get(0));
		TransformedParticipant omsForwardUserParticipant = MergerUtility.getUserParticipant(forwardTxn.get(0));

		if (Objects.isNull(omsForwardMerchantParticipant) || Objects.isNull(omsForwardUserParticipant)) {
			log.warn("Oms Forward txn details are not available for merging in refund txn. Forward txnId: {},"
					+ " Oms Refund txnId: {}", forwardTxn.get(0).getTxnId(), omsRefundTxn.getTxnId());
			return;
		}

		TransformedParticipant omsRefundMerchantParticipant = MergerUtility.getMerchantParticipant(omsRefundTxn);
		TransformedParticipant omsRefundUserParticipant = MergerUtility.getUserParticipant(omsRefundTxn);

		omsRefundTxn.setCartDetails(forwardTxn.get(0).getCartDetails());

		if (Objects.nonNull(omsRefundMerchantParticipant)) {
			omsRefundMerchantParticipant.setName(omsForwardMerchantParticipant.getName());
			omsRefundMerchantParticipant.setLogoUrl(omsForwardMerchantParticipant.getLogoUrl());
		}

		if (Objects.nonNull(omsRefundUserParticipant)
				&& TO_SOURCE.equals(omsRefundTxn.getContextMap().get(REFUND_TYPE))) {
			TransformedBankData refundTxnBankData = omsRefundUserParticipant.getBankData();
			TransformedBankData forwardTxnBankData = omsForwardUserParticipant.getBankData();

			if (Objects.nonNull(forwardTxnBankData)) {
				refundTxnBankData.setBankName(forwardTxnBankData.getBankName());
				refundTxnBankData.setIfsc(forwardTxnBankData.getIfsc());
				refundTxnBankData.setAccNumber(forwardTxnBankData.getAccNumber());
				refundTxnBankData.setAccountType(forwardTxnBankData.getAccountType());
			}
		}

		ParentDetails parentDetails = ParentDetails.builder()
			.txnId(forwardTxn.get(0).getTxnId())
			.txnDate(forwardTxn.get(0).getTxnDate().toString())
			.txnSource(Objects
				.requireNonNull(TransactionSource.getTransactionSourceEnumByKey(forwardTxn.get(0).getStreamSource()))
				.toString())
			.parentExistsInDb(true)
			.build();

		omsRefundTxn.setParentDetails(parentDetails);
	}

}