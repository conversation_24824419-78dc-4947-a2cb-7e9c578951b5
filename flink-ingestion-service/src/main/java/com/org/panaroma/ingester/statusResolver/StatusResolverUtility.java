package com.org.panaroma.ingester.statusResolver;

import static com.org.panaroma.ingester.constants.Constants.CHANNEL_CODE;
import static com.org.panaroma.ingester.constants.Constants.DATA;
import static com.org.panaroma.ingester.constants.Constants.SEQ_NO;
import static com.org.panaroma.ingester.constants.Constants.TRANSACTION_ID;
import static com.org.panaroma.ingester.constants.Constants.UTH;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.UpiStatusFetcherRequest;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import java.io.Serializable;
import java.util.Date;
import java.security.NoSuchAlgorithmException;

import com.org.panaroma.commons.utils.HashUtility;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class StatusResolverUtility implements Serializable {

	private ObjectMapper objectMapper;

	@Autowired
	public StatusResolverUtility(final ObjectMapper objectMapper) {
		this.objectMapper = objectMapper;
	}

	public static UpiStatusFetcherRequest createUpiStatusFetcherRequest(
			final TransformedTransactionHistoryDetail detail) {
		return UpiStatusFetcherRequest.builder()
			.channelCode(UTH)
			.txnId(detail.getTxnId())
			.seqNum(detail.getTxnId())
			.build();
	}

	String createJwtForGatewayRouting(UpiStatusFetcherRequest upiStatusFetcherRequest, String secretKey)
			throws JsonProcessingException, NoSuchAlgorithmException {
		String requestBody = objectMapper.writeValueAsString(upiStatusFetcherRequest);
		return JWT.create()
			.withIssuer(UTH)
			.withIssuedAt(new Date())
			.withClaim(DATA, HashUtility.generateSha256Hash(requestBody))
			.sign(Algorithm.HMAC256(secretKey));
	}

	String createJwt(TransformedTransactionHistoryDetail tthd, String seqNum, String secretKey) {
		return JWT.create()
			.withIssuer(UTH)
			.withIssuedAt(new Date())
			.withClaim(CHANNEL_CODE, UTH)
			.withClaim(SEQ_NO, seqNum)
			.withClaim(TRANSACTION_ID, tthd.getTxnId())
			.sign(Algorithm.HMAC256(secretKey));
	}

}
