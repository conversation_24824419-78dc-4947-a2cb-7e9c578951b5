package com.org.panaroma.ingester.transformer;

import static com.org.panaroma.ingester.constants.Constants.omsVisbileOrderStatus;
import static com.org.panaroma.ingester.constants.Constants.omsVisbilePaymentStatus;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.OmsPaymentKind;
import com.org.panaroma.commons.enums.OmsPaymentMethodEnum;
import com.org.panaroma.commons.enums.OmsOrderStatusEnum;
import com.org.panaroma.commons.enums.OmsPaymentStatusEnum;
import com.org.panaroma.commons.utility.configurablePropertyUtility.BopConfigurablePropertiesHolder;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.ingester.dto.OmsDto;
import com.org.panaroma.ingester.dto.OmsTransactionResponse;
import com.org.panaroma.ingester.dto.PaymentDetails;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.utils.OmsDataToThdConverter;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class OmsDataTransformer extends BroadcastProcessFunction<OmsDto, Map<String, Object>, TransactionHistoryDetails>
		implements FlatMapFunction<OmsDto, TransactionHistoryDetails>, Serializable {

	private OmsDataToThdConverter omsDataToThdConverter;

	private final RolloutStrategyHelper rolloutStrategyHelper;

	private final MetricsAgent metricsAgent;

	private final ObjectMapper objectMapper;

	private final BopConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public OmsDataTransformer(final OmsDataToThdConverter omsDataToThdConverter,
			final RolloutStrategyHelper rolloutStrategyHelper, final MetricsAgent metricsAgent,
			final ObjectMapper objectMapper, final BopConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.omsDataToThdConverter = omsDataToThdConverter;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.metricsAgent = metricsAgent;
		this.objectMapper = objectMapper;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	@Override
	public void flatMap(final OmsDto omsDto, final Collector<TransactionHistoryDetails> collector) throws Exception {
		if (Objects.isNull(omsDto)) {
			log.warn("OMS Event is null");
			return;
		}

		Set<TransactionHistoryDetails> transformedSet = new HashSet<>();
		try {
			log.debug("OMS Event received from OMS kafka : {}", omsDto);
			log.warn("OMS Event received from OMS kafka orderId : {}, customerId : {}", omsDto.getOrderId(),
					omsDto.getCustomerId());

			metricsAgent.incrementCount("OMS_EVENT_COUNT");

			if (!isOmsEventEligibleToProcess(omsDto)) {
				// log.warn("OMS Event is not eligible to process so dropping it");
				return;
			}

			log.warn("Valid OMS Event received from OMS kafka : {}", omsDto);

			Set<TransactionHistoryDetails> transactionHistoryDetails = omsDataToThdConverter.mapOmsDataToThd(omsDto,
					omsDto.getPayments().get(0).getKind());

			if (ObjectUtils.isEmpty(transformedSet)) {
				log.warn("Transformed event set is empty omsDto: {}", omsDto);
			}

			transformedSet.addAll(transactionHistoryDetails);
		}
		catch (Exception e) {
			log.error("Exception while converting oms data to thd for orderId : {}, Exception :{}", omsDto.getOrderId(),
					CommonsUtility.exceptionFormatter(e));
			metricsAgent.incrementCount("OMS_TRANSFORMATION_ERROR_COUNT");
		}
		transformedSet.forEach(collector::collect);
	}

	private boolean isOmsEventEligibleToProcess(final OmsDto omsDto) {
		if (Objects.isNull(omsDto)) {
			return false;
		}

		if (StringUtils.isBlank(omsDto.getOrderId()) || StringUtils.isBlank(omsDto.getGrandtotal())
				|| StringUtils.isBlank(omsDto.getCustomerId()) || StringUtils.isBlank(omsDto.getCreatedAt())
				|| StringUtils.isBlank(omsDto.getUpdatedAt())) {
			log.warn("orderId/grandTotal/created/updatedDate is/are null {}", omsDto);
			return false;
		}

		if (!rolloutStrategyHelper.isUserWhiteListed("omsIntegration", omsDto.getCustomerId())) {
			log.warn("User is not whitelisted for OMS Integration txnId: {}", omsDto.getOrderId());
			return false;
		}

		OmsOrderStatusEnum omsOrderStatusEnum = OmsOrderStatusEnum.getStatusEnumByKey(omsDto.getPaymentStatus());
		if (Objects.isNull(omsOrderStatusEnum) || !omsVisbileOrderStatus.contains(omsOrderStatusEnum)) {
			log.warn("Invalid OMS Order Status : {}", omsDto);
			return false;
		}

		if (CollectionUtils.isEmpty(omsDto.getPayments())) {
			log.warn("Payment Info is empty : {}", omsDto);
			return false;
		}

		for (PaymentDetails paymentDetails : omsDto.getPayments()) {
			if (StringUtils.isBlank(paymentDetails.getPgAmount())) {
				log.warn("Payment Amount is null orderId: {}, paymentDetails: {}", omsDto.getOrderId(), paymentDetails);
			}

			OmsPaymentKind paymentKind = OmsPaymentKind.getOmsPaymentKindByKey(paymentDetails.getKind());
			OmsPaymentMethodEnum paymentMethod = OmsPaymentMethodEnum
				.getStatusEnumByValue(paymentDetails.getPaymentMethod());
			OmsPaymentStatusEnum paymentStatus = OmsPaymentStatusEnum.getStatusEnumByKey(paymentDetails.getStatus());

			if (Objects.isNull(paymentKind) || OmsPaymentKind.REFUND.equals(paymentKind)) {
				log.warn("Invalid paymentKind or Refund payment Kind orderId: {}, paymentDetails: {}",
						omsDto.getOrderId(), paymentDetails);
				return false;
			}

			if (!isPaymentMethodAndAmountValid(omsDto, paymentDetails, paymentMethod)) {
				return false;
			}

			if (Objects.isNull(paymentStatus) || !omsVisbilePaymentStatus.contains(paymentStatus)) {
				log.warn("Invalid or not handled payment Status orderId: {}, paymentDetails: {}", omsDto.getOrderId(),
						paymentDetails);
				return false;
			}

			if (OmsPaymentMethodEnum.UPI.equals(paymentMethod)) {
				if (Objects.nonNull(paymentDetails.getOmsTransactionResponse())) {
					if (paymentDetails.getOmsTransactionResponse() instanceof String) {
						try {
							OmsTransactionResponse omsTransactionResponse = objectMapper.readValue(
									(String) paymentDetails.getOmsTransactionResponse(), OmsTransactionResponse.class);
							if (StringUtils.isNotBlank(omsTransactionResponse.getRrnCode())) {
								return true;
							}
						}
						catch (Exception e) {
							log.error(
									"Exception while parsing omsTransactionResponse for orderId: {},"
											+ " omsTransactionResponse: {}, exception: {}",
									omsDto.getOrderId(), paymentDetails.getOmsTransactionResponse(),
									CommonsUtility.exceptionFormatter(e));
							return false;
						}
					}
				}
				log.warn("RRN is null for Payment Method UPI orderId: {}, paymentDetails: {}", omsDto.getOrderId(),
						paymentDetails);
				return false;
			}
		}

		return true;
	}

	private boolean isPaymentMethodAndAmountValid(OmsDto omsDto, PaymentDetails paymentDetails,
			OmsPaymentMethodEnum paymentMethod) {
		if (Objects.isNull(paymentMethod)) {
			log.warn("Invalid or not handled payment method orderId: {}  paymentDetails: {}", omsDto.getOrderId(),
					paymentDetails);
			return false;
		}

		List<String> whitelistedPaymentMethodList = configurablePropertiesHolder
			.getProperty(ConfigPropertiesEnum.OMS_WHITELISTED_PAYMENT_METHOD_LIST.getKey(), ArrayList.class);

		if (whitelistedPaymentMethodList != null && !whitelistedPaymentMethodList.isEmpty()
				&& !whitelistedPaymentMethodList.contains(paymentMethod.name())) {
			log.warn("Unhandled payment method data received from oms, orderId: {}, paymentDetails: {}",
					omsDto.getOrderId(), paymentDetails);
			return false;
		}

		try {
			if ((OmsPaymentMethodEnum.BANK_MANDATE.equals(paymentMethod)
					|| OmsPaymentMethodEnum.EMI.equals(paymentMethod)
					|| OmsPaymentMethodEnum.LOYALTY_POINT.equals(paymentMethod))
					&& Double.parseDouble(omsDto.getGrandtotal()) <= 1.0) {
				log.warn(
						"Amount for payment method {} with grand total: {} for orderId: {} is lower than expected amount",
						paymentMethod, omsDto.getGrandtotal(), omsDto.getOrderId());
				return false;
			}
		}
		catch (Exception e) {
			log.error("Exception while parsing amount : {} for orderId : {}", omsDto.getGrandtotal(),
					omsDto.getOrderId());
			return false;
		}
		return true;
	}

	@Override
	public void processElement(OmsDto omsDto,
			BroadcastProcessFunction<OmsDto, Map<String, Object>, TransactionHistoryDetails>.ReadOnlyContext readOnlyContext,
			Collector<TransactionHistoryDetails> collector) throws Exception {
		flatMap(omsDto, collector);
	}

	@Override
	public void processBroadcastElement(Map<String, Object> value,
			BroadcastProcessFunction<OmsDto, Map<String, Object>, TransactionHistoryDetails>.Context context,
			Collector<TransactionHistoryDetails> collector) throws Exception {
		log.warn("Oms Data transformer Received broadcast at : {}", value);
		this.configurablePropertiesHolder.addProperties(value);
	}

}
