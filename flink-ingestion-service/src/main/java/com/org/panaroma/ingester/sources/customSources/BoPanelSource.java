package com.org.panaroma.ingester.sources.customSources;

import com.org.panaroma.commons.tags.dto.TagConfigurationCache;
import com.org.panaroma.commons.tags.dto.TagDto;
import com.org.panaroma.commons.tags.dto.TagRule;
import com.org.panaroma.commons.tags.service.TagConfigurationRefreshService;
import com.org.panaroma.commons.utility.configurablePropertyUtility.BoPanelConfigurablePropertiesImpl;
import com.org.panaroma.commons.utils.CommonsUtility;
import java.io.Serializable;
import java.time.Duration;
import java.time.temporal.TemporalUnit;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ThreadUtils;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static com.org.panaroma.ingester.constants.PipelineConstants.INTELLIGENT_TAGS_CONFIGURATION_CACHE;

@Component
@Log4j2
public class BoPanelSource implements Serializable, SourceFunction<Map<String, Object>> {

	private final BoPanelConfigurablePropertiesImpl boPanelConfigurableProperties;

	private TagConfigurationRefreshService tagConfigurationRefreshService;

	private final Map<String, Object> availableMap = new HashMap<>();

	private Long lastRuntime;

	private volatile boolean keepRunning;

	private final Long emissionGap;

	private final Long sleepTime;

	private Long tagConfigurationCacheLastUpdated;

	private Long tagConfigurationCacheEmissionGap;

	public BoPanelSource(final BoPanelConfigurablePropertiesImpl boPanelConfigurableProperties,
			@Value("${flinkBoPanelSource.emissionGap.seconds}") final Long emissionGapSecond,
			@Value("${flinkBoPanelSource.loopSleepTime.seconds}") final Long sleepTimeSecond,
			@Value("${flinkBoPanelSource.enabled}") final boolean enabled,
			@Value("${flinkTagConfigurationCache.emissionGap.seconds}") final Long tagConfigurationCacheEmissionGapSecond,
			final TagConfigurationRefreshService tagConfigurationRefreshService) {
		this.boPanelConfigurableProperties = boPanelConfigurableProperties;
		this.emissionGap = emissionGapSecond * 1000L;
		this.sleepTime = sleepTimeSecond * 1000L;
		this.keepRunning = enabled;
		this.tagConfigurationCacheEmissionGap = tagConfigurationCacheEmissionGapSecond * 1000L;
		this.tagConfigurationRefreshService = tagConfigurationRefreshService;
	}

	@Override
	public void run(final SourceContext<Map<String, Object>> ctx) throws Exception {
		while (keepRunning) {

			Map<String, Object> propertiesFromBo = checkAndUpdateBoPanelProperties();
			Map<String, Map<TagRule, TagDto>> tagConfigurationCache = checkAndUpdateTagConfigurationCache();
			if (MapUtils.isNotEmpty(tagConfigurationCache)) {
				propertiesFromBo.put(INTELLIGENT_TAGS_CONFIGURATION_CACHE, tagConfigurationCache);
			}
			if (MapUtils.isNotEmpty(propertiesFromBo)) {
				log.warn("Emitting : {}", propertiesFromBo);
				ctx.collect(propertiesFromBo);
			}
		}
	}

	private Map<String, Object> checkAndUpdateBoPanelProperties() throws Exception {
		Map<String, Object> propertiesFromBo;
		if (lastRuntime == null || System.currentTimeMillis() - lastRuntime >= emissionGap) {
			propertiesFromBo = getPropertiesFromBo();
			lastRuntime = System.currentTimeMillis();
			return propertiesFromBo;
		}
		else {
			try {
				Thread.sleep(sleepTime);
			}
			catch (InterruptedException e) {
				log.error("Thread interrupted : {}", CommonsUtility.exceptionFormatter(e));
			}
			catch (Exception e) {
				log.error("{}", CommonsUtility.exceptionFormatter(e));
			}
			return Collections.emptyMap();
		}
	}

	private Map<String, Map<TagRule, TagDto>> checkAndUpdateTagConfigurationCache() {
		Map<String, Map<TagRule, TagDto>> tagCache;
		if (tagConfigurationCacheLastUpdated == null
				|| System.currentTimeMillis() - tagConfigurationCacheLastUpdated >= tagConfigurationCacheEmissionGap) {
			tagCache = tagConfigurationRefreshService.refreshCache();
			tagConfigurationCacheLastUpdated = System.currentTimeMillis();
			return tagCache;
		}
		else {
			try {
				Thread.sleep(sleepTime);
			}
			catch (InterruptedException e) {
				log.error("Thread interrupted : {}", CommonsUtility.exceptionFormatter(e));
			}
			catch (Exception e) {
				log.error("{}", CommonsUtility.exceptionFormatter(e));
			}
			return Collections.emptyMap();
		}
	}

	public Map<String, Object> getPropertiesFromBo() throws Exception {
		Map<String, Object> properties = boPanelConfigurableProperties.readConfigurablePropertiesMapFromSource();
		if (properties == null) {
			return new HashMap<>();
		}
		if (availableMap.isEmpty() && !properties.isEmpty()) {
			availableMap.putAll(properties);
			return availableMap;
		}
		removeSameProperties(properties, availableMap);
		availableMap.putAll(properties);
		return properties;
	}

	private void removeSameProperties(final Map<String, Object> newProperties, final Map<String, Object> availableMap) {
		Set<String> repeatEntries = new HashSet<>();
		newProperties.forEach((key, value) -> {
			Object availableValue = availableMap.get(key);
			if (availableValue == null) {
				return;
			}
			if (availableValue.equals(value)) {
				repeatEntries.add(key);
			}
		});
		log.warn("These were same : {}", repeatEntries);
		repeatEntries.forEach(newProperties::remove);
	}

	@Override
	public void cancel() {
		keepRunning = false;
		log.warn("Closing FlinkBoPanelSource: {}", this);
	}

}
