package com.org.panaroma.ingester;

import static com.org.panaroma.commons.constants.CommonConstants.IS_BACKFILLING_FOR_PTH_DB;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;
import static com.org.panaroma.commons.constants.Constants.STATUS_UPDATED_VIA_UPDATE_TXN_STATUS_API_IDENTIFIER;
import static com.org.panaroma.ingester.client.PmsCustIdApiClient.FAILURE_STATUS;
import static com.org.panaroma.ingester.constants.Constants.COLON;
import static com.org.panaroma.ingester.constants.Constants.COMMA;
import static com.org.panaroma.ingester.constants.Constants.IS_INVALID_CUSTID;
import static com.org.panaroma.ingester.constants.Constants.ORDER_EVENT;
import static com.org.panaroma.ingester.constants.Constants.PG;
import static com.org.panaroma.ingester.constants.Constants.USER_EVENT;
import static com.org.panaroma.ingester.constants.Constants.USER_VPA;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.DAYS;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EVENT_TYPE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.FALSE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.FILTERED_EVENT;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.INCOMING_EVENTS_COUNT;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.PMS_CUST_ID_API_FAILURE_COUNT;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.SOURCE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.TRUE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.TXN_DATE_CURRENT_DATE_DIFF_DAYS;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.TXN_TYPE;

import com.org.panaroma.commons.clients.IClients;
import com.org.panaroma.commons.constants.CommonConstants;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.ingester.dto.PMSCustIdApi.PmsCustIdApiRequest;
import com.org.panaroma.ingester.dto.PMSCustIdApi.PmsCustIdApiResponse;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.utils.RetryUtility;
import com.org.panaroma.ingester.utils.Utility;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class ThdStreamFilter
		implements FlatMapFunction<TransactionHistoryDetails, TransactionHistoryDetails>, Serializable {

	private final Long currentDateAndTxnDateDiffLimit;

	private final MetricsAgent metricsAgent;

	private final Long pgDataCutOffDateInEpoch;

	private final RetryUtility retryUtility;

	private final IClients<PmsCustIdApiRequest, PmsCustIdApiResponse> pmsCustIdApiClient;

	@Autowired
	public ThdStreamFilter(final MetricsAgent metricsAgent,
			@Value("${txn.event.max.days.diff.from.current.to.process.txn}") final Long currentDateAndTxnDateDiffLimit,
			@Value("${pgData.cut.off.date.in.epoch}") final Long pgDataCutOffDateInEpoch,
			final RetryUtility retryUtility,
			final IClients<PmsCustIdApiRequest, PmsCustIdApiResponse> pmsCustIdApiClient) {
		this.currentDateAndTxnDateDiffLimit = currentDateAndTxnDateDiffLimit;
		this.metricsAgent = metricsAgent;
		this.pgDataCutOffDateInEpoch = pgDataCutOffDateInEpoch;
		this.retryUtility = retryUtility;
		this.pmsCustIdApiClient = pmsCustIdApiClient;
	}

	@Override
	public void flatMap(final TransactionHistoryDetails thd, final Collector<TransactionHistoryDetails> collector)
			throws Exception {
		try {
			if (thd == null) {
				return;
			}

			// Filter IN_PORT and OUT_PORT events
			if (isMandatePortEvent(thd)) {
				return;
			}

			// Invalid cust id handling
			if (Utility.isInvalidCustIdEvent(thd)) {
				log.warn("Invalid CustId event received for txnId: {}", thd.getSystemId());
				// Invalid CustId Handling - PTH-558
				invalidCustIdHandling(thd);
			}

			if (Utility.isInvalidCustIdEvent(thd)) {
				log.warn("Invalid CustId event could not processed for txnId: {}, dropping this event",
						thd.getSystemId());
				return;
			}

			// we can skip the code in this flatmap for events which are coming for just
			// updating the status
			if (Objects.nonNull(thd) && Objects.nonNull(thd.getContextMap())
					&& thd.getContextMap().containsKey(STATUS_UPDATED_VIA_UPDATE_TXN_STATUS_API_IDENTIFIER)) {
				collector.collect(thd);
				return;
			}
			if (Objects.nonNull(thd.getContextMap()) && thd.getContextMap().containsKey(IS_BACKFILLING_FOR_PTH_DB)) {
				if (TransactionTypeEnum.IPO_MANDATE.equals(thd.getTxnType())) {
					if (Long.parseLong(thd.getTxnDate()) >= 1711909800000L) {
						metricsAgent.incrementCount("IPO_MANDATE_BACKFILL_EVENTS_COUNT");
						collector.collect(thd);
					}
					else {
						log.warn(
								"IPO Mandate event received for backfilling has older txnDate than 1st Apr 2024. THD : {}",
								thd);
					}
				}
				else {
					log.warn("Event received for backfilling is not for IPO Mandate txn. THD : {}", thd);
				}
				return;
			}
			Long currentTime = new Date().getTime();
			Long txnDate = Long.valueOf(thd.getTxnDate());
			Long timeDiff = currentTime - txnDate;

			long daysDiff = TimeUnit.DAYS.convert(timeDiff, TimeUnit.MILLISECONDS);
			if (daysDiff > 0) {
				String tags = DAYS + COLON + daysDiff + COMMA + SOURCE + thd.getStreamSource();
				metricsAgent.incrementCount(TXN_DATE_CURRENT_DATE_DIFF_DAYS, tags);
			}

			String tags = SOURCE + thd.getStreamSource() + COMMA + TXN_TYPE + thd.getTxnType() + COMMA + EVENT_TYPE
					+ Utility.getEventType(thd);

			if (Utility.isEventOlderThanLimit(Long.parseLong(thd.getTxnDate()), currentDateAndTxnDateDiffLimit)
					&& com.org.panaroma.commons.utils.Utility.isSkipOldestEvent(thd)) {
				log.warn("Event received in StreamFilter THD : {}, systemId : {}, TimeDiff : {} days", thd,
						thd.getSystemId(),
						(new Date().getTime() - Long.parseLong(thd.getTxnDate())) / (1000 * 60 * 60 * 24));

				tags = tags + COMMA + FILTERED_EVENT + TRUE;
				metricsAgent.incrementCount(INCOMING_EVENTS_COUNT, tags);
				return;
			}

			tags = tags + COMMA + FILTERED_EVENT + FALSE;
			metricsAgent.incrementCount(INCOMING_EVENTS_COUNT, tags);

			// Filtering the events who get retried by pg data publisher and eligible to
			// send to ppbl uth
			if (TransactionSource.PG.equals(thd.getStreamSource())
					&& (Utility.isPgDataPublishRetryEvent(thd) || isThisTxnNeededToBeSentToUth(thd))) {
				metricsAgent.incrementCount("PG_DROP_EVENT_COUNT");
				log.warn("Dropping event bcz it's before cutoff date or is retried by pg data publisher txnId: {}",
						thd.getSystemId());
				return;
			}

			/**
			 * This condition is used to discard event received from UPI in case of
			 * RECURRING_MANDATE txns where retryAttempt>0 and status is Failure and
			 * sourceSystem is not PG
			 */
			if (Utility.isFailureUpiEventToBeDiscardedForRecurringMandateTxns(thd)) {
				log.info("Discarded UPI event for retryAttempt>0 for umn: {} and systemId: {}", thd.getUmn(),
						thd.getSystemId());
				return;
			}

			collector.collect(thd);
		}
		catch (Exception ex) {
			log.warn("Exception in filtering data for systemId in thdFilter : {}, with Exception : {}",
					thd.getSystemId(), ex);
			collector.collect(thd);
		}
	}

	private boolean isMandatePortEvent(final TransactionHistoryDetails thd) {
		String txnPurpose = com.org.panaroma.commons.utils.Utility.getPurpose(thd);
		if (PURPOSE_IN_PORT.equalsIgnoreCase(txnPurpose) || PURPOSE_OUT_PORT.equalsIgnoreCase(txnPurpose)) {
			log.debug("Discarded {} event in UPI pipeline - not storing in UPI ES. systemId: {}, umn: {}", txnPurpose,
					thd.getSystemId(), thd.getUmn());
			return true;
		}
		return false;
	}

	private void invalidCustIdHandling(final TransactionHistoryDetails thd) throws Exception {
		// InValid CustId Handling for PG
		setCustIdAsNullIfItIsInvalid(thd);

		// Getting userVpa from thd
		String userVpa = getUserVpa(thd);

		try {
			PmsCustIdApiRequest pmsCustIdApiRequest = pmsCustIdApiClient.createRequestBody(userVpa);
			log.warn("Request body created for PMS CustId API: {}, txnId: {}", pmsCustIdApiRequest, thd.getSystemId());

			PmsCustIdApiResponse pmsCustIdApiResponse = pmsCustIdApiClient.getResponseFrom(pmsCustIdApiRequest);
			log.warn("Response received from PMS CustId API: {}, txnId: {}", pmsCustIdApiResponse, thd.getSystemId());

			// Handling of success case
			if (pmsCustIdApiResponse != null && pmsCustIdApiResponse.getRespDetails() != null
					&& StringUtils.isNotBlank(pmsCustIdApiResponse.getRespDetails().getCustId())) {
				setCustIdInParticipants(thd, pmsCustIdApiResponse.getRespDetails().getCustId());

				// removing invalid custId Key
				removeInvalidCustIdKey(thd);
			}

			// Handling of retry able failure cases
			if (pmsCustIdApiResponse != null && FAILURE_STATUS.equalsIgnoreCase(pmsCustIdApiResponse.getStatus())) {
				metricsAgent.incrementCount(PMS_CUST_ID_API_FAILURE_COUNT);

				log.warn("Retrying event because of retryable failure from PMS CustId API, txnId: {}",
						thd.getSystemId());
				this.retryUtility.pushDataToKafka(PG, thd);
			}

		}
		catch (Exception ex) {
			log.error("Error while handling invalid custId txnId: {}, Ex: {}", thd.getSystemId(), ex);
			this.retryUtility.pushDataToKafka(PG, thd);
		}
	}

	private void removeInvalidCustIdKey(final TransactionHistoryDetails thd) {
		if (thd == null || thd.getContextMap() == null) {
			return;
		}
		thd.getContextMap().remove(IS_INVALID_CUSTID);
	}

	private String getUserVpa(final TransactionHistoryDetails thd) {
		if (thd == null) {
			return null;
		}

		// This is for order level event since userVpa is not present in participants upi
		// data
		if (thd.getContextMap() != null && StringUtils.isNotBlank(thd.getContextMap().get(USER_VPA))) {
			return thd.getContextMap().get(USER_VPA);
		}

		// This is for user level event
		if (thd.getParticipants() != null) {
			for (TxnParticipants participant : thd.getParticipants()) {
				if (participant.getUpiData() != null && StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
					return participant.getUpiData().getVpa();
				}
			}
		}

		return null;
	}

	private void setCustIdInParticipants(final TransactionHistoryDetails thd, final String custId) {
		if (thd == null || CollectionUtils.isEmpty(thd.getParticipants())) {
			return;
		}

		String eventType = Utility.getEventType(thd);

		if (ORDER_EVENT.equalsIgnoreCase(eventType) || USER_EVENT.equalsIgnoreCase(eventType)) {
			for (TxnParticipants participant : thd.getParticipants()) {
				// If EntityType is not MERCHANT then only set custId
				if (!EntityTypesEnum.MERCHANT.equals(participant.getEntityType())) {
					participant.setCustomerId(custId);
				}
			}
		}
	}

	private void setCustIdAsNullIfItIsInvalid(final TransactionHistoryDetails thd) {
		if (thd == null || thd.getContextMap() == null) {
			return;
		}

		if (CommonConstants.TRUE.equalsIgnoreCase(thd.getContextMap().get(IS_INVALID_CUSTID))) {
			if (thd.getParticipants() != null) {
				for (TxnParticipants participant : thd.getParticipants()) {
					participant.setCustomerId(null);
				}
			}
		}
	}

	private boolean isThisTxnNeededToBeSentToUth(final TransactionHistoryDetails thd) {
		if (Objects.isNull(thd.getTxnDate())) {
			return false;
		}

		Long txnDate = Utility.getOrderTxnDateFromPgTxnId(thd.getSystemId(), Long.parseLong(thd.getTxnDate()));
		if (Objects.isNull(txnDate)) {
			txnDate = Long.parseLong(thd.getTxnDate());
		}

		// txnDate epoch is smaller than pg data cut off epoch
		return txnDate <= pgDataCutOffDateInEpoch;
	}

}