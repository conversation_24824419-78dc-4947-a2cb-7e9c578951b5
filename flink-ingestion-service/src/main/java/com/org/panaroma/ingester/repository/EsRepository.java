package com.org.panaroma.ingester.repository;

import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.Constants.IS_OTHER_PARTY_ENTITY_ID_VIRTUAL;
import static com.org.panaroma.commons.constants.Constants.OTHER_PARTY_ENTITY_ID;
import static com.org.panaroma.commons.constants.Constants.UNDERSCORE_DOC;
import static com.org.panaroma.commons.repository.es.EsUtility.getQueryBuilder;
import static com.org.panaroma.ingester.constants.Constants.CHAT_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.ENTITY_ID;
import static com.org.panaroma.ingester.constants.Constants.IS_FOR_GROUPING;
import static com.org.panaroma.ingester.constants.Constants.IS_FROM_DC_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.OTHER_PARTY_ENTITY_ID_RELATED_FIELDS_UPDATE_QUERY_FAILURE;
import static com.org.panaroma.ingester.constants.Constants.TRUE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.ES_SINK_EXCEPTION_COUNT;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.ES_SINK_TIME;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.ES_TIME_TAKEN;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EXCEPTION_IN_GETTING_MANDATE_DATA_FROM_ES;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EXTRACTED_STORED_DOC_FOR_PG_TWICE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EXTRACTED_TXN_DATE_FROM_PG_NULL;
import static org.elasticsearch.common.xcontent.XContentFactory.jsonBuilder;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.commons.dto.mandate.MandateBaseDto;
import com.org.panaroma.commons.dto.mandate.MandateInfoData;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import com.org.panaroma.commons.repository.es.EsUtility;
import com.org.panaroma.commons.utility.MandateUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.IndexUtility;
import com.org.panaroma.commons.utils.RepoUtility;
import com.org.panaroma.ingester.configuration.elasticsearch.EsReadSourceConfiguration;
import com.org.panaroma.ingester.configuration.elasticsearch.objects.EsReadConfigObject;
import com.org.panaroma.ingester.dto.insertUpdateExecutorDtos.BulkUpdateDto;
import com.org.panaroma.ingester.dto.insertUpdateExecutorDtos.CustomUpdateParams;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.utils.Utility;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.Requests;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
@Primary
@Log4j2
public class EsRepository implements IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> {

	private RestHighLevelClient restHighLevelClient;

	private ObjectMapper objectMapper;

	private String aliasName;

	private String indexNamePrefix;

	private List<String> updatedIndexList;

	private Integer port;

	private List<String> hostList;

	private final int socketTimeout;

	private final int connectTimeOut;

	private final int maxRetryTimeOut;

	private MetricsAgent metricsAgent;

	private String udirDumpIndexPrefix;

	private String pipelineName;

	private Map<String, EsReadConfigObject> esReadConfigObjectMap;

	private String mandateActivityIndexAlias;

	private String mandateInfoIndexAlias;

	@Autowired
	public EsRepository(final ObjectMapper objectMapper, @Value("${es-port}") final Integer port,
			@Value("#{'${es-host-list}'.split(',')}") final List<String> hostList,
			@Value("${payment-history-alias}") final String aliasName,
			@Value("${index.name.prefix}") final String indexNamePrefix,
			@Value("#{'${updated.index.month.list}'.split(',')}") final List<String> updatedIndexList,
			@Value("${es-socket-timeout:#{2000}}") final int socketTimeout,
			@Value("${es-max-retry-timeout:#{2000}}") final int maxRetryTimeOut,
			@Value("${es-connect-timeout:#{2000}}") final int connectTimeOut,
			@Value("${upi-udir-dump-index.name.prefix}") final String udirDumpIndexPrefix,
			final MetricsAgent metricsAgent, final EsReadSourceConfiguration esReadSourceConfiguration,
			@Value("${mandate-activity-index-alias}") final String mandateActivityIndexAlias,
			@Value("${mandate-info-index-alias}") final String mandateInfoIndexAlias) throws MalformedURLException {
		this.objectMapper = objectMapper;
		this.port = port;
		this.hostList = hostList;
		this.socketTimeout = socketTimeout;
		this.connectTimeOut = connectTimeOut;
		this.maxRetryTimeOut = maxRetryTimeOut;
		// initializeRestClient();
		this.aliasName = aliasName;
		this.indexNamePrefix = indexNamePrefix;
		this.updatedIndexList = updatedIndexList;
		this.metricsAgent = metricsAgent;
		this.udirDumpIndexPrefix = udirDumpIndexPrefix;
		this.esReadConfigObjectMap = (Objects.nonNull(esReadSourceConfiguration))
				? esReadSourceConfiguration.getSourceMap() : null;
		this.mandateActivityIndexAlias = mandateActivityIndexAlias;
		this.mandateInfoIndexAlias = mandateInfoIndexAlias;
	}

	@Override
	public List<TransformedTransactionHistoryDetail> fetchData(final SearchContext searchContext,
			final PaginationParams paginationParams, final boolean sortInAscending) throws Exception {

		try {
			esureInitialized();
			// create Es request
			SearchSourceBuilder searchSourceBuilder = EsUtility.createSearchSourceBuilder(searchContext,
					paginationParams, sortInAscending);
			SearchRequest searchRequest = new SearchRequest().indices(IndexUtility
				.getIndexListInRange(indexNamePrefix, searchContext.getFromDate(), searchContext.getToDate())
				.toArray(new String[0]));
			searchRequest.source(searchSourceBuilder);
			if (!StringUtils.isEmpty(searchContext.getEntityId())) {
				searchRequest.routing(searchContext.getEntityId());
			}
			log.warn("Es request for search context : {}", searchRequest);
			// get Response from Es
			SearchResponse searchResponse = null;
			searchResponse = this.search(searchRequest);

			// validate Es response
			SearchHits hits = searchResponse.getHits();
			List<SearchHit> hitsList = Arrays.asList(hits.getHits());

			if (hitsList.isEmpty()) {
				log.info("No result found from Es for request:{}", searchContext);
				return null;
			}

			// convert into TTHD
			List<TransformedTransactionHistoryDetail> transactionHistoryDetailsEs = new ArrayList<>();
			for (SearchHit hit : hits) {
				String sourceAsString = hit.getSourceAsString();
				if (sourceAsString != null) {
					TransformedTransactionHistoryDetail detail = objectMapper.readValue(sourceAsString,
							TransformedTransactionHistoryDetail.class);
					transactionHistoryDetailsEs.add(detail);
				}
			}
			// return
			return transactionHistoryDetailsEs;
		}
		catch (Exception e) {
			log.error("Exception while fetching es response for search context :{} Exception :{}", searchContext,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	@Override
	public List<TransformedTransactionHistoryDetail> fetchData(final Map<String, Object> paramMap,
			final TransformedTransactionHistoryDetail tthd) throws Exception {
		try {
			log.info("Fetching details from ES with paramMap:{}", paramMap);
			SearchRequest searchRequest = new SearchRequest().indices(this.getIndexNames(tthd));
			return getTransformedTransactionHistoryDetails(paramMap, searchRequest, tthd.getTxnId());
		}
		catch (Exception e) {
			log.warn("Exception while getting es record. Exception :{}", CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	@Override
	public List<TransformedTransactionHistoryDetail> fetchData(final Map<String, Object> paramMap, final Long txnDate,
			final String tracingId) throws Exception {
		try {

			if (txnDate == null) {
				log.error("txnDate is null for paramMap : {}", paramMap);
				throw new Exception("TxnDate is mandatory for fetching data");
			}

			log.info("Fetching details from ES with paramMap:{}", paramMap);
			SearchRequest searchRequest = new SearchRequest().indices(this.getIndexNames(txnDate));
			return getTransformedTransactionHistoryDetails(paramMap, searchRequest, tracingId);
		}
		catch (Exception e) {
			log.error("Exception while getting es record. Exception :{}", CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	@Override
	public Long fetchCount(final Map<String, Object> paramMap, final SearchContext searchContext) {
		log.info("Fetching count from ES with paramMap : {}", paramMap);
		CountRequest countRequest = new CountRequest().indices(this.aliasName);
		return getCount(paramMap, searchContext, countRequest);
	}

	@Override
	public SearchResponse search(final SearchRequest searchRequest) throws IOException {
		SearchResponse searchResponse = null;
		long startTime = System.currentTimeMillis();
		searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
		long endTime = System.currentTimeMillis();
		long latency = endTime - startTime;
		log.debug("Time taken in fetching data from Es :{}", latency);
		this.metricsAgent.pushTimeDiff(ES_TIME_TAKEN, startTime, endTime);
		this.metricsAgent.recordReadCountAndLatencyForFlinkEs(startTime);
		return searchResponse;
	}

	private List<TransformedTransactionHistoryDetail> getTransformedTransactionHistoryDetails(
			final Map<String, Object> paramMap, final SearchRequest searchRequest, final String systemId)
			throws IOException {
		esureInitialized();
		List<TransformedTransactionHistoryDetail> transactionHistoryDetailsEs = new ArrayList<>();
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
		for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
			if (entry.getValue() == null) {
				log.info("Null value found for entry:{} for ParamMap:{}", entry, paramMap);
				return null;
			}
			if (entry.getKey().contains(".") && entry.getKey().split("\\.")[0].equalsIgnoreCase("participants")) {
				queryBuilder.must(QueryBuilders.nestedQuery("participants",
						QueryBuilders.termQuery(entry.getKey(), paramMap.get(entry.getKey())), ScoreMode.None));
			}
			else {
				queryBuilder.filter(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
			}
		}

		searchSourceBuilder.query(queryBuilder);
		searchSourceBuilder.version(true);
		searchRequest.source(searchSourceBuilder);
		// searchRequest.preference("_primary");
		if (paramMap.get(ENTITY_ID) != null) {
			searchRequest.routing(String.valueOf(paramMap.get(ENTITY_ID)));
		}
		SearchResponse searchResponse = null;
		try {
			searchResponse = this.search(searchRequest);
		}
		catch (ElasticsearchStatusException e) {
			log.warn("Index Name not found for the txnId : {} so will search on alias.", systemId);
			searchRequest.indices(this.aliasName);
			searchResponse = this.search(searchRequest);
		}

		SearchHit[] hits = searchResponse.getHits().getHits();
		if (hits == null || hits.length == 0) {
			log.info("No result found from Es for request:{}", paramMap);
			return null;
		}

		for (SearchHit hit : hits) {
			String sourceAsString = hit.getSourceAsString();
			log.debug("pipeline: {}, ES Source String:{}", Utility.getActivePipelineName(), sourceAsString);
			if (sourceAsString != null) {
				TransformedTransactionHistoryDetail detail = objectMapper.readValue(sourceAsString,
						TransformedTransactionHistoryDetail.class);
				// detail.setVersion(hit.getVersion());
				log.debug("pipeline:{}, request Params:{}, version:{}, foundData from Es:{}",
						Utility.getActivePipelineName(), paramMap, hit.getVersion(), detail);
				transactionHistoryDetailsEs.add(detail);
			}
		}

		return transactionHistoryDetailsEs;
	}

	@Override
	public TransformedTransactionHistoryDetail fetchDataFromAlias(final Map<String, Object> paramMap,
			final TransformedTransactionHistoryDetail tthd) throws Exception {
		try {
			// List<TransformedTransactionHistoryDetail> transactionHistoryDetailsES = new
			// ArrayList<>();
			esureInitialized();
			log.info("Fetching details from ES with paramMap:{}", paramMap);
			SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
			BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
			for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
				if (entry.getValue() == null) {
					log.info("Null value found for entry:{} for ParamMap:{}", entry, paramMap);
					return null;
				}
				queryBuilder.filter(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
			}

			searchSourceBuilder.query(queryBuilder);
			searchSourceBuilder.version(true);

			SearchRequest searchRequest = new SearchRequest().indices(this.getIndexNames(tthd));
			searchRequest.source(searchSourceBuilder);
			if (!StringUtils.isEmpty(tthd.getEntityId())) {
				searchRequest.routing(tthd.getEntityId());
			}
			SearchResponse searchResponse = null;
			try {
				searchResponse = this.search(searchRequest);
			}
			catch (ElasticsearchStatusException e) {
				log.warn("Index Name not found for the txnId : {} so will search on alias.", tthd.getTxnId());
				searchRequest.indices(this.aliasName);
				searchResponse = this.search(searchRequest);
			}

			SearchHit[] hits = searchResponse.getHits().getHits();
			if (hits == null || hits.length == 0) {
				log.warn("No result found in month index. going to search in alias TxnId: {}", tthd.getTxnId());
				searchRequest.indices(this.aliasName);
				searchResponse = this.search(searchRequest);
				hits = searchResponse.getHits().getHits();

				if (hits == null || hits.length == 0) {
					log.info("No result found from Es for request:{}", paramMap);
					return null;
				}
			}

			if (hits.length > 1) {
				log.info("More than 1 result found from Es for request:{}", paramMap);
				return null;
			}

			TransformedTransactionHistoryDetail detail = objectMapper.readValue(hits[0].getSourceAsString(),
					TransformedTransactionHistoryDetail.class);

			return detail;
		}
		catch (Exception e) {
			log.warn("Exception while getting es record. Exception :{}", CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	public MandateBaseDto fetchMandateDataFromAlias(final Map<String, Object> paramMap, final String entityId,
			final boolean getMandateInfoData) throws Exception {
		if (getMandateInfoData) {
			return fetchMandateDataFromAlias(paramMap, entityId, mandateInfoIndexAlias,
					new TypeReference<MandateInfoData>() {
					});
		}
		else {
			return fetchMandateDataFromAlias(paramMap, entityId, mandateActivityIndexAlias,
					new TypeReference<MandateActivityData>() {
					});
		}
	}

	public MandateBaseDto fetchMandateDataFromAlias(final Map<String, Object> paramMap, final String entityId,
			final String index, final TypeReference type) throws Exception {
		try {
			esureInitialized();
			log.info("Fetching mandate data from ES with paramMap : {}", paramMap);
			SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
			BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
			for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
				if (entry.getValue() == null) {
					log.info("Null value found for entry : {} for ParamMap:{}", entry, paramMap);
					return null;
				}
				/*
				 * Below check is added for a case where we want to get first execution
				 * failure doc when retryAttempt = 1 event arrives. We can't proceed
				 * without this must_not query because the query can fetch 2 docs at the
				 * same time : 1 -> actual doc which we want 2 -> non-terminal doc with
				 * same txnId which we don't want
				 */
				if ("mustNotTxnId".equalsIgnoreCase(entry.getKey())) {
					queryBuilder.mustNot(QueryBuilders.termQuery("txnId", entry.getValue()));
					continue;
				}
				queryBuilder.filter(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
			}

			searchSourceBuilder.query(queryBuilder);
			searchSourceBuilder.version(true);

			// TODO We can think of optimising the searchRequest from alias to some set of
			// indices
			SearchRequest searchRequest = new SearchRequest().indices(index);
			searchRequest.source(searchSourceBuilder);
			if (StringUtils.isNotBlank(entityId)) {
				searchRequest.routing(entityId);
			}
			SearchResponse searchResponse;
			searchResponse = this.search(searchRequest);

			SearchHit[] hits = searchResponse.getHits().getHits();
			if (hits == null || hits.length == 0) {
				log.info("No mandate doc found from Es for request : {}", paramMap);
				return null;
			}

			if (hits.length > 1) {
				log.info("More than 1 mandate doc found from Es for request : {}", paramMap);
				return null;
			}

			return (MandateBaseDto) objectMapper.readValue(hits[0].getSourceAsString(), type);
		}
		catch (Exception exception) {
			log.warn("Exception while getting mandate data. Exception : {}",
					CommonsUtility.exceptionFormatter(exception));
			throw exception;
		}
	}

	@Override
	public List<TransformedTransactionHistoryDetail> fetchDataOnlyFromAlias(final Map<String, Object> paramMap,
			final TransformedTransactionHistoryDetail tthd) throws Exception {
		try {
			List<TransformedTransactionHistoryDetail> tthdList = new ArrayList<>();
			SearchHit[] hits = getSearchHits(paramMap, tthd);
			if (hits == null) {
				return null;
			}
			for (SearchHit hit : hits) {
				TransformedTransactionHistoryDetail detail = objectMapper.readValue(hit.getSourceAsString(),
						TransformedTransactionHistoryDetail.class);
				tthdList.add(detail);
			}
			return tthdList;
		}
		catch (Exception e) {
			log.warn("Exception while getting es record. Exception :{}", CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	private SearchHit[] getSearchHits(final Map<String, Object> paramMap,
			final TransformedTransactionHistoryDetail tthd) throws IOException {
		esureInitialized();
		log.info("Fetching details from ES with paramMap:{}", paramMap);
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder queryBuilder = getQueryBuilder(paramMap, tthd);
		if (queryBuilder == null) {
			return null;
		}
		searchSourceBuilder.query(queryBuilder);
		searchSourceBuilder.version(true);
		SearchRequest searchRequest = new SearchRequest().indices(this.aliasName);
		searchRequest.source(searchSourceBuilder);
		if (!StringUtils.isEmpty(tthd.getEntityId())) {
			searchRequest.routing(tthd.getEntityId());
		}
		SearchResponse searchResponse = getSearchResponse(tthd, searchRequest);
		return searchResponse.getHits().getHits();
	}

	private SearchResponse getSearchResponse(final TransformedTransactionHistoryDetail tthd,
			final SearchRequest searchRequest) throws IOException {
		SearchResponse searchResponse;
		try {
			searchResponse = this.search(searchRequest);
		}
		catch (ElasticsearchStatusException e) {
			log.warn("Index Name not found for the txnId : {} so will search on alias.", tthd.getTxnId());
			searchRequest.indices(this.aliasName);
			searchResponse = this.search(searchRequest);
		}
		return searchResponse;
	}

	/***
	 * This method returns the set of index names which can be involved for finding
	 * related doc.
	 * @param detail (dataType: tthd)
	 * @return
	 */
	private String[] getIndexNames(final TransformedTransactionHistoryDetail detail) {
		try {
			if (detail.getContextMap() != null && detail.getContextMap().get(IS_FOR_GROUPING) != null
					&& "true".equalsIgnoreCase(detail.getContextMap().get(IS_FOR_GROUPING))) {
				return new String[] { this.aliasName };
			}
			Long txnDate = detail.getTxnDate();
			if (txnDate != null) {
				return getIndexNames(txnDate);
			}
		}
		catch (Exception e) {
			log.error("Exception while creating index name. txnId : {} txnDate : {}, Exception : {}", detail.getTxnId(),
					detail.getTxnDate(), CommonsUtility.exceptionFormatter(e));
		}
		return new String[] { this.aliasName };
	}

	/***
	 * This method returns the set of index names which can be involved for finding
	 * related doc.
	 * @param txnDate (dataType: Long)
	 * @return
	 */
	private String[] getIndexNames(final Long txnDate) {
		// changed location of function : getIndexNames from Ingester module to Commons
		// module
		return IndexUtility.getIndexNames(txnDate, this.indexNamePrefix, this.updatedIndexList);
	}

	@Override
	public void updateTxnsForGrouping(final Set<TransformedTransactionHistoryDetail> detailSet, final String groupId) {
		if (CollectionUtils.isEmpty(detailSet)) {
			log.debug("pipeline:{}, GroupID:{}, Group txn not found.", Utility.getActivePipelineName(), groupId);
			return;
		}
		this.esureInitialized();
		log.debug("pipeline:{}, Updating bulk response for groupID:{}", Utility.getActivePipelineName(), groupId);
		BulkRequest bulkRequest = new BulkRequest();
		detailSet.forEach(detail -> bulkRequest.add(this.createUpdateRequestForGrouping(detail, groupId)));
		try {
			long startTime = System.currentTimeMillis();
			BulkResponse responses = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
			this.metricsAgent.recordWriteCountAndLatencyForFlinkEs(startTime);
			log.debug("pipeline:{}, bulk group update response:{}", Utility.getActivePipelineName(), responses);
			responses.forEach(response -> log.info("pipeline:{}, bulk group update response Item:{}",
					Utility.getActivePipelineName(), response.getResponse()));
		}
		catch (IOException e) {
			log.error("pipeline:{}, Bulk Update request execution failed. Exception:{}",
					Utility.getActivePipelineName(), CommonsUtility.exceptionFormatter(e));
		}
	}

	@Override
	public void updateTxnsForGrouping(final BulkUpdateDto bulkUpdateDto) {
		if (Objects.isNull(bulkUpdateDto) || CollectionUtils.isEmpty(bulkUpdateDto.getCustomUpdateParams())) {
			log.debug("GroupID:{}, Group txn not found.", bulkUpdateDto.getGroupId());
			return;
		}
		this.esureInitialized();
		log.debug("Updating bulk response for groupID:{}", bulkUpdateDto.getGroupId());
		BulkRequest bulkRequest = new BulkRequest();
		bulkUpdateDto.getCustomUpdateParams()
			.forEach(customUpdateParams -> bulkRequest
				.add(this.createUpdateRequestForGrouping(customUpdateParams, bulkUpdateDto.getGroupId())));
		try {
			long startTime = System.currentTimeMillis();
			BulkResponse responses = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
			this.metricsAgent.recordWriteCountAndLatencyForFlinkEs(startTime);
			metricsAgent.pushTimeDiff(ES_SINK_TIME, startTime, System.currentTimeMillis(),
					metricsAgent.getMonitoringTagForSinkRequest("bulk-update", pipelineName));
			log.debug("pipeline:{}, bulk group update response:{}", Utility.getActivePipelineName(), responses);
			responses.forEach(response -> log.info("pipeline:{}, bulk group update response Item:{}",
					Utility.getActivePipelineName(), response.getResponse()));
		}
		catch (IOException e) {
			metricsAgent.incrementCount(ES_SINK_EXCEPTION_COUNT,
					metricsAgent.getMonitoringTagForSinkRequest("bulk-update", pipelineName));
			log.error("pipeline:{}, Bulk Update request execution failed. Exception:{}", pipelineName,
					CommonsUtility.exceptionFormatter(e));
		}
	}

	@Override
	public void updateOtherPartyEntityIdRelatedFields(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd)) {
			log.warn("tthd object received to update otherPartyEntityId related fields is null for txnId : {}",
					tthd.getTxnId());
		}
		String otherPartyEntityId = tthd.getOtherPartyEntityId();
		Boolean isOtherPartyEntityIdVirtual = tthd.getIsOtherPartyEntityIdVirtual();
		if (StringUtils.isBlank(otherPartyEntityId) || Objects.isNull(isOtherPartyEntityIdVirtual)) {
			log.debug(
					"otherPartyEntityId or isOtherPartyEntityIdVirtual field received to be updated is null for txnId : {}",
					tthd.getTxnId());
			return;
		}
		this.esureInitialized();
		log.debug("Updating otherPartyEntityId & isOtherPartyEntityIdVirtual fields for txnId : {}", tthd.getTxnId());
		UpdateRequest updateRequest = createUpdateRequestForOtherPartyEntityIdRelatedFields(tthd, otherPartyEntityId,
				isOtherPartyEntityIdVirtual);
		if (Objects.isNull(updateRequest)) {
			log.warn(
					"Not hitting ES for updating otherPartyEntityId & isOtherPartyEntityIdVirtual fields for txnId : {}"
							+ " as update request formed is null.",
					tthd.getTxnId());
		}
		else {
			try {
				long startTime = System.currentTimeMillis();
				UpdateResponse response = restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
				log.debug("update response : {}", response);
				this.metricsAgent.recordWriteCountAndLatencyForFlinkEs(startTime);
			}
			catch (Exception e) {
				log.error(
						"Update request execution for otherPartyEntityId & isOtherPartyEntityIdVirtual fields failed for"
								+ " txnId : {}. Exception : {}",
						tthd.getTxnId(), CommonsUtility.exceptionFormatter(e));
				metricsAgent.incrementCount(OTHER_PARTY_ENTITY_ID_RELATED_FIELDS_UPDATE_QUERY_FAILURE);
				Utility.setRetryCountAndReasonInTthdContextMap(tthd, CHAT_PIPELINE,
						OTHER_PARTY_ENTITY_ID_RELATED_FIELDS_UPDATE_QUERY_FAILURE);
			}
		}
	}

	@Override
	public UpdateRequest createUpdateRequestForTags(final TransformedTransactionHistoryDetail tthd) {
		log.debug("SystemId:{}, creating update request for tags", tthd.getTxnId());
		try {
			if (Objects.nonNull(tthd.getTags())) {
				UpdateRequest updateRequest = new UpdateRequest();
				updateRequest.index(this.getIndexName(tthd));
				updateRequest.type("_doc");
				updateRequest.id(tthd.docId());
				updateRequest.routing(tthd.getEntityId());
				log.debug("Doc ID to be updated:{}", tthd.docId());
				String tagsString = objectMapper.writeValueAsString(tthd.getTags());
				String searchTags = objectMapper.writeValueAsString(
						tthd.getTags().stream().map(TransformedTag::getTag).collect(Collectors.toSet()));
				updateRequest.doc(jsonBuilder().startObject()
					.field("tags", objectMapper.readValue(tagsString, new TypeReference<List<Map<String, Object>>>() {
					}))
					.startObject("searchFields")
					.field("searchTags", objectMapper.readValue(searchTags, new TypeReference<List<String>>() {
					}))
					.endObject()
					.field("docUpdatedDate", tthd.getDocUpdatedDate())
					.endObject());
				return updateRequest;
			}
		}
		catch (IOException e) {
			log.error("pipeline:{}, SystemID:{}, Update request creation failed for tags. Exception:{}",
					Utility.getActivePipelineName(), tthd.getTxnId(), CommonsUtility.exceptionFormatter(e));
			return null;
		}
		return null;
	}

	private void esureInitialized() {
		if (restHighLevelClient == null) {
			try {
				log.error("Rest Client is null");
				initializeRestClient();
			}
			catch (Exception e) {
				log.error("Exception while creating rest client. Exception :{}", CommonsUtility.exceptionFormatter(e));
			}
		}
	}

	private void initializeRestClient() throws MalformedURLException {
		if (Utility.isEsReadConfigAvailable(esReadConfigObjectMap, pipelineName)) {
			EsReadConfigObject configObject = esReadConfigObjectMap.get(pipelineName);
			this.restHighLevelClient = new RestHighLevelClient(RepoUtility.buildRestClient(configObject.getHostList(),
					configObject.getPort(), configObject.getMaxRetryTimeout(), configObject.getConnectionTimeout(),
					configObject.getSocketTimeout()));
			if (StringUtils.isNotBlank(configObject.getIndexNamePrefix())) {
				this.indexNamePrefix = configObject.getIndexNamePrefix();
			}
			log.warn("Setting client for pipeline : {}", pipelineName);
		}
		else {
			this.restHighLevelClient = new RestHighLevelClient(RepoUtility.buildRestClient(hostList, port,
					this.maxRetryTimeOut, this.connectTimeOut, this.socketTimeout));
		}
	}

	private UpdateRequest createUpdateRequestForGrouping(final TransformedTransactionHistoryDetail detail,
			final String groupId) {
		log.debug("SystemId:{}, creating update request for group id:{}", detail.getTxnId(), groupId);
		UpdateRequest updateRequest = new UpdateRequest();
		updateRequest.index(this.getIndexName(detail));
		updateRequest.type("_doc");
		updateRequest.id(detail.docId());
		updateRequest.routing(detail.getEntityId());
		log.debug("Doc ID to be updated:{}", detail.docId());
		try {
			// update DocUpdatedDate field also with group ID and showInListing
			Long currentDate = new Date().getTime();
			if (StringUtils.isNotBlank(groupId) && detail.getShowInListing() != null
					&& detail.getIsSourceSystemUpdated()) {
				updateRequest.doc(jsonBuilder().startObject()
					.field("showInListing", detail.getShowInListing())
					.field("groupId", groupId)
					.field("status", detail.getStatus())
					.field("docUpdatedDate", currentDate)
					.field("sourceSystem", detail.getSourceSystem())
					.endObject());
			}
			else if (StringUtils.isNotBlank(groupId) && detail.getShowInListing() != null) {
				updateRequest.doc(jsonBuilder().startObject()
					.field("showInListing", detail.getShowInListing())
					.field("groupId", groupId)
					.field("status", detail.getStatus())
					.field("docUpdatedDate", currentDate)
					.endObject());
			}
			else if (StringUtils.isNotBlank(groupId)) {
				updateRequest.doc(jsonBuilder().startObject()
					.field("groupId", groupId)
					.field("docUpdatedDate", currentDate)
					.endObject());
			}
			else if (detail.getShowInListing() != null) {
				updateRequest.doc(jsonBuilder().startObject()
					.field("showInListing", detail.getShowInListing())
					.field("status", detail.getStatus())
					.field("docUpdatedDate", currentDate)
					.endObject());
			}
		}
		catch (IOException e) {
			log.error("pipeline:{}, SystemID:{}, Update request creation failed. Exception:{}",
					Utility.getActivePipelineName(), detail.getTxnId(), CommonsUtility.exceptionFormatter(e));
		}
		return updateRequest;
	}

	private UpdateRequest createUpdateRequestForGrouping(final CustomUpdateParams customUpdateParams,
			final String groupId) {
		log.debug("SystemId:{}, creating update request for group id:{}", customUpdateParams.getTxnId(), groupId);
		UpdateRequest updateRequest = new UpdateRequest();
		updateRequest
			.index(this.getIndexNameFromTxnDate(customUpdateParams.getTxnDate(), customUpdateParams.getTxnId()));
		updateRequest.type("_doc");
		updateRequest.id(customUpdateParams.getDocId());
		updateRequest.routing(customUpdateParams.getEntityId());
		log.debug("Doc ID to be updated:{}", customUpdateParams.getDocId());
		try {
			if (StringUtils.isNotBlank(groupId) && customUpdateParams.getShowInListing() != null) {
				XContentBuilder json = jsonBuilder().startObject()
					.field("showInListing", customUpdateParams.getShowInListing())
					.field("groupId", groupId)
					.field("status", customUpdateParams.getStatus())
					.field("docUpdatedDate", customUpdateParams.getDocUpdatedDate())
					.endObject();

				// Adding Context Map
				if (ObjectUtils.isNotEmpty(customUpdateParams.getContextMap())) {
					json.startObject().field("contextMap", customUpdateParams.getContextMap()).endObject();
				}
				updateRequest.doc(json);
			}
			else if (StringUtils.isNotBlank(groupId)) {
				updateRequest.doc(jsonBuilder().startObject()
					.field("groupId", groupId)
					.field("docUpdatedDate", customUpdateParams.getDocUpdatedDate())
					.endObject());
			}
			else if (customUpdateParams.getShowInListing() != null) {
				updateRequest.doc(jsonBuilder().startObject()
					.field("showInListing", customUpdateParams.getShowInListing())
					.field("status", customUpdateParams.getStatus())
					.field("docUpdatedDate", customUpdateParams.getDocUpdatedDate())
					.endObject());
			}
		}
		catch (IOException e) {
			log.error("pipeline:{}, SystemID:{}, Update request creation failed. Exception:{}",
					Utility.getActivePipelineName(), customUpdateParams.getTxnId(),
					CommonsUtility.exceptionFormatter(e));
		}
		return updateRequest;
	}

	public String getIndexName(final TransformedTransactionHistoryDetail element) {
		String indexName = IndexUtility.getUpdatedIndexName(this.indexNamePrefix, element.getTxnDate(),
				this.updatedIndexList);
		// log-optimisation
		log.debug("Fetched index Name: {} for ES search/insert doc. txnid :{}, txnDate:{}", indexName,
				element.getTxnId(), element.getTxnDate());
		return indexName;
	}

	public String getIndexNameFromTxnDate(final Long txnDate, final String txnId) {
		String indexName = IndexUtility.getUpdatedIndexName(this.indexNamePrefix, txnDate, this.updatedIndexList);
		// log-optimisation
		log.debug("Fetched index Name: {} for ES search/insert doc. txnid :{}, txnDate:{}", indexName, txnId, txnDate);
		return indexName;
	}

	@Override
	public TransformedTransactionHistoryDetail getRecordUsingUniqueId(final TransformedTransactionHistoryDetail detail)
			throws IOException {
		return this.getStoredUniqueDoc(detail.docId(), detail.getTxnDate(), detail.getEntityId(), detail);
	}

	public MandateBaseDto getMandateData(final MandateBaseDto mandateBaseDto, final boolean getMandateInfoData,
			final boolean getUsingMandateInfoDoc) throws Exception {
		if (getMandateInfoData) {
			return fetchMandateDataFromAlias(createMandateQueryBuilderParamMap(mandateBaseDto, true),
					mandateBaseDto.getEntityId(), true);
		}
		else if (!getUsingMandateInfoDoc) {
			return fetchMandateDataFromAlias(createMandateQueryBuilderParamMap(mandateBaseDto, false),
					mandateBaseDto.getEntityId(), false);
		}
		return null;
	}

	public MandateBaseDto getSelfMandateData(final MandateBaseDto mandateBaseDto) throws Exception {
		String indexName = MandateUtility.getIndexName(mandateBaseDto, "mandate");
		return getMandateDataUsingDocId(mandateBaseDto.getEntityId(), mandateBaseDto.docId(), indexName,
				mandateBaseDto.getObjectType());
	}

	private Map<String, Object> createMandateQueryBuilderParamMap(final MandateBaseDto mandateBaseDto,
			final boolean getMandateInfoData) {
		Map<String, Object> paramMap = new HashMap<>();
		if (getMandateInfoData) {
			paramMap.put("entityId", mandateBaseDto.getEntityId());
			paramMap.put("umn", mandateBaseDto.getUmn());
		}
		else {
			paramMap.put("entityId", mandateBaseDto.getEntityId());
			paramMap.put("umn", mandateBaseDto.getUmn());
			paramMap.put("txnId", mandateBaseDto.getTxnId());
		}
		return paramMap;
	}

	public MandateBaseDto getMandateDataUsingDocId(final String entityId, final String docId, final String index,
			final TypeReference type) throws IOException {
		log.info("getting mandate data from ES for docId : {}", docId);
		try {
			esureInitialized();
			GetRequest getRequest = new GetRequest().id(docId).routing(entityId).index(index);
			// TODO optimise the above getRequest by getting the index by the use of
			// validityStartDate
			long startTime = System.currentTimeMillis();
			GetResponse response = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
			metricsAgent.recordReadCountAndLatencyForFlinkEs(startTime);
			MandateBaseDto storedMandateDoc = null;
			if (response.isExists()) {
				String sourceAsString = response.getSourceAsString();
				if (StringUtils.isNotBlank(sourceAsString)) {
					storedMandateDoc = (MandateBaseDto) objectMapper.readValue(sourceAsString, type);
					log.info("Mandate data fetched from es with docId : {} is : {}", docId, storedMandateDoc);
				}
			}
			else {
				log.info("No mandate record found in ES with docId : {}", docId);
			}
			return storedMandateDoc;
		}
		catch (Exception exception) {
			if (exception instanceof ElasticsearchStatusException) {
				log.warn("No index found for docId : {}", docId);
				return null;
			}
			log.error("Exception while fetching mandate data from ES for docId : {}, Exception : {}", docId,
					CommonsUtility.exceptionFormatter(exception));
			metricsAgent.incrementCount(EXCEPTION_IN_GETTING_MANDATE_DATA_FROM_ES);
			throw exception;
		}
	}

	@Override
	public TransformedTransactionHistoryDetail getStoredUniqueDoc(final String docId, final Long txnDate,
			final String entityId, final TransformedTransactionHistoryDetail detail) throws IOException {
		try {
			log.debug("getting data from ES for document id:{}", docId);
			// TODO : create a diff class.
			esureInitialized();
			List<String> indicesIncludingBoundary = new ArrayList<>(
					Arrays.asList(IndexUtility.getIndexNames(txnDate, this.indexNamePrefix, this.updatedIndexList)));
			String exactIndex = IndexUtility.getUpdatedIndexName(this.indexNamePrefix, txnDate, this.updatedIndexList);
			if (indicesIncludingBoundary.size() > 1) {
				indicesIncludingBoundary.remove(exactIndex);
				indicesIncludingBoundary.add(0, exactIndex);
			}

			TransformedTransactionHistoryDetail storedObj = null;
			for (String index : indicesIncludingBoundary) {
				GetRequest getRequest = new GetRequest().id(docId).routing(entityId).index(index);
				long startTime = System.currentTimeMillis();
				GetResponse response = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
				metricsAgent.recordReadCountAndLatencyForFlinkEs(startTime);
				if (response.isExists()) {
					String sourceAsString = response.getSourceAsString();
					if (sourceAsString != null) {
						storedObj = objectMapper.readValue(sourceAsString, TransformedTransactionHistoryDetail.class);
						log.debug("pipeline:{}, docID: {}, foundData from Es", Utility.getActivePipelineName(), docId);
					}
					break;
				}
				else if (TransactionSource.isPgTypeSource(detail.getStreamSource())) {
					storedObj = handlePgTxnsWhereRecordNotFoundFromTxnDate(detail);
				}
				else {
					log.debug("No record found in ES with Doc ID:{}", docId);
				}
			}
			log.info("Data fetched from es with unique id : {}", storedObj);
			return storedObj;
		}
		catch (Exception ex) {
			if (ex instanceof ElasticsearchStatusException && (Objects.isNull(detail.getContextMap())
					|| !TRUE.equals(detail.getContextMap().get(IS_FROM_DC_PIPELINE)))) {
				// Can use diff message and add alert for the same.
				log.info("No index found for txnid :{}", docId);
				return null;
			}

			BackFillingIdentifierEnum backfillingIdentifier = null;
			if (Objects.nonNull(detail.getContextMap()) && detail.getContextMap().containsKey(BACKFILLING_IDENTIFIER)) {
				backfillingIdentifier = BackFillingIdentifierEnum
					.getBackFillingIdentifierEnumByKey(detail.getContextMap().get(BACKFILLING_IDENTIFIER));
			}
			log.error("backfillingIdentifier : {} Exception while fetching data for id :{}, Exception:{}",
					backfillingIdentifier, docId, CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	private TransformedTransactionHistoryDetail handlePgTxnsWhereRecordNotFoundFromTxnDate(
			final TransformedTransactionHistoryDetail detail) throws IOException {
		// if pg txn is not being able to be found in index where txnDate of this event
		// belongs
		// then process the below instruction set
		Long extractedTxnDate = Utility.getOrderTxnDateFromPgTxnId(detail.getTxnId(), detail.getTxnDate());
		// if extracted txnDate is null will return null response
		if (extractedTxnDate == null) {
			log.error("Extracted txnDate from txnId: {} is null", detail.getTxnId());
			metricsAgent.incrementCount(EXTRACTED_TXN_DATE_FROM_PG_NULL);
			return null;
		}

		String indexToQueryUpon = this.getIndexNameFromTxnDate(extractedTxnDate, detail.getTxnId());
		// if extracted txnDate and txndate field both queries over same index
		// then no nedd to query over ES again
		if (this.getIndexName(detail).equals(indexToQueryUpon)) {
			return null;
		}

		log.error("Going to query over ES again for PG with txnDate extracted : {} from " + "txnId: {} is null",
				extractedTxnDate, detail.getTxnId());
		metricsAgent.incrementCount(EXTRACTED_STORED_DOC_FOR_PG_TWICE);

		String docId = detail.docId();
		GetRequest getRequest = new GetRequest().id(docId).routing(detail.getEntityId()).index(indexToQueryUpon);
		long startTime = System.currentTimeMillis();
		GetResponse response = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
		metricsAgent.recordReadCountAndLatencyForFlinkEs(startTime);
		TransformedTransactionHistoryDetail storedObj = null;
		if (response.isExists()) {
			String sourceAsString = response.getSourceAsString();
			if (sourceAsString != null) {
				storedObj = objectMapper.readValue(sourceAsString, TransformedTransactionHistoryDetail.class);
				log.debug("pipeline:{}, docID: {}, foundData from requering Es:{}", Utility.getActivePipelineName(),
						docId, detail);
			}
		}
		else {
			log.debug("No record found in ES with Doc ID:{}, System ID:{}, by requering over index : {}", docId,
					detail.getTxnId(), indexToQueryUpon);
		}
		return storedObj;
	}

	@Override
	public List<TransformedTransactionHistoryDetail> fetchRecord(final Map<String, Object> paramMap, final Long txnDate,
			final String systemId) throws Exception {
		try {
			log.info("Fetching details from ES with paramMap:{}", paramMap);
			SearchRequest searchRequest = new SearchRequest().indices(this.getIndexNames(txnDate));
			return getTransformedTransactionHistoryDetails(paramMap, searchRequest, systemId);
		}
		catch (Exception e) {
			log.warn("Exception while getting es record. Exception :{}", CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	private Long getCount(final Map<String, Object> paramMap, final SearchContext searchContext,
			final CountRequest countRequest) {

		Long count = null;
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
		queryBuilder.filter(
				QueryBuilders.rangeQuery("txnDate").gte(searchContext.getFromDate()).lte(searchContext.getToDate()));
		for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
			if (entry.getValue() == null) {
				log.info("Null value found for entry : {} for paramMap : {}", entry, paramMap);
				return null;
			}
			queryBuilder.filter(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
		}

		searchSourceBuilder.query(queryBuilder);
		countRequest.source(searchSourceBuilder);
		esureInitialized();
		CountResponse countResponse = null;
		try {
			long startTime = System.currentTimeMillis();
			countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
			metricsAgent.recordReadCountAndLatencyForFlinkEs(startTime);
		}
		catch (Exception e) {
			log.error("Some exception occurred while executing count request on ES : {}",
					CommonsUtility.exceptionFormatter(e));
			return null;
		}

		if (Objects.nonNull(countResponse)) {
			count = countResponse.getCount();
		}

		return count;
	}

	@Override
	public UpdateRequest createUserImageUpdateRequest(final TransformedTransactionHistoryDetail detail) {
		log.debug("SystemId:{}, creating update request", detail.getTxnId());
		try {
			if (!StringUtils.isEmpty(detail.getSecondUserImageUrl())) {
				UpdateRequest updateRequest = new UpdateRequest();
				updateRequest.index(this.getIndexName(detail));
				updateRequest.type("_doc");
				updateRequest.id(detail.docId());
				updateRequest.routing(detail.getEntityId());
				log.debug("Doc ID to be updated:{}", detail.docId());
				updateRequest.doc(jsonBuilder().startObject()
					.field("secondUserImageUrl", detail.getSecondUserImageUrl())
					.field("docUpdatedDate", detail.getDocUpdatedDate())
					.endObject());
				return updateRequest;
			}
		}
		catch (IOException e) {
			log.error("pipeline:{}, SystemID:{}, Update request creation failed. Exception:{}",
					Utility.getActivePipelineName(), detail.getTxnId(), CommonsUtility.exceptionFormatter(e));
			return null;
		}
		return null;
	}

	private UpdateRequest createUpdateRequestForOtherPartyEntityIdRelatedFields(
			final TransformedTransactionHistoryDetail tthd, final String otherPartyEntityId,
			final Boolean isOtherPartyEntityIdVirtual) {
		UpdateRequest updateRequest = new UpdateRequest();
		updateRequest.index(this.getIndexName(tthd));
		updateRequest.type(UNDERSCORE_DOC);
		updateRequest.id(tthd.docId());
		updateRequest.routing(tthd.getEntityId());
		log.debug("Doc ID to be updated is : {}", tthd.docId());
		try {
			updateRequest.doc(jsonBuilder().startObject()
				.field(OTHER_PARTY_ENTITY_ID, otherPartyEntityId)
				.field(IS_OTHER_PARTY_ENTITY_ID_VIRTUAL, isOtherPartyEntityIdVirtual)
				// This updated is not being taken care in dualWriteEs. So not updating
				// docUpdatedDate here.
				// .field(DOC_UPDATED_DATE, currentDate)
				.endObject());
		}
		catch (IOException e) {
			log.error(
					"txnId : {}, Update request creation for otherPartyEntityId & isOtherPartyEntityIdVirtual fields failed. Exception : {}",
					tthd.getTxnId(), CommonsUtility.exceptionFormatter(e));
			return null;
		}
		return updateRequest;
	}

	public void dumpUdirDocInEs(final Object obj, final String docId) throws IOException {
		byte[] json = Utility.writeValueAsBytes(obj);
		// udirDumpIndexPrefix is the index since it has no index rotation based on time
		IndexRequest indexRequest = Requests.indexRequest()
			.index(udirDumpIndexPrefix)
			.type("_doc")
			.source(json, XContentType.JSON)
			.id(docId);
		// routing over txnId/refId as docId = txnId/refId
		indexRequest.routing(docId);
		long startTime = System.currentTimeMillis();
		restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
		metricsAgent.recordWriteCountAndLatencyForFlinkEs(startTime);
	}

	public List<MandateActivityData> fetchMandateActivityData(final SearchContext searchContext,
			final PaginationParams paginationParams, final boolean sortInAscending) throws Exception {

		try {
			esureInitialized();

			SearchSourceBuilder searchSourceBuilder = EsUtility.createSearchSourceBuilder(searchContext,
					paginationParams, sortInAscending);
			SearchRequest searchRequest = new SearchRequest().indices(mandateActivityIndexAlias);
			searchRequest.source(searchSourceBuilder);

			SearchResponse searchResponse = null;
			searchResponse = this.search(searchRequest);

			SearchHits hits = searchResponse.getHits();
			List<SearchHit> hitsList = Arrays.asList(hits.getHits());

			if (hitsList.isEmpty()) {
				log.warn("No result found from Es for request : {}", searchContext);
				return null;
			}

			List<MandateActivityData> mandateActivityDataList = new ArrayList<>();
			for (SearchHit hit : hits) {
				String sourceAsString = hit.getSourceAsString();
				if (sourceAsString != null) {
					MandateActivityData mandateActivityData = objectMapper.readValue(sourceAsString,
							MandateActivityData.class);
					mandateActivityDataList.add(mandateActivityData);
				}
			}

			return mandateActivityDataList;
		}
		catch (Exception e) {
			log.error("Exception while fetching es response for search context : {}. Exception : {}", searchContext,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	public void setPipelineName(final String pipelineName) {
		this.pipelineName = pipelineName;
	}

}