package com.org.panaroma.ingester.statusResolver;

import static com.org.panaroma.commons.utils.Utility.isMandateTransaction;
import static com.org.panaroma.ingester.constants.Constants.AUTHORIZATION;
import static com.org.panaroma.ingester.constants.Constants.BEARER;
import static com.org.panaroma.ingester.constants.Constants.CHANNEL;
import static com.org.panaroma.ingester.constants.Constants.CONTENT_TYPE;
import static com.org.panaroma.ingester.constants.Constants.RelayConstants.IS_FROM_RELAY;
import static com.org.panaroma.ingester.constants.Constants.RelayConstants.ONE;
import static com.org.panaroma.ingester.constants.Constants.UTH;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.UpiStatusFetcherRequest;
import com.org.panaroma.commons.dto.UpiStatusFetcherResponse;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.ConversionUtility;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.monitoring.MonitoringConstants;
import com.org.panaroma.ingester.utils.RelayUtility;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Log4j2
public class UpiStatusResolver implements IStatusResolver {

	private Integer readTimeOut;

	private String upiSecretKey;

	private String upiSecretKeyForRelay;

	private String upiBaseUrlForMandateTxns;

	private String upiBaseUrlForNonMandateTxns;

	private String upiBaseUrlForRelay;

	private String statusFetcherApiUrl;

	private String statusFetcherRelayApiUrl;

	private String mandateStatusFetcherApiUrl;

	private ObjectMapper objectMapper;

	private static Gson gson = new GsonBuilder().create();

	private MetricsAgent metricsAgent;

	private final RelayUtility relayUtility;

	private StatusResolverUtility statusResolverUtility;

	@Autowired
	public UpiStatusResolver(@Value("${upi.status.check.connection.timeout}") final Integer readTimeOut,
			@Value("${upi.secret.key}") final String upiSecretKey,
			@Value("${upi.relay.secret.key}") final String upiSecretKeyForRelay,
			@Value("${upi.baseUrl.for.mandate.txns}") final String upiBaseUrlForMandateTxns,
			@Value("${upi.baseUrl.for.non.mandate.txns}") final String upiBaseUrlForNonMandateTxns,
			@Value("${upi.relay.baseUrl}") final String upiBaseUrlForRelay,
			@Value("${upi.status.check.url}") final String statusFetcherApiUrl,
			@Value("${upi.relay.status.check.url}") final String statusFetcherRelayApiUrl,
			@Value("${upi.mandate.status.check.url}") final String mandateStatusFetcherApiUrl,
			final ObjectMapper objectMapper, final MetricsAgent metricsAgent, final RelayUtility relayUtility,
			final StatusResolverUtility statusResolverUtility) {
		this.readTimeOut = readTimeOut;
		this.upiSecretKey = upiSecretKey;
		this.upiSecretKeyForRelay = upiSecretKeyForRelay;
		this.upiBaseUrlForMandateTxns = upiBaseUrlForMandateTxns;
		this.upiBaseUrlForNonMandateTxns = upiBaseUrlForNonMandateTxns;
		this.upiBaseUrlForRelay = upiBaseUrlForRelay;
		this.statusFetcherApiUrl = statusFetcherApiUrl;
		this.statusFetcherRelayApiUrl = statusFetcherRelayApiUrl;
		this.mandateStatusFetcherApiUrl = mandateStatusFetcherApiUrl;
		this.objectMapper = objectMapper;
		this.metricsAgent = metricsAgent;
		this.relayUtility = relayUtility;
		this.statusResolverUtility = statusResolverUtility;
	}

	@Override
	public TransactionSource appliesTo() {
		return TransactionSource.UPI;
	}

	@Override
	public TransactionHistoryDetails getUpdatedDto(final TransformedTransactionHistoryDetail detail) throws Exception {

		TransactionHistoryDetails transactionHistoryDetails = null;
		long startTime = new Date().getTime();
		long endTime;
		try {
			this.metricsAgent.incrementCount(MonitoringConstants.STATUS_CHECK_API_COUNT, MonitoringConstants.CLIENT_TAG
					+ MonitoringConstants.SEPARATOR + TransactionSource.UPI.getTransactionSource());

			// create request body
			UpiStatusFetcherRequest upiStatusFetcherRequest = StatusResolverUtility
				.createUpiStatusFetcherRequest(detail);

			// create token
			String jwtToken = this.generateJwtToken(detail, detail.getTxnId(), upiStatusFetcherRequest);
			if (StringUtils.isEmpty(jwtToken)) {
				log.warn("Jwt token generated is null");
			}
			else {
				log.warn("Jwt token generated succesfully");
			}

			log.warn("Fetching data from UPI for request :{}", upiStatusFetcherRequest);
			String response = this.getResponse(upiStatusFetcherRequest, jwtToken, detail);
			log.warn("Response received  :{} for txnId :{}", response, detail.getTxnId());
			UpiStatusFetcherResponse upiStatusFetcherResponse = this.convertResponse(response);

			log.warn("Converted Response :{} for txnId :{}", upiStatusFetcherResponse, detail.getTxnId());
			if (Objects.isNull(upiStatusFetcherResponse) || Objects.isNull(upiStatusFetcherResponse.getTxnDetails())) {
				return null;
			}

			log.warn("Response is not null so converting into thd for txnId :{}", detail.getTxnId());
			transactionHistoryDetails = ConversionUtility
				.convertToTransactionHistoryDetailObject(upiStatusFetcherResponse.getTxnDetails());
			if (relayUtility.isRelayEvent(detail)) {
				Map<String, String> contextMap = transactionHistoryDetails.getContextMap();
				if (contextMap == null) {
					contextMap = new HashMap<>();
				}
				contextMap.put(IS_FROM_RELAY, ONE);
				transactionHistoryDetails.setContextMap(contextMap);
			}
		}
		catch (Exception e) {
			log.error("Exception while fetching data from UPI. Exception :{}", CommonsUtility.exceptionFormatter(e));
			endTime = new Date().getTime();
			metricsAgent.pushTimeDiff(MonitoringConstants.STATUS_CHECK_API_EXECUTION_TIME + "_"
					+ TransactionSource.UPI.getTransactionSource(), startTime, endTime);
			throw e;
		}
		endTime = new Date().getTime();
		metricsAgent.pushTimeDiff(MonitoringConstants.STATUS_CHECK_API_EXECUTION_TIME + "_"
				+ TransactionSource.UPI.getTransactionSource(), startTime, endTime);
		return transactionHistoryDetails;
	}

	private CloseableHttpClient getHttpClient() {
		RequestConfig config = RequestConfig.custom().setConnectionRequestTimeout(readTimeOut).build();
		return HttpClientBuilder.create().setDefaultRequestConfig(config).build();
	}

	/*
	 * decommissioning UPi Proxy. We'll no longer do JWT verification with TSP, we'll do
	 * token verification with Gateway and in-turn gateway will do verification with TSP
	 */
	private String generateJwtToken(final TransformedTransactionHistoryDetail tthd, final String seqNum,
			final UpiStatusFetcherRequest upiStatusFetcherRequest)
			throws JsonProcessingException, NoSuchAlgorithmException {

		if (Objects.nonNull(tthd) && relayUtility.isRelayEvent(tthd)) {
			return statusResolverUtility.createJwtForGatewayRouting(upiStatusFetcherRequest, getUpiSecretKey(tthd));
		}
		return statusResolverUtility.createJwt(tthd, seqNum, getUpiSecretKey(tthd));
	}

	private String getResponse(final UpiStatusFetcherRequest upiStatusFetcherRequest, final String jwtToken,
			final TransformedTransactionHistoryDetail detail) throws Exception {
		try (CloseableHttpClient httpClient = this.getHttpClient()) {
			String statusFetchApiUrl = getStatusFetchApiUrl(detail);
			HttpPost request = new HttpPost(getUpiBaseUrl(detail) + statusFetchApiUrl);
			StringEntity params = new StringEntity(this.objectMapper.writeValueAsString(upiStatusFetcherRequest));
			request.addHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
			request.addHeader(AUTHORIZATION, BEARER + jwtToken);
			if (Objects.nonNull(detail) && relayUtility.isRelayEvent(detail)) {
				request.addHeader(CHANNEL, UTH);
			}
			request.setEntity(params);
			log.warn("UPI status fetch request :{}", request);
			try (CloseableHttpResponse closeableHttpResponse = httpClient.execute(request)) {
				HttpEntity responseEntity = closeableHttpResponse.getEntity();
				log.warn("Response entity :{}", responseEntity);
				return EntityUtils.toString(responseEntity);
			}
		}
		catch (Exception e) {
			log.warn("Exception while getting response from UPI. Exception :{}", CommonsUtility.exceptionFormatter(e));
			throw e;
		}

	}

	private UpiStatusFetcherResponse convertResponse(final String response) {
		UpiStatusFetcherResponse upiStatusFetcherResponse = null;
		try {
			if (!StringUtils.isEmpty(response)) {
				upiStatusFetcherResponse = gson.fromJson(response, UpiStatusFetcherResponse.class);
				log.warn("Response after parsing from UPI API is : {}", upiStatusFetcherResponse);
			}
		}
		catch (Exception e) {
			log.error("Exception occurred : {} while parsing response : {} received from UPI status fetch API",
					CommonsUtility.exceptionFormatter(e), response);
		}
		if (upiStatusFetcherResponse != null) {
			metricsAgent.incrementCount(MonitoringConstants.STATUS_CHECK_API_RESPONSE_CODE_COUNT,
					MonitoringConstants.SOURCE + TransactionSource.UPI.getTransactionSource(),
					MonitoringConstants.RESPONSE_CODE + upiStatusFetcherResponse.getRespCode());
		}
		return upiStatusFetcherResponse;
	}

	private String getStatusFetchApiUrl(final TransformedTransactionHistoryDetail detail) {
		if (Objects.nonNull(detail)) {

			if (relayUtility.isRelayEvent(detail)) {
				return statusFetcherRelayApiUrl;
			}

			else if (isMandateTransaction(detail)) {
				return mandateStatusFetcherApiUrl;
			}

		}
		return statusFetcherApiUrl;
	}

	private String getUpiBaseUrl(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd) && relayUtility.isRelayEvent(tthd)) {
			return upiBaseUrlForRelay;
		}
		else if (Objects.nonNull(tthd) && isMandateTransaction(tthd)) {
			return upiBaseUrlForMandateTxns;
		}
		return upiBaseUrlForNonMandateTxns;
	}

	private String getUpiSecretKey(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd) && relayUtility.isRelayEvent(tthd)) {
			return upiSecretKeyForRelay;
		}
		return upiSecretKey;
	}

}
