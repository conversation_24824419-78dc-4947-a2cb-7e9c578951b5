package com.org.panaroma.ingester.transformer;

import static com.org.panaroma.commons.constants.Constants.UNDERSCORE;
import static com.org.panaroma.ingester.constants.Constants.USER_TRANSACTION_TAG_ORIGINATOR;

import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.cache.OldestTxnUpdateCache;
import com.org.panaroma.commons.dto.cache.ZeroDeltaCache;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.ingester.constants.PipelineConstants;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class CacheInvalidator implements FlatMapFunction<TransformedTransactionHistoryDetail, CacheUpdaterKafkaDto> {

	private Boolean isNtuCacheEnabled;

	private Utility utility;

	@Autowired
	public CacheInvalidator(final Utility utility, @Value("${isNtuCacheEnabled}") final Boolean isNtuCacheEnabled) {
		this.isNtuCacheEnabled = isNtuCacheEnabled;
		this.utility = utility;
	}

	@Override
	public void flatMap(final TransformedTransactionHistoryDetail tthd, final Collector<CacheUpdaterKafkaDto> collector)
			throws Exception {
		try {
			Map<CacheInfo, String> cacheNameToCacheKeyMapping = getCacheNameToCacheKeyMapping(tthd);
			CacheUpdaterKafkaDto cacheUpdaterCommonData = getCacheUpdaterKafkaDto(tthd, cacheNameToCacheKeyMapping);
			cacheUpdaterCommonData.setUid(tthd.getTxnId());

			cacheUpdaterCommonData.setOriginator(USER_TRANSACTION_TAG_ORIGINATOR);
			Long currentTime = System.currentTimeMillis();
			cacheUpdaterCommonData.setCreatedDate(currentTime);
			cacheUpdaterCommonData.putInMetaInfo(PipelineConstants.TIME_WHEN_INVALIDATION_DTO_PUSHED,
					currentTime.toString());
			collector.collect(cacheUpdaterCommonData);

		}
		catch (Exception exception) {
			log.error("inside CacheInvalidator {} {}", tthd.docId(), CommonsUtility.exceptionFormatter(exception));
		}
	}

	public String getDetailPageCacheKey(final String userId, final String txnId,
			final TransactionSource transactionSource, final Boolean showBankData) {
		return userId + UNDERSCORE + txnId + UNDERSCORE + transactionSource + UNDERSCORE + showBankData;
	}

	public Map<CacheInfo, String> getCacheNameToCacheKeyMapping(final TransformedTransactionHistoryDetail tthd) {
		Map<CacheInfo, String> cacheNameToCacheKeyMapping = new HashMap<>();
		if (Objects.isNull(tthd) || StringUtils.isBlank(tthd.getEntityId())) {
			return cacheNameToCacheKeyMapping;
		}

		TransactionSource transactionSource = TransactionSource
			.getTransactionSourceEnumByKey(tthd.getStreamSource().intValue());

		cacheNameToCacheKeyMapping.put(CacheInfo.UTH_NTU_CACHE, tthd.getEntityId());
		cacheNameToCacheKeyMapping.put(CacheInfo.UTH_APP_SIDE_CACHE_DATA, tthd.getEntityId());
		cacheNameToCacheKeyMapping.put(CacheInfo.UTH_DETAIL_CACHE,
				getDetailPageCacheKey(tthd.getEntityId(), tthd.getTxnId(), transactionSource, Boolean.TRUE));

		if (cacheNameToCacheKeyMapping.containsKey(CacheInfo.UTH_NTU_CACHE) && !(Boolean.TRUE.equals(isNtuCacheEnabled)
				&& utility.checkIfWhiteListed(tthd.getEntityId(), CacheInfo.UTH_NTU_CACHE.getCacheName()))) {
			log.warn("Ntu cache is not enabled or user is not whitelisted for txnId : {}, entityId : {}",
					tthd.getTxnId(), tthd.getEntityId());
			cacheNameToCacheKeyMapping.remove(CacheInfo.UTH_NTU_CACHE);
		}

		return cacheNameToCacheKeyMapping;
	}

	private CacheUpdaterKafkaDto getCacheUpdaterKafkaDto(final TransformedTransactionHistoryDetail tthd,
			final Map<CacheInfo, String> cacheNameToCacheKeyMapping) {
		if (Objects.isNull(cacheNameToCacheKeyMapping) || cacheNameToCacheKeyMapping.isEmpty()) {
			return null;
		}

		CacheUpdaterKafkaDto cacheUpdaterKafkaDto = new CacheUpdaterKafkaDto();
		cacheUpdaterKafkaDto.setCacheMetaInfo(cacheNameToCacheKeyMapping);
		cacheUpdaterKafkaDto.setIsNonTransactingUser(Boolean.FALSE);
		cacheUpdaterKafkaDto.setEntityId(tthd.getEntityId());

		if (tthd.getDocUpdatedDate() != null) {
			AppSideCacheData appSideCacheData = new AppSideCacheData();
			appSideCacheData
				.setZeroDeltaCacheData(ZeroDeltaCache.builder().fromUpdatedDate(tthd.getDocUpdatedDate()).build());
			appSideCacheData.setOldestTxnUpdateCacheData(OldestTxnUpdateCache.builder()
				.docUpdatedDate(tthd.getDocUpdatedDate())
				.txnDate(tthd.getTxnDate())
				.build());

			cacheUpdaterKafkaDto.setAppSideCacheData(appSideCacheData);
		}
		else {
			log.error("Not setting App side cache in cacheUpdaterFlatmap, " + "as docUpdatedDate = null, for txn: {}",
					tthd);
		}

		return cacheUpdaterKafkaDto;
	}

}
