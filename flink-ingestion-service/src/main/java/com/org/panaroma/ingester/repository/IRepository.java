package com.org.panaroma.ingester.repository;

import com.org.panaroma.commons.dto.PaginationParams;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.ingester.cst.dto.EsGenericsResponse;
import com.org.panaroma.ingester.dto.insertUpdateExecutorDtos.BulkUpdateDto;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;

public interface IRepository<T, O> extends Serializable {

	List<TransformedTransactionHistoryDetail> fetchData(SearchContext searchContext, PaginationParams paginationParams,
			boolean sortInAscending) throws Exception;

	List<T> fetchData(O searchObject, T object) throws Exception;

	List<T> fetchData(O searchObject, Long txnDate, String tracingId) throws Exception;

	T fetchDataFromAlias(O searchObject, T object) throws Exception;

	List<T> fetchDataOnlyFromAlias(O searchObject, T object) throws Exception;

	void updateTxnsForGrouping(Set<T> detailList, String groupId);

	default void updateTxnsForGrouping(BulkUpdateDto bulkUpdateDto) {

	}

	T getRecordUsingUniqueId(T t) throws IOException;

	List<T> fetchRecord(O searchObject, Long txnDate, String id) throws Exception;

	Long fetchCount(O searchObject, SearchContext searchContext);

	UpdateRequest createUserImageUpdateRequest(T detail);

	void updateOtherPartyEntityIdRelatedFields(T tthd);

	UpdateRequest createUpdateRequestForTags(T tthd);

	T getStoredUniqueDoc(String docId, Long txnDate, String entityId, T detail) throws IOException;

	SearchResponse search(final SearchRequest searchRequest) throws Exception;

	default EsGenericsResponse fetchDocUsingDocId(final String docId, final String indexName, final String routing,
			final Class outputClass) throws IOException {
		return null;
	}

}
