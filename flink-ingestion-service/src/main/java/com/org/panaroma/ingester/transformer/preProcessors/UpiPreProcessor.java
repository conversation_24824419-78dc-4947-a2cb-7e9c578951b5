package com.org.panaroma.ingester.transformer.preProcessors;

import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.Constants.IS_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.CHANNEL_CODE;
import static com.org.panaroma.commons.constants.WebConstants.ONLINE_ONUS_NATIVE;
import static com.org.panaroma.ingester.constants.Constants.TRUE;
import static com.org.panaroma.ingester.utils.Utility.areParticipantCustIdsEqual;
import static com.org.panaroma.ingester.utils.Utility.isP2pTransaction;

import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import com.org.panaroma.commons.utils.UpiViaCcUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.transformer.interfaces.IPreProcessor;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class UpiPreProcessor implements IPreProcessor {

	private final RolloutStrategyHelper rolloutStrategyHelper;

	private final MetricsAgent metricsAgent;

	@Autowired
	public UpiPreProcessor(RolloutStrategyHelper rolloutStrategyHelper, final MetricsAgent metricsAgent) {
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.metricsAgent = metricsAgent;
	}

	@Override
	public void preProcess(final TransactionHistoryDetails thd) {
		if (thd == null) {
			return;
		}
		if (UpiViaCcUtility.isPartialEmiEvent(thd)) {
			UpiViaCcUtility.removeStatusFromEvent(thd);
		}

		// don't populate location for mandateTxn PTH-11
		if (Utility.isMandateTransaction(thd)) {
			Utility.removeLocationData(thd);
		}

		// Adding source details as OMS if it's ONUS Txns
		if (isOmsSourceDetailsRequired(thd)) {
			metricsAgent.incrementCount("UPI_ONUS_EVENT_TXN_COUNT");
			log.warn("Source system and source txnId oms and rrn added txnId: {}", thd.getSystemId());
			thd.setSourceSystem(TransactionSource.OMS);
			thd.setSourceSystemId(thd.getContextMap().get("rrn"));
		}

		/*
		 * introduced to identify self payment done via QR Code as
		 * selfTransferTransaction. if we do self transfer via "to self a/c" then it is
		 * displayed as a self transfer transaction in passbook -> need to do similar
		 * handling when payment is done to self from one account to another account via
		 * QR code. (we receive same custId in both participants) -> make the custId of
		 * credit participant null and set isSelfTransfer flag = true.
		 */
		handleSelfTransferTransactionViaQr(thd);
	}

	private boolean isOmsSourceDetailsRequired(final TransactionHistoryDetails thd) {
		if (Objects.isNull(thd.getContextMap()) || StringUtils.isBlank(thd.getContextMap().get("rrn"))) {
			return false;
		}

		// if not OMS Txns
		// Txn won't have onus channel code
		if (Boolean.FALSE
			.equals(StringUtils.equalsIgnoreCase(thd.getContextMap().get(CHANNEL_CODE), ONLINE_ONUS_NATIVE))
				|| BackFillingIdentifierEnum.UPI_ONUS_PENDING_AFTER_N_DAYS.getBackFillingIdentifierKey()
					.equals(thd.getContextMap().get(BACKFILLING_IDENTIFIER))) {
			return false;
		}

		// Todo: Discarding relay event from grouping

		// Adding txn type check for now. Not setting for non P2M txns type
		if (!TransactionTypeEnum.P2M.equals(thd.getTxnType())) {
			return false;
		}

		String custId = getCustId(thd);
		if (!rolloutStrategyHelper.isUserWhiteListed("omsIntegration", custId)) {
			log.warn("User is not whitelisted for OMS Integration txnId: {}", thd.getSystemId());
			return false;
		}

		return true;
	}

	private void handleSelfTransferTransactionViaQr(TransactionHistoryDetails thd) {

		if (ObjectUtils.isNotEmpty(thd) && ObjectUtils.isNotEmpty(thd.getParticipants()) && isP2pTransaction(thd)) {
			TxnParticipants creditParticipant = null;

			for (TxnParticipants participant : thd.getParticipants()) {
				if (TransactionIndicator.CREDIT.equals(participant.getTxnIndicator())) {
					creditParticipant = participant;
					break;
				}
			}

			if (ObjectUtils.isNotEmpty(creditParticipant) && areParticipantCustIdsEqual(thd.getParticipants())) {
				creditParticipant.setCustomerId(null);
				setSelfTransferTransactionFlag(thd);
			}
		}
	}

	public static void setSelfTransferTransactionFlag(TransactionHistoryDetails thd) {
		if (thd.getContextMap() == null) {
			thd.setContextMap(new HashMap<>());
		}
		thd.getContextMap().put(IS_SELF_TRANSFER, TRUE);
	}

	private String getCustId(final TransactionHistoryDetails txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getParticipants())) {
			return null;
		}

		for (TxnParticipants participant : txn.getParticipants()) {
			if (StringUtils.isNotBlank(participant.getCustomerId())) {
				return participant.getCustomerId();
			}
		}

		return null;
	}

	@Override
	public List<TransactionSource> appliesOnSource() {
		return Arrays.asList(TransactionSource.UPI);
	}

}
