package com.org.panaroma.ingester.pipeline.builder;

import static com.org.panaroma.ingester.constants.PipelineConstants.BACKFILLING_PIPELINE_NAME;

import com.org.panaroma.commons.dto.MandateBackFillingKafkaObject;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.ingester.configuration.flink.FlinkProperties;
import com.org.panaroma.ingester.pipeline.FlinkPipeline;
import com.org.panaroma.ingester.pipeline.interfaces.IPipeline;
import com.org.panaroma.ingester.pipeline.interfaces.IPipelineBuilder;
import com.org.panaroma.ingester.repository.EsRepository;
import com.org.panaroma.ingester.sinks.factory.FlinkSinksFactory;
import com.org.panaroma.ingester.sinks.objects.FlinkSink;
import com.org.panaroma.ingester.sources.factory.FlinkSourcesFactory;
import com.org.panaroma.ingester.sources.objects.FlinkSource;
import com.org.panaroma.ingester.stats.SimpleCounter;
import com.org.panaroma.ingester.transformer.MandateBackFillingProcessor;
import com.org.panaroma.ingester.utils.Utility;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class MandateDataBackFillingPipelineBuilder implements IPipelineBuilder {

	private FlinkSourcesFactory sourceFunctionFactory;

	private FlinkProperties flinkProperties;

	private MandateBackFillingProcessor mandateBackFillingProcessor;

	private FlinkSinksFactory sinkFunctionFactory;

	private final EsRepository esRepository;

	@Autowired
	public MandateDataBackFillingPipelineBuilder(final FlinkSourcesFactory flinkSourcesFactory,
			final FlinkProperties flinkProperties, final MandateBackFillingProcessor mandateBackFillingProcessor,
			final FlinkSinksFactory flinkSinksFactory, final EsRepository esRepository) {
		this.sourceFunctionFactory = flinkSourcesFactory;
		this.flinkProperties = flinkProperties;
		this.mandateBackFillingProcessor = mandateBackFillingProcessor;
		this.sinkFunctionFactory = flinkSinksFactory;
		this.esRepository = esRepository;
	}

	@Override
	public IPipeline build(final String pipelineName) {
		StreamExecutionEnvironment executionEnvironment = StreamExecutionEnvironment.getExecutionEnvironment();
		this.setParallelism(pipelineName, executionEnvironment);
		Utility.setCheckPointing(executionEnvironment, flinkProperties, pipelineName);
		esRepository.setPipelineName(BACKFILLING_PIPELINE_NAME);
		List<SourceFunction> sf = sourceFunctionFactory.getSourceFunctionsByPipeline(pipelineName)
			.stream()
			.map(FlinkSource::getSource)
			.collect(Collectors.toList());
		List<SinkFunction> sinkFunctions = sinkFunctionFactory.getSinksByPipeline(pipelineName)
			.stream()
			.map(FlinkSink::getSink)
			.collect(Collectors.toList());
		DataStream<MandateBackFillingKafkaObject> inputStream = null;
		for (SourceFunction sourceFunction : sf) {
			DataStream<MandateBackFillingKafkaObject> tempStream = executionEnvironment.addSource(sourceFunction)
				.uid(pipelineName + "-source")
				.name(pipelineName + "-source");
			if (inputStream == null) {
				inputStream = tempStream;
			}
			else {
				inputStream.union(tempStream);
			}
		}
		inputStream.map(new SimpleCounter(pipelineName + "-inputCount"));

		DataStream<MandateActivityData> convertedStream = inputStream.flatMap(this.mandateBackFillingProcessor)
			.uid(pipelineName + "-mandate-backfilling")
			.name(pipelineName + "-mandate-backfilling");

		for (SinkFunction sinkFunction : sinkFunctions) {
			convertedStream.addSink(sinkFunction).uid(pipelineName + "-sink").name(pipelineName + "-sink");
		}

		return new FlinkPipeline(executionEnvironment, pipelineName);
	}

	@Override
	public List<String> appliesTo() {
		return Collections.singletonList(BACKFILLING_PIPELINE_NAME);
	}

	private void setParallelism(final String pipelineName, final StreamExecutionEnvironment env) {
		if (!this.flinkProperties.getParallelismEnabled()) {
			return;
		}
		env.setParallelism(Utility.getParallelismValue(pipelineName, flinkProperties));
	}

}
