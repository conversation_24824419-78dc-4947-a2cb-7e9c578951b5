package com.org.panaroma.ingester;

import static com.org.panaroma.ingester.monitoring.MonitoringConstants.CLASS_NAME;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.DELAY_IN_STORING_TAG_IN_DB;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.FALSE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.RETRY_ABLE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.TAG_EXCEPTION_COUNT;

import com.org.panaroma.commons.dto.es.SearchFields;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.kafkaPushAPI.TagTypeEnum;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.ingester.cache.CacheKeyManager;
import com.org.panaroma.ingester.dao.ICacheDataDao;
import com.org.panaroma.ingester.dto.TxnTagsData;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class TagFiledEnricher
		implements FlatMapFunction<TxnTagsData, TransformedTransactionHistoryDetail>, Serializable {

	public static final String TRANSFORMATION_NAME = "TagFieldEnricher";

	final ICacheDataDao iCacheDataDao;

	final MetricsAgent metricsAgent;

	@Autowired
	public TagFiledEnricher(final ICacheDataDao iCacheDataDao, final MetricsAgent metricsAgent) {
		this.iCacheDataDao = iCacheDataDao;
		this.metricsAgent = metricsAgent;
	}

	private TransformedTransactionHistoryDetail getUpdatedDocForDb(final TxnTagsData txnTagsData) {
		// cache get and then update
		log.warn("Input Received from tags source : {}", txnTagsData);
		TransformedTransactionHistoryDetail detail = iCacheDataDao.getDataFromCache(CacheKeyManager.getSelfKey(
				Math.toIntExact(txnTagsData.getStreamSource()), txnTagsData.getTxnId(), txnTagsData.getUserId()));

		List<TransformedTag> updatedTransformedTags = txnTagsData.getTags()
			.stream()
			.map(t -> new TransformedTag(t.getTagName(), TagTypeEnum.U.getTagTypeKey(), t.getUpdatedTime()))
			.collect(Collectors.toList());
		Set<String> tags = updatedTransformedTags.stream().map(TransformedTag::getTag).collect(Collectors.toSet());

		if (Objects.isNull(detail)) {
			log.warn("No record found in cache for tag updation : {}", txnTagsData);

			SearchFields searchFields = SearchFields.builder().searchTags(tags).build();
			detail = TransformedTransactionHistoryDetail.builder()
				.streamSource(Math.toIntExact(txnTagsData.getStreamSource()))
				.txnId(txnTagsData.getTxnId())
				.entityId(txnTagsData.getUserId())
				.tags(updatedTransformedTags)
				.searchFields(searchFields)
				.txnDate(Long.valueOf(txnTagsData.getTxnDate()))
				.build();
		}
		else {
			// update in cache
			log.warn("Record found in cache for tag updation : {}, txn: {}", txnTagsData, detail);

			detail.setTags(updatedTransformedTags);
			SearchFields searchFields = Objects.isNull(detail.getSearchFields()) ? new SearchFields()
					: detail.getSearchFields();
			searchFields.setSearchTags(tags);
			iCacheDataDao.updateDocInCache(CacheKeyManager.getSelfKey(detail), detail);

			log.warn("Saved in cache with updated tag updation : {}, txn: {}", txnTagsData, detail);
		}
		detail.setDocUpdatedDate(new Date().getTime());
		log.warn("TTHD for tags update request : {}", detail);
		return detail;
	}

	@Override
	public void flatMap(final TxnTagsData txnTagsData, final Collector<TransformedTransactionHistoryDetail> collector)
			throws Exception {
		try {
			if (Objects.isNull(txnTagsData)) {
				return;
			}
			TransformedTransactionHistoryDetail detail = this.getUpdatedDocForDb(txnTagsData);
			collector.collect(detail);

			// Pushing Metrics for Tag saving in ES Delay
			metricsAgent.pushTimeDiff(DELAY_IN_STORING_TAG_IN_DB, txnTagsData.getUpdatedDate(),
					DateTimeUtility.currentTimeEpoch());
		}
		catch (Exception e) {
			log.error("Exception while processing the record. Exception :{}", CommonsUtility.exceptionFormatter(e));
			metricsAgent.incrementCount(TAG_EXCEPTION_COUNT, CLASS_NAME + TRANSFORMATION_NAME, RETRY_ABLE + FALSE);
		}
	}

}
