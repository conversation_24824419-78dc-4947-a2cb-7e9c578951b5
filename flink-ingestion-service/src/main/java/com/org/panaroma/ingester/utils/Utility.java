package com.org.panaroma.ingester.utils;

import static com.org.panaroma.commons.constants.BankDataConstants.IS_VIRTUAL_PARTICIPANT;
import static com.org.panaroma.commons.constants.BankDataConstants.IS_VIRTUAL_PARTICIPANT_ADDED;
import static com.org.panaroma.commons.constants.BankDataConstants.PAYTM_BANK_IFSC;
import static com.org.panaroma.commons.constants.BankDataConstants.PPBL_TS_IMPS_REPORT_CODES;
import static com.org.panaroma.commons.constants.BankDataConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.CommonConstants.IGNORED_PARTICIPANT;
import static com.org.panaroma.commons.constants.CommonConstants.UPI_BACK_FILLING_PIPELINE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_EXPIRE;
import static com.org.panaroma.commons.constants.WebConstants.MANDATE_TXN_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_PURPOSE;
import static com.org.panaroma.commons.dto.ClientStatusEnum.FAILURE;
import static com.org.panaroma.commons.utils.BankDataMapperUtility.FOUR;
import static com.org.panaroma.constants.Constants.TXN_PURPOSE;
import static com.org.panaroma.ingester.constants.Constants.GET_CONTEXT_MAP;
import static com.org.panaroma.ingester.constants.Constants.GET_IS_FOR_ES_SINK;
import static com.org.panaroma.ingester.constants.Constants.IS_CHAT_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.RETRY_PIPELINE_V2;
import static com.org.panaroma.ingester.constants.Constants.CHAT_BACK_FILLING_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RETRY_BACK_FILLING_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.CBS;
import static com.org.panaroma.ingester.constants.Constants.CBS_DATA_CONEVRTER_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.TS;
import static com.org.panaroma.ingester.constants.Constants.PG_RETRY_V2_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.IS_MERGED_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.PUSH_TIME_STAMP;
import static com.org.panaroma.ingester.constants.Constants.TRUE;
import static com.org.panaroma.ingester.constants.Constants.COLLECT_STATUS;
import static com.org.panaroma.ingester.constants.Constants.FAILURE_COLLECT_STATUSES;
import static com.org.panaroma.ingester.constants.Constants.P2M_TRANSACTION_TYPES;
import static com.org.panaroma.ingester.constants.Constants.STARTNG_DAY_TIME;
import static com.org.panaroma.ingester.constants.PipelineConstants.API_RESPONSE_CACHE_POPULATION_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.BACKFILLING_PIPELINE_NAME;
import static com.org.panaroma.ingester.constants.PipelineConstants.CACHE_UPDATER_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.CBS_RETRY_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.CBS_STREAM_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.CHAT_DATA_PUBLISH_API_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.DATA_AUDIT_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.DC_MAIN_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.DC_UTH_ENRICHER_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.ES_INSERT_UPDATE_EXECUTOR_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.MANDATE;
import static com.org.panaroma.ingester.constants.PipelineConstants.MANDATE_RETRY;
import static com.org.panaroma.ingester.constants.PipelineConstants.OMS;
import static com.org.panaroma.ingester.constants.PipelineConstants.OMS_REFUND;
import static com.org.panaroma.ingester.constants.PipelineConstants.PG_DATA_PUBLISH_API_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.PPBL_PG;
import static com.org.panaroma.ingester.constants.PipelineConstants.PROMO_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.PROMO_UPI_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.TOGGLE_VISIBILITY;
import static com.org.panaroma.ingester.constants.PipelineConstants.UPI_RECON_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.UPI_RELAY_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.UPI_V2;
import static com.org.panaroma.ingester.constants.PipelineConstants.USERID_FETHCER_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.USER_ANALYTICS_MONTH_AGG_CREATOR;
import static com.org.panaroma.ingester.constants.PipelineConstants.USER_SPEND_DOCS_CREATOR_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.VAN_PIPELINE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.FROM;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.SOURCE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.STATUS;
import static com.org.panaroma.ingester.constants.Constants.CART;
import static com.org.panaroma.ingester.constants.Constants.CHAT_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RECENT_TXN_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RECON_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.STATUS_RESOLVER;
import static com.org.panaroma.ingester.constants.Constants.TAGS_ENRICHER;
import static com.org.panaroma.ingester.constants.Constants.USER_IMAGE_URL;
import static com.org.panaroma.ingester.constants.Constants.UTH_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.LAST_DAY_TIME;
import static com.org.panaroma.ingester.constants.Constants.EVENT;
import static com.org.panaroma.ingester.constants.Constants.ORDER_EVENT;
import static com.org.panaroma.ingester.constants.Constants.XFER_RPT_CODES;
import static com.org.panaroma.ingester.constants.Constants.MINUS_ONE;
import static com.org.panaroma.ingester.constants.Constants.TXN_TYPE;
import static com.org.panaroma.ingester.constants.Constants.WHITE_LISTED_REPORT_CODES_FOR_PPBL_GROUPING;
import static com.org.panaroma.ingester.constants.Constants.MAIN_TXN_TYPE;
import static com.org.panaroma.ingester.constants.Constants.CHAT_UNIQUE_IDENTIFIER;
import static com.org.panaroma.ingester.constants.Constants.USER_BANK_LOGO_URL;
import static com.org.panaroma.ingester.constants.Constants.ACCT_NUM;
import static com.org.panaroma.ingester.constants.Constants.IFSC;
import static com.org.panaroma.ingester.constants.Constants.BENEF_ACCT_NUM;
import static com.org.panaroma.ingester.constants.Constants.BENEF_IFSC;
import static com.org.panaroma.ingester.constants.Constants.REMITTER_ACCT_NUM;
import static com.org.panaroma.ingester.constants.Constants.REMITTER_IFSC;
import static com.org.panaroma.ingester.constants.Constants.FREE_FIELD_9;
import static com.org.panaroma.ingester.constants.Constants.REPORT_CODES_PUSHED_TO_MERGED_DATA_ONLY_FOR_ACCREFNUM;
import static com.org.panaroma.ingester.constants.Constants.RETRY_FOR_PMS_CALL;
import static com.org.panaroma.ingester.constants.Constants.REPORT_CODES_TO_HIT_BENEFICIARY_FOR_ACCREFNUM;
import static com.org.panaroma.ingester.constants.Constants.REPORT_CODES_TO_HIT_PMS_FOR_ACCREFNUM;
import static com.org.panaroma.ingester.constants.Constants.REASON_FOR_RETRY;
import static com.org.panaroma.ingester.constants.Constants.RETRY_FROM;
import static com.org.panaroma.ingester.constants.Constants.PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.COUNT;
import static com.org.panaroma.ingester.constants.Constants.BANK_DATA;
import static com.org.panaroma.ingester.constants.Constants.UPI_DATA;
import static com.org.panaroma.ingester.constants.Constants.MOBILE_DATA;
import static com.org.panaroma.ingester.constants.Constants.CARD_DATA;
import static com.org.panaroma.ingester.constants.Constants.BLANK_STRING;
import static com.org.panaroma.ingester.constants.Constants.NULL_STRING;
import static com.org.panaroma.ingester.constants.Constants.Rrn;
import static com.org.panaroma.ingester.constants.Constants.RELATIVE_PRESENT;
import static com.org.panaroma.ingester.constants.Constants.IGNORE_DTOS_ARE_SAME;
import static com.org.panaroma.ingester.constants.Constants.COLLECT;
import static com.org.panaroma.ingester.constants.Constants.IS_INVALID_CUSTID;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.AccountTypeEnum;
import com.org.panaroma.commons.dto.BankData;
import com.org.panaroma.commons.dto.CardData;
import com.org.panaroma.commons.dto.CardType;
import com.org.panaroma.commons.dto.CategoryTags;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.DateTimeEnum;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.MerchantData;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.MobileData;
import com.org.panaroma.commons.dto.PayModeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.PostStitchData;
import com.org.panaroma.commons.dto.RepeatPaymentData;
import com.org.panaroma.commons.dto.TagType;
import com.org.panaroma.commons.dto.Tags;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.commons.dto.UPIData;
import com.org.panaroma.commons.dto.WalletData;
import com.org.panaroma.commons.dto.WalletIssuer;
import com.org.panaroma.commons.dto.WalletType;
import com.org.panaroma.commons.dto.es.TransformedBankData;
import com.org.panaroma.commons.dto.es.TransformedCardData;
import com.org.panaroma.commons.dto.es.TransformedGeoLocation;
import com.org.panaroma.commons.dto.es.TransformedLocation;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedRepeatPaymentData;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.commons.dto.spendDtos.kafka.UserMonthAggKafkaConfig;
import com.org.panaroma.commons.dto.spendDtos.kafka.UserSpendKafkaConfig;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.commons.utils.BankUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.IfscUtility;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.NumberFormatUtility;
import com.org.panaroma.ingester.configuration.elasticsearch.objects.EsReadConfigObject;
import com.org.panaroma.ingester.configuration.flink.FlinkProperties;
import com.org.panaroma.ingester.constants.Constants;
import com.org.panaroma.ingester.dto.UpiReconDto;
import com.org.panaroma.ingester.dto.VanDto;
import com.org.panaroma.ingester.dto.streamDTO.PromoServiceDto;
import com.org.panaroma.ingester.dto.streamDTO.PromoUpiServiceDto;
import com.org.panaroma.ingester.dto.streamDTO.PromoUthDto;
import com.org.panaroma.ingester.objects.RunArgumentsConfigObject;
import com.org.panaroma.ingester.objects.RunConfigKeyOptionsEnum;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;

import lombok.extern.log4j.Log4j2;
import org.apache.avro.generic.GenericDatumWriter;
import org.apache.avro.generic.GenericRecord;
import org.apache.avro.io.BinaryEncoder;
import org.apache.avro.io.DatumWriter;
import org.apache.avro.io.EncoderFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.flink.runtime.state.StateBackend;
import org.apache.flink.runtime.state.filesystem.FsStateBackend;
import org.apache.flink.streaming.api.environment.CheckpointConfig.ExternalizedCheckpointCleanup;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.joda.time.DateTime;
import tech.allegro.schema.json2avro.converter.JsonAvroConverter;

@Log4j2
public class Utility {

	private static ObjectMapper objectMapper;

	private static Gson gson;

	private static String activePipelineName;

	private static JsonAvroConverter jsonAvroConverter;

	private static List<Integer> txnTypesForSingleDoc = Arrays.asList();

	private static final List<String> SPECIAL_EQUALS_CHECK_FIELDS = Arrays.asList(GET_CONTEXT_MAP, GET_IS_FOR_ES_SINK);

	private static List<TransactionTypeEnum> p2pTransactionTypes = Arrays.asList(TransactionTypeEnum.P2P_OUTWARD,
			TransactionTypeEnum.P2P_OUTWARD_REVERSAL, TransactionTypeEnum.P2P_INWARD,
			TransactionTypeEnum.P2P_INWARD_3P_APP, TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD,
			TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD, TransactionTypeEnum.P2P2M, TransactionTypeEnum.P2P2M_REFUND,
			TransactionTypeEnum.P2P_INWARD_REVERSAL, TransactionTypeEnum.P2P2M_INWARD,
			TransactionTypeEnum.P2P2M_OUTWARD);

	/*
	 * In phase1 we are going with the floowing txnTypw with some conditions : cond1 : To
	 * avoid duplicacy we will send only one tthd(debit) when both user is on paytm. ex :
	 * UPI p2p, WALLET p2p, WALLET p2p2m
	 */
	private static List<Integer> supportedP2pTxnForChatTopic = Arrays.asList(
			TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey(),
			TransactionTypeEnum.P2P_INWARD.getTransactionTypeKey(),
			TransactionTypeEnum.P2P2M_INWARD.getTransactionTypeKey(),
			TransactionTypeEnum.P2P2M_OUTWARD.getTransactionTypeKey());

	private static List<Integer> supportedTxnTypesForChatTopic = Arrays.asList(
			TransactionTypeEnum.P2M.getTransactionTypeKey(), TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey(),
			TransactionTypeEnum.ADD_AND_PAY.getTransactionTypeKey(),
			TransactionTypeEnum.PPBL_TRANSACTION.getTransactionTypeKey(),
			TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.getTransactionTypeKey(),
			TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.getTransactionTypeKey());

	/**
	 * Create Run time argument object from supplied key value pair.
	 * @param keyValuePairs (dataType: array of String)
	 * @return
	 */
	public static RunArgumentsConfigObject getRunArguments(final String[] keyValuePairs) {
		Map<String, String> map = new HashMap<>();
		for (String pair : keyValuePairs) {
			String[] entry = pair.split("="); // split the pairs to get key and value
			map.put(entry[0].trim(), entry[1].trim()); // add them to the hashmap and trim
			// whitespaces
		}
		String pipelineName = map.get(RunConfigKeyOptionsEnum.PIPELINE_NAME.getKeyName());
		activePipelineName = pipelineName;
		String profile = map.get(RunConfigKeyOptionsEnum.PROFILE.getKeyName());
		return new RunArgumentsConfigObject(profile, pipelineName);
	}

	/***
	 * this will retrun the active pipeline name. (UPI/PG/WALLET)
	 * @return
	 */
	public static String getActivePipelineName() {
		return activePipelineName;
	}

	/**
	 * Converts a generic record to the given class type.
	 * @param record - Generic Avro Record
	 * @param clazz - Class type to which avro record will be converted.
	 */
	public static <T> T convertAvroRecordToObject(final GenericRecord record, final Class<T> clazz) {
		try {
			ensureInitialized();
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			BinaryEncoder encoder = EncoderFactory.get().binaryEncoder(baos, null);
			DatumWriter<GenericRecord> datumWriter = new GenericDatumWriter<>(record.getSchema());
			datumWriter.write(record, encoder);
			encoder.flush();
			baos.flush();
			byte[] binaryJson = jsonAvroConverter.convertToJson(baos.toByteArray(), record.getSchema());
			return objectMapper.readValue(binaryJson, clazz);
		}
		catch (Exception e) {
			throw new RuntimeException("Avro record to Object conversion failed.");
		}
	}

	/*
	 * Always assign objectMapper in last other wise it has double checked locking problem
	 */
	private static void ensureInitialized() {
		if (objectMapper != null && jsonAvroConverter != null && gson != null) {
			return;
		}

		synchronized (Utility.class) {
			if (objectMapper == null) {
				objectMapper = new ObjectMapper();
			}
			if (gson == null) {
				gson = new Gson();
			}
			if (jsonAvroConverter == null) {
				jsonAvroConverter = new JsonAvroConverter();
			}
		}
	}

	public static String getMonthFromEpochDate(final String epochTimeStamp) {
		return com.org.panaroma.commons.utils.IndexUtility.getDateFromEpochTime(epochTimeStamp, "MM");
	}

	public static String getYearFromEpochDate(final String epochTimeStamp) {
		return com.org.panaroma.commons.utils.IndexUtility.getDateFromEpochTime(epochTimeStamp, "yyyy");
	}

	public static Map<DateTimeEnum, String> getDateTime(final String epochTimeStamp,
			final DateTimeEnum... dateFormats) {
		Map<DateTimeEnum, String> dateMap = new HashMap<>();
		long time = Long.parseLong(epochTimeStamp.length() == 10 ? epochTimeStamp + "000" : epochTimeStamp);
		SimpleDateFormat sdf = null;
		for (DateTimeEnum dateTimeEnum : dateFormats) {
			String dateFormat = dateTimeEnum.getDateFormat();
			if (StringUtils.isEmpty(epochTimeStamp)) {
				throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
			}
			else {
				sdf = new SimpleDateFormat(dateFormat);
				sdf.setTimeZone(TimeZone.getTimeZone(com.org.panaroma.commons.constants.Constants.IST));
				try {
					String parsedDate = sdf.format(new Date(time));
					dateMap.put(dateTimeEnum, parsedDate);
				}
				catch (Exception var6) {
					throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
				}
			}
		}
		return dateMap;
	}

	private static Long addTimeInSeconds(final Long epochTime, final Long seconds) {
		String epochTimeStamp = String.valueOf(epochTime);
		try {
			long time = Long.parseLong(epochTimeStamp.length() == 10 ? epochTimeStamp + "000" : epochTimeStamp);
			DateTime dt = new DateTime(time);
			return dt.plus(seconds * 1000).getMillis();
		}
		catch (Exception var6) {
			throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
		}
	}

	public static String getDateFromEpochTime(final String epochTimeStamp, final String passedDateFormat)
			throws IllegalArgumentException {
		String dateFormat = passedDateFormat;
		if (StringUtils.isEmpty(dateFormat)) {
			dateFormat = "yyyy-MM-dd HH:mm:ss";
		}

		if (StringUtils.isEmpty(epochTimeStamp)) {
			throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
		}
		else {
			SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);

			try {
				long time = Long.parseLong(epochTimeStamp.length() == 10 ? epochTimeStamp + "000" : epochTimeStamp);
				String parsedDate = sdf.format(new Date(time));
				return parsedDate;
			}
			catch (Exception var6) {
				throw new IllegalArgumentException("Invalid epoch time passed" + epochTimeStamp);
			}
		}
	}

	public static String getPurpose(final TransformedTransactionHistoryDetail detail) {
		if (detail != null && detail.getContextMap() != null
				&& !StringUtils.isEmpty(detail.getContextMap().get(TXN_PURPOSE))) {
			return detail.getContextMap().get(TXN_PURPOSE);
		}

		return null;
	}

	public static String getPurpose(final List<TransformedParticipant> participants,
			final Integer transactionIndicator) {
		for (TransformedParticipant participant : participants) {
			if (participant.getTxnIndicator().equals(transactionIndicator) && participant.getContextMap() != null) {
				return participant.getContextMap().get(TXN_PURPOSE);
			}
		}
		return null;
	}

	public static String getPurpose(final TransactionHistoryDetails detail) {
		if (detail != null && detail.getContextMap() != null
				&& !StringUtils.isEmpty(detail.getContextMap().get(TXN_PURPOSE))) {
			return detail.getContextMap().get(TXN_PURPOSE);
		}
		return null;
	}

	public static String getPurpose(final List<TxnParticipants> participants,
			final TransactionIndicator transactionIndicator) {
		for (TxnParticipants participant : participants) {
			if (participant != null && transactionIndicator.equals(participant.getTxnIndicator())
					&& participant.getContextMap() != null) {
				return participant.getContextMap().get(TXN_PURPOSE);
			}
		}
		return null;
	}

	public static TransactionHistoryDetails getTransactionHistoryDetailObject(
			final TransformedTransactionHistoryDetail detail) {
		TransactionHistoryDetails transactionHistoryDetails = new TransactionHistoryDetails();
		// need to change ES mapping for location.
		List<TxnParticipants> participants = null;
		if (detail.getParticipants() != null) {
			participants = new ArrayList<>();
			for (TransformedParticipant transformedParticipant : detail.getParticipants()) {
				TxnParticipants txnParticipant = getTxnParticipantsFromTransformedParticipant(transformedParticipant,
						detail);
				participants.add(txnParticipant);
			}
			if (participants.size() == 0) {
				// creating a default participant. because in case of pg, we create es
				// document when they send order details on the first leg.
				TxnParticipants txnParticipants = TxnParticipants.builder()
					.customerId(detail.getEntityId())
					.txnIndicator(TransactionIndicator.getTransactionIndicatorEnumByKey(detail.getTxnIndicator()))
					.txnDate(String.valueOf(detail.getTxnDate()))
					.updatedDate(String.valueOf(detail.getUpdatedDate()))
					.build();
				participants.add(txnParticipants);
			}
		}
		List<Tags> tags = null;
		if (detail.getTags() != null) {
			tags = new ArrayList<>();
			for (TransformedTag transformedTag : detail.getTags()) {
				tags.add(new Tags(transformedTag.getTag(), TagType.getTagTypeByKey(transformedTag.getTagType()),
						transformedTag.getCreatedDate()));
			}
		}
		transactionHistoryDetails = TransactionHistoryDetails.builder()
			.amount(detail.getAmount() == null ? null
					: Currency.getCurrencyAmountInHigherDenomination(detail.getAmount(), detail.getCurrency()))
			.currency(Currency.getCurrencyByKey(detail.getCurrency()))
			.contextMap(detail.getContextMap())
			.parentTxnId(detail.getParentTxnId())
			.participants(participants)
			.sourceSystem(TransactionSource.getTransactionSourceEnumByKey(detail.getSourceSystem()))
			.status(ClientStatusEnum.getStatusEnumByKey(detail.getStatus()))
			.sourceSystemId(detail.getSourceTxnId())
			.streamSource(TransactionSource.getTransactionSourceEnumByKey(detail.getStreamSource()))
			.systemId(detail.getTxnId())
			.tags(tags)
			.txnDate(String.valueOf(detail.getTxnDate()))
			.txnType(TransactionTypeEnum.getTransactionTypeEnumByKey(detail.getTxnType()))
			.updatedDate(String.valueOf(detail.getUpdatedDate()))
			.build();
		return transactionHistoryDetails;
	}

	private static TxnParticipants getTxnParticipantsFromTransformedParticipant(
			final TransformedParticipant transformedParticipant, final TransformedTransactionHistoryDetail detail) {
		BankData bankData = null;
		if (transformedParticipant.getBankData() != null) {
			bankData = BankData.builder()
				.bankName(transformedParticipant.getBankData().getBankName())
				.accNum(transformedParticipant.getBankData().getAccNumber())
				.ifsc(transformedParticipant.getBankData().getIfsc())
				.accType(AccountTypeEnum.getAccountTypeEnumByKey(transformedParticipant.getBankData().getAccountType()))
				.accRefNum(transformedParticipant.getBankData().getAccRefNum())
				.build();
		}
		CardData cardData = null;
		if (transformedParticipant.getCardData() != null) {
			cardData = CardData.builder()
				.bin(transformedParticipant.getCardData().getBin())
				.cardNum(transformedParticipant.getCardData().getCardNum())
				.cardNetwork(transformedParticipant.getCardData().getCardNetwork())
				.cardType(CardType.getCardTypeByKey(transformedParticipant.getCardData().getCardType()))
				.build();
		}
		UPIData upiData = null;
		if (transformedParticipant.getUpiData() != null) {
			upiData = UPIData.builder().vpa(transformedParticipant.getUpiData().getVpa()).build();
		}
		WalletData walletData = null;
		if (transformedParticipant.getWalletData() != null) {
			walletData = WalletData.builder()
				.walletIssuer(
						WalletIssuer.getWalletIssuerByKey(transformedParticipant.getWalletData().getWalletIssuer()))
				.walletMobileNumber(transformedParticipant.getWalletData().getWalletMobileNumber())
				.walletType(WalletType.getWalletTypeByKey(transformedParticipant.getWalletData().getWalletType()))
				.walletUserId(transformedParticipant.getWalletData().getWalletUserId())
				.closingBalance(transformedParticipant.getWalletData().getClosingBalance())
				.build();
		}
		MerchantData merchantData = null;
		if (transformedParticipant.getMerchantData() != null) {
			merchantData = MerchantData.builder()
				.merchantCategory(CategoryTags
					.getCategoryTagEnumByKey(transformedParticipant.getMerchantData().getMerchantCategory()))
				.merchantId(transformedParticipant.getMerchantData().getMerchantId())
				.merchantPayMode(
						PayModeEnum.getPayModeEnumByKey(transformedParticipant.getMerchantData().getMerchantPayMode()))
				.merchantType(MerchantTypeEnum
					.getMerchantTypeByKey(transformedParticipant.getMerchantData().getMerchantType()))
				.mccCode(transformedParticipant.getMerchantData().getMccCode())
				.merchantSubCategory(transformedParticipant.getMerchantData().getMerchantSubCategory())
				.build();
		}
		MobileData mobileData = null;
		if (transformedParticipant.getMobileData() != null) {
			mobileData = MobileData.builder()
				.mobileNumber(transformedParticipant.getMobileData().getMobileNumber())
				.build();
		}
		return TxnParticipants.builder()
			.amount(transformedParticipant.getAmount() == null ? null
					: Currency.getCurrencyAmountInHigherDenomination(transformedParticipant.getAmount(),
							transformedParticipant.getCurrency()))
			.bankData(bankData)
			.cardData(cardData)
			.mobileData(mobileData)
			.contextMap(transformedParticipant.getContextMap())
			.currency(Currency.getCurrencyByKey(transformedParticipant.getCurrency()))
			.customerId(EntityTypesEnum.USER.getEntityTypeKey().equals(transformedParticipant.getEntityType())
					? transformedParticipant.getEntityId() : null)
			.entityType(EntityTypesEnum.getEntityTypeEnumByKey(transformedParticipant.getEntityType()))
			.logoUrl(transformedParticipant.getLogoUrl())
			.name(transformedParticipant.getName())
			.paymentSystem(PaymentSystemEnum.getPaymentSystemEnumByKey(transformedParticipant.getPaymentSystem()))
			.paymentTxnId(transformedParticipant.getPaymentTxnId())
			.remarks(transformedParticipant.getRemarks())
			.status(ClientStatusEnum.getStatusEnumByKey(transformedParticipant.getStatus()))
			.txnDate(transformedParticipant.getTxnDate())
			.txnIndicator(
					TransactionIndicator.getTransactionIndicatorEnumByKey(transformedParticipant.getTxnIndicator()))
			.updatedDate(transformedParticipant.getUpdatedDate())
			.upiData(upiData)
			.walletData(walletData)
			.merchantData(merchantData)
			.build();
	}

	public static Set<TransformedTransactionHistoryDetail> removeDuplicateDoc(
			final Set<TransformedTransactionHistoryDetail> docs) {
		Map<String, String> map = new HashMap<>();
		Set<TransformedTransactionHistoryDetail> uniqueDocs = new HashSet<>();
		if (docs != null) {
			docs.forEach(doc -> {
				String docId = doc.docId();
				if (map.get(docId) == null && (Objects.isNull(doc.getContextMap())
						|| Objects.isNull(doc.getContextMap().get(IS_CHAT_DOCUMENT)))) {
					uniqueDocs.add(doc);
					map.put(docId, "true");
				}
				else if (Objects.nonNull(doc.getContextMap())
						&& Objects.nonNull(doc.getContextMap().get(IS_CHAT_DOCUMENT))) {
					uniqueDocs.add(doc);
				}
			});
		}
		return uniqueDocs;
	}

	/***
	 * This will check if the both object are same except the date.
	 */
	public static boolean checkIfObjectEqual(final TransformedTransactionHistoryDetail storedDto1,
			final TransformedTransactionHistoryDetail storedDto2) throws JsonProcessingException {
		ensureInitialized();
		if (storedDto1 == null && storedDto2 == null) {
			return true;
		}
		if (storedDto1 == null || storedDto2 == null) {
			return false;
		}

		String jsonStr1 = objectMapper.writeValueAsString(storedDto1);
		TransformedTransactionHistoryDetail dto1 = objectMapper.readValue(jsonStr1,
				TransformedTransactionHistoryDetail.class);

		String jsonStr2 = objectMapper.writeValueAsString(storedDto2);
		TransformedTransactionHistoryDetail dto2 = objectMapper.readValue(jsonStr2,
				TransformedTransactionHistoryDetail.class);

		dto1.setTxnDate(null);
		dto1.setUpdatedDate(null);
		dto2.setUpdatedDate(null);
		dto2.setTxnDate(null);

		// Setting null isNewDoc, Created and Updated Date to avoid write in ES
		dto1.setIsNewDoc(null);
		dto1.setDocUpdatedDate(null);
		dto1.setDocCreatedDate(null);
		dto1.setDocEndStatusDate(null);
		dto2.setIsNewDoc(null);
		dto2.setDocUpdatedDate(null);
		dto2.setDocCreatedDate(null);
		dto2.setDocEndStatusDate(null);
		dto1.setHash(0);
		dto2.setHash(0);
		dto1.setIs1stEvent(null);
		dto2.setIs1stEvent(null);

		dto1.getParticipants().forEach(participant -> {
			participant.setTxnDate(null);
			participant.setUpdatedDate(null);
		});

		dto2.getParticipants().forEach(participant -> {
			participant.setTxnDate(null);
			participant.setUpdatedDate(null);
		});

		return dto1.equals(dto2);

	}

	public static byte[] writeValueAsBytes(final Object value) throws JsonProcessingException {
		ensureInitialized();
		return objectMapper.writeValueAsBytes(value);
	}

	public static void setParallelism(final String pipelineName, final StreamExecutionEnvironment env,
			final FlinkProperties flinkProperties) {

		if (!flinkProperties.getParallelismEnabled()) {
			return;
		}

		env.setParallelism(Utility.getParallelismValue(pipelineName, flinkProperties));
	}

	// can use Utility.setParallelism to set the ParallelismValue
	@Deprecated
	public static int getParallelismValue(final String pipelineName, final FlinkProperties flinkParallelismProperties) {
		switch (pipelineName) {
			case "pg":
				return flinkParallelismProperties.getPgParallelismValue();
			case OMS:
				return flinkParallelismProperties.getOmsParallelismValue();
			case OMS_REFUND:
				return flinkParallelismProperties.getOmsRefundParallelismValue();
			case PPBL_PG:
				return flinkParallelismProperties.getPpblPgParallelismValue();
			case "wallet":
				return flinkParallelismProperties.getWalletParallelismValue();
			case "upi":
				return flinkParallelismProperties.getUpiParallelismValue();
			case UPI_V2:
				return flinkParallelismProperties.getUpiV2ParallelismValue();
			case "retry-pipeline":
				return flinkParallelismProperties.getRetryPipelineParallelismValue();
			case RETRY_PIPELINE_V2:
				return flinkParallelismProperties.getRetryPipelineV2ParallelismValue();
			case CHAT_BACK_FILLING_PIPELINE:
				return flinkParallelismProperties.getChatBackFillingParallelismValue();
			case UPI_BACK_FILLING_PIPELINE:
				return flinkParallelismProperties.getUpiBackFillingParallelismValue();
			case RETRY_BACK_FILLING_PIPELINE:
				return flinkParallelismProperties.getRetryBackFillingParallelismValue();
			case CBS:
				return flinkParallelismProperties.getCbsParallelismValue();
			case CBS_DATA_CONEVRTER_PIPELINE:
				return flinkParallelismProperties.getCbsConverterParallelismValue();
			case "marketplace":
				return 1;
			case TS:
				return flinkParallelismProperties.getTsParallelismValue();
			case PG_RETRY_V2_PIPELINE:
				return flinkParallelismProperties.getPgRetryV2ParallelismValue();
			case CART:
				return flinkParallelismProperties.getCartParallelismValue();
			case CHAT_PIPELINE:
				return flinkParallelismProperties.getChatParallelismValue();
			case RECENT_TXN_PIPELINE:
				return flinkParallelismProperties.getRecentTxnParallelismValue();
			case RECON_PIPELINE:
				return flinkParallelismProperties.getReconParallelismValue();
			case STATUS_RESOLVER:
				return flinkParallelismProperties.getStatusResolverParallelismValue();
			case USER_IMAGE_URL:
				return flinkParallelismProperties.getUserImageUrlParallelismValue();
			case UTH_PIPELINE:
				return flinkParallelismProperties.getUthParallelismValue();
			case PROMO_PIPELINE:
				return flinkParallelismProperties.getPromoParallelismValue();
			case CBS_STREAM_PIPELINE:
				return flinkParallelismProperties.getCbsStreamParallelismValue();
			case CBS_RETRY_PIPELINE:
				return flinkParallelismProperties.getCbsRetryParallelismValue();
			case VAN_PIPELINE:
				return flinkParallelismProperties.getVanParallelismValue();
			case TAGS_ENRICHER:
				return flinkParallelismProperties.getTagsEnricherParallelismValue();
			case CACHE_UPDATER_PIPELINE:
				return flinkParallelismProperties.getCacheUpdaterParallelismValue();
			case UPI_RECON_PIPELINE:
				return flinkParallelismProperties.getUpiReconParallelismValue();
			case USER_SPEND_DOCS_CREATOR_PIPELINE:
				return flinkParallelismProperties.getUserSpendDocsCreatorParallelismValue();
			case USER_ANALYTICS_MONTH_AGG_CREATOR:
				return flinkParallelismProperties.getUserAnalyticsMonthAggCreatorParallelismValue();
			case USERID_FETHCER_PIPELINE:
				return flinkParallelismProperties.getUserIdFetcherPipelineParallelismValue();
			case PROMO_UPI_PIPELINE:
				return flinkParallelismProperties.getPromoUpiPipelineParallelismValue();
			case DC_MAIN_PIPELINE:
				return flinkParallelismProperties.getDcMainPipelineParallelismValue();
			case DC_UTH_ENRICHER_PIPELINE:
				return flinkParallelismProperties.getDcUthEnricherPipelineParallelismValue();
			case ES_INSERT_UPDATE_EXECUTOR_PIPELINE:
				return flinkParallelismProperties.getEsInsertUpdateExecutorPipelineParallelismValue();
			case DATA_AUDIT_PIPELINE:
				return flinkParallelismProperties.getDataAuditPipelineParallelismValue();
			case CHAT_DATA_PUBLISH_API_PIPELINE:
				return flinkParallelismProperties.getChatDataPublishApiPipelineParallelismValue();
			case API_RESPONSE_CACHE_POPULATION_PIPELINE:
				return flinkParallelismProperties.getApiResponseCachePopulationPipelineParallelismValue();
			case PG_DATA_PUBLISH_API_PIPELINE:
				return flinkParallelismProperties.getPgDataPublishApiPipelineParallelismValue();
			case UPI_RELAY_PIPELINE:
				return flinkParallelismProperties.getUpiRelayPipelineParallelismValue();
			case MANDATE:
				return flinkParallelismProperties.getMandatePipelineParallelismValue();
			case MANDATE_RETRY:
				return flinkParallelismProperties.getMandateRetryPipelineParallelismValue();
			case BACKFILLING_PIPELINE_NAME:
				return flinkParallelismProperties.getBackFillingPipelineParallelismValue();
			case TOGGLE_VISIBILITY:
				return flinkParallelismProperties.getToggleVisibilityPipelineParallelismValue();
			default:
		}
		return flinkParallelismProperties.getDefaultParallelismValue();
	}

	public static String getKafkaKey(final Object elem) {
		String systemId = null;
		try {
			if (elem instanceof TransactionHistoryDetails) {
				ensureInitialized();
				TransactionHistoryDetails thd = objectMapper.convertValue(elem, TransactionHistoryDetails.class);
				switch (thd.getStreamSource()) {
					case PPBL:
						systemId = thd.getSystemId();
						return systemId;// TODO Recheck: or cust id???
					case UPI:
						return getCustomerId(thd);
					default:
				}
				systemId = thd.getSystemId();
				return systemId;
			}
			else if (elem instanceof TransformedTransactionHistoryDetail) {
				TransformedTransactionHistoryDetail tthd = (TransformedTransactionHistoryDetail) elem;
				TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getMainTxnType());
				if (P2M_TRANSACTION_TYPES.contains(txnType) && StringUtils.isNotBlank(tthd.getOrderId())) {
					return tthd.getOrderId();
				}
				return tthd.getTxnId();
			}
			else if (elem instanceof PostStitchData) {
				return ((PostStitchData) elem).getPayload().getTransactionId();
			}
			else if (elem instanceof PromoServiceDto) {
				return ((PromoServiceDto) elem).getCustId();
			}
			else if (elem instanceof PromoUpiServiceDto) {
				return String.valueOf(((PromoUpiServiceDto) elem).getPayerCustId());
			}
			else if (elem instanceof VanDto) {
				return String.valueOf(((VanDto) elem).getPayload().getTransactionId());
			}
			else if (elem instanceof CacheUpdaterKafkaDto) {
				return ((CacheUpdaterKafkaDto) elem).getEntityId();
			}
			else if (elem instanceof UpiReconDto) {
				return String.valueOf(((UpiReconDto) elem).getPayload().getTransactionId());
			}
			else if (elem instanceof UserSpendKafkaConfig) {
				return ((UserSpendKafkaConfig) elem).getUserId();
			}
			else if (elem instanceof UserMonthAggKafkaConfig) {
				return ((UserMonthAggKafkaConfig) elem).getUserId();
			}
			else if (elem instanceof PromoUthDto) {
				return ((PromoUthDto) elem).getReceiverCustId();
			}
			else if (elem instanceof MandateActivityData) {
				return ((MandateActivityData) elem).getUmn();
			}
		}
		catch (Exception e) {
			log.error("Exception while creating kafka key for id : {} , Exception :{}", systemId,
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	private static String getCustomerId(final TransactionHistoryDetails detail) {
		if (detail.getParticipants() != null) {
			for (TxnParticipants participant : detail.getParticipants()) {
				if (participant.getCustomerId() != null) {
					return participant.getCustomerId();
				}
			}
		}
		return null;
	}

	public static String getPreviosMonthFromEpochDate(final Long txnDate, final Integer integer) {
		int i = integer;
		i = i % 12;
		Integer requiredMonth = getPreviousMonthInInteger(txnDate, i);
		if (requiredMonth <= 9) {
			return "0" + requiredMonth;
		}
		else {
			return String.valueOf(requiredMonth);
		}
	}

	private static Integer getPreviousMonthInInteger(final Long txnDate, final Integer integer) {
		int i = integer;
		i = i % 12;
		Integer month = Integer
			.valueOf(com.org.panaroma.commons.utils.IndexUtility.getDateFromEpochTime(String.valueOf(txnDate), "MM"));
		Integer requiredMonth = (month - i) % 12;
		if (requiredMonth <= 0) {
			requiredMonth = requiredMonth + 12;
		}
		return requiredMonth;
	}

	public static String getPreviosMonthsYearFromEpochDate(final Long txnDate, final Integer integer) {
		int i = integer;
		Integer yearDiff = i / 12;
		i = i % 12;
		String currentYear = getYearFromEpochDate(String.valueOf(txnDate));
		Integer month = Integer
			.valueOf(com.org.panaroma.commons.utils.IndexUtility.getDateFromEpochTime(String.valueOf(txnDate), "MM"));
		Integer year = Integer.valueOf(currentYear);
		Integer requiredMonth = month - i;
		Integer requiredYear = year - yearDiff;
		if (requiredMonth <= 0) {
			requiredYear = requiredYear - 1;
		}
		else if (requiredMonth > 12) {
			requiredYear = requiredYear + (requiredMonth / 12);
		}
		String reqYear = String.valueOf(requiredYear);
		if (reqYear.length() < 4) {
			return "0" + reqYear;
		}
		return reqYear;
	}

	public static String[] getIndexNames(final Long txnDate, final String indexNamePrefix,
			final List<String> updatedIndexList) {
		Set<String> indexNames = new HashSet<>();
		String dateInString = null;
		try {
			dateInString = getDateFromEpochTime(String.valueOf(txnDate), null);
		}
		catch (Exception e) {
			log.error("Exception :{} while convering date.", CommonsUtility.exceptionFormatter(e));
		}
		indexNames.add(Utility.getUpdatedIndexName(indexNamePrefix, txnDate, updatedIndexList));
		String boundaryState = Utility.getBoundaryState(txnDate);
		long sixtyMinutesInSeconds = (long) (60 * 60);
		if (StringUtils.isNotEmpty(boundaryState)) {
			switch (boundaryState) {
				case STARTNG_DAY_TIME:
					log.warn("In STARTNG_DAY_TIME block for txnDate:{}, epochTxnDateTime:{}", dateInString, txnDate);
					indexNames.add(Utility.getUpdatedIndexName(indexNamePrefix,
							Utility.addTimeInSeconds(txnDate, -sixtyMinutesInSeconds), updatedIndexList));
					break;
				case LAST_DAY_TIME:
					Long currentEpoch = Instant.now().toEpochMilli();
					Long addedEpoch = Utility.addTimeInSeconds(txnDate, sixtyMinutesInSeconds);
					Long consideredEpoch = addedEpoch;
					if (currentEpoch < addedEpoch) {
						consideredEpoch = currentEpoch;
					}
					log.warn(
							"In LAST_DAY_TIME block for txnDate:{}, epochTxnDateTime:{}, currentEpoch:{}, consideredEpoch :{}",
							dateInString, txnDate, currentEpoch, consideredEpoch);
					indexNames.add(Utility.getUpdatedIndexName(indexNamePrefix, consideredEpoch, updatedIndexList));
					break;
				default:
			}
		}
		// log-optimisation
		log.debug("Index names on which search will be perform :{} for txnDate :{}", indexNames, txnDate);
		return indexNames.toArray(new String[0]);
	}

	public static String getBoundaryState(final Long txnDate) {
		Map<DateTimeEnum, String> dateMap = getDateTime(String.valueOf(txnDate), DateTimeEnum.year, DateTimeEnum.month,
				DateTimeEnum.date, DateTimeEnum.hour);
		YearMonth yearMonth = YearMonth.of(Integer.parseInt(dateMap.get(DateTimeEnum.year)),
				Integer.parseInt(dateMap.get(DateTimeEnum.month)));
		int totalMonthDays = yearMonth.lengthOfMonth();
		int date = Integer.parseInt(dateMap.get(DateTimeEnum.date));
		int hour = Integer.parseInt(dateMap.get(DateTimeEnum.hour));

		return IndexUtility.getBoundaryStateOnTxnDateAndDay(txnDate, date, hour, totalMonthDays);

	}

	public static String getInputRptCodeWithTxnIndicator(final TransformedTransactionHistoryDetail detail) {
		String reportCode = null;
		if (Objects.nonNull(detail) && detail.getContextMap() != null) {
			reportCode = detail.getContextMap().get(REPORT_CODE);
		}
		return reportCode;
	}

	public static String getNbin(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd)) {
			TransformedParticipant selfParticipant = com.org.panaroma.commons.utils.Utility.getSelfParticipant(tthd);
			if (Objects.nonNull(selfParticipant)) {
				Map<String, String> participantLevelContextMap = selfParticipant.getContextMap();
				if (Objects.nonNull(participantLevelContextMap)) {
					return participantLevelContextMap.get(FREE_FIELD_9);
				}
			}
		}
		return null;
	}

	public static void setCheckPointing(final StreamExecutionEnvironment env, final FlinkProperties flinkProperties,
			final String pipelineName) {
		if (!flinkProperties.getIsCheckPointingEnabled()) {
			log.info("Checkpointing is disabled now.");
			return;
		}
		StateBackend stateBackend = new FsStateBackend(flinkProperties.getCheckPointingUrl() + "/" + pipelineName);
		env.setStateBackend(stateBackend);
		env.enableCheckpointing(flinkProperties.getEnableCheckpointingInterval());
		env.getCheckpointConfig()
			.setMinPauseBetweenCheckpoints(flinkProperties.getMinPauseBetweenCheckpointsInterval());
		env.getCheckpointConfig().enableExternalizedCheckpoints(ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

	}

	private static boolean isSpecialBoundary(final Long epochTimeMiliseconds) {
		String stringVersion = epochTimeMiliseconds.toString();
		stringVersion = stringVersion.length() == 10 ? stringVersion + "000" : stringVersion;
		Long epochMillis = Long.valueOf(stringVersion);
		// 1610562600000L = 14th January 2020 00:00:00
		return epochMillis > 1610562600000L;
	}

	public static String getUpdatedIndexName(final String indexNamePrefix, final Long txnDate,
			final List<String> updatedIndexList) {
		Map<DateTimeEnum, String> dateMap = getDateTime(String.valueOf(txnDate), DateTimeEnum.year, DateTimeEnum.month,
				DateTimeEnum.date, DateTimeEnum.hour);
		String month = dateMap.get(DateTimeEnum.month);
		String year = dateMap.get(DateTimeEnum.year);
		String date = dateMap.get(DateTimeEnum.date);
		String suffix = IndexUtility.getSuffix(txnDate, Integer.parseInt(date));

		String indexName = indexNamePrefix + "-" + month + "-" + year + suffix;
		try {
			if (updatedIndexList.contains(indexName)) {
				indexName = indexNamePrefix + "v2-" + month + "-" + year + suffix;
			}
		}
		catch (Exception e) {
			log.error("Exception while getting updated indexName. upatedIndexList :{} Exception :{}", updatedIndexList,
					CommonsUtility.exceptionFormatter(e));
		}
		// log.warn("Updated IndexName : {} for month : {}, year : {}", indexName, month,
		// year);
		return indexName;
	}

	/***
	 * This method returns the type of event which PG event contains.
	 */
	public static List<String> getPaymentLegDetails(final TransformedTransactionHistoryDetail detail) {
		List<String> availablePaymentsLegs = new ArrayList<>();
		for (TransformedParticipant txnParticipants : detail.getParticipants()) {
			// Except the order level event, PG sends paymentTxnId at participant level.
			if (txnParticipants.getEntityType() != null && StringUtils.isNotBlank(txnParticipants.getPaymentTxnId())
					&& Boolean.FALSE.equals(Utility.isaVirtualParticipant(txnParticipants, detail))) {
				availablePaymentsLegs
					.add(EntityTypesEnum.getEntityTypeEnumByKey(txnParticipants.getEntityType()).getEntityTypeValue()
							+ " " + EVENT);
			}
		}
		// PG sends these information only in order level events.
		if (detail.getAmount() != null && detail.getStatus() != null) {
			availablePaymentsLegs.add(ORDER_EVENT);
		}
		return availablePaymentsLegs;
	}

	public static String getEventType(final TransactionHistoryDetails detail) {
		if (!TransactionSource.PG.equals(detail.getStreamSource())) {
			return "Full_Event";
		}

		for (TxnParticipants txnParticipants : detail.getParticipants()) {
			// Except the order level event, PG sends paymentTxnId at participant level.
			if (txnParticipants.getEntityType() != null && StringUtils.isNotBlank(txnParticipants.getPaymentTxnId())) {
				return txnParticipants.getEntityType() + " " + EVENT;
			}
		}
		// PG sends these information only in order level events.
		if (detail.getAmount() != null && detail.getStatus() != null) {
			return ORDER_EVENT;
		}
		return null;
	}

	// This function removes the Dtos present in mergedDtos which are also present in
	// stoedDtos and are exaclty same
	public static Set<TransformedTransactionHistoryDetail> extractOnlyUpdatedDtosFromGrpdDtos(
			final Set<TransformedTransactionHistoryDetail> storedDtos,
			final Set<TransformedTransactionHistoryDetail> mergedDtos,
			final TransformedTransactionHistoryDetail enrichedDoc) throws Exception {
		ensureInitialized();
		if (mergedDtos == null) {
			return null;
		}
		if (storedDtos == null) {
			return mergedDtos;
		}

		Set<TransformedTransactionHistoryDetail> removedDuplicateDtos = new HashSet<>();
		removedDuplicateDtos.addAll(mergedDtos);
		for (TransformedTransactionHistoryDetail mergedDto : mergedDtos) {
			for (TransformedTransactionHistoryDetail storedDto : storedDtos) {
				if (mergedDto.getEntityId() != null && mergedDto.getEntityId().equals(storedDto.getEntityId())
						&& customEquals(storedDto, mergedDto)
						&& !mergedDto.getEntityId().equals(enrichedDoc.getEntityId())) {
					removedDuplicateDtos.remove(mergedDto);
					// storedDtosClone.remove(storedDto);
					break;
				}
			}
		}

		return removedDuplicateDtos;
	}

	// TODO : need to add visibility changes here as well
	public static boolean needToCreateSingleDoc(final TransformedTransactionHistoryDetail detail) {
		if (txnTypesForSingleDoc.contains(detail.getMainTxnType())) {
			TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(detail.getMainTxnType());
			if (txnType != null) {
				switch (txnType) {
					case PPBL_TRANSACTION:
						String reportCode = detail.getContextMap() == null ? null
								: detail.getContextMap().get(Constants.REPORT_CODE);
						if (XFER_RPT_CODES.contains(reportCode)) {
							return true;
						}
						break;
					default:
						break;
				}
			}
			return true;
		}
		return false;
	}

	public static boolean isXferTxn(final TransformedTransactionHistoryDetail detail) {
		String reportCode = detail.getContextMap() == null ? null : detail.getContextMap().get(Constants.REPORT_CODE);
		if (XFER_RPT_CODES.contains(reportCode)) {
			return true;
		}
		return false;
	}

	// need to update this customEqual function everyime when there is addition or removal
	// of any field in TransformedTransactionhistoryDetail
	private static boolean customEquals(final TransformedTransactionHistoryDetail stored,
			final TransformedTransactionHistoryDetail merged) throws Exception {
		if (merged == stored) {
			return true;
		}
		if (stored == null || merged == null) {
			return false;
		}
		Method[] methods = stored.getClass().getMethods();
		for (Method method : methods) {
			// Synthetic Check(Support For Jacoco)
			if (com.org.panaroma.commons.utils.Utility.skipMethod(method)) {
				continue;
			}
			if (method.getDeclaringClass().equals(stored.getClass()) && method.getName().startsWith("get")) {
				String methodName = method.getName();
				Object storedValue = method.invoke(stored);
				Object mergedValue = method.invoke(merged);
				if (SPECIAL_EQUALS_CHECK_FIELDS.contains(methodName)) {
					switch (methodName) {
						case GET_CONTEXT_MAP:
							if (!isContextMapEqual((Map<String, String>) storedValue,
									(Map<String, String>) mergedValue)) {
								return false;
							}
							break;
						default:
							log.info("Skipping methodName :{}  from equals check.", methodName);
							break;
					}
					continue;
				}
				if (!Objects.equals(storedValue, mergedValue)) {
					return false;
				}
			}
		}
		return true;
	}

	private static boolean isContextMapEqual(final Map<String, String> storedValue,
			final Map<String, String> mergedValue) {
		if (storedValue == null && mergedValue == null) {
			return true;
		}
		if ((storedValue == null && mergedValue != null) || (storedValue != null && mergedValue == null)) {
			return false;
		}
		for (String key : storedValue.keySet()) {
			if (!IS_CHAT_DOCUMENT.equals(key)) {
				if (!Objects.equals(storedValue.get(key), mergedValue.get(key))) {
					return false;
				}
			}
		}
		return true;
	}

	/***
	 * open for all : whiteListedFields = -1 open for selected values : whiteListedFields
	 * = 124,546 block for all : whiteListedFields = (empty array)
	 */
	public static Boolean isWhiteListingRequired(final String fieldValue, final List<String> whiteListedFields) {
		if ((whiteListedFields.contains(MINUS_ONE) && whiteListedFields.size() == 1)
				|| whiteListedFields.contains(fieldValue)) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	public static String[] getBasicTagsForMetrics(final TransformedTransactionHistoryDetail tthd) {
		return getBasicTagsListForMetrics(tthd).toArray(new String[0]);
	}

	public static List<String> getBasicTagsListForMetrics(final TransformedTransactionHistoryDetail tthd) {
		ArrayList<String> tags = new ArrayList();
		if (Objects.isNull(tthd)) {
			return tags;
		}

		// null check is handled where this method is called
		if (TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getMainTxnType()) == null
				|| TransactionSource.getTransactionSourceEnumByKey(tthd.getStreamSource()) == null) {
			log.warn("Invalid txnType or source for metrics basic tags: txntype: {}, txnSource: {}, txnId: {}",
					tthd.getTxnType(), tthd.getStreamSource(), tthd.getTxnId());
			return tags;

			// no data is required if the txnType or source is invalid
		}
		tags.add(
				TXN_TYPE + TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getMainTxnType()).getTransactionType());
		tags.add(SOURCE
				+ TransactionSource.getTransactionSourceEnumByKey(tthd.getStreamSource()).getTransactionSource());
		tags.add(STATUS + ClientStatusEnum.getStatusEnumByKey(tthd.getOriginalStatus()).getStatusValue());
		return tags;
	}

	public static String[] getMetricTagsForGetRequestServedForGroupingAndMerging(
			final TransformedTransactionHistoryDetail tthd, final String requestServedFrom) {
		List<String> tags = getBasicTagsListForMetrics(tthd);
		tags.add(FROM + requestServedFrom);
		return tags.toArray(new String[0]);
	}

	/*
	 * Method for checking that PPBL event is for TS Txn or not
	 */
	private static boolean isPpblEventForTsTxn(final TransformedTransactionHistoryDetail ppblTxn,
			final TransactionSource transactionSource) {
		if (TransactionSource.PPBL.equals(transactionSource)
				&& TransactionSource.TS.getTransactionSourceKey().equals(ppblTxn.getSourceSystem())
				&& ppblTxn.getContextMap() != null && ppblTxn.getContextMap().containsKey(REPORT_CODE)) {
			return WHITE_LISTED_REPORT_CODES_FOR_PPBL_GROUPING.contains(ppblTxn.getContextMap().get(REPORT_CODE));
		}
		return false;
	}

	public static boolean txnNeedsToSavedForParentOrNot(final TransformedTransactionHistoryDetail txn,
			final TransactionSource transactionSource) {

		if (TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey().equals(txn.getTxnType())) {
			return false;
		}
		// Checking Txn for Ts since SourceTxnId is Null in PPBL Txn for TS
		if (StringUtils.isNotBlank(txn.getSourceTxnId())) {
			return true;
		}
		return isPpblEventForTsTxn(txn, transactionSource);
	}

	/***
	 * This method update the txnType and mainTxnType for TS events.
	 *
	 */
	public static void updateTsDocTxnType(final TransactionHistoryDetails transactionHistoryDetails,
			final TxnParticipants txnParticipant) {
		if (transactionHistoryDetails == null || txnParticipant == null) {
			return;
		}
		if (!TransactionSource.TS.equals(transactionHistoryDetails.getStreamSource())) {
			return;
		}
		String reportCode = transactionHistoryDetails.getContextMap() != null
				? transactionHistoryDetails.getContextMap().get(Constants.REPORT_CODE) : null;
		if (PPBL_TS_IMPS_REPORT_CODES.contains(reportCode)) {
			return;
		}

		transactionHistoryDetails.setTxnType(BankUtility.getTxnType(reportCode,
				TransactionIndicator.CREDIT.equals(txnParticipant.getTxnIndicator()) ? "C" : "D", null));
		Map<String, String> contextMap = transactionHistoryDetails.getContextMap() == null ? new HashMap<>()
				: transactionHistoryDetails.getContextMap();
		contextMap.put(MAIN_TXN_TYPE, TransactionTypeEnum.PPBL_TRANSACTION.getTransactionType());
		transactionHistoryDetails.setContextMap(contextMap);

	}

	public static boolean isP2pTransaction(final TransformedTransactionHistoryDetail tthd) {
		if (p2pTransactionTypes.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()))) {
			return true;
		}
		return false;
	}

	public static boolean isP2pTransaction(final TransactionHistoryDetails thd) {
		if (p2pTransactionTypes.contains(thd.getTxnType())) {
			return true;
		}
		return false;
	}

	public static boolean areParticipantCustIdsEqual(final List<TxnParticipants> participants) {
		if (participants == null || participants.isEmpty()) {
			return false;
		}

		String creditCustId = null;
		String debitCustId = null;

		for (TxnParticipants participant : participants) {
			if (TransactionIndicator.CREDIT.equals(participant.getTxnIndicator())) {
				creditCustId = participant.getCustomerId();
			}
			else if (TransactionIndicator.DEBIT.equals(participant.getTxnIndicator())) {
				debitCustId = participant.getCustomerId();
			}
		}

		if (StringUtils.isNotEmpty(creditCustId) && StringUtils.isNotEmpty(debitCustId)
				&& creditCustId.equals(debitCustId)) {
			return true;
		}
		return false;
	}

	public static boolean isaVirtualParticipant(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail detail) {
		if (Objects.nonNull(participant) && Objects.nonNull(participant.getContextMap())
				&& Objects.nonNull(detail.getContextMap())
				&& detail.getContextMap().containsKey(IS_VIRTUAL_PARTICIPANT_ADDED)
				&& TRUE.equalsIgnoreCase(detail.getContextMap().get(IS_VIRTUAL_PARTICIPANT_ADDED))) {
			if (participant.getContextMap().containsKey(IS_VIRTUAL_PARTICIPANT)
					&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IS_VIRTUAL_PARTICIPANT))) {
				return true;
			}
		}
		return false;
	}

	public static boolean isaVirtualParticipant(final TxnParticipants participant,
			final TransactionHistoryDetails detail) {
		if (Objects.nonNull(participant) && Objects.nonNull(participant.getContextMap())
				&& Objects.nonNull(detail.getContextMap())
				&& detail.getContextMap().containsKey(IS_VIRTUAL_PARTICIPANT_ADDED)
				&& TRUE.equalsIgnoreCase(detail.getContextMap().get(IS_VIRTUAL_PARTICIPANT_ADDED))) {
			if (participant.getContextMap().containsKey(IS_VIRTUAL_PARTICIPANT)
					&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IS_VIRTUAL_PARTICIPANT))) {
				return true;
			}
		}
		return false;
	}

	public static boolean isValidParticipantForVisibilityCheck(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail detail) {
		if (isaVirtualParticipant(participant, detail)) {
			log.debug("Ignoring participant because it is Virtual Participant txnId: {}, paymentTxnId: {}",
					detail.getTxnId(), participant.getPaymentTxnId());
			return false;
		}

		// Ignoring wallet Debit Participant from Visibility checks
		if (TransactionSource.WALLET.getTransactionSourceKey().equals(detail.getStreamSource())
				&& TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(detail.getMainTxnType())
				&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
			log.debug("Ignoring add money wallet debit participant from visibility  txnId: {}, paymentTxnId: {}",
					detail.getTxnId(), participant.getPaymentTxnId());
			return false;
		}
		return true;
	}

	public static Map<Integer, List<Integer>> initUthCategorySetterMap(
			final Map<String, List<String>> inputUthCategorySetterMap) {
		if (inputUthCategorySetterMap == null) {
			log.error("uthCategorySetterMap in properties is null");
			throw new RuntimeException("uthCategorySetterMap in properties is null");
		}

		Map<Integer, List<Integer>> tempUthCategorySetterMap = new HashMap<>();
		try {
			setUthCategoryMap(tempUthCategorySetterMap, inputUthCategorySetterMap);
		}
		catch (Exception e) {
			throw e;
		}
		return tempUthCategorySetterMap;
	}

	private static void setUthCategoryMap(final Map<Integer, List<Integer>> tempUthCategorySetterMap,
			final Map<String, List<String>> inputUthCategorySetterMap) {
		for (String key : inputUthCategorySetterMap.keySet()) {
			TransactionSource source = null;
			if (TransactionSource.getTransactionSourceEnumByName(key) != null) {
				source = TransactionSource.getTransactionSourceEnumByName(key);
			}
			else {
				log.error(
						"source : {} , in uthCategorySetterMap is not present in " + "TransactionSource enum mappings",
						key);
				throw new RuntimeException("source : " + key + " , in uthCategorySetterMap is not present in "
						+ "TransactionSource enum mappings");
			}

			List<Integer> txnTypeList = new ArrayList<>();
			for (String txnType : inputUthCategorySetterMap.get(key)) {
				if (TransactionTypeEnum.getTransactionTypeEnumByName(txnType) != null) {
					txnTypeList.add(TransactionTypeEnum.getTransactionTypeEnumByName(txnType).getTransactionTypeKey());
				}
				else {
					log.error("txnType : {} added for source : {} , in uthCategorySetterMap is not present in "
							+ "txntypeEnum mappings", txnType, key);
					throw new RuntimeException("txnType : " + txnType + " added for source : " + key + " , in "
							+ "uthCategorySetterMap is not present in txntypeEnum mappings");
				}
			}
			tempUthCategorySetterMap.put(source.getTransactionSourceKey(), txnTypeList);
		}
	}

	public static boolean allowedToFetchUthCategory(final Map<Integer, List<Integer>> uthCategorySetterMap,
			final TransformedTransactionHistoryDetail enrichedDoc) {
		if (uthCategorySetterMap.containsKey(enrichedDoc.getStreamSource())
				&& uthCategorySetterMap.get(enrichedDoc.getStreamSource()).contains(enrichedDoc.getTxnType())) {
			return true;
		}
		return false;
	}

	public static boolean isThirdPartyUpiDoc(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd)
				&& TransactionSource.UPI.equals(TransactionSource.getTransactionSourceEnumByKey(tthd.getStreamSource()))
				&& Objects.isNull(tthd.getSourceSystem())) {
			return true;
		}
		return false;
	}

	public static void populateIsChatDocFlagInContextMap(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd)) {
			Map<String, String> contextMap = tthd.getContextMap();
			if (Objects.isNull(contextMap)) {
				contextMap = new HashMap<>();
			}
			contextMap.put(IS_CHAT_DOCUMENT, "true");
			if (tthd.getIsBankData() && PPBL_TS_IMPS_REPORT_CODES.contains(tthd.getContextMap().get(REPORT_CODE))
					&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(tthd.getTxnIndicator())) {
				contextMap.put(CHAT_UNIQUE_IDENTIFIER, String.format("%s|%s|%s", tthd.getContextMap().get("rrn"),
						tthd.getEntityId(), tthd.getContextMap().get("reportCode")));
			}
			tthd.setContextMap(contextMap);
		}
	}

	/*
	 * This we use to create logo which we send to chat where chat use them as ->
	 * "Sent to 'receiver_bank_logo' Bank" string for P2P_OUTWARD txns ->
	 * "Received in 'self_bank_logo' Bank" string for P2P_INWARD txns -> In case of p2m
	 * also where UPI is involved
	 */
	public static void populateBankLogoForUserParticipant(final TransformedTransactionHistoryDetail tthd,
			final String bankLogoBaseUrl) {
		/*
		 * get User Type Participant with payment system UPI create bank logo for that
		 * user set it in contextMap of the user at participant level
		 */
		if (TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource())
				|| TransactionSource.PG.getTransactionSourceKey().equals(tthd.getStreamSource())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				PaymentSystemEnum paymentSystem = PaymentSystemEnum
					.getPaymentSystemEnumByKey(participant.getPaymentSystem());
				if (EntityTypesEnum.USER.equals(EntityTypesEnum.getEntityTypeEnumByKey(tthd.getEntityType()))
						&& PaymentSystemEnum.UPI.equals(paymentSystem)) {
					// Using participant bank data to create Bank logo and set it in
					// contextMap
					setBankLogoInContextMap(participant, bankLogoBaseUrl);
				}
			}
		}
	}

	private static void setBankLogoInContextMap(final TransformedParticipant userUpiParticipant,
			final String bankLogoBaseUrl) {
		if (Objects.nonNull(userUpiParticipant.getBankData())) {

			String userBankLogo = LogoUtility.getBankLogo(userUpiParticipant.getBankData().getIfsc(),
					userUpiParticipant.getBankData().getBankName(), bankLogoBaseUrl);

			Map<String, String> contextMap = userUpiParticipant.getContextMap();
			if (Objects.isNull(contextMap)) {
				contextMap = new HashMap<>();
			}
			contextMap.put(USER_BANK_LOGO_URL, userBankLogo);
			userUpiParticipant.setContextMap(contextMap);
		}
	}

	public static Map<String, String> getOtherPartyAccountDetails(final TransformedTransactionHistoryDetail tthd) {
		if (TransactionSource.TS.getTransactionSourceKey().equals(tthd.getStreamSource())) {
			return getOtherPartyAccountDetailsTs(tthd);
		}
		else {
			return getOtherPartyAccountDetailsCbs(tthd);
		}
	}

	public static Map<String, String> getOtherPartyAccountDetailsTs(final TransformedTransactionHistoryDetail tthd) {
		TransformedParticipant otherParty = com.org.panaroma.commons.utils.Utility.getOtherParticipant(tthd);
		if (otherParty == null || otherParty.getBankData() == null) {
			return null;
		}
		Map<String, String> accountDetails = new HashMap<>();
		accountDetails.put(ACCT_NUM, otherParty.getBankData().getAccNumber());
		accountDetails.put(IFSC, otherParty.getBankData().getIfsc());
		return accountDetails;
	}

	public static Map<String, String> getOtherPartyAccountDetailsCbs(final TransformedTransactionHistoryDetail tthd) {
		String accNumKey;
		String ifscKey;
		if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(tthd.getTxnIndicator())) {
			accNumKey = BENEF_ACCT_NUM;
			ifscKey = BENEF_IFSC;
		}
		else {
			accNumKey = REMITTER_ACCT_NUM;
			ifscKey = REMITTER_IFSC;
		}
		TransformedParticipant selfParticipant = com.org.panaroma.commons.utils.Utility.getSelfParticipant(tthd);
		if (selfParticipant == null || selfParticipant.getContextMap() == null) {
			return null;
		}
		Map<String, String> participantLevelContextMap = selfParticipant.getContextMap();
		Map<String, String> accountDetails = new HashMap<>();
		accountDetails.put(ACCT_NUM, participantLevelContextMap.get(accNumKey));
		accountDetails.put(IFSC, participantLevelContextMap.get(ifscKey));
		return accountDetails;
	}

	public static boolean toBePushedToMergedDataTopicOnlyForAccRefNum(final TransformedTransactionHistoryDetail tthd) {
		return tthd.getIsVisible() && TransactionSource.PPBL.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& Objects.nonNull(tthd.getContextMap())
				&& REPORT_CODES_PUSHED_TO_MERGED_DATA_ONLY_FOR_ACCREFNUM.contains(tthd.getContextMap().get(REPORT_CODE))
				&& StringUtils.isBlank(tthd.getOtherPartyEntityId());
	}

	public static boolean isToBeSentToChat(final TransformedTransactionHistoryDetail tthd) {
		// we will send to chat only once even if event is retried in chat pipeline
		// multiple times
		/*
		 * return Objects.nonNull(contextMap) && contextMap.containsKey(IS_CHAT_DOCUMENT)
		 * && contextMap.get(IS_CHAT_DOCUMENT).equalsIgnoreCase(TRUE) &&
		 * (!contextMap.containsKey(getRetryCountFlagName(CHAT_PIPELINE)) ||
		 * Integer.parseInt(contextMap.get(getRetryCountFlagName(CHAT_PIPELINE))) == 1) &&
		 * ((supportedTxnTypesForChatTopic == null ||
		 * supportedTxnTypesForChatTopic.isEmpty() ||
		 * supportedTxnTypesForChatTopic.contains(tthd.getMainTxnType())) ||
		 * PPBL_TS_IMPS_REPORT_CODES.contains(contextMap.get(REPORT_CODE)));
		 */

		if (Objects.isNull(tthd) || ObjectUtils.isEmpty(tthd.getContextMap())
				|| Boolean.FALSE.equals(TRUE.equalsIgnoreCase(tthd.getContextMap().get(IS_CHAT_DOCUMENT)))) {
			log.warn("Document is not valid for sending to chat. txnId: {}", tthd.getTxnId());
			return false;
		}

		Map<String, String> contextMap = tthd.getContextMap();

		Integer retryCount = StringUtils.isBlank(tthd.getContextMap().get(getRetryCountFlagName(CHAT_PIPELINE))) ? 1
				: Integer.parseInt(tthd.getContextMap().get(getRetryCountFlagName(CHAT_PIPELINE)));

		// we will send to chat only once even if event is retried in chat pipeline
		// multiple times
		if (retryCount > 1) {
			return false;
		}

		boolean isToBeSentToChat = false;

		if (ObjectUtils.isEmpty(supportedTxnTypesForChatTopic)
				|| supportedTxnTypesForChatTopic.contains(tthd.getMainTxnType())) {
			log.warn("Document is valid for p2m sending to chat. txnId: {}", tthd.getTxnId());
			isToBeSentToChat = true;
		}
		else if (PPBL_TS_IMPS_REPORT_CODES.contains(contextMap.get(REPORT_CODE))) {
			isToBeSentToChat = true;
		}
		else if (Utility.isUpiLiteTxn(tthd) && Boolean.FALSE.equals(
				TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey().equals(tthd.getMainTxnType()))) {
			isToBeSentToChat = true;
		}
		else if (isValidP2pTxnforSending2Chat(tthd)) {
			log.warn("Document is valid for sending p2p to chat. txnId: {}", tthd.getTxnId());
			isToBeSentToChat = true;
		}

		return isToBeSentToChat;
	}

	private static boolean isValidP2pTxnforSending2Chat(final TransformedTransactionHistoryDetail tthd) {
		/*
		 * case 1 : Should not be walletInterOp UPI p2p Event Reason : Because here two
		 * diff doc is used UPI for one user and wallet for other user. hence 2 deeplink
		 * will be there to call detail page. which we are avoiding in phase 1.
		 *
		 * case 2 : Will not send Wallet event where txn happens from wallet to account.
		 * Reason : Chat don't have handling at their end for this txn.
		 *
		 * case 3 : Avoid passing CBS & TS Reason : Avoiding in phase 1
		 *
		 * case 4 : Will not send foreign inward remittance events
		 */

		if (UtilityExtension.isUpiFirTxn(tthd)) {
			return false;
		}
		else if (Utility.isWallet2AccountTxn(tthd)) {
			return false;
		}
		else if (TransactionSource.PPBL.getTransactionSourceKey().equals(tthd.getStreamSource())
				|| TransactionSource.TS.getTransactionSourceKey().equals(tthd.getStreamSource())) {
			return false;
		}
		else {
			return supportedP2pTxnForChatTopic.contains(tthd.getMainTxnType())
					&& Boolean.FALSE.equals(Utility.isDuplicateDataForChat(tthd));
		}
	}

	private static boolean isDuplicateDataForChat(final TransformedTransactionHistoryDetail tthd) {
		/*
		 * Duplicacy possible in following cases : case 1: VPA2VPA (paytm to paytm vpa),
		 * where we create two doc with p2p outward and inward txnType. so here we can
		 * avoid sending p2p inward to chat. case 2: P2P Transfer from wallet to wallet,
		 * so here we can avoid sending p2p inward to chat. case 3: P2P2M Transfer from
		 * wallet to wallet, so here we can avoid sending p2p2m inward to chat.
		 */
		return (TransactionTypeEnum.P2P_INWARD.getTransactionTypeKey().equals(tthd.getMainTxnType())
				|| TransactionTypeEnum.P2P2M_INWARD.getTransactionTypeKey().equals(tthd.getMainTxnType()))
				&& Utility.bothArePaytmUser(tthd);
	}

	public static boolean isForRetryInChat(final TransformedTransactionHistoryDetail tthd) {
		if (tthd.getContextMap() == null || tthd.getContextMap().get(RETRY_FOR_PMS_CALL) == null) {
			return false;
		}
		return tthd.getContextMap().get(RETRY_FOR_PMS_CALL).equalsIgnoreCase(TRUE);
	}

	public static boolean isToBeSentToMergedDataTopic(final TransformedTransactionHistoryDetail tthd) {
		Map<String, String> contextMap = tthd.getContextMap();
		if (Objects.nonNull(contextMap) && contextMap.containsKey(IS_CHAT_DOCUMENT)
				&& contextMap.get(IS_CHAT_DOCUMENT).equalsIgnoreCase(TRUE)) {
			return true;
		}
		return false;
	}

	public static boolean isMandateType(final TransformedTransactionHistoryDetail tthd) {
		return tthd.getMainTxnType().equals(TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey())
				|| tthd.getMainTxnType().equals(TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey())
				|| tthd.getMainTxnType().equals(TransactionTypeEnum.ONE_TIME_MANDATE.getTransactionTypeKey())
				|| tthd.getMainTxnType().equals(TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey());
	}

	public static boolean isValidDocToFetchAccRefNumFromThirdParty(final TransformedTransactionHistoryDetail tthd) {
		return Objects.nonNull(tthd) && tthd.getIsVisible()
				&& (TransactionSource.PPBL.getTransactionSourceKey().equals(tthd.getStreamSource())
						|| TransactionSource.TS.getTransactionSourceKey().equals(tthd.getStreamSource()))
				&& Objects.nonNull(tthd.getContextMap())
				&& (REPORT_CODES_TO_HIT_BENEFICIARY_FOR_ACCREFNUM.contains(tthd.getContextMap().get(REPORT_CODE))
						|| REPORT_CODES_TO_HIT_PMS_FOR_ACCREFNUM.contains(tthd.getContextMap().get(REPORT_CODE)))
				&& (StringUtils.isBlank(tthd.getOtherPartyEntityId())
						|| tthd.getOtherPartyEntityId().equalsIgnoreCase("null"));
	}

	public static boolean isUpiP2mRefund(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd)
				&& TransactionSource.UPI.equals(TransactionSource.getTransactionSourceEnumByKey(tthd.getStreamSource()))
				&& TransactionTypeEnum.P2M_REFUND
					.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getMainTxnType()))) {
			return true;
		}
		return false;
	}

	public static void populateErrorMessageContextMap(final TransformedTransactionHistoryDetail tDto) {
		List<TransformedParticipant> participants = tDto.getParticipants();
		if (Objects.nonNull(tDto.getContextMap()) && tDto.getContextMap().containsKey(WebConstants.ERROR_MESSAGE)
				&& !StringUtils.isBlank(tDto.getContextMap().get(WebConstants.ERROR_MESSAGE))) {
			return;
		}
		if (Objects.isNull(participants)) {
			return;
		}
		for (TransformedParticipant tp : participants) {
			if (Objects.isNull(tDto.getContextMap())) {
				tDto.setContextMap(new HashMap<>());
			}
			if (Objects.nonNull(tp.getContextMap()) && tp.getContextMap().containsKey(WebConstants.ERROR_MESSAGE)
					&& !StringUtils.isBlank(tp.getContextMap().get(WebConstants.ERROR_MESSAGE))) {
				String errorMessage = tp.getContextMap().get(WebConstants.ERROR_MESSAGE);
				tDto.getContextMap().put(WebConstants.ERROR_MESSAGE, errorMessage);
			}
		}
	}

	public static TransformedRepeatPaymentData createRepeatPaymentData(final TransactionHistoryDetails thd) {
		RepeatPaymentData repeatPaymentData = thd.getRepeatPaymentData();
		TransformedRepeatPaymentData transformedRepeatPaymentData = null;
		if (Objects.nonNull(repeatPaymentData)) {
			transformedRepeatPaymentData = new TransformedRepeatPaymentData();
			transformedRepeatPaymentData.setUrl(repeatPaymentData.getUrl());
			transformedRepeatPaymentData.setIsRepeatable(repeatPaymentData.getIsRepeatable());
		}
		return transformedRepeatPaymentData;
	}

	public static void setIgnoredParticipantField(final TransformedTransactionHistoryDetail tthd) {
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())
					&& !PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
				Map<String, String> contextMap = participant.getContextMap() == null ? new HashMap<>()
						: participant.getContextMap();
				contextMap.put(IGNORED_PARTICIPANT, TRUE);
				participant.setContextMap(contextMap);
			}
		}
	}

	/**
	 * Method to add merge flag if any merging happens in Doc at any UTH merger.
	 * @param tthd txn dto
	 */
	public static void setMergeFlag(final TransformedTransactionHistoryDetail tthd) {
		Map<String, String> contextMap = tthd.getContextMap();
		if (null == contextMap) {
			contextMap = new HashMap<>();
		}
		contextMap.put(IS_MERGED_DOCUMENT, "true");
		tthd.setContextMap(contextMap);
	}

	public static boolean isWalletInterOpTxn(final TransformedTransactionHistoryDetail txn) {
		return com.org.panaroma.commons.utils.Utility.isWalletInterOpTxn(txn);
	}

	public static TransformedParticipant getIgnoredParticipant(final TransformedTransactionHistoryDetail tthd) {
		if (tthd == null || ObjectUtils.isEmpty(tthd.getParticipants())) {
			return null;
		}

		TransformedParticipant ignoredParticipant = null;
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (participant.getContextMap() != null
					&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IGNORED_PARTICIPANT))) {
				ignoredParticipant = participant;
			}
		}
		return ignoredParticipant;
	}

	public static void setRetryCountAndReasonInTthdContextMap(final TransformedTransactionHistoryDetail tthd,
			final String pipelineName, final String reasonForRetry) {
		Integer retryCount = null;
		Map<String, String> contextMap = tthd.getContextMap() == null ? new HashMap<>() : tthd.getContextMap();
		String retryCountFlagName = getRetryCountFlagName(pipelineName);
		if (StringUtils.isNotBlank(contextMap.get(retryCountFlagName))) {
			retryCount = Integer.valueOf(contextMap.get(retryCountFlagName)) + 1;
		}
		else {
			retryCount = 1;
		}
		contextMap.put(retryCountFlagName, String.valueOf(retryCount));
		// not used as of now but may be used in future
		contextMap.put(REASON_FOR_RETRY, reasonForRetry);
		tthd.setContextMap(contextMap);
	}

	public static String getRetryCountFlagName(final String pipelineName) {
		StringBuilder retryCountFlagName = new StringBuilder();
		retryCountFlagName.append(RETRY_FROM).append(pipelineName).append(PIPELINE).append(COUNT);
		return retryCountFlagName.toString();
	}

	public static Boolean isForRetryInChatPipeline(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd)) {
			Map<String, String> contextMap = tthd.getContextMap();
			String retryCountFlagName = getRetryCountFlagName(CHAT_PIPELINE);
			if (Objects.nonNull(contextMap) && contextMap.containsKey(retryCountFlagName)) {
				return true;
			}
		}
		return false;
	}

	/*
	 * this function extracts txnDate from txnID based on below 2 logics: 1 yyyymmdd* 2
	 * yymmdd* Also we will extract txnDate for only cases where the txn order was created
	 * at max to last year have taken a delta of at max 2 years from the txndate to
	 * extract date
	 */
	public static Long getOrderTxnDateFromPgTxnId(final String pgTxnId, final long txnDate) {
		try {
			LocalDateTime currentDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(txnDate),
					TimeZone.getTimeZone("IST").toZoneId());
			LocalDateTime lastYearDate = currentDate.minusDays(365);
			LocalDateTime finalExtractDate = null;

			if (Boolean.FALSE.equals(NumberFormatUtility.isIntParsable(pgTxnId.substring(0, 4)))) {
				log.warn("PG TxnId is not Integer Parsable for fetching txn date from txnId {}", pgTxnId);
				return null;
			}
			int yyyyFormatYear = Integer.parseInt(pgTxnId.substring(0, 4));
			int yyFormatYear = Integer.parseInt(pgTxnId.substring(0, 2));
			// below function extracts date (if the txnId is of format yyyy) to which year
			// this txn belongs
			// to current year or last year and return that txnDate
			LocalDateTime yyyyMatchedDate = fetchYyyyMatchedDate(currentDate, lastYearDate, yyyyFormatYear);
			if (yyyyMatchedDate != null) {

				if (Boolean.FALSE.equals(NumberFormatUtility.isIntParsable(pgTxnId.substring(4, 6))
						&& NumberFormatUtility.isIntParsable(pgTxnId.substring(6, 8)))) {
					log.warn("PG TxnId is not Integer Parsable for fetching txn date from txnId: {}", pgTxnId);
					return null;
				}

				int month = Integer.parseInt(pgTxnId.substring(4, 6));
				int day = Integer.parseInt(pgTxnId.substring(6, 8));
				finalExtractDate = LocalDateTime.of(yyyyMatchedDate.getYear(), month, day, 0, 0);
				return finalExtractDate.atZone(TimeZone.getTimeZone("IST").toZoneId()).toInstant().toEpochMilli();
			}

			// below function extracts date (if the txnId is of format yy) to which year
			// this txn belongs
			// to current year or last year and return that txnDate
			LocalDateTime yyMatchedDate = fetchYyMatchedDate(currentDate, lastYearDate, yyFormatYear);
			if (yyMatchedDate != null) {

				if (Boolean.FALSE.equals(NumberFormatUtility.isIntParsable(pgTxnId.substring(2, 4))
						&& NumberFormatUtility.isIntParsable(pgTxnId.substring(4, 6)))) {
					log.warn("PG TxnId is not Integer Parsable for fetching txn date from txnId: {}", pgTxnId);
					return null;
				}

				int month = Integer.parseInt(pgTxnId.substring(2, 4));
				int day = Integer.parseInt(pgTxnId.substring(4, 6));
				finalExtractDate = LocalDateTime.of(yyMatchedDate.getYear(), month, day, 0, 0);
				return finalExtractDate.atZone(TimeZone.getTimeZone("IST").toZoneId()).toInstant().toEpochMilli();
			}
		}
		catch (Exception ex) {
			log.error("Exception while fetching txn date from PG txnId txnId: {}, Exception: {}", pgTxnId,
					CommonsUtility.exceptionFormatter(ex));
		}
		return null;
	}

	private static LocalDateTime fetchYyyyMatchedDate(final LocalDateTime currentDate, final LocalDateTime lastYearDate,
			final int year) {
		if (currentDate.getYear() == year) {
			return currentDate;
		}
		if (lastYearDate.getYear() == year) {
			return lastYearDate;
		}
		return null;
	}

	private static LocalDateTime fetchYyMatchedDate(final LocalDateTime currentDate, final LocalDateTime lastYearDate,
			final int year) {
		if (currentDate.getYear() % 100 == year) {
			return currentDate;
		}
		if (lastYearDate.getYear() % 100 == year) {
			return lastYearDate;
		}
		return null;
	}

	public static void setBankDataParamsForUpdate(final TransformedParticipant userParticipant,
			final Map<String, Object> parameters) throws JsonProcessingException {
		ensureInitialized();
		if (userParticipant != null && userParticipant.getBankData() != null && parameters != null) {
			String bankDataString = objectMapper.writeValueAsString(userParticipant.getBankData());
			parameters.put(BANK_DATA, objectMapper.readValue(bankDataString, HashMap.class));
		}
	}

	public static void setUpiDataParamsForUpdate(final TransformedParticipant userParticipant,
			final Map<String, Object> parameters) throws JsonProcessingException {
		ensureInitialized();
		if (userParticipant != null && userParticipant.getUpiData() != null && parameters != null) {
			String upiDataString = objectMapper.writeValueAsString(userParticipant.getUpiData());
			parameters.put(UPI_DATA, objectMapper.readValue(upiDataString, HashMap.class));
		}
	}

	public static void setMobileDataParamsForUpdate(final TransformedParticipant userParticipant,
			final Map<String, Object> parameters) throws JsonProcessingException {
		ensureInitialized();
		if (userParticipant != null && userParticipant.getMobileData() != null && parameters != null) {
			String mobileDataString = objectMapper.writeValueAsString(userParticipant.getMobileData());
			parameters.put(MOBILE_DATA, objectMapper.readValue(mobileDataString, HashMap.class));
		}
	}

	public static void setCardDataParamsForUpdate(final TransformedParticipant userParticipant,
			final Map<String, Object> parameters) throws JsonProcessingException {
		ensureInitialized();
		if (userParticipant != null && userParticipant.getCardData() != null && parameters != null) {
			String cardDataString = objectMapper.writeValueAsString(userParticipant.getCardData());
			parameters.put(CARD_DATA, objectMapper.readValue(cardDataString, HashMap.class));
		}
	}

	public static String getReportCode(final TransformedTransactionHistoryDetail tthd) {
		if (tthd.getContextMap() != null && tthd.getContextMap().containsKey(REPORT_CODE)) {
			return tthd.getContextMap().get(REPORT_CODE);
		}
		return null;
	}

	public static String getLastFourDigitsOfCard(final TransformedTransactionHistoryDetail tthd) {
		for (TransformedParticipant participant : tthd.getParticipants()) {
			TransformedCardData cardData = participant.getCardData();
			if (cardData != null && tthd.getTxnIndicator().equals(participant.getTxnIndicator())) {
				return cardData.getLastFourDigits();
			}
		}
		return null;
	}

	public static String getLastFourDigitsOfCard(final CardData cardData) {
		if (cardData.getLastFourDigits() != null) {
			return cardData.getLastFourDigits();
		}
		return extractLastFourDigits(cardData);
	}

	private static String extractLastFourDigits(final CardData cardData) {
		String cardNo = cardData.getCardNum();
		if (cardNo != null && cardNo.length() >= FOUR) {
			return cardNo.substring(cardNo.length() - FOUR);
		}
		return null;
	}

	public static TransformedTransactionHistoryDetail getClosestObjBasedOnTxnDate(
			final TransformedTransactionHistoryDetail tthd,
			final List<TransformedTransactionHistoryDetail> candidatesList) {
		long min = Long.MAX_VALUE;
		TransformedTransactionHistoryDetail detailObj = null;
		for (TransformedTransactionHistoryDetail candidate : candidatesList) {
			long absDiff = Math.abs(candidate.getTxnDate() - tthd.getTxnDate());
			if (absDiff < min) {
				min = absDiff;
				detailObj = candidate;
			}
		}
		return detailObj;
	}

	public static boolean isImps(final TransformedTransactionHistoryDetail transactionHistoryDetails) {
		if (transactionHistoryDetails == null || transactionHistoryDetails.getContextMap() == null
				|| transactionHistoryDetails.getContextMap().get("reportCode") == null) {
			return false;
		}
		return transactionHistoryDetails.getContextMap().get("reportCode").matches("20212|20211");
	}

	public static void handleTsNullEntityId(final TransactionHistoryDetails transactionHistoryDetails) {
		if (!TransactionSource.TS.equals(transactionHistoryDetails.getStreamSource())) {
			return;
		}
		transactionHistoryDetails.getParticipants().forEach(txnParticipants -> {
			if (NULL_STRING.equalsIgnoreCase(txnParticipants.getCustomerId())) {
				txnParticipants.setCustomerId(null);
			}
		});
	}

	public static void setBankDetailsForTs(final TransactionHistoryDetails transactionHistoryDetails) {
		if (!TransactionSource.TS.equals(transactionHistoryDetails.getStreamSource())) {
			return;
		}
		String rrn = null;
		if (transactionHistoryDetails.getContextMap() != null) {
			rrn = transactionHistoryDetails.getContextMap().get(Rrn);
			if (rrn != null && (rrn.equalsIgnoreCase(BLANK_STRING) || rrn.equalsIgnoreCase(NULL_STRING))) {
				rrn = null;
				transactionHistoryDetails.getContextMap().put(Rrn, rrn);
			}
		}

		for (TxnParticipants participant : transactionHistoryDetails.getParticipants()) {
			BankData bankData = participant.getBankData();
			if (bankData == null) {
				continue;
			}
			if (bankData.getBankName() == null && bankData.getIfsc() != null) {
				bankData.setBankName(IfscUtility.getBankName(bankData.getIfsc()));
			}
			if (rrn != null) {
				if (participant.getContextMap() == null) {
					participant.setContextMap(new HashMap<>());
				}
				participant.getContextMap().put(Rrn, rrn);
			}
		}
	}

	public static boolean isUpiLiteTxn(final TransformedTransactionHistoryDetail txn) {
		return com.org.panaroma.commons.utils.UpiLiteUtility.isUpiLiteTxn(txn);
	}

	public static boolean isUpiLiteTxnAndPaymentInstrument(final TransformedParticipant participant) {
		return com.org.panaroma.commons.utils.UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(participant);
	}

	public static Boolean isValidLocation(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd)) {
			return false;
		}
		if (Objects.isNull(tthd.getLocation())) {
			return false;
		}
		if (Objects.isNull(tthd.getLocation().getGeoLocation())) {
			return false;
		}
		if (Objects.isNull(tthd.getLocation().getGeoLocation().getLat())
				|| Objects.isNull(tthd.getLocation().getGeoLocation().getLon())) {
			return false;
		}
		return true;
	}

	public static boolean isUpiViaCcTxn(final TransformedTransactionHistoryDetail txn) {
		return com.org.panaroma.commons.utils.Utility.isUpiViaCcTxn(txn);
	}

	public static boolean isUpiViaCcTxn(final TransformedParticipant participant) {
		return com.org.panaroma.commons.utils.Utility.isUpiViaCcTxn(participant);
	}

	public static void addIsUpiViaCcTxnFlag(final TransformedTransactionHistoryDetail txn) {
		com.org.panaroma.commons.utils.Utility.addIsUpiViaCcTxnFlag(txn);
	}

	public static boolean bothArePaytmUser(final TransformedTransactionHistoryDetail txn) {
		return com.org.panaroma.commons.utils.UtilityExtension.bothArePaytmUser(txn);
	}

	public static boolean isWallet2AccountTxn(final TransformedTransactionHistoryDetail txn) {
		return com.org.panaroma.commons.utils.UtilityExtension.isWallet2AccountTxn(txn);
	}

	public static void removeNonRequiredFields(final TransformedTransactionHistoryDetail detail) {
		if (detail == null) {
			return;
		}
		if (detail.getContextMap() != null) {
			detail.getContextMap().remove(RELATIVE_PRESENT);
		}
		if (detail.getContextMap() != null) {
			detail.getContextMap().remove(IGNORE_DTOS_ARE_SAME);
		}
	}

	public static boolean isUpiP2mCollectTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& TransactionTypeEnum.P2M.getTransactionTypeKey().equals(txn.getTxnType())
				&& Objects.nonNull(txn.getContextMap()) && COLLECT
					.equalsIgnoreCase(txn.getContextMap().get(com.org.panaroma.ingester.constants.Constants.TXN_TYPE));
	}

	public static void setPushTimeStampInContextMap(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn)) {
			return;
		}

		try {
			Map<String, String> contextMap = Objects.isNull(txn.getContextMap()) ? new HashMap<>()
					: txn.getContextMap();

			Long timestamp = DateTimeUtility.currentTimeEpoch();

			contextMap.put(PUSH_TIME_STAMP, String.valueOf(timestamp));

			txn.setContextMap(contextMap);
		}
		catch (Exception ex) {
			log.error("Getting exception while setting push timestamp Exception: {}",
					CommonsUtility.exceptionFormatter(ex));
		}
	}

	public static Long getPushTimeStampFromContextMap(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getContextMap())) {
			return null;
		}

		String timeStamp = txn.getContextMap().get(PUSH_TIME_STAMP);

		if (NumberFormatUtility.isLongParsable(timeStamp)) {
			return Long.parseLong(timeStamp);
		}
		return null;
	}

	public static void populatePushTimeStampInMergedDoc(final Set<TransformedTransactionHistoryDetail> mergedDto,
			final TransformedTransactionHistoryDetail txn) {
		try {
			if (Objects.isNull(txn) || ObjectUtils.isEmpty(mergedDto)) {
				return;
			}

			if (ObjectUtils.isEmpty(txn.getContextMap()) || Boolean.FALSE
				.equals(NumberFormatUtility.isLongParsable(txn.getContextMap().get(PUSH_TIME_STAMP)))) {
				return;
			}

			for (TransformedTransactionHistoryDetail tthd : mergedDto) {
				if (isTxnDocIsMerged(txn)) {
					tthd.getContextMap().put(PUSH_TIME_STAMP, txn.getContextMap().get(PUSH_TIME_STAMP));
				}
			}
		}
		catch (Exception ex) {
			log.error("Exception while populating push time stamp Exception: {}",
					CommonsUtility.exceptionFormatter(ex));
		}
	}

	private static boolean isTxnDocIsMerged(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getContextMap())) {
			return false;
		}
		return TRUE.equalsIgnoreCase(txn.getContextMap().get(IS_MERGED_DOCUMENT));
	}

	public static void removePushTimeStampFields(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getContextMap())) {
			return;
		}
		txn.getContextMap().remove(PUSH_TIME_STAMP);
	}

	public static boolean isOrgKsUsedForTxn(final TransformedBankData bankData) {
		if (Objects.nonNull(bankData) && StringUtils.isNotEmpty(bankData.getBankName())) {
			String bankName = bankData.getBankName();
			return StringUtils.equalsIgnoreCase(IfscUtility.getBankNameWithCamelCase(PAYTM_BANK_IFSC), bankName);
		}
		return false;
	}

	public static boolean isValidParticipantForFindingChild(final TransformedParticipant transformedParticipant) {
		return Objects.nonNull(transformedParticipant) && (PaymentSystemEnum.UPI.getPaymentSystemKey()
			.equals(transformedParticipant.getPaymentSystem())
				|| (PaymentSystemEnum.BANK.getPaymentSystemKey().equals(transformedParticipant.getPaymentSystem())
						&& Utility.isOrgKsUsedForTxn(transformedParticipant.getBankData())));
	}

	public static boolean isRecurringMandateTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn.getContextMap()) && txn.getContextMap().containsKey(MANDATE_TXN_TYPE);
	}

	public static boolean isExpiredOrDeclinedTxn(final TransformedTransactionHistoryDetail transactionHistoryDetail) {
		if (StringUtils.isNotBlank(transactionHistoryDetail.getContextMap().get(COLLECT_STATUS))
				&& Objects.nonNull(transactionHistoryDetail.getStatus())) {
			return FAILURE_COLLECT_STATUSES.contains(transactionHistoryDetail.getContextMap().get(COLLECT_STATUS))
					&& FAILURE.getStatusKey().equals(transactionHistoryDetail.getStatus());
		}
		return false;
	}

	public static boolean isEsReadConfigAvailable(final Map<String, EsReadConfigObject> esReadConfigObjectMap,
			final String pipelineName) {
		return StringUtils.isNotBlank(pipelineName) && Objects.nonNull(esReadConfigObjectMap)
				&& Objects.nonNull(esReadConfigObjectMap.get(pipelineName));
	}

	public static boolean isEventOlderThanLimit(final Long txnDate, final Long maxAllowedDiff) {
		if (txnDate == null) {
			return true;
		}
		Long currentTime = new Date().getTime();
		Long timeDiff = currentTime - txnDate;
		return timeDiff > Duration.ofDays(maxAllowedDiff).toMillis();
	}

	public static boolean isEventOlderThanLimit(final Long txnDate, final Long maxAllowedDiff,
			final TransformedTransactionHistoryDetail tthd) {
		if (txnDate == null) {
			return true;
		}
		if (TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey().equals(tthd.getTxnType())
				&& Objects.nonNull(tthd.getContextMap())
				&& BackFillingIdentifierEnum.IPO_MANDATE_TXNS_BACKFILLING.getBackFillingIdentifierKey()
					.equals(tthd.getContextMap().get(BACKFILLING_IDENTIFIER))
				&& tthd.getTxnDate() >= 1711909800000L) {
			return false;
		}
		Long currentTime = new Date().getTime();
		Long timeDiff = currentTime - txnDate;
		return timeDiff > Duration.ofDays(maxAllowedDiff).toMillis();
	}

	// This method checks if the time interval between every cache update is elapsed or
	// not.
	public static boolean isCacheUpdateFixedIntervalElapsed(final Long currentTime, final Long lastUpdateTime,
			final Long updateInterval) {
		if (Objects.isNull(currentTime) || Objects.isNull(lastUpdateTime) || Objects.isNull(updateInterval)) {
			return false;
		}
		return (currentTime - lastUpdateTime) > updateInterval;
	}

	public static boolean isPgDataPublishRetryEvent(final TransactionHistoryDetails thd) {
		if (Objects.isNull(thd) || ObjectUtils.isEmpty(thd.getContextMap())) {
			return false;
		}

		return thd.getContextMap().containsKey("pgApiRetryCount")
				&& StringUtils.isNotBlank(thd.getContextMap().get("pgApiRetryCount"));
	}

	/**
	 * This function determines whether an event received from UPI should be discarded in
	 * the context of RECURRING_MANDATE transactions, specifically when the retryAttempt
	 * is greater than 0, the status is Failure, and the sourceSystem is not PG.
	 */
	public static boolean isFailureUpiEventToBeDiscardedForRecurringMandateTxns(final TransactionHistoryDetails data) {
		if (data == null) {
			return true;
		}

		Map<String, String> contextMap = data.getContextMap();
		if (data.getContextMap() == null || contextMap.get(WebConstants.MandateConstants.RETRY_ATTEMPT) == null) {
			// If "retryAttempt" value is missing, handle accordingly
			return false;
		}

		Integer retryAttempt;
		try {
			retryAttempt = Integer.valueOf(contextMap.get(WebConstants.MandateConstants.RETRY_ATTEMPT));
		}
		catch (NumberFormatException | NullPointerException e) {
			log.error("Error in converting retryAttempt");
			return false;
		}

		// Check for potential null values before using them in comparisons
		if ((TransactionTypeEnum.RECURRING_MANDATE.equals(data.getTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.equals(data.getTxnType()))
				&& Objects.isNull(data.getSourceSystem()) && StringUtils.isBlank(data.getSourceSystemId())
				&& !ClientStatusEnum.SUCCESS.equals(data.getStatus()) && retryAttempt > 0) {
			return true;
		}
		return false;
	}

	public static boolean isEligibleForEsSink(final TransformedTransactionHistoryDetail element,
			final Map<String, String> contextMap) {
		return element != null
				&& (contextMap == null || (contextMap != null && contextMap.get(IS_CHAT_DOCUMENT) == null)
						|| (Boolean.TRUE.equals(element.getIsForEsSink())));
	}

	public static Long getAmountInLowerDenomination(final String amount) {
		if (StringUtils.isBlank(amount)) {
			return null;
		}
		BigDecimal bigDecimalAmount = new BigDecimal(amount).multiply(new BigDecimal(100));
		return bigDecimalAmount.longValue();
	}

	public static String getOrderId(final Map<String, String> contextMap) {
		if (contextMap != null && StringUtils.isNotBlank(contextMap.get("merchantOrderId"))) {
			return contextMap.get("merchantOrderId");
		}
		return null;
	}

	public static void createLocation(final TransactionHistoryDetails thd,
			final TransformedTransactionHistoryDetail tDto) {
		TransformedLocation transformedLocation = new TransformedLocation();
		if (thd.getLocation() != null) {
			transformedLocation.setTimeZone(thd.getLocation().getTimeZone());
			if (NumberUtils.isParsable(thd.getLocation().getLatitude())
					&& NumberUtils.isParsable(thd.getLocation().getLongitude())) {
				TransformedGeoLocation transformedGeoLocation = new TransformedGeoLocation();
				BigDecimal lat = new BigDecimal(thd.getLocation().getLatitude()).setScale(3, RoundingMode.HALF_UP);
				BigDecimal lon = new BigDecimal(thd.getLocation().getLongitude()).setScale(3, RoundingMode.HALF_UP);
				if (isValidGeoLocation(lat, lon)) {
					transformedGeoLocation.setLat(lat.doubleValue());
					transformedGeoLocation.setLon(lon.doubleValue());
					transformedLocation.setGeoLocation(transformedGeoLocation);
					tDto.setLocation((com.org.panaroma.commons.utils.Utility
						.isDefaultLocationCoordinates(transformedGeoLocation)) ? null : transformedLocation);
				}
				else {
					// log-optimisation
					log.debug(
							"Values for location provided in request is invalid and not in permissible limit, location: {} for systemId: {}",
							thd.getLocation(), thd.getSystemId());
				}
			}
			else {
				// log-optimisation
				log.debug("Values for location provided in request is null or invalid and not parsable in number"
						+ " format location: {} for systemId: {}", thd.getLocation(), thd.getSystemId());
			}
		}
	}

	private static boolean isValidGeoLocation(final BigDecimal latitude, final BigDecimal longitude) {
		if (latitude != null && latitude.doubleValue() >= -90 && latitude.doubleValue() <= 90 && longitude != null
				&& longitude.doubleValue() >= -180 && longitude.doubleValue() <= 180) {
			return true;
		}
		return false;
	}

	public static boolean isInvalidCustIdEvent(final TransactionHistoryDetails thd) {
		if (thd == null || thd.getContextMap() == null) {
			return false;
		}
		return TRUE.equalsIgnoreCase(thd.getContextMap().get(IS_INVALID_CUSTID));
	}

	public static <T> T getCopy(final T object, final Class<T> T) {
		return gson.fromJson(gson.toJson(object), T);
	}

	/**
	 * Checks if the transaction is an expired event.
	 * @param transactionHistoryDetail The transaction detail to check.
	 * @return true if contextMap.TXN_TYPE == EXPIRE.
	 */
	public static boolean isMandateExpiredTxn(final TransformedTransactionHistoryDetail transactionHistoryDetail) {
		return Objects.nonNull(transactionHistoryDetail.getContextMap())
				&& PURPOSE_EXPIRE.equals(transactionHistoryDetail.getContextMap().get(TRANSACTION_PURPOSE));
	}

}