package com.org.panaroma.ingester;

import static com.org.panaroma.ingester.constants.Constants.IS_CHAT_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.TRUE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.PUSHED_FROM_RETRY_KAFKA_TO_CACHE_UPDATER;

import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.cache.OldestTxnUpdateCache;
import com.org.panaroma.commons.dto.cache.ZeroDeltaCache;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.ingester.constants.PipelineConstants;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class CacheUpdaterKafkaDtoGenerator
		implements FlatMapFunction<TransformedTransactionHistoryDetail, CacheUpdaterKafkaDto> {

	private Boolean isNtuCacheEnabled;

	private Utility utility;

	private MetricsAgent metricsAgent;

	@Autowired
	public CacheUpdaterKafkaDtoGenerator(final Utility utility,
			@Value("${isNtuCacheEnabled}") final Boolean isNtuCacheEnabled, final MetricsAgent metricsAgent) {
		this.utility = utility;
		this.isNtuCacheEnabled = isNtuCacheEnabled;
		this.metricsAgent = metricsAgent;
	}

	@Override
	// This flatMap returns an object to be pushed to Cache Updater Kafka corresponding to
	// TTHD
	public void flatMap(final TransformedTransactionHistoryDetail tthd, final Collector<CacheUpdaterKafkaDto> collector)
			throws Exception {

		if (tthd == null || (tthd.getContextMap() != null && TRUE.equals(tthd.getContextMap().get(IS_CHAT_DOCUMENT)))) {
			return;
		}

		if (Utility.isOnlyForGrouping(tthd)) {
			metricsAgent.incrementCount(PUSHED_FROM_RETRY_KAFKA_TO_CACHE_UPDATER);
		}
		log.warn(
				"TTHD received for generating cache updater kafka DTO for txnId : {}, entityId : {}, isVisible: {} and showInListing: {}",
				tthd.getTxnId(), tthd.getEntityId(), tthd.getIsVisible(), tthd.getShowInListing());

		if (StringUtils.isNotBlank(tthd.getEntityId())
				&& (Boolean.TRUE.equals(tthd.getIsVisible()) || Boolean.TRUE.equals(tthd.getShowInListing()))) {

			// below map will have all cache info to cache key mappings applicable on a
			// TTHD object irrespective of whitelisting checks
			Map<CacheInfo, String> listingCacheNameToCacheKeyMapping = Utility
				.getListingCacheNameToCacheKeyMappingApplicableOnTthd(tthd);

			if (listingCacheNameToCacheKeyMapping.containsKey(CacheInfo.UTH_NTU_CACHE)
					&& !(Boolean.TRUE.equals(isNtuCacheEnabled) && utility.checkIfWhiteListed(tthd.getEntityId(),
							CacheInfo.UTH_NTU_CACHE.getCacheName()))) {
				log.warn("NTU cache is not enabled or user is not whitelisted for txnId : {}, entityId : {}",
						tthd.getTxnId(), tthd.getEntityId());
				listingCacheNameToCacheKeyMapping.remove(CacheInfo.UTH_NTU_CACHE);
			}

			collector.collect(getCacheUpdaterKafkaDto(tthd, listingCacheNameToCacheKeyMapping));

		}
	}

	private CacheUpdaterKafkaDto getCacheUpdaterKafkaDto(final TransformedTransactionHistoryDetail tthd,
			final Map<CacheInfo, String> listingCacheNameToCacheKeyMapping) {
		if (Objects.isNull(listingCacheNameToCacheKeyMapping) || listingCacheNameToCacheKeyMapping.isEmpty()) {
			return null;
		}

		CacheUpdaterKafkaDto cacheUpdaterKafkaDto = new CacheUpdaterKafkaDto();
		cacheUpdaterKafkaDto.setUid(tthd.getTxnId());
		cacheUpdaterKafkaDto.setCacheMetaInfo(listingCacheNameToCacheKeyMapping);
		cacheUpdaterKafkaDto.setIsNonTransactingUser(Boolean.FALSE);
		cacheUpdaterKafkaDto.setEntityId(tthd.getEntityId());

		if (tthd.getDocUpdatedDate() != null) {
			AppSideCacheData appSideCacheData = new AppSideCacheData();
			appSideCacheData
				.setZeroDeltaCacheData(ZeroDeltaCache.builder().fromUpdatedDate(tthd.getDocUpdatedDate()).build());
			appSideCacheData.setOldestTxnUpdateCacheData(OldestTxnUpdateCache.builder()
				.docUpdatedDate(tthd.getDocUpdatedDate())
				.txnDate(tthd.getTxnDate())
				.build());

			cacheUpdaterKafkaDto.setAppSideCacheData(appSideCacheData);
		}
		else {
			log.error("Not setting App side cache in cacheUpdaterFlatmap, " + "as docUpdatedDate = null, for txn: {}",
					tthd);
		}
		cacheUpdaterKafkaDto.setOriginator(PipelineConstants.INGESTOR);
		Long currentTime = System.currentTimeMillis();
		cacheUpdaterKafkaDto.setCreatedDate(currentTime);
		cacheUpdaterKafkaDto.putInMetaInfo(PipelineConstants.TIME_WHEN_INVALIDATION_DTO_PUSHED, currentTime.toString());
		return cacheUpdaterKafkaDto;
	}

}
