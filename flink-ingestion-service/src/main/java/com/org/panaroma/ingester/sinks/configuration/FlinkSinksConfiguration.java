package com.org.panaroma.ingester.sinks.configuration;

import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.ingester.constants.Constants.DC_PIPELINES;
import static com.org.panaroma.ingester.constants.Constants.DETAIL_CACHE_INVALIDATOR_SINK;
import static com.org.panaroma.ingester.constants.Constants.ES_SINK_INSERT;
import static com.org.panaroma.ingester.constants.Constants.ES_SINK_UDIR;
import static com.org.panaroma.ingester.constants.Constants.ES_SINK_UPDATE;
import static com.org.panaroma.ingester.constants.Constants.EVENT_ALREADY_PRESENT_IN_ES;
import static com.org.panaroma.ingester.constants.Constants.IGNORE_DTOS_ARE_SAME;
import static com.org.panaroma.ingester.constants.Constants.IS_MERGED_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.STATUS_RESOLVER;
import static com.org.panaroma.ingester.constants.Constants.STRING_SERIALISED_PIPELINE_NAME;
import static com.org.panaroma.ingester.constants.Constants.TAGS_ENRICHER;
import static com.org.panaroma.ingester.constants.Constants.TRUE;
import static com.org.panaroma.ingester.constants.Constants.UPDATE_REQUEST_RETRY_COUNT;
import static com.org.panaroma.ingester.constants.Constants.USER_IMAGE_URL;
import static com.org.panaroma.ingester.constants.Constants.UTH_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.DC_UTH_ENRICHER_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.ES_INSERT_UPDATE_EXECUTOR_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.PG_DATA_PUBLISH_API_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.UDIR;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.ERROR_CREATING_INDEX_REQ;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.EXP_LAST_CAUSE;
import static com.org.panaroma.ingester.monitoring.MonitoringConstants.NOT_INDEX_REQUEST_OBJECT;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.cst.commons.dao.TransformedComplaintDetails;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.mandate.MandateActivityData;
import com.org.panaroma.commons.dto.spendDtos.kafka.UserSpendKafkaConfig;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.IndexUtility;
import com.org.panaroma.commons.utils.RepoUtility;
import com.org.panaroma.ingester.avro.AvroSerializationSchema;
import com.org.panaroma.ingester.configuration.elasticsearch.EsConfiguration;
import com.org.panaroma.ingester.configuration.elasticsearch.objects.EsTargetConfigurationObject;
import com.org.panaroma.ingester.configuration.kafka.KafkaConfiguration;
import com.org.panaroma.ingester.configuration.kafka.TargetKafkaConfig;
import com.org.panaroma.ingester.configuration.kafka.objects.KafkaSourceConfigurationObject;
import com.org.panaroma.ingester.configuration.kafka.objects.KafkaTargetConfigObject;
import com.org.panaroma.ingester.constants.Constants.KafkaSecurity;
import com.org.panaroma.ingester.enums.FlinkPipelineNameEnum;
import com.org.panaroma.ingester.enums.SerializationTypeEnum;
import com.org.panaroma.ingester.merger.BaseMerger;
import com.org.panaroma.ingester.merger.interfaces.IMergerFactory;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.repository.IRepository;
import com.org.panaroma.ingester.serializer.StringSerializationSchema;
import com.org.panaroma.ingester.sinks.objects.FlinkRetrySink;
import com.org.panaroma.ingester.sinks.objects.FlinkSink;
import com.org.panaroma.ingester.switchAdaptor.external.PromoUpiServiceExternalAdaptor;
import com.org.panaroma.ingester.utils.EsInsertUpdateInterceptorUtility;
import com.org.panaroma.ingester.utils.RetryUtility;
import com.org.panaroma.ingester.utils.Utility;
import io.confluent.kafka.serializers.KafkaAvroSerializer;
import io.confluent.kafka.serializers.KafkaAvroSerializerConfig;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.connectors.elasticsearch.ActionRequestFailureHandler;
import org.apache.flink.streaming.connectors.elasticsearch.ElasticsearchSinkFunction;
import org.apache.flink.streaming.connectors.elasticsearch.RequestIndexer;
import org.apache.flink.streaming.connectors.elasticsearch6.ElasticsearchSink;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.util.ExceptionUtils;
import org.apache.http.HttpHost;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.ElasticsearchParseException;
import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.Requests;
import org.elasticsearch.common.util.concurrent.EsRejectedExecutionException;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Log4j2
public class FlinkSinksConfiguration implements ISinksConfiguration<FlinkSink, FlinkRetrySink> {

	public static final String CHAT = "chat";

	public static final String INSERT = "insert";

	public static final String UPDATE = "update";

	public static final String GENERIC_INSERT = "generic-insert";

	private EsConfiguration esConfiguration;

	private RetryUtility retryUtility;

	private Integer bulkFlushActionValue;

	private Integer upiUdirBulkFlushActionValue;

	private Integer upiUdirBulkFlushIntervalValue;

	private TargetKafkaConfig targetKafkaConfig;

	private List<String> updatedIndexList;

	private Integer bulkFlushIntervalValue;

	private IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> esRepository;

	private IMergerFactory iMergerFactory;

	private List<String> whiteListedUserList;

	private Integer whiteListingRequiredForUpdateViaUthPipeline;

	private Double rollOutPercentageForUpdateViaUthPipeline;

	private KafkaConfiguration kafkaConfiguration;

	private ObjectMapper objectMapper;

	private MetricsAgent metricsAgent;

	private SpendEsFlinkSink spendEsFlinkSink;

	private EsInsertUpdateInterceptorUtility esInsertUpdateInterceptorUtility;

	private MandateEsFlinkSink mandateEsFlinkSink;

	@Autowired
	public FlinkSinksConfiguration(final EsConfiguration esConfiguration, final RetryUtility retryUtility,
			@Value("${es.bulk.flush.action.value}") final Integer bulkFlushActionValue,
			final TargetKafkaConfig targetKafkaConfig,
			@Value("#{'${updated.index.month.list}'.split(',')}") final List<String> updatedIndexList,
			@Value("${es.bulk.flush.interval.value}") final Integer bulkFlushIntervalValue,
			@Value("${upi.udir.es.bulk.flush.interval.value}") final Integer upiUdirBulkFlushIntervalValue,
			@Value("${upi.udir.es.bulk.flush.action.value}") final Integer upiUdirBulkFlushActionValue,
			final IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> esRepository,
			final IMergerFactory iMergerFactory,
			@Value("${white.listed.users.list}") final List<String> whiteListedUserList,
			@Value("${is.whitelisting.required.for.update.via.uth.pipeline}") final Integer whiteListingRequiredForUpdateViaUthPipeline,
			@Value("${rollOut.percent.for.update.via.uth.pipeline}") final Double rollOutPercentageForUpdateViaUthPipeline,
			final ObjectMapper objectMapper, final MetricsAgent metricsAgent,
			final KafkaConfiguration kafkaConfiguration, final SpendEsFlinkSink spendEsFlinkSink,
			final EsInsertUpdateInterceptorUtility esInsertUpdateInterceptorUtility,
			final MandateEsFlinkSink mandateEsFlinkSink) {
		this.esConfiguration = esConfiguration;
		this.retryUtility = retryUtility;
		this.bulkFlushActionValue = bulkFlushActionValue;
		this.targetKafkaConfig = targetKafkaConfig;
		this.updatedIndexList = updatedIndexList;
		this.bulkFlushIntervalValue = bulkFlushIntervalValue;
		this.upiUdirBulkFlushIntervalValue = upiUdirBulkFlushIntervalValue;
		this.upiUdirBulkFlushActionValue = upiUdirBulkFlushActionValue;
		this.esRepository = esRepository;
		this.iMergerFactory = iMergerFactory;
		this.whiteListedUserList = whiteListedUserList;
		this.whiteListingRequiredForUpdateViaUthPipeline = whiteListingRequiredForUpdateViaUthPipeline;
		this.rollOutPercentageForUpdateViaUthPipeline = rollOutPercentageForUpdateViaUthPipeline;
		this.objectMapper = objectMapper;
		this.metricsAgent = metricsAgent;
		this.kafkaConfiguration = kafkaConfiguration;
		this.spendEsFlinkSink = spendEsFlinkSink;
		this.esInsertUpdateInterceptorUtility = esInsertUpdateInterceptorUtility;
		this.mandateEsFlinkSink = mandateEsFlinkSink;
	}

	@Override
	@Bean
	public List<FlinkSink> getSinks() {
		// TODO : Need to add adaptors list for ES Sinks too
		List<FlinkSink> sinkList = esConfiguration.getTarget()
			.stream()
			.filter(esTargetConfigurationObject -> INSERT
				.equalsIgnoreCase(esTargetConfigurationObject.getRequestType()))
			.map(esTargetConfigurationObject -> new FlinkSink(esTargetConfigurationObject.getPipelineName(),
					esTargetConfigurationObject.getSwitchAdaptorsName(),
					this.createIndexSinkFunction(esTargetConfigurationObject)))
			.collect(Collectors.toList());
		sinkList.addAll(targetKafkaConfig.getTargetList()
			.stream()
			.map(kafkaTargetConfigObject -> new FlinkSink(kafkaTargetConfigObject.getPipelineName(),
					kafkaTargetConfigObject.getSwitchAdaptorsName(),
					this.createKafkaSinkFunction(kafkaTargetConfigObject)))
			.collect(Collectors.toList()));

		sinkList.addAll(esConfiguration.getTarget()
			.stream()
			.filter(esTargetConfigurationObject -> UPDATE
				.equalsIgnoreCase(esTargetConfigurationObject.getRequestType()))
			.map(esTargetConfigurationObject -> new FlinkSink(esTargetConfigurationObject.getPipelineName(),
					esTargetConfigurationObject.getSwitchAdaptorsName(),
					this.createUpdateSinkFunction(esTargetConfigurationObject)))
			.collect(Collectors.toList()));

		sinkList.addAll(esConfiguration.getTarget()
			.stream()
			.filter(esTargetConfigurationObject -> GENERIC_INSERT
				.equalsIgnoreCase(esTargetConfigurationObject.getRequestType()))
			.map(esTargetConfigurationObject -> new FlinkSink(esTargetConfigurationObject.getPipelineName(),
					esTargetConfigurationObject.getSwitchAdaptorsName(),
					this.createGenericIndexSinkFunction(esTargetConfigurationObject)))
			.collect(Collectors.toList()));

		return sinkList;
	}

	private SinkFunction createKafkaSinkFunction(final KafkaTargetConfigObject kafkaTargetConfigObject) {
		Properties producerProps = new Properties();
		producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaTargetConfigObject.getBootstrapServers());
		producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		if (STRING_SERIALISED_PIPELINE_NAME.contains(kafkaTargetConfigObject.getPipelineName())) {
			producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
		}
		else {
			producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class.getName());
			producerProps.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG,
					kafkaTargetConfigObject.getConfluentKafkaRegistryUrl());
			producerProps.put(KafkaAvroSerializerConfig.AUTO_REGISTER_SCHEMAS, "false");
		}
		producerProps.put(ProducerConfig.BATCH_SIZE_CONFIG, kafkaTargetConfigObject.getBatchSizeBytes());
		producerProps.put(ProducerConfig.LINGER_MS_CONFIG, kafkaTargetConfigObject.getLingerMs());
		producerProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, kafkaTargetConfigObject.getRequestTimeoutMs());
		producerProps.put(ProducerConfig.ACKS_CONFIG, "all");
		// Add Zstd compression
		producerProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "zstd");
		// Optional: Configure compression level (default is 3, range is -5 to 22)
		producerProps.put("compression.zstd.level", "3");
		producerProps.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 1024);
		producerProps.put(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG, kafkaTargetConfigObject.getTransactionTimeout());

		if (kafkaTargetConfigObject.isKafkaSecurityApplicable()) {
			producerProps.put(KafkaSecurity.SECURITY_PROTOCOL, kafkaTargetConfigObject.getSecurityProtocol());
			producerProps.put(KafkaSecurity.SASL_MECHANISM, kafkaTargetConfigObject.getSecuritySaslMechanism());
			producerProps.put(KafkaSecurity.SASL_JASS_CONFIG, kafkaTargetConfigObject.getSecuritySaslJaasConfig());
		}

		/***
		 * Semantic.NONE: Flink will not guarantee anything. Produced records can be lost
		 * or they can be duplicated. Semantic.AT_LEAST_ONCE (default setting): This
		 * guarantees that no records will be lost (although they can be duplicated).
		 * Semantic.EXACTLY_ONCE: Kafka transactions will be used to provide exactly-once
		 * semantic. Whenever you write to Kafka using transactions, do not forget about
		 * setting desired isolation.level (read_committed or read_uncommitted - the
		 * latter one is the default value) for any application consuming records from
		 * Kafka.
		 */
		FlinkKafkaProducer kafkaProducer = null;
		if (STRING_SERIALISED_PIPELINE_NAME != null && !STRING_SERIALISED_PIPELINE_NAME.isEmpty()
				&& STRING_SERIALISED_PIPELINE_NAME.contains(kafkaTargetConfigObject.getPipelineName())) {

			if (FlinkPipelineNameEnum.BACKFILLING.getPipelineName()
				.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(), MandateActivityData.class,
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
			}

			if (DETAIL_CACHE_INVALIDATOR_SINK.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(), CacheUpdaterKafkaDto.class,
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
				// TODO: Need to make it generic
			}
			else if (FlinkPipelineNameEnum.PROMO_PIPELINE.getPipelineName()
				.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(),
								FlinkPipelineNameEnum.PROMO_PIPELINE.getInputClazz(),
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
			}
			else if (FlinkPipelineNameEnum.CACHE_UPDATER_PIPELINE.getPipelineName()
				.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(),
								FlinkPipelineNameEnum.CACHE_UPDATER_PIPELINE.getInputClazz(),
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
			}
			else if (FlinkPipelineNameEnum.VAN_PIPELINE.getPipelineName()
				.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(),
								FlinkPipelineNameEnum.VAN_PIPELINE.getInputClazz(),
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
			}
			else if (FlinkPipelineNameEnum.UPI_RECON_PIPELINE.getPipelineName()
				.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(),
								FlinkPipelineNameEnum.UPI_RECON_PIPELINE.getInputClazz(),
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
			}
			else if (FlinkPipelineNameEnum.USER_SPEND_DOCS_CREATOR_PIPELINE.getPipelineName()
				.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(), UserSpendKafkaConfig.class,
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
			}
			else if (FlinkPipelineNameEnum.USERID_FETHCER_PIPELINE.getPipelineName()
				.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(),
								FlinkPipelineNameEnum.USERID_FETHCER_PIPELINE.getInputClazz(),
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
			}
			else if (FlinkPipelineNameEnum.PROMO_UPI_PIPELINE.getPipelineName()
				.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
				kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
						new StringSerializationSchema(kafkaTargetConfigObject.getTopic(),
								FlinkPipelineNameEnum.PROMO_UPI_PIPELINE.getInputClazz(),
								kafkaTargetConfigObject.getPipelineName()),
						producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
						kafkaTargetConfigObject.getProducersPoolSize());
			}
			else {

				// Todo: Need to make it generic
				// it's for PromoUpiServiceAdaptor
				if (ObjectUtils.isNotEmpty(kafkaTargetConfigObject.getSwitchAdaptorsName())
						&& kafkaTargetConfigObject.getSwitchAdaptorsName()
							.contains(PromoUpiServiceExternalAdaptor.switchName)) {
					kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
							new StringSerializationSchema(kafkaTargetConfigObject.getTopic(),
									PromoUpiServiceExternalAdaptor.outputClazz,
									kafkaTargetConfigObject.getPipelineName()),
							producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
							kafkaTargetConfigObject.getProducersPoolSize());
				}
				else {
					kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
							new StringSerializationSchema(kafkaTargetConfigObject.getTopic(),
									TransformedTransactionHistoryDetail.class,
									kafkaTargetConfigObject.getPipelineName()),
							producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
							kafkaTargetConfigObject.getProducersPoolSize());
				}

			}
		}
		else if (STATUS_RESOLVER.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())
				|| PG_DATA_PUBLISH_API_PIPELINE.equalsIgnoreCase(kafkaTargetConfigObject.getPipelineName())) {
			kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
					new AvroSerializationSchema(kafkaTargetConfigObject.getTopic(), TransactionHistoryDetails.class),
					producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
					kafkaTargetConfigObject.getProducersPoolSize());
		}
		else {
			kafkaProducer = new FlinkKafkaProducer(kafkaTargetConfigObject.getTopic(),
					new AvroSerializationSchema(kafkaTargetConfigObject.getTopic(),
							TransformedTransactionHistoryDetail.class),
					producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
					kafkaTargetConfigObject.getProducersPoolSize());
		}
		return kafkaProducer;
	}

	private SinkFunction createUpdateSinkFunction(final EsTargetConfigurationObject esTargetConfigurationObject) {

		List<HttpHost> httpHosts = null;
		try {
			httpHosts = Arrays.asList(RepoUtility.getHosts(esTargetConfigurationObject.getHosts(),
					esTargetConfigurationObject.getPort()));
			log.info("Host list : {} ", httpHosts);
		}
		catch (Exception e) {
			log.error("Exception while creation http hosts. Exception :{}", CommonsUtility.exceptionFormatter(e));
		}

		ElasticsearchSink.Builder<TransformedTransactionHistoryDetail> esSinkBuilder = new ElasticsearchSink.Builder<TransformedTransactionHistoryDetail>(
				httpHosts,
				new EsUpdateSinkFunction(esTargetConfigurationObject.getPipelineName(), esRepository, iMergerFactory,
						whiteListedUserList, whiteListingRequiredForUpdateViaUthPipeline,
						rollOutPercentageForUpdateViaUthPipeline, metricsAgent, esInsertUpdateInterceptorUtility));

		esSinkBuilder.setBulkFlushMaxActions(this.bulkFlushActionValue);
		esSinkBuilder.setBulkFlushInterval(this.bulkFlushIntervalValue);
		esSinkBuilder.setFailureHandler(new FlinkSinksConfiguration.UpdateCustomFailureHandler(metricsAgent,
				esTargetConfigurationObject.getPipelineName()));
		return esSinkBuilder.build();
	}

	private SinkFunction createIndexSinkFunction(final EsTargetConfigurationObject esTargetConfigurationObject) {

		if (FlinkPipelineNameEnum.USER_SPEND_DOCS_CREATOR_PIPELINE.getPipelineName()
			.equals(esTargetConfigurationObject.getPipelineName())) {
			return spendEsFlinkSink.createEsSinkFunction(esTargetConfigurationObject);
		}

		if (FlinkPipelineNameEnum.MANDATE.getPipelineName().equals(esTargetConfigurationObject.getPipelineName())
				|| FlinkPipelineNameEnum.MANDATE_RETRY.getPipelineName()
					.equals(esTargetConfigurationObject.getPipelineName())) {
			return mandateEsFlinkSink.createEsSinkFunction(esTargetConfigurationObject);
		}

		List<HttpHost> httpHosts = null;
		try {
			httpHosts = Arrays.asList(RepoUtility.getHosts(esTargetConfigurationObject.getHosts(),
					esTargetConfigurationObject.getPort()));
			log.info("Host list : {} ", httpHosts);
		}
		catch (Exception e) {
			log.error("Exception while creation http hosts. Exception :{}", CommonsUtility.exceptionFormatter(e));
		}

		ElasticsearchSink.Builder<TransformedTransactionHistoryDetail> esSinkBuilder = new ElasticsearchSink.Builder<TransformedTransactionHistoryDetail>(
				httpHosts, new EsIndexSinkFunction(esTargetConfigurationObject.getIndexName(), updatedIndexList,
						esInsertUpdateInterceptorUtility, esTargetConfigurationObject.getPipelineName()));

		esSinkBuilder.setBulkFlushMaxActions(this.bulkFlushActionValue);
		esSinkBuilder.setBulkFlushInterval(this.bulkFlushIntervalValue);
		esSinkBuilder.setFailureHandler(new FlinkSinksConfiguration.CustomFailureHandler(retryUtility, objectMapper,
				metricsAgent, esTargetConfigurationObject.getPipelineName()));
		return esSinkBuilder.build();
	}

	private SinkFunction createGenericIndexSinkFunction(final EsTargetConfigurationObject esTargetConfigurationObject) {

		List<HttpHost> httpHosts = null;
		try {
			httpHosts = Arrays.asList(RepoUtility.getHosts(esTargetConfigurationObject.getHosts(),
					esTargetConfigurationObject.getPort()));
			log.info("Host list : {} ", httpHosts);
		}
		catch (Exception e) {
			log.error("Exception while creation http hosts. Exception :{}", CommonsUtility.exceptionFormatter(e));
		}

		ElasticsearchSink.Builder esSinkBuilder = new ElasticsearchSink.Builder(httpHosts,
				new EsGenericIndexSinkFunction(esTargetConfigurationObject.getIndexName(), metricsAgent));
		esSinkBuilder.setBulkFlushMaxActions(this.upiUdirBulkFlushActionValue);
		esSinkBuilder.setBulkFlushInterval(this.upiUdirBulkFlushIntervalValue);
		esSinkBuilder.setFailureHandler(
				new FlinkSinksConfiguration.CustomComplaintFailureHandler(retryUtility, objectMapper, metricsAgent));
		return esSinkBuilder.build();
	}

	private static class EsGenericIndexSinkFunction implements ElasticsearchSinkFunction {

		private static final long serialVersionUID = 1L;

		private String indexPrefix;

		private MetricsAgent metricsAgent;

		public EsGenericIndexSinkFunction(final String indexNamePrefix, final MetricsAgent metricsAgent) {
			this.indexPrefix = indexNamePrefix;
			this.metricsAgent = metricsAgent;
		}

		private IndexRequest createIndexRequest(final TransformedComplaintDetails element)
				throws JsonProcessingException {
			byte[] json = Utility.writeValueAsBytes(element);
			// change time to UTC every time.
			String indexName = IndexUtility.getIndexNameForUpiUdirDoc(element.getOrgTxnDate(), indexPrefix);
			Long currentTime = new Date().getTime();
			IndexRequest indexRequest = Requests.indexRequest()
				.index(indexName)
				.type("_doc")
				.source(json, XContentType.JSON)
				.id(element.docId());

			if (element.isFirstEvent()) {
				// if firstevent is true set opType to create ,
				// as this will be required in concurrency control with two parallel 1st
				// events
				indexRequest.opType(DocWriteRequest.OpType.CREATE);
			}
			else {
				// if event is not a first event then set seqNo and primary term for
				// concurrency control
				indexRequest.setIfSeqNo(element.getSeqNo()).setIfPrimaryTerm(element.getPrimaryTerm());
			}

			// route with entityId
			indexRequest.routing(element.getEntityId());
			return indexRequest;
		}

		@Override
		public void process(final Object element, final RuntimeContext ctx, final RequestIndexer indexer) {
			if (element instanceof TransformedComplaintDetails) {
				TransformedComplaintDetails tcd = (TransformedComplaintDetails) element;
				try {
					if (tcd != null) {
						log.info("Sending event to ES insert : {}", tcd);
						indexer.add(createIndexRequest(tcd));
					}
				}
				catch (Exception e) {
					log.error("Exception: {} while pushing data into ES, So repushing data. SystemID:{}",
							CommonsUtility.exceptionFormatter(e), tcd.getOrgTxnId());
					metricsAgent.incrementCount(ERROR_CREATING_INDEX_REQ, EXP_LAST_CAUSE + CommonsUtility
						.exceptionFormatter(com.org.panaroma.commons.utils.Utility.getExceptionFinalCause(e)));
				}
			}

		}

	}

	private static class EsIndexSinkFunction implements ElasticsearchSinkFunction<TransformedTransactionHistoryDetail> {

		private static final long serialVersionUID = 1L;

		private String indexPrefix;

		private List<String> updateIndexList;

		private EsInsertUpdateInterceptorUtility esInsertUpdateInterceptorUtility;

		private String pipelineName;

		public EsIndexSinkFunction(final String indexNamePrefix, final List<String> updateIndexList,
				final EsInsertUpdateInterceptorUtility esInsertUpdateInterceptorUtility, final String pipelineName) {
			indexPrefix = indexNamePrefix;
			this.updateIndexList = updateIndexList;
			this.esInsertUpdateInterceptorUtility = esInsertUpdateInterceptorUtility;
			this.pipelineName = pipelineName;
		}

		private IndexRequest createIndexRequest(final TransformedTransactionHistoryDetail element)
				throws JsonProcessingException {
			element.setEsDocUpdatedDate(System.currentTimeMillis()); // setting in index
																		// request to have
																		// more accurate
																		// value set in ES
			// log.warn("Setting esDocUpdatedDate as {}, for txnId : {}",
			// element.getDocUpdatedDate(), element.getTxnId());
			// Removing push time stamp field from storing in ES
			Utility.removePushTimeStampFields(element);

			byte[] json = Utility.writeValueAsBytes(element);
			// change time to UTC every time.
			String indexName = getIndexName(element, indexPrefix, updateIndexList);
			Long currentTime = new Date().getTime();
			// log-optimisation
			log.debug(
					"Index Name: {},  StreamSource: {}, SystemId: {}, Time diff log : {}, updatedDate: {}, txnDate: {}, "
							+ "currentTime: {}",
					indexName, element.getStreamSource(), element.getTxnId(), currentTime - element.getUpdatedDate(),
					element.getUpdatedDate(), element.getTxnDate(), currentTime);
			IndexRequest indexRequest = Requests.indexRequest()
				.index(indexName)
				.type("_doc")
				.source(json, XContentType.JSON)
				.id(element.docId());
			// if(element.getVersion() != null) {
			// indexRequest.version(element.getVersion());
			// }
			indexRequest.routing(element.getEntityId());

			if (element.getIs1stEvent()) {
				// if IsNewDoc is true set opType to create,
				// to control overriding any already present event thinking this coming
				// event as the first event
				indexRequest.opType(DocWriteRequest.OpType.CREATE);
			}
			return indexRequest;
		}

		@Override
		public void process(final TransformedTransactionHistoryDetail element, final RuntimeContext ctx,
				final RequestIndexer indexer) {
			try {
				Map<String, String> contextMap = element == null ? null : element.getContextMap();
				if (Utility.isEligibleForEsSink(element, contextMap)) {
					log.info("Sending event to ES insert : {}", element);
					// removing unnecessary field from getting saved in ES
					Utility.removeNonRequiredFields(element);
					indexer.add(createIndexRequest(element));
				}
				/**
				 * If current pipeline is not
				 * ES_INSERT_UPDATE_EXECUTOR_PIPELINE,DC_PIPELINES, then push tthd to new
				 * kafka. for parallel write.
				 */
				if (Utility.isEligibleForEsSink(element, contextMap)
						&& !FlinkPipelineNameEnum.ES_INSERT_UPDATE_EXECUTOR_PIPELINE.getPipelineName()
							.equals(pipelineName)
						&& !DC_PIPELINES.contains(pipelineName)) {
					esInsertUpdateInterceptorUtility.pushUpdateOrInsertToKafka(element, "index");
				}
			}
			catch (Exception e) {
				log.error("Exception: {} while pushing data into ES, So repushing data. SystemID:{}",
						CommonsUtility.exceptionFormatter(e), element.getTxnId());
			}

		}

	}

	private static class EsUpdateSinkFunction
			implements ElasticsearchSinkFunction<TransformedTransactionHistoryDetail> {

		private static final long serialVersionUID = 1L;

		String pipelineName = null;

		IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> esRepository = null;

		private IMergerFactory iMergerFactory;

		private List<String> whiteListedUserList;

		private Integer whiteListingRequiredForUpdateViaUthPipeline;

		private Double rollOutPercentageForUpdateViaUthPipeline;

		private MetricsAgent metricsAgent;

		private EsInsertUpdateInterceptorUtility esInsertUpdateInterceptorUtility;

		public EsUpdateSinkFunction(final String pipelineName,
				final IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> esRepository,
				final IMergerFactory iMergerFactory, final List<String> whiteListedUserList,
				final Integer whiteListingRequiredForUpdateViaUthPipeline,
				final Double rollOutPercentageForUpdateViaUthPipeline, final MetricsAgent metricsAgent,
				final EsInsertUpdateInterceptorUtility esInsertUpdateInterceptorUtility) {
			this.pipelineName = pipelineName;
			this.esRepository = esRepository;
			this.iMergerFactory = iMergerFactory;
			this.whiteListedUserList = whiteListedUserList;
			this.whiteListingRequiredForUpdateViaUthPipeline = whiteListingRequiredForUpdateViaUthPipeline;
			this.rollOutPercentageForUpdateViaUthPipeline = rollOutPercentageForUpdateViaUthPipeline;
			this.metricsAgent = metricsAgent;
			this.esInsertUpdateInterceptorUtility = esInsertUpdateInterceptorUtility;
		}

		@Override
		public void process(final TransformedTransactionHistoryDetail element, final RuntimeContext runtimeContext,
				final RequestIndexer indexer) {
			try {
				element.setEsDocUpdatedDate(System.currentTimeMillis());
				/**
				 * sourcePipelineName :- pipeline from which this dto got pushed to
				 * ES_INSERT_UPDATE_EXECUTOR_PIPELINE. Will use this sourcePipelineName to
				 * create update requests.
				 */
				String sourcePipeline = pipelineName;

				// Push Kafka Write Timeout
				metricsAgent.pushTimeStampDelayMetrics("ES_WRITE_TIME_DIFF", element);

				// Removing unused fields
				Utility.removePushTimeStampFields(element);

				UpdateRequest updateRequest = null;
				Map<String, String> contextMap = element == null ? null : element.getContextMap();

				/**
				 * Get sourcePipelineName(pipeline from which this dto got pushed to
				 * ES_INSERT_UPDATE_EXECUTOR_PIPELINE) from dto. and remove this
				 * sourcePipelineName from dto. Will use this sourcePipelineName to create
				 * update requests.
				 */
				String srcPipelineName = esInsertUpdateInterceptorUtility.getAndRemoveSourcePipelineName(contextMap);
				if (StringUtils.isNotBlank(srcPipelineName)) {
					sourcePipeline = srcPipelineName;
				}

				if (element != null && checkMergedFlag(element)
						&& com.org.panaroma.commons.utils.Utility.isUserWhitelistedFromPercentageOrUserList(
								rollOutPercentageForUpdateViaUthPipeline, whiteListingRequiredForUpdateViaUthPipeline,
								whiteListedUserList, element)) {
					updateRequest = createUpdateRequest(element, sourcePipeline);

					// For Debugging UTH Pipeline issue
					if (ObjectUtils.isNotEmpty(whiteListedUserList)
							&& whiteListedUserList.contains(element.getEntityId())) {
						log.warn("Sending Merged Document for ES update : {}", element);
					}

					if (Objects.isNull(updateRequest)) {
						log.warn(
								"Update request created to update via uth-pipeline is null for txnId : {}"
										+ " , txnType : {} , streamSource : {} , sourceSystem : {}",
								element.getTxnId(), element.getMainTxnType(), element.getStreamSource(),
								element.getSourceSystem());
					}
					else {
						log.warn(
								"Update request created to update via uth-pipeline for txnId : {}"
										+ " , txnType : {} , streamSource : {} , sourceSystem : {}",
								element.getTxnId(), element.getMainTxnType(), element.getStreamSource(),
								element.getSourceSystem());
					}
				}
				if (element != null && (USER_IMAGE_URL.equalsIgnoreCase(sourcePipeline)
						|| TAGS_ENRICHER.equalsIgnoreCase(sourcePipeline))) {
					updateRequest = createUpdateRequest(element, sourcePipeline);
				}
				if (Objects.nonNull(updateRequest)) {
					updateRequest.retryOnConflict(2);
					indexer.add(updateRequest);
					log.info("Sending Document for ES update : {}", element);
				}

				/**
				 * If current pipeline is not
				 * ES_INSERT_UPDATE_EXECUTOR_PIPELINE,DC_PIPELINES, then push tthd to new
				 * kafka. for parallel write.
				 */
				if (!FlinkPipelineNameEnum.ES_INSERT_UPDATE_EXECUTOR_PIPELINE.getPipelineName().equals(pipelineName)
						&& updateRequest != null && !DC_PIPELINES.contains(pipelineName)) {
					esInsertUpdateInterceptorUtility.pushUpdateOrInsertToKafka(element, "update", pipelineName);
				}
			}
			catch (Exception e) {
				log.error("Exception: {} while pushing data into ES. SystemID:{}", CommonsUtility.exceptionFormatter(e),
						element.getTxnId());
			}
		}

		private boolean checkMergedFlag(final TransformedTransactionHistoryDetail element) {
			Map<String, String> contextMap = element == null ? null : element.getContextMap();
			if (contextMap != null && contextMap.containsKey(IS_MERGED_DOCUMENT)
					&& TRUE.equalsIgnoreCase(contextMap.get(IS_MERGED_DOCUMENT))) {
				return true;
			}
			else if (CollectionUtils.isNotEmpty(element.getRefundDetails())) {
				return true;
			}
			else if (Objects.nonNull(element.getParentDetails())) {
				return true;
			}

			return false;
		}

		private UpdateRequest createUpdateRequest(final TransformedTransactionHistoryDetail element,
				final String pipelineName) throws IOException {
			try {
				if (USER_IMAGE_URL.equalsIgnoreCase(pipelineName)) {
					return esRepository.createUserImageUpdateRequest(element);
				}
				if (TAGS_ENRICHER.equalsIgnoreCase(pipelineName)) {
					return esRepository.createUpdateRequestForTags(element);
				}
				if (UTH_PIPELINE.equalsIgnoreCase(pipelineName) || DC_UTH_ENRICHER_PIPELINE.equals(pipelineName)) {
					BaseMerger baseMerger = iMergerFactory
						.getMergerByType(TransactionTypeEnum.getTransactionTypeEnumByKey(element.getMainTxnType()));
					if (baseMerger != null) {
						return baseMerger.createUpdateRequest(element);
					}
				}
			}
			catch (Exception e) {
				log.error(
						"Some exception : {} occurred while creating update request for ES for txnId : {} and"
								+ " txnType : {}",
						CommonsUtility.exceptionFormatter(e), element.getTxnId(), element.getMainTxnType());
			}
			return null;
		}

	}

	private static class CustomFailureHandler implements ActionRequestFailureHandler {

		private RetryUtility retryUtility;

		private ObjectMapper objectMapper;

		private MetricsAgent metricsAgent;

		private String pipelineName;

		public CustomFailureHandler(final RetryUtility retryUtility, final ObjectMapper objectMapper,
				final MetricsAgent metricsAgent, final String pipelineName) {
			this.retryUtility = retryUtility;
			this.objectMapper = objectMapper;
			this.metricsAgent = metricsAgent;
			this.pipelineName = pipelineName;
		}

		@Override
		public void onFailure(final ActionRequest action, final Throwable failure, final int restStatusCode,
				final RequestIndexer indexer) {
			TransformedTransactionHistoryDetail tthd = getTthdObjectToBeIndexed(action);
			BackFillingIdentifierEnum backfillingIdentifier = null;
			if (Objects.nonNull(tthd.getContextMap()) && tthd.getContextMap().containsKey(BACKFILLING_IDENTIFIER)) {
				backfillingIdentifier = BackFillingIdentifierEnum
					.getBackFillingIdentifierEnumByKey(tthd.getContextMap().get(BACKFILLING_IDENTIFIER));
			}
			try {
				if (Objects.isNull(tthd)) {
					log.warn("Some failure : {} in ES sink", failure.getStackTrace());
				}
				else {
					if (ES_INSERT_UPDATE_EXECUTOR_PIPELINE.equals(pipelineName)) {
						if (tthd.getContextMap() == null) {
							tthd.setContextMap(new HashMap<>());
						}
						tthd.getContextMap().put(IGNORE_DTOS_ARE_SAME, TRUE);
					}
					metricsAgent.pushEsSinkExceptionMetrics(action, failure, ES_SINK_INSERT, pipelineName);
					if (ExceptionUtils.findThrowable(failure, EsRejectedExecutionException.class).isPresent()) {
						// full queue; re-add document for indexing
						log.error("Re-adding the document with txnId : {} in indexer for indexing. Exception : {}",
								tthd.getTxnId(), failure.getStackTrace());
						indexer.add(action);
					}
					else if (ExceptionUtils.findThrowable(failure, ElasticsearchParseException.class).isPresent()) {
						// malformed document; simply drop request without failing sink
						log.error("Document : {} can't be parsed. Exception : {}", tthd, failure.getStackTrace());
					}
					else if (ExceptionUtils.findThrowable(failure, ElasticsearchException.class).isPresent()) {
						String detailedMessage = ((ElasticsearchException) failure).getDetailedMessage();
						if (detailedMessage.contains("version_conflict_engine_exception")) {
							// ES document version changed in between, re push it to
							// Kafka.
							log.error("Version mismatch for document with txnId : {}.", tthd.getTxnId());

							if (tthd.getContextMap() == null) {
								tthd.setContextMap(new HashMap<>());
							}
							tthd.getContextMap().put(EVENT_ALREADY_PRESENT_IN_ES, TRUE);
							retryUtility.pushDataToKafka(tthd);
						}
						else {
							// All exceptions like circuit breaking exception will land
							// here
							log.error("Exception : {} with message : {} occurred for document with txnId : {}",
									failure.getStackTrace(), detailedMessage, tthd.getTxnId());
							retryUtility.pushDataToKafka(tthd);
						}
					}
					else {
						log.error(
								"backfillingIdentifier : {} Some error occurred while indexing data in ES sink. Pushing to retry kafka. Exception : {}",
								backfillingIdentifier,
								CommonsUtility.printExceptionWithoutStackTrace((Exception) failure));
						retryUtility.pushDataToKafka(tthd);
					}
				}
			}
			catch (Exception e) {
				log.error("Some unknown exception while sinking the data. Exception : {}",
						CommonsUtility.exceptionFormatter(e));
				retryUtility.pushDataToKafka(tthd);
			}
		}

		private TransformedTransactionHistoryDetail getTthdObjectToBeIndexed(final ActionRequest action) {
			try {
				if (action instanceof IndexRequest) {
					String jsonStr = ((IndexRequest) action).source().utf8ToString();
					return objectMapper.readValue(jsonStr, TransformedTransactionHistoryDetail.class);
				}
				else {
					log.warn("ActionRequest object : {} received for sinking to ES is not an instance of IndexRequest",
							action);
					metricsAgent.incrementCount(NOT_INDEX_REQUEST_OBJECT);
				}
			}
			catch (Exception e) {
				log.error("Exception while converting ActionRequest object : {} to TTHD", action);
			}
			return null;
		}

	}

	private static class CustomComplaintFailureHandler implements ActionRequestFailureHandler {

		private RetryUtility retryUtility;

		private ObjectMapper objectMapper;

		private MetricsAgent metricsAgent;

		public CustomComplaintFailureHandler(final RetryUtility retryUtility, final ObjectMapper objectMapper,
				final MetricsAgent metricsAgent) {
			this.retryUtility = retryUtility;
			this.objectMapper = objectMapper;
			this.metricsAgent = metricsAgent;
		}

		@Override
		public void onFailure(final ActionRequest action, final Throwable failure, final int restStatusCode,
				final RequestIndexer indexer) {
			TransformedComplaintDetails tcd = (TransformedComplaintDetails) getTthdObjectToBeIndexed(action,
					TransformedComplaintDetails.class);

			try {
				if (Objects.isNull(tcd)) {
					log.warn("Some failure : {} in ES sink", failure.getStackTrace());
				}
				else {
					metricsAgent.pushEsSinkExceptionMetrics(action, failure, ES_SINK_UDIR, tcd);
					if (ExceptionUtils.findThrowable(failure, EsRejectedExecutionException.class).isPresent()) {
						// full queue; re-add document for indexing
						log.error("Re-adding the document with txnId : {} in indexer for indexing. Exception : {}",
								tcd.getOrgTxnId(), failure.getStackTrace());
						indexer.add(action);
					}
					else if (ExceptionUtils.findThrowable(failure, ElasticsearchParseException.class).isPresent()) {
						// malformed document; simply drop request without failing sink
						log.error("Document : {} can't be parsed. Exception : {}", tcd, failure.getStackTrace());
					}
					else if (ExceptionUtils.findThrowable(failure, ElasticsearchException.class).isPresent()) {
						String detailedMessage = ((ElasticsearchException) failure).getDetailedMessage();
						if (detailedMessage.contains("version_conflict_engine_exception")) {
							// ES document version changed in between, re push it to
							// Kafka.
							log.error("Version mismatch for document with txnId : {}.", tcd.getOrgTxnId());
						}
						else {
							// All exceptions like circuit breaking exception will land
							// here
							log.error("Exception : {} with message : {} occurred for document with txnId : {}",
									failure.getStackTrace(), detailedMessage, tcd.getOrgTxnId());
							retryUtility.pushDataToKafka(UDIR, tcd);
						}
					}
					else {
						log.error(
								"Some error occurred while indexing data in ES sink. Pushing to retry kafka. Exception : {}",
								failure.getStackTrace());
						retryUtility.pushDataToKafka(UDIR, tcd);
					}
				}
			}
			catch (Exception e) {
				log.error("Some unknown exception while sinking the data. Exception : {}",
						CommonsUtility.exceptionFormatter(e));
				retryUtility.pushDataToKafka(UDIR, tcd);
			}
		}

		private Object getTthdObjectToBeIndexed(final ActionRequest action, final Class cls) {
			try {
				if (action instanceof IndexRequest) {
					String jsonStr = ((IndexRequest) action).source().utf8ToString();
					return objectMapper.readValue(jsonStr, cls);
				}
				else {
					log.warn("ActionRequest object : {} received for sinking to ES is not an instance of IndexRequest",
							action);
					metricsAgent.incrementCount(NOT_INDEX_REQUEST_OBJECT);
				}
			}
			catch (Exception e) {
				log.error("Exception while converting ActionRequest object : {} to TTHD", action);
			}
			return null;
		}

	}

	private static String getIndexName(final TransformedTransactionHistoryDetail element, final String indexPrefix,
			final List<String> updateIndexList) {
		return IndexUtility.getUpdatedIndexName(indexPrefix, element.getTxnDate(), updateIndexList);
	}

	private static class UpdateCustomFailureHandler implements ActionRequestFailureHandler {

		private MetricsAgent metricsAgent;

		private String pipelineName;

		public UpdateCustomFailureHandler(final MetricsAgent metricsAgent, final String pipelineName) {
			this.metricsAgent = metricsAgent;
			this.pipelineName = pipelineName;
		}

		@Override
		public void onFailure(final ActionRequest action, final Throwable failure, final int restStatusCode,
				final RequestIndexer indexer) {
			try {
				if (ExceptionUtils.findThrowable(failure, EsRejectedExecutionException.class).isPresent()) {
					// full queue; re-add document for indexing
					log.error("Document rejected by ES. pipeline : {} . docId : {}", Utility.getActivePipelineName(),
							((UpdateRequest) action).id());
					metricsAgent.pushEsSinkExceptionMetrics(action, failure, ES_SINK_UPDATE, pipelineName);
					indexer.add(action);
				}
				else if (ExceptionUtils.findThrowable(failure, ElasticsearchParseException.class).isPresent()) {
					// malformed document; simply drop request without failing sink
					log.error("Document can not be parsed. pipeline:{}. docId : {}", Utility.getActivePipelineName(),
							((UpdateRequest) action).id());
					metricsAgent.pushEsSinkExceptionMetrics(action, failure, ES_SINK_UPDATE, pipelineName);
				}
				else if (failure instanceof ElasticsearchException) {
					String detailedMessage = ((ElasticsearchException) failure).getDetailedMessage();
					if (detailedMessage.contains("document_missing_exception")) {
						log.warn(
								"Document_missing_exception : {} while updating doc in Es. , pipeline : {}, docId : {}",
								CommonsUtility.printExceptionWithoutStackTrace((ElasticsearchException) failure),
								Utility.getActivePipelineName(), ((UpdateRequest) action).id());
					}
					else {
						String jsonStr = "";
						if (action instanceof UpdateRequest && Objects.nonNull(((UpdateRequest) action).script())) {
							jsonStr = ((UpdateRequest) action).script().toString();
						}

						log.error(
								"Some exception :{} occurred while updating doc in Es . Request : {} , pipeline : {}, docId : {}",
								CommonsUtility.exceptionFormatter((ElasticsearchException) failure), jsonStr,
								Utility.getActivePipelineName(), ((UpdateRequest) action).id());
					}

					// Re-queuing update request
					UpdateRequest retryUpdateRequest = getUpdatedRequestForRetry(action, failure, pipelineName);
					if (Objects.nonNull(retryUpdateRequest)) {
						log.warn("Re-Queued update rquest for retry DocID: {}", ((UpdateRequest) action).id());
						indexer.add(retryUpdateRequest);
					}
				}
				else {
					log.error(
							"some unhandled exception :{} occurred while updating doc in Es in {} pipeline. docId : {}",
							failure, Utility.getActivePipelineName(), ((UpdateRequest) action).id());
					metricsAgent.pushEsSinkExceptionMetrics(action, failure, ES_SINK_UPDATE, pipelineName);
				}
			}
			catch (Exception e) {
				log.error("Some unknown exception while updating the data. Exception : {}",
						CommonsUtility.exceptionFormatter(e));
			}
		}

		private UpdateRequest getUpdatedRequestForRetry(final ActionRequest action, final Throwable failure,
				final String pipelineName) {
			if (!(action instanceof UpdateRequest)) {
				return null;
			}

			if (Objects.isNull(((UpdateRequest) action).script())) {
				metricsAgent.pushEsSinkExceptionMetrics(action, failure, ES_SINK_UPDATE, pipelineName);
				// not re queuing update Request for non script update request because of
				// infinite retry
				return null;
			}
			else {
				Map<String, Object> params = new HashMap<>(((UpdateRequest) action).script().getParams());

				Integer retryCount = (Integer) params.get(UPDATE_REQUEST_RETRY_COUNT);
				retryCount = retryCount == null ? 1 : retryCount + 1;

				params.put(UPDATE_REQUEST_RETRY_COUNT, retryCount);

				String scriptCodeStr = ((UpdateRequest) action).script().getIdOrCode();
				((UpdateRequest) action).script(new Script(ScriptType.INLINE, "painless", scriptCodeStr, params));

				metricsAgent.pushEsSinkExceptionMetrics(action, failure, ES_SINK_UPDATE, retryCount, pipelineName);

				// Not retrying update request if retryCount limit breached
				if (retryCount > 5) {
					log.error(
							"Not retrying this update request since retryCount for this is expired exception: {}, script: {}",
							CommonsUtility.exceptionFormatter((Exception) failure),
							((UpdateRequest) action).script().toString());
					return null;
				}
			}
			return (UpdateRequest) action;
		}

	}

	// Streaming changes
	@Override
	@Bean
	public List<FlinkRetrySink> getRetrySinks() {
		List<FlinkRetrySink> retrySinkList = null;
		if (kafkaConfiguration != null && !CollectionUtils.isEmpty(kafkaConfiguration.getSourceList())) {
			retrySinkList = new ArrayList<>();
			for (KafkaSourceConfigurationObject sourceConfig : kafkaConfiguration.getSourceList()) {
				if (sourceConfig.getRetryTarget() != null) {
					FlinkRetrySink flinkRetrySink = new FlinkRetrySink(sourceConfig.getPipelineName(),
							createRetryKafkaSinkFunction(sourceConfig));
					retrySinkList.add(flinkRetrySink);
				}

			}
		}
		return retrySinkList;
	}

	private SinkFunction createRetryKafkaSinkFunction(
			final KafkaSourceConfigurationObject kafkaSourceConfigurationObject) {
		Properties producerProps = new Properties();
		producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
				kafkaSourceConfigurationObject.getRetryTarget().getBootstrapServers());
		producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		if (SerializationTypeEnum.STRING.equals(SerializationTypeEnum
			.getSerializationTypeEnumByType(kafkaSourceConfigurationObject.getSerializationType()))) {
			producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
		}
		else {
			producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class.getName());
			producerProps.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG,
					kafkaSourceConfigurationObject.getConfluentKafkaRegistryUrl());
			producerProps.put(KafkaAvroSerializerConfig.AUTO_REGISTER_SCHEMAS, "false");
		}
		producerProps.put(ProducerConfig.BATCH_SIZE_CONFIG,
				kafkaSourceConfigurationObject.getRetryTarget().getBatchSizeBytes());
		producerProps.put(ProducerConfig.LINGER_MS_CONFIG,
				kafkaSourceConfigurationObject.getRetryTarget().getLingerMs());
		producerProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG,
				kafkaSourceConfigurationObject.getRetryTarget().getRequestTimeoutMs());
		producerProps.put(ProducerConfig.ACKS_CONFIG, "all");
		producerProps.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 1024);
		producerProps.put(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG,
				kafkaSourceConfigurationObject.getRetryTarget().getTransactionTimeout());

		producerProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "zstd");
		// Optional: Configure compression level (default is 3, range is -5 to 22)
		producerProps.put("compression.zstd.level", "3");

		/***
		 * Semantic.NONE: Flink will not guarantee anything. Produced records can be lost
		 * or they can be duplicated. Semantic.AT_LEAST_ONCE (default setting): This
		 * guarantees that no records will be lost (although they can be duplicated).
		 * Semantic.EXACTLY_ONCE: Kafka transactions will be used to provide exactly-once
		 * semantic. Whenever you write to Kafka using transactions, do not forget about
		 * setting desired isolation.level (read_committed or read_uncommitted - the
		 * latter one is the default value) for any application consuming records from
		 * Kafka.
		 */
		FlinkKafkaProducer kafkaProducer = null;
		FlinkPipelineNameEnum flinkPipeline = FlinkPipelineNameEnum
			.getPipelineNameByName(kafkaSourceConfigurationObject.getPipelineName());
		/**
		 * If Flink pipeline not defined in FlinkPipelineEnum then throw Runtime exception
		 */
		if (Objects.isNull(flinkPipeline)) {
			log.error("Pipeline is not defined in FlinkPipeline Enum pipeline: {}",
					kafkaSourceConfigurationObject.getPipelineName());
			throw new RuntimeException("Pipeline is not defined in FlinkPipeline Enum");
		}

		if (SerializationTypeEnum.STRING.equals(SerializationTypeEnum
			.getSerializationTypeEnumByType(kafkaSourceConfigurationObject.getSerializationType()))) {
			kafkaProducer = new FlinkKafkaProducer(kafkaSourceConfigurationObject.getRetryTarget().getRetryTopic(),
					new StringSerializationSchema(kafkaSourceConfigurationObject.getRetryTarget().getRetryTopic(),
							flinkPipeline.getInputClazz(), kafkaSourceConfigurationObject.getPipelineName()),
					producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
					kafkaSourceConfigurationObject.getRetryTarget().getProducersPoolSize());
		}
		else {
			kafkaProducer = new FlinkKafkaProducer(kafkaSourceConfigurationObject.getRetryTarget().getRetryTopic(),
					new AvroSerializationSchema(kafkaSourceConfigurationObject.getRetryTarget().getRetryTopic(),
							flinkPipeline.getInputClazz()),
					producerProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE,
					kafkaSourceConfigurationObject.getRetryTarget().getProducersPoolSize());
		}
		return kafkaProducer;
	}

}
