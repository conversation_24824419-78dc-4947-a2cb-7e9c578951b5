package com.org.panaroma.ingester.switchAdaptor.internal;

import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE_EVENT_PROCESSING_TIME;
import static com.org.panaroma.ingester.constants.Constants.COLON;
import static com.org.panaroma.ingester.constants.Constants.USER_TRANSACTION_TAG_ORIGINATOR;
import static com.org.panaroma.ingester.constants.PipelineConstants.ORIGINATOR;

import com.org.panaroma.commons.cache.dto.ListingSpecificCacheData;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.ingester.cache.AerospikeCacheClient;
import com.org.panaroma.ingester.constants.PipelineConstants;
import com.org.panaroma.ingester.dto.streamDTO.CacheUpdaterStreamDto;
import com.org.panaroma.ingester.exception.SwitchAdaptorException;
import com.org.panaroma.ingester.filter.CacheUpdaterValidationFilter;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.switchAdaptor.AbstractSwitchAdaptor;
import com.org.panaroma.ingester.transformer.streamTransformer.DefaultTransformer;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.log4j.Log4j2;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class CommonCacheAdaptor extends AbstractSwitchAdaptor<CacheUpdaterKafkaDto, CacheUpdaterKafkaDto> {

	public static final String switchName = "CommonCacheAdaptor";

	@Value("${isNtuCacheEnabled}")
	private Boolean isNtuCacheEnabled;

	@Value("${isUpiPassbookCacheEnabled}")
	private Boolean isUpiPassbookCacheEnabled;

	List<CacheInfo> validCachesForAdaptor = Arrays.asList(CacheInfo.UTH_NTU_CACHE);

	@Autowired
	CacheUpdaterValidationFilter cacheUpdaterValidationFilter;

	@Autowired
	MetricsAgent metricsAgent;

	@Autowired
	AerospikeCacheClient cacheClient;

	@Autowired
	private RolloutStrategyHelper rolloutStrategyHelper;

	@Value("${listingCache.delay.between.deleteAndSave.milliseconds}")
	Long delayBetweenDeleteAndSave;

	Long maxAllowedDelay = 6000L;

	public CommonCacheAdaptor() {
		super(null, null);
	}

	@Override
	public String getSwitchName() {
		return switchName;
	}

	@Override
	public boolean isFilterApplicable(final String pipelineName) {
		return true;
	}

	@Override
	public FilterFunction<CacheUpdaterKafkaDto> getFilter(final String pipelineName) {
		return cacheUpdaterValidationFilter;
	}

	@Override
	public boolean isMappingRequired(final String pipelineName) {
		return true;
	}

	@Override
	public DataStream getApplicableStream(final Map<String, DataStream> dataStreamMap, final String pipelineName) {
		return dataStreamMap.get(DefaultTransformer.TRANSFORMATION_NAME);
	}

	@Override
	protected Object getOutputStreamObject(final CacheUpdaterKafkaDto nonStreamObj) {
		return new CacheUpdaterStreamDto(nonStreamObj);
	}

	@Override
	protected boolean isApiBasedSink() {
		return true;
	}

	@Override
	protected void sendApiRequest(final CacheUpdaterKafkaDto kafkaDto) throws Exception {

		/*
		 * Will remove this if block as events won't reach here if isNtuCacheEnabled &
		 * this will be done in next deployment. Have to add this check as we need to stop
		 * populating these caches without deploying main pipelines & only deploy
		 * cache-updater pipeline
		 */
		if (!isNtuCacheEnabled) {
			return;
		}

		Map<CacheInfo, String> cachesToBeUpdated = kafkaDto.getCacheMetaInfo();
		for (CacheInfo cacheInfo : cachesToBeUpdated.keySet()) {
			if (!validCachesForAdaptor.contains(cacheInfo)) {
				continue;
			}
			String key = cachesToBeUpdated.get(cacheInfo);
			log.warn(cacheInfo.getCacheName() + " : Event received for entityId : {} and uid : {} is : {} ",
					kafkaDto.getEntityId(), kafkaDto.getUid(), kafkaDto);

			ListingSpecificCacheData newCacheDto = getListingSpecificCacheDataFromKafkaDto(kafkaDto);

			if (PipelineConstants.INGESTOR.equals(kafkaDto.getOriginator())
					|| USER_TRANSACTION_TAG_ORIGINATOR.equalsIgnoreCase(kafkaDto.getOriginator())) {
				handleIngestorEvent(kafkaDto, cacheInfo, key, newCacheDto);
			}
		}
	}

	/**
	 * Handling for originator = ingester.
	 */
	private void handleIngestorEvent(final CacheUpdaterKafkaDto kafkaDto, final CacheInfo cacheInfo, final String key,
			final ListingSpecificCacheData newCacheDto) throws Exception {
		saveAndPushMetrics(kafkaDto, cacheInfo, key, newCacheDto);
	}

	private void saveAndPushMetrics(final CacheUpdaterKafkaDto kafkaDto, final CacheInfo currentCache, final String key,
			final ListingSpecificCacheData newCacheDto) throws Exception {
		cacheClient.saveNtuCacheData(key, newCacheDto, currentCache);
		log.debug(currentCache.getCacheName() + " : Event saved in cache for entityId : {} and uid : {} is : {}",
				kafkaDto.getEntityId(), kafkaDto.getUid(), newCacheDto);
		metricsAgent.pushTimeDiff(CACHE_EVENT_PROCESSING_TIME, kafkaDto.getCreatedDate(), newCacheDto.getCreatedDate(),
				CACHE + COLON + currentCache.getCacheName(), ORIGINATOR + COLON + kafkaDto.getOriginator());
	}

	private ListingSpecificCacheData getListingSpecificCacheDataFromKafkaDto(final CacheUpdaterKafkaDto kafkaDto) {
		ListingSpecificCacheData cacheDto = new ListingSpecificCacheData();
		cacheDto.setIsNonTransactingUser(kafkaDto.getIsNonTransactingUser());
		cacheDto.setListingData(kafkaDto.getListingData());
		Long currentTime = System.currentTimeMillis();
		cacheDto.setCreatedDate(currentTime);
		return cacheDto;
	}

	@Override
	protected void applyMasking(final CacheUpdaterKafkaDto nonStreamObj) {

	}

	@Override
	protected void applyCustomOperation(final CacheUpdaterKafkaDto nonStreamObj) {

	}

	@Override
	protected CacheUpdaterKafkaDto getMappedObject(final CacheUpdaterKafkaDto nonStreamObj)
			throws SwitchAdaptorException {
		return nonStreamObj;
	}

	@Override
	public TypeInformation getProducedType() {
		return TypeInformation.of(new TypeHint<CacheUpdaterStreamDto>() {
		});
	}

}
