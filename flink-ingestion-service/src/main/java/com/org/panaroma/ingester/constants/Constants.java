package com.org.panaroma.ingester.constants;

import static com.org.panaroma.commons.constants.CommonConstants.OUTWARD_REPORT_CODES_SENT_TO_CHAT;
import static com.org.panaroma.commons.constants.CommonConstants.UPI_BACK_FILLING_PIPELINE;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.enums.OmsOrderStatusEnum;
import com.org.panaroma.commons.enums.OmsPaymentStatusEnum;
import com.org.panaroma.commons.enums.OmsRefundOrderStatusEnum;
import com.org.panaroma.ingester.enums.FlinkPipelineNameEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class Constants {

	public static final String VALUE = "value";

	public static final String TRANSACTION_SOURCE = "transactionSource";

	public static final String STREAM_SOURCE = "streamSource";

	public static final String USER_VPA = "userVpa";

	public static final String IS_INVALID_CUSTID = "isInvalidCustId";

	public static final String SHOW_IN_LISTING = "showInListing";

	public static final String AMOUNT = "amount";

	public static final String REMARKS = "remarks";

	public static final String STATUS = "status";

	public static final String ORIGINAL_STATUS = "originalStatus";

	public static final String SYSTEM_ID = "systemId";

	public static final String SOURCE_SYSTEM_ID = "sourceSystemId";

	public static final String SOURCE_SYSTEM = "sourceSystem";

	public static final String PARENT_TXN_ID = "parentTxnId";

	public static final String TXN_TYPE = "txnType";

	public static final String TXN_ID = "txnId";

	public static final String CATEGORY_ID = "categoryId";

	public static final String TXN_DATE = "txnDate";

	public static final String CONTEXT_MAP_REPORT_CODE = "contextFilterMap.reportCode";

	public static final String CONTEXT_MAP_RRN = "contextFilterMap.rrn";

	public static final String UPDATED_DATE = "updatedDate";

	public static final String TXN_PARTICIPANTS = "participants";

	public static final String ENTITY_TYPE = "entityType";

	public static final String TXN_INDICATOR = "txnIndicator";

	public static final String TAGS = "tags";

	public static final String CONTEXT_MAP = "contextMap";

	public static final String INDEX_NAME = "payment-history";

	public static final String TXN_PURPOSE = "txn_purpose";

	public static final String TXN_NARRATION = "txn_narration";

	public static final String ADD_AND_PAY_TXN_PURPOSE = "ADD_AND_PAY";

	public static final String PAYTM_GIFT_VOUCHER = "Paytm Gift Voucher";

	public static final String PAYTMGIFTVOUCHER = "PaytmGiftVoucher";

	public static final String REASON_FOR_NOT_VISIBLE = "reasonForNotVisible";

	public static final String WALLET = "wallet";

	public static final String PG = "pg";

	public static final String CBS = "cbs";

	public static final String TS = "ts";

	public static final String RETRY_PIPELINE = "retry-pipeline";

	public static final String PG_RETRY_V2_PIPELINE = "pg-retry-v2";

	public static final String CART = "cart";

	public static final String DETAIL_CACHE_INVALIDATOR_SINK = "detail_cache_invalidator_sink";

	public static final String SINK_TO_CART = "sink2cart";

	public static final String SINK_TO_UTH = "sink2uth";

	public static final String SINK_TO_DC_UTH = "sink2DcUth";

	public static final String SINK_TO_CACHE_UPDATER = "sink2CacheUpdater";

	public static final String CACHEUPDATER_KAFKASINK = "-cacheUpdater-kafka-sink";

	public static final String SINK_TO_MERGED_DATA_FOR_RETRY_FROM_CHAT = "sink2mergedDataForRetryFromChat";

	public static final String RETRY_PIPELINE_V2 = "retry-pipeline-v2";

	public static final String RETRY_BACK_FILLING_PIPELINE = "retry-back-filling";

	public static final String CHAT_BACK_FILLING_PIPELINE = "chat-back-filling";

	public static final String MARKETPLACE = "marketplace";

	public static final String CBS_DATA_CONEVRTER_PIPELINE = "cbs-converter";

	public static final String CHAT_PIPELINE = "chat";

	public static final String RECENT_TXN_PIPELINE = "recent-txn";

	public static final String RECON_PIPELINE = "recon";

	public static final String STATUS_RESOLVER = "status-resolver";

	public static final String TAGS_ENRICHER = "tags-enricher";

	public static final String UTH = "UTH";

	public static final String UTH_PIPELINE = "uth";

	public static final String TOGGLE_VISIBILITY_PIPELINE = "toggle-visibility";

	public static final String IAT = "iat";

	public static final String SEQ_NO = "seq-no";

	public static final String CHANNEL_CODE = "channel-code";

	public static final String TRANSACTION_ID = "txn-id";

	public static final String DATA = "data";

	public static final String CONTENT_TYPE = "Content-Type";

	public static final String AUTHORIZATION = "authorization";

	public static final String BEARER = "Bearer ";

	public static final String CHANNEL = "channel";

	public static final String CLIENT_ID = "clientId";

	public static final String SUCCESS = "SUCCESS";

	public static final String KAFKA_RETRY_COUNT = "kafkaRetryCount";

	public static final String USER_IMAGE_URL = "userImageUrl";

	public static final String COLLECT = "COLLECT";

	public static final String TRANSACTION_PURPOSE = "txnPurpose";

	public static final String BANK_TRANSFER_PREAPPROVED = "BANK_TRANSFER_PREAPPROVED";

	public static final String IS_FOR_GROUPING = "isForGrouping";

	public static final String LISTING_CUSTOM_NARRATION = "listingCustomNarration";

	public static final String DETAILS_CUSTOM_NARRATION = "detailsCustomNarration";

	public static final String IS_FOR_RETRY_FROM_SYSTEM = "isForRetryFromSystem";

	public static final String ORDER_EVENT = "order event";

	public static final String MERCHANT_EVENT = "merchant event";

	public static final String USER_EVENT = "user event";

	public static final String EVENT = "event";

	public static final String FREE_FIELD_9 = "freeField9";

	public static final String PAYTM = "Paytm";

	public static final String PAYTM_MID = "Paytm Mid";

	public static final String LAST_DAY_TIME = "lastDayAndTime";

	public static final String STARTNG_DAY_TIME = "startingDayTime";

	public static final String UTH_CATEGORY_OTHERS = "OTHERS";

	public static final String ACCT_NUM = "acctNum";

	public static final String IFSC = "ifsc";

	public static final String PYTM = "PYTM";

	public static final String DEBIT_PAYMENT_SYSTEM = "debitPaymentSystem";

	public static final String CREDIT_PAYMENT_SYSTEM = "creditPaymentSystem";

	public static final String DEBIT_UPI_PAYMENT_SYSTEM = "debitUpiPaymentSystem";

	public static final String DEBIT_WALLET_PAYMENT_SYSTEM = "debitWalletPaymentSystem";

	public static final String CREDIT_UPI_PAYMENT_SYSTEM = "creditUpiPaymentSystem";

	public static final String CREDIT_WALLET_PAYMENT_SYSTEM = "creditWalletPaymentSystem";

	public static final String REPORT_CODE = "reportCode";

	public static final String IS_VISIBLE = "isVisible";

	public static final String TRUE = "true";

	public static final String IS_CHAT_DOCUMENT = "isChatDocument";

	public static final String IS_MERGED_DOCUMENT = "isMergedDocument";

	public static final String DEBIT_CONTEXT_MAP = "debitContextMap";

	public static final String CHAT_UNIQUE_IDENTIFIER = "chatUniqueIdentifier";

	public static final String REFUND_DETAILS = "refundDetails";

	public static final String PARENT_DETAILS = "parentDetails";

	public static final String LOCATION = "location";

	public static final String Rrn = "rrn";

	public static final String PAYTM_HANDLE = "@paytm";

	public static final String REPORT_CODE_20270 = "20270";

	public static final String REPORT_CODE_20271 = "20271";

	public static final String REPORT_CODE_20212 = "20212";

	public static final String PPBL_SET_NAME = "uthPPBLRecordSet";

	public static final String IS_FOR_ES_SINK = "isForEsSink";

	public static final String GET_CONTEXT_MAP = "getContextMap";

	public static final String GET_IS_FOR_ES_SINK = "getIsForEsSink";

	public static final String MINUS_ONE = "-1";

	public static final String MAIN_TXN_TYPE = "mainTxnType";

	public static final List<String> XFER_RPT_CODES = Arrays.asList("20270", "20271");

	public static final String DROOLS_IDENTIFIER = "drools";

	public static final String UNDERSCORE = "_";

	public static final String WALLET_PARTICIPANT = "walletParticipant";

	public static final String WALLET_PARTICIPANT_LIST = "walletParticipantList";

	public static final String PAYMENT_SYSTEM = "paymentSystem";

	public static final String USER_WALLET_DATA = "userWalletData";

	public static final String WALLET_PAYMENT_SYSTEM = "walletPaymentSystem";

	public static final String RETRY_FROM = "retryFrom";

	public static final String PIPELINE = "Pipeline";

	public static final String REASON_FOR_RETRY = "reasonForRetry";

	public static final String COUNT = "Count";

	public static final String NULL_STRING = "null";

	public static final String BLANK_STRING = "";

	public static final String OTHER_PARTY_ENTITY_ID_RELATED_FIELDS_UPDATE_QUERY_FAILURE = "OTHER_PARTY_ENTITY_ID_RELATED_FIELDS_UPDATE_QUERY_FAILURE";

	public static final String INTEGRATION_ISSUE_WITH_SOURCE = "integrationIssueWithSource";

	public static final String REMITTER_ACCT_NUM = "remitterAcctNum";

	public static final String BENEF_ACCT_NUM = "benefAcctNum";

	public static final String BENEF_BANK_NAME = "benefBankName";

	public static final String BENEF_BANK = "benefBank";

	public static final String REMITTER_IFSC = "remitterIfsc";

	public static final String BENEF_IFSC = "benefIfsc";

	public static final String REMITTER_ACC_REF_NUM = "remitterAccRefNum";

	public static final String REMITTER_BANK_NAME = "remitterBankName";

	public static final String BENEF_BANK_LOGO_URL = "benefBankLogoUrl";

	public static final String REMITTER_BANK_LOGO_URL = "remitterBankLogoUrl";

	public static final String BENEF_ACC_REF_NUM = "benefAccRefNum";

	public static final String IS_FOR_CHAT_BACK_FILLING = "isForChatBackFilling";

	public static final String BENEFICIARY_NAME = "benefName";

	public static final String REMITTER_NAME = "remitterName";

	public static final String IS_VIRTUAL_ENTITY_ID = "isVirtualEntityId";

	public static final List<String> xferInwardReportCodeList = Arrays.asList("20270");

	public static final List<String> xferOutwardReportCodeList = Arrays.asList("20271");

	// These are merging constant please check all merger script before changing value of
	// any of these constant it
	public static final String DOC_UPDATED_DATE = "docUpdatedDate";

	public static final String DEBIT = "debit";

	public static final String CREDIT = "credit";

	public static final String DEBIT_PARTICIPANT = "debitParticipant";

	public static final String UPI_DATA = "upiData";

	public static final String CARD_DATA = "cardData";

	public static final String MOBILE_DATA = "mobileData";

	public static final String BANK_DATA = "bankData";

	public static final String PAYMENT_TXN_ID = "paymentTxnId";

	public static final String ORDER_ID = "orderId";

	public static final String NAME = "name";

	public static final String WALLET_DATA = "walletData";

	public static final String SEARCH_FIELDS = "searchFields";

	public static final String SPEND_ANALYTICS_FIELDS = "spendAnalyticsFields";

	public static final String ENTITY_ID = "entityId";

	public static final String UPI = "upi";

	public static final String COLON = ":";

	public static final String COMMA = ",";

	public static final String EXCEPTION = "exception";

	public static final String TYPE = "type";

	public static final String UPDATED_TAGS = "updatedTags";

	public static final String ADDED_TAGS = "addedTags";

	public static final String DELETED_TAGS = "deletedTags";

	public static final String ACTION = "action";

	public static final String USER_ID = "userId";

	public static final String TAG_NAME = "tagName";

	public static final String CREATED_TIME = "createdTime";

	public static final String UPDATED_TIME = "updatedTime";

	public static final String DYNAMO_DB = "dynamodb";

	public static final String NEW_IMAGE = "NewImage";

	public static final String PIPELINE_NAME = "pipelineName";

	public static final String S = "S";

	public static final String L = "L";

	public static final String N = "N";

	public static final String UNKNOWN_EXCEPTION_RESPONSECODE = "responseCode:4003";

	public static final String ADD = "ADD";

	public static final String DELETE = "DELETE";

	public static final String FORWARD_TXNDATE = "forwardTxnDate";

	public static final String TO_SOURCE = "TO_SOURCE";

	public static final String REFUND_TYPE = "refundType";

	public static final String RETRY_PIPELINE_PUSH_COUNT = "retryPipelinePushCount";

	// Spend Analytics Constants
	public static final String SPEND_DOC_SET = "SPEND_DOC_SET";

	// Spend Analytics Constants
	public static final String GENERIC_KAFKA_DTO_CLASS = "GenericKafkaDto";

	// Spend Analytics Constants
	public static final String TRANSFORMED_COMPLAINT_DETAILS_CLASS = "TransformedComplaintDetails";

	public static final String REASON = "reason";

	public static final String USER_BANK_LOGO_URL = "userBankLogoUrl";

	public static final String RETRY_COUNT_FOR_CART_ENRICHER = "RETRY_COUNT_FOR_CART_ENRICHER";

	public static final Integer RETRY_COUNT_FOR_CART_ENRICHER_LIMIT = 2;

	public static final List<String> INWARD_REPORT_CODES_SENT_TO_CHAT = Collections.emptyList();

	public static final List<String> REPORT_CODES_TO_HIT_BENEFICIARY_FOR_ACCREFNUM = Arrays.asList("20211", "20410",
			"20430", "20411", "20293");

	public static final List<String> REPORT_CODES_PUSHED_TO_MERGED_DATA_ONLY_FOR_ACCREFNUM = Arrays.asList("20430",
			"20411", "20293", "20440");

	public static final List<String> REPORT_CODES_TO_HIT_PMS_FOR_ACCREFNUM = Arrays.asList("20212", "20420", "20440");

	public static final List<String> PPBL_REPORT_CODES_HANDLED_FOR_CHAT = new ArrayList<>();

	public static final List<TransactionTypeEnum> P2P_OUTWARD_INWARD_TRANSACTION_TYPES = Arrays
		.asList(TransactionTypeEnum.P2P_OUTWARD, TransactionTypeEnum.P2P_INWARD);

	public static final List<Integer> END_STATUS_INT_LIST = Arrays.asList(ClientStatusEnum.FAILURE.getStatusKey(),
			ClientStatusEnum.SUCCESS.getStatusKey());

	public static final String TOKEN_CACHE_SET_NAME = "tokenset";

	// ts whitelisted report codes
	public static final List<String> TS_WHITE_LISTED_REPORT_CODES = Arrays.asList("20261", "20263", "20264", "20265",
			"20275", "20276", "20277", "20212");

	public static final List<String> PARTIAL_IFSC_TO_FETCH_FROM_NBIN_REPORT_CODES = Arrays.asList("20211");

	public static final List<String> PARTIAL_IFSC_MADE_FROM_FIRST_FOUR_CHAR_REPORT_CODES = Arrays.asList("20410",
			"20430", "20411", "20293");

	// Need to add all the report codes in which we group ppbl transactions.
	public static final List<String> WHITE_LISTED_REPORT_CODES_FOR_PPBL_GROUPING = Arrays.asList("20261", "20265",
			"20263", "20264", "20275", "20276", "20277", "20212", "20211");

	public static final List<String> STRING_SERIALISED_PIPELINE_NAME = Arrays.asList(CART, SINK_TO_CART, CHAT_PIPELINE,
			SINK_TO_DC_UTH, CHAT_BACK_FILLING_PIPELINE, RECON_PIPELINE, UPI_BACK_FILLING_PIPELINE, SINK_TO_UTH,
			SINK_TO_CACHE_UPDATER, DETAIL_CACHE_INVALIDATOR_SINK,
			FlinkPipelineNameEnum.CBS_STREAM_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.PROMO_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.CBS_RETRY_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.CACHE_UPDATER_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.VAN_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.UPI_RECON_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.CBS_RETRY_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.CACHE_UPDATER_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.USER_SPEND_DOCS_CREATOR_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.USERID_FETHCER_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.PROMO_UPI_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.RETRY_PIPELINE_V3.getPipelineName(),
			FlinkPipelineNameEnum.DC_MAIN_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.CHAT_DATA_PUBLISH_API_PIPELINE.getPipelineName());

	public static final String STATIC_PROFILE_URL = "static_profile_url";

	public static final String USER_IMAGE_URL_KEY = "userImageUrl";

	public static final String ACCEPT = "accept";

	public static final String APPLICATION_JSON = "application/json";

	public static final String VERIFICATION_TYPE = "verification_type";

	public static final String SERVICE_TOKEN = "service_token";

	public static final Long LONG_ONE = 1L;

	public static final String SECOND_USER_IMAGE_URL = "secondUserImageUrl";

	public static final String ES_SINK_INSERT = "insert";

	public static final String ES_SINK_UPDATE = "update";

	public static final String RELATIVE_PRESENT = "noOfRelPresent";

	public static final List<String> SIZE_NOT_ALLOWED_FOR_REGROUPING = Arrays.asList("0", "1");

	public static final String ES_SINK_UDIR = "udir";

	public static final String UDIR_DUMP_INDEX_NAME = "udir-dump";

	public static final String PUSH_TIME_STAMP = "pushTimeStamp";

	public static final Set<String> P_2_P_REPORT_CODES_FOR_CHAT = new HashSet<>();

	public static final Set<String> XFER_P_2_P_REPORT_CODE_LIST = new HashSet<>();

	public static final String KAFKASINK = "-kafka-sink";

	public static final String SINK = "-sink";

	public static final String EXPIRATION_TIME = "expirationTime";

	public static final List<TransactionTypeEnum> P2M_TRANSACTION_TYPES = Arrays.asList(TransactionTypeEnum.P2M,
			TransactionTypeEnum.P2M_REFUND);

	public static final List<TransactionSource> P2M_STREAM_SOURCES_TO_SEND_TO_CHAT = Arrays.asList(TransactionSource.PG,
			TransactionSource.UPI, TransactionSource.WALLET, TransactionSource.PPBL_PG, TransactionSource.OMS);

	public static final List<TransactionTypeEnum> WALLET_INTEROP_CREDIT_TYPE_LIST = Arrays
		.asList(TransactionTypeEnum.UPI_WALLET_CREDIT, TransactionTypeEnum.WALLET_UPI_DEBIT_REVERSAL);

	public static final List<TransactionTypeEnum> WALLET_INTEROP_DEBIT_TYPE_LIST = Arrays.asList(
			TransactionTypeEnum.UPI_WALLET_CREDIT_REVERSAL, TransactionTypeEnum.WALLET_UPI_DEBIT_P2M,
			TransactionTypeEnum.WALLET_UPI_DEBIT_P2P);

	public static final List<OmsOrderStatusEnum> omsVisbileOrderStatus = Arrays.asList(OmsOrderStatusEnum.CREATED,
			OmsOrderStatusEnum.SUCCESS, OmsOrderStatusEnum.FAILURE);

	public static final List<OmsRefundOrderStatusEnum> omsRefundVisbileOrderStatus = Arrays
		.asList(OmsRefundOrderStatusEnum.SUCCESS);

	public static final List<OmsPaymentStatusEnum> omsVisbilePaymentStatus = Arrays.asList(OmsPaymentStatusEnum.PENDING,
			OmsPaymentStatusEnum.SUCCESS, OmsPaymentStatusEnum.FAILURE);

	public static final String ORDER_EVENT_REMARK = "ORDER_EVENT_REMARK";

	public static final String UPDATE_REQUEST_RETRY_COUNT = "updateRequestRetryCount";

	public static final String FLAG_FOR_ADD_MONEY_3P_REFUND = "topupRfndBackToSrc";

	public static final String ADD_MONEY_TO_WALLET = "Add Money to Wallet";

	public static final String FILTER = "-filter";

	public static final String FIVE_MINUTRES_EPOCHMILLI = "300000";

	public static final String RETRY_FOR_PMS_CALL = "retryForPMSCall";

	public static final String PROMO_PIPELINE_LOGS = "promoPipelineLogs";

	static {
		P_2_P_REPORT_CODES_FOR_CHAT.addAll(INWARD_REPORT_CODES_SENT_TO_CHAT);
		P_2_P_REPORT_CODES_FOR_CHAT.addAll(OUTWARD_REPORT_CODES_SENT_TO_CHAT);
		P_2_P_REPORT_CODES_FOR_CHAT.addAll(xferInwardReportCodeList);
		P_2_P_REPORT_CODES_FOR_CHAT.addAll(xferOutwardReportCodeList);

		XFER_P_2_P_REPORT_CODE_LIST.addAll(xferOutwardReportCodeList);
		XFER_P_2_P_REPORT_CODE_LIST.addAll(xferInwardReportCodeList);

		PPBL_REPORT_CODES_HANDLED_FOR_CHAT.addAll(INWARD_REPORT_CODES_SENT_TO_CHAT);
		PPBL_REPORT_CODES_HANDLED_FOR_CHAT.addAll(OUTWARD_REPORT_CODES_SENT_TO_CHAT);
		PPBL_REPORT_CODES_HANDLED_FOR_CHAT.addAll(XFER_P_2_P_REPORT_CODE_LIST);
	}

	public static final String USER_TRANSACTION_TAG_ORIGINATOR = "USER_TRANSACTION_TAG";

	public static final String COLLECT_STATUS = "collect_status";

	public static final String REQUEST_PENDING = "REQUEST_PENDING";

	public static final String REQUESTED = "REQUESTED";

	public static final List<String> FAILURE_COLLECT_STATUSES = Arrays.asList("EXPIRED", "DECLINED");

	public static final String IS_FROM_DC_PIPELINE = "isFromDcPipeline";

	public static final String P2P_INWARD_FAILURE_PENDING_EVENTS_FILTER_ROLLOUT = "p2p_inward_failure_pending_events_filter_rollout";

	public static final String PARTIAL_EMI_EVENT_RETRY_PIPELINE_PUSH_COUNT = "partialEmiEventRetryPipelinePushCount";

	public static final String EVENT_ALREADY_PRESENT_IN_ES = "prsntInEsDoc";

	public static final List<String> DC_PIPELINES = Arrays.asList(PipelineConstants.DC_MAIN_PIPELINE,
			PipelineConstants.DC_UTH_ENRICHER_PIPELINE);

	public static final String IGNORE_DTOS_ARE_SAME = "ignoreDtosAreSame";

	public static final String PARENT_TXN = "parentTxn";

	public static final String CHILD_TXNS = "childTxns";

	public static class KafkaSecurity {

		public static final String SECURITY_PROTOCOL = "security.protocol";

		public static final String SASL_MECHANISM = "sasl.mechanism";

		public static final String SASL_JASS_CONFIG = "sasl.jaas.config";

	}

	public static class RelayConstants {

		public static final String IS_FROM_RELAY = "ifr";

		public static final String ONE = "1";

		public static final String RELAY_EVENTS = "RELAY_EVENTS";

		public static final String REASON = "reason";

		public static final String DISCARD = "discard";

		public static final String OVERRIDE = "override";

		public static final String TPAP_DOC_ALREADY_PRESENT = "tpapDocAlreadyPresent";

	}

}
