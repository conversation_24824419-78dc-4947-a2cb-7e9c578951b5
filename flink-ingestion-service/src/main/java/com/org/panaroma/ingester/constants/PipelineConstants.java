package com.org.panaroma.ingester.constants;

import static com.org.panaroma.ingester.constants.Constants.CART;
import static com.org.panaroma.ingester.constants.Constants.CHAT_BACK_FILLING_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.CHAT_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RECON_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.SINK_TO_CART;

import com.org.panaroma.ingester.enums.FlinkPipelineNameEnum;
import java.util.Arrays;
import java.util.List;

public class PipelineConstants {

	public static final String CBS_STREAM_PIPELINE = "cbs-stream";

	public static final String PROMO_PIPELINE = "promo";

	public static final String CBS_RETRY_PIPELINE = "cbs-retry-pipeline";

	public static final String VAN_PIPELINE = "van-pipeline";

	public static final String CACHE_UPDATER_PIPELINE = "cache-updater-pipeline";

	public static final String UPI_RECON_PIPELINE = "upi-recon-pipeline";

	public static final String USER_SPEND_DOCS_CREATOR_PIPELINE = "user-spend-docs-creator";

	public static final String USER_ANALYTICS_MONTH_AGG_CREATOR = "user-analytics-month-agg-creator";

	public static final String TAGS_AGG_PIPELINE = "tag-agg-pipeline";

	public static final String TAGS_AGG_RETRY_PIPELINE = "tag-agg-retry-pipeline";

	public static final String UDIR = "udir";

	public static final String USERID_FETHCER_PIPELINE = "userId-fetcher-pipeline";

	public static final String PROMO_UPI_PIPELINE = "promo-upi-pipeline";

	public static final List<String> STRING_SERIALISED_PIPELINE_NAME = Arrays.asList(CART, SINK_TO_CART, CHAT_PIPELINE,
			CHAT_BACK_FILLING_PIPELINE, RECON_PIPELINE, FlinkPipelineNameEnum.CBS_STREAM_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.PROMO_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.CBS_RETRY_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.VAN_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.UPI_RECON_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.USER_SPEND_DOCS_CREATOR_PIPELINE.getPipelineName(),
			FlinkPipelineNameEnum.OMS.getPipelineName());

	public static final String ORIGINATOR = "originator";

	public static final String INGESTOR = "ingestor";

	public static final String TIME_WHEN_INVALIDATION_DTO_PUSHED = "timeWhenInvalidationDtoPushed";

	public static final String UTH_STREAMING_RETRY_COUNT = "uthStreamingRetryCount";

	public static final String GENERIC_RETRY_PIPELINE = "generic-retry-pipeline";

	public static final String DC_MAIN_PIPELINE = "dc-main-pipeline";

	public static final String DC_UTH_ENRICHER_PIPELINE = "dc-uth-enricher-pipeline";

	public static final String CHAT_DATA_PUBLISH_API_PIPELINE = "chat-data-publish-api-pipeline";

	public static final String PG_DATA_PUBLISH_API_PIPELINE = "pg-data-publish-api-pipeline";

	public static final String PPBL_PG = "ppbl-pg";

	public static final String ES_INSERT_UPDATE_EXECUTOR_PIPELINE = "es-insert-update-executor-pipeline";

	public static final String DATA_AUDIT_PIPELINE = "data-audit-pipeline";

	public static final String API_RESPONSE_CACHE_POPULATION_PIPELINE = "api-response-cache-population-pipeline";

	public static final String UPI_V2 = "upiV2";

	public static final String OMS = "oms";

	public static final String OMS_REFUND = "oms-refund";

	public static final String UPI_RELAY_PIPELINE = "upi-relay-pipeline";

	public static final String MANDATE = "mandate";

	public static final String MANDATE_RETRY = "mandate-retry";

	public static final String BACKFILLING_PIPELINE_NAME = "backfilling";

	public static final String TOGGLE_VISIBILITY = "toggle-visibility";

	public static final String INTELLIGENT_TAGS_CONFIGURATION_CACHE = "intelligentTagsConfigurationCache";

}
