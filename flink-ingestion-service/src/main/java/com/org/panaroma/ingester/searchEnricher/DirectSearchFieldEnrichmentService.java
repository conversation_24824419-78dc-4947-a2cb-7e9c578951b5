package com.org.panaroma.ingester.searchEnricher;

import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.constants.BankDataConstants;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.es.SearchFields;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.WalletType;
import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.IfscUtility;
import com.org.panaroma.ingester.utils.DroolsUtility;
import java.io.Serializable;
import java.util.Set;
import java.util.HashSet;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.POSTPAID_REPAYMENT_MERCHANT_IDS;

@Service
@Log4j2
public class DirectSearchFieldEnrichmentService implements Serializable {

	public TransformedTransactionHistoryDetail enrichSearchFields(final TransformedTransactionHistoryDetail tthd) {
		if (tthd == null) {
			return tthd;
		}

		// Initialize search fields if null
		if (tthd.getSearchFields() == null) {
			tthd.setSearchFields(new SearchFields());
		}

		// Process in order of salience
		// salience 11
		processOrderLevelDetails(tthd);

		// salience 10
		processWalletSelfParticipant(tthd);
		processUPISelfParticipant(tthd);
		processBankSelfParticipant(tthd);
		processPGSelfParticipant(tthd);
		processPostpaidSelfParticipant(tthd);
		processMGVSelfParticipant(tthd);
		processGVSelfParticipant(tthd);
		processPPBLSelfParticipant(tthd);
		processTSSelfParticipant(tthd);
		processTSOtherParticipant(tthd);
		processWalletAddMoneyRefund(tthd);

		// salience 9
		processWalletOtherUserParticipant(tthd);
		processUPIOtherUserParticipant(tthd);
		processBankOtherUserParticipant(tthd);
		processPGOtherUserParticipant(tthd);
		processPPBLOtherUserParticipant(tthd);

		// salience 8
		processWalletOtherMerchantParticipant(tthd);
		processUPIOtherMerchantParticipant(tthd);
		processBankOtherMerchantParticipant(tthd);
		processPGOtherMerchantParticipant(tthd);
		processPPBLOtherMerchantParticipant(tthd);

		// salience 7
		processEntityTypeNullParticipant(tthd);

		// salience 6
		processPaymentSystemToOrderLevel(tthd);
		processBankAccountId(tthd);
		processFDSpecificRules(tthd);

		// salience 1
		processUPILiteTopUpAndDeRegister(tthd);
		processSpecialAddMoney(tthd);

		// salience 0
		processGVSpecificRules(tthd);

		// salience -1
		processSpecialAddAndPay(tthd);

		// Additional special cases without salience
		processSelfUPITransferSearch(tthd);
		processSetChannelCode(tthd);
		processSetEmiStatus(tthd);
		processSetVerticalIdsSearch(tthd);
		processUPILinkedPPBLAccounts(tthd);
		processPGDebitCardTransactions(tthd);
		processPaytmPostpaidRepayment(tthd);
		processUPIViaCCBankAccountId(tthd);

		return tthd;
	}

	private void processOrderLevelDetails(final TransformedTransactionHistoryDetail tthd) {
		// Rule: set_order_level_details (salience 11)
		SearchFields searchFields = tthd.getSearchFields();
		searchFields.setSearchOrderId(tthd.getOrderId());
		DroolsUtility.updateSearchTagField(tthd);

		// Setting uth visible txn indicator for txn
		DroolsUtility.setUthVisibleTxnIndicator(tthd);
	}

	private void processWalletSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: wallet_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());
				searchFields.setSearchReferenceId(tthd.getTxnId());

				if (participant.getWalletData() != null
						&& WalletType.getWalletTypeByKey(participant.getWalletData().getWalletType()) != null) {
					Set<String> walletTypes = searchFields.getSearchWalletType();
					if (walletTypes == null) {
						walletTypes = new HashSet<>();
					}
					walletTypes.add(String
						.valueOf(WalletType.getEnumFromWalletTypeKey(participant.getWalletData().getWalletType())
							.getWalletTypeKey()));
					walletTypes.add(String
						.valueOf(WalletType.getEnumFromWalletTypeKey(participant.getWalletData().getWalletType())
							.getWalletTypeUserViewValue()));
					searchFields.setSearchWalletType(walletTypes);
				}
			});
	}

	private void processUPISelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: upi_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());

				if (participant.getBankData() != null) {
					DroolsUtility.updateSelfAccountType(tthd, participant);
				}

				if (tthd.getContextMap() != null && tthd.getContextMap().containsKey("rrn")) {
					searchFields.setSearchReferenceId(tthd.getContextMap().get("rrn"));
				}
			});
	}

	private void processBankSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: bank_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.BANK.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());

				if (participant.getBankData() != null) {
					DroolsUtility.updateSelfAccountType(tthd, participant);
				}

				String phoneNumber = Utility.getValidPhoneNumber(participant,
						TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()), true);
				searchFields.setSearchOtherMobileNo(phoneNumber);
			});
	}

	private void processPGSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: pg_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.PG.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());

				if (participant.getBankData() != null) {
					DroolsUtility.updateSelfAccountType(tthd, participant);
					searchFields.setSearchReferenceId(participant.getBankData().getBankTxnId());
				}
			});
	}

	private void processPostpaidSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: postpaid_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.PAYTM_POSTPAID.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());

				if (participant.getContextMap() != null && participant.getContextMap().containsKey("postpaid_txn_id")) {
					searchFields.setSearchReferenceId(participant.getContextMap().get("postpaid_txn_id"));
				}
			});
	}

	private void processMGVSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: mgv_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.MGV.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());
			});
	}

	private void processGVSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: gv_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.GV.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());

				// Set merchant VPA if available
				if (participant.getUpiData() != null && participant.getUpiData().getVpa() != null) {
					searchFields.setSearchOtherVpa(participant.getUpiData().getVpa());
				}

				// Set merchant MID if available
				if (participant.getMerchantData() != null && participant.getMerchantData().getMerchantId() != null) {
					searchFields.setSearchOtherAccountNo(participant.getMerchantData().getMerchantId());
				}
			});
	}

	private void processPPBLSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: ppbl_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());

				if (participant.getBankData() != null) {
					DroolsUtility.updateSelfAccountType(tthd, participant);
					searchFields.setSearchReferenceId(participant.getBankData().getRrn());
				}

				String phoneNumber = Utility.getValidPhoneNumber(participant,
						TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()), true);
				searchFields.setSearchOtherMobileNo(phoneNumber);
			});
	}

	private void processTSSelfParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: ts_self (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& tthd.getTxnIndicator().equals(participant.getTxnIndicator())
					&& PaymentSystemEnum.TS.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setSelfDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());

				if (participant.getBankData() != null) {
					DroolsUtility.updateSelfAccountType(tthd, participant);
				}
			});
	}

	private void processTSOtherParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: ts_Other_User (salience 10)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null
					&& !(tthd.getEntityId().equals(participant.getEntityId())
							&& tthd.getTxnIndicator().equals(participant.getTxnIndicator()))
					&& PaymentSystemEnum.TS.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus())))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchRemarks(participant.getRemarks());
			});
	}

	private void processEntityTypeNullParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: entityTypeNull (salience 7)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& participant.getEntityType() == null)
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getUpiData() != null) {
					searchFields.setSearchOtherVpa(participant.getUpiData().getVpa());
				}

				if (participant.getMobileData() != null) {
					searchFields.setSearchOtherMobileNo(participant.getMobileData().getMobileNumber());
				}

				if (participant.getMerchantData() != null && UthCategoryEnum
					.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory()) != null) {
					Set<String> merchantCategories = searchFields.getSearchUthMerchantCategory();
					if (merchantCategories == null) {
						merchantCategories = new HashSet<>();
					}
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryId());
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryUserView());
					searchFields.setSearchUthMerchantCategory(merchantCategories);
				}
			});
	}

	private void processPaymentSystemToOrderLevel(final TransformedTransactionHistoryDetail tthd) {
		// Rule: paymentSystemToOrderLevel (salience 6)
		if (tthd.getParticipants() == null) {
			return;
		}

		final Set<String> paymentSystems = new HashSet<>(tthd.getSearchFields().getSearchPaymentSystem() != null
				? tthd.getSearchFields().getSearchPaymentSystem() : new HashSet<>());

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null
					&& !TransactionTypeEnum.ADD_AND_PAY.getTransactionTypeKey().equals(tthd.getMainTxnType())
					&& tthd.getEntityId().equals(participant.getEntityId()))
			.forEach(participant -> {
				if (participant.getPaymentSystem() != null
						&& PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem()) != null) {
					if (PaymentSystemEnum.BANK.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
						paymentSystems.add(String.valueOf(PaymentSystemEnum.PPBL.getPaymentSystemKey()));
						paymentSystems.add(PaymentSystemEnum.PPBL.getPaymentSystemUserViewValue());
					}
					else if (UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(participant)) {
						paymentSystems.add(String.valueOf(PaymentSystemEnum.UPI_LITE.getPaymentSystemKey()));
						paymentSystems.add(PaymentSystemEnum.UPI_LITE.getPaymentSystemUserViewValue());
					}
					else if (Utility.isUpiViaCcTxn(participant)) {
						paymentSystems.add(String.valueOf(PaymentSystemEnum.UPI_CREDIT_CARD.getPaymentSystemKey()));
						paymentSystems.add(PaymentSystemEnum.UPI_CREDIT_CARD.getPaymentSystemUserViewValue());
						paymentSystems.add(String.valueOf(PaymentSystemEnum.UPI.getPaymentSystemKey()));
						paymentSystems.add(PaymentSystemEnum.UPI.getPaymentSystemUserViewValue());
					}
					else {
						paymentSystems.add(String
							.valueOf(PaymentSystemEnum.getEnumFromPaymentSystemKey(participant.getPaymentSystem())
								.getPaymentSystemKey()));
						paymentSystems.add(PaymentSystemEnum.getEnumFromPaymentSystemKey(participant.getPaymentSystem())
							.getPaymentSystemUserViewValue());
					}
				}
			});

		tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
	}

	private void processUPILiteTopUpAndDeRegister(final TransformedTransactionHistoryDetail tthd) {
		// Rule: upi_lite_top_up_and_de_register_handling (salience 1)
		if (tthd.getMainTxnType() != null && (TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.getTransactionTypeKey()
			.equals(tthd.getMainTxnType())
				|| TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.getTransactionTypeKey().equals(tthd.getMainTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey().equals(tthd.getMainTxnType()))) {
			Set<String> paymentSystems = tthd.getSearchFields().getSearchPaymentSystem();
			if (paymentSystems == null) {
				paymentSystems = new HashSet<>();
			}
			paymentSystems.add(String.valueOf(PaymentSystemEnum.UPI.getPaymentSystemKey()));
			paymentSystems.add(PaymentSystemEnum.UPI.getPaymentSystemUserViewValue());
			tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
			DroolsUtility.updateSearchKeywordsForSelfTransfer(tthd);
		}
	}

	private void processBankAccountId(final TransformedTransactionHistoryDetail tthd) {
		// Rule: setBankAccId (salience 6)
		if (tthd.getContextMap() == null || Utility.isUpiViaCcTxn(tthd) || tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& participant.getBankData() != null
					&& StringUtils.isNotBlank(participant.getBankData().getAccNumber())
					&& StringUtils.isNotBlank(participant.getBankData().getIfsc()))
			.findFirst()
			.ifPresent(participant -> {
				tthd.getSearchFields().setSelfDataUpdatedDate(participant.getTxnDate());
				String accNumber = participant.getBankData().getAccNumber();
				String ifsc = participant.getBankData().getIfsc();

				// For account numbers longer than 4 digits, take last 4 digits
				if (accNumber.length() > 4) {
					accNumber = accNumber.substring(accNumber.length() - 4);
				}

				tthd.getSearchFields().setBankAcctId(accNumber + "_" + ifsc);
			});
	}

	private void processFDSpecificRules(final TransformedTransactionHistoryDetail tthd) {
		// Rule: fd_specific_rules (salience 6)
		if (tthd.getContextFilterMap() == null || tthd.getContextFilterMap().get(WebConstants.REPORT_CODE) == null
				|| !WebConstants.FD_REPORT_CODES.contains(tthd.getContextFilterMap().get(WebConstants.REPORT_CODE))) {
			return;
		}

		Set<String> paymentSystems = tthd.getSearchFields().getSearchPaymentSystem();
		if (paymentSystems == null) {
			paymentSystems = new HashSet<>();
		}
		paymentSystems.add(WebConstants.FIXED_DEPOSIT);
		tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
	}

	private void processSpecialAddMoney(final TransformedTransactionHistoryDetail tthd) {
		// Rule: special_Add_Money_handling (salience 1)
		if (TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(tthd.getMainTxnType())) {
			Set<String> paymentSystems = tthd.getSearchFields().getSearchPaymentSystem();
			if (paymentSystems == null) {
				paymentSystems = new HashSet<>();
			}
			paymentSystems.add(String.valueOf(PaymentSystemEnum.WALLET.getPaymentSystemKey()));
			paymentSystems.add(PaymentSystemEnum.WALLET.getPaymentSystemUserViewValue());
			tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
		}
	}

	private void processSpecialAddAndPay(final TransformedTransactionHistoryDetail tthd) {
		// Rule: special_Add_And_Pay_handling (salience -1)
		if (TransactionTypeEnum.ADD_AND_PAY.getTransactionTypeKey().equals(tthd.getMainTxnType())) {
			Set<String> paymentSystems = tthd.getSearchFields().getSearchPaymentSystem();
			if (paymentSystems == null) {
				paymentSystems = new HashSet<>();
			}
			paymentSystems.clear();
			paymentSystems.add(String.valueOf(PaymentSystemEnum.WALLET.getPaymentSystemKey()));
			paymentSystems.add(PaymentSystemEnum.WALLET.getPaymentSystemUserViewValue());
			tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
		}
	}

	private void processGVSpecificRules(final TransformedTransactionHistoryDetail tthd) {
		// Rule: gv_specific_rules (salience 0)
		if (tthd.getParticipants() == null) {
			return;
		}

		final Set<String> paymentSystems = new HashSet<>(tthd.getSearchFields().getSearchPaymentSystem() != null
				? tthd.getSearchFields().getSearchPaymentSystem() : new HashSet<>());

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& !(ClientStatusEnum.FAILURE.getStatusKey().equals(participant.getStatus())
							&& !ClientStatusEnum.FAILURE.getStatusKey().equals(tthd.getStatus()))
					&& participant.getContextMap() != null
					&& WebConstants.GV_PURCHASED.equals(participant.getContextMap().get(WebConstants.TXN_PURPOSE)))
			.findFirst()
			.ifPresent(participant -> {
				paymentSystems.add(WebConstants.PAYTM_GIFT_VOUCHER);

				if (participant.getUpiData() != null) {
					paymentSystems.add(String.valueOf(PaymentSystemEnum.UPI.getPaymentSystemKey()));
					paymentSystems.add(PaymentSystemEnum.UPI.getPaymentSystemUserViewValue());
				}
			});
		tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
	}

	private void processSelfUPITransferSearch(final TransformedTransactionHistoryDetail tthd) {
		// Rule: self_upi_transfer_search
		if (tthd.getContextMap() != null && "true".equals(tthd.getContextMap().get("isSelfTransfer"))) {
			DroolsUtility.updateSearchKeywordsForSelfTransfer(tthd);
		}
	}

	private void processSetChannelCode(final TransformedTransactionHistoryDetail tthd) {
		// Rule: set_channel_code
		if (tthd.getContextMap() != null && tthd.getContextMap().get("channelCode") != null
				&& WebConstants.IVR.equalsIgnoreCase(tthd.getContextMap().get("channelCode"))) {
			tthd.getSearchFields().setSearchChannelCode(WebConstants.IVR);
		}
	}

	private void processSetEmiStatus(final TransformedTransactionHistoryDetail tthd) {
		// Rule: set_emi_status
		if (tthd.getContextMap() != null && Boolean.TRUE.equals(Utility.isUpiViaCcTxn(tthd))) {
			DroolsUtility.updateEmiStatus(tthd);
		}
	}

	private void processSetVerticalIdsSearch(final TransformedTransactionHistoryDetail tthd) {
		// Rule: set_vertical_ids_search
		if (tthd.getCartDetails() != null && tthd.getCartDetails().getItems() != null) {
			DroolsUtility.updateVerticalIdToSearchFields(tthd);
		}
	}

	private void processWalletAddMoneyRefund(final TransformedTransactionHistoryDetail tthd) {
		// Rule: wallet_add_money_refund (salience 10)

		final Set<String> paymentSystems = new HashSet<>(tthd.getSearchFields().getSearchPaymentSystem() != null
				? tthd.getSearchFields().getSearchPaymentSystem() : new HashSet<>());
		final Set<String> walletTypes = new HashSet<>(tthd.getSearchFields().getSearchWalletType() != null
				? tthd.getSearchFields().getSearchWalletType() : new HashSet<>());

		if (tthd.getTxnType() != null
				&& TransactionTypeEnum.ADD_MONEY_REFUND.getTransactionTypeKey().equals(tthd.getTxnType())
				&& tthd.getParticipants() != null) {
			tthd.getParticipants()
				.stream()
				.filter(participant -> PaymentSystemEnum.PG.getPaymentSystemKey()
					.equals(participant.getPaymentSystem()))
				.findFirst()
				.ifPresent(pgParticipant -> {
					if (pgParticipant.getWalletData() != null
							&& WalletType.getWalletTypeByKey(pgParticipant.getWalletData().getWalletType()) != null) {
						walletTypes.add(String
							.valueOf(WalletType.getEnumFromWalletTypeKey(pgParticipant.getWalletData().getWalletType())
								.getWalletTypeKey()));
						walletTypes.add(String
							.valueOf(WalletType.getEnumFromWalletTypeKey(pgParticipant.getWalletData().getWalletType())
								.getWalletTypeUserViewValue()));
						paymentSystems.add(String.valueOf(PaymentSystemEnum.WALLET.getPaymentSystemKey()));
						paymentSystems.add(PaymentSystemEnum.WALLET.getPaymentSystemUserViewValue());
					}
				});
			tthd.getSearchFields().setSearchWalletType(walletTypes);
			tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
		}
	}

	private void processWalletOtherUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: wallet_Other_User (salience 9)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getMobileData() != null) {
					searchFields.setSearchOtherMobileNo(participant.getMobileData().getMobileNumber());
				}
			});
	}

	private void processUPIOtherUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: upi_Other_User (salience 9)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getUpiData() != null) {
					searchFields.setSearchOtherVpa(participant.getUpiData().getVpa());
				}

				if (participant.getMobileData() != null) {
					searchFields.setSearchOtherMobileNo(participant.getMobileData().getMobileNumber());
				}
			});
	}

	private void processBankOtherUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: bank_Other_User (salience 9)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.BANK.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());
			});
	}

	private void processPGOtherUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: pg_Other_User (salience 9)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.PG.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getUpiData() != null) {
					searchFields.setSearchOtherVpa(participant.getUpiData().getVpa());
				}

				if (participant.getMobileData() != null) {
					searchFields.setSearchOtherMobileNo(participant.getMobileData().getMobileNumber());
				}
			});
	}

	private void processPPBLOtherUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: ppbl_Other_User (salience 9)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getMobileData() != null) {
					searchFields.setSearchOtherMobileNo(participant.getMobileData().getMobileNumber());
				}
			});
	}

	private void processWalletOtherMerchantParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: wallet_Other_Merchant (salience 8)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getMerchantData() != null && UthCategoryEnum
					.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory()) != null) {
					Set<String> merchantCategories = searchFields.getSearchUthMerchantCategory();
					if (merchantCategories == null) {
						merchantCategories = new HashSet<>();
					}
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryId());
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryUserView());
					searchFields.setSearchUthMerchantCategory(merchantCategories);
				}
			});
	}

	private void processUPIOtherMerchantParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: upi_Other_Merchant (salience 8)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getUpiData() != null) {
					searchFields.setSearchOtherVpa(participant.getUpiData().getVpa());
				}

				if (participant.getMerchantData() != null && UthCategoryEnum
					.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory()) != null) {
					Set<String> merchantCategories = searchFields.getSearchUthMerchantCategory();
					if (merchantCategories == null) {
						merchantCategories = new HashSet<>();
					}
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryId());
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryUserView());
					searchFields.setSearchUthMerchantCategory(merchantCategories);
				}
			});
	}

	private void processBankOtherMerchantParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: bank_Other_Merchant (salience 8)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.BANK.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getMerchantData() != null && UthCategoryEnum
					.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory()) != null) {
					Set<String> merchantCategories = searchFields.getSearchUthMerchantCategory();
					if (merchantCategories == null) {
						merchantCategories = new HashSet<>();
					}
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryId());
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryUserView());
					searchFields.setSearchUthMerchantCategory(merchantCategories);
				}
			});
	}

	private void processPGOtherMerchantParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: pg_Other_Merchant (salience 8)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.PG.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getMerchantData() != null && UthCategoryEnum
					.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory()) != null) {
					Set<String> merchantCategories = searchFields.getSearchUthMerchantCategory();
					if (merchantCategories == null) {
						merchantCategories = new HashSet<>();
					}
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryId());
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryUserView());
					searchFields.setSearchUthMerchantCategory(merchantCategories);
				}
			});
	}

	private void processPPBLOtherMerchantParticipant(final TransformedTransactionHistoryDetail tthd) {
		// Rule: ppbl_Other_Merchant (salience 8)
		if (tthd.getParticipants() == null) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())
					&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())
					&& PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(participant.getPaymentSystem()))
			.forEach(participant -> {
				SearchFields searchFields = tthd.getSearchFields();
				searchFields.setOtherDataUpdatedDate(participant.getTxnDate());
				searchFields.setSearchOtherName(participant.getName());

				if (participant.getMerchantData() != null && UthCategoryEnum
					.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory()) != null) {
					Set<String> merchantCategories = searchFields.getSearchUthMerchantCategory();
					if (merchantCategories == null) {
						merchantCategories = new HashSet<>();
					}
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryId());
					merchantCategories
						.add(UthCategoryEnum.getEnumFromUthCategoryId(participant.getMerchantData().getUthCategory())
							.getUthCategoryUserView());
					searchFields.setSearchUthMerchantCategory(merchantCategories);
				}
			});
	}

	private void processUPILinkedPPBLAccounts(final TransformedTransactionHistoryDetail tthd) {
		// Rule: upi_linked_ppbl_accounts_handling
		if (tthd.getParticipants() == null) {
			return;
		}

		final Set<String> paymentSystems = new HashSet<>(tthd.getSearchFields().getSearchPaymentSystem() != null
				? tthd.getSearchFields().getSearchPaymentSystem() : new HashSet<>());

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& Utility.isTxnApplicableForUpiLinkedPpblHandling(tthd, participant)
					&& participant.getBankData() != null
					&& BankDataConstants.PAYTM_BANK_IFSC.equalsIgnoreCase(participant.getBankData().getIfsc()))
			.forEach(participant -> {
				paymentSystems.add(String.valueOf(PaymentSystemEnum.PPBL.getPaymentSystemKey()));
				paymentSystems.add(PaymentSystemEnum.PPBL.getPaymentSystemUserViewValue());
			});
		tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
	}

	private void processPGDebitCardTransactions(final TransformedTransactionHistoryDetail tthd) {
		// Rule: pg_debit_card_txns_handling
		if (tthd.getParticipants() == null) {
			return;
		}

		final Set<String> paymentSystems = new HashSet<>(tthd.getSearchFields().getSearchPaymentSystem() != null
				? tthd.getSearchFields().getSearchPaymentSystem() : new HashSet<>());

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId())
					&& PaymentSystemEnum.PG.getPaymentSystemKey().equals(participant.getPaymentSystem())
					&& participant.getCardData() != null
					&& StringUtils.equalsIgnoreCase(
							IfscUtility.getBankNameWithCamelCase(BankDataConstants.PAYTM_BANK_IFSC),
							participant.getCardData().getCardIssuer()))
			.forEach(participant -> {
				paymentSystems.add(String.valueOf(PaymentSystemEnum.PPBL.getPaymentSystemKey()));
				paymentSystems.add(PaymentSystemEnum.PPBL.getPaymentSystemUserViewValue());
			});
		tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
	}

	private void processPaytmPostpaidRepayment(final TransformedTransactionHistoryDetail tthd) {
		// Rule: paytm_postpaid_repayment_special_handling
		if (tthd.getParticipants() == null) {
			return;
		}

		final Set<String> paymentSystems = new HashSet<>(tthd.getSearchFields().getSearchPaymentSystem() != null
				? tthd.getSearchFields().getSearchPaymentSystem() : new HashSet<>());

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null
					&& TransactionTypeEnum.P2M.getTransactionTypeKey().equals(tthd.getMainTxnType())
					&& !(tthd.getEntityId().equals(participant.getEntityId())
							&& tthd.getTxnIndicator().equals(participant.getTxnIndicator()))
					&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())
					&& participant.getEntityId() != null
					&& POSTPAID_REPAYMENT_MERCHANT_IDS.contains(participant.getEntityId()))
			.forEach(participant -> {
				paymentSystems.add(String.valueOf(PaymentSystemEnum.PAYTM_POSTPAID.getPaymentSystemKey()));
				paymentSystems.add(PaymentSystemEnum.PAYTM_POSTPAID.getPaymentSystemUserViewValue());
			});
		tthd.getSearchFields().setSearchPaymentSystem(paymentSystems);
	}

	private void processUPIViaCCBankAccountId(final TransformedTransactionHistoryDetail tthd) {
		// Rule: upi_Via_Cc_Bank_Acct_Id_Special_Handling
		if (tthd.getParticipants() == null || tthd.getContextMap() == null || !Utility.isUpiViaCcTxn(tthd)) {
			return;
		}

		tthd.getParticipants()
			.stream()
			.filter(participant -> tthd.getEntityId() != null && tthd.getEntityId().equals(participant.getEntityId()))
			.findFirst()
			.ifPresent(participant -> {
				tthd.getSearchFields().setSelfDataUpdatedDate(participant.getTxnDate());
				if (participant.getCardData() != null
						&& StringUtils.isNotBlank(participant.getCardData().getCardNum())) {
					tthd.getSearchFields().setBankAcctId(participant.getCardData().getCardNum());
				}
			});
	}

}