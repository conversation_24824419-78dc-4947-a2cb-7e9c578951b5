package com.org.panaroma.web;

import com.org.panaroma.commons.cache.dto.ListingSpecificCacheData;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.web.cache.AerospikeCacheClient;
import com.org.panaroma.web.cache.ICacheClient;
import com.org.panaroma.web.client.oauth.IOauthClient;
import com.org.panaroma.web.dto.*;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.repo.EsProgressiveRecordsSearchingRepo;
import com.org.panaroma.web.service.DefaultTransactionHistoryService;
import com.org.panaroma.web.utility.Util;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.util.*;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesServiceScheduler;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import reactor.core.publisher.Mono;

import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE;
import static com.org.panaroma.commons.constants.WebConstants.*;
import static com.org.panaroma.web.monitoring.MonitoringConstants.*;
import static org.mockito.ArgumentMatchers.*;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PanaromaWebApp.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ListingCacheTest {

	@MockBean
	ConfigurablePropertiesServiceScheduler configurablePropertiesServiceScheduler;

	@Mock
	ConfigurablePropertiesHolder configurablePropertiesHolder;

	@MockBean
	IOauthClient oauthClient;

	@MockBean
	AerospikeCacheClient iCacheClient;

	@MockBean
	IKafkaClient iKafkaClient;

	@MockBean
	MetricsAgent metricsAgent;

	@MockBean
	EsProgressiveRecordsSearchingRepo repo;

	@Autowired
	@InjectMocks
	DefaultTransactionHistoryService defaultTransactionHistoryService;

	// Below 2 maps are treated as caches in place of actual listing & upiPassbook caches
	static Map<String, ListingSpecificCacheData> mockedListingCacheClient;

	static Map<String, ListingSpecificCacheData> mockedUpiPassbookListingCacheClient;

	Map<String, String> listingRequestParamMap;

	Map<String, String> upiPassbookListingRequestParamMap;

	Map<String, String> tokens = new HashMap<>();

	RepoResponseSearchApiDto repoResponseSearchApiDto = new RepoResponseSearchApiDto();

	boolean isServedFromListingCache;

	boolean isServedFromUpiPassbookCache;

	@Before
	public void init() throws Exception {
		mockedListingCacheClient = new HashMap<>();

		mockedUpiPassbookListingCacheClient = new HashMap<>();

		Mockito.doAnswer(invocationOnMock -> {
			Object[] args = invocationOnMock.getArguments();
			SearchContext searchContext = (SearchContext) args[0];
			return mockedListingCacheClient.get(searchContext.getEntityId());
		}).when(iCacheClient).getNtuCacheData(any(), eq(CacheInfo.UTH_NTU_CACHE));

		Mockito.doAnswer(invocationOnMock -> {
			Object[] args = invocationOnMock.getArguments();
			ListingSpecificCacheData listingSpecificCacheData = new ListingSpecificCacheData(
					((CacheUpdaterKafkaDto) args[2]).getListingData(), null, System.currentTimeMillis(), null, null);
			mockedListingCacheClient.put((String) args[1], listingSpecificCacheData);
			return null;
		}).when(iKafkaClient).pushIntoKafka(any(), eq("listingCacheTestEntityId"), any());

		Mockito.doAnswer(invocationOnMock -> {
			Object[] args = invocationOnMock.getArguments();
			SearchContext searchContext = (SearchContext) args[0];
			return mockedUpiPassbookListingCacheClient.get(searchContext.getEntityId());
		}).when(iCacheClient).getNtuCacheData(any(), eq(CacheInfo.UPI_PASSBOOK_LISTING_CACHE));

		Mockito.doAnswer(invocationOnMock -> {
			Object[] args = invocationOnMock.getArguments();
			ListingSpecificCacheData listingSpecificCacheData = new ListingSpecificCacheData(
					((CacheUpdaterKafkaDto) args[2]).getListingData(), null, System.currentTimeMillis(), null, null);
			mockedUpiPassbookListingCacheClient.put((String) args[1], listingSpecificCacheData);
			return null;
		}).when(iKafkaClient).pushIntoKafka(any(), eq("upiPassbookCacheTestEntityId"), any());

		Mockito.doAnswer(invocationOnMock -> {
			isServedFromListingCache = true;
			return null;
		})
			.when(metricsAgent)
			.incrementCount(LISTING_REQUEST_SERVED_FROM_CACHE, CACHE + COLON + CacheInfo.UTH_NTU_CACHE);

		Mockito.doAnswer(invocationOnMock -> {
			isServedFromUpiPassbookCache = true;
			return null;
		})
			.when(metricsAgent)
			.incrementCount(LISTING_REQUEST_SERVED_FROM_CACHE, CACHE + COLON + CacheInfo.UPI_PASSBOOK_LISTING_CACHE);

		CustomOauthUserDetailsResponse customOauthUserDetailsResponseForListingCacheTest = new CustomOauthUserDetailsResponse();
		customOauthUserDetailsResponseForListingCacheTest.setUserId("listingCacheTestEntityId");

		CustomOauthUserDetailsResponse customOauthUserDetailsResponseForUpiPassbookCacheTest = new CustomOauthUserDetailsResponse();
		customOauthUserDetailsResponseForUpiPassbookCacheTest.setUserId("upiPassbookCacheTestEntityId");

		Mockito.when(oauthClient.getUserDetailsFromOauth(eq("tokenForListingCacheTest"), any(), any()))
			.thenReturn(Mono.just(customOauthUserDetailsResponseForListingCacheTest));
		Mockito.when(oauthClient.getUserDetailsFromOauth(eq("tokenForUpiPassbookCacheTest"), any(), any()))
			.thenReturn(Mono.just(customOauthUserDetailsResponseForUpiPassbookCacheTest));

		List<TransformedTransactionHistoryDetail> tthdList = Util.getTthdListFromFile("addAndPay");

		for (TransformedTransactionHistoryDetail tthd : tthdList) {
			tthd.setTxnDate(Calendar.getInstance().getTimeInMillis());
		}
		PaginationParams paginationParams = new PaginationParams();
		paginationParams.setPaginationTxnId("abc");
		paginationParams.setPaginationStreamSource("PG");
		paginationParams.setTransactionDateEpoch("12345678");
		paginationParams.setPageNo(2);
		repoResponseSearchApiDto.setPaginationParams(paginationParams);
		repoResponseSearchApiDto.setTotalHits(2L);
		repoResponseSearchApiDto.setTransformedTransactionHistoryDetailsIterable(tthdList);
		Mockito.when(repo.search(any(), any(), any())).thenReturn(Mono.just(repoResponseSearchApiDto));
		Mockito.when(configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class))
			.thenReturn("2022-01-01 00:00:00.000 +0530");

		// Populate paramMap
		listingRequestParamMap = new HashMap<>();
		listingRequestParamMap.put("pageNo", "1");
		listingRequestParamMap.put("pageSize", "20");
		listingRequestParamMap.put("playStore", "true");
		listingRequestParamMap.put("lang_id", "1");
		listingRequestParamMap.put("language", "en");
		listingRequestParamMap.put("locale", "en-IN");
		listingRequestParamMap.put("deviceName", "IN2011");
		listingRequestParamMap.put("version", "10.4.0");
		listingRequestParamMap.put("long", "72.9735432");
		listingRequestParamMap.put("deviceIdentifier", "OnePlus-IN2011-73d5387d6df032e1");
		listingRequestParamMap.put("osVersion", "11");
		listingRequestParamMap.put("client", "androidapp");
		listingRequestParamMap.put("deviceManufacturer", "OnePlus");
		listingRequestParamMap.put("networkType", "4G");
		listingRequestParamMap.put("lat", "26.2487896");
		listingRequestParamMap.put("child_site_id", "1");
		listingRequestParamMap.put("site_id", "1");

		upiPassbookListingRequestParamMap = new HashMap<>();
		upiPassbookListingRequestParamMap.put("pageNo", "1");
		upiPassbookListingRequestParamMap.put("filterApplied", "true");
		upiPassbookListingRequestParamMap.put("passbookFilter", "true");
		upiPassbookListingRequestParamMap.put("paymentSystem", "UPI");
		upiPassbookListingRequestParamMap.put("upiIdentifier", "6148_SBIN0000572");
		upiPassbookListingRequestParamMap.put("pageSize", "20");

	}

	public Iterable<TransformedTransactionHistoryDetail> getHistoryDetailsIterable(
			final List<TransformedTransactionHistoryDetail> tthd) {

		return () -> new Iterator<>() {
			Iterator<TransformedTransactionHistoryDetail> searchHitIterator = tthd.iterator();

			@Override
			public boolean hasNext() {
				return searchHitIterator.hasNext();
			}

			@Override
			public TransformedTransactionHistoryDetail next() {
				return searchHitIterator.next();
			}
		};
	}

	@Test
	public void listingCacheTest() {
		tokens.put("Authorization", "{userToken=tokenForListingCacheTest}");
		testHappyCase();
		testNullPaginationParamCase();
		testNullListingDataCase();
	}

	@Test
	public void upiPassbookCacheTest() {
		tokens.put("Authorization", "{userToken=tokenForUpiPassbookCacheTest}");
		normalUpiPassbookTest();
		upiPassbookFilterTest();
		upiPassbookFilterAppliedWithOtherFiltersTest();
	}

	private void testHappyCase() {
		defaultTransactionHistoryService.search(listingRequestParamMap, tokens).block();
		// Check if data got cached.
		Assert.assertNotNull(mockedListingCacheClient.get("listingCacheTestEntityId"));
		defaultTransactionHistoryService.search(listingRequestParamMap, tokens).block();
		// Check if response got served from cache.
		Assert.assertTrue(isServedFromListingCache);
		System.out.println("Test executed successfully");
	}

	private void testNullPaginationParamCase() {
		// Clear cache.
		mockedListingCacheClient.clear();
		// reset isServedFromCache flag
		isServedFromListingCache = false;
		repoResponseSearchApiDto.setPaginationParams(new PaginationParams());
		defaultTransactionHistoryService.search(listingRequestParamMap, tokens).block();
		// Check if data got cached.
		Assert.assertNotNull(mockedListingCacheClient.get("listingCacheTestEntityId"));
		defaultTransactionHistoryService.search(listingRequestParamMap, tokens).block();
		// Check if response got served from cache.
		Assert.assertTrue(isServedFromListingCache);
	}

	/*
	 * This test case was added as initially first parameter for BeanUtils.copyProperties
	 * method was null & 4003 error was being thrown whenever such case was being
	 * encountered. This was later handled & if in future this case again arises, this
	 * test case will fail
	 */
	private void testNullListingDataCase() {
		isServedFromListingCache = false;
		ListingSpecificCacheData listingSpecificCacheData = new ListingSpecificCacheData();
		listingSpecificCacheData.setCreatedDate(System.currentTimeMillis());
		mockedListingCacheClient.put("listingCacheTestEntityId", listingSpecificCacheData);
		defaultTransactionHistoryService.search(listingRequestParamMap, tokens).block();
		// Check if response got served from cache.
		Assert.assertFalse(isServedFromListingCache);
	}

	public void normalUpiPassbookTest() {

		defaultTransactionHistoryService.search(upiPassbookListingRequestParamMap, tokens).block();
		// Check if data got cached.
		Assert.assertNotNull(mockedUpiPassbookListingCacheClient.get("upiPassbookCacheTestEntityId"));
		defaultTransactionHistoryService.search(upiPassbookListingRequestParamMap, tokens).block();
		// Check if response got served from cache.
		Assert.assertTrue(isServedFromUpiPassbookCache);
	}

	public void upiPassbookFilterTest() {
		upiPassbookListingRequestParamMap.remove("passbookFilter", "true");
		// Clear cache.
		mockedUpiPassbookListingCacheClient.clear();
		// reset isServedFromCache flag
		isServedFromUpiPassbookCache = false;
		defaultTransactionHistoryService.search(upiPassbookListingRequestParamMap, tokens).block();
		// Check if data got cached.
		Assert.assertNotNull(mockedUpiPassbookListingCacheClient.get("upiPassbookCacheTestEntityId"));
		defaultTransactionHistoryService.search(upiPassbookListingRequestParamMap, tokens).block();
		// Check if response got served from cache.
		Assert.assertTrue(isServedFromUpiPassbookCache);
	}

	public void upiPassbookFilterAppliedWithOtherFiltersTest() {
		upiPassbookListingRequestParamMap.remove("passbookFilter", "true");
		upiPassbookListingRequestParamMap.put("status", "success");
		// Clear cache.
		mockedUpiPassbookListingCacheClient.clear();
		// reset isServedFromCache flag
		isServedFromUpiPassbookCache = false;
		defaultTransactionHistoryService.search(upiPassbookListingRequestParamMap, tokens).block();
		// Check if data got cached.
		Assert.assertNull(mockedUpiPassbookListingCacheClient.get("upiPassbookCacheTestEntityId"));
		defaultTransactionHistoryService.search(upiPassbookListingRequestParamMap, tokens).block();
		// Response should not be served from cache as it is not pushed to cache.
		Assert.assertFalse(isServedFromUpiPassbookCache);
	}

}