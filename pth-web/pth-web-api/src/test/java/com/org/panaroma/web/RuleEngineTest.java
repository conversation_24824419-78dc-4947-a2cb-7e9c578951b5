package com.org.panaroma.web;

import static com.org.panaroma.commons.constants.WebConstants.CHAT_CTA_LABEL;
import static com.org.panaroma.commons.constants.WebConstants.CUSTOM_SECOND_PARTY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.FAILED;
import static com.org.panaroma.commons.constants.WebConstants.PENDING;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;
import static com.org.panaroma.web.utility.Util.getDetailResponseV2FromFile;
import static com.org.panaroma.web.utility.Util.getEsResponseTxnFromFile;
import static com.org.panaroma.web.utility.Util.getTTHDFromFile;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.constants.CommonConstants;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.RefundDetails;
import com.org.panaroma.commons.dto.es.TransformedBankData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.es.TransformedUPIData;
import com.org.panaroma.commons.dto.es.TransformedWalletData;
import com.org.panaroma.web.detailAPI.P2pOutwardDetailResponseBuilder;
import com.org.panaroma.web.dto.cstData.CstDto;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.CtaNode;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailResponseV2Mapper;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.InstrumentDtoV2;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Arrays;
import java.util.Map;

import com.org.panaroma.commons.service.AsyncDataProcessor;
import com.org.panaroma.rule.engine.service.RuleEngineService;
import com.org.panaroma.web.cache.AerospikeCacheClient;
import com.org.panaroma.web.config.KafkaGroupingProducer;
import com.org.panaroma.web.detailAPI.CashBackDetailResponseBuilder;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.SecondPartyInfo;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.ParticipantInfo;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailApiResponseV3;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.Util;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesServiceScheduler;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import static com.org.panaroma.commons.constants.WebConstants.*;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO_ONE;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.MANDATE_AMOUNT;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = PanaromaWebApp.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RuleEngineTest {

	@MockBean
	AerospikeCacheClient aerospikeCacheClient;

	@MockBean
	KafkaGroupingProducer kafkaGroupingProducer;

	@MockBean
	AsyncDataProcessor asyncDataProcessor;

	@MockBean
	ConfigurablePropertiesServiceScheduler configurablePropertiesServiceScheduler;

	@Autowired
	RuleEngineService ruleEngineService;

	@Autowired
	CashBackDetailResponseBuilder cashBackDetailResponseBuilder;

	@Test
	public void listingCustomNarrationTest() throws Exception {
		TransformedTransactionHistoryDetail listingVisibleTxn = Util.getTTHDFromFile("PPBLNeftOutward");
		EsResponseTxn response = new EsResponseTxn();
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));

		ruleEngineService.applyRules(listingVisibleTxn, response);

		Assert.assertEquals("Money sent to", response.getNarration());
		Assert.assertEquals("dummy name", response.getSecondPartyInfo().getName());
		Assert.assertEquals("dummy url", response.getSecondPartyInfo().getLogoUrl());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.OTHER_DEBIT.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put("reportCode", "91034");
		listingVisibleTxn.getParticipants().get(0).getContextMap().put("benefName", "BDPaytm Money");

		ruleEngineService.applyRules(listingVisibleTxn, response);

		Assert.assertEquals("Automatic payment to", response.getNarration());
		Assert.assertEquals("Paytm Money", response.getSecondPartyInfo().getName());
		Assert.assertEquals("https://static.orgk.com/uth/images/merchant-logo/paytm-money.png",
				response.getSecondPartyInfo().getLogoUrl());
	}

	@Test
	public void listingCustomNarrationP2pOutwardTest() throws Exception {
		TransformedTransactionHistoryDetail listingVisibleTxn = createTransformedTransactionHistoryDetailBuilder();
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		EsResponseTxn response = new EsResponseTxn();
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));

		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money sent to", response.getNarration());

		response.setNarration("Random Narration");
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.SUCCESS.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money sent to", response.getNarration());

		response.setNarration("Random Narration");
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.PENDING.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money transfer pending to", response.getNarration());

		response.setNarration("Random Narration");
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.FAILURE.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money transfer failed to", response.getNarration());

		response.setNarration("Random Narration");
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.SUCCESS.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money sent to", response.getNarration());

		response.setNarration("Random Narration");
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.PENDING.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money transfer pending to", response.getNarration());

		response.setNarration("Random Narration");
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.FAILURE.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money transfer failed to", response.getNarration());

		response.setNarration("Random Narration");
		listingVisibleTxn.setContextMap(new HashMap<>());
		for (TransformedParticipant tp : listingVisibleTxn.getParticipants()) {
			if (tp.getContextMap() == null) {
				tp.setContextMap(new HashMap<>());
			}
			if (listingVisibleTxn.getEntityId().equals(tp.getEntityId())) {
				tp.getContextMap().put("txn_purpose", "GV_PURCHASED");
			}
		}
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.SUCCESS.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Random Narration", response.getNarration());

		response.setNarration("Random Narration");
		for (TransformedParticipant tp : listingVisibleTxn.getParticipants()) {
			if (listingVisibleTxn.getEntityId().equals(tp.getEntityId())) {
				if (tp.getContextMap() == null) {
					tp.setContextMap(new HashMap<>());
				}
				tp.getContextMap().put("txn_purpose", "GV_PURCHASED");
			}
		}
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.PENDING.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Random Narration", response.getNarration());

		response.setNarration("Random Narration");
		for (TransformedParticipant tp : listingVisibleTxn.getParticipants()) {
			if (listingVisibleTxn.getEntityId().equals(tp.getEntityId())) {
				if (tp.getContextMap() == null) {
					tp.setContextMap(new HashMap<>());
				}
				tp.getContextMap().put("txn_purpose", "GV_PURCHASED");
			}
		}
		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.FAILURE.getStatusKey());
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Random Narration", response.getNarration());
	}

	@Test
	public void detailV1CustomNarrationTest() throws Exception {
		TransformedTransactionHistoryDetail listingVisibleTxn = Util.getTTHDFromFile("PPBLNeftOutward");
		DetailApiResponse response = new DetailApiResponse();
		InstrumentDto firstInst = new InstrumentDto();
		firstInst.setName("Random Name");
		firstInst.setLogoUrl("Dummy Url");
		response.setFirstInstrument(Arrays.asList(firstInst));
		InstrumentDto secInst = new InstrumentDto();
		secInst.setInstrumentDetail("no acct number");
		response.setSecondInstrument(Arrays.asList(secInst));
		CstDto cstDto = new CstDto();
		cstDto.setLabel("Cst Label");
		response.setCstorderItem(cstDto);

		ruleEngineService.applyRules(listingVisibleTxn, response);

		Assert.assertEquals("Random Name", response.getFirstInstrument().get(0).getName());
		Assert.assertEquals("Dummy Url", response.getFirstInstrument().get(0).getLogoUrl());

		listingVisibleTxn.getContextMap().put("reportCode", "91034");
		listingVisibleTxn.getParticipants().get(0).getContextMap().put("benefName", "BDPaytm Money");

		ruleEngineService.applyRules(listingVisibleTxn, response);

		Assert.assertEquals("Paytm Money", response.getFirstInstrument().get(0).getName());
		Assert.assertEquals("https://static.orgk.com/uth/images/merchant-logo/paytm-money.png",
				response.getFirstInstrument().get(0).getLogoUrl());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		DetailApiResponseV2 responseV2 = new DetailApiResponseV2();
		responseV2 = DetailResponseV2Mapper.getDetailResponseV2(response, listingVisibleTxn, null, "DetailV2");
		ruleEngineService.applyRules(listingVisibleTxn, responseV2);
		Assert.assertEquals("Sent Successfully", responseV2.getDetailNarration());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.FAILURE.getStatusKey());
		responseV2 = DetailResponseV2Mapper.getDetailResponseV2(response, listingVisibleTxn, null, "DetailV2");
		ruleEngineService.applyRules(listingVisibleTxn, responseV2);
		Assert.assertEquals("Transfer Failed", responseV2.getDetailNarration());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.PENDING.getStatusKey());
		responseV2 = DetailResponseV2Mapper.getDetailResponseV2(response, listingVisibleTxn, null, "DetailV2");
		ruleEngineService.applyRules(listingVisibleTxn, responseV2);
		Assert.assertEquals("Transfer Pending", responseV2.getDetailNarration());
	}

	@Test
	public void gvPuchasedForOthersChatRedirectCtaTest() throws Exception {
		TransformedTransactionHistoryDetail walletTxn = Util.getTTHDFromFile("GvPuchasedForOthersWalletEvent");
		// TransformedTransactionHistoryDetail walletTxn =
		// Util.getTTHDFromFile("p2mWalletTxn");
		P2pOutwardDetailResponseBuilder builder = new P2pOutwardDetailResponseBuilder();
		DetailApiResponse response = builder.getResponse(Arrays.asList(walletTxn), walletTxn.getTxnId(), null);
		DetailApiResponseV2 detailApiResponseV2 = DetailResponseV2Mapper.getDetailResponseV2(response, walletTxn, null,
				null);
		ruleEngineService.applyRules(walletTxn, detailApiResponseV2);
		Assert.assertNotNull(detailApiResponseV2);
		Assert.assertNotNull(detailApiResponseV2.getChatRedirectInfo());
		Assert.assertEquals(CHAT_CTA_LABEL, detailApiResponseV2.getChatRedirectInfo().getLabel());
		Assert.assertEquals(
				"paytmmp://chat?featuretype=start_chat&userType=CUSTOMER&custId=190077351&custName=DEEPESH&custPhone=8377076406",
				detailApiResponseV2.getChatRedirectInfo().getUrl());
		Assert.assertNotNull(detailApiResponseV2.getCtasMap());
		Assert.assertNotNull(detailApiResponseV2.getCtasMap().get("CHAT_PROFILE"));
		CtaNode chatNode = detailApiResponseV2.getCtasMap().get("CHAT_PROFILE");
		Assert.assertEquals("CHAT_PROFILE", chatNode.getCtaType());
		Assert.assertEquals(CHAT_CTA_LABEL, chatNode.getLabel());
		Assert.assertEquals("PARAMS", chatNode.getValueType());
		Assert.assertNotNull(chatNode.getValue());
		Assert.assertEquals("190077351", ((HashMap) chatNode.getValue()).get("identifier"));
		Assert.assertEquals("DEEPESH", ((HashMap) chatNode.getValue()).get("name"));
		Assert.assertEquals("8377076406", ((HashMap) chatNode.getValue()).get("mobileNo"));
	}

	@Test
	public void listingLogoOrderForWalletCashbackTest() throws Exception {
		TransformedTransactionHistoryDetail tthd = Util.getTTHDFromFile("WalletCashback");
		EsResponseTxn response = new EsResponseTxn();
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(tthd, response);
		Assert.assertEquals(tthd.getContextMap().get(CUSTOM_SECOND_PARTY_LOGO),
				response.getSecondPartyInfo().getLogoOrder().get(0).getValue());

		// If customSecondPartyLogo field of contextMap is empty.
		// Logo order will be null.
		tthd.getContextMap().put(CUSTOM_SECOND_PARTY_LOGO, "");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(tthd, response);
		Assert.assertNull(response.getSecondPartyInfo().getLogoOrder());
		// Default logo will be there.
		Assert.assertNotNull(response.getSecondPartyInfo().getLogoUrl());

		// If customSecondPartyLogo is not present in context map.
		// Logo order will be null.
		tthd.getContextMap().remove(CUSTOM_SECOND_PARTY_LOGO);
		response = new EsResponseTxn();
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(tthd, response);
		Assert.assertNull(response.getSecondPartyInfo().getLogoOrder());
		// Default logo will be there.
		Assert.assertNotNull(response.getSecondPartyInfo().getLogoUrl());

	}

	@Test
	public void detailLogoOrderForWalletCashback() throws Exception {

		TransformedTransactionHistoryDetail tthd = Util.getTTHDFromFile("WalletCashback");
		DetailApiResponse response = cashBackDetailResponseBuilder.getResponse(Arrays.asList(tthd), tthd.getTxnId(),
				null);
		ruleEngineService.applyRules(tthd, response);
		Assert.assertEquals(tthd.getContextMap().get(CUSTOM_SECOND_PARTY_LOGO),
				response.getFirstInstrument().get(0).getLogoOrder().get(0).getValue());

		// If customSecondPartyLogo field of contextMap is empty.
		// Logo order will be null.
		tthd.getContextMap().put(CUSTOM_SECOND_PARTY_LOGO, "");
		response = cashBackDetailResponseBuilder.getResponse(Arrays.asList(tthd), tthd.getTxnId(), null);
		ruleEngineService.applyRules(tthd, response);
		Assert.assertNull(response.getFirstInstrument().get(0).getLogoOrder());
		// Default logoUrl will be there.
		Assert.assertNotNull(response.getFirstInstrument().get(0).getLogoUrl());

		// If customSecondPartyLogo is not present in context map.
		// Logo order will be null.
		tthd.getContextMap().remove(CUSTOM_SECOND_PARTY_LOGO);
		response = cashBackDetailResponseBuilder.getResponse(Arrays.asList(tthd), tthd.getTxnId(), null);
		ruleEngineService.applyRules(tthd, response);
		Assert.assertNull(response.getFirstInstrument().get(0).getLogoOrder());
		// Default logoUrl will be there.
		Assert.assertNotNull(response.getFirstInstrument().get(0).getLogoUrl());
	}

	@Test
	public void droolsAcctNumMaskingTest() {
		TransformedTransactionHistoryDetail listingVisibleTxn = new TransformedTransactionHistoryDetail();
		DetailApiResponse detailApiResponse = new DetailApiResponse();
		InstrumentDto firstInst = new InstrumentDto();
		firstInst.setInstrumentDetail("Masked Acct Number %{AcctNum : *********}");
		ParticipantInfo participantInfo = new ParticipantInfo();
		TransformedBankData bankData = new TransformedBankData();
		bankData.setAccNumber("********");
		TransformedParticipant participant = new TransformedParticipant();
		participant.setBankData(bankData);
		participantInfo.setParticipant(participant);
		firstInst.setParticipantInfo(participantInfo);
		detailApiResponse.setFirstInstrument(Arrays.asList(firstInst));

		InstrumentDto secInst = new InstrumentDto();
		secInst.setInstrumentDetail("no acct number");
		detailApiResponse.setSecondInstrument(Arrays.asList(secInst));
		ruleEngineService.applyRules(listingVisibleTxn, detailApiResponse);

		Assert.assertEquals("Masked Acct Number - 6789", firstInst.getInstrumentDetail());
		Assert.assertEquals("no acct number", secInst.getInstrumentDetail());
	}

	@Test
	public void testRefundDetailsInDetailApiResponseV3() throws IOException {
		final TransformedTransactionHistoryDetail tthd = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("p2mPGTxnThroughWallet", TransformedTransactionHistoryDetail.class);
		final DetailApiResponseV3 detailApiResponseV3 = new DetailApiResponseV3();
		detailApiResponseV3.setSecondInstrument(new ArrayList<>());

		ruleEngineService.applyRules(tthd, detailApiResponseV3);

		Assert.assertNull(detailApiResponseV3.getRefundDetails());

		List<RefundDetails> refundDetailsList = new ArrayList<>();
		RefundDetails refundDetails = RefundDetails.builder()
			.amount(6000l)
			.txnId("PTM0*********")
			.status(2)
			.orderId("Order")
			.date(1670572113l)
			.build();
		refundDetailsList.add(refundDetails);
		tthd.setRefundDetails(refundDetailsList);

		ruleEngineService.applyRules(tthd, detailApiResponseV3);

		Assert.assertNotNull(detailApiResponseV3.getRefundDetails());
		Assert.assertEquals("60.00", detailApiResponseV3.getRefundDetails().get(0).getAmount());
		Assert.assertEquals(tthd.getRefundDetails().get(0).getTxnId(),
				detailApiResponseV3.getRefundDetails().get(0).getTxnId());
		Assert.assertEquals("2022-12-09 13:18:33", detailApiResponseV3.getRefundDetails().get(0).getDateTime());
		Assert.assertEquals("SUCCESS", detailApiResponseV3.getRefundDetails().get(0).getStatus());
		Assert.assertEquals("PG", detailApiResponseV3.getRefundDetails().get(0).getTxnSource());
	}

	@Test
	public void testRefundDetailsForFailedStatus() throws IOException {
		final TransformedTransactionHistoryDetail tthd = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("p2mPGTxnThroughWallet", TransformedTransactionHistoryDetail.class);
		final DetailApiResponseV3 detailApiResponseV3 = new DetailApiResponseV3();
		detailApiResponseV3.setSecondInstrument(new ArrayList<>());

		List<RefundDetails> refundDetailsList = new ArrayList<>();
		RefundDetails refundDetails = RefundDetails.builder()
			.amount(6000l)
			.txnId("PTM0*********")
			.status(1)
			.orderId("Order")
			.date(1670572113l)
			.build();
		refundDetailsList.add(refundDetails);
		tthd.setRefundDetails(refundDetailsList);

		ruleEngineService.applyRules(tthd, detailApiResponseV3);

		Assert.assertEquals(new ArrayList<>(), detailApiResponseV3.getRefundDetails());
	}

	@Test
	public void testMultipleRefundDetailsInDetailApiResponseV3() throws IOException {
		final TransformedTransactionHistoryDetail tthd = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("p2mPGTxnThroughWallet", TransformedTransactionHistoryDetail.class);
		final DetailApiResponseV3 detailApiResponseV3 = new DetailApiResponseV3();
		detailApiResponseV3.setSecondInstrument(new ArrayList<>());

		ruleEngineService.applyRules(tthd, detailApiResponseV3);

		Assert.assertNull(detailApiResponseV3.getRefundDetails());

		RefundDetails refundDetails = RefundDetails.builder()
			.amount(6000l)
			.txnId("PTM0*********")
			.status(2)
			.orderId("Order")
			.date(1670572113l)
			.build();

		RefundDetails refundDetails1 = RefundDetails.builder()
			.amount(6200l)
			.txnId("PTM0*********q")
			.status(2)
			.orderId("Order1")
			.date(1670581903l)
			.build();

		List<RefundDetails> refundDetailsList = new ArrayList<>();
		refundDetailsList.add(refundDetails);
		refundDetailsList.add(refundDetails1);
		tthd.setRefundDetails(refundDetailsList);

		ruleEngineService.applyRules(tthd, detailApiResponseV3);

		Assert.assertNotNull(detailApiResponseV3.getRefundDetails());
		Assert.assertEquals(2, detailApiResponseV3.getRefundDetails().size());
		Assert.assertEquals("PTM0*********", detailApiResponseV3.getRefundDetails().get(0).getTxnId());
		Assert.assertEquals("PTM0*********q", detailApiResponseV3.getRefundDetails().get(1).getTxnId());
	}

	@Test
	public void testUthCategoryInDetailApiResponseV2() throws Exception {
		final TransformedTransactionHistoryDetail detail = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("P2MTxn", TransformedTransactionHistoryDetail.class);
		final DetailApiResponseV2 detailApiResponseV2 = new DetailApiResponseV2();

		ruleEngineService.applyRules(detail, detailApiResponseV2);

		Assert.assertEquals("OO2", detailApiResponseV2.getUthCategory());
	}

	@Test
	public void testTwoDigitUthCategoryInDetailApiResponseV2() throws Exception {
		final TransformedTransactionHistoryDetail detail = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("p2mPGTxnThroughWallet", TransformedTransactionHistoryDetail.class);
		final DetailApiResponseV2 detailApiResponseV2 = new DetailApiResponseV2();

		ruleEngineService.applyRules(detail, detailApiResponseV2);

		Assert.assertEquals("O12", detailApiResponseV2.getUthCategory());
	}

	@Test
	public void testThreeDigitUthCategoryInDetailApiResponseV2() throws Exception {
		final TransformedTransactionHistoryDetail detail = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("P2MUpiTxn", TransformedTransactionHistoryDetail.class);
		final DetailApiResponseV2 detailApiResponseV2 = new DetailApiResponseV2();

		ruleEngineService.applyRules(detail, detailApiResponseV2);

		Assert.assertEquals("102", detailApiResponseV2.getUthCategory());
	}

	@Test
	public void testP2pInwardRemittance_agentRefId_vendorNameWithRefNo() throws Exception {
		final TransformedTransactionHistoryDetail detail = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("P2PInternationRemittanceInwards", TransformedTransactionHistoryDetail.class);
		final DetailApiResponse detailAPIResponse = new DetailApiResponse();

		ruleEngineService.applyRules(detail, detailAPIResponse);

		Assert.assertEquals("2323344212", detailAPIResponse.getReferenceIdMap()
			.get(detail.getContextMap().getOrDefault(WebConstants.VENDOR_NAME, WebConstants.PARTNER) + " Ref. No."));
		Assert.assertEquals("Ria Money Transfer Ref. No. : 2323344212", detailAPIResponse.getReferenceIds().get(0));
	}

	@Test
	public void testP2pInwardRemittance_agentRefId_partnerWithRefNo() throws Exception {
		final TransformedTransactionHistoryDetail detail = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("P2PInternationRemittanceInwards", TransformedTransactionHistoryDetail.class);
		detail.getContextMap().remove(WebConstants.VENDOR_NAME);
		final DetailApiResponse detailAPIResponse = new DetailApiResponse();

		ruleEngineService.applyRules(detail, detailAPIResponse);

		Assert.assertEquals("2323344212", detailAPIResponse.getReferenceIdMap()
			.get(detail.getContextMap().getOrDefault(WebConstants.VENDOR_NAME, WebConstants.PARTNER) + " Ref. No."));
		Assert.assertEquals("Partner Ref. No. : 2323344212", detailAPIResponse.getReferenceIds().get(0));
	}

	@Test
	public void testP2pInwardRemittance_agentRefId_vendorNameWithEmptyRefNo() throws Exception {
		final TransformedTransactionHistoryDetail detail = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("P2PInternationRemittanceInwards", TransformedTransactionHistoryDetail.class);
		detail.getContextMap().replace(WebConstants.TRANSFER_REF_NO, "");
		final DetailApiResponse detailAPIResponse = new DetailApiResponse();

		ruleEngineService.applyRules(detail, detailAPIResponse);

		Assert.assertEquals(null, detailAPIResponse.getReferenceIdMap());
		Assert.assertEquals(null, detailAPIResponse.getReferenceIds());
	}

	@Test
	public void testP2pInwardRemittance_agentRefId_vendorNameWithoutRefNo() throws Exception {
		final TransformedTransactionHistoryDetail detail = (TransformedTransactionHistoryDetail) Util
			.getObjectFromFile("P2PInternationRemittanceInwards", TransformedTransactionHistoryDetail.class);
		detail.getContextMap().remove(WebConstants.TRANSFER_REF_NO);
		final DetailApiResponse detailAPIResponse = new DetailApiResponse();

		ruleEngineService.applyRules(detail, detailAPIResponse);

		Assert.assertEquals(null, detailAPIResponse.getReferenceIdMap());
		Assert.assertEquals(null, detailAPIResponse.getReferenceIds());
	}

	@Test
	public void droolsAcctNumUnmaskingTest() throws Exception {
		TransformedTransactionHistoryDetail listingVisibleTxn = new TransformedTransactionHistoryDetail();
		listingVisibleTxn.setIsAccountNumberUnmaskingEnabled(true);
		DetailApiResponse detailApiResponse = new DetailApiResponse();
		InstrumentDto firstInst = new InstrumentDto();
		firstInst.setInstrumentDetail("Masked Acct Number %{AcctNum : *********}");
		ParticipantInfo participantInfo = new ParticipantInfo();
		TransformedBankData bankData = new TransformedBankData();
		bankData.setAccNumber("********");
		TransformedParticipant participant = new TransformedParticipant();
		participant.setBankData(bankData);
		participantInfo.setParticipant(participant);
		firstInst.setParticipantInfo(participantInfo);
		detailApiResponse.setFirstInstrument(Arrays.asList(firstInst));

		InstrumentDto secInst = new InstrumentDto();
		secInst.setInstrumentDetail("no acct number");
		detailApiResponse.setSecondInstrument(Arrays.asList(secInst));
		ruleEngineService.applyRules(listingVisibleTxn, detailApiResponse);

		Assert.assertEquals("Masked Acct Number - 6789", firstInst.getInstrumentDetail());
		Assert.assertEquals("no acct number", secInst.getInstrumentDetail());

	}

	@Test
	public void droolsAcctNumMaskingTestForHybridTxns() {

		// Checking the cases where multiple instrumentDtos are there in first or
		// secondInstrument.
		// if first instrument dto's instrumentDetail is null.
		// than also all the dto's should be traversed.
		TransformedTransactionHistoryDetail tthd = new TransformedTransactionHistoryDetail();
		DetailApiResponse detailApiResponse = new DetailApiResponse();
		InstrumentDto instrumentOne = new InstrumentDto();
		instrumentOne.setInstrumentDetail(null);
		InstrumentDto instrumentTwo = new InstrumentDto();
		instrumentTwo.setInstrumentDetail("Masked Acct Number %{AcctNum : *********}");
		detailApiResponse.setFirstInstrument(Arrays.asList(instrumentOne, instrumentTwo));
		ruleEngineService.applyRules(tthd, detailApiResponse);
		Assert.assertEquals("Masked Acct Number - 6789", instrumentTwo.getInstrumentDetail());

		InstrumentDto instrumentDtoOne = new InstrumentDto();
		instrumentDtoOne.setInstrumentDetail(null);
		InstrumentDto instrumentDtoTwo = new InstrumentDto();
		instrumentDtoTwo.setInstrumentDetail("Masked Acct Number %{AcctNum : *********}");
		detailApiResponse.setSecondInstrument(Arrays.asList(instrumentDtoOne, instrumentDtoTwo));
		ruleEngineService.applyRules(tthd, detailApiResponse);
		Assert.assertEquals("Masked Acct Number - 6789", instrumentDtoTwo.getInstrumentDetail());

	}

	@Test
	public void testDroolsMaskingIndentifierFunction() {
		String str = GenericUtility.getDroolsMaskedAccountNumber("*********0", null);
		Assert.assertEquals("%{AcctNum : *********0}", str);
	}

	@Test
	public void listingCustomNarrationRecurringMandateWhenMerchantNotNullTest() throws Exception {
		TransformedTransactionHistoryDetail listingVisibleTxn = createTransformedTransactionHistoryDetailBuilder();
		listingVisibleTxn.setTxnType(TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey());
		if (listingVisibleTxn.getContextMap() == null) {
			listingVisibleTxn.setContextMap(new HashMap<>());
		}
		listingVisibleTxn.getContextMap().put(TRANSACTION_PURPOSE, CommonConstants.PURPOSE_COLLECT);
		listingVisibleTxn.getContextMap().put(EXECUTION_NO, EXECUTION_NO_ONE);
		listingVisibleTxn.getContextMap().put(MANDATE_AMOUNT, "1");
		EsResponseTxn response = new EsResponseTxn();
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Automatic payment of ₹1 setup for", response.getNarration());

		listingVisibleTxn.getContextMap().put(EXECUTION_NO, "2");
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Automatic payment for", response.getNarration());
	}

	@Test
	public void listingCustomNarrationRecurringMandateWhenMerchantNullTest() throws Exception {
		TransformedTransactionHistoryDetail listingVisibleTxn = createTransformedTransactionHistoryDetail_1Builder();
		listingVisibleTxn.setTxnType(TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey());
		if (listingVisibleTxn.getContextMap() == null) {
			listingVisibleTxn.setContextMap(new HashMap<>());
		}
		listingVisibleTxn.getContextMap().put(TRANSACTION_PURPOSE, CommonConstants.PURPOSE_COLLECT);
		listingVisibleTxn.getContextMap().put(EXECUTION_NO, EXECUTION_NO_ONE);
		listingVisibleTxn.getContextMap().put(MANDATE_AMOUNT, "1");
		EsResponseTxn response = new EsResponseTxn();
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, null, null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Automatic payment of ₹1 setup", response.getNarration());

		listingVisibleTxn.getContextMap().put(EXECUTION_NO, "2");
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, null, null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Automatic payment", response.getNarration());
	}

	@Test
	public void listingCustomNarrationIpoMandateTest() throws Exception {
		TransformedTransactionHistoryDetail listingVisibleTxn = createTransformedTransactionHistoryDetailBuilder();
		listingVisibleTxn.setTxnType(TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey());
		if (listingVisibleTxn.getContextMap() == null) {
			listingVisibleTxn.setContextMap(new HashMap<>());
		}
		listingVisibleTxn.getContextMap().put(TRANSACTION_PURPOSE, CommonConstants.PURPOSE_REVOKE);
		EsResponseTxn response = new EsResponseTxn();
		response.setNarration("Random Narration");
		response.setUserInstrumentNarration(null);
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "Paytm", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money Unblocked from", response.getNarration());
		Assert.assertEquals("Unblocked from", response.getUserInstrumentNarration());
		Assert.assertEquals("Paytm IPO", response.getSecondPartyInfo().getName());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put(TRANSACTION_PURPOSE, CommonConstants.PURPOSE_CREATE);
		response.setNarration("Random Narration");
		response.setUserInstrumentNarration(null);
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money Blocked for", response.getNarration());
		Assert.assertEquals("Blocked in", response.getUserInstrumentNarration());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put(TRANSACTION_PURPOSE, CommonConstants.PURPOSE_UPDATE);
		response.setNarration("Random Narration");
		response.setUserInstrumentNarration(null);
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "Paytm", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("IPO Bid Updated for", response.getNarration());
		Assert.assertEquals("Blocked in", response.getUserInstrumentNarration());
		Assert.assertEquals("Paytm", response.getSecondPartyInfo().getName());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put(TRANSACTION_PURPOSE, CommonConstants.PURPOSE_COLLECT);
		listingVisibleTxn.setStatus(ClientStatusEnum.SUCCESS.getStatusKey());
		response.setNarration("Random Narration");
		response.setUserInstrumentNarration(null);
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Paid for", response.getNarration());
		Assert.assertEquals(null, response.getUserInstrumentNarration());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put(TRANSACTION_PURPOSE, CommonConstants.PURPOSE_COLLECT);
		listingVisibleTxn.setStatus(ClientStatusEnum.FAILURE.getStatusKey());
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Payment for", response.getNarration());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put(TRANSACTION_PURPOSE, CommonConstants.PURPOSE_COLLECT);
		listingVisibleTxn.setStatus(ClientStatusEnum.PENDING.getStatusKey());
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Payment for", response.getNarration());
	}

	@Test
	public void listingCustomNarrationOnetimeMandateTest() throws Exception {
		TransformedTransactionHistoryDetail listingVisibleTxn = createTransformedTransactionHistoryDetailBuilder();
		listingVisibleTxn.setTxnType(TransactionTypeEnum.ONE_TIME_MANDATE.getTransactionTypeKey());
		if (listingVisibleTxn.getContextMap() == null) {
			listingVisibleTxn.setContextMap(new HashMap<>());
		}
		listingVisibleTxn.getContextMap().put(TXN_PURPOSE, CommonConstants.PURPOSE_COLLECT);
		listingVisibleTxn.setStatus(ClientStatusEnum.SUCCESS.getStatusKey());
		EsResponseTxn response = new EsResponseTxn();
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Paid to", response.getNarration());

		listingVisibleTxn.getContextMap().put(TXN_PURPOSE, CommonConstants.PURPOSE_COLLECT);
		listingVisibleTxn.setTxnType(TransactionTypeEnum.ONE_TIME_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.setStatus(ClientStatusEnum.FAILURE.getStatusKey());
		response = new EsResponseTxn();
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Payment to", response.getNarration());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.ONE_TIME_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put(TXN_PURPOSE, CommonConstants.PURPOSE_COLLECT);
		listingVisibleTxn.setStatus(ClientStatusEnum.PENDING.getStatusKey());
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Payment to", response.getNarration());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.ONE_TIME_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put(TXN_PURPOSE, CommonConstants.PURPOSE_CREATE);
		listingVisibleTxn.setStatus(ClientStatusEnum.SUCCESS.getStatusKey());
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Money on hold for", response.getNarration());

		listingVisibleTxn.setTxnType(TransactionTypeEnum.ONE_TIME_MANDATE.getTransactionTypeKey());
		listingVisibleTxn.getContextMap().put(TXN_PURPOSE, CommonConstants.PURPOSE_REVOKE);
		listingVisibleTxn.setStatus(ClientStatusEnum.SUCCESS.getStatusKey());
		response.setNarration("Random Narration");
		response.setSecondPartyInfo(new SecondPartyInfo("dummy url", null, "dummy name", null, null, null));
		ruleEngineService.applyRules(listingVisibleTxn, response);
		Assert.assertEquals("Automatic Payment cancelled for", response.getNarration());
	}

	private DetailApiResponse createResponseBuilder() {
		DetailApiResponse detailAPIResponse = new DetailApiResponse();
		detailAPIResponse.setDetailNarration("Money Paid");
		List<InstrumentDto> firstInstrument = new ArrayList<InstrumentDto>();
		InstrumentDto instrument1 = new InstrumentDto();
		instrument1.setNarration("to");
		instrument1.setInstrumentDetail("bhahahaha");
		instrument1.setParticipantInfo(ParticipantInfo.builder().participant(getTransformedParticipantFirst()).build());
		firstInstrument.add(instrument1);
		List<InstrumentDto> secondInstrument = new ArrayList<>();
		InstrumentDto instrument2 = new InstrumentDto();
		instrument2.setNarration("from");
		instrument2.setInstrumentDetail("GroomGroom");
		instrument2.setParticipantInfo(ParticipantInfo.builder().participant(getTransformedParticipantSelf()).build());
		secondInstrument.add(instrument2);
		detailAPIResponse.setFirstInstrument(firstInstrument);
		detailAPIResponse.setSecondInstrument(secondInstrument);
		return detailAPIResponse;
	}

	private DetailApiResponse createP2mResponseBuilder() {
		DetailApiResponse detailAPIResponse = new DetailApiResponse();
		detailAPIResponse.setDetailNarration("Money Paid");
		List<InstrumentDto> firstInstrument = new ArrayList<InstrumentDto>();
		InstrumentDto instrument1 = new InstrumentDto();
		instrument1.setNarration("to");
		instrument1.setInstrumentDetail("bhahahaha");
		instrument1
			.setParticipantInfo(ParticipantInfo.builder().participant(getTransformedParticipantMerchant()).build());
		firstInstrument.add(instrument1);
		List<InstrumentDto> secondInstrument = new ArrayList<>();
		InstrumentDto instrument2 = new InstrumentDto();
		instrument2.setNarration("from");
		instrument2.setInstrumentDetail("GroomGroom");
		instrument2.setParticipantInfo(ParticipantInfo.builder().participant(getTransformedParticipantSelf()).build());
		secondInstrument.add(instrument2);
		detailAPIResponse.setFirstInstrument(firstInstrument);
		detailAPIResponse.setSecondInstrument(secondInstrument);
		return detailAPIResponse;
	}

	private DetailApiResponse createUpiToWalletInwardsResponseBuilder() {
		DetailApiResponse detailAPIResponse = new DetailApiResponse();
		detailAPIResponse.setDetailNarration("Money Paid");
		List<InstrumentDto> firstInstrument = new ArrayList<InstrumentDto>();
		InstrumentDto instrument1 = new InstrumentDto();
		instrument1.setNarration("to");
		instrument1.setInstrumentDetail("bhahahaha");
		instrument1.setParticipantInfo(
				ParticipantInfo.builder().participant(getTransformedParticipantDebitExternalUpi()).build());
		firstInstrument.add(instrument1);
		List<InstrumentDto> secondInstrument = new ArrayList<>();
		InstrumentDto instrument2 = new InstrumentDto();
		instrument2.setNarration("from");
		instrument2.setInstrumentDetail("GroomGroom");
		instrument2.setParticipantInfo(
				ParticipantInfo.builder().participant(getTransformedParticipantUpiToWalletCreditSelf()).build());
		secondInstrument.add(instrument2);
		detailAPIResponse.setFirstInstrument(firstInstrument);
		detailAPIResponse.setSecondInstrument(secondInstrument);
		return detailAPIResponse;
	}

	@Test
	public void vpaTests() {
		DetailApiResponse detailApiResponse = createResponseBuilder();
		TransformedTransactionHistoryDetail tthd = createTransformedTransactionHistoryDetailBuilder();
		ruleEngineService.applyRules(tthd, detailApiResponse);
		Assert.assertTrue(detailApiResponse.getFirstInstrument().get(0).getInstrumentDetail().contains("PhonePe"));
	}

	@Test
	public void vpaTestsMerchant() {
		DetailApiResponse detailApiResponse = createP2mResponseBuilder();
		TransformedTransactionHistoryDetail tthd = createTransformedTransactionHistoryDetailP2mBuilder();
		ruleEngineService.applyRules(tthd, detailApiResponse);
		Assert.assertTrue(detailApiResponse.getFirstInstrument().get(0).getInstrumentDetail().contains("PhonePe"));
	}

	@Test
	public void vpaTestsUpiToWalletInwards() {
		DetailApiResponse detailApiResponse = createUpiToWalletInwardsResponseBuilder();
		TransformedTransactionHistoryDetail tthd = createTthdUpiToWalletInwardsBuilder();
		ruleEngineService.applyRules(tthd, detailApiResponse);
		Assert.assertTrue(detailApiResponse.getFirstInstrument().get(0).getInstrumentDetail().contains("PhonePe"));
	}

	private TransformedTransactionHistoryDetail createTransformedTransactionHistoryDetailBuilder() {
		List<TransformedParticipant> participants = new ArrayList<>();
		TransformedParticipant firstParticipant = getTransformedParticipantFirst();

		participants.add(firstParticipant);

		TransformedParticipant secondParticipant = getTransformedParticipantSelf();

		participants.add(secondParticipant);

		return TransformedTransactionHistoryDetail.builder()
			.entityId("*********")
			.entityType(1)
			.groupId("2_20200721111212801300168138476512411")
			.orderId("ML-OR1689ISB-001A-789959-1")
			.streamSource(3)
			.isSource(true)
			.isBankData(false)
			.showInListing(true)
			.isVisible(true)
			.amount(7178L)
			.currency(1)
			.txnType(5)
			.txnIndicator(2)
			.status(2)
			.txnId("***********")
			.sourceTxnId("20200721111212801300168138476512411")
			.txnDate(*************L)
			.updatedDate(*************L)
			.participants(participants)
			.build();
	}

	private TransformedTransactionHistoryDetail createTransformedTransactionHistoryDetail_1Builder() {
		List<TransformedParticipant> participants = new ArrayList<>();
		TransformedParticipant firstParticipant = getTransformedParticipantWithNameAsNull();

		participants.add(firstParticipant);

		TransformedParticipant secondParticipant = getTransformedParticipantSelf();

		participants.add(secondParticipant);

		return TransformedTransactionHistoryDetail.builder()
			.entityId("*********")
			.entityType(1)
			.groupId("2_20200721111212801300168138476512411")
			.orderId("ML-OR1689ISB-001A-789959-1")
			.streamSource(3)
			.isSource(true)
			.isBankData(false)
			.showInListing(true)
			.isVisible(true)
			.amount(7178L)
			.currency(1)
			.txnType(5)
			.txnIndicator(2)
			.status(2)
			.txnId("***********")
			.sourceTxnId("20200721111212801300168138476512411")
			.txnDate(*************L)
			.updatedDate(*************L)
			.participants(participants)
			.build();
	}

	private TransformedTransactionHistoryDetail createTransformedTransactionHistoryDetailP2mBuilder() {
		List<TransformedParticipant> participants = new ArrayList<>();
		TransformedParticipant firstParticipant = getTransformedParticipantMerchant();

		participants.add(firstParticipant);

		TransformedParticipant secondParticipant = getTransformedParticipantSelf();

		participants.add(secondParticipant);

		return TransformedTransactionHistoryDetail.builder()
			.entityId("*********")
			.entityType(1)
			.groupId("2_20200721111212801300168138476512411")
			.orderId("ML-OR1689ISB-001A-789959-1")
			.streamSource(3)
			.isSource(true)
			.isBankData(false)
			.showInListing(true)
			.isVisible(true)
			.amount(7178L)
			.currency(1)
			.txnType(5)
			.txnIndicator(1)
			.status(2)
			.txnId("***********")
			.sourceTxnId("20200721111212801300168138476512411")
			.txnDate(*************L)
			.updatedDate(*************L)
			.participants(participants)
			.build();
	}

	private TransformedParticipant getTransformedParticipantMerchant() {
		return TransformedParticipant.builder()
			.entityType(2)
			.name("Medlife Wellness")
			.amount(7178L)
			.remarks("Refund for order")
			.status(2)
			.txnIndicator(2)
			.paymentSystem(3)
			.paymentTxnId("***********")
			.txnDate("*************")
			.updatedDate("*************")
			.upiData(new TransformedUPIData("blablabla@ybl", null, null, null, null, null))
			.build();
	}

	private TransformedParticipant getTransformedParticipantFirst() {
		return TransformedParticipant.builder()
			.entityId("Medlif19243838539231")
			.entityType(2)
			.name("Medlife Wellness")
			.amount(7178L)
			.remarks("Refund for order")
			.status(2)
			.txnIndicator(1)
			.paymentSystem(3)
			.paymentTxnId("***********")
			.txnDate("*************")
			.updatedDate("*************")
			.upiData(new TransformedUPIData("blablabla@ybl", null, null, null, null, null))
			.build();
	}

	private TransformedParticipant getTransformedParticipantSelf() {
		return TransformedParticipant.builder()
			.entityId("*********")
			.entityType(1)
			.name("GURPREET SINGH")
			.logoUrl(
					"https://s3-ap-southeast-1.amazonaws.com/assets.paytm.com/images/catalog/pg/category/Health+care.png")
			.amount(7178L)
			.currency(1)
			.remarks("Refund for order")
			.status(2)
			.txnIndicator(2)
			.paymentSystem(3)
			.paymentTxnId("***********")
			.txnDate("*************")
			.updatedDate("*************")
			.upiData(new TransformedUPIData("blablabla@okicici", null, null, null, null, null))
			.build();
	}

	private TransformedTransactionHistoryDetail createTthdUpiToWalletInwardsBuilder() {
		List<TransformedParticipant> participants = new ArrayList<>();
		TransformedParticipant firstParticipant = getTransformedParticipantDebitExternalUpi();

		participants.add(firstParticipant);

		TransformedParticipant secondParticipant = getTransformedParticipantUpiToWalletCreditSelf();

		participants.add(secondParticipant);

		return TransformedTransactionHistoryDetail.builder()
			.entityId("*********")
			.entityType(1)
			.groupId("2_20200721111212801300168138476512411")
			.orderId("ML-OR1689ISB-001A-789959-1")
			.streamSource(2)
			.sourceSystem(3)
			.isBankData(false)
			.showInListing(true)
			.isVisible(true)
			.amount(7178L)
			.currency(1)
			.txnType(TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD.getTransactionTypeKey())
			.txnIndicator(2)
			.status(2)
			.txnId("***********")
			.sourceTxnId("20200721111212801300168138476512411")
			.txnDate(*************L)
			.updatedDate(*************L)
			.participants(participants)
			.build();
	}

	private TransformedParticipant getTransformedParticipantWithNameAsNull() {
		return TransformedParticipant.builder()
			.entityType(2)
			.name(null)
			.amount(7178L)
			.remarks("Refund for order")
			.status(2)
			.txnIndicator(2)
			.paymentSystem(3)
			.paymentTxnId("***********")
			.txnDate("*************")
			.updatedDate("*************")
			.walletData(new TransformedWalletData())
			.build();
	}

	private TransformedParticipant getTransformedParticipantUpiToWalletCreditSelf() {
		return TransformedParticipant.builder()
			.entityType(2)
			.name("Medlife Wellness")
			.amount(7178L)
			.remarks("Refund for order")
			.status(2)
			.txnIndicator(2)
			.paymentSystem(3)
			.paymentTxnId("***********")
			.txnDate("*************")
			.updatedDate("*************")
			.walletData(new TransformedWalletData())
			.build();
	}

	private TransformedParticipant getTransformedParticipantDebitExternalUpi() {
		return TransformedParticipant.builder()
			.entityType(2)
			.name("Medlife Wellness")
			.amount(7178L)
			.remarks("Refund for order")
			.status(2)
			.txnIndicator(1)
			.paymentSystem(3)
			.paymentTxnId("***********")
			.txnDate("*************")
			.updatedDate("*************")
			.upiData(new TransformedUPIData("blablabla@ybl", null, null, null, null, null))
			.build();
	}

	@Test
	public void testCheckBalanceCtaSelfTransferSuccess() throws IOException {
		TransformedTransactionHistoryDetail tthd = Util.getTTHDFromFile("P2P_SelfUpiTransfer");
		DetailApiResponseV2 detailApiResponseV2 = (DetailApiResponseV2) Util
			.getObjectFromFile("DetailApiResponseV2_Self_BeforeRule", DetailApiResponseV2.class);

		ruleEngineService.applyRules(tthd, detailApiResponseV2);
		Map<String, CtaNode> ctasMap = detailApiResponseV2.getFirstInstrument().get(0).getCtasMap();
		Assert.assertNotNull(ctasMap);
		Assert.assertNotNull(ctasMap.get("ACCOUNT_CHECK_BALANCE"));

		CtaNode ctaNode = ctasMap.get("ACCOUNT_CHECK_BALANCE");
		Assert.assertEquals("ACCOUNT_CHECK_BALANCE", ctaNode.getCtaType());
		Assert.assertEquals("Check Balance", ctaNode.getLabel());
		Assert.assertEquals("PARAMS", ctaNode.getValueType());

		Object obj = ctaNode.getValue();
		ObjectMapper objectMapper = new ObjectMapper();
		Map<String, String> ctaValue = objectMapper.convertValue(obj, Map.class);
		Assert.assertNotNull(ctaValue);
		Assert.assertEquals("XX 0386", ctaValue.get("accountNumber"));
		Assert.assertEquals("ICIC0004321", ctaValue.get("ifsc"));
		Assert.assertNull(ctaNode.getLogoUrl());
		Assert.assertNull(ctaNode.getVersion());
		Assert.assertNull(ctaNode.getOrder());
	}

	@Test
	public void testCheckBalanceCtaSelfTransferFailure() throws IOException {
		TransformedTransactionHistoryDetail tthd = Util.getTTHDFromFile("P2P_SelfUpiTransfer");
		tthd.setStatus(1);
		DetailApiResponseV2 detailApiResponseV2 = (DetailApiResponseV2) Util
			.getObjectFromFile("DetailApiResponseV2_Self_BeforeRule", DetailApiResponseV2.class);
		detailApiResponseV2.setStatus("FAILED");

		ruleEngineService.applyRules(tthd, detailApiResponseV2);

		Assert.assertNull(detailApiResponseV2.getFirstInstrument().get(0).getCtasMap());
	}

	@Test
	public void testCheckBalanceCtaSelfTransferPending() throws IOException {
		TransformedTransactionHistoryDetail tthd = Util.getTTHDFromFile("P2P_SelfUpiTransfer");
		tthd.setStatus(3);
		DetailApiResponseV2 detailApiResponseV2 = (DetailApiResponseV2) Util
			.getObjectFromFile("DetailApiResponseV2_Self_BeforeRule", DetailApiResponseV2.class);
		detailApiResponseV2.setStatus("PENDING");

		ruleEngineService.applyRules(tthd, detailApiResponseV2);

		Assert.assertNull(detailApiResponseV2.getFirstInstrument().get(0).getCtasMap());
	}

	@Test
	public void testCheckBalanceCtaNonSelfTransfer() throws IOException {
		TransformedTransactionHistoryDetail tthd = Util.getTTHDFromFile("P2P_SelfUpiTransfer");
		tthd.getContextMap().replace("isSelfTransfer", "false");
		DetailApiResponseV2 detailApiResponseV2 = (DetailApiResponseV2) Util
			.getObjectFromFile("DetailApiResponseV2_Self_BeforeRule", DetailApiResponseV2.class);

		ruleEngineService.applyRules(tthd, detailApiResponseV2);

		Assert.assertNull(detailApiResponseV2.getFirstInstrument().get(0).getCtasMap());
	}

	// This testcase is for txn like p2p, p2m, upi_to_wallet, addMoney where debit
	// instrument is upiLite.
	// where we do not add check Balance in secondInstrument.

	@Test
	public void testCheckBalanceCtaForUpiLiteTxnOfOutwardType() throws IOException {
		// checking for p2p_outward
		TransformedTransactionHistoryDetail tthd = Util.getTTHDFromFile("p2p_outward_using_upiLite.json");
		DetailApiResponseV2 detailApiResponseV2 = (DetailApiResponseV2) Util
			.getObjectFromFile("DetailApiResponseV2_p2p_outward_upiLite", DetailApiResponseV2.class);

		ruleEngineService.applyRules(tthd, detailApiResponseV2);

		Assert.assertNull(detailApiResponseV2.getSecondInstrument().get(0).getCtasMap());
	}

	@Test
	public void selfTransferListingTest() throws Exception {
		// Success
		TransformedTransactionHistoryDetail tthd1 = getTTHDFromFile("P2P_SelfUpiTransfer");
		EsResponseTxn esResponseTxn1 = getEsResponseTxnFromFile("EsResponseTxn_SelfUpiTransfer");
		ruleEngineService.applyRules(tthd1, esResponseTxn1);
		String selfTransferLogoUrl = "https://static.orgk.com/uth/images/category-logo/self_transfer.png";
		Assert.assertEquals("URL",
				esResponseTxn1.getSecondPartyInfo().getLogoOrder().get(0).getType().getLogoTypeValue());
		Assert.assertEquals(selfTransferLogoUrl, esResponseTxn1.getSecondPartyInfo().getLogoOrder().get(0).getValue());
		Assert.assertEquals("ICICI Bank - 0386", esResponseTxn1.getSecondPartyInfo().getName());
		Assert.assertEquals("Transferred to Self,", esResponseTxn1.getNarration());

		// Failed
		TransformedTransactionHistoryDetail tthd2 = getTTHDFromFile("P2P_SelfUpiTransfer");
		tthd2.setStatus(ClientStatusEnum.FAILURE.getStatusKey());
		EsResponseTxn esResponseTxn2 = getEsResponseTxnFromFile("EsResponseTxn_SelfUpiTransfer");
		esResponseTxn2.setStatus(FAILED);
		ruleEngineService.applyRules(tthd2, esResponseTxn2);
		Assert.assertEquals("URL",
				esResponseTxn2.getSecondPartyInfo().getLogoOrder().get(0).getType().getLogoTypeValue());
		Assert.assertEquals(selfTransferLogoUrl, esResponseTxn2.getSecondPartyInfo().getLogoOrder().get(0).getValue());
		Assert.assertEquals("ICICI Bank - 0386", esResponseTxn2.getSecondPartyInfo().getName());
		Assert.assertEquals("Self-Transfer Failed To", esResponseTxn2.getNarration());

		// Pending
		TransformedTransactionHistoryDetail tthd3 = getTTHDFromFile("P2P_SelfUpiTransfer");
		tthd2.setStatus(ClientStatusEnum.PENDING.getStatusKey());
		EsResponseTxn esResponseTxn3 = getEsResponseTxnFromFile("EsResponseTxn_SelfUpiTransfer");
		esResponseTxn3.setStatus(PENDING);
		ruleEngineService.applyRules(tthd3, esResponseTxn3);
		Assert.assertEquals("URL",
				esResponseTxn3.getSecondPartyInfo().getLogoOrder().get(0).getType().getLogoTypeValue());
		Assert.assertEquals(selfTransferLogoUrl, esResponseTxn3.getSecondPartyInfo().getLogoOrder().get(0).getValue());
		Assert.assertEquals("ICICI Bank - 0386", esResponseTxn3.getSecondPartyInfo().getName());
		Assert.assertEquals("Self-Transfer Pending To", esResponseTxn3.getNarration());
	}

	@Test
	public void selfTransferDetailTest() throws Exception {
		TransformedTransactionHistoryDetail tthd1 = getTTHDFromFile("P2P_SelfUpiTransfer");
		DetailApiResponseV2 response1 = getDetailResponseV2FromFile("DetailApiResponseV2_Self_BeforeRule");
		ruleEngineService.applyRules(tthd1, response1);
		Assert.assertEquals("Transferred Successfully", response1.getDetailNarration());
		for (InstrumentDtoV2 instrumentDtoV2 : response1.getFirstInstrument()) {
			Assert.assertEquals("To Your", instrumentDtoV2.getNarration());
			Assert.assertEquals("ICICI Bank - 0386", instrumentDtoV2.getName());
			Assert.assertNull(instrumentDtoV2.getNameVerifiedLogo());
			Assert.assertEquals("URL",
					instrumentDtoV2.getEntityDetails().getLogoOrder().get(0).getType().getLogoTypeValue());
			String logoUrl = "https://static.orgk.com/uth/images/bank-logo/ICIC.png";
			Assert.assertEquals(logoUrl, instrumentDtoV2.getEntityDetails().getLogoOrder().get(0).getValue());
			Assert.assertNull(instrumentDtoV2.getInstrumentDetail());
			Assert.assertNull(instrumentDtoV2.getInstrumentDetailsMap());
			Assert.assertNull(instrumentDtoV2.getVerifiedNameLabel());
		}
		for (InstrumentDtoV2 instrumentDtoV2 : response1.getSecondInstrument()) {
			Assert.assertEquals("From Your", instrumentDtoV2.getNarration());
			Assert.assertEquals("State Bank Of India - 3629", instrumentDtoV2.getName());
			Assert.assertNull(instrumentDtoV2.getInstrumentDetail());
			Assert.assertNull(instrumentDtoV2.getInstrumentDetailsMap());
		}
	}

	@Test
	public void wmcTest() throws Exception {
		TransformedTransactionHistoryDetail deduction = getTTHDFromFile("WMC_DEDUCTED.json");
		DetailApiResponseV2 responseV2 = getDetailResponseV2FromFile("DetailApiResponseV2_WalletCharges.json");
		ruleEngineService.applyRules(deduction, responseV2);
		Assert.assertEquals("Charges Deducted", responseV2.getDetailNarration());
		for (InstrumentDtoV2 instrumentDtoV2 : responseV2.getFirstInstrument()) {
			Assert.assertEquals("For", instrumentDtoV2.getNarration());
			Assert.assertEquals("Quarterly Wallet Maintenance Charge", instrumentDtoV2.getName());
			String logoUrl = "https://static.orgk.com/uth/images/wallet-logo/wallet_maintenance_charges.png";
			Assert.assertEquals(logoUrl, instrumentDtoV2.getLogoUrl());
		}
	}

	@Test
	public void wmcReversalTest() throws Exception {
		TransformedTransactionHistoryDetail deduction = getTTHDFromFile("WMC_REVERSAL.json");
		DetailApiResponseV2 responseV2 = getDetailResponseV2FromFile("DetailApiResponseV2_WalletCharges.json");
		ruleEngineService.applyRules(deduction, responseV2);
		Assert.assertEquals("Charges Reversed", responseV2.getDetailNarration());
		for (InstrumentDtoV2 instrumentDtoV2 : responseV2.getFirstInstrument()) {
			Assert.assertEquals("For", instrumentDtoV2.getNarration());
			Assert.assertEquals("Reversal of Quarterly Wallet Maintenance Charge", instrumentDtoV2.getName());
			String logoUrl = "https://static.orgk.com/uth/images/wallet-logo/wallet_maintenance_charges_reversed.png";
			Assert.assertEquals(logoUrl, instrumentDtoV2.getLogoUrl());
		}
	}

}
