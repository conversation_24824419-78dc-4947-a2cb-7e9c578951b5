{"4001-PS": {"responseCode": 4001, "httpCode": 400, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Invalid request body."}, "4002-PS": {"responseCode": 4002, "httpCode": 200, "httpCodeStr": 500, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Error in retrieving data from ES"}, "4003-PS": {"responseCode": 4003, "httpCode": 200, "httpCodeStr": 500, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Un-defined error. Internal server error"}, "4004-PS": {"responseCode": 4004, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Invalid parameter value"}, "4005-PS": {"responseCode": 4005, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "please provide both second party id and type"}, "4006-PS": {"responseCode": 4006, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "For page number more that 1, transactionDateEpoch, entityId, streamsource must be present"}, "4007-PS": {"responseCode": 4007, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Error creating query for Search. Please check search filters"}, "4008-PS": {"responseCode": 4008, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Invalid date. Date must be in epoch milliseconds"}, "4009-PS": {"responseCode": 4009, "httpCode": 200, "httpCodeStr": 500, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Error converting ES response to Response DTO"}, "4010-PS": {"responseCode": 4010, "httpCode": 200, "message": "It seems you have not transacted recently. Your transactions after FROM_SEARCH_DATE will be shown here.", "description": "No data in date range"}, "4011-PS": {"responseCode": 4011, "httpCode": 200, "httpCodeStr": 500, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Error while iterating search context to create query"}, "4012-PS": {"responseCode": 4012, "httpCode": 401, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Authorization error from o<PERSON><PERSON>."}, "4013-PS": {"responseCode": 4013, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "detail could not be loaded for the transaction"}, "4014-PS": {"responseCode": 4014, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Exception while validation of token"}, "4015-PS": {"responseCode": 4015, "httpCode": 200, "httpCodeStr": 500, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Known Outage, backend can't provide response"}, "4016-PS": {"responseCode": 4016, "httpCode": 401, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "please provide clientId."}, "4017-PS": {"responseCode": 4017, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "please provide Request-Token."}, "4018-PS": {"responseCode": 4018, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "please provide entityId"}, "4019-PS": {"responseCode": 4019, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "invalid jwt <PERSON> Received."}, "4020-PS": {"responseCode": 4020, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Rate limit applied no further calls are accepted. Please try after some time."}, "4021-PS": {"responseCode": 4021, "httpCode": 200, "httpCodeStr": 500, "message": "Sorry, we experienced an error in displaying search suggestions.", "description": "Error converting ES response to AutoComplete Response DTO."}, "4022-PS": {"responseCode": 4022, "httpCode": 200, "message": "Sorry, we experienced an error in displaying search suggestions.", "description": "AutoComplete Disabled."}, "4023-PS": {"responseCode": 4023, "httpCode": 200, "message": "Sorry, we experienced an error in displaying search suggestions.", "description": "Internal Validations Error."}, "4024-PS": {"responseCode": 4024, "httpCode": 507}, "4029-PS": {"responseCode": 4029, "httpCode": 200, "message": "Sorry, please come back in one hour and try again", "description": "Hourly Rate Limit Exceeded."}, "4030-PS": {"responseCode": 4030, "httpCode": 200, "message": "Sorry, please come back in sometime and try again", "description": "Rate Limit Exceeded."}, "4032-PS": {"responseCode": 4032, "httpCode": 200, "message": "Invalid Request Parameter Value.", "description": "Invalid parameter value"}, "4033-PS": {"responseCode": 4033, "httpCode": 200, "message": "Sorry, we experienced an error in displaying your Payment History.", "description": "Data might not present in ES due to lag."}, "4034-PS": {"responseCode": 4034, "httpCode": 200, "message": "Sorry, we experienced an error in displaying results{0} for duration prior to {1}. Please try searching for specific payments within the balance and history section directly.", "description": "Because of some data issues, older transactions can't be served"}, "4035-PS": {"responseCode": 4035, "httpCode": 507, "message": "Sorry, we experienced an error in displaying your Payment History."}, "4036-PS": {"responseCode": 4036, "httpCode": 200, "message": "We are currently facing issues in displaying your older payments. Please try again in sometime.", "description": "Because of paginationParam cached at FE fromDate can't be changed. So throwing custom exceptions so that we can fail listing request prior to virtual from date."}, "4037-PS": {"responseCode": 4037, "httpCode": 200, "message": "It seems you have not transacted recently. We are currently facing issues in displaying your transactions older than {0} days.", "description": "Since Virtual From Date comes in picture and transactions not found till VFD then we won't be saving NTU cached data in NTU cache until this flag is enabled."}, "4038-PS": {"responseCode": 4038, "httpCode": 200, "message": "We are currently unable to display your complete transaction history. Please try again later or download your UPI statement to view all transactions.", "description": "Because of paginationParam cached at FE fromDate can't be changed. So throwing custom exceptions so that we can fail listing request prior to virtual from date."}}