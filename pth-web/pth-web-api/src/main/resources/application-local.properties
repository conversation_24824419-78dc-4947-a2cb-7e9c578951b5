server.port                                        = 8092

app.name                                           = @project.artifactId@
app.version                                        = @project.version@
git.commit.id   = 122
git.branch      = master


external-integration-service-http-url              = http://eis-ite.orgk.com
oauth.service.base.url                             = https://accounts-staging.paytm.in

oauthClient.connectionProvider.name                = webflux
oauthClient.connectionProvider.maxConnections      = 500
oauthClient.connectionProvider.acquireTimeout      = 10000
oauthClient.connectionProvider.maxIdleTime         = 5

bank.oauth.service.base.url                        = https://oauth-ite.orgk.com


#es-host-list                                       = 127.0.0.1
es-port                                            = 9200

dc-es-host-list                       = 127.0.0.1
dc-es-port                            = 9200

aws-es-from-date                      = 2022-08-01 00:00:00.000 +0530

#fetching max these no txns from range
max-page-size-of-range   = 20
max-page-size-functionality-enable = true

elastic-search-index                               = payment_history_alias
elastic-search-index-prefix                        = payment-history-
elastic-search-index-prefix-for-details            = payment-history

server.tomcat.accesslog.enabled                    = true
server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
server.tomcat.accesslog.directory                  = /log/middleware-transaction-history-service
server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
server.tomcat.accesslog.rotate                     = true
server.tomcat.accesslog.rename-on-rotate           = true
server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
server.tomcat.accesslog.buffered                   = false
server.tomcat.accesslog.request-attributes-enabled = true


#CBS sftp config



static-bank-logo-base-url = https://static.orgk.com/uth/images/bank-logo/
static-category-logo-base-url = https://static.orgk.com/uth/images/category-logo/
static-status-logo-base-url = https://static.orgk.com/uth/images/status-logo/
static-wallet-logo-base-url =  https://static.orgk.com/uth/images/wallet-logo/

#Prometheus Monitoring Constants
prometheus.explorer                                         = TRANSACTION_HISTORY
prometheus.hostname                                         = localhost
prometheus.port                                             = 9200

#known - outage, any of our system down, to throw non-retriable error
known-issue-at-backend = false

#Default Value should be false
use-bank-oauth = false

# initially this value will be picked from BO-Panel if it's not present there then this will be default value
# Whether to use OAuth tokeninfo API or not, if it's value is false then /v2/user will be used
use-tokenInfo-api = false

# from date to filter data based on backfill completion format: yyyy-MM-dd HH:mm:ss.SSS Z
# error code 4010 needs to be updated whenever this date changes
from-date-listing-filter = 2020-08-01 00:00:00.000 +0530
default-from-date-listing-month-duration = 1
kafka.bootstrap-servers = localhost:9092

logging.config                                  = classpath:log4j2-local.json

aerospike.namespace                               = test
aerospike.host-name                               = localhost
aerospike.port                                    = 3000
aerospike.cache-name                              = upi
aerospike.cache-expiry-time                       = 7200
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
nonTransactingUser.cache.expiry.in.seconds = -1
localisation.asyc.corePool =5
localisation.asyc.maxPool=7
localisation.asyc.queueCapacity=10

kafka.localisation.target.client =marketplace
kafka.localisation.target.topic =uth_localisation_data
kafka.localisation.target.bootstrapServers =localhost:9092
kafka.localisation.target.batchSizeBytes =30000
kafka.localisation.target.lingerMs =15
kafka.localisation.target.requestTimeoutMs =5000
kafka.localisation.target.confluentKafkaRegistryUrl=http://localhost:8081
localise.detail.fields=detailNarration,firstInstrument.narration,secondInstrument.narration,recentTransactionInfo.narration,cstorderItem.label,repeatPayment.name
batch.size=50
localise.listing.fields=narration
prepopulate.data.list=Add Money Failed,Add Money Pending,Add Money to %s,Add Money to Paytm Payments Bank,Added Back to Your,Cashback Received,Cashback Received from %s,For Order At,For reversal of money transfer to,From,From Your,In,In Your,Money Added,Money Debited,Money On Hold,Money Paid,Money Received,Money Refunded,Money Restored,Money Sent,Money Transfer Failed,Money Transfer Pending,Money added to %s,Money added to Paytm Payments Bank,Money on hold for Order at %s,Money on hold released by %s,Paid to %s,Payment Failed,Payment Pending,Payment to %s,Received from %s,Refund Failed,Refund for failed transfer to %s,Refund from %s,Refund pending,Reversal of Money Sent,Sent to %s,To,To Your,Transfer to %s,Using Your,Settlement Received from %s,Gift Voucher Received from %s,Gift Voucher Sent To %s,For reversal of failed transfer to,Compensation Received,As compensation for,Automatic Payment made to %s,For Automatic Payment to,Refund Received,Interest earned for %s,Interest Received,Interest Earned,Cash Withdrawal at %s,Cash Withdrawal,At,Cash Withdrawal at,For,Cash Withdrawal at Paytm Bank Agent,ATM Cash Withdrawal at %s,At ATM,At Store,Refund for failed cash withdrawal,For reversal of cash withdrawal at ATM,Cash Deposited at %s,Cash Deposited,Cash Deposited with Paytm Bank Agent,Cash Deposit at ATM\,%s,At ATM Recycler,Cash Deposit,Salary Received,Flexi-Salary Received,Reimbursement Received,Charges Paid,Charges Paid for purchase of Paytm Payments Bank Debit Card,For Purchase of,For Cash Withdrawal at ATM,Charges Paid for transaction at ATM,For Transaction at ATM,For dispute raised against ATM Cash Withdrawal at,For dispute raised against store payment at,Cashback Received from Paytm Payments Bank,Cashback Received from Paytm,Cheque Return Charges,Aadhaar based direct benefit received,Recovery Deduction,Lien Recovery Reversal,Refund of excess card charges,Issuance Fee charged for Debit card,Added for failed transaction,Money transferred,Money paid to %s via cheque,Money paid via cheque,Money received from cheque payment,LPG subsidy received,Deducted by %s,Reversal of Debit Card fees,Charges Refunded,For Failed Purchase of,Charges Refunded for Debit Card,Annual Charge Paid for Debit Card,Charges Refunded for Cheque Book,Charges Refunded for cash withdrawal at ATM,Charges Refunded for ATM transaction,Charges Paid for payment at %s,For Payment at,Charges Refunded for payment at %s,Charges Paid for cash withdrawal at ATM,Charges Paid for ATM transaction,For dispute raised against,For ATM Transaction at,For Failed ATM Transaction at,For Failed Payment at,For dispute raised against online payment at,Charges Paid for payment to %s,For Payment to,For Failed Payment to,Refund for failed cheque payment,Refund for failed cheque payment to %s,For reversal of Cheque Payment to,Money Deducted,For reversal of money received for,For reversal of money received from,Refund for failed payment to %s,Money reversed for failed transfer from %s,Refund for failed transfer via UPI,For reversal of,Fixed Deposit Created,Refund for failed Fixed Deposit creation,Fixed Deposit Redeemed,For reversal of failed,Refund of AePS fund transfer,Refund for AePS Cash withdrawal,AePS fund transfer,AePS Cash withdrawal,Paid Successfully,Recent Payments With %s,Paid,Received,Refunded,Recent Gift Vouchers,Sent,Added to %s,Pending Add Money to %s,Failed Add Money to %s,Created,Deposited at %s,Withdrawal at Paytm Bank Agent,Withdrawal at %s,Earned for %s,Need Help with this Payment,View more details,Foreign Remittance Received from,Paid for Outward Remittance to,Refund of Outward Remittance sent to,RECEIVED IN
whitelisted-bank-users = *********,*********
whitelisted-localisation-users = -1
show-bank-data = true

spring.datasource.url                            = ******************************************
spring.datasource.username                       = root
spring.datasource.password                       = ${PTH_MYSQL_PASSWORD}
spring.datasource.driver-class-name              = com.mysql.jdbc.Driver
spring.datasource.configuration.maximum-pool-size=2
clientConfig.cron.expression = 0 30 14 * * ?
jwt.verification.enable=false

configurable_properties-index-alias = configurable_properties_alias
configurable_properties.scheduler.cron.expression = * * * * * ?
configurable.scheduler.create.new.indices.cron.expression = 0,10,20,30 0-5 0 1 * ?
configurable_properties.source = ElasticSearch
configurable_properties-index = configurable_properties-1

#Bo Panel Properties
configurable_properties.source.bo_panel = BOPanel

container.hostname                                = ${HOSTNAME}
fetch-user-image-from-cache = true
push-missing-user-image-list-to-kafka = false
#Kafka properties
kafka.target-v2-list[0].kafka-client-name               = recon_config
kafka.target-v2-list[0].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_recon_config_data
kafka.target-v2-list[0].bootstrap-servers               = localhost:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://localhost:8081
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = txnHistoryUserImageClient
kafka.target-v2-list[1].kafka-producer-key              = user_image_txn_history_producer
kafka.target-v2-list[1].topic                           = user_image_url_data
kafka.target-v2-list[1].bootstrap-servers               = localhost:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 5000

kafka.target-v2-list[2].kafka-client-name               = nonTransactingUserKafkaClient
kafka.target-v2-list[2].kafka-producer-key              = nonTransactingUserProducer
kafka.target-v2-list[2].topic                           = uth_cache_updater_data
kafka.target-v2-list[2].bootstrap-servers               = localhost:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].confluent-kafka-registry-url    = http://localhost:8081
kafka.target-v2-list[2].linger-ms                       = 15
kafka.target-v2-list[2].request-timeout-ms              = 5000

kafka.target-v2-list[3].kafka-client-name               = uthAnalyticsKafkaClient
kafka.target-v2-list[3].kafka-producer-key              = uthAnalyticsProducer
kafka.target-v2-list[3].topic                           = uth-dwh-ingestion-topic
kafka.target-v2-list[3].bootstrap-servers               = localhost:9092
kafka.target-v2-list[3].batch-size-bytes                = 3000
kafka.target-v2-list[3].linger-ms                       = 1
kafka.target-v2-list[3].request-timeout-ms              = 100

kafka.target-v2-list[4].kafka-client-name               = DUAL_WRITE_AUDIT_CLIENT
kafka.target-v2-list[4].kafka-producer-key              = dualWriteAuditProducer
kafka.target-v2-list[4].topic                           = uth_audit_data
kafka.target-v2-list[4].bootstrap-servers               = localhost:9092
kafka.target-v2-list[4].batch-size-bytes                = 3000
kafka.target-v2-list[4].linger-ms                       = 1
kafka.target-v2-list[4].request-timeout-ms              = 100

test.controller.enabled = false

upi.timeline.baseUrl = http://localhost:8092
upi.timeline.uri =  /upi/int/txn/v4/transaction/status
upi.timeline.sync.connection.timeout = 10000
upi.only.timeline.connection.timeout = 10000
upi.timeline.socket.timeout = 10000
upi.timeline.timeout = 10000
upi.timeline.secret.key = xyz

timeline.url.path = /transaction-history/ext/v2/{sourceContext}/detail?txnId={txnId}&status={status}&showOnlyTimeline=true
timeline.server = http://localhost:8092

recon.config.cron1.delay.in.milli = 50000
recon.config.cron1.lock.at.most.in.milli = 500000

metaDataApi.whitelisted.users = -1

chat.url.path = paytmmp://chat?featuretype=start_chat


outwardInternationalRemittance.url = paytmmp://payment_bank?pageId=passbookPage&featuretype=outward_remittance
updateElasticSearchTemplate                      = false
max-length-of-autoComplete-query = 20

max-Num-Of-ContactBook-Numbers = 4
contactbook-search-whitelisted-users-custId-Identifier = -1
contactbook-search-whitelisting-percentage = 100

oauth.token.evaluation.skip = true

rollout.config-list[0].percentage = 100
rollout.config-list[0].whitelisting-required = 0
rollout.config-list[0].whitelisting-for = detailPageRoleOutFeature

rollout.config-list[1].percentage = 100
rollout.config-list[1].whitelisting-required = -1
rollout.config-list[1].whitelisting-for = uthNtuCache

rollout.config-list[2].percentage = 100
rollout.config-list[2].whitelisting-required = -1
rollout.config-list[2].whitelisting-for = upiPassbookListingCache

rollout.config-list[3].percentage = 100
rollout.config-list[3].whitelisting-required = 1
rollout.config-list[3].user-list = 0
rollout.config-list[3].whitelisting-for = taggingOnDetails


rollout.config-list[4].percentage = 0
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = *********,4********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********
rollout.config-list[4].whitelisting-for = imps

rollout.config-list[5].percentage = 0
rollout.config-list[5].whitelisting-required = 1
rollout.config-list[5].user-list = *********,4********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[5].whitelisting-for = DcListingRouting

rollout.config-list[6].percentage = 100
rollout.config-list[6].whitelisting-required = 1
rollout.config-list[6].user-list = *********,*********
rollout.config-list[6].whitelisting-for = UpiCcEmiCta

rollout.config-list[7].percentage = 0
rollout.config-list[7].whitelisting-required = 1
rollout.config-list[7].user-list = *********,4********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********,6236193
rollout.config-list[7].whitelisting-for = UthAnalyticsWhiteListing

rollout.config-list[8].percentage = 0
rollout.config-list[8].whitelisting-required = 0
rollout.config-list[8].user-list = *********,4********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[8].whitelisting-for = trafficDiversionToDcForWhiteListedApis

rollout.config-list[9].percentage = 0
rollout.config-list[9].whitelisting-required = 0
rollout.config-list[9].user-list = *********,4********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[9].whitelisting-for = listingPageNoMoreThanOneTrafficDiversionToDc

rollout.config-list[10].percentage = 0
rollout.config-list[10].whitelisting-required = 1
rollout.config-list[10].user-list = **********
rollout.config-list[10].whitelisting-for = UserRateLimitingWhiteListing

rollout.config-list[11].percentage = 0
rollout.config-list[11].whitelisting-required = 1
rollout.config-list[11].user-list = 4********,*********,*********,********,*********,*********,**********,**********,**********,**********,*********,*********,*********,*********,**********,********,********,*********
rollout.config-list[11].whitelisting-for = bankOauthApiRollout

detailPageApiCachingEnabled = false
isNtuCacheEnabled = true
isUpiPassbookCacheEnabled = false

searchFields-userView-mapping = {\
                                    "searchFields.searchOtherName":"Name", \
                                    "searchFields.searchOtherBankName":"Bank Name", \
                                    "searchFields.searchOtherMobileNo":"Mobile No.", \
                                    "searchFields.searchPaymentSystem":"Account", \
                                    "searchFields.searchTxnIndication":"Type", \
                                    "searchFields.searchTxnCategory":"Type", \
                                    "searchFields.searchTxnStatus":"Status", \
                                    "searchFields.searchUthMerchantCategory":"Category", \
                                    "searchFields.searchOtherVpa":"UPI ID", \
                                    "searchFields.searchVoucherName":"Voucher", \
                                    "searchFields.searchWalletType":"Sub Wallet", \
                                    "searchFields.searchOtherAccountNo":"Account Number", \
                                    "searchFields.searchSelfBankName":"Bank Name", \
                                    "searchFields.searchTags":"Tag", \
                                    "searchFields.searchSelfAccountNo":"Account Number", \
                                    "searchFields.searchSelfAccTyp":"Account" }

#metadata api Oauth Call
oauth.call.enable.in.metadata.api = false


#ForEnabling Req Money Cta
enable.requestMoney.cta = true

ipoMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9pcG8ifQ==

#For routing to Dc cluster.
isListingRoutingEnabled = true
isDetailRoutingEnabled = true
isTrafficDiversionEnabled = true

#Maximum Days Diff for tagging a txn.
maxAllowedDayDiffForTagging = 30
springdoc.api-docs.path=/int/v3/api-docs
logging.level.root: DEBUG
manage.automatic.payment.cta.base.deeplink =  paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZX0sInBhdGgiOiIvIy9tdC9hdXRvbWF0aWMtcGF5bWVudHMvbWFuZGF0ZS1kZXRhaWxzIn0=&umn=
recurringMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9tYW5kYXRlcyJ9

uth.cst.txn.history.secret.key = uthcst#jwt@12345

uth.user.rate.limit.per.hour = 5
uth.user.rate.limit.hash.algo = SHA256
uth.user.rate.limit.enable = true

uth.analytics.send.to.kafka = true
ondc.verticalId = 204
showTagEnable = true
tagsUpdationEnable = true

min-amount-limit-for-amount-range-filter = 0
max-amount-limit-for-amount-range-filter = 1000000


uth.downstream.auth.circuit.breaker.config = {\
  "refreshPeriod":"60",\
  "limit":"2"}

uth.downstream.auth.circuit.breaker.enable = true

uth.downstream.auth.circuit.breaker.failure.httpcodes = 500,502,503,504,410,401

uth.user.daily.rate.limit.enable = false
uth.user.daily.rate.limit.threshold = 10

uth.user.rate.limit.apis.blacklisted = /transaction-history/ext/v1/metaData

txn.stream.lag.based.tracking.feature.enabled           = true
txn.stream.lag.based.txn.details.rejections.enabled     = true
txn.details.source.txn.stream.max.lag.rejection.buffer.seconds   = 1

cta.view-history-deeplink-for-onus-txns = paytmmp://mini-app?aId=840e7060809249e0a9b4a50a7b86388f&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvcGIvaGlzdG9yeS1saXN0In0=
merchant.type.change.based.on.oms.order.logic.enable = true

detailPage.listOfCtaType.enabled = CST_NEED_HELP,VIEW_UPI_LITE,SPLIT_BILL,DESCRIPTION,MANAGE_AUTOMATIC_PAYMENT