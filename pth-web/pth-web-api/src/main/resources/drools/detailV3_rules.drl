import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import static com.org.panaroma.web.utility.DroolsUtil.*;
import com.org.panaroma.web.dto.detailAPI.detailV3.detailResponseV3.*;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import static com.org.panaroma.web.utility.GenericUtilityExtension.getTransactionPurpose;
import static com.org.panaroma.web.utility.GenericUtilityExtension.isTxnPoweredByWallet;
import static com.org.panaroma.web.utility.GenericUtilityExtension.getPoweredByUrlEnabledInFooterLogo;
import static com.org.panaroma.web.utility.GenericUtilityExtension.isUserPpblParticipant;
import static com.org.panaroma.commons.utils.LogoUtility.getPoweredByWalletLogoUrl;
import static com.org.panaroma.commons.utils.LogoUtility.getPoweredByBankLogoUrl;
import static com.org.panaroma.web.utility.GenericUtilityExtension.isRecurringMandate;
import static com.org.panaroma.web.utility.GenericUtilityExtension.isRecurringMandateAndUpiCc;
import static com.org.panaroma.web.utility.GenericUtilityExtension.isUpiViaCcTxn;
import static com.org.panaroma.web.utility.GenericUtilityExtension.getPoweredByLogoUrlForUpiInstrument;

global com.org.panaroma.web.dto.detailAPI.detailV3.DetailApiResponseV3 response;

dialect  "mvel"

rule "maskAccountNumber"
    when
        $tthd : TransformedTransactionHistoryDetail(this != null);
    then
        //AccountNumber masking is done in format :
        //presMasking + AccountNumbersubstring(AcctNumberSize-endDigitsSize, AcctNumberSize) + postMasking
        String preMasking = " - ";
        String postMasking = "";
        int endDigitsSize = 4;
        getMaskedAccountNumberForV3(response.getSecondInstrument(), preMasking,
            postMasking, endDigitsSize);
end

rule "setRefundDetails"
    when
        tthd:TransformedTransactionHistoryDetail(
                        (getMainTxnType() in (TransactionTypeEnum.P2M.getTransactionTypeKey())));
    then
        setRefundDetails(response, tthd);
end

rule "setShowTags"
    when
        tthd : TransformedTransactionHistoryDetail(this != null);
    then
      	checkAndSetShowTagsFlag(response, tthd);

end

rule "setUpdateTags"
    when
        tthd : TransformedTransactionHistoryDetail(this != null);
    then
      	setTagUpdationFlag(response, tthd);

end

rule "populate_recurring_mandate_footer_logo_url" salience 12
    when
        tthd:TransformedTransactionHistoryDetail(isRecurringMandate(tthd))
    then
         response.setFooterLogoUrl(getPoweredByLogoUrlForUpiInstrument(tthd, null));
end

rule "populate_upi_cc_footer_logo_url" salience 11
    when
        tthd:TransformedTransactionHistoryDetail(isUpiViaCcTxn(tthd))
    then
         response.setFooterLogoUrl(getPoweredByLogoUrlForUpiInstrument(tthd, null));
end

rule "populate_recurring_mandate_and_upi_cc_footer_logo_url" salience 10
    when
        tthd:TransformedTransactionHistoryDetail(isRecurringMandateAndUpiCc(tthd))
    then
         response.setFooterLogoUrl(getPoweredByLogoUrlForUpiInstrument(tthd, null));
end

rule "populate_wallet_footer_logo_url" salience 10
    when
         eval(response.getPoweredByUpiLogoUrl() == null
                && getPoweredByUrlEnabledInFooterLogo()
                && isTxnPoweredByWallet(response));
    then
        response.setFooterLogoUrl(getPoweredByWalletLogoUrl());
end

rule "populate_bank_footer_logo_url" salience 11
    when
         eval(response.getPoweredByUpiLogoUrl() == null
            && response.getFooterLogoUrl() == null
            && getPoweredByUrlEnabledInFooterLogo())
         && tthd:TransformedTransactionHistoryDetail(isUserPpblParticipant(tthd));
    then
        response.setFooterLogoUrl(getPoweredByBankLogoUrl());
end

rule "datetimelabel_SbmdMandate_success_Create"
    when
        tthd:TransformedTransactionHistoryDetail(
                getMainTxnType() in (TransactionTypeEnum.SBMD_MANDATE.getTransactionTypeKey())
                && status == ClientStatusEnum.SUCCESS.getStatusKey()
                && eval("CREATE" == getTransactionPurpose(tthd)));
    then
        response.setDateTimeLabel("Blocked at");
end