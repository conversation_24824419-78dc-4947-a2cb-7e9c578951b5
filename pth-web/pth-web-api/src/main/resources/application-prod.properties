server.port                           = 10700

app.name                              = @project.artifactId@
app.version                           = @project.version@

external-integration-service-http-url = http://external-integration.default:80

es-host-list = uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
es-port                               = 9200
elastic-search-index                  = payment_history_alias
es-socket-timeout                     = 2000
es-max-retry-timeout                  = 2000
es-connect-timeout                    = 1000
es-connect-request-timeout            = 1000

static-bank-logo-base-url             = https://static.orgk.com/uth/images/bank-logo/
static-category-logo-base-url         = https://static.orgk.com/uth/images/category-logo/
static-paytm-logo-base-url            = https://static.orgk.com/uth/images/paytm-logo/
static-paymentMode-logo-base-url = https://static.orgk.com/uth/images/payment-intiation-mode/
static-passbook-singleapi-logo-base-url = https://tpap-logo.paytm.com/passbook/singleAPIlogos/images/

#Prometheus Monitoring Constants
prometheus.explorer                                         = UNIFIED_HISTORY_SERVICE
prometheus.hostname                                         = localhost
prometheus.port                                             = 8130

#auth

oauth.service.base.url                = https://accounts-plus-proxy.paytm.com

oauth.client.id                                    = payment-transaction-history-prod
oauth.client.secret                                = ${PTH_OAUTHCONFIG_CLIENTSECRET}
oauthClient.connectionProvider.name                = webflux
oauthClient.connectionProvider.maxConnections      = 500
oauthClient.connectionProvider.acquireTimeout      = 10000
oauthClient.connectionProvider.maxIdleTime         = 5
oauth.client.connection.timeout       = 800
oauth.read.timeout                    = 100


#bank-oauth
bank.oauth.service.base.url                        = http://oauth-internal.orgk.com
bank.oauth.service.url.category                    = /bank-oauth/ext
bank.oauth.client.id                               = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTID
bank.oauth.client.secret                           = MIDDLEWARE_PAYMENT_HISTORY_BANKOAUTH_CONFIG_CLIENTSECRET

#whetherto use bankOauth or not
use-bank-oauth = false

# initially this value will be picked from BO-Panel if it's not present there then this will be default value
# Whether to use OAuth tokeninfo API or not, if it's value is false then /v2/user will be used
use-tokenInfo-api = false

# from date to filter data based on backfill completion format: yyyy-MM-dd HH:mm:ss.SSS Z
# error code 4010 needs to be updated whenever this date changes
from-date-listing-filter = 2021-08-01 00:00:00.000 +0530
default-from-date-listing-month-duration = 1

#known - outage, any of our system down, to throw non-retriable error
known-issue-at-backend = false

kafka.bootstrap-servers = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
whitelisted-bank-users = *********,*********,7881770,*********,**********,2164859,*********,********,*********,9246658,*********,*********,*********,********,*********,1306150,*********,*********,*********,*********,*********,*********,7082329,**********,**********,9831647,********,********,********,2953086,*********,********,5840750,*********,7202709,*********,*********
show-bank-data = true
aerospike.namespace                = pth
aerospike.host-name                = uth-aerospike1-new.uth-prod.paytm.local
aerospike.port                     = 3000
aerospike.writePolicyDefault.sleepBetweenRetries  = 50
aerospike.writePolicyDefault.expiration = 300
aerospike.writePolicyDefault.socketTimeout = 500
aerospike.writePolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.sleepBetweenRetries = 30
aerospike.readPolicyDefault.totalTimeout = 900
aerospike.readPolicyDefault.socketTimeout = 400
localise.detail.fields=detailNarration,firstInstrument.narration,secondInstrument.narration,recentTransactionInfo.narration,cstorderItem.label,repeatPayment.name
batch.size=50
localise.listing.fields=narration
whitelisted-localisation-users = -1
prepopulate.data.list=Add Money Failed,Add Money Pending,Add Money to %s,Add Money to Paytm Payments Bank,Added Back to Your,Cashback Received,Cashback Received from %s,For Order At,For reversal of money transfer to,From,From Your,In,In Your,Money Added,Money Debited,Money On Hold,Money Paid,Money Received,Money Refunded,Money Restored,Money Sent,Money Transfer Failed,Money Transfer Pending,Money added to %s,Money added to Paytm Payments Bank,Money on hold for Order at %s,Money on hold released by %s,Paid to %s,Payment Failed,Payment Pending,Payment to %s,Received from %s,Refund Failed,Refund for failed transfer to %s,Refund from %s,Refund pending,Reversal of Money Sent,Sent to %s,To,To Your,Transfer to %s,Using Your,Settlement Received from %s,Gift Voucher Received from %s,Gift Voucher Sent To %s,For reversal of failed transfer to,Compensation Received,As compensation for,Automatic Payment made to %s,For Automatic Payment to,Refund Received,Interest earned for %s,Interest Received,Interest Earned,Cash Withdrawal at %s,Cash Withdrawal,At,Cash Withdrawal at,For,Cash Withdrawal at Paytm Bank Agent,ATM Cash Withdrawal at %s,At ATM,At Store,Refund for failed cash withdrawal,For reversal of cash withdrawal at ATM,Cash Deposited at %s,Cash Deposited,Cash Deposited with Paytm Bank Agent,Cash Deposit at ATM\,%s,At ATM Recycler,Cash Deposit,Salary Received,Flexi-Salary Received,Reimbursement Received,Charges Paid,Charges Paid for purchase of Paytm Payments Bank Debit Card,For Purchase of,For Cash Withdrawal at ATM,Charges Paid for transaction at ATM,For Transaction at ATM,For dispute raised against ATM Cash Withdrawal at,For dispute raised against store payment at,Cashback Received from Paytm Payments Bank,Cashback Received from Paytm,Cheque Return Charges,Aadhaar based direct benefit received,Recovery Deduction,Lien Recovery Reversal,Refund of excess card charges,Issuance Fee charged for Debit card,Added for failed transaction,Money transferred,Money paid to %s via cheque,Money paid via cheque,Money received from cheque payment,LPG subsidy received,Deducted by %s,Reversal of Debit Card fees,Charges Refunded,For Failed Purchase of,Charges Refunded for Debit Card,Annual Charge Paid for Debit Card,Charges Refunded for Cheque Book,Charges Refunded for cash withdrawal at ATM,Charges Refunded for ATM transaction,Charges Paid for payment at %s,For Payment at,Charges Refunded for payment at %s,Charges Paid for cash withdrawal at ATM,Charges Paid for ATM transaction,For dispute raised against,For ATM Transaction at,For Failed ATM Transaction at,For Failed Payment at,For dispute raised against online payment at,Charges paid for payment to %s,For Payment to,For Failed Payment to,Refund for failed cheque payment,Refund for failed cheque payment to %s,For reversal of Cheque Payment to,Money Deducted,For reversal of money received for,For reversal of money received from,Refund for failed payment to %s,Money reversed for failed transfer from %s,Refund for failed transfer via UPI,For reversal of,Fixed Deposit Created,Refund for failed Fixed Deposit creation,Fixed Deposit Redeemed,For reversal of failed,Refund of AePS fund transfer,Refund for AePS Cash withdrawal,AePS fund transfer,AePS Cash withdrawal,Paid Successfully,Recent Payments With %s,Paid,Received,Refunded,Recent Gift Vouchers,Sent,Added to %s,Pending Add Money to %s,Failed Add Money to %s,Created,Deposited at %s,Withdrawal at Paytm Bank Agent,Withdrawal at %s,Earned for %s,Need Help with this Payment,View more details,Foreign Remittance Received from,Paid for Outward Remittance to,Refund of Outward Remittance sent to,RECEIVED IN,Charges paid for Debit Card Dispute,Cashback received for payment at %s,Tip paid to %s,Tip Paid,Surcharge for payment at %s,Surcharge Paid,Refunded for payment at %s
salary.reportCodes = 90100,90110,90120

scheduler.cron.expression = 0 ${random.int[0,59]} 0 * * ?
clientConfig.cron.expression = 0 0 1 * * ?
spring.jpa.hibernate.ddl-auto                    = none
spring.datasource.url                            = *****************************************************
spring.datasource.username                       = pth_pc_w
spring.datasource.password                       = ${MW_PTH_DB_PASSWORD}
spring.datasource.driver-class-name              = com.mysql.jdbc.Driver
spring.jpa.show-sql                              = true
spring.datasource.configuration.maximum-pool-size=2
jwt.verification.enable=true

container.hostname                                = ${HOSTNAME}

configurable_properties-index-alias = configurable_properties_alias
configurable_properties.scheduler.cron.expression = * * * * * ?
configurable_properties.source = ElasticSearch
configurable_properties-index = configurable_properties-1
configurable_properties.source.elastic_search = ElasticSearch
configurable_properties.source.dynamoDB = dynamoDB
configurable_properties.source.table = configurable_properties

mapOf.ElasticSearchTemplate.templateFile          = {\
                                                      'configurable_properties_template': 'esConfigurationPropertiesTemplate.json' \
                                                   }
updateElasticSearchTemplate                      = true

metaDataApi.whitelisted.users = *********,*********,*********,*********,2164859,330511075,*********,********,*********,********,7881770,*********,********,16211563,418569378,*********,*********,118501134,3770465,9268107,99392518,*********,*********,11201492,*********,9178274,957604,131765382,2953086,267806444,298394047,230084261,255453517,244889640,*********,420071448,9831647,20422332,137938892,23024634,9268107,1240933502,182183537,1452122,*********,230084261,272527877,*********,9661621,204402215,131639348,54517546,*********,55772598,*********,*********,423215038,********,*********,2556302,16211563,399697124,418569378,117523406,20511073,3770465,125262162,2953086,2461554,7202709,14012198,27669850,401264800,2003295,30378965,24405049,4759401,********,230076407,1284033,25209101,1574406,6236193,24971888,108768010,957604,11395214,7524428,22615627,1284347,18286455,245178554,347748179,16386605,1242662593,58627652,191733975,286665205,600182887,342090935,197176405,738496,255971089,231017873,1089807822,25335912,231701817,600283353,1185507635,1172512886,31143459,188952843,2844400,24534571,1025968896,57437884,16386605,183503419,3366855,1147729658,7202709,56612796,437535,217145117
fetch-user-image-from-cache = true
push-missing-user-image-list-to-kafka = false

upi.timeline.baseUrl = https://upisecure.orgk.com
upi.timeline.uri =  /upi/int/txn/v4/transaction/status
upi.timeline.sync.connection.timeout = 500
upi.only.timeline.connection.timeout = 500
upi.timeline.socket.timeout = 500
upi.timeline.timeout = 500
upi.timeline.secret.key = ${UTH_UPI_TIMELINE_SECRET_KEY}

timeline.url.path = /transaction-history/ext/v2/{sourceContext}/detail?txnId={txnId}&status={status}&showOnlyTimeline=true
timeline.server = https://accounts-panaroma.orgk.com

#Kafka properties
kafka.target-v2-list[0].kafka-client-name               = recon_config
kafka.target-v2-list[0].kafka-producer-key              = RECON_CONFIG_DATA_PRODUCER
kafka.target-v2-list[0].topic                           = middleware_transaction_history_recon_config_data
kafka.target-v2-list[0].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://kafka-schema-registry.default:80/
kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = txnHistoryUserImageClient
kafka.target-v2-list[1].kafka-producer-key              = user_image_txn_history_producer
kafka.target-v2-list[1].topic                           = user_image_url_data
kafka.target-v2-list[1].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 500

kafka.target-v2-list[2].kafka-client-name               = uthAnalyticsKafkaClient
kafka.target-v2-list[2].kafka-producer-key              = uthAnalyticsProducer
kafka.target-v2-list[2].topic                           = uth-dwh-ingestion-topic
kafka.target-v2-list[2].bootstrap-servers               = pth-kafka1.uth-prod.paytm.local:9092,pth-kafka2.uth-prod.paytm.local:9092,pth-kafka3.uth-prod.paytm.local:9092,pth-kafka4.uth-prod.paytm.local:9092,pth-kafka5.uth-prod.paytm.local:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].linger-ms                       = 1
kafka.target-v2-list[2].request-timeout-ms              = 100

#Revisit these prop later
recon.config.cron1.delay.in.milli = 900000
recon.config.cron1.lock.at.most.in.milli = 500000

chat.url.path = paytmmp://chat?featuretype=start_chat
outwardInternationalRemittance.url = paytmmp://payment_bank?pageId=passbookPage&featuretype=outward_remittance

#managed-es-host = https://vpc-prod-v1-mid-paymenthistoryae-xtb44doayjgizjt4b53julngp4.ap-south-1.es.amazonaws.com
#whitelisted.users.list.for.managed.es = *********,*********,*********,*********,*********,7881770,*********,********,*********,*********,2953086
#whitelisted.users.list.for.managed.es = -1
#managed.roll.out.percentage = 100

#primary.es.cluster = V2
managed-es-v2-host=uth-es-app-nlb-d4fa226472071754.elb.ap-south-1.amazonaws.com
max-length-of-autoComplete-query = 20
max-Num-Of-ContactBook-Numbers = 4
contactbook-search-whitelisted-users-custId-Identifier = -1
contactbook-search-whitelisting-percentage = 100

oauth.token.evaluation.skip = false

rollout.config-list[0].percentage = 100
rollout.config-list[0].whitelisting-required = 1
rollout.config-list[0].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********,********,*********
rollout.config-list[0].whitelisting-for = detailPageRoleOutFeature

rollout.config-list[1].percentage = 0
rollout.config-list[1].whitelisting-required = 1
rollout.config-list[1].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********,*********,********
rollout.config-list[1].whitelisting-for = uthNtuCache

rollout.config-list[2].percentage = 0
rollout.config-list[2].whitelisting-required = 1
rollout.config-list[2].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,**********,*********,********
rollout.config-list[2].whitelisting-for = upiPassbookListingCache


rollout.config-list[3].percentage = 0
rollout.config-list[3].whitelisting-required = 1
rollout.config-list[3].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********
rollout.config-list[3].whitelisting-for = imps

rollout.config-list[4].percentage = 0
rollout.config-list[4].whitelisting-required = 1
rollout.config-list[4].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[4].whitelisting-for = DcListingRouting

rollout.config-list[5].percentage = 0
rollout.config-list[5].whitelisting-required = 1
rollout.config-list[5].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[5].whitelisting-for = UthAnalyticsWhiteListing

rollout.config-list[6].percentage = 0
rollout.config-list[6].whitelisting-required = 0
rollout.config-list[6].user-list = *********,*********,*********,*********,*********,*********,7881770,*********,********,**********,*********,**********,**********,**********,********,**********
rollout.config-list[6].whitelisting-for = appCacheOptimisation

rollout.config-list[7].percentage = 0
rollout.config-list[7].whitelisting-required = 1
rollout.config-list[7].user-list = **********
rollout.config-list[7].whitelisting-for = UserRateLimitingWhiteListing


detailPageApiCachingEnabled = true

dynamo.secret-key = ${UTH_DYNAMO_SECRET_KEY}
dynamo.access-key = ${UTH_DYNAMO_ACCESS_KEY}
dynamo.connection-timeout = 1000
dynamo.client-execution-timeout = 1000
dynamo.request-timeout = 1000
dynamo.socket-timeout = 1000
dynamo.max-error-retries = 10


#ForEnabling Req Money Cta
enable.requestMoney.cta = true

ipoMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9pcG8ifQ==

#For routing to Dc cluster.
isListingRoutingEnabled = true
isDetailRoutingEnabled = true

#Maximum Days Diff for tagging a txn.
maxAllowedDayDiffForTagging = 30
manage.automatic.payment.cta.base.deeplink =  paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7ImNhblB1bGxEb3duIjpmYWxzZSwic2hvd1RpdGxlQmFyIjpmYWxzZX0sInBhdGgiOiIvIy9tdC9hdXRvbWF0aWMtcGF5bWVudHMvbWFuZGF0ZS1kZXRhaWxzIn0=&umn=
recurringMandate.h5.base.url = paytmmp://mini-app?aId=bf0fde1e742c426bbaacadab0e33b09e&data=eyJzcGFyYW1zIjp7InNob3dUaXRsZUJhciI6ZmFsc2V9LCJwYXRoIjoiLyMvbXQvYXV0b21hdGljLXBheW1lbnRzL2ZhcS9tYW5kYXRlcyJ9
ondc.verticalId = 204
showTagEnable = true
tagsUpdationEnable = true

uth.user.rate.limit.per.hour = 360
uth.user.rate.limit.hash.algo = SHA256
uth.user.rate.limit.enable = false
uth.analytics.send.to.kafka = true

min-amount-limit-for-amount-range-filter = 0
max-amount-limit-for-amount-range-filter = 1000000

uth.downstream.auth.circuit.breaker.config = {\
  "refreshPeriod":"5",\
  "limit":"40"}

uth.downstream.auth.circuit.breaker.enable = false
uth.downstream.auth.circuit.breaker.failure.httpcodes = 500,502,503,504
uth.downstream.auth.circuit.breaker.failure.classes = RequestTimeOutException

uth.user.daily.rate.limit.enable = false
uth.user.daily.rate.limit.threshold = 10

uth.user.rate.limit.apis.blacklisted = /transaction-history/ext/v1/metaData

# Powered By Logo mapping wrt upiHandle
poweredBy.upiLogo.list = paytm