package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.APP_CACHE_INVALIDATE_VERSION;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.DETAIL_BLACKLISTED_STREAM_SOURCE;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.FILTER_SOURCE;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.SPEND_PAGE;
import static com.org.panaroma.commons.constants.WebConstants.API_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.CUSTOMER_CREATION_DATE;
import static com.org.panaroma.commons.constants.WebConstants.DATE;
import static com.org.panaroma.commons.constants.WebConstants.DATE_RANGE_VALUE;
import static com.org.panaroma.commons.constants.WebConstants.ENTITY_ID;
import static com.org.panaroma.commons.constants.WebConstants.FROM_DATE_LISTING_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_FROM_DATE_FORMAT;
import static com.org.panaroma.commons.constants.WebConstants.ONE;
import static com.org.panaroma.commons.constants.WebConstants.PAGE_NO;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_SYSTEM;
import static com.org.panaroma.commons.constants.WebConstants.RoutingConstants.CURRENT_DATE_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.RoutingConstants.UTH_V2_LISTING_URL;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_API_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.TO_DATE;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.UPI_CC_FILTER_FROM_DATE;
import static com.org.panaroma.commons.constants.WebConstants.ONUS_VERTICAL_FILTER_FROM_DATE;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.CUSTOM_RETRY;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NO_TRANSACTION_FOUND_FOR_LISTING;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_URI;

import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.enums.ApiVersion;
import com.org.panaroma.commons.enums.PthVersion;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.commons.utils.PthVersionUtility;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.dto.UpdateResponseDto;
import com.org.panaroma.web.enums.ListingApisEnum;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@Log4j2
public class RoutingUtility {

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	private static RolloutStrategyHelper rolloutStrategyHelper;

	RoutingUtility(final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final RolloutStrategyHelper rolloutStrategyHelper) {
		RoutingUtility.configurablePropertiesHolder = configurablePropertiesHolder;
		RoutingUtility.rolloutStrategyHelper = rolloutStrategyHelper;
	}

	/**
	 * This will preProcess the Listing request before passing it to actual service.
	 * <li>if we only get txnDate in pagination -> will throw 507. which will be
	 * redirected to dc cluster.
	 * <li>if "isListingRoutingEnabled" is marked false(Mostly in case of prodMtDc) and we
	 * only get txnDate in pagination params. then will remove this txnDate from
	 * pagination and add it to toDate of request.
	 * <li>This never blocks any request based on any criteria on DC servers.
	 * <li>On AWS servers, blocks if custom paginations are used. Throws exception for
	 * re-rerouting to DC.
	 *
	 */
	public static void preProcessListingRequestForRouting(final Map<String, String> paramMap) throws PanaromaException {
		Long currentFromDate = null;
		try {
			if (Boolean.FALSE.equals(isRequestValidForOneYearHistory(paramMap))) {
				return; // will return from here
			}

			/*
			 * Due to issue at android end. For pullToRefresh request app is sending
			 * pagination params of last scrolled page with page No 1. Which is causing
			 * issue when last scrolled page is of dc. Removing pagination params from
			 * page no.1
			 */
			removedPaginationParamsFromPageOne(paramMap);
			if (Boolean.FALSE
				.equals(configurablePropertiesHolder.getProperty("isListingRoutingEnabled", Boolean.class))) {

				if (isRequestForDcPageOne(paramMap)) {
					paramMap.put(TO_DATE, paramMap.get("transactionDateEpoch"));
					paramMap.remove("transactionDateEpoch");
					paramMap.put("isDcPageOne", TRUE);
				}

				return;
			}
			currentFromDate = new SimpleDateFormat(LISTING_FROM_DATE_FORMAT)
				.parse(configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class))
				.getTime();
		}
		catch (Exception e) {
			log.error("Exception while applying preProcessing for listing for routing : {}",
					CommonsUtility.exceptionFormatter(e));
		}
		if ((Objects.nonNull(paramMap.get("transactionDateEpoch")) && Objects.nonNull(currentFromDate)
				&& Long.parseLong(paramMap.get("transactionDateEpoch")) <= currentFromDate)
				|| UTH_V2_LISTING_URL.equals(paramMap.get("nextListingUrl"))) {
			throw ExceptionFactory.getException(PANAROMA_SERVICE, CUSTOM_RETRY);
		}

		// Checking for Listing APIs v2 url. if it then through custom routing error
		if (ListingApisEnum.isListingNextUrlKeyV2(MdcUtility.getConstantValue(API_URI),
				paramMap.get("nextListingUrl"))) {
			throw ExceptionFactory.getException(PANAROMA_SERVICE, CUSTOM_RETRY);
		}
	}

	/**
	 * This will preProcess the Detail request before passing it to actual service. 1. if
	 * txnDate is less then current fromDate -> route it to DC cluster.
	 */
	public static void preProcessDetailRequestForRouting(final String passedIds) {
		Long currentFromDate = null;
		String txnDate = null;
		try {
			if (Boolean.FALSE
				.equals(configurablePropertiesHolder.getProperty("isDetailRoutingEnabled", Boolean.class))) {
				return;
			}

			String[] idPair = WebControllerUtility.getIdToQueryEs(passedIds);
			if (idPair.length >= 2) {
				txnDate = idPair[1];
			}
			currentFromDate = new SimpleDateFormat(LISTING_FROM_DATE_FORMAT)
				.parse(configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class))
				.getTime();
		}
		catch (Exception e) {
			log.error("Exception while applying preProcessing for detail for routing : {}",
					CommonsUtility.exceptionFormatter(e));
		}
		if (StringUtils.isNotBlank(txnDate) && Objects.nonNull(currentFromDate)
				&& Long.parseLong(txnDate) < currentFromDate) {
			throw ExceptionFactory.getException(PANAROMA_SERVICE, CUSTOM_RETRY);
		}
	}

	public static void preProcessDetailRequestForRouting(final String passedIds, TransactionSource transactionSource) {
		preProcessDetailRequestForRouting(passedIds);
		if (Objects.nonNull(transactionSource)) {
			List<String> detailBlackListedStreamSource = configurablePropertiesHolder
				.getProperty(DETAIL_BLACKLISTED_STREAM_SOURCE, List.class);

			if (ObjectUtils.isNotEmpty(detailBlackListedStreamSource) && detailBlackListedStreamSource
				.contains(String.valueOf(transactionSource.getTransactionSourceKey()))) {
				throw ExceptionFactory.getException(PANAROMA_SERVICE, CUSTOM_RETRY);
			}
		}
	}

	public static UpdateResponseDto postProcessListingRequestForRoutingUpdates(final UpdateResponseDto responseDto,
			final Map<String, String> paramMap) {
		if (responseDto.isInvalidateStoredData()) {
			paramMap.put(PAGE_NO, ONE);
			postProcessListingRequestForRouting(responseDto, paramMap);
		}
		return responseDto;
	}

	/**
	 * Post-processing for routing to Dc cluster.<br>
	 * If pagination params are not present add currentFromDate as txnDateEpoch in
	 * pagination param. No profile specific changes as we assume that we will keep
	 * "isListingRoutingEnabled" flag true in aws profile only.
	 * @param responseDto response to be modified
	 * @param paramMap param map that came in request
	 * @return resposeDto same as request
	 */
	public static ResponseDto postProcessListingRequestForRouting(final ResponseDto responseDto,
			final Map<String, String> paramMap) {
		try {
			if (Boolean.FALSE.equals(isRequestValidForOneYearHistory(paramMap))) {
				return responseDto;
			}

			// If pagination params are available add next listing url to it.
			if ((paramMap.getOrDefault(SEARCH_API_VERSION, ApiVersion.v3.name()).compareTo(ApiVersion.v3.name()) >= 0)
					&& Objects.nonNull(responseDto.getPaginationParams())
					&& Objects.nonNull(responseDto.getPaginationParams().getPaginationTxnId())) {
				responseDto.getPaginationParams()
					.setNextListingUrl(configurablePropertiesHolder.getProperty("listingUrl", String.class));

				// Setting next listing URL for new Listing APIs
				if (ListingApisEnum.isListingApi(MdcUtility.getConstantValue(API_URI))) {
					ListingApisEnum listingApisEnum = ListingApisEnum
						.getListingApiEnum(MdcUtility.getConstantValue(API_URI));
					responseDto.getPaginationParams().setNextListingUrl(listingApisEnum.getApiNextListingUrlKey());
				}

				// Adding BG APP SYNC Next Page Listing URL
				responseDto.getPaginationParams()
					.setBgAppSyncNextListingUrl(
							configurablePropertiesHolder.getProperty("bgAppSyncListingUrl", String.class));

			}

			if (Boolean.FALSE
				.equals(configurablePropertiesHolder.getProperty("isListingRoutingEnabled", Boolean.class))) {
				return responseDto;
			}

			// returns from here for DC UTH servers
			// below codes are for AWS UTH servers only

			// UserWhitelisting checks.

			if ((paramMap.getOrDefault(SEARCH_API_VERSION, ApiVersion.v3.name()).compareTo(ApiVersion.v3.name()) < 0)
					&& Boolean.FALSE.equals(
							rolloutStrategyHelper.isUserWhiteListed("DcListingRouting", responseDto.getEntityId()))) {
				return responseDto;
			}
			String currentFromDateString = configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER,
					String.class);

			Long currentFromDate = DateTimeUtility.listingDateFormat.parse(currentFromDateString).getTime();

			/*
			 * We don't need to go to pth-v2-service for old txns in case upi cc filter
			 * from date is greater than or equal to pth-service from date. So return from
			 * here & don't let custom pagination params be created for pth-v2-service in
			 * below code
			 */
			if (GenericUtilityExtension.isUpiViaCcFilterRequest(paramMap) && DateTimeUtility.isFirstDateLatest(
					configurablePropertiesHolder.getProperty(UPI_CC_FILTER_FROM_DATE, String.class),
					currentFromDateString)) {
				return responseDto;
			}

			/*
			 * We don't need to go to pth-v2-service for old txns in case onus vertical
			 * filter from date is greater than or equal to pth-service from date. So
			 * return from here & don't let custom pagination params be created for
			 * pth-v2-service in below code
			 */
			if (GenericUtilityExtension.isOnusVerticalFilterRequest(paramMap) && DateTimeUtility.isFirstDateLatest(
					configurablePropertiesHolder.getProperty(ONUS_VERTICAL_FILTER_FROM_DATE, String.class),
					currentFromDateString)) {
				return responseDto;
			}

			/**
			 * before setting Pagination param we will make use of customerCreationDate,
			 * if customerCreationDate is greater than awsFromDate i.e. if fromDate is
			 * 30th August and customerCreationDate is 30th September it means user don't
			 * have txns in DC server so we don't need to pass the paginationParam as this
			 * will be the last page
			 */
			if (Boolean.TRUE.equals(
					configurablePropertiesHolder.getProperty("isUseOfCustomerCreationDateEnabled", Boolean.class))) {
				String customerCreationDate = paramMap.getOrDefault(CUSTOMER_CREATION_DATE, null);
				if (customerCreationDate != null && currentFromDate != null
						&& (Long.parseLong(customerCreationDate) > currentFromDate)) {
					log.info(
							"Not setting paginationParam for next page hit as customerCreationDate :{} is greater than currentFromDate :{}",
							customerCreationDate, currentFromDate);
					return responseDto;
				}
			}

			if (!validForAwsToDcRoutingForDateFilter(paramMap)) {
				return responseDto;
			}

			/*
			 * Reduce one millisecond from current fromDate. So one milliseconds duplicate
			 * txns can be avoided.
			 */
			if (Objects.isNull(responseDto.getPaginationParams())
					|| Objects.isNull(responseDto.getPaginationParams().getPaginationTxnId())) {

				int pageNo = Integer.parseInt(paramMap.getOrDefault("pageNo", ONE));
				Long lastTxnTxnDate = null;
				if (responseDto.getTxns().size() > 0) {
					lastTxnTxnDate = responseDto.getTxns().get(responseDto.getTxns().size() - 1).getTxnDate();
					// reducing lastTxnTxnDate by 1 milli second.So that same txn won't
					// get picked from routed server.
					lastTxnTxnDate = lastTxnTxnDate - 1;
				}
				String fromUpdatedDate = (responseDto.getPaginationParams() != null)
						? responseDto.getPaginationParams().getFromUpdatedDate() : null;
				PaginationParams paginationParams = PaginationParams.builder()
					.nextListingUrl(getV2ListingUrl()) // Setting next listing URL for
														// Listing APIs
					.transactionDateEpoch(CURRENT_DATE_IDENTIFIER)
					.pageNo(pageNo + 1)
					.fromUpdatedDate(fromUpdatedDate)
					.paginationTxnId(null)
					.paginationStreamSource(null)
					.build();
				if (!configurablePropertiesHolder.getProperty("fullScanForSecondaryDbEnabled", Boolean.class)
						&& Objects.nonNull(lastTxnTxnDate)) {
					paginationParams.setTransactionDateEpoch(String.valueOf(lastTxnTxnDate));
				}
				responseDto.setPaginationParams(paginationParams);
				log.info("passing pagination params for dcRouting, entityId : {} : {}", responseDto.getEntityId(),
						paginationParams);
			}
		}
		catch (Exception e) {
			log.error("Exception while applying post processing on listing for routerService : {}",
					CommonsUtility.exceptionFormatter(e));
		}
		return responseDto;
	}

	private static String getV2ListingUrl() {
		if (ListingApisEnum.isListingApi(MdcUtility.getConstantValue(API_URI))) {
			ListingApisEnum listingApisEnum = ListingApisEnum.getListingApiEnum(MdcUtility.getConstantValue(API_URI));
			return listingApisEnum.getApiNextListingUrlKeyForV2();
		}
		return UTH_V2_LISTING_URL;
	}

	/**
	 * Checks if the data range allows AWS to DC routing. The fromDate in date filter
	 * should be greater than the from-date
	 * @param paramMap params of request
	 * @return true if data is expected to be in the DC servers
	 */
	protected static boolean validForAwsToDcRoutingForDateFilter(final Map<String, String> paramMap) {
		if (StringUtils.isNotBlank(paramMap.get(DATE)) || StringUtils.isNotBlank(paramMap.get(DATE_RANGE_VALUE))) {
			String filterFromDate;
			if (StringUtils.isNotBlank(paramMap.get(DATE_RANGE_VALUE))) {
				filterFromDate = paramMap.get(DATE_RANGE_VALUE).split(",")[0];
			}
			else {
				filterFromDate = paramMap.get(DATE).split("-")[0];
			}
			try {
				long serverFromDate = new SimpleDateFormat(LISTING_FROM_DATE_FORMAT)
					.parse(configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class))
					.getTime();
				return Long.parseLong(filterFromDate) < serverFromDate;
			}
			catch (Exception e) {
				log.error("Exception while parsing. Forwarding request to DC. {}",
						CommonsUtility.exceptionFormatter(e));
			}
			return false;
		}
		else {
			return true;
		}

	}

	/**
	 * This Flag will not be set for 4010(nonTransacting) requests routed to DC. As these
	 * requests will not have custom modified paginations instead will be same as aws
	 * pageNo 1 request.
	 */
	public static boolean isDcFirstPage(final Map<String, String> paramMap) {
		return Objects.nonNull(paramMap) && TRUE.equals(paramMap.get("isDcPageOne"));
	}

	private static void removedPaginationParamsFromPageOne(final Map<String, String> paramMap) {
		if (StringUtils.isBlank(paramMap.get("pageNo")) || ONE.equals(paramMap.get("pageNo"))) {
			paramMap.remove("transactionDateEpoch");
			paramMap.remove("paginationTxnId");
			paramMap.remove("paginationStreamSource");
		}
	}

	public static Mono<? extends ResponseDto> handle4010(final Map<String, String> paramMap,
			final Throwable exception) {
		if (Boolean.FALSE.equals(isNonTransactingException(exception))) {
			return Mono.error(exception);
		}
		if (isDcFirstPage(paramMap)) {
			return handle4010ForDc(paramMap, exception);
		}
		else {
			return handle4010ForAws(paramMap, exception);
		}
	}

	public static Mono<? extends ResponseDto> handle4010ForDc(final Map<String, String> paramMap,
			final Throwable exception) {
		if (Objects.nonNull(paramMap) && Boolean.FALSE
			.equals(rolloutStrategyHelper.isUserWhiteListed("DcNonTransactingHandling", paramMap.get(ENTITY_ID)))) {
			return Mono.error(exception);
		}
		ResponseDto emptyResponse = ResponseDto.builder()
			.setCurrentPage(paramMap.get(PAGE_NO))
			.setTxns(Collections.emptyList())
			.build();
		if (paramMap.getOrDefault(SEARCH_API_VERSION, ApiVersion.v3.name())
			.compareToIgnoreCase(ApiVersion.v3.name()) >= 0) {
			emptyResponse.setInvalidateVersion(
					configurablePropertiesHolder.getProperty(APP_CACHE_INVALIDATE_VERSION, Integer.class));
		}
		return Mono.just(emptyResponse);
	}

	/**
	 * Handling 4010 on aws, so that nonTransactingUser on aws can view txns from dc also.
	 * Will directly throw 507 http exception to route to Dc in case of
	 * nonTransacting(4010) Exception. As Passing empty Response with customPaginations is
	 * not handled at app end.
	 *
	 * if nonTransacting(4010) request routed to Dc also throws 4010 at DC then it will be
	 * handled here. As 4010(nonTransacting) requests routed to DC will be treated as
	 * normal aws pageNo 1 hit only. As these requests will not have custom modified
	 * pagination params instead will be same as aws pageNo 1 request.
	 */
	public static Mono<? extends ResponseDto> handle4010ForAws(final Map<String, String> paramMap,
			final Throwable exception) {

		String customerCreationDate = paramMap.getOrDefault(CUSTOMER_CREATION_DATE, null);
		String paymentSystem = paramMap.getOrDefault(PAYMENT_SYSTEM, "");

		if (paymentSystem.contains("upi_lite") && Boolean.FALSE
			.equals(configurablePropertiesHolder.getProperty("nonTransactingRoutingToDc_upiLite", Boolean.class))) {
			return Mono.error(exception);
		}

		if (!paymentSystem.contains("upi_lite") && Boolean.FALSE
			.equals(configurablePropertiesHolder.getProperty("nonTransactingRoutingToDc_allTxns", Boolean.class))) {
			return Mono.error(exception);
		}

		if (isRequestValidForHandling4010OnAws(paramMap)) {
			Long currentFromDate = null;
			if (paymentSystem.contains("upi_lite") || Boolean.TRUE
				.equals(configurablePropertiesHolder.getProperty("nonTransactingRoutingToDc_allTxns", Boolean.class))) {
				try {
					currentFromDate = new SimpleDateFormat(LISTING_FROM_DATE_FORMAT)
						.parse(configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class))
						.getTime();
				}
				catch (ParseException e) {
					log.error("Error in parsing from Date :{}",
							configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class));
				}

				/**
				 * if this condition satisfies than it means user doesn't have transaction
				 * in DC as user arrived on PAYTM after UTH AWS from date , so in this
				 * case we don't need to route the request on DC as there it will again
				 * throw 4010
				 */
				if (customerCreationDate != null && currentFromDate != null
						&& (Long.parseLong(customerCreationDate) > currentFromDate)) {
					log.info(
							"User is NTU on AWS and his customerCreationDate > currentFromDate {} > {} ,then routing to DC will still result in"
									+ " the same exception so throwing 4010 and not throwing 507",
							customerCreationDate, currentFromDate);
					return Mono.error(exception);
				}
				if (!validForAwsToDcRoutingForDateFilter(paramMap)) {
					return Mono.error(exception);
				}
				throw ExceptionFactory.getException(PANAROMA_SERVICE, CUSTOM_RETRY);
			}
		}
		return Mono.error(exception);
	}

	public static boolean isNonTransactingException(final Throwable exception) {
		return exception instanceof PanaromaException
				&& NO_TRANSACTION_FOUND_FOR_LISTING.equals(((PanaromaException) exception).getResponseCode());
	}

	private static boolean isRequestValidForOneYearHistory(final Map<String, String> paramMap) {
		/*
		 *
		 * Date Range and spend filter will not be supported currently. As query for these
		 * filters are on alias.
		 */
		return !SPEND_PAGE.equalsIgnoreCase(paramMap.get(FILTER_SOURCE));
	}

	public static boolean isRequestForDcPageOne(final Map<String, String> paramMap) {
		return StringUtils.isBlank(paramMap.get("paginationTxnId"))
				&& StringUtils.isNotBlank(paramMap.get("transactionDateEpoch"));
	}

	/**
	 * Handling 4010 on aws currently only for upi_lite.
	 */
	public static boolean isRequestValidForHandling4010OnAws(final Map<String, String> paramMap) {
		return isRequestValidForOneYearHistory(paramMap);
	}

	public static ResponseDto getEmptyResponseWithCustomPageNo(final Map<String, String> paramMap,
			final String pageNo) {
		ResponseDto emptyResponse = ResponseDto.builder()
			.setCurrentPage(pageNo)
			.setTxns(Collections.emptyList())
			.build();
		if (PthVersionUtility.isRequestPthVersionGreaterThanOrEqualTo(PthVersion.V_1_0)
				|| paramMap.getOrDefault(API_VERSION, ApiVersion.v3.name())
					.compareToIgnoreCase(ApiVersion.v3.name()) >= 0) {
			emptyResponse.setInvalidateVersion(
					configurablePropertiesHolder.getProperty(APP_CACHE_INVALIDATE_VERSION, Integer.class));
		}
		return emptyResponse;
	}

	/**
	 * This checks for the case of page no : -1, which is the default pageNo in pagination
	 * parmas. This resolves the issue with app contract, where if paginationParam !=
	 * null, it means the app has to hit fort next page, and then the app hits for next
	 * page with null pageNo. This results in a loop.
	 * @param paramMap paramMao in current request. Used to fetch page no
	 * @return returns true, if it is the -1 page request mentioned in the description
	 * above.
	 */
	public static boolean checkIfNegativePage(final Map<String, String> paramMap) {
		return paramMap.get(PAGE_NO) != null && paramMap.get(PAGE_NO).equals("-1");
	}

}
