package com.org.panaroma.web.controller.nonMonoController;

import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.dto.PassbookAccountType;
import com.org.panaroma.commons.enums.ApiVersion;
import com.org.panaroma.commons.utils.LoggerUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.commons.utils.PthVersionUtility;
import com.org.panaroma.web.dto.BaseResponse;
import com.org.panaroma.web.dto.Client;
import com.org.panaroma.web.dto.SearchAppRequest;
import com.org.panaroma.web.dto.listing.v4.ListingResponseV4;
import com.org.panaroma.web.dto.updates.v2.UpdatesResponseV4;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.service.nonMono.TransactionHistoryV4Service;
import com.org.panaroma.web.utility.ErrorResponseUtility;
import com.org.panaroma.web.utility.HttpRequestMonitoringUtility;
import com.org.panaroma.web.utility.VirtualDateHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.Constants.V2;
import static com.org.panaroma.commons.constants.Constants.V4;
import static com.org.panaroma.commons.constants.WebConstants.AUTHORIZATION;
import static com.org.panaroma.commons.constants.WebConstants.CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.OPEN_SOURCE;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PTH_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_API_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.USER_ID;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.MAPPER_EXCEPTION;
import static com.org.panaroma.web.monitoring.MonitoringConstants.FAILURE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SEARCH;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SUCCESS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API;

@RestController
@Log4j2
@RequiredArgsConstructor
@RequestMapping({ "/pth/ext/v4" })
public class PanaromaWebControllerV4NonMono {

	private final TransactionHistoryV4Service transactionHistoryV4Service;

	private final ErrorResponseUtility errorResponseUtility;

	private final HttpRequestMonitoringUtility httpRequestMonitoringUtility;

	@PostMapping(value = { "/search", "/bg/listing", "/listing", "/listing/filter", "/{accountType}/listing",
			"/{accountType}/listing/filter" }, produces = "application/json; charset=UTF-8")
	public Object search(
			@PathVariable(name = "accountType", required = false) final PassbookAccountType passbookAccountType,
			@RequestHeader(AUTHORIZATION) final Map<String, String> tokenMap,
			@RequestHeader(value = CLIENT) final Client client,
			@RequestHeader(value = "userid", required = false) final String userId,
			@RequestHeader(value = OPEN_SOURCE, required = false) final String openSource,
			@RequestParam final Map<String, String> paramMap, @RequestBody final SearchAppRequest searchAppRequest)
			throws Throwable {

		long apiStartTime = Instant.now().toEpochMilli();
		try {
			// pth_version less than 6 is not valid for this api.
			if (!PthVersionUtility.isRequestValidForListingV4()) {
				log.info("Invalid pth_version for search api, pth_version: {}, request: {}, paramMap : {}",
						MdcUtility.getConstantValue(PTH_VERSION), searchAppRequest, paramMap);
				throw ExceptionFactory.getException(PANAROMA_SERVICE, MAPPER_EXCEPTION);
			}
			paramMap.put(SEARCH_API_VERSION, ApiVersion.v4.name());
			if (StringUtils.isNotBlank(userId)) {
				paramMap.put("userId", userId);
			}

			if (StringUtils.isNotBlank(openSource)) {
				paramMap.put(OPEN_SOURCE, openSource);
			}
			httpRequestMonitoringUtility.preprocessRequestAndPushMetrics(paramMap, searchAppRequest, tokenMap, client,
					"search", V4);

			// Adding this flag to make use of this if Virtual From Date flag is enabled
			// or not
			paramMap.put(ConfigPropertiesEnum.VIRTUAL_FROM_DATE_OVER_FROM_DATE_ENABLED.getKey(),
					VirtualDateHelper.getVirtualFromDateOverFromDateEnabledFlag());
			try {
				ListingResponseV4 response;
				response = transactionHistoryV4Service.preProcessRequestWithVirtualFromDateIdentifier(paramMap);
				if (Objects.nonNull(response)) {
					return response;
				}
				response = transactionHistoryV4Service.search(paramMap, tokenMap);

				// push execution time metrics for success response
				httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, SEARCH,
						V4, SUCCESS, apiStartTime);
				return response;
			}
			catch (Exception ex) {
				// push execution time metrics for failure response
				BaseResponse baseResp = this.errorResponseUtility.handleError((Throwable) ex, SEARCH,
						paramMap.get(CLIENT), paramMap);
				httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, SEARCH,
						V4, FAILURE, apiStartTime);
				return baseResp;
			}

		}
		catch (Exception e) {
			httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, SEARCH, V4,
					FAILURE, apiStartTime);
			throw e;
		}
	}

	@PostMapping(value = { "/updates" }, produces = "application/json; charset=UTF-8")
	public Object updates(@RequestHeader(AUTHORIZATION) final Map<String, String> tokenMap,
			@RequestHeader(value = CLIENT) final com.org.panaroma.web.dto.Client client,
			@RequestParam final Map<String, String> paramMap, @RequestBody final SearchAppRequest searchAppRequest,
			@RequestHeader(value = "userid", required = false) final String userId) throws Exception {

		// pth_version less than 6 is not valid for this api.
		if (!PthVersionUtility.isRequestValidForListingV4()) {
			log.info("Invalid pth_version for updates api, pth_version: {}, request: {}, paramMap : {}",
					MdcUtility.getConstantValue(PTH_VERSION), searchAppRequest, paramMap);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, MAPPER_EXCEPTION);
		}

		if (Objects.nonNull(userId)) {
			paramMap.put(USER_ID, userId);
			MdcUtility.populateConstant(USER_ID, userId);
		}

		long apiStartTime = Instant.now().toEpochMilli();
		try {
			// pth_version less than 5 is not valid for this api.
			if (!PthVersionUtility.isRequestValidForListingV4()) {
				log.info("Invalid pth_version for search api, pth_version: {}, request: {}, paramMap : {}",
						MdcUtility.getConstantValue(PTH_VERSION), searchAppRequest, paramMap);
				throw ExceptionFactory.getException(PANAROMA_SERVICE, MAPPER_EXCEPTION);
			}
			httpRequestMonitoringUtility.preprocessRequestAndPushMetrics(paramMap, searchAppRequest, tokenMap, client,
					UPDATES_API, V2);
			log.info("request received for updates with parameters {} & token : {}", paramMap,
					LoggerUtility.createMaskedMap(tokenMap));
			try {
				UpdatesResponseV4 response;
				response = transactionHistoryV4Service.getUpdates(paramMap, tokenMap);
				// push execution time metrics for success response
				httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, UPDATES,
						V2, SUCCESS, apiStartTime);
				return response;
			}
			catch (Exception exception) {
				BaseResponse baseResponse = this.errorResponseUtility.handleError((Throwable) exception, UPDATES,
						paramMap.get(CLIENT), paramMap);
				// push execution time metrics for failure response
				httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, UPDATES,
						V2, FAILURE, apiStartTime);
				return baseResponse;
			}
		}
		catch (Exception e) {
			httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, UPDATES, V2,
					FAILURE, apiStartTime);
			throw e;
		}
	}

}
