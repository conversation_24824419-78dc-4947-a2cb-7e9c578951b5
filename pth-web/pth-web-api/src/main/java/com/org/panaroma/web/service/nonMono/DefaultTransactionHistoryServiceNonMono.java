package com.org.panaroma.web.service.nonMono;

import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE;
import static com.org.panaroma.commons.constants.CommonConstants.ONE_DAY_IN_MILLIS;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_FAILURE_ERROR_CODES_LIST;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_LIST;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.FILTER_SOURCE;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.SPEND_PAGE;
import static com.org.panaroma.commons.constants.WebConstants.*;
import static com.org.panaroma.commons.utils.AppCacheUtility.getCacheState;
import static com.org.panaroma.commons.utils.DateTimeUtility.getCustomerCreationDateFormattedDate;
import static com.org.panaroma.commons.utils.UtilityExtension.isOnlineTxn;
import static com.org.panaroma.commons.utils.UtilityExtension.isOnusTransaction;
import static com.org.panaroma.web.constants.CacheConstants.CacheName.DETAIL_API_CACHE;
import static com.org.panaroma.web.constants.CacheConstants.DETAIL_PAGE_API_CACHING_ENABLED;
import static com.org.panaroma.web.constants.CacheConstants.DETAIL_PAGE_ROLE_OUT_FEATURE_NAME;
import static com.org.panaroma.web.constants.metadataconstants.Constants.DETAILS_API_PENDING_STATUS_TTL_CACHE;
import static com.org.panaroma.web.constants.metadataconstants.Constants.UPDATES_API_FROM_DATE_GAP;
import static com.org.panaroma.web.constants.metadataconstants.Constants.UPDATES_API_PAGE_SIZE;
import static com.org.panaroma.web.constants.metadataconstants.Constants.DETAILS_API_TTL_NON_PENDING_DAYS_WISE_CACHE;
import static com.org.panaroma.web.constants.timelineconstants.TimelineConstants.TIMELINE_ERROR_MSG_MAP;
import static com.org.panaroma.web.dto.detailAPI.detailV3.DetailResponseV3Mapper.mapDetailResponseV3UsingDetailResponseV2;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.KNOWN_ISSUE_ERROR;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NO_TRANSACTION_FOUND_FOR_LISTING;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_NAME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.CACHE_DATE_DIFFERENCE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.monitoring.MonitoringConstants.CRITERIA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.DETAIL_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ERROR_THROWN;
import static com.org.panaroma.web.monitoring.MonitoringConstants.IS_SERVED_FROM_CACHE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.LOCALE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.NOT_AVAILABLE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.REQUEST_ID;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RESPONSE_TIME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SEARCH_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SERVED_FROM_ZERO_DELTA_CACHE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.STATUS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TXN_DATE_ABSENT_IN_DETAIL_REQUEST;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TXN_DATE_DIFFERENCE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API_FROM_DATE_GAP_METRICS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API_INVALIDATION;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API_NO_TXNS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.monitoringTagFormat;
import static com.org.panaroma.web.utility.GenericUtilityExtension.getFooterLogoTxnType;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.constants.ConfigurationPropertiesConstants;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.PspInfo;
import com.org.panaroma.commons.dto.cache.CacheState;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.enums.CtaType;
import com.org.panaroma.commons.enums.PthVersion;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.PthVersionUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.rollout.strategy.IRolloutStrategyHelper;
import com.org.panaroma.rule.engine.service.RuleEngineService;
import com.org.panaroma.web.AbstractSearchContext;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.SearchContextFactory;
import com.org.panaroma.web.cache.CacheContext;
import com.org.panaroma.web.cache.CacheKeyManager;
import com.org.panaroma.web.cache.CacheWrapper;
import com.org.panaroma.web.cache.ClientConfigService;
import com.org.panaroma.web.cache.StringSerializedCacheWrapper;
import com.org.panaroma.web.client.oauth.IOauthClient;
import com.org.panaroma.web.client.oauth.IOauthClientNonMono;
import com.org.panaroma.web.constants.CacheConstants;
import com.org.panaroma.web.detailAPI.P2mDetailResponseBuilder;
import com.org.panaroma.web.detailAPI.P2pInwardDetailResponseBuilder;
import com.org.panaroma.web.detailAPI.P2pOutwardDetailResponseBuilder;
import com.org.panaroma.web.dto.CacheValueWrapperDto;
import com.org.panaroma.web.dto.CustomOauthUserDetailsResponse;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.dto.UpdateResponseDto;
import com.org.panaroma.web.dto.ValidationResult;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.web.dto.detailAPI.ExtraDetailRequestFields;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailResponseV2Mapper;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.ShareScreenEnricher;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.Timeline;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailApiResponseV3;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailRequestV3;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailResponseV3Mapper;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailVersionResponseMapper;
import com.org.panaroma.web.dto.detailAPI.detailV3.InstrumentDtoV3;
import com.org.panaroma.web.dto.detailAPI.timelineDto.TimelineErrorMsgMapperDto;
import com.org.panaroma.web.dto.detailAPI.timelineDto.TimelineErrorType;
import com.org.panaroma.web.dto.detailAPI.timelineDto.TimelineFieldsDto;
import com.org.panaroma.web.dto.detailAPI.timelineDto.UpiTimelineApiResponse;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.exceptionhandler.ExceptionHandlerUtil;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.monitoring.MetricsPublishHelper;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import com.org.panaroma.web.repo.TransactionHistoryRepoNonMono;
import com.org.panaroma.web.service.DefaultTransactionHistoryService;
import com.org.panaroma.web.service.DetailResponseV2Enricher;
import com.org.panaroma.web.service.UpiTimelineServiceNonMono;
import com.org.panaroma.web.service.WebUtilityServiceNonMono;
import com.org.panaroma.web.service.timeline.ITimelineService;
import com.org.panaroma.web.service.timeline.ITimelineServiceFactory;
import com.org.panaroma.web.utility.AmountSplitDetailHelper;
import com.org.panaroma.web.utility.AuditUtility;
import com.org.panaroma.web.utility.CacheUtility;
import com.org.panaroma.web.utility.CrossSellUtility;
import com.org.panaroma.web.utility.CtasNodeUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.FeatureRoleOutStrategyUtility;
import com.org.panaroma.web.utility.FooterLogoUtility;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.LocalisationUtility;
import com.org.panaroma.web.utility.ResponseBuilder;
import com.org.panaroma.web.utility.ResponseBuilderNonMono;
import com.org.panaroma.web.utility.TokenValidatorUtility;
import com.org.panaroma.web.utility.WebControllerUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.function.Consumer;

import com.org.panaroma.web.utility.configurablePropertyUtility.InternalCache;
import com.org.panaroma.web.utility.configurablePropertyUtility.LocalizationWrapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.org.panaroma.web.dto.detailAPI.detailV3.InstrumentDetailsDtoV3;
import static com.org.panaroma.web.monitoring.MonitoringConstants.IGNORING_LISTING_ES_GET_CALL_BASED_ON_NTU_DATA;

@Component
// @DependsOn("aerospike_cache_manager")
@Log4j2
public class DefaultTransactionHistoryServiceNonMono implements TransactionHistoryServiceNonMono {

	private final TransactionHistoryRepoNonMono repo;

	private final TransactionHistoryRepoNonMono esBlockingRepo;

	private final IOauthClient oauthClient;

	private final IOauthClientNonMono oauthClientNonMono;

	private final boolean knownIssueAtBackend;

	private final MetricsAgent metricsAgent;

	private final boolean showBankDataProp;

	private final List<String> whiteListedBankUserId;

	private final List<String> whiteListedLocalisationUserId;

	private final List<String> salaryReportCodes;

	private final boolean fetchUserImageFromCache;

	private final boolean pushMissingUserImageListToKafka;

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	private final ITimelineServiceFactory iTimelineServiceFactory;

	private final DetailResponseV2Enricher detailResponseV2Enricher;

	private final boolean enableUnmaskAccountNumber;

	private final ClientConfigService clientConfigService;

	private final SearchContextFactory searchContextFactory;

	private final CacheUtility cacheUtility;

	private final CacheWrapper<String, CacheValueWrapperDto<List<TransformedTransactionHistoryDetail>>> detailsCacheWrapper;

	private final FeatureRoleOutStrategyUtility featureRoleOutStrategyUtility;

	private ObjectMapper objectMapper;

	private final IRolloutStrategyHelper iRolloutStrategyHelper;

	private final ResponseBuilder responseBuilder;

	private final RuleEngineService ruleEngineService;

	private final WebUtilityServiceNonMono webUtilityService;

	private final UpiTimelineServiceNonMono upiTimelineServiceNonMono;

	private final AuditUtility auditUtility;

	private final DefaultTransactionHistoryService defaultTransactionHistoryService;

	private final P2mDetailResponseBuilder p2mDetailResponseBuilder;

	private final P2pInwardDetailResponseBuilder p2pInwardDetailResponseBuilder;

	private final P2pOutwardDetailResponseBuilder p2pOutwardDetailResponseBuilder;

	private final AmountSplitDetailHelper amountSplitDetailHelper;

	private final LocalizationWrapper localizationWrapper;

	@Autowired
	private InternalCache internalCache;

	@Autowired
	private MetricsPublishHelper metricsPublishHelper;

	@Autowired
	public DefaultTransactionHistoryServiceNonMono(

			@Qualifier("EsProgressiveSearchingHistoryRepoNonMono") final TransactionHistoryRepoNonMono repo,
			@Qualifier("EsBlockingTransactionHistoryRepoNonMono") final TransactionHistoryRepoNonMono esBlockingRepo,
			final IOauthClient oauthClient, final MetricsAgent metricsAgent,
			@Value("${show-bank-data}") final boolean showBankDataProp,
			@Value("${known-issue-at-backend}") final boolean knownIssueAtBackend,
			@Value("${whitelisted-bank-users}") final List<String> whiteListedBankUserId,
			@Value("${whitelisted-localisation-users}") final List<String> whiteListedLocalisationUserId,
			@Value("${salary.reportCodes}") final List<String> salaryReportCodes,
			@Value("${fetch-user-image-from-cache}") final boolean fetchUserImageFromCache,
			@Value("${push-missing-user-image-list-to-kafka}") final boolean pushMissingUserImageListToKafka,
			final ConfigurablePropertiesHolder configurablePropertiesHolder,
			@Value("${enable.unmask.accNumber}") final boolean enableUnmaskAccountNumber,
			final DetailResponseV2Enricher detailResponseV2Enricher,
			final ITimelineServiceFactory iTimelineServiceFactory, final ClientConfigService clientConfigService,
			final SearchContextFactory searchContextFactory, final CacheUtility cacheUtility,
			@Qualifier("aerospike_cache_manager") final CacheManager cacheManager,
			final FeatureRoleOutStrategyUtility featureRoleOutStrategyUtility,
			final IRolloutStrategyHelper iRolloutStrategyHelper, final ResponseBuilder responseBuilder,
			final RuleEngineService ruleEngineService, final WebUtilityServiceNonMono webUtilityService,
			final UpiTimelineServiceNonMono upiTimelineServiceNonMono, final AuditUtility auditUtility,
			final DefaultTransactionHistoryService defaultTransactionHistoryService,
			final IOauthClientNonMono oauthClientNonMono,
			final P2pOutwardDetailResponseBuilder p2pOutwardDetailResponseBuilder,
			final P2pInwardDetailResponseBuilder p2pInwardDetailResponseBuilder,
			final P2mDetailResponseBuilder p2mDetailResponseBuilder,
			final AmountSplitDetailHelper amountSplitDetailHelper, final LocalizationWrapper localizationWrapper) {
		this.repo = repo;
		this.esBlockingRepo = esBlockingRepo;
		this.oauthClient = oauthClient;
		this.metricsAgent = metricsAgent;
		this.knownIssueAtBackend = knownIssueAtBackend;
		this.showBankDataProp = showBankDataProp;
		this.whiteListedBankUserId = whiteListedBankUserId;
		this.whiteListedLocalisationUserId = whiteListedLocalisationUserId;
		this.salaryReportCodes = salaryReportCodes;
		this.fetchUserImageFromCache = fetchUserImageFromCache;
		this.pushMissingUserImageListToKafka = pushMissingUserImageListToKafka;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.detailResponseV2Enricher = detailResponseV2Enricher;
		this.iTimelineServiceFactory = iTimelineServiceFactory;
		this.enableUnmaskAccountNumber = enableUnmaskAccountNumber;
		this.clientConfigService = clientConfigService;
		this.searchContextFactory = searchContextFactory;
		this.cacheUtility = cacheUtility;
		this.responseBuilder = responseBuilder;
		this.ruleEngineService = ruleEngineService;
		this.defaultTransactionHistoryService = defaultTransactionHistoryService;
		this.detailsCacheWrapper = new StringSerializedCacheWrapper<>(cacheManager, DETAIL_API_CACHE.value,
				metricsAgent, new TypeReference<>() {
				});
		this.featureRoleOutStrategyUtility = featureRoleOutStrategyUtility;
		this.iRolloutStrategyHelper = iRolloutStrategyHelper;
		this.webUtilityService = webUtilityService;
		this.upiTimelineServiceNonMono = upiTimelineServiceNonMono;
		this.auditUtility = auditUtility;
		this.oauthClientNonMono = oauthClientNonMono;
		this.p2pOutwardDetailResponseBuilder = p2pOutwardDetailResponseBuilder;
		this.p2pInwardDetailResponseBuilder = p2pInwardDetailResponseBuilder;
		this.p2mDetailResponseBuilder = p2mDetailResponseBuilder;
		this.amountSplitDetailHelper = amountSplitDetailHelper;
		this.localizationWrapper = localizationWrapper;
	}

	private Optional<Throwable> checkKnownIssue() {
		if (knownIssueAtBackend) {
			try {
				ExceptionHandlerUtil.sendUnRetryAbleError();
			}
			catch (Exception ex) {
				return Optional.of(ex);
			}
		}
		return Optional.empty();
	}

	private void ensureObjMapperInitialized() {
		if (objectMapper != null) {
			return;
		}
		synchronized (DefaultTransactionHistoryService.class) {
			if (objectMapper == null) {
				objectMapper = new ObjectMapper();
			}
		}
	}

	@Override
	public ResponseDto search(final Map<String, String> paramMap, final Map<String, String> tokens) throws Exception {
		try {
			return searchInternal(paramMap, tokens);
		}
		catch (Exception ex) {
			try {
				defaultTransactionHistoryService.processParamMapForExceptionInSearch(paramMap);
			}
			catch (Exception e) {
				log.error("error while processing paramMap for exception with requestId : {}",
						ThreadContext.get("requestId"));
			}
			Exception e = defaultTransactionHistoryService.customizeExceptionAsPerFilter(ex, paramMap);
			throw e;
		}
	}

	@Override
	public UpdateResponseDto getUpdates(final Map<String, String> paramMap, final Map<String, String> tokens)
			throws Exception {
		try {
			return searchInternalForUpdates(paramMap, tokens);
		}
		catch (Exception ex) {
			try {
				defaultTransactionHistoryService.processParamMapForExceptionInSearch(paramMap);
			}
			catch (Exception e) {
				log.error("error while processing paramMap for exception with requestId : {}",
						ThreadContext.get("requestId"));
			}
			Exception e = defaultTransactionHistoryService.customizeExceptionAsPerFilter(ex, paramMap);
			throw e;
		}
	}

	private ResponseDto searchInternal(final Map<String, String> paramMap, final Map<String, String> tokens)
			throws Exception {

		Optional<Throwable> optionalThrowable = checkKnownIssue();

		if (optionalThrowable.isPresent()) {
			Throwable throwable = optionalThrowable.get();
			throw (PanaromaException) throwable;
		}
		// since paramMap float most of the code part so placing this threadId as unique
		// identifier
		// in paramMap to be used for logging
		String id = ThreadContext.get("requestId");
		paramMap.put(REQUEST_ID, id);

		AbstractSearchContext isearchContext = searchContextFactory.getSearchContext(paramMap.get(CLIENT));
		SearchContext searchContext = isearchContext.setSearchContext(paramMap, null);
		if (Objects.isNull(searchContext)) {
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		long startTime = System.currentTimeMillis();
		try {
			ResponseDto responseDto = this.getListingData(searchContext, tokens, paramMap);
			long timeTaken = System.currentTimeMillis() - startTime;
			if (StringUtils.isNotBlank(paramMap.get(LISTING_REQUEST_SERVED_FROM_CACHE))) {
				metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken, API_NAME + COLON + SEARCH_API,
						IS_SERVED_FROM_CACHE + COLON + TRUE);
			}
			else if (StringUtils.isNotBlank(paramMap.get(LISTING_REQUEST_APPLICABLE_TO_BE_SERVED_USING_CACHE))) {
				metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken, API_NAME + COLON + SEARCH_API,
						IS_SERVED_FROM_CACHE + COLON + FALSE);
			}
			// this.pushMetricTxnCountAndDateDiff(responseDto, paramMap, searchContext);
			return responseDto;
		}
		catch (Exception ex) {
			long timeTaken = System.currentTimeMillis() - startTime;
			if (StringUtils.isNotBlank(paramMap.get(NO_TXN_PRESENT_IDENTIFIED_USING_CACHE))) {
				metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken,
						API_NAME + COLON + NO_TRANSACTION_FOUND_FOR_LISTING, IS_SERVED_FROM_CACHE + COLON + TRUE);
			}
			else if (ex instanceof PanaromaException
					&& NO_TRANSACTION_FOUND_FOR_LISTING.equals(((PanaromaException) ex).getResponseCode())
					&& StringUtils.isNotBlank(paramMap.get(LISTING_REQUEST_APPLICABLE_TO_CHECK_NO_TXN_FOUND))) {
				metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken,
						API_NAME + COLON + NO_TRANSACTION_FOUND_FOR_LISTING, IS_SERVED_FROM_CACHE + COLON + FALSE);
			}
			throw ex;
		}
	}

	private UpdateResponseDto searchInternalForUpdates(final Map<String, String> paramMap,
			final Map<String, String> tokens) throws IOException, InterruptedException {

		Optional<Throwable> optionalThrowable = checkKnownIssue();

		if (optionalThrowable.isPresent()) {
			Throwable throwable = optionalThrowable.get();
			throw (PanaromaException) throwable;
		}
		// since paramMap float most of the code part so placing this threadId as unique
		// identifier
		// in paramMap to be used for logging
		String id = ThreadContext.get("requestId");
		paramMap.put(REQUEST_ID, id);
		paramMap.put(FOR_UPDATES, TRUE);

		AbstractSearchContext iSearchContext = searchContextFactory.getSearchContext(paramMap.get(CLIENT));
		SearchContext searchContext = iSearchContext.setSearchContext(paramMap, null);

		if (defaultTransactionHistoryService.invalidForUpdates(searchContext, paramMap)) {
			log.warn("Setting updates as invalid. Returning search result.");
			searchContext.setInvalidForUpdates(true);
			searchContext.setForUpdates(false);
			searchContext.setFromUpdatedDate(null);
			searchContext.setPaginationTxnId(null);
			searchContext.setPaginationStreamSource(null);
			searchContext.setTransactionDateEpoch(null);
			searchContext.setPageNo(1);
			metricsAgent.incrementCount(UPDATES_API_INVALIDATION,
					String.format(monitoringTagFormat, CRITERIA, "fromUpdatedDateCheck"),
					String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)),
					String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)));
		}
		else {
			searchContext.setForUpdates(true);
			searchContext.setShowInListing(null);
			searchContext.setSearchPageSize(searchContext.getPageSize());
			searchContext.setPageSize(configurablePropertiesHolder.getProperty(UPDATES_API_PAGE_SIZE, Integer.class));
			// Decreasing fromUpdatedDate by 15 seconds to account for delayed updates in
			// ES
			searchContext.setPaginationStreamSource(null);
			searchContext.setPageNo(1);
			searchContext.setPaginationTxnId(null);
			searchContext.setFromDate(searchContext.getFromUpdatedDate()
					- configurablePropertiesHolder.getProperty(UPDATES_API_FROM_DATE_GAP, Long.class));
			searchContext.setTransactionDateEpoch(null);

		}
		try {
			long startTime = System.currentTimeMillis();
			log.info("Updated search context : {}", searchContext);
			UpdateResponseDto updateResponseDto = this.getListingUpdatesData(searchContext, tokens, paramMap);
			long timeTaken = System.currentTimeMillis() - startTime;
			metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken, API_NAME + COLON + UPDATES_API);
			// As response logs are getting printed using Logbook. search "Logbook" &&
			// "response" to check these logs.
			// log.info("Update Response created : {}", updateResponseDto);
			return updateResponseDto;
		}
		catch (Exception ex) {
			throw ex;
		}
	}

	private ResponseDto getListingData(final SearchContext searchContext, final Map<String, String> tokens,
			final Map<String, String> paramMap) throws Exception {
		String id = ThreadContext.get("requestId");
		String client = paramMap.get(CLIENT);
		String langId = LocalisationUtility.validateAndSetLanguageId(paramMap.get(LOCALE), client);

		if (clientConfigService.isS2sClient(client)) {
			try {
				ThreadContext.put("requestId", id);
				return this.getTxnResponse(searchContext, langId, paramMap, null);
			}
			catch (Exception ex) {
				log.error("Error creating listing response. Exception: {}, with reqId : {}",
						CommonsUtility.exceptionFormatter((Exception) ex), id);
				throw defaultTransactionHistoryService.customizeExceptionAsPerFilter(ex, searchContext);
			}
		}

		try {
			ValidationResult validationResult = webUtilityService.validateAndGetUserId(tokens, id, paramMap);
			ThreadContext.put("requestId", id);
			if (searchContext.getEntityId() != null
					&& !validationResult.getUserId().equalsIgnoreCase(searchContext.getEntityId())) {
				log.info(
						"entity Id passed is different from user id fetched from token. Setting entityId as userId from token");
			}
			searchContext.setEntityId(validationResult.getUserId());

			// Adding customer creation date in thread context
			if (validationResult.getCustomerCreationDate() != null) {
				Long customerCreationDate = getCustomerCreationDateFormattedDate(
						validationResult.getCustomerCreationDate());
				ThreadContext.put(CUSTOMER_CREATION_DATE, String.valueOf(customerCreationDate));
			}

			/**
			 * in case if we use v2/user api in that case we will make use of
			 * customerCreationDate to shrink search range so will update from date
			 * accordingly
			 */
			if (Boolean.TRUE.equals(configurablePropertiesHolder.getProperty(
					ConfigurationPropertiesConstants.WebApiV1Constants.USE_OF_CUSTOMER_CREATION_DATE_ENABLED,
					Boolean.class))) {
				GenericUtility.updateFromDateAndAddMetrics(searchContext, validationResult, paramMap);
			}
			paramMap.put(ENTITY_ID, searchContext.getEntityId());
			boolean isBankDataFilter = getBankDataFilter(searchContext.isShowBankData(), validationResult);
			// showing bank data.
			searchContext.setShowBankData(isBankDataFilter);

			// Cache Context object contains all the relevant info for performing cache
			// related tasks
			CacheContext cacheContext = cacheUtility.getCacheContext(searchContext, paramMap);

			if (Objects.isNull(cacheContext) || !cacheContext.isRequestValidToThrow4010Error()) {
				return this.getTxnResponse(searchContext, langId, paramMap, cacheContext);
			}
			else {
				// Adding this for metrics purpose.
				paramMap.put(NO_TXN_PRESENT_IDENTIFIED_USING_CACHE, TRUE);
				log.error("NTU cache : No transaction fetched for userId : {} from : {} to today",
						searchContext.getEntityId(), DateTimeUtility.getDateTime(searchContext.getFromDate()));
				metricsAgent.incrementCount(IGNORING_LISTING_ES_GET_CALL_BASED_ON_NTU_DATA);
				throw ExceptionFactory.getCustomizedFromDateException(PANAROMA_SERVICE,
						NO_TRANSACTION_FOUND_FOR_LISTING, DateTimeUtility.getDateTimeForExp(
								configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class)));
			}
		}
		catch (Exception ex) {
			log.error("Error creating listing response. Exception: {}",
					ExceptionHandlerUtil.printPthServiceException(ex));
			throw defaultTransactionHistoryService.customizeExceptionAsPerFilter(ex, searchContext);
		}
	}

	private UpdateResponseDto getListingUpdatesData(final SearchContext searchContext, final Map<String, String> tokens,
			final Map<String, String> paramMap) throws IOException, InterruptedException {
		String id = ThreadContext.get("requestId");
		String client = paramMap.get(CLIENT);
		String langId = LocalisationUtility.validateAndSetLanguageId(paramMap.get(LOCALE), client);

		if (clientConfigService.isS2sClient(client)) {
			try {
				ThreadContext.put("requestId", id);
				return this.getTxnUpdateResponse(searchContext, langId, paramMap, CacheState.NORMAL);
			}
			catch (Exception ex) {
				log.error("Error creating listing response. Exception: {}, with reqId : {}",
						CommonsUtility.exceptionFormatter((Exception) ex), id);
				defaultTransactionHistoryService.customizeExceptionAsPerFilter(ex, searchContext);
			}
		}

		try {
			ValidationResult validationResult = this.validateAndGetUserId(tokens, id, searchContext.isShowBankData());
			ThreadContext.put("requestId", id);
			if (searchContext.getEntityId() != null
					&& !validationResult.getUserId().equalsIgnoreCase(searchContext.getEntityId())) {
				log.info(
						"entity Id passed is different from user id fetched from token. Setting entityId as userId from token");
			}
			searchContext.setEntityId(validationResult.getUserId());
			boolean isBankDataFilter = getBankDataFilter(searchContext.isShowBankData(), validationResult);
			// showing bank data.
			searchContext.setShowBankData(isBankDataFilter);
			CacheState cacheState = CacheState.NORMAL;
			try {
				if (searchContext.isForUpdates() && !searchContext.isInvalidForUpdates() && iRolloutStrategyHelper
					.isUserWhiteListed("appCacheOptimisation", searchContext.getEntityId())) {
					AppSideCacheData cacheData = cacheUtility.getAppSideCacheData(searchContext,
							CacheInfo.UTH_APP_SIDE_CACHE_DATA);
					cacheState = getCacheState(cacheData);
					if (cacheState == CacheState.ZERO_DELTA && cacheData.getZeroDeltaCacheData()
						.getFromUpdatedDate() <= searchContext.getFromUpdatedDate()) {
						log.info("Returning empty response based on Zero Delta cache for user : {}",
								searchContext.getEntityId());
						metricsAgent.incrementCount(UPDATES_API_NO_TXNS,
								String.format(monitoringTagFormat, SERVED_FROM_ZERO_DELTA_CACHE, true));
						return emptyUpdatesApiResponse(searchContext, langId, paramMap);
					}
					else if (cacheState == CacheState.OLD_TXN_UPDATES) {
						defaultTransactionHistoryService.modifySearchContextForOldTxns(searchContext, cacheData);
					}
				}
				long fromDate = searchContext.getFromDate();
				int timeGapInDays = (int) Math.ceil((System.currentTimeMillis() - fromDate) / (ONE_DAY_IN_MILLIS));
				metricsAgent.recordTimeGap(UPDATES_API_FROM_DATE_GAP_METRICS, timeGapInDays,
						String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)),
						String.format(monitoringTagFormat, "validForUpdates", searchContext.isForUpdates()),
						String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)));
			}
			catch (Exception e) {
				log.error("Cache couldn't be used due to exception. Serving via normal ES search.", e);
			}
			return this.getTxnUpdateResponse(searchContext, langId, paramMap, cacheState);
		}
		catch (Exception ex) {
			log.error("Error creating listing response. Exception: {}",
					ExceptionHandlerUtil.printPthServiceException(ex));
			defaultTransactionHistoryService.customizeExceptionAsPerFilter(ex, searchContext);
			throw ex;
		}
	}

	private ResponseDto getTxnResponse(final SearchContext searchContext, final String langId,
			final Map<String, String> paramMap, final CacheContext cacheContext) {
		RepoResponseSearchApiDto repoResponseMono;

		// Ignoring Progressive Search if Date Range Filter is applied or filterSource is
		// spendPage on request
		if (SPEND_PAGE.equalsIgnoreCase(paramMap.get(FILTER_SOURCE))
				|| (StringUtils.isNotBlank(searchContext.getEsDateRange())
						&& searchContext.getEsDateRange().contains(","))) {
			// the above esDateRange checks means go to blocking when multiple data ranges
			// used
			repoResponseMono = esBlockingRepo.search(searchContext, paramMap);
		}
		else {
			repoResponseMono = repo.search(searchContext, paramMap, cacheContext);
		}
		try {
			ResponseDto responseDto = ResponseBuilderNonMono.buildTxnListingResponseDto(repoResponseMono, searchContext,
					langId, salaryReportCodes, paramMap, pushMissingUserImageListToKafka, fetchUserImageFromCache);

			if (LocalisationUtility.needLocalisation(langId) && (whiteListedLocalisationUserId.contains("-1")
					|| whiteListedLocalisationUserId.contains(searchContext.getEntityId()))) {
				return LocalisationUtility.localiseListingDetails(responseDto, langId, searchContext.getEntityId());
			}
			return responseDto;
		}
		catch (Exception ex) {
			log.error("error while receiving response from repo");
			throw ex;
		}
	}

	@Override
	public DetailApiResponse getDetails(final TransactionSource transactionSource, final String passedIds,
			final Map<String, String> tokenMap, final boolean showBankData, final String client, final String locale,
			final String userId) throws PanaromaException, IOException, InterruptedException {
		try {
			// pair's key contains txnId and value contains groupId (if present) in the
			// below pair
			String[] idPair = WebControllerUtility.getIdToQueryEs(passedIds);
			String txnId = idPair[0];
			String txnDate = idPair[1];
			// GroupId will be null if it's not there in txnId
			String groupId = idPair[3];

			DetailInputParams detailInputParams = new DetailInputParams();
			detailInputParams.setClient(client);
			ExtraDetailRequestFields extraDetailRequestFields = new ExtraDetailRequestFields();
			extraDetailRequestFields.setTxnDate(txnDate);
			return getDetailsInternal(transactionSource, txnId, tokenMap, showBankData, detailInputParams, locale,
					userId, groupId, extraDetailRequestFields);
		}
		catch (Exception ex) {
			throw ex;
		}
	}

	private DetailApiResponse getDetailsInternal(final TransactionSource transactionSource, final String txnId,
			final Map<String, String> tokenMap, final boolean showBankData, final DetailInputParams detailInputParams,
			final String locale, final String userId, final String groupId,
			final ExtraDetailRequestFields extraDetailRequestFields)
			throws PanaromaException, IOException, InterruptedException {

		Optional<Throwable> optionalThrowable = checkKnownIssue();

		if (optionalThrowable.isPresent()) {
			Throwable throwable = optionalThrowable.get();
			throw (PanaromaException) throwable;
		}

		if (Objects.isNull(extraDetailRequestFields) || StringUtils.isBlank(extraDetailRequestFields.getTxnDate())) {
			String openSource = Objects.nonNull(extraDetailRequestFields)
					&& StringUtils.isNotBlank(extraDetailRequestFields.getOpenSource())
							? extraDetailRequestFields.getOpenSource() : NOT_AVAILABLE;
			Boolean throwErrorIfTxnDateAbsent = configurablePropertiesHolder
				.getProperty(TXN_DATE_MANDATORY_FOR_DETAIL_API, Boolean.class);
			log.error(
					"txnDate absent for Detail API request for txnId : {}, openSource : {}. Error thrown on the basis of value"
							+ "of txn-date-mandatory-for-detail-api flag : {}",
					txnId, openSource, throwErrorIfTxnDateAbsent);
			metricsAgent.incrementCount(TXN_DATE_ABSENT_IN_DETAIL_REQUEST, OPEN_SOURCE + COLON + openSource,
					ERROR_THROWN + COLON + throwErrorIfTxnDateAbsent);
			// Adding this check to not hit payment_history_alias for details api in any
			// case whenever txn-date-mandatory-for-detail-api flag is true
			if (throwErrorIfTxnDateAbsent) {
				throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
			}
		}
		if (Objects.nonNull(extraDetailRequestFields) && StringUtils.isNotBlank(extraDetailRequestFields.getTxnDate())
				&& StringUtils.isNotBlank(extraDetailRequestFields.getOpenSource())) {
			GenericUtilityExtension.isReqNeedToBeServed(transactionSource.getTransactionSource(),
					extraDetailRequestFields, detailInputParams);
		}

		String requestId = ThreadContext.get("requestId");
		String client = detailInputParams.getClient();
		String langId = LocalisationUtility.validateAndSetLanguageId(locale, client);
		if (clientConfigService.isS2sClient(client)) {
			return this.getDetailResponse(userId, txnId, transactionSource,
					clientConfigService.getClientConfigMap().get(client).isShowBankData(), detailInputParams, langId,
					groupId, extraDetailRequestFields, locale);
		}
		ValidationResult validationResult = StringUtils.isNotBlank(userId)
				? webUtilityService.validateAndGetUserId(tokenMap, requestId,
						MonitoringConstants.OAUTH_SERVICE_USER_API, userId)
				: webUtilityService.validateAndGetUserId(tokenMap, requestId,
						MonitoringConstants.OAUTH_SERVICE_USER_API);
		try {
			ThreadContext.put(WebConstants.REQUEST_ID, requestId);
			ThreadContext.put(WebConstants.USERID, validationResult.getUserId());
			boolean isBankDataFilter = getBankDataFilter(showBankData, validationResult);

			return this.getDetailResponse(validationResult.getUserId(), txnId, transactionSource, isBankDataFilter,
					detailInputParams, langId, groupId, extraDetailRequestFields, locale);
		}
		catch (PanaromaException ex) {
			throw ex;
		}
	}

	private boolean getBankDataFilter(final boolean showBankData, final ValidationResult validationResult) {
		boolean isBankDataFilter = showBankDataProp;
		if (showBankDataProp) {
			/*
			 * if (validationResult.isKycConsentPresent()) {
			 * log.info("Kyc consent is present, including bank data"); } else { log.
			 * info("Kyc consent is not present, so setting the showBankData as false and not including bankData"
			 * ); isBankDataFilter = false; }
			 */
		}
		else {
			// log.info("showBankData flag is false, so not including bank data");
			if (whiteListedBankUserId.size() > 0 && whiteListedBankUserId.contains(validationResult.getUserId())) {
				log.info("including bank data as user is whitelisted");
				isBankDataFilter = true;
			}
		}
		return isBankDataFilter;
	}

	private DetailApiResponse getDetailResponse(final String userId, final String txnId,
			final TransactionSource transactionSource, final boolean isBankData,
			final DetailInputParams detailInputParams, final String langId, final String groupId,
			final ExtraDetailRequestFields extraDetailRequestFields, final String locale) {
		// TODO : handling of showBankData is false
		List<TransformedTransactionHistoryDetail> detailedList = getCachedDetails(userId, txnId, transactionSource,
				isBankData, groupId, extraDetailRequestFields);
		String openSource = extraDetailRequestFields == null ? null : extraDetailRequestFields.getOpenSource();
		try {
			DetailApiResponse detailApiResponse = ResponseBuilderNonMono.buildDetailApiResponse(detailedList, userId,
					txnId, detailInputParams, langId, whiteListedLocalisationUserId, extraDetailRequestFields,
					iRolloutStrategyHelper, locale);
			log.debug("successfully created response for detail API {}. DetailV2 Source: {}", detailApiResponse,
					openSource);
			return detailApiResponse;
		}
		catch (Exception ex) {
			log.error("error while creating response for detail. DetailV2 Source: {}", openSource);
			throw ex;
		}
	}

	private List<TransformedTransactionHistoryDetail> getCachedDetails(final String userId, final String txnId,
			final TransactionSource transactionSource, final Boolean isBankData, final String groupId,
			final ExtraDetailRequestFields extraDetailRequestFields) {
		boolean isCacheEnabled = featureRoleOutStrategyUtility.checkFeatureEnabled(userId,
				DETAIL_PAGE_API_CACHING_ENABLED, DETAIL_PAGE_ROLE_OUT_FEATURE_NAME, Boolean.FALSE);
		if (isCacheEnabled) {
			String detailCacheKey = CacheKeyManager.getDetailPageCacheKey(userId, txnId, transactionSource, isBankData);
			Optional<CacheValueWrapperDto<List<TransformedTransactionHistoryDetail>>> cachedDetailData = detailsCacheWrapper
				.get(detailCacheKey);
			if (cachedDetailData.isPresent()) {
				CacheValueWrapperDto<List<TransformedTransactionHistoryDetail>> cacheValueWrapperDto = cachedDetailData
					.get();
				List<TransformedTransactionHistoryDetail> tthdList = cacheValueWrapperDto.getValue();
				pushDetailCacheMonitoringMetrics(txnId, tthdList, Boolean.TRUE, cacheValueWrapperDto.getCreatedDate());
				log.info("Inside getCachedDetails() records present in cache key: {}", detailCacheKey);
				return tthdList;
			}
		}

		///// need to check this
		List<TransformedTransactionHistoryDetail> detailedList = repo.getDetails(userId, txnId,
				transactionSource.getTransactionSourceKey(), isBankData, groupId, extraDetailRequestFields);
		if (detailedList != null) {
			detailedList.forEach(auditUtility::pushDataToKafkaForAudit);
			if (isCacheEnabled) {
				pushDetailCacheMonitoringMetrics(txnId, detailedList, Boolean.FALSE, null);
				String detailCacheKey = CacheKeyManager.getDetailPageCacheKey(userId, txnId, transactionSource,
						isBankData);
				log.info("Inside getCachedDetails() put record in cache key: {}", detailCacheKey);
				CacheValueWrapperDto<List<TransformedTransactionHistoryDetail>> cacheValueWrapperDto = new CacheValueWrapperDto<>(
						detailedList, System.currentTimeMillis());
				Optional<Duration> ttlOptional = getDynamicDetailCacheTtl(detailedList, txnId);
				if (ttlOptional.isPresent()) {
					detailsCacheWrapper.put(detailCacheKey, cacheValueWrapperDto, ttlOptional.get());
				}
				else {
					detailsCacheWrapper.put(detailCacheKey, cacheValueWrapperDto);
				}
			}
			setCreditParticipantEntityIdForFastagTxn(detailedList);
		}
		return detailedList;
	}

	// PTH-712: This method is used to set the entityId as null for the credit participant
	// in case of Fastag txn
	private void setCreditParticipantEntityIdForFastagTxn(
			final List<TransformedTransactionHistoryDetail> detailedList) {
		if (Objects.nonNull(detailedList)) {
			detailedList.forEach(txn -> {
				if (Objects.nonNull(txn) && Objects.nonNull(txn.getParticipants())) {
					txn.getParticipants().forEach(participant -> {
						if (Objects.nonNull(participant)
								&& TransactionIndicator.CREDIT.getTransactionIndicatorKey()
									.equals(participant.getTxnIndicator())
								&& Objects.nonNull(txn.getContextMap())
								&& FASTAG_TOPUP.equals(txn.getContextMap().get(TXNPURPOSE))) {
							participant.setEntityId(null);
						}
					});
				}
			});
		}
	}

	private Optional<Duration> getDynamicDetailCacheTtl(final List<TransformedTransactionHistoryDetail> data,
			final String txnId) {
		for (TransformedTransactionHistoryDetail tthd : data) {
			if (tthd.getTxnId().equalsIgnoreCase(txnId)) {
				Duration duration = Duration.ofMillis(System.currentTimeMillis() - tthd.getTxnDate());
				log.info("inside getDynamicDetailCacheTtl() txnId:- {} duration:- {}", txnId, duration);
				if (ClientStatusEnum.PENDING.getStatusKey().equals(tthd.getStatus())) {
					Long boPanelCachePending = configurablePropertiesHolder
						.getPropertyWithDefaultValue(DETAILS_API_PENDING_STATUS_TTL_CACHE, Long.class, null);
					if (boPanelCachePending != null) {
						return Optional.of(Duration.ofSeconds(boPanelCachePending));
					}
					return Optional.empty();
				}
				else {
					Map<String, String> boPanelCacheMap = configurablePropertiesHolder.getPropertyWithDefaultValue(
							DETAILS_API_TTL_NON_PENDING_DAYS_WISE_CACHE, Map.class, Collections.emptyMap());
					String boPanelCacheMapKey = boPanelCacheMap.keySet()
						.stream()
						.sorted(Comparator.reverseOrder())
						.filter(key -> duration.getSeconds() >= Duration.ofDays(Long.parseLong(key)).getSeconds())
						.findFirst()
						.orElse(null);
					if (boPanelCacheMapKey != null) {
						long timeInSeconds = Long.parseLong(boPanelCacheMap.get(boPanelCacheMapKey));
						return Optional.of(Duration.ofSeconds(timeInSeconds));
					}
					if (duration.getSeconds() >= CacheConstants.CacheDuration.TXN_GAP_DAYS_3.getTxnGap().getSeconds()) {
						return Optional.of(CacheConstants.CacheDuration.TXN_GAP_DAYS_3.getTtl());
					}
					else if (duration.getSeconds() >= CacheConstants.CacheDuration.TXN_GAP_DAYS_1.getTxnGap()
						.getSeconds()) {
						return Optional.of(CacheConstants.CacheDuration.TXN_GAP_DAYS_1.getTtl());
					}
					else {
						return Optional.of(CacheConstants.CacheDuration.TXN_GAP_DAYS_0.getTtl());
					}
				}
			}
		}
		return Optional.empty();
	}

	private void pushDetailCacheMonitoringMetrics(final String txnId,
			final List<TransformedTransactionHistoryDetail> detailList, final boolean isCached,
			final Long cacheCreatedDate) {
		detailList.forEach(tthd -> {
			if (txnId.equalsIgnoreCase(tthd.getTxnId())) {
				metricsAgent.recordTimeDiffBetweenDates(TXN_DATE_DIFFERENCE,
						System.currentTimeMillis() - tthd.getTxnDate(), "api:" + DETAIL_API,
						STATUS + COLON + ClientStatusEnum.getStatusEnumByKey(tthd.getStatus()).getStatusValue(),
						"CACHE_STATUS" + COLON + isCached);

				if (Objects.nonNull(cacheCreatedDate)) {
					metricsAgent.recordTimeDiffBetweenDates(CACHE_DATE_DIFFERENCE,
							System.currentTimeMillis() - cacheCreatedDate, "api:" + DETAIL_API,
							STATUS + COLON + ClientStatusEnum.getStatusEnumByKey(tthd.getStatus()).getStatusValue(),
							"CACHE_STATUS" + COLON + isCached);
				}
			}
		});
	}

	@Override
	public DetailApiResponseV2 getDetailsV2(final TransactionSource transactionSource, final String passedIds,
			final Map<String, String> tokenMap, final boolean showBankData, final ClientStatusEnum status,
			final String client, final String locale, final String userId, final boolean showOnlyTimeline,
			final ExtraDetailRequestFields extraDetailRequestFields) throws PanaromaException {

		try {
			String appVersion = null;
			String openSource = null;
			if (extraDetailRequestFields != null) {
				appVersion = extraDetailRequestFields.getAppVersion();
				openSource = extraDetailRequestFields.getOpenSource();
			}
			TimelineFieldsDto timelineFieldsDto = new TimelineFieldsDto(appVersion, client);
			// pair's key contains txnId and value contains groupId (if present) in the
			// below pair
			String[] idPair = WebControllerUtility.getIdToQueryEs(passedIds);
			String txnId = idPair[0];
			String txnDate = idPair[1];
			// GroupId will be null if it's not there in txnId
			String groupId = idPair[3];

			extraDetailRequestFields.setTxnDate(txnDate);
			if (showOnlyTimeline) {
				return getOnlyTimelineResponse(transactionSource, tokenMap, status, showOnlyTimeline, openSource,
						timelineFieldsDto, txnId);
			}
			else {
				DetailInputParams detailInputParams = getDetailInputParams(client, DETAIL_V2);
				DetailApiResponseV2 detailApiResponseV2Mono = getDetailApiResponseV2(transactionSource, tokenMap,
						showBankData, locale, userId, detailInputParams, extraDetailRequestFields, txnId, groupId,
						timelineFieldsDto);
				// return detailApiResponseV2Mono.map(detailApiResponseV2 -> {
				// apply whitelisting on %on response.
				localiseDetailResponse(client, locale, userId, txnId, detailApiResponseV2Mono);
				log.info("DetailApiResponseV2 Mapped response from detailApiResponse for txnId: {} is {}", txnId,
						detailApiResponseV2Mono);
				return detailApiResponseV2Mono;
			}
		}
		catch (Throwable ex) {
			// need to handle exception
			log.error("DetailApiResponseV2 error {}", ex);
			throw (PanaromaException) ex;
		}
	}

	private DetailInputParams getDetailInputParams(final String client, final String apiVersion) {
		DetailInputParams detailInputParams = new DetailInputParams();
		detailInputParams.setClient(client);
		detailInputParams.setIsAccountNumberUnmaskingEnabled(enableUnmaskAccountNumber);
		detailInputParams.setDetailCallVersion(apiVersion);
		return detailInputParams;
	}

	private void localiseDetailResponse(final String client, final String locale, final String userId,
			final String txnId, final Object detailApiResponse) {
		String langId = LocalisationUtility.validateAndSetLanguageId(locale, client);
		if (LocalisationUtility.needLocalisation(langId)
				&& (whiteListedLocalisationUserId.contains("-1") || whiteListedLocalisationUserId.contains(userId))) {
			LocalisationUtility.localiseDetailApiResponse(detailApiResponse, langId, txnId);
		}
	}

	@Override
	public DetailApiResponseV3 getDetailsV3(final DetailRequestV3 detailRequestV3, final Map<String, String> tokenMap)
			throws PanaromaException {

		try {
			TimelineFieldsDto timelineFieldsDto = new TimelineFieldsDto(detailRequestV3.getAppVersion(),
					detailRequestV3.getClient());

			if (detailRequestV3.isShowOnlyTimeline()) {
				DetailApiResponseV2 detailApiResponseV2 = getOnlyTimelineResponse(
						detailRequestV3.getTransactionSource(), tokenMap, detailRequestV3.getStatus(),
						detailRequestV3.isShowOnlyTimeline(), detailRequestV3.getOpenSource(), timelineFieldsDto,
						detailRequestV3.getTxnIdToQueryEs());
				return mapDetailResponseV3UsingDetailResponseV2(detailApiResponseV2);
			}
			DetailInputParams detailInputParams = getDetailInputParams(detailRequestV3.getClient(), DETAIL_V3);

			/*
			 * For few detail api hits for eg. when openSource = uth_detail, we don't get
			 * txnDate value appended in txnId field but we get it in separate txnDate
			 * field in request body
			 */
			String txnDate = StringUtils.isNotBlank(detailRequestV3.getTxnDateToQueryEs())
					? detailRequestV3.getTxnDateToQueryEs() : detailRequestV3.getTxnDate();

			DetailApiResponseV2 detailApiResponseV2 = getDetailApiResponseV2(detailRequestV3.getTransactionSource(),
					tokenMap, detailRequestV3.isShowBankData(), detailRequestV3.getLocale(),
					detailRequestV3.getUserId(), detailInputParams,
					new ExtraDetailRequestFields(detailRequestV3.getAppVersion(), detailRequestV3.getOpenSource(),
							txnDate),
					detailRequestV3.getTxnIdToQueryEs(), detailRequestV3.getGroupIdToQueryEs(), timelineFieldsDto);

			DetailApiResponseV3 detailApiResponseV3 = DetailResponseV3Mapper.getDetailEnhancedResponseForV3(
					detailApiResponseV2, detailInputParams.getListingVisibleTxn(),
					detailInputParams.getIsAccountNumberUnmaskingEnabled(), detailRequestV3.getLocale());

			ruleEngineService.applyRules(detailInputParams.getListingVisibleTxn(), detailApiResponseV3);
			FooterLogoUtility.updatePoweredByFooterLogo(detailApiResponseV3, detailRequestV3.getTheme(),
					getFooterLogoTxnType(detailApiResponseV3, detailInputParams.getListingVisibleTxn()),
					detailInputParams.getListingVisibleTxn());

			// it will not be null as null handling is already there in
			// getPthVersionDouble method, if it is null then 1.0 will be returned.
			double requestPthVersion = PthVersionUtility.getRequestPthVersionDouble();

			DetailVersionResponseMapper.filterFieldsForDetailVersion(detailApiResponseV3, requestPthVersion,
					detailInputParams.getListingVisibleTxn());

			localiseDetailResponse(detailRequestV3.getClient(), detailRequestV3.getLocale(),
					detailRequestV3.getUserId(), detailRequestV3.getTxnIdToQueryEs(), detailApiResponseV3);

			postModificationInDetailResponse(detailApiResponseV3, detailInputParams);

			// Initially this was only handled for ADD_MONEY txns. But this is not for
			// that anymore because ADD_MONEY to Wallet txns are not shown anymore on
			// passbook
			// This is required in UPI txns now as per
			// https://jira.mypaytm.com/browse/PTH-786
			amountSplitDetailHelper.setAmountBreakUpInfoInDetailResponse(detailInputParams.getListingVisibleTxn(),
					detailApiResponseV3);

			detailApiResponseV3
				.setDetailNarration(localizationWrapper.getLocalizedValue(detailApiResponseV3.getDetailNarration()));

			CrossSellUtility.setAdDetails(detailApiResponseV3, detailInputParams.getListingVisibleTxn());

			// As response logs are getting printed using Logbook. search "Logbook" &&
			// "response" to check these logs.
			/*
			 * log.info( "DetailApiResponseV3 Mapped response for txnId: {} is {}",
			 * detailRequestV3.getTxnIdToQueryEs(), detailApiResponseV3);
			 */
			return detailApiResponseV3;
		}
		catch (Exception ex) {
			log.error("DetailApiResponseV3 error for txnId :{} error {}", detailRequestV3.getTxnId(), ex);
			throw (PanaromaException) ex;
		}
	}

	private void postModificationInDetailResponse(final DetailApiResponseV3 detailApiResponseV3,
			final DetailInputParams detailInputParams) {
		if (Objects.isNull(detailApiResponseV3)) {
			return;
		}

		// Modify InstrumentDetailsDtoV3 response in both first and second Instrument
		modifyInstrumentDetailsDtoV3(detailApiResponseV3);

		setRepeatPaymentUrl(detailApiResponseV3, detailInputParams);

		CtasNodeUtility.removeCtaFromDetailResponse(detailApiResponseV3);

		if (ObjectUtils.isNotEmpty(detailApiResponseV3.getFirstInstrument())) {
			for (InstrumentDtoV3 instrumentDtoV3 : detailApiResponseV3.getFirstInstrument()) {
				CtasNodeUtility.removeCtaFromInstrument(instrumentDtoV3);
			}
		}

		if (ObjectUtils.isNotEmpty(detailApiResponseV3.getSecondInstrument())) {
			for (InstrumentDtoV3 instrumentDtoV3 : detailApiResponseV3.getSecondInstrument()) {
				CtasNodeUtility.removeCtaFromInstrument(instrumentDtoV3);
			}
		}

	}

	// PTH-905
	// Validates if an instrument is valid by checking if it exists and has non-empty
	// instrument details.
	// @param instrumentDto The instrument to validate
	// @return true if the instrument is valid, false otherwise
	private boolean isValidInstrument(InstrumentDtoV3 instrumentDto) {
		return Objects.nonNull(instrumentDto) && !CollectionUtils.isEmpty(instrumentDto.getInstrumentDetailsMap());
	}

	// PTH-905
	// Sets the logo URL to null for all instrument details in the given instrument.
	// This is used when the feature is disabled or when PSP info is not available.
	private void setDetailApiLogoUrl(InstrumentDtoV3 instrument) {
		if (isValidInstrument(instrument)) {
			instrument.getInstrumentDetailsMap()
				.forEach(instrumentDetailsDtoV3 -> instrumentDetailsDtoV3.setLogoUrl(null));
		}
	}

	// PTH-905
	// Sets the logo order to null for all second instruments in the detail response.
	// This is used to ensure consistent behavior across all versions.
	private void setDetailApiLogoOrderForSecondInstrument(DetailApiResponseV3 detailApiResponseV3) {
		if (Objects.isNull(detailApiResponseV3.getSecondInstrument())) {
			return;
		}

		detailApiResponseV3.getSecondInstrument()
			.stream()
			.filter(this::isValidInstrument)
			.forEach(instrument -> instrument.getInstrumentDetailsMap().forEach(details -> details.setLogoOrder(null)));
	}

	// PTH-905
	// Processes the first instrument based on the display type (logo URL or display
	// text).
	// In both cases: Sets logo order to null
	// @param firstInstrument The first instrument to process
	// @param instrumentDisplayType The type of display modification to apply
	// (SET_LOGO_URL or SET_DISPLAY_TEXT)
	private void processFirstInstrument(InstrumentDtoV3 firstInstrument, String instrumentDisplayType) {
		if (!isValidInstrument(firstInstrument)) {
			return;
		}

		// Get PSP information from the first instrument's VPA
		String vpa = firstInstrument.getInstrumentDetailsMap().get(0).getValue();
		String pspHandle = Utility.getPspHandle(vpa);
		PspInfo pspInfo = internalCache.getPspInfo(pspHandle);

		for (InstrumentDetailsDtoV3 details : firstInstrument.getInstrumentDetailsMap()) {
			if (pspInfo != null) {
				if (SET_LOGO_URL.equals(instrumentDisplayType)) {
					// Set PSP logo URL for logo URL feature
					details.setLogoUrl(pspInfo.getPspLogo());
				}
				else {
					// Append PSP display text for display text feature
					String currentValue = details.getValue();
					if (StringUtils.isNotBlank(currentValue) && StringUtils.isNotBlank(pspInfo.getPspDisplayText())) {
						details.setValue(currentValue + " " + pspInfo.getPspDisplayText());
					}
				}
			}
			else {
				// If PSP info is not available, set logo URL to null
				details.setLogoUrl(null);
			}
			// Always set logo order to null for consistency
			details.setLogoOrder(null);
		}
	}

	// PTH-905 Modifies instrument details in the detail response based on feature flags.
	protected void modifyInstrumentDetailsDtoV3(DetailApiResponseV3 detailApiResponseV3) {
		if (Objects.isNull(detailApiResponseV3) || Objects.isNull(detailApiResponseV3.getFirstInstrument())
				|| Objects.isNull(detailApiResponseV3.getSecondInstrument())) {
			return;
		}

		try {
			InstrumentDtoV3 firstInstrument = detailApiResponseV3.getFirstInstrument().get(0);

			if (PthVersionUtility.isTxnDetailsOtherPartyPspInfoFeatureDisabled()) {
				// Feature disabled: Set all logo URLs(For both first and second
				// instrument) to null
				setDetailApiLogoUrl(firstInstrument);
				detailApiResponseV3.getSecondInstrument().forEach(this::setDetailApiLogoUrl);
			}
			else if (PthVersionUtility.isTxnDetailsOtherPartyPspInfoAsTextFeatureEnabled()) {
				// Display text feature: Append PSP display text
				processFirstInstrument(firstInstrument, SET_DISPLAY_TEXT);
				setDetailApiLogoOrderForSecondInstrument(detailApiResponseV3);
			}
			else if (PthVersionUtility.isTxnDetailsOtherPartyPspInfoAsLogoFeatureEnabled()) {
				// Logo URL feature: Set PSP logo URLs
				processFirstInstrument(firstInstrument, SET_LOGO_URL);
				setDetailApiLogoOrderForSecondInstrument(detailApiResponseV3);
			}
		}
		catch (Exception e) {
			log.error("Error while filtering fields for detail response v3: ", e);
		}
	}

	// For PTH-766
	// If status belongs to repeatPaymentDisabledStatusList or in case of failed txns if
	// the error code belongs to the list of repeatPaymentDisabledFailureErrorCodesList
	// then repeat payment will be disabled.
	public boolean isRepeatPaymentDisabled(TransformedTransactionHistoryDetail txn) {
		List<String> repeatPaymentDisabledStatusList = configurablePropertiesHolder
			.getProperty(TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_LIST, List.class);
		List<String> repeatPaymentDisabledFailureErrorCodesList = configurablePropertiesHolder
			.getProperty(TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_FAILURE_ERROR_CODES_LIST, List.class);
		if (txn != null && txn.getContextMap() != null) {
			String errorCode = txn.getContextMap().get(ERROR_CODE);
			if (repeatPaymentDisabledStatusList
				.contains(ClientStatusEnum.getStatusEnumByKey(txn.getStatus()).toString())
					|| repeatPaymentDisabledFailureErrorCodesList.contains(errorCode)) {
				return true;
			}
		}
		return false;
	}

	private void setRepeatPaymentUrl(final DetailApiResponseV3 detailApiResponseV3,
			final DetailInputParams detailInputParams) {
		detailApiResponseV3.setRepeatPayment(null);
		TransformedTransactionHistoryDetail txn = detailInputParams.getListingVisibleTxn();

		if (isRepeatPaymentDisabled(txn)) {
			// if true repeatPayment will remain null and further code will not execute.
			return;
		}

		if (Objects.nonNull(txn.getTxnType())
				&& TransactionTypeEnum.P2P_INWARD.getTransactionTypeKey().equals(txn.getTxnType())) {
			for (TransformedParticipant participant : txn.getParticipants()) {
				if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
					detailApiResponseV3
						.setRepeatPayment(p2pInwardDetailResponseBuilder.getRepeatPaymentDetails(participant, txn));
				}
			}
		}

		if (Objects.nonNull(txn.getTxnType())
				&& TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey().equals(txn.getTxnType())) {
			for (TransformedParticipant participant : txn.getParticipants()) {
				if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
					detailApiResponseV3
						.setRepeatPayment(p2pOutwardDetailResponseBuilder.getRepeatPaymentDetails(participant, txn));
				}
			}
		}

		if (Objects.nonNull(txn.getTxnType())
				&& TransactionTypeEnum.P2M.getTransactionTypeKey().equals(txn.getTxnType())) {
			if ((!isOnlineTxn(txn) && !isOnusTransaction(txn)
					&& (!CollectionUtils.isEmpty(txn.getContextMap()) && FALSE.equals(txn.getContextMap().get(IS_DQR))))
					|| (txn.getContextMap().containsKey(CHANNEL_CODE)
							&& PAYTM_QR_MERCHANTS.equals(txn.getContextMap().get(CHANNEL_CODE)))) {
				detailApiResponseV3.setRepeatPayment(
						p2mDetailResponseBuilder.getRepeatPaymentDetails(null, txn, txn, detailInputParams));
			}
		}
	}

	private void postInstrumentCtasModification(final InstrumentDtoV3 instrumentDtoV3) {
		if (Objects.isNull(instrumentDtoV3)) {
			return;
		}

		if (ObjectUtils.isNotEmpty(instrumentDtoV3.getCtasMap())) {
			instrumentDtoV3.getCtasMap().keySet().removeIf(cta -> !CtaType.CST_NEED_HELP.getCtaType().equals(cta));
		}
	}

	public DetailApiResponseV2 getDetailApiResponseV2(final TransactionSource transactionSource,
			final Map<String, String> tokenMap, final boolean showBankData, final String locale, final String userId,
			final DetailInputParams detailInputParams, final ExtraDetailRequestFields extraDetailRequestFields,
			final String txnId, final String groupId, final TimelineFieldsDto timelineFieldsDto)
			throws PanaromaException, IOException, InterruptedException {
		DetailApiResponse detailApiResponse = getDetailsInternal(transactionSource, txnId, tokenMap, showBankData,
				detailInputParams, locale, userId, groupId, extraDetailRequestFields);
		DetailApiResponseV2 detailApiResponseV2 = DetailResponseV2Mapper.getDetailResponseV2(detailApiResponse,
				detailInputParams.getListingVisibleTxn(), timelineFieldsDto, detailInputParams.getDetailCallVersion());
		ShareScreenEnricher.enrichShareDetailInDetailResponseV2(detailApiResponseV2);

		detailResponseV2Enricher.enrichDetailsV2(detailApiResponseV2, detailInputParams.getListingVisibleTxn());
		return detailApiResponseV2;
	}

	private DetailApiResponseV2 getOnlyTimelineResponse(final TransactionSource transactionSource,
			final Map<String, String> tokenMap, final ClientStatusEnum status, final boolean showOnlyTimeline,
			final String openSource, final TimelineFieldsDto timelineFieldsDto, final String txnId) throws Exception {
		if (Boolean.parseBoolean(
				configurablePropertiesHolder.getProperty(ConfigPropertiesEnum.TIMELINE_ENABLED, String.class))) {
			log.info("Request received for timeline txnId: {} , Detail Source: {}", txnId, openSource);
			DetailApiResponseV2 timeLineResponse = getTimelineResponse(transactionSource, txnId, status,
					showOnlyTimeline, timelineFieldsDto);
			return timeLineDataWithTokenValidation(tokenMap, txnId, showOnlyTimeline, timeLineResponse);
		}
		else {
			log.error(
					"Error Got Request for timeline where timeline is disable Source: {}, TxnId: {}, Detail Source: {}",
					transactionSource, txnId, openSource);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, KNOWN_ISSUE_ERROR);
		}
	}

	private DetailApiResponseV2 getTimelineResponse(final TransactionSource source, final String txnId,
			final ClientStatusEnum status, final boolean showOnlyTimeline, final TimelineFieldsDto timelineFieldsDto)
			throws Exception {
		ITimelineService timelineService = iTimelineServiceFactory.getTimelineServiceBySource(source);

		if (timelineService == null) {
			log.error("Not Enabled Timeline For This Source: {}, TxnId: {}", source, txnId);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		Timeline timelineResponse = upiTimelineServiceNonMono.getTimelineResponse(source, txnId, status,
				showOnlyTimeline, timelineFieldsDto);

		DetailApiResponseV2 detailApiResponse = new DetailApiResponseV2();
		detailApiResponse.setTimeline(timelineResponse);
		log.info("Timeline Response: {} for txnId: {}", timelineResponse, txnId);

		return detailApiResponse;
	}

	private DetailApiResponseV2 timeLineDataWithTokenValidation(final Map<String, String> tokenMap, final String txnId,
			final boolean showOnlyTimeline, final DetailApiResponseV2 timeLineResponse)
			throws IOException, InterruptedException {
		ensureObjMapperInitialized();
		if (Objects.isNull(tokenMap)) {
			log.error("Either token map or userID is null for this txnId: {}", txnId);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}
		ValidationResult validationResult = webUtilityService.validateAndGetUserId(tokenMap, txnId,
				MonitoringConstants.OAUTH_SERVICE_USER_API);
		final String[] userId = new String[1];
		final Boolean[] isTokenValidated = { Boolean.FALSE };
		userId[0] = validationResult.getUserId();
		if (Objects.nonNull(timeLineResponse.getTimeline().getResponse())) {
			UpiTimelineApiResponse upiTimelineApiResponse = objectMapper
				.convertValue(timeLineResponse.getTimeline().getResponse(), UpiTimelineApiResponse.class);
			log.debug("UpiTimelineResponse : {} for this txnId :{}, ", upiTimelineApiResponse, txnId);
			isTokenValidated[0] = Objects.nonNull(upiTimelineApiResponse.getPayerDetails())
					&& (StringUtils.equals(userId[0], upiTimelineApiResponse.getPayerDetails().getCustId()));
			timeLineResponse.setUserId(userId[0]);
			timeLineResponse.setRequestId(ThreadContext.get(WebConstants.REQUEST_ID));
			if (isTokenValidated[0]) {
				return timeLineResponse;
			}
			else {
				return getTimelineResponseForAuthMismatch(timeLineResponse, txnId);
			}
		}
		else {
			return getTimelineResponseForAuthMismatch(timeLineResponse, txnId);
		}
	}

	private DetailApiResponseV2 getTimelineResponseForAuthMismatch(final DetailApiResponseV2 timeLineResponse,
			final String txnId) {

		Timeline timeline = timeLineResponse.getTimeline();
		timeline.setResponse(null);
		timeline.setIsAvailable(Boolean.FALSE);
		timeline.setRefreshTimeline(Boolean.FALSE);
		String errorMsg = TIMELINE_ERROR_MSG_MAP.get(TimelineErrorMsgMapperDto.builder()
			.showOnlyTimeline(Boolean.TRUE)
			.timelineErrorType(TimelineErrorType.AUTHENTICATION_ERROR)
			.build());
		timeline.setMessage(errorMsg);
		timeLineResponse.setTimeline(timeline);
		log.info("Timeline Response for authMismatch: {} for txnId: {}, ", timeLineResponse.getTimeline(), txnId);
		return timeLineResponse;
	}

	public ValidationResult validateAndGetUserId(final Map<String, String> tokens, final String reqId,
			final boolean showBankData) throws IOException, InterruptedException {

		log.debug("trying to get response from Oauth for reqId {} ", reqId);
		String oauthToken = TokenValidatorUtility
			.getTokenFromMap(tokens.getOrDefault(AUTHORIZATION, tokens.get(AUTHORIZATION.toLowerCase())), USER_TOKEN);
		if (StringUtils.isBlank(oauthToken)) {
			log.error("User Token is empty or null for reqId {} ", reqId);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}
		long webClientstartTime = System.currentTimeMillis();
		CustomOauthUserDetailsResponse oauthResponsw = null;
		try {
			oauthResponsw = StringUtils.isNotBlank(tokens.get("userId"))
					? oauthClientNonMono.getUserDetailsFromOauth(oauthToken, USER_ID_DEFAULT, reqId)
					: oauthClientNonMono.getUserDetailsFromOauth(oauthToken, USER_ID_DEFAULT, reqId,
							tokens.get("userId"));
			defaultTransactionHistoryService.handleOauthResponse(oauthResponsw, reqId);

		}
		catch (Exception ex) {
			ExceptionHandlerUtil.handleOauthValidationException(ex, reqId, webClientstartTime);
		}

		try {
			ThreadContext.put("requestId", reqId);
			ValidationResult validationResult = ValidationResult.builder()
				.userId(oauthResponsw.getUserId())
				.customerCreationDate(oauthResponsw.getDefaultInfo().getCustomerCreationDate())
				.build();
			long timeTakenByOauth = System.currentTimeMillis() - webClientstartTime;
			log.debug("Time taken by oauth service to process user api request : latency :{}. reqId {}",
					timeTakenByOauth, reqId);
			metricsAgent.recordApiExecutionTime(MonitoringConstants.OAUTH_SERVICE_USER_API, timeTakenByOauth);
			return validationResult;
		}
		catch (Exception ex) {
			log.error("Error creating listing response. Exception: {}",
					CommonsUtility.exceptionFormatter((Exception) ex));
			throw ex;
		}

		/*
		 * Mono<ValidationResult> validationResultMono = oauthUserDetailsResponse
		 * .flatMap(response ->
		 * Mono.just(ValidationResult.builder().userId(response.getUserId()).
		 * customerCreationDate(response.getDefaultInfo()
		 * .getCustomerCreationDate()).build())).subscriberContext(ctx -> {
		 * ThreadContext.put("requestId", reqId); return ctx; }); //TODO : integrate
		 * TncService.getKycConsent() and return the combined mono of validationResult
		 * with both data. return validationResultMono .elapsed() .flatMap(obj -> { log.
		 * info("Time taken by oauth service to process user api request : {} . latency :{}. reqId {}"
		 * , obj.getT1(), System.currentTimeMillis() - webClientstartTime, reqId);
		 * metricsAgent.recordApiExecutionTime(MonitoringConstants.OAUTH_SERVICE_USER_API,
		 * obj.getT1()); return Mono.just(obj.getT2()); }) .doOnError(e ->
		 * LogHelper.logOnError(ex ->
		 * log.error("Error creating listing response. Exception: {}",
		 * CommonsUtility.exceptionFormatter((Exception) ex)))) .subscriberContext(ctx ->
		 * { ThreadContext.put("requestId", reqId); return ctx; });
		 */
	}

	private UpdateResponseDto emptyUpdatesApiResponse(final SearchContext searchContext, final String langId,
			final Map<String, String> paramMap) {

		PaginationParams paginationParams = new PaginationParams();
		paginationParams.setFromUpdatedDate(Long.toString(searchContext.getFromUpdatedDate()));

		RepoResponseSearchApiDto responseSearchApiDto = new RepoResponseSearchApiDto();
		responseSearchApiDto.setTransformedTransactionHistoryDetailsIterable(Collections.emptyList());
		responseSearchApiDto.setTotalHits(0L);
		responseSearchApiDto.setPaginationParams(paginationParams);

		try {
			UpdateResponseDto updateResponseDto = ResponseBuilderNonMono.buildTxnUpdateResponseDto(responseSearchApiDto,
					searchContext, langId, salaryReportCodes, paramMap, pushMissingUserImageListToKafka,
					fetchUserImageFromCache);

			return updateResponseDto;
		}
		catch (Exception ex) {
			log.error("error while receiving response from repo");
			throw ex;
		}
	}

	private UpdateResponseDto getTxnUpdateResponse(final SearchContext searchContext, final String langId,
			final Map<String, String> paramMap, final CacheState storedCacheState) {
		RepoResponseSearchApiDto response = repo.search(searchContext, paramMap, null);
		if (searchContext.isForUpdates() && !searchContext.isInvalidForUpdates()
				&& defaultTransactionHistoryService.checkIfResponseHasNoUpdates(response, searchContext)) {
			metricsAgent.incrementCount(UPDATES_API_NO_TXNS,
					String.format(monitoringTagFormat, SERVED_FROM_ZERO_DELTA_CACHE, false));
			cacheUtility.pushAppSideCacheDataToKafkaForCache(searchContext, CacheState.ZERO_DELTA, false);
			defaultTransactionHistoryService.modifyPaginationParamsForSingleTxn(response);
		}
		else if (response.getTransformedTransactionHistoryDetailsIterable().size() == searchContext.getPageSize()
				&& searchContext.isForUpdates()) {
			metricsAgent.incrementCount(UPDATES_API_INVALIDATION,
					String.format(monitoringTagFormat, CRITERIA, "txnSize"),
					String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)),
					String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)));
			log.warn("Update search from ES returned {} entries. Need to search ES again for search response",
					response.getTransformedTransactionHistoryDetailsIterable().size());
			searchContext.setInvalidForUpdates(true);
			searchContext.setPageSize(searchContext.getSearchPageSize());
			searchContext.setFromUpdatedDate(null);
			searchContext.setShowInListing(TRUE);
			searchContext.setPaginationTxnId(null);
			searchContext.setPaginationStreamSource(null);
			searchContext.setTransactionDateEpoch(null);
			searchContext.setPageNo(1);
			response = repo.searchWithoutMono(searchContext, paramMap, null);
		}
		else if (storedCacheState == CacheState.OLD_TXN_UPDATES) {
			cacheUtility.pushAppSideCacheDataToKafkaForCache(searchContext, CacheState.OLD_TXN_UPDATES, false);
		}
		try {
			UpdateResponseDto updateResponseDto = ResponseBuilderNonMono.buildTxnUpdateResponseDto(response,
					searchContext, langId, salaryReportCodes, paramMap, pushMissingUserImageListToKafka,
					fetchUserImageFromCache);

			return updateResponseDto;
		}
		catch (Exception ex) {
			log.error("error while receiving response from repo");
			throw ex;
		}
	}

}
