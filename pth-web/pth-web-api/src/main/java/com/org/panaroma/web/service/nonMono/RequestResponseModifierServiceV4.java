package com.org.panaroma.web.service.nonMono;

import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.web.dto.listing.v4.ListingResponseV4;
import com.org.panaroma.web.dto.updates.v2.UpdatesResponseV4;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.CommonConstants.PAGINATION_STREAM_SOURCE;

@Component
public class RequestResponseModifierServiceV4 implements IRequestResponseModifierInterface {

	@Override
	public void modifySearchRequest(final Map<String, String> paramMap) {
		if (Objects.isNull(paramMap)) {
			return;
		}

		// As in search v4 we started sending actual streamSource value (i.e :- UPI) in
		// pagination instead of key.
		// So converting actual value back to key before proceeding further.
		if (paramMap.containsKey(PAGINATION_STREAM_SOURCE)) {
			TransactionSource transactionSource = TransactionSource
				.getTransactionSourceEnumByName(paramMap.get(PAGINATION_STREAM_SOURCE));
			if (Objects.nonNull(transactionSource)) {
				paramMap.put(PAGINATION_STREAM_SOURCE, transactionSource.getTransactionSourceKey().toString());
			}
		}
	}

	@Override
	public void modifySearchResponse(final ListingResponseV4 listingResponseV4) {
		if (Objects.isNull(listingResponseV4)) {
			return;
		}

		// As in search v4 we started sending actual streamSource value (i.e :- UPI) in
		// pagination instead of key.
		// So converting already set key to actual value.
		if (Objects.nonNull(listingResponseV4.getPaginationParams())
				&& StringUtils.isNotBlank(listingResponseV4.getPaginationParams().getStreamSource())) {
			TransactionSource transactionSource = TransactionSource.getTransactionSourceEnumByKey(
					Integer.parseInt(listingResponseV4.getPaginationParams().getStreamSource()));
			if (Objects.nonNull(transactionSource)) {
				listingResponseV4.getPaginationParams().setStreamSource(transactionSource.getTransactionSource());
			}
		}
	}

	@Override
	public void modifyUpdatesResponse(final UpdatesResponseV4 updatesResponseV4) {

		if (Objects.isNull(updatesResponseV4)) {
			return;
		}

		// As in search v4 we started sending actual streamSource value (i.e :- UPI) in
		// pagination instead of key.
		// So converting already set key to actual value.
		if (Objects.nonNull(updatesResponseV4.getPaginationParams())
				&& StringUtils.isNotBlank(updatesResponseV4.getPaginationParams().getStreamSource())) {
			TransactionSource transactionSource = TransactionSource.getTransactionSourceEnumByKey(
					Integer.parseInt(updatesResponseV4.getPaginationParams().getStreamSource()));
			if (Objects.nonNull(transactionSource)) {
				updatesResponseV4.getPaginationParams().setStreamSource(transactionSource.getTransactionSource());
			}
		}
	}

}
