package com.org.panaroma.web.dto.listing.v4;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InstrumentInfoV4 {

	private Integer instrumentType;

	private Integer subInstrumentType;

	private String identifier;

	private String accountType;

	private String logoUrl;

	private String instrumentName;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
