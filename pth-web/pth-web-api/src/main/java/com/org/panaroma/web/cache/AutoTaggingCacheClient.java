package com.org.panaroma.web.cache;

import static com.org.panaroma.commons.constants.AerospikeConstants.V3_PREFIX;
import static com.org.panaroma.commons.constants.AerospikeConstants.VALUE_STRING;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE_FAILURE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE_SUCCESS;
import static com.org.panaroma.commons.constants.Constants.FUTURE_LOG_REMOVER_IDENTIFIER;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;

import com.aerospike.client.Bin;
import com.aerospike.client.Host;
import com.aerospike.client.Key;
import com.aerospike.client.Record;
import com.aerospike.client.policy.ClientPolicy;
import com.aerospike.client.policy.CommitLevel;
import com.aerospike.client.policy.WritePolicy;
import com.fasterxml.jackson.core.type.TypeReference;
import com.org.panaroma.commons.aerospikeExtend.AerospikeClientExtend;
import com.org.panaroma.commons.cache.dto.ListingSpecificCacheData;
import com.org.panaroma.commons.constants.CommonCacheConstants;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.entity.AutoTaggingDetails;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.monitoring.MetricsAgent;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Log4j2
@Component
@Qualifier("AutoTaggingCacheClient")
public class AutoTaggingCacheClient implements ICacheClient {

	private static AerospikeClientExtend aerospikeClient = null;

	private String aeroSpikeUrl;

	private String aerospikeNamespace;

	private Integer aerospikePort;

	private Integer writeDefaultCacheSocketTimeout;

	private Integer writeDefaultCacheTotalTimeout;

	private Integer writeDefaultSleepBetweenRetries;

	private Integer writeDefaultCacheExpiryTime;

	private Integer readDefaultSleepBetweenRetries;

	private Integer readDefaultCacheTotalTimeout;

	private Integer readDefaultCacheSocketTimeout;

	private MetricsAgent metricsAgent;

	@Autowired
	public AutoTaggingCacheClient(@Value("${autoTagging.aerospike.host-name}") final String aeroSpikeUrl,
			@Value("${autoTagging.aerospike.namespace}") final String aerospikeNamespace,
			@Value("${autoTagging.aerospike.port}") final Integer aerospikePort,
			@Value("${aerospike.writePolicyDefault.socketTimeout}") final Integer writeDefaultCacheSocketTimeout,
			@Value("${aerospike.writePolicyDefault.totalTimeout}") final Integer writeDefaultCacheTotalTimeout,
			@Value("${aerospike.writePolicyDefault.sleepBetweenRetries}") final Integer writeDefaultSleepBetweenRetries,
			@Value("${aerospike.writePolicyDefault.expiration}") final Integer writeDefaultCacheExpiryTime,
			@Value("${aerospike.readPolicyDefault.sleepBetweenRetries}") final Integer readDefaultSleepBetweenRetries,
			@Value("${aerospike.readPolicyDefault.totalTimeout}") final Integer readDefaultCacheTotalTimeout,
			@Value("${aerospike.readPolicyDefault.socketTimeout}") final Integer readDefaultCacheSocketTimeout,
			final MetricsAgent metricsAgent) {
		this.aeroSpikeUrl = aeroSpikeUrl;
		this.aerospikeNamespace = aerospikeNamespace;
		this.aerospikePort = aerospikePort;
		this.readDefaultCacheSocketTimeout = readDefaultCacheSocketTimeout;
		this.readDefaultCacheTotalTimeout = readDefaultCacheTotalTimeout;
		this.readDefaultSleepBetweenRetries = readDefaultSleepBetweenRetries;
		this.writeDefaultCacheExpiryTime = writeDefaultCacheExpiryTime;
		this.writeDefaultCacheSocketTimeout = writeDefaultCacheSocketTimeout;
		this.writeDefaultCacheTotalTimeout = writeDefaultCacheTotalTimeout;
		this.writeDefaultSleepBetweenRetries = writeDefaultSleepBetweenRetries;
		this.metricsAgent = metricsAgent;
		initializeCache();
	}

	/*
	 * Always assign aerospikeClient in last other wise it has double checked locking
	 * problem
	 */
	private void initializeCache() {
		if (aerospikeClient != null) {
			return;
		}

		synchronized (this) {
			if (aerospikeClient == null) {
				log.debug("Initializing aerospike client.");
				ClientPolicy policy = new ClientPolicy();
				policy.writePolicyDefault.expiration = this.writeDefaultCacheExpiryTime;
				policy.writePolicyDefault.commitLevel = CommitLevel.COMMIT_ALL;
				policy.writePolicyDefault.socketTimeout = this.writeDefaultCacheSocketTimeout;
				policy.writePolicyDefault.totalTimeout = this.writeDefaultCacheTotalTimeout;
				policy.writePolicyDefault.sleepBetweenRetries = this.writeDefaultSleepBetweenRetries;
				policy.readPolicyDefault.socketTimeout = this.readDefaultCacheSocketTimeout;
				policy.readPolicyDefault.totalTimeout = this.readDefaultCacheTotalTimeout;
				policy.readPolicyDefault.sleepBetweenRetries = this.readDefaultSleepBetweenRetries;

				// Parse comma-separated hosts
				String[] hosts = aeroSpikeUrl.split(",");
				Host[] aerospikeHosts = new Host[hosts.length];
				for (int i = 0; i < hosts.length; i++) {
					aerospikeHosts[i] = new Host(hosts[i].trim(), aerospikePort);
				}

				aerospikeClient = new AerospikeClientExtend(policy, aerospikeHosts);
			}
		}
	}

	@Override
	public Map<String, String> getLocalisedBatchRecords(List<String> cacheBatchKey) {
		throw new UnsupportedOperationException("Operation not supported for AutoTaggingCache");
	}

	@Override
	public Map<String, UserDetails> getUserImageUrlMapFromCache(List<String> key) {
		throw new UnsupportedOperationException("Operation not supported for AutoTaggingCache");
	}

	@Override
	public UserDetails getUserImageDetailsFromEntityId(String entityId) {
		throw new UnsupportedOperationException("Operation not supported for AutoTaggingCache");
	}

	@Override
	public AppSideCacheData getAppSideCacheData(SearchContext searchContext, CacheInfo cacheInfo) {
		throw new UnsupportedOperationException("Operation not supported for AutoTaggingCache");
	}

	@Override
	public Record getRecordFromCache(String key, String set, TypeReference type) throws Exception {
		throw new UnsupportedOperationException("Operation not supported for AutoTaggingCache");
	}

	@Override
	public TransformedTransactionHistoryDetail getTthdFromSet(String txnId, String cacheSet) {
		throw new UnsupportedOperationException("Operation not supported for AutoTaggingCache");
	}

	/**
	 * Saves auto-tagging details to the cache. PTH-228 Auto-tagging implementation
	 * @param key The key to save the auto-tagging details under
	 * @param setName The set name to save the auto-tagging details under
	 * @param expiryTime The expiry time for the auto-tagging details
	 * @param autoTaggingDetails The auto-tagging details to save
	 * @throws Exception if there's an error while saving the auto-tagging details
	 */
	@Override
	public void saveAutoTaggingDetails(final String key, final String setName, final Integer expiryTime,
			final AutoTaggingDetails autoTaggingDetails) throws Exception {
		try {
			// Check if the key, setName and autoTaggingDetails are not null
			if (StringUtils.isBlank(key) || StringUtils.isBlank(setName) || Objects.isNull(autoTaggingDetails)) {
				log.warn("Key: {} or Data: {} or Set: {} to be saved is null.", key, autoTaggingDetails, setName);
				return;
			}
			// Save the auto tagging details to the cache
			String finalKey = V3_PREFIX + key;

			// Adding this log for testing at QA
			log.info(FUTURE_LOG_REMOVER_IDENTIFIER
					+ "Saving record to Aerospike for autotagging for key: {} and set: {}, host: {}, namespace: {}",
					key, setName, aeroSpikeUrl, aerospikeNamespace);

			log.debug("Saving AutoTaggingDetails record to cache for key :{}", key);
			put(finalKey, setName, expiryTime, autoTaggingDetails);
		}
		catch (Exception e) {
			log.error("Exception occurred while saving AutoTaggingDetails to Cache for : {}, {}", setName,
					CommonsUtility.exceptionFormatter(e));
			throw e;
		}
	}

	public void put(final String key, final String set, final Integer expiryTime, final Object record)
			throws Exception {
		if (StringUtils.isBlank(key) || Objects.isNull(record) || StringUtils.isBlank(set)
				|| Objects.isNull(expiryTime)) {
			log.warn("Key: {} or Data: {} or Set: {} or expiryTime: {} to be saved is null.", key, record, set,
					expiryTime);
			return;
		}
		long startTime = System.currentTimeMillis();
		try {
			initializeCache();
			WritePolicy writePolicy = getWritePolicy(expiryTime);

			Key cacheKey = new Key(aerospikeNamespace, set, key);

			aerospikeClient.put(writePolicy, cacheKey, "", new Bin(VALUE_STRING, record));
			metricsAgent.incrementCount(CommonCacheConstants.AEROSPIKE, CommonCacheConstants.SET + COLON + set,
					CommonCacheConstants.STATUS + COLON + WRITE_SUCCESS);
		}
		catch (Exception ex) {
			log.error(
					"Exception occurred while saving record Key: {}, Data: {},  Set: {}, expiryTime: {}, Exception: {}",
					key, record, set, expiryTime, CommonsUtility.exceptionFormatter(ex));
			metricsAgent.incrementCount(CommonCacheConstants.AEROSPIKE, CommonCacheConstants.SET + COLON + set,
					CommonCacheConstants.STATUS + COLON + WRITE_FAILURE);
			throw ex;
		}
		finally {
			metricsAgent.recordExecutionTime(CommonCacheConstants.AEROSPIKE, System.currentTimeMillis() - startTime,
					CommonCacheConstants.SET + COLON + set, CommonCacheConstants.ACTION + COLON + WRITE);
		}
	}

	private WritePolicy getWritePolicy(final int expiryTimeInSec) {
		WritePolicy writePolicy = new WritePolicy();
		writePolicy.expiration = expiryTimeInSec;
		writePolicy.commitLevel = CommitLevel.COMMIT_ALL;
		writePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
		writePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
		writePolicy.sleepBetweenRetries = writeDefaultSleepBetweenRetries;
		return writePolicy;
	}

}