// package com.org.panaroma.web.dto.detailAPI.detailV3;
//
// import static com.org.panaroma.web.monitoring.MonitoringConstants.DETAIL_API;
//
// import java.util.Collections;
// import java.util.List;
// import lombok.AllArgsConstructor;
// import lombok.Getter;
//
// @Getter
// @AllArgsConstructor
// public enum ApiAppVersionEnum {
// //versionNumber|D|digit after decimal i.e 1.0- v1D0, 1.1-v1D1
// v0D9(0.9,Collections.unmodifiableList(List.of(DETAIL_API))),
// v1D0(1.0, Collections.unmodifiableList(List.of(DETAIL_API))),
//
// /*
// We will start getting the detailVersion as 2.0 from app version 10.35
// The version has been raised to 2.0 to ensure that the deeplink for the "View History"
// cta is shared
// exclusively for P2M transactions, including both 3rd party and OFUS transactions,
// beginning with detailVersion 2.0.
// */
// v2D0(2.0,Collections.unmodifiableList(List.of(DETAIL_API))),
// v3D0(3.0,Collections.unmodifiableList(List.of(DETAIL_API))),
// v4D0(4.0,Collections.unmodifiableList(List.of(DETAIL_API))),// PTH-1063 -> Added
// support for version 4
// v4D1(4.1, Collections.unmodifiableList(List.of(DETAIL_API))); // PTH-1063 -> Added
// support for version 4.1
//
//
// private Double version;
// private List<String> supportedApis;
//
// public static boolean isVersionSupportedForApi(final Double version, final String
// apiName) {
// for (ApiAppVersionEnum apiAppVersionEnum :
// ApiAppVersionEnum.values()) {
// if (apiAppVersionEnum.getVersion().equals(version)) {
// return apiAppVersionEnum.getSupportedApis().contains(apiName);
// }
// }
// return false;
// }
//
// public static ApiAppVersionEnum getApiAppVersionEnumByKey(final Double version) {
// for (ApiAppVersionEnum apiAppVersionEnum :
// ApiAppVersionEnum.values()) {
// if (apiAppVersionEnum.getVersion().equals(version)) {
// return apiAppVersionEnum;
// }
// }
// return null;
// }
// }
