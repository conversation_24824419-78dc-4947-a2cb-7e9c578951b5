package com.org.panaroma.web.dto.listing.v4;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.JsonUtils;
import com.org.panaroma.web.dto.listing.v4.BankDetailV4;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SecondPartyInfoV4 {

	private String fallbackLogoUrl;

	private String logoUrl;

	private String identifier;

	private String reportedCustId;

	private String name;

	private String mobileNumber;

	private BankDetailV4 bankDetail;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
