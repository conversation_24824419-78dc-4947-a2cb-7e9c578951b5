package com.org.panaroma.web.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.org.panaroma.commons.dto.Logo;
import java.util.List;

import com.org.panaroma.commons.dto.es.TransformedParticipant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SecondPartyInfo {

	String logoUrl;

	List<Logo> logoOrder;

	// secondary party is identified based on entityId,
	// name is picked from participant level, but that to depend on TransactionType??
	String name;

	String entityId;

	List<NameDetails> nameOrder;

	@JsonIgnore
	private TransformedParticipant participant;

}
