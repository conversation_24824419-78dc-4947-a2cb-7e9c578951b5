package com.org.panaroma.web.detailAPI;

import com.org.panaroma.commons.dto.CardType;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedCardData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.UpiInternationalUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.cstData.CstDataUtility;
import com.org.panaroma.web.dto.Client;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.web.utility.CurrencyUtility;
import com.org.panaroma.web.utility.DataValidationUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.MandateUtility;
import com.org.panaroma.web.utility.StatusLogoUtility;
import com.org.panaroma.web.utility.UpiLiteViewUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;

import static com.org.panaroma.commons.constants.CommonConstants.IGNORED_PARTICIPANT;
import static com.org.panaroma.commons.constants.CommonConstants.IS_ADDED_PARTICIPANT_BY_MERGING;
import static com.org.panaroma.commons.constants.CommonConstants.TRUE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.REPEAT_PAYMENT_BLOCKING_PAYTM_MERCHANT_VPA_PREFIX_FOR_VPA_BASED_REPEAT_PAYMENT;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.REPEAT_PAYMENT_VPA_BASED_FOR_PAYTM_QR_MERCHANTS_ENABLE;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_P2M_FAILURE;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_P2M_PENDING;
import static com.org.panaroma.commons.constants.LocalizationConstants.ENGLISH_LOCALE;
import static com.org.panaroma.commons.constants.WebConstants.AC_NO;
import static com.org.panaroma.commons.constants.WebConstants.ADD_DETAIL_SEPARATOR;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.BANK_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.CARD_NO;
import static com.org.panaroma.commons.constants.WebConstants.CHANNEL_CODE;
import static com.org.panaroma.commons.constants.WebConstants.CREDIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.DEBIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.ERROR_MESSAGE;
import static com.org.panaroma.commons.constants.WebConstants.FASTAG;
import static com.org.panaroma.commons.constants.WebConstants.FASTAG_ID;
import static com.org.panaroma.commons.constants.WebConstants.FASTAG_ORDER_ID;
import static com.org.panaroma.commons.constants.WebConstants.FROM;
import static com.org.panaroma.commons.constants.WebConstants.GIFT_VOUCHER;
import static com.org.panaroma.commons.constants.WebConstants.LOCALIZATION_KEY_STORECASH_DISPLAY_NAME;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_DEFAULT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_VPA;
import static com.org.panaroma.commons.constants.WebConstants.MID;
import static com.org.panaroma.commons.constants.WebConstants.MONEY_DEDUCTED_ON;
import static com.org.panaroma.commons.constants.WebConstants.ORDER_ID;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_QR_MERCHANTS;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_STORECASH;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_LOAN;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.QR_ID;
import static com.org.panaroma.commons.constants.WebConstants.RESULT_MSG;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_MERCHANT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.STORECASH_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.TOLL_CROSSED_ON;
import static com.org.panaroma.commons.constants.WebConstants.TXN_ID;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;
import static com.org.panaroma.commons.constants.WebConstants.UPI_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.P2M;
import static com.org.panaroma.commons.constants.WebConstants.VEHICLE_REG_NO;
import static com.org.panaroma.commons.constants.WebConstants.VIA_NET_BANKING;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_TXN_ID;
import static com.org.panaroma.commons.constants.WebConstants.BANK_POWEREDBY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.LOYALTY_POINT;
import static com.org.panaroma.commons.utils.Utility.getSelfParticipant;

@Component
@Log4j2
public class P2mDetailResponseBuilder extends AbstractTransactionTypeDetailResponseBuilder {

	@Autowired
	RecurringMandateDetailResponseBuilder recurringMandateDetailResponseBuilder;

	@Autowired
	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Value("${rpt.paymnt.url.p2m.android}")
	String rptPaymntUrlAndroid;

	@Value("${rpt.paymnt.url.p2m.ios}")
	String rptPaymntUrlIos;

	@Value("${rpt.paymnt.url.p2m.upi}")
	String rptPaymntUrlP2mUpi;

	@Value("${rpt.paymnt.url.p2m.paytQR.mid}")
	String rptPaymentUrlPaytmQrForMid;

	@Value("${rpt.paymnt.url.p2m.paytmQR.vpa}")
	String rptPaymentUrlPaytmQrForVpa;

	public static final String URL_NAME = "Pay Again";

	private static Map<String, Boolean> fieldsForUrlForAndroidClient = new HashMap<>();

	private static Map<String, Boolean> fieldsForUrlForIosClient = new HashMap<>();

	private static Map<String, Boolean> fieldsForUrlForPaytmQrForMid = new HashMap<>();

	private static Map<String, Boolean> fieldsForUrlForPaytmQrForVpa = new HashMap<>();

	private static Map<String, Boolean> fieldsForUrlForP2mUpi = new HashMap<>();

	static {
		fieldsForUrlForAndroidClient.put(QR_ID, true);

		fieldsForUrlForIosClient.put(QR_ID, true);

		fieldsForUrlForPaytmQrForMid.put(MID, true);

		fieldsForUrlForPaytmQrForVpa.put(MERCHANT_VPA, true);
		fieldsForUrlForPaytmQrForVpa.put(RPT_PYMNT_MERCHANT_NAME, true);

		fieldsForUrlForP2mUpi.put(RPT_PYMNT_MERCHANT_NAME, true);
		fieldsForUrlForP2mUpi.put(MERCHANT_VPA, true);
		fieldsForUrlForP2mUpi.put(AMOUNT, false);
	}

	@Override
	public TransactionTypeEnum buildDetailApiResponseFor() {
		return TransactionTypeEnum.P2M;
	}

	@Override
	public List<TransactionTypeEnum> listToBuildDetailApiResponseFor() {
		return Arrays.asList(TransactionTypeEnum.P2M, TransactionTypeEnum.P2M_INTERNATIONAL);
	}

	@Override
	public DetailApiResponse getResponse(final List<TransformedTransactionHistoryDetail> detailList, final String txnId)
			throws Exception {
		return getResponse(detailList, txnId, null);
	}

	public DetailApiResponse getResponse(final List<TransformedTransactionHistoryDetail> detailList, final String txnId,
			final DetailInputParams detailInputParams) throws Exception {

		TransformedTransactionHistoryDetail txn = null;
		TransformedTransactionHistoryDetail detailNeededForTxn = null;
		TransformedTransactionHistoryDetail walletTxn = null;
		TransformedTransactionHistoryDetail upiTxn = null;

		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (Boolean.TRUE.equals(detail.getShowInListing())) {
				txn = detail;
			}
			if (detail.getTxnId().equalsIgnoreCase(txnId)) {
				detailNeededForTxn = detail;
			}
			if (TransactionSource.WALLET.getTransactionSourceKey().equals(detail.getStreamSource())) {
				walletTxn = detail;
			}
			if (TransactionSource.UPI.getTransactionSourceKey().equals(detail.getStreamSource())) {
				upiTxn = detail;
			}
		}

		if (MandateUtility.txnValidForRecurring(detailNeededForTxn)) {
			return recurringMandateDetailResponseBuilder.getResponse(detailList, txnId);
		}

		boolean isPgTxn = TransactionSource.isPgTypeSource(txn.getStreamSource());
		boolean isOmsTxn = TransactionSource.OMS.getTransactionSourceKey().equals(txn.getStreamSource());
		boolean isUpiTxn = TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource());

		DetailApiResponse detailApiResponse = new DetailApiResponse();

		// spl handling for UPI txns due to a bug at pg's end
		if (TransactionSource.UPI.getTransactionSourceKey().equals(detailNeededForTxn.getStreamSource())
				&& TransactionSource.isPgTypeSource(detailNeededForTxn.getSourceSystem())) {
			detailApiResponse.setStatus(ClientStatusEnum.getStatusEnumByKey(detailNeededForTxn.getStatus()).toString());
		}
		else {
			detailApiResponse.setStatus(GenericUtility.getStatus(detailNeededForTxn, detailList));
		}
		detailApiResponse.setStatusLogoUrl(
				StatusLogoUtility.getStatusLogoUrl(ClientStatusEnum.valueOf(detailApiResponse.getStatus())));
		detailApiResponse.setCurrency(Currency.getCurrencyByKey(txn.getCurrency()));
		detailApiResponse.setDateTime(DateTimeUtility.getDateTime(txn.getTxnDate()));
		detailApiResponse.setAmount(Currency.getCurrencyAmountInHigherDenomination(txn.getAmount(), txn.getCurrency()));
		detailApiResponse.setTxnIndicator(String.valueOf(txn.getTxnIndicator()));
		detailApiResponse
			.setDetailNarration(getNarrationForStatus(ClientStatusEnum.valueOf(detailApiResponse.getStatus())));

		// setting notes in case status is not SUCCESS
		if (!ClientStatusEnum.SUCCESS.equals(ClientStatusEnum.valueOf(detailApiResponse.getStatus()))
				&& detailNeededForTxn.getContextMap() != null) {
			if (TransactionSource.isPgTypeSource(detailNeededForTxn.getStreamSource())
					&& getSelfParticipant(detailNeededForTxn) != null
					&& getSelfParticipant(detailNeededForTxn).getContextMap() != null
					&& StringUtils.isNotBlank(getSelfParticipant(detailNeededForTxn).getContextMap().get(RESULT_MSG))) {
				// Setting contextMap.resultMsg as notes for PG events when available
				detailApiResponse.setNotes(getSelfParticipant(detailNeededForTxn).getContextMap().get(RESULT_MSG));
			}
			else if (detailNeededForTxn.getContextMap().containsKey(ERROR_MESSAGE)
					&& StringUtils.isNotBlank(detailNeededForTxn.getContextMap().get(ERROR_MESSAGE))) {
				detailApiResponse.setNotes(detailNeededForTxn.getContextMap().get(ERROR_MESSAGE));
			}
		}
		Map<String, String> refIds = new HashMap<>();
		List<InstrumentDto> creditInstruments = new ArrayList<>();
		List<InstrumentDto> debitInstruments = new ArrayList<>();
		for (TransformedParticipant participant : txn.getParticipants()) {
			if (null != participant.getContextMap() && participant.getContextMap().containsKey(IGNORED_PARTICIPANT)
					&& TRUE.equalsIgnoreCase(participant.getContextMap().get(IGNORED_PARTICIPANT))) {
				continue;
			}
			InstrumentDto instrumentDto = new InstrumentDto();
			instrumentDto.setCurrency(Currency.getCurrencyByKey(participant.getCurrency()));
			instrumentDto.setAmount(
					Currency.getCurrencyAmountInHigherDenomination(participant.getAmount(), participant.getCurrency()));
			instrumentDto.setInstrumentStatus(participant.getStatus());

			DetailApiUtil.setParticipantInfo(txn, participant, instrumentDto, buildDetailApiResponseFor(), false);
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())
					&& !txn.getEntityId().equals(participant.getEntityId())) {
				instrumentDto.setNarration("To");
				instrumentDto.setName(participant.getName());
				if (participant.getUpiData() != null) {
					instrumentDto.setInstrumentDetail(participant.getUpiData().getVpa());
				}
				detailApiResponse.setMerchantType(Objects.isNull(participant.getMerchantData())
						|| Objects.isNull(participant.getMerchantData().getMerchantType())
								? MerchantTypeEnum.OTHER.toString()
								: MerchantTypeEnum.getMerchantTypeByKey(participant.getMerchantData().getMerchantType())
									.toString());
				instrumentDto.setAdditionalDetail(participant.getRemarks());
				if (walletTxn != null && walletTxn.getContextMap() != null
						&& FASTAG.equals(walletTxn.getContextMap().get(TXN_PURPOSE))) {
					String fstg = "";
					for (TransformedParticipant transformedParticipant : walletTxn.getParticipants()) {
						if (transformedParticipant.getMerchantData() != null
								&& TransactionIndicator.CREDIT.getTransactionIndicatorKey()
									.equals(transformedParticipant.getTxnIndicator())) {
							instrumentDto.setName(transformedParticipant.getName());
							fstg = getFastagDetails(transformedParticipant.getContextMap());
							break;
						}
					}
					if (!StringUtils.isEmpty(fstg)) {
						if (StringUtils.isEmpty(instrumentDto.getAdditionalDetail())) {
							instrumentDto.setAdditionalDetail(fstg);
						}
						else {
							instrumentDto
								.setAdditionalDetail(instrumentDto.getAdditionalDetail() + ADD_DETAIL_SEPARATOR + fstg);
						}
					}
				}
				instrumentDto.setLogoUrl(getLogoUrl(participant, txn));
				instrumentDto.setIsLogoUrlOfEntity(true);
				// for pending or failed transaction we will be adding single credit
				// instrument only
				// for successful txns add all instruments. This will be handled in
				// GenericUtility.formatDetailResponse()
				if (ClientStatusEnum.SUCCESS.equals(ClientStatusEnum.valueOf(detailApiResponse.getStatus()))) {
					creditInstruments.add(instrumentDto);
				}
				else {
					if (creditInstruments.isEmpty()) {
						creditInstruments.add(instrumentDto);
					}
				}
			}
			else if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())
					&& txn.getEntityId().equals(participant.getEntityId())) {
				instrumentDto.setNarration("From Your");
				setData(instrumentDto, participant, detailList, refIds, debitInstruments, isPgTxn, isUpiTxn, isOmsTxn,
						txn);
			}
		}

		// Special handling of status at participant level for UPI International
		// for case PSP - other and Bank - Paytm
		// we get only pending upi event with pending status at order and participant
		// level both
		// we handle order level status at ingester level but participant not.
		// so at view we will set status same as order level for this scenario
		if (UpiInternationalUtility.isUpiInternationalAndOtherPspUpiEvent(txn)) {
			// setting for credit lag
			for (InstrumentDto creditDto : creditInstruments) {
				creditDto.setInstrumentStatus(txn.getStatus());
			}
			// setting for debit lag
			for (InstrumentDto debitDto : debitInstruments) {
				debitDto.setInstrumentStatus(txn.getStatus());
			}
		}
		detailApiResponse.setRepeatPayment(getRepeatPaymentDetails(walletTxn, txn, upiTxn, detailInputParams));

		refIds.put(ORDER_ID, txn.getOrderId());

		if (Utility.isWalletInterOpTxn(upiTxn)) {
			String rrn = GenericUtilityExtension.getRrnFromUpiDoc(upiTxn);
			if (org.apache.commons.lang3.StringUtils.isNotBlank(rrn)) {
				refIds.put(UPI_REFERENCE_NO, rrn);
			}
		}

		detailApiResponse.setFirstInstrument(creditInstruments);
		detailApiResponse.setSecondInstrument(debitInstruments);
		detailApiResponse.setReferenceIdMap(refIds);
		detailApiResponse
			.setCstorderItem(CstDataUtility.getCstForMultipleSystem(detailNeededForTxn, detailList, detailApiResponse));
		detailApiResponse.setReferenceIds(GenericUtility.convertRefIdMapToList(detailApiResponse.getReferenceIdMap()));
		detailApiResponse.setInternationalCurrencyConversionDetails(
				CurrencyUtility.getCurrencyConversionDetails(detailNeededForTxn));

		return detailApiResponse;
	}

	private String getLogoUrl(final TransformedParticipant participant, final TransformedTransactionHistoryDetail txn) {
		Logo logo = LogoUtility.checkAndGetLogoByVpa(participant, txn);
		if (Objects.nonNull(logo)) {
			return logo.getValue();
		}
		String logoUrl = LogoUtility.getLogoHelper(participant);
		if (!StringUtils.isEmpty(logoUrl)) {
			return logoUrl;
		}
		return LogoUtility.getLogo(participant, txn, MERCHANT_DEFAULT_LOGO, LogoType.TRANSACTION_CATEGORY_ICON);
	}

	public RepeatPayment getRepeatPaymentDetails(final TransformedTransactionHistoryDetail walletTxn,
			final TransformedTransactionHistoryDetail txn, final TransformedTransactionHistoryDetail upiTxn,
			final DetailInputParams detailInputParams) {

		if (DataValidationUtility.isRepeatPaymentLegacyImplEnabled()) {
			if (!Objects.isNull(detailInputParams) && !Objects.isNull(walletTxn)
					&& !Objects.isNull(walletTxn.getContextMap())
					&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(txn.getTxnIndicator())) {
				if (Client.androidapp.name().equals(detailInputParams.getClient())) {
					return GenericUtility.getRepeatPaymentDetails(fieldsForUrlForAndroidClient, rptPaymntUrlAndroid,
							null, walletTxn, URL_NAME);
				}
				if (Client.iosapp.name().equals(detailInputParams.getClient())) {
					return GenericUtility.getRepeatPaymentDetails(fieldsForUrlForIosClient, rptPaymntUrlIos, null,
							walletTxn, URL_NAME);
				}
			}

			//
			if (Objects.nonNull(txn) && txn.getContextMap() != null && txn.getContextMap().containsKey(CHANNEL_CODE)
					&& PAYTM_QR_MERCHANTS.equals(txn.getContextMap().get(CHANNEL_CODE))) {
				TransformedParticipant merchantParticipant = null;
				for (TransformedParticipant participant : txn.getParticipants()) {
					if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
						merchantParticipant = participant;
					}
				}
				// if merchantId is present then set merchantId
				if (Objects.nonNull(merchantParticipant) && Objects.nonNull(merchantParticipant.getMerchantData())
						&& Objects.nonNull(merchantParticipant.getMerchantData().getMerchantId())) {
					return GenericUtility.getRepeatPaymentDetails(fieldsForUrlForPaytmQrForMid,
							rptPaymentUrlPaytmQrForMid, merchantParticipant, upiTxn, URL_NAME);
				}

				if (configurablePropertiesHolder.getProperty(REPEAT_PAYMENT_VPA_BASED_FOR_PAYTM_QR_MERCHANTS_ENABLE,
						Boolean.class)) {
					// if merchantId is not present then set merchantVpa
					String merchantVpa = "";
					if (Objects.nonNull(merchantParticipant) && Objects.nonNull(merchantParticipant.getUpiData())
							&& org.apache.commons.lang3.StringUtils
								.isNotBlank(merchantParticipant.getUpiData().getVpa())) {
						merchantVpa = merchantParticipant.getUpiData().getVpa();
					}

					String merchantVpaPrefix = configurablePropertiesHolder.getProperty(
							REPEAT_PAYMENT_BLOCKING_PAYTM_MERCHANT_VPA_PREFIX_FOR_VPA_BASED_REPEAT_PAYMENT,
							String.class);

					// merchant blocking prefix is blank or not matching with
					// merchantVpaPrefix
					if (org.apache.commons.lang3.StringUtils.isBlank(merchantVpaPrefix)
							|| (org.apache.commons.lang3.StringUtils.isNotBlank(merchantVpa)
									&& !merchantVpa.startsWith(merchantVpaPrefix))) {
						return GenericUtility.getRepeatPaymentDetails(fieldsForUrlForPaytmQrForVpa,
								rptPaymentUrlPaytmQrForVpa, merchantParticipant, upiTxn, URL_NAME);
					}
				}

				// if Merchant VPA based deeplink is not enable or merchantVpa is not
				// present then return null
				return null;
			}

			/*
			 * Condition for P2M UPI Repeat payment's deeplink upiTxn - not null &&
			 * isSource TRUE && sourceSystem null && txnIndicator - debit
			 */
			if (Objects.nonNull(upiTxn) && Boolean.TRUE.equals(upiTxn.getIsSource())
					&& Objects.isNull(upiTxn.getSourceSystem())
					&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(upiTxn.getTxnIndicator())) {
				TransformedParticipant merchantParticipant = null;
				for (TransformedParticipant participant : upiTxn.getParticipants()) {
					if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
						merchantParticipant = participant;
					}
				}
				RepeatPayment repeatPayment = GenericUtility.getRepeatPaymentDetails(fieldsForUrlForP2mUpi,
						rptPaymntUrlP2mUpi, merchantParticipant, upiTxn, URL_NAME);
				return repeatPayment;
			}
		}
		return null;
	}

	private String getFastagDetails(final Map<String, String> contextMap) {
		if (contextMap == null) {
			return null;
		}
		StringBuilder str = new StringBuilder();
		if (contextMap.containsKey("orderId") && !StringUtils.isEmpty(contextMap.get("orderId"))) {
			str.append(FASTAG_ORDER_ID).append(SPACE).append(contextMap.get("orderId"));
		}
		if (contextMap.containsKey("vehicleRegNo") && !StringUtils.isEmpty(contextMap.get("vehicleRegNo"))) {
			str.append(ADD_DETAIL_SEPARATOR)
				.append(VEHICLE_REG_NO)
				.append(SPACE)
				.append(contextMap.get("vehicleRegNo"));
		}
		if (contextMap.containsKey("tollCrossedOn") && !StringUtils.isEmpty(contextMap.get("tollCrossedOn"))) {
			str.append(ADD_DETAIL_SEPARATOR)
				.append(TOLL_CROSSED_ON)
				.append(SPACE)
				.append(contextMap.get("tollCrossedOn"));
		}
		if (contextMap.containsKey("moneyDeductedOn") && !StringUtils.isEmpty(contextMap.get("moneyDeductedOn"))) {
			str.append(ADD_DETAIL_SEPARATOR)
				.append(MONEY_DEDUCTED_ON)
				.append(SPACE)
				.append(contextMap.get("moneyDeductedOn"));
		}
		if (contextMap.containsKey("fastagId") && !StringUtils.isEmpty(contextMap.get("fastagId"))) {
			str.append(ADD_DETAIL_SEPARATOR).append(FASTAG_ID).append(SPACE).append(contextMap.get("fastagId"));
		}
		if (str.indexOf("\n") == 0) {
			str.delete(0, 1);
		}
		return str.toString();
	}

	public String getNarrationForStatus(final ClientStatusEnum status) {
		String narration = "";
		switch (status) {
			case SUCCESS:
				narration = "Money Paid";
				break;
			case FAILURE:
				narration = DETAIL_NARRATION_P2M_FAILURE.getLocaleMsgKey();
				break;
			case PENDING:
				narration = DETAIL_NARRATION_P2M_PENDING.getLocaleMsgKey();
				break;
			default:
		}
		return narration;
	}

	private void setData(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final List<TransformedTransactionHistoryDetail> detailList, final Map<String, String> refIds,
			final List<InstrumentDto> debitInstruments, final boolean isPgTxn, final boolean isUpiTxn,
			final boolean isOmsTxn, final TransformedTransactionHistoryDetail txn) throws Exception {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		if (paymentSystem == null) {
			log.error("unexpected payment system for P2M detail response, participant entityId:{} ",
					participant.getEntityId());
			throw new RuntimeException("Unexpected payment system P2M detail response");
		}

		switch (paymentSystem) {
			case WALLET:
				if (isPgTxn) {
					setWalletData(instrumentDto, detailList, participant, debitInstruments, refIds, txn);
				}
				else if (isUpiTxn) { // Wallet InterOp Case where paymentSystem is wallet
					setWalletData(instrumentDto, detailList, participant, debitInstruments, refIds, txn);
				}
				else {
					instrumentDto
						.setName(WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()));
					instrumentDto.setLogoUrl(LogoUtility.getLogo(instrumentDto.getName(), LogoType.WALLET_ICON));
					debitInstruments.add(instrumentDto);
					String refId = GenericUtilityExtension.setRefIdsForWallet(detailList);
					if (org.apache.commons.lang3.StringUtils.isNotBlank(refId)) {
						refIds.put(WALLET_TXN_ID, refId);
					}
				}
				break;
			case UPI:
				if (isPgTxn || isOmsTxn) {
					if (UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(participant)) {
						UpiLiteViewUtility.setSecondInstrumentForDetailResponse(instrumentDto, P2M);
						debitInstruments.add(instrumentDto);
						setRrn(refIds, participant);
					}
					else {
						setUpiData(instrumentDto, detailList, participant, debitInstruments, refIds);
					}
				}
				else {
					if (UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(participant)) {
						UpiLiteViewUtility.setSecondInstrumentForDetailResponse(instrumentDto, P2M);
					}
					else if (Utility.isUpiViaCcTxn(txn, participant)) {
						setInstrumentForUpiViaCc(participant, instrumentDto);
					}
					else if (Objects.nonNull(participant.getBankData())
							&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankName())
							&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getIfsc())
							&& org.apache.commons.lang3.StringUtils
								.isNotBlank(participant.getBankData().getAccNumber())) {
						instrumentDto.setName(participant.getBankData().getBankName());
						instrumentDto.setInstrumentDetail(
								AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
						instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
								participant.getBankData().getBankName()));
					}
					debitInstruments.add(instrumentDto);
					setRrn(refIds, participant);
				}
				break;
			case PAYTM_POSTPAID:
				instrumentDto.setName(POSTPAID_LOAN);
				instrumentDto.setLogoUrl(LogoUtility.getLogo(POSTPAID_LOGO, LogoType.WALLET_ICON));
				debitInstruments.add(instrumentDto);
				if (participant.getContextMap() != null) {
					refIds.put(TXN_ID, participant.getContextMap().getOrDefault("postpaid_txn_id", null));
				}
				break;
			case PG:
			case BANK:
				if (participant.getBankData() != null && participant.getBankData().getBankTxnId() != null) {
					refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
				}
				TransformedCardData cardData = participant.getCardData();
				if (cardData != null) {

					if (org.apache.commons.lang3.StringUtils.isNotBlank(cardData.getCardNum())) {
						instrumentDto.setInstrumentDetail(CARD_NO + " " + cardData.getCardNum());
					}

					String cardType = CardType.CREDIT.getCardTypeKey().equals(cardData.getCardType()) ? CREDIT_CARD
							: DEBIT_CARD;
					instrumentDto.setName(cardData.getCardIssuer() + " " + cardType);
					instrumentDto.setLogoUrl(LogoUtility.getBankLogo(null, cardData.getCardIssuer()));
				}
				else if (Objects.nonNull(participant.getBankData())
						&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankName())
						&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getIfsc())) {

					instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
							participant.getBankData().getBankName()));
					instrumentDto.setName(participant.getBankData().getBankName());
					instrumentDto.setInstrumentDetail(VIA_NET_BANKING);
				}
				else {
					if (participant.getUpiData() != null
							&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
						instrumentDto.setNarration(FROM);
						instrumentDto.setName(participant.getUpiData().getVpa());
						instrumentDto.setLogoUrl(LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER));
					}
				}
				debitInstruments.add(instrumentDto);
				break;
			case MGV:
			case OTHER:
				DetailApiUtil.setOtherData(instrumentDto, participant);
				debitInstruments.add(instrumentDto);
				break;
			case GV:
				instrumentDto.setLogoUrl(LogoUtility.getLogo(GIFT_VOUCHER, LogoType.GIFT_VOUCHER_ICON));
				debitInstruments.add(instrumentDto);
				break;
			case STORE_CASH:
				instrumentDto.setNarration(FROM);
				instrumentDto.setName(LocalizedDataCacheService
					.getLocalizedValue(LOCALIZATION_KEY_STORECASH_DISPLAY_NAME, PAYTM_STORECASH, ENGLISH_LOCALE));
				instrumentDto.setLogoUrl(LogoUtility.getLogo(STORECASH_LOGO, LogoType.STORECASH_ICON));
				debitInstruments.add(instrumentDto);
				break;

			case EMI:
				handleEmiCase(instrumentDto, participant, refIds, debitInstruments);
				break;

			case LOYALTY_POINT:
				handleLoyaltyPointCase(instrumentDto, participant, refIds, debitInstruments);
				break;

			case BANK_MANDATE:
				handleBankMandateCase(instrumentDto, participant, refIds, debitInstruments);
				break;

			case PAYTM_CASH:
				handlePaytmCashCase(instrumentDto, participant, refIds, debitInstruments);
				break;

			case EMI_DEBIT_CARD:
				handleEmiDebitCardCase(instrumentDto, participant, refIds, debitInstruments);
				break;

			case NET_BANKING:
				handleNetBankingCase(instrumentDto, participant, refIds, debitInstruments);
				break;

			default:
				log.error("unexpected payment system for P2M detail response, participant entityId:{} ",
						participant.getEntityId());
				throw new RuntimeException("Unexpected payment system P2M detail response");
		}
	}

	/**
	 * Handles the EMI case for the setData method.
	 */
	private void handleEmiCase(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final Map<String, String> refIds, final List<InstrumentDto> debitInstruments) {
		if (participant.getBankData() != null && participant.getBankData().getBankTxnId() != null) {
			refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
		}
		instrumentDto.setNarration(FROM);
		if (Objects.nonNull(participant.getBankData())
				&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankName())) {
			instrumentDto.setName(participant.getBankData().getBankName() + " EMI");
			instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
					participant.getBankData().getBankName()));
			if (org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
				instrumentDto
					.setInstrumentDetail(AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
			}
		}
		else {
			instrumentDto.setName("EMI");
			instrumentDto.setLogoUrl(LogoUtility
				.defaultLogo(PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem())));
		}
		debitInstruments.add(instrumentDto);
	}

	/**
	 * Handles the LOYALTY_POINT case for the setData method.
	 */
	private void handleLoyaltyPointCase(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final Map<String, String> refIds, final List<InstrumentDto> debitInstruments) {
		if (participant.getBankData() != null
				&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankTxnId())) {
			refIds.put(participant.getBankData().getBankTxnId(), participant.getBankData().getBankTxnId());
		}
		instrumentDto.setNarration("FROM");
		if (participant.getBankData() != null
				&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankName())) {
			if (LOYALTY_POINT.equals(participant.getBankData().getBankName())) {
				instrumentDto.setName("Loyalty Points");
			}
			else {
				instrumentDto.setName(participant.getBankData().getBankName());
			}
		}
		else {
			instrumentDto.setName("Loyalty Points");
		}
		instrumentDto.setLogoUrl(LogoUtility.getLogo(LOYALTY_POINT, LogoType.OTHER));
		debitInstruments.add(instrumentDto);
	}

	/**
	 * Handles the BANK_MANDATE case for the setData method.
	 */
	private void handleBankMandateCase(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final Map<String, String> refIds, final List<InstrumentDto> debitInstruments) {
		if (participant.getBankData() != null) {
			if (org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankTxnId())) {
				refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
			}
			instrumentDto.setName(participant.getBankData().getBankName());
			instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
					participant.getBankData().getBankName()));
			if (org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
				instrumentDto
					.setInstrumentDetail(AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
			}
		}
		else {
			instrumentDto.setName("Bank Mandate");
			instrumentDto.setLogoUrl(LogoUtility
				.defaultLogo(PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem())));
		}
		instrumentDto.setNarration("FROM");
		debitInstruments.add(instrumentDto);
	}

	/**
	 * Handles the PAYTM_CASH case for the setData method.
	 */
	private void handlePaytmCashCase(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final Map<String, String> refIds, final List<InstrumentDto> debitInstruments) {
		instrumentDto.setNarration("FROM");
		instrumentDto.setName("Paytm Cash");
		instrumentDto.setLogoUrl(LogoUtility.getLogo("PAYTM_CASH_LOGO", LogoType.OTHER));
		if (org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankTxnId())) {
			refIds.put(TXN_ID, participant.getContextMap().get("paytm_cash_txn_id"));
		}
		debitInstruments.add(instrumentDto);
	}

	/**
	 * Handles the EMI_DEBIT_CARD case for the setData method.
	 */
	private void handleEmiDebitCardCase(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final Map<String, String> refIds, final List<InstrumentDto> debitInstruments) {
		if (participant.getBankData() != null) {
			if (org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankTxnId())) {
				refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
			}
			instrumentDto.setName(participant.getBankData().getBankName() + " EMI");
			instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
					participant.getBankData().getBankName()));
			if (org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
				instrumentDto
					.setInstrumentDetail(AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
			}
		}
		else {
			instrumentDto.setName("EMI Debit Card");
			instrumentDto.setLogoUrl(LogoUtility
				.defaultLogo(PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem())));
		}
		instrumentDto.setNarration("FROM");
		debitInstruments.add(instrumentDto);
	}

	/**
	 * Handles the NET_BANKING case for the setData method.
	 */
	private void handleNetBankingCase(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final Map<String, String> refIds, final List<InstrumentDto> debitInstruments) {
		if (participant.getBankData() != null) {
			if (org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankTxnId())) {
				refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
			}
			instrumentDto.setName(participant.getBankData().getBankName());
			instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
					participant.getBankData().getBankName()));
			if (org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
				instrumentDto
					.setInstrumentDetail(AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
			}
		}
		else {
			instrumentDto.setName("Net Banking");
			instrumentDto.setLogoUrl(LogoUtility
				.defaultLogo(PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem())));
		}
		instrumentDto.setNarration("FROM");
		debitInstruments.add(instrumentDto);
	}

	private void setUpiData(final InstrumentDto instrumentDto,
			final List<TransformedTransactionHistoryDetail> detailList, final TransformedParticipant participant,
			final List<InstrumentDto> debitInstruments, final Map<String, String> refIds) {
		TransformedTransactionHistoryDetail txn = null;

		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (TransactionSource.UPI.getTransactionSourceKey().equals(detail.getStreamSource())
					&& participant.getPaymentTxnId().equalsIgnoreCase(detail.getSourceTxnId())) {
				txn = detail;
			}
		}

		if (txn == null) {
			instrumentDto.setNarration(FROM);
			if (participant.getUpiData() != null
					&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
				instrumentDto.setName(participant.getUpiData().getVpa());
			}

			setRrn(refIds, participant);
			instrumentDto.setLogoUrl(LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER));
			debitInstruments.add(instrumentDto);
			return;
		}

		for (TransformedParticipant transformedParticipant : txn.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey()
				.equals(transformedParticipant.getTxnIndicator())) {
				if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(transformedParticipant.getPaymentSystem())) {
					InstrumentDto instrumentDto1 = new InstrumentDto();
					instrumentDto1.setNarration("From Your");
					instrumentDto1.setAmount(Currency.getCurrencyAmountInHigherDenomination(
							transformedParticipant.getAmount(), transformedParticipant.getCurrency()));
					instrumentDto1.setCurrency(Currency.getCurrencyByKey(transformedParticipant.getCurrency()));
					if (UpiLiteUtility.isUpiLiteTxn(txn)) {
						UpiLiteViewUtility.setSecondInstrumentForDetailResponse(instrumentDto1, P2M);
					}
					else if (Utility.isUpiViaCcTxn(txn, transformedParticipant)) {
						setInstrumentForUpiViaCc(transformedParticipant, instrumentDto1);
					}
					else if (Objects.nonNull(participant.getBankData())
							&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getBankName())
							&& org.apache.commons.lang3.StringUtils.isNotBlank(participant.getBankData().getIfsc())
							&& org.apache.commons.lang3.StringUtils
								.isNotBlank(participant.getBankData().getAccNumber())) {
						// Todo: PTH-36 should we require handling for user bank data
						// details ?
						instrumentDto1.setInstrumentDetail(
								AC_NO + getMaskedAccountNumber(transformedParticipant.getBankData().getAccNumber()));
						instrumentDto1
							.setLogoUrl(LogoUtility.getBankLogo(transformedParticipant.getBankData().getIfsc(),
									transformedParticipant.getBankData().getBankName()));
						instrumentDto1.setName(transformedParticipant.getBankData().getBankName());
					}
					instrumentDto1.setInstrumentStatus(transformedParticipant.getStatus());
					setRrn(refIds, participant);
					DetailApiUtil.setParticipantInfo(txn, transformedParticipant, instrumentDto1,
							buildDetailApiResponseFor(), false);
					debitInstruments.add(instrumentDto1);
				}
				else if (PaymentSystemEnum.WALLET.getPaymentSystemKey()
					.equals(transformedParticipant.getPaymentSystem())) {
					// Wallet InterOp Case if merging not happened then UPI Doc
					// PaymentSystem would be Wallet
					setWalletData(instrumentDto, detailList, participant, debitInstruments, refIds, txn);
				}
			}
		}
	}

	private void setRrn(final Map<String, String> refIds, final TransformedParticipant participant) {
		String rrn = GenericUtilityExtension.getRrn(participant);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(rrn)) {
			refIds.put(UPI_REFERENCE_NO, rrn);
		}
	}

	private void setWalletData(final InstrumentDto instrumentDto,
			final List<TransformedTransactionHistoryDetail> detailList, final TransformedParticipant walletParticipant,
			final List<InstrumentDto> debitInstruments, final Map<String, String> refIds,
			final TransformedTransactionHistoryDetail parentTxn) {

		// Todo: need to handle for partial merged doc in hybrid txns
		boolean isMergedDoc = walletParticipant.getContextMap() != null
				&& walletParticipant.getContextMap().containsKey(IS_ADDED_PARTICIPANT_BY_MERGING);
		if (Boolean.TRUE.equals(isMergedDoc)) {
			createWalletInstrumentDto(debitInstruments, parentTxn, walletParticipant);
			refIds.put(WALLET_TXN_ID, walletParticipant.getPaymentTxnId());
		}
		else {
			TransformedTransactionHistoryDetail txn = null;

			for (TransformedTransactionHistoryDetail detail : detailList) {
				if (TransactionSource.WALLET.getTransactionSourceKey().equals(detail.getStreamSource())) {
					txn = detail;
				}
			}

			if (txn == null) {
				if (walletParticipant.getWalletData() != null) {
					instrumentDto.setName(
							WalletTypesEnum.getWalletDisplayName(walletParticipant.getWalletData().getWalletType()));
					instrumentDto.setLogoUrl(LogoUtility.getLogo(instrumentDto.getName(), LogoType.WALLET_ICON));
				}
				else {
					instrumentDto.setName(WalletTypesEnum.SCLW.getDisplayName());
					instrumentDto.setLogoUrl(LogoUtility.getLogo(instrumentDto.getName(), LogoType.WALLET_ICON));
				}
				debitInstruments.add(instrumentDto);
				return;
			}

			for (TransformedParticipant transformedParticipant : txn.getParticipants()) {
				if (TransactionIndicator.DEBIT.getTransactionIndicatorKey()
					.equals(transformedParticipant.getTxnIndicator())) {
					createWalletInstrumentDto(debitInstruments, txn, transformedParticipant);
				}
			}
			refIds.put(WALLET_TXN_ID, txn.getTxnId());
		}
	}

	private void createWalletInstrumentDto(final List<InstrumentDto> debitInstruments,
			final TransformedTransactionHistoryDetail txn, final TransformedParticipant transformedParticipant) {
		InstrumentDto instrumentDto1 = new InstrumentDto();
		instrumentDto1.setNarration("From Your");
		instrumentDto1.setAmount(Currency.getCurrencyAmountInHigherDenomination(transformedParticipant.getAmount(),
				transformedParticipant.getCurrency()));
		instrumentDto1.setCurrency(Currency.getCurrencyByKey(transformedParticipant.getCurrency()));
		if (transformedParticipant.getWalletData() != null) {
			instrumentDto1
				.setName(WalletTypesEnum.getWalletDisplayName(transformedParticipant.getWalletData().getWalletType()));
		}
		else {
			instrumentDto1.setName(WalletTypesEnum.SCLW.getDisplayName());
		}
		instrumentDto1.setLogoUrl(LogoUtility.getLogo(instrumentDto1.getName(), LogoType.WALLET_ICON));
		instrumentDto1.setInstrumentStatus(transformedParticipant.getStatus());
		DetailApiUtil.setParticipantInfo(txn, transformedParticipant, instrumentDto1, buildDetailApiResponseFor(),
				false);
		debitInstruments.add(instrumentDto1);
	}

	private void setInstrumentForUpiViaCc(final TransformedParticipant transformedParticipant,
			final InstrumentDto instrumentDto) {
		instrumentDto.setName(GenericUtilityExtension.getInstrumentDetailForUpiViaCc(
				transformedParticipant.getBankData().getBankName(), transformedParticipant.getCardData().getCardNum()));
		instrumentDto.setInstrumentDetail(null);
		instrumentDto.setLogoUrl(LogoUtility.getCardLogo(transformedParticipant.getCardData().getCardNetwork()));
	}

}
