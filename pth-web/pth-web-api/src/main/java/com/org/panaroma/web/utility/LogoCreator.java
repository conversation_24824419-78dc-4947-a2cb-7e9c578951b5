package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.BankDataConstants.XFER_RPT_CODES;
import static com.org.panaroma.commons.constants.WebConstants.BHIM_UPI_NAME;
import static com.org.panaroma.commons.constants.WebConstants.CASHBACK_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT;
import static com.org.panaroma.commons.constants.WebConstants.IPO_MANDATE_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_BANK_ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_DEFAULT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.RECURRING_MANDATE_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.P2M_REPORT_CODES;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_PAYMENTS_BANK;
import static com.org.panaroma.commons.constants.WebConstants.PPBL_CONSTANT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.PPBL_TXN_TYPES_FOR_COMMON_LOGOS;
import static com.org.panaroma.commons.constants.WebConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.WebConstants.UPI_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_MERCHANT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UTH_CATEGORY_OTHERS;
import static com.org.panaroma.commons.enums.LogoType.OTHER;
import static com.org.panaroma.commons.enums.LogoType.TRANSACTION_CATEGORY_ICON;
import static com.org.panaroma.commons.utils.LogoUtility.getUthCategoryLogo;
import static com.org.panaroma.commons.utils.Utility.getSelfParticipant;
import static com.org.panaroma.commons.utils.Utility.getVpaHandle;
import static com.org.panaroma.web.utility.configurablePropertyUtility.InternalCache.getPspInfo;

import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.constants.WebConstants.LogoPurpose;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.PspInfo;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoOrderTypeEnum;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.utils.BeanUtil;
import com.org.panaroma.commons.utils.IfscUtility;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.web.config.BankConfig;
import com.org.panaroma.web.config.BankDataConfigEnum;
import com.org.panaroma.web.config.RptCodeConfig;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@Log4j2
public class LogoCreator {

	private static HashSet<String> p2mReportCodeSet = null;

	static {
		p2mReportCodeSet = new HashSet<>(Arrays.asList(P2M_REPORT_CODES.split("\\|")));
	}

	/*
	 * This Class will define which flow to choose for logo(user/seconduser) This Class
	 * will have tcnType specific functions to fetch logo Structure of functions is:
	 * function will have params: participant, tthd, isForlogoOrder, user/SecondUser
	 * Return Type will be List<Logos>
	 */
	public static List<Logo> getCashBackLogosForUser(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail tthd, final boolean isForLogoOrder) {
		List<Logo> logos = new ArrayList<>();
		String logoUrl = null;
		String instrumentName = null;
		if (!isForLogoOrder) {
			PaymentSystemEnum paymentSystem = PaymentSystemEnum
				.getPaymentSystemEnumByKey(participant.getPaymentSystem());
			if (paymentSystem == null) {
				log.error("Payment system is Null for Cashback detail response, participant entityId:{} , txnId: {}",
						participant.getEntityId(), tthd.getTxnId());
				throw new RuntimeException("Payment system is Null while fetching Cashback LogoUrl");
			}
			switch (paymentSystem) {
				case WALLET:
					logoUrl = LogoUtility.getLogo(
							WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()),
							LogoType.WALLET_ICON);
					instrumentName = WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType());
					break;
				case BANK:
					if (!Objects.isNull(participant.getBankData())) {
						logoUrl = LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
								participant.getBankData().getBankName());

						if (StringUtils.isNotBlank(participant.getBankData().getBankName())) {
							instrumentName = participant.getBankData().getBankName();
						}
						else {
							instrumentName = StringUtils.isNotBlank(participant.getBankData().getIfsc())
									? IfscUtility.getBank(participant.getBankData().getIfsc()) : LINKED_BANK_ACCOUNT;
						}
					}
					else if (participant.getUpiData() != null) {
						logoUrl = LogoUtility.getLogo(UPI_LOGO, OTHER);
						instrumentName = BHIM_UPI_NAME;
					}
					else {
						// set logo url as default bank logo.
						logoUrl = LogoUtility.getBankLogo(null, null);
						instrumentName = LINKED_BANK_ACCOUNT;
					}
					break;
				case UPI:
					if (participant.getBankData() != null) {
						// Logo will be the bank logo linked with UPI and if there is no
						// bank logo then logo will be the default bhim UPI logo
						logoUrl = LogoUtility.getUpiBankLogo(participant.getBankData().getIfsc(),
								participant.getBankData().getBankName());

						if (StringUtils.isNotBlank(participant.getBankData().getBankName())) {
							instrumentName = participant.getBankData().getBankName();
						}
						else {
							instrumentName = StringUtils.isNotBlank(participant.getBankData().getIfsc())
									? IfscUtility.getBank(participant.getBankData().getIfsc()) : BHIM_UPI_NAME;
						}
					}
					else {
						logoUrl = LogoUtility.getLogo(UPI_LOGO, OTHER);
						instrumentName = BHIM_UPI_NAME;
					}
					break;
				default:
					log.error(
							"unexpected payment system for Cashback detail response, participant entityId:{} , txnId: {}",
							participant.getEntityId(), tthd.getTxnId());
					throw new RuntimeException("Unexpected payment system while fetching Cashback LogoUrl");
			}
			Logo logo = new Logo(LogoOrderTypeEnum.URL, logoUrl);
			logo.setLogoName(instrumentName);
			logos.add(logo);
		}
		return logos;
	}

	public static String getBankLogoFor3PAppTxn(final TransformedTransactionHistoryDetail tthd) {
		TransformedParticipant selfParticipant = getSelfParticipant(tthd);
		String handle = getVpaHandle(selfParticipant);
		PspInfo pspInfo = getPspInfo(handle);
		if (ObjectUtils.isNotEmpty(pspInfo)) {
			return pspInfo.getPspIconLogo();
		}

		return LogoUtility.getLogoUrl(DEFAULT);
	}

	public static List<Logo> getCashBackLogosForSecondUser(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail tthd, final boolean isForLogoOrder) {
		List<Logo> logos = new ArrayList<>();
		String logo = null;
		if (!isForLogoOrder) {
			logo = LogoUtility.getLogo(CASHBACK_LOGO, TRANSACTION_CATEGORY_ICON);
			logos.add(new Logo(LogoOrderTypeEnum.URL, logo));
		}
		return logos;
	}

	public static List<Logo> getIpoMandateLogosForSecondUser(final TransformedParticipant participant,
			final boolean isForLogoOrder) {
		List<Logo> logos = new ArrayList<>();
		String logo = null;
		if (isForLogoOrder && Objects.nonNull(participant.getLogoUrl())) {
			logos.add(new Logo(LogoOrderTypeEnum.URL, participant.getLogoUrl()));
		}
		logo = LogoUtility.getLogo(IPO_MANDATE_LOGO, OTHER);
		logos.add(new Logo(LogoOrderTypeEnum.URL, logo));
		return logos;
	}

	public static List<Logo> getRecurringMandateLogosForSecondUser(final TransformedParticipant participant,
			final boolean isForLogoOrder) {
		List<Logo> logos = new ArrayList<>();
		String logo = null;
		if (isForLogoOrder && Objects.nonNull(participant.getLogoUrl())) {
			logos.add(new Logo(LogoOrderTypeEnum.URL, participant.getLogoUrl()));
		}
		logo = LogoUtility.getLogo(RECURRING_MANDATE_LOGO, OTHER);
		logos.add(new Logo(LogoOrderTypeEnum.URL, logo));
		return logos;
	}

	public static List<Logo> getPpblLogosForUser(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail tthd, final boolean isForLogoOrder) {
		if (!PPBL_TXN_TYPES_FOR_COMMON_LOGOS
			.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()))
				|| !TransactionSource.PPBL.getTransactionSourceKey().equals(tthd.getStreamSource())) {
			return null;
		}

		String reportCode = null;
		if (participant != null && participant.getContextMap() != null) {
			reportCode = participant.getContextMap().get(REPORT_CODE);
		}
		else {
			log.error("No User Participant/ContextMap for PPBL Transaction for txnType : {}", tthd.getTxnId());
			return null;
		}

		boolean p2mCodes = isForSplP2mRptCodes(reportCode);

		List<Logo> logos = new ArrayList<>();
		String logoUrl = null;

		if (!isForLogoOrder) {
			if (p2mCodes) {
				logoUrl = LogoUtility.formLogoUrl("p2m.png", TRANSACTION_CATEGORY_ICON);
			}
			else {
				logoUrl = LogoUtility.getLogo(PPBL_CONSTANT_LOGO, TRANSACTION_CATEGORY_ICON);
			}
			Logo logo = new Logo(LogoOrderTypeEnum.URL, logoUrl);
			logo.setLogoName(PAYTM_PAYMENTS_BANK);
			logos.add(logo);
		}
		return logos;
	}

	public static List<Logo> getPpblLogosForSecondUser(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail tthd, final boolean isForLogoOrder) {
		if (!PPBL_TXN_TYPES_FOR_COMMON_LOGOS
			.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()))
				|| !TransactionSource.PPBL.getTransactionSourceKey().equals(tthd.getStreamSource())) {
			return null;
		}

		String reportCode = null;
		Map<String, String> contextMap = new HashMap<>();
		for (TransformedParticipant transformedParticipant : tthd.getParticipants()) {
			if (tthd.getTxnIndicator().equals(transformedParticipant.getTxnIndicator())
					&& transformedParticipant.getContextMap() != null) {
				reportCode = transformedParticipant.getContextMap().get(REPORT_CODE);
				contextMap = transformedParticipant.getContextMap();
			}
		}

		BankConfig bankConfig = BeanUtil.getBean(BankConfig.class);
		RptCodeConfig rptCodeConfig = GenericUtility.getReportCodeConfig(bankConfig, reportCode, tthd, contextMap);

		List<Logo> logos = new ArrayList<>();
		String logo = null;
		if (!isForLogoOrder) {
			logo = BankDataConfigEnum.getStaticKeyFroDynamicKey(rptCodeConfig.getImage(), contextMap,
					bankConfig.getBankStaticPropertiesConfig(), tthd);
		}
		logos.add(new Logo(LogoOrderTypeEnum.URL, logo));
		return logos;
	}

	public static boolean isForSplP2mRptCodes(final String reportCode) {
		if (p2mReportCodeSet.contains(reportCode)) {
			return true;
		}
		else {
			return false;
		}
	}

	public static List<Logo> getP2mLogosForSecondUser(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn, final boolean isForLogoOrder) {
		List<Logo> logoOrder = new ArrayList<>();

		if (isForLogoOrder) {
			if (TransactionTypeEnum.P2M_INTERNATIONAL.getTransactionTypeKey().equals(txn.getMainTxnType())
					|| TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL.getTransactionTypeKey()
						.equals(txn.getMainTxnType())) {
				// Set Name Initials logo
				String nameInitial = getNameInitials(participant.getName());
				if (StringUtils.isNotBlank(nameInitial)) {
					logoOrder.add(new Logo(LogoOrderTypeEnum.STRING, nameInitial));
				}

				String logoForP2mInt = LogoUtility.getLogo(WebConstants.P2M_INTERNATIONAL_LOGO,
						TRANSACTION_CATEGORY_ICON);
				logoOrder.add(new Logo(LogoOrderTypeEnum.URL, logoForP2mInt));
			}
			else if ((TransactionSource.UPI.getTransactionSourceKey().equals(txn.getSourceSystem())
					|| (TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
							&& Objects.isNull(txn.getSourceSystem())))
					&& Objects.nonNull(participant.getUpiData())) {
				Logo logo = LogoUtility.getUrlTypeLogo(participant.getUpiData().getVpa(), UPI_MERCHANT_LOGO);
				logoOrder.add(logo);
			}

			getLogoOrderForMerchantEntity(participant, txn, logoOrder);
		}
		return logoOrder;
	}

	/**
	 * Common logo order for all the cases in which merchant entity is present
	 */
	public static void getLogoOrderForMerchantEntity(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn, final List<Logo> logoOrder) {

		if (Objects.nonNull(participant.getLogoUrl())) {
			logoOrder.add(new Logo(LogoOrderTypeEnum.URL, participant.getLogoUrl()));
		}

		if (Objects.nonNull(participant.getMerchantData())
				&& StringUtils.isNotBlank(participant.getMerchantData().getUthCategory())) {

			String uthCategoryId = participant.getMerchantData().getUthCategory();

			// Added this check as in case of UthCategory as OTHERS, getting the
			// categoryId
			// with respect to OTHERS category enum
			if (UTH_CATEGORY_OTHERS.equalsIgnoreCase(uthCategoryId)) {
				uthCategoryId = UthCategoryEnum.OTHERS.getUthCategoryId();
			}

			UthCategoryEnum uthCategoryEnum = UthCategoryEnum.getEnumFromUthCategoryId(uthCategoryId);
			String categoryLogo = getUthCategoryLogo(uthCategoryEnum);

			if (categoryLogo != null) {
				logoOrder.add(new Logo(LogoOrderTypeEnum.URL, categoryLogo));
			}
		}

		// Setting default merchant logo
		String merchantLogo = LogoUtility.getLogo(MERCHANT_DEFAULT_LOGO, TRANSACTION_CATEGORY_ICON);
		logoOrder.add(new Logo(LogoOrderTypeEnum.URL, merchantLogo));
	}

	// method for getting logoOrder of phoneNumber and nameInitials
	public static List<Logo> getLogoOrderDetails(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn, final Map<String, UserDetails> userIdImageUrlMapFromCache) {
		// return null if entity type is MERCHANT
		if (Objects.isNull(participant)
				|| (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())
						&& !GenericUtilityExtension.isInternationalRemittance(txn))) {
			return null;
		}
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getMainTxnType());
		List<Logo> logos = new ArrayList<>();

		// UTH - 77 : set BHIM-UPI.png logo for first Instrument
		if (UtilityExtension.isp2pOutwardReversalUsingPpbl(txn)) {
			String bhimUpiLogo = LogoUtility.getLogo(UPI_LOGO, OTHER);
			logos.add(new Logo(LogoOrderTypeEnum.URL, bhimUpiLogo));
			return logos;
		}

		// setting user image url in non ppbl cases
		setUserImageInNonPpblTxns(participant, txn, userIdImageUrlMapFromCache, txnType, logos);

		// Setting Phone number
		String phoneNumber = Utility.getValidPhoneNumber(participant, txnType, true);
		if (StringUtils.isNotEmpty(phoneNumber)) {
			logos.add(new Logo(LogoOrderTypeEnum.PHONENUMBER, phoneNumber));
		}
		// Setting name initials
		String nameInitials = null;
		// Special handling of bank Txns for name Initials
		if (TransactionTypeEnum.PPBL_TRANSACTION.equals(txnType)) {
			// special handling for setting logoOrder for second party in xfer cases
			setLogoOrderForXfer(participant, txn, userIdImageUrlMapFromCache, logos);
			String name = Utility.getSecUserNameForBankTxn(participant);
			nameInitials = getNameInitials(name);
		}
		else {
			nameInitials = getNameInitials(participant.getName());
		}
		if (StringUtils.isNotBlank(nameInitials)) {
			if (!StringUtils.isAlpha(nameInitials)) {
				log.debug("NameInitials has special or numerical characters NameInitials: {}, TxnId: {}", nameInitials,
						txn.getTxnId());
			}
			logos.add(new Logo(LogoOrderTypeEnum.STRING, nameInitials));
		}
		return logos.isEmpty() ? null : logos;
	}

	private static void setUserImageInNonPpblTxns(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn, final Map<String, UserDetails> userIdImageUrlMapFromCache,
			final TransactionTypeEnum txnType, final List<Logo> logos) {
		if (!TransactionTypeEnum.PPBL_TRANSACTION.equals(txnType)
				&& StringUtils.isNotBlank(txn.getSecondUserImageUrl())) {
			setUserImageInLogoOrder(logos, LogoOrderTypeEnum.URL, txn.getSecondUserImageUrl());
		}
		else if (!TransactionTypeEnum.PPBL_TRANSACTION.equals(txnType) && userIdImageUrlMapFromCache != null
				&& participant.getEntityId() != null
				&& userIdImageUrlMapFromCache.get(participant.getEntityId()) != null
				&& userIdImageUrlMapFromCache.get(participant.getEntityId()).getUserImageUrl() != null) {
			setUserImageInLogoOrder(logos, LogoOrderTypeEnum.URL,
					userIdImageUrlMapFromCache.get(participant.getEntityId()).getUserImageUrl());
		}
	}

	private static void setLogoOrderForXfer(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn, final Map<String, UserDetails> userIdImageUrlMapFromCache,
			final List<Logo> logos) {
		if (participant.getContextMap() != null && participant.getContextMap().get(REPORT_CODE) != null
				&& XFER_RPT_CODES.contains(participant.getContextMap().get(REPORT_CODE))) {
			if (StringUtils.isNotBlank(txn.getSecondUserImageUrl())) {
				setUserImageInLogoOrder(logos, LogoOrderTypeEnum.URL, txn.getSecondUserImageUrl());
				return;
			}
			if (userIdImageUrlMapFromCache != null) {
				TransformedParticipant otherParticipant = getOtherParticipant(participant, txn);
				if (otherParticipant != null) {
					setUserImageFromCacheMap(userIdImageUrlMapFromCache, logos, otherParticipant);
				}
			}
		}
	}

	private static TransformedParticipant getOtherParticipant(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn) {
		return txn.getParticipants()
			.stream()
			.filter(p -> !participant.getEntityId().equals(p.getEntityId()))
			.findFirst()
			.orElse(null);
	}

	private static void setUserImageFromCacheMap(final Map<String, UserDetails> userIdImageUrlMapFromCache,
			final List<Logo> logos, final TransformedParticipant txnParticipant) {
		UserDetails userDetails = userIdImageUrlMapFromCache.get(txnParticipant.getEntityId());
		if (userDetails != null && userDetails.getUserImageUrl() != null) {
			setUserImageInLogoOrder(logos, LogoOrderTypeEnum.URL, userDetails.getUserImageUrl());
		}
	}

	private static void setUserImageInLogoOrder(final List<Logo> logos, final LogoOrderTypeEnum logoOrderTypeEnum,
			final String secondUserImageUrl) {
		logos.add(new Logo(logoOrderTypeEnum, secondUserImageUrl, LogoPurpose.OAUTH_IMAGE));
	}

	// Method to get initials of Username
	public static String getNameInitials(final String name) {
		String nameInitials = null;
		String userName = name;

		if (StringUtils.isBlank(userName)) {
			return nameInitials;
		}
		// normalizing the spaces of string
		userName = StringUtils.normalizeSpace(userName);

		String[] splittedName = userName.split("\\s+");
		StringBuilder sb = new StringBuilder();
		sb.append(Character.toUpperCase(splittedName[0].charAt(0)));
		if (splittedName.length > 1) {
			sb.append(Character.toUpperCase(splittedName[splittedName.length - 1].charAt(0)));
		}
		nameInitials = sb.toString();
		return nameInitials;
	}

}
