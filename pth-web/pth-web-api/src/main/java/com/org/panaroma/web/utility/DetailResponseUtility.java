package com.org.panaroma.web.utility;

import com.org.panaroma.commons.constants.CommonsConstants;
import com.org.panaroma.commons.constants.LocaleMsgConstants;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoOrderTypeEnum;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.dto.NameDetails;
import com.org.panaroma.web.dto.NameOrderTypeEnum;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.EntityDetails;
import com.org.panaroma.web.dto.detailAPI.detailV3.InstrumentDetailsDtoV3;
import com.org.panaroma.web.dto.detailAPI.detailV3.InstrumentDtoV3;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.org.panaroma.commons.constants.Constants.BANK_NAME_FOR_3P_APP_RECEIVER;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT_USER_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_BANK;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_DEFAULT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_SYSTEM_TYPE_GV;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_MERCHANT;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_USER;
import static com.org.panaroma.commons.constants.WebConstants.TO_ACCOUNT_MODE;
import static com.org.panaroma.commons.constants.WebConstants.UPI_ID;
import static com.org.panaroma.commons.constants.WebConstants.USER;
import static com.org.panaroma.commons.constants.WebConstants.GIFT_VOUCHER;
import static com.org.panaroma.web.utility.LogoCreator.getBankLogoFor3PAppTxn;

@Component
@Log4j2
public class DetailResponseUtility {

	public static void populateVpaDetailsOnInstrumentDetailMap(final InstrumentDtoV3 instrumentDto,
			final TransformedParticipant participant, List<String> paytmTpapHandlesList,
			TransformedTransactionHistoryDetail tthd) {
		List<InstrumentDetailsDtoV3> instrumentDetailMap = instrumentDto.getInstrumentDetailsMap();
		if (Objects.isNull(instrumentDetailMap)) {
			instrumentDetailMap = new ArrayList<>();
		}

		// Set UPI Data on detailInstrument Map
		if (Objects.nonNull(participant) && Objects.nonNull(participant.getUpiData())
				&& StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
			if (CollectionUtils.isNotEmpty(paytmTpapHandlesList)
					&& paytmTpapHandlesList.stream()
						.anyMatch(entry -> participant.getUpiData().getVpa().endsWith(entry))
					|| TransactionTypeEnum.P2P_INWARD_3P_APP.getTransactionTypeKey().equals(tthd.getMainTxnType())) {
				String rawVpa = participant.getUpiData().getVpa();
				InstrumentDetailsDtoV3 vpa = new InstrumentDetailsDtoV3(UPI_ID, rawVpa, null, null);
				instrumentDetailMap.add(vpa);
			}
			instrumentDto.setInstrumentDetailsMap(instrumentDetailMap);
		}
	}

	/**
	 * Populates bank details on instrument detail map with payment system type
	 * optimization
	 */
	public static void populateBankDetailsOnInstrumentDetailMap(final InstrumentDtoV3 instrumentDto,
			final TransformedParticipant participant, final TransformedTransactionHistoryDetail listingVisibleTxn,
			final String paymentSystemType) {
		// Early validation - check for null participant first
		if (Objects.isNull(participant)) {
			log.warn("Skipping bank details population due to null participant");
			return;
		}

		// Initialize instrument detail map if needed
		List<InstrumentDetailsDtoV3> instrumentDetailMap = instrumentDto.getInstrumentDetailsMap();
		if (Objects.isNull(instrumentDetailMap)) {
			instrumentDetailMap = new ArrayList<>();
			instrumentDto.setInstrumentDetailsMap(instrumentDetailMap);
		}

		// Handle based on pre-determined payment system type
		if (PAYMENT_SYSTEM_TYPE_GV.equals(paymentSystemType)) {
			addGiftVoucherDetails(instrumentDetailMap, instrumentDto);
			return;
		}

		// Validate bank data availability for non-GV transactions
		if (!participantHasBankData(participant)) {
			log.warn("Skipping bank details population due to missing bank data");
			return;
		}

		// Process based on transaction type
		if (Utility.isUpiViaCcTxn(listingVisibleTxn)) {
			setUpiCreditCardDetailsToInstrumentMap(instrumentDto, participant, instrumentDetailMap);
		}
		else {
			setBankDetailsToInstrumentMap(instrumentDto, participant, instrumentDetailMap, listingVisibleTxn);
		}
	}

	/**
	 * Adds Gift Voucher details to the instrument detail map
	 */
	private static void addGiftVoucherDetails(final List<InstrumentDetailsDtoV3> instrumentDetailMap,
			final InstrumentDtoV3 instrumentDto) {
		InstrumentDetailsDtoV3 gvDetails = new InstrumentDetailsDtoV3(CommonsConstants.SPACE,
				LocalizedDataCacheService.getLocalizedValue(LocaleMsgConstants.DETAIL_INSTRUMENT_NAME_GIFT_CARD));
		gvDetails.setLogoUrl(LogoUtility.getLogo(GIFT_VOUCHER, LogoType.GIFT_VOUCHER_ICON));
		instrumentDetailMap.add(gvDetails);
		// Set the updated list back to the DTO
		instrumentDto.setInstrumentDetailsMap(instrumentDetailMap);
	}

	/**
	 * Validates if participant has required bank data
	 */
	private static boolean participantHasBankData(final TransformedParticipant participant) {
		return Objects.nonNull(participant.getBankData())
				&& StringUtils.isNotBlank(participant.getBankData().getBankName())
				&& StringUtils.isNotBlank(participant.getBankData().getAccNumber());
	}

	/**
	 * Processes a regular bank transaction by extracting and formatting bank details.
	 */
	private static void setBankDetailsToInstrumentMap(final InstrumentDtoV3 instrumentDto,
			final TransformedParticipant participant, final List<InstrumentDetailsDtoV3> instrumentDetailMap,
			TransformedTransactionHistoryDetail tthd) {
		// Additional check for IFSC code which is specific to bank transactions
		if (StringUtils.isBlank(participant.getBankData().getIfsc())) {
			log.debug("Missing IFSC code for bank transaction");
			return;
		}

		String bankName = participant.getBankData().getBankName();
		String bankAccNum = participant.getBankData().getAccNumber();
		String bankIfsc = participant.getBankData().getIfsc();

		// Format bank name and account number
		String bankNameAccNo = getBankNameAccNum(null, bankName, bankAccNum);

		InstrumentDetailsDtoV3 bankDetails;
		String logo;

		if (TransactionTypeEnum.P2P_INWARD_3P_APP.getTransactionTypeKey().equals(tthd.getMainTxnType())
				&& TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
			bankDetails = new InstrumentDetailsDtoV3(LINKED_BANK, BANK_NAME_FOR_3P_APP_RECEIVER);
			logo = getBankLogoFor3PAppTxn(tthd);
		}

		else {
			bankDetails = new InstrumentDetailsDtoV3(LINKED_BANK, bankNameAccNo);
			logo = LogoUtility.getBankLogoOrNull(bankIfsc, bankName);
		}
		// Set bank logo

		if (StringUtils.isNotBlank(logo)) {
			bankDetails.setLogoUrl(logo);
		}

		// Update instrument detail map
		instrumentDetailMap.add(bankDetails);
		// Ensure the updated list is set back to the DTO
		instrumentDto.setInstrumentDetailsMap(instrumentDetailMap);
	}

	/**
	 * Handles UPI via Credit Card transaction by setting appropriate details and card
	 * logo. Uses bank details if available, otherwise falls back to card data.
	 */
	private static void setUpiCreditCardDetailsToInstrumentMap(final InstrumentDtoV3 instrumentDto,
			final TransformedParticipant participant, final List<InstrumentDetailsDtoV3> instrumentDetailMap) {
		String bankName = participant.getBankData().getBankName();
		String bankAccNum = participant.getBankData().getAccNumber();
		String bankNameAccNo = getBankNameAccNum(null, bankName, bankAccNum);

		InstrumentDetailsDtoV3 bankDetails = new InstrumentDetailsDtoV3(LINKED_BANK, bankNameAccNo);

		// Pass empty string if cardData is null - getCardLogo handles this internally
		String cardNetwork = (participant.getCardData() != null) ? participant.getCardData().getCardNetwork() : "";
		String logo = LogoUtility.getCardLogo(cardNetwork);

		if (StringUtils.isNotBlank(logo)) {
			bankDetails.setLogoUrl(logo);
		}

		instrumentDetailMap.add(bankDetails);

		// Set the updated list back to the DTO
		instrumentDto.setInstrumentDetailsMap(instrumentDetailMap);
	}

	public static String getBankNameAccNum(final String txnInitiationMode, final String bankName,
			final String bankAccNum) {
		if (bankAccNum != null) {
			if (Objects.nonNull(txnInitiationMode) && TO_ACCOUNT_MODE.equalsIgnoreCase(txnInitiationMode)) {
				return String.format("%s - %s", bankName, bankAccNum);
			}
			else {
				return String.format("%s - %s", bankName, GenericUtilityExtension.getLastNcharacters(bankAccNum, 4));
			}
		}
		else {
			return bankName;
		}
	}

	public static void handleNullNameForInstrument(final InstrumentDtoV3 instrumentDto,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (Objects.isNull(instrumentDto) || Objects.isNull(instrumentDto.getParticipantInfo())
				|| Objects.isNull(instrumentDto.getParticipantInfo().getParticipant())
				|| StringUtils.isNotBlank(instrumentDto.getParticipantInfo().getParticipant().getName())) {
			return;
		}
		// Code will work in case we have got name - null or empty.
		TransformedParticipant participant = instrumentDto.getParticipantInfo().getParticipant();
		EntityDetails entityDetails = instrumentDto.getEntityDetails();
		List<NameDetails> nameOrder = new ArrayList<>();
		List<Logo> logoOrder = new ArrayList<>();

		if (Objects.nonNull(entityDetails)) {
			if (Objects.nonNull(entityDetails.getNameOrder())) {
				nameOrder = entityDetails.getNameOrder();
			}
			if (Objects.nonNull(entityDetails.getLogoOrder())) {
				logoOrder = entityDetails.getLogoOrder();
			}
		}

		if (isP2MTypeTxnType(listingVisibleTxn)) {
			if (StringUtils.isNotBlank(participant.getEntityId())) {
				nameOrder.add(new NameDetails(NameOrderTypeEnum.STRING, PAYTM_MERCHANT));
			}
			else {
				nameOrder.add(new NameDetails(NameOrderTypeEnum.STRING, MERCHANT));
			}

			// Add default merchant Logo.
			logoOrder.add(new Logo(LogoOrderTypeEnum.URL, LogoUtility.getLogo(MERCHANT_DEFAULT_LOGO, LogoType.OTHER)));
		}
		else {
			// Add Default name for p2p/default cases.
			if (StringUtils.isNotBlank(participant.getEntityId())) {
				nameOrder.add(new NameDetails(NameOrderTypeEnum.STRING, PAYTM_USER));
			}
			else {
				nameOrder.add(new NameDetails(NameOrderTypeEnum.STRING, USER));
			}

			// Add default Logo.
			logoOrder.add(new Logo(LogoOrderTypeEnum.URL, LogoUtility.getLogo(DEFAULT_USER_LOGO, LogoType.OTHER)));
		}

		entityDetails.setNameOrder(nameOrder);
		entityDetails.setLogoOrder(logoOrder);
		instrumentDto.setEntityDetails(entityDetails);
	}

	private static boolean isP2MTypeTxnType(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		return Objects.nonNull(listingVisibleTxn) && Objects.nonNull(listingVisibleTxn.getTxnType())
				&& (TransactionTypeEnum.P2M.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
						|| TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
						|| TransactionTypeEnum.CASHBACK_RECEIVED.getTransactionTypeKey()
							.equals(listingVisibleTxn.getTxnType())
						|| TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey()
							.equals(listingVisibleTxn.getTxnType())
						|| TransactionTypeEnum.ONE_TIME_MANDATE.getTransactionTypeKey()
							.equals(listingVisibleTxn.getTxnType())
						|| TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey()
							.equals(listingVisibleTxn.getTxnType())
						|| TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.getTransactionTypeKey()
							.equals(listingVisibleTxn.getTxnType())
						|| TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.getTransactionTypeKey()
							.equals(listingVisibleTxn.getTxnType())
						|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey()
							.equals(listingVisibleTxn.getTxnType()));
	}

}