package com.org.panaroma.web.service.nonMono;

import com.org.panaroma.web.dto.listing.v4.ListingResponseV4;
import com.org.panaroma.web.dto.updates.v2.UpdatesResponseV4;

import java.util.Map;

public interface IRequestResponseModifierInterface {

	public void modifySearchRequest(final Map<String, String> paramMap);

	public void modifySearchResponse(final ListingResponseV4 listingResponseV4);

	public void modifyUpdatesResponse(final UpdatesResponseV4 updatesResponseV4);

}
