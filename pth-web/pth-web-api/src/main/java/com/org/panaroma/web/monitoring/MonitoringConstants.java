package com.org.panaroma.web.monitoring;

public class MonitoringConstants {

	public static final String TXN_HISTORY_APP = "application:TransactionHistory";

	public static final String API_EXECUTION_TIME = "API_EXECUTION_TIME";

	public static final String API_NAME = "api_name";

	public static final String API_URI = "api_uri";

	public static final String API_TYPE = "api_type";

	public static final String API_STATUS_COUNT = "API_STATUS_COUNT";

	public static final String PAYMENT_SYSTEM_COUNT = "PAYMENT_SYSTEM_COUNT";

	public static final String API_RESPONSE_CODE_COUNT = "API_RESPONSE_CODE_COUNT";

	public static final String STATUS = "status";

	public static final String RESPONSE_CODE = "responseCode";

	public static final String EXCEPTION_TYPE = "exception_type";

	public static final String AUTH_TIMEOUT = "auth_timeout";

	public static final String AUTHORIZATION_EXCEPTION = "authorization_exception";

	public static final String ES_HOST = "esHost";

	public static final String OAUTH_DOWNSTREAM_RESPONSE_COUNT = "OAUTH_DOWNSTREAM_RESPONSE_COUNT";

	public static final String PTH_OAUTH_VALIDATION_RESPONSE_COUNT = "PTH_OAUTH_VALIDATION_RESPONSE_COUNT";

	public static final String HTTP_CODE = "httpCode";

	public static final String HTTP_CODE_STR = "httpCodeStr";

	public static final String ERROR_THROWN = "errorThrown";

	public static final String OAUTH_RESPONSE_COUNT = "OAUTH_RESPONSE_COUNT";

	public static final String API_HIT_COUNT = "API_HIT_COUNT";

	public static final String ES_API_HIT_COUNT = "ES_API_HIT_COUNT";

	public static final String SEARCH_API = "SEARCH_API";

	public static final String BG_LISTING = "BG_LISTING";

	public static final String LISTING_FILTER = "LISTING_FILTER";

	public static final String UPDATES_API = "UPDATES_API";

	public static final String RECON_API = "RECON_API";

	public static final String DETAIL_API = "DETAIL_API";

	public static final String DETAIL_API_V2 = "DETAIL_API_V2";

	public static final String DETAIL_API_V3 = "DETAIL_API_V3";

	public static final String METADATA_API = "METADATA_API";

	public static final String DETAIL_API_V2_HIT_COUNT = "DETAIL_API_V2";

	public static final String CST_API = "CST_API";

	public static final String CST_SEARCH_API = "CST_SEARCH_API";

	public static final String S2S = "s2s";

	public static final String S2S_DETAIL = "s2s-detail";

	public static final String S2S_SEARCH = "s2s-search";

	public static final String GLOBAL_EXCEPTION_HANDLER = "GLOBAL_EXCEPTION_HANDLER";

	public static final String OAUTH_SERVICE_USER_API = "OAUTH_SERVICE_USER_API";

	public static final String OAUTH_SERVICE_USER_API_METADATA = "OAUTH_SERVICE_USER_API_METADATA";

	public static final String OAUTH_SERVICE_USER_API_AUTO_COMPLETE = "OAUTH_SERVICE_USER_API_AUTO_COMPLETE";

	public static final String ES_REST_CLIENT_DETAIL_API = "ES_REST_CLIENT_DETAIL_API";

	public static final String ES_REST_CLIENT_SEARCH_API = "ES_REST_CLIENT_SEARCH_API";

	public static final String TXN_HISTORY_LISTING_API = "TXN_HISTORY_LISTING_API";

	public static final String LOCALE = "locale";

	public static final String CACHE_LOCALISED = "cacheLocalised";

	public static final String REQUIRED_LOCALISED = "requiredLocalised";

	public static final String RECORD_DATE_DIFF = "apiLastRecordDateDiff";

	public static final String NO_OF_ES_CALLS = "noOfEsCalls";

	public static final String DETAIL_ORDER_IN_LISTING = "detailOrderInListing";

	public static final String PAGE_NO = "pageNo";

	public static final String DEFAULT_PAGE_NO = "1";

	public static final String CONTAINER_HOST = "containerHost";

	public static final String COLON = ":";

	public static final String COUNT_OF_RECORDS_CONFIGURABLE_PROPERTIES = "COUNT_OF_RECORDS_CONFIGURABLE_PROPERTIES";

	public static final String GROUPING_VIA_DETAILS = "GROUPING_VIA_DETAILS";

	public static final String SEARCH = "search";

	public static final String UPDATES = "updates";

	public static final String DETAIL = "detail";

	public static final String DETAIL_V2 = "detailV2";

	public static final String AUTO_COMPLETE = "autocomplete";

	public static final String TXN_STATUS_WISE_API_HIT_COUNT = "TXN_STATUS_WISE_API_HIT_COUNT";

	public static final String TXN_TYPE = "txnType";

	public static final String EMPTY_RESPONSE = "EMPTY_RESPONSE";

	public static final String DETAIL_V1_URL = "/transaction-history/ext/v1/<sourceContext>/detail";

	public static final String DETAIL_V1_URL_S2S = "/transaction-history/ext/v1/<sourceContext>/detail/s2s";

	public static final String DETAIL_V2_URL = "/transaction-history/ext/v2/<sourceContext>/detail";

	public static final String DETAIL_V3_URL = "/transaction-history/ext/v3/<sourceContext>/detail";

	public static final String DETAIL_V1_WEBV2_URL = "/payments-history-v2/ext/v1/<sourceContext>/detail";

	public static final String DETAIL_V2_WEBV2_URL = "/payments-history-v2/ext/v2/<sourceContext>/detail";

	public static final String DETAIL_V3_WEBV2_URL = "/payments-history-v2/ext/v3/<sourceContext>/detail";

	// Timeline Monitoring Constant
	public static final String UPI_TIMELINE_API = "UPI_TIMELINE_API";

	public static final String UPI_RESPONSE_COUNT = "UPI_RESPONSE_COUNT";

	public static final String TIMELINE_FAILURE_COUNT = "TIMELINE_FAILURE_COUNT";

	public static final String TIMELINE_BAD_RESP_CODE = "TIMELINE_BAD_RESP_CODE";

	public static final String TIMELINE_BAD_STATUS = "TIMELINE_BAD_STATUS";

	public static final String TIMELINE_STATUS_COUNT = "TIMELINE_STATUS_COUNT";

	public static final String SOURCE = "source";

	public static final String ERROR_TYPE = "errorType";

	public static final String TIMELINE_RESPONSE_CODE = "TimelineRespCode";

	public static final String ONLY_TIMELINE = "onlyTimeline";

	public static final String UTH_STATUS = "uthStatus";

	public static final String TIMELINE_STATUS = "timelineStatus";

	public static final String AVAILABLE = "available";

	public static final String COLLAPSED = "collapsed";

	public static final String TIMELINE_SYNC_COUNT = "TIMELINE_SYNC_COUNT";

	public static final String USER_IMAGE_URL_RECORDS_IN_CACHE = "USER_IMAGE_URL_RECORDS_IN_CACHE";

	public static final String USER_IMAGE_URL_RECORDS_MISSING_IN_CACHE = "USER_IMAGE_URL_RECORDS_MISSING_IN_CACHE";

	public static final String SUCCESS = "success";

	public static final String FAILURE = "failure";

	public static final String AUTO_COMPLETE_LIMIT_BREACHED_COUNT = "AUTO_COMPLETE_LIMIT_BREACHED_COUNT";

	public static final String ES_REST_CLIENT_AUTO_COMPLETE_API_SERVICE = "ES_REST_CLIENT_AUTO_COMPLETE_API_SERVICE";

	public static final String AUTO_COMPLETE_LIMIT_BREACHED = "AUTO_COMPLETE_LIMIT_BREACHED ";

	public static final String LIMIT_BREACHED = "limitBreached";

	public static final String LIMIT_TIME = "limitTime";

	public static final String SERVICE_ELASTIC_SEARCH = "serviceEs";

	public static final String COMMA = ",";

	public static final String NINE_ONE_PREFIX_MOB_NO = "91";

	public static final String METRICS_SOURCE_CONTEXT = "sourceContext:";

	public static final String METRICS_API_VERSION = "apiVersion:";

	public static final String SEARCH_TYPE = "searchType";

	public static final String AUTOCOMPLETE_SEARCH_TYPE = "AUTOCOMPLETE_SEARCH_TYPE";

	public static final String DIFF_QUERY_VALUE_FOR_SAME_SUGGESTION_AND_FIELD = "DIFFERENT_QUERY_VALUE_FOR_SAME_SUGGESTION_AND_FIELD";

	public static final String DIFF_QUERY_VALUE_FOR_SAME_SUGGESTION_AND_DIFF_FIELD = "DIFF_QUERY_VALUE_FOR_SAME_SUGGESTION_AND_DIFF_FIELD";

	public static final String SEARCH_ON_MULTIPLE_FIELDS_IDENTIFIER = "MULTIPLE_FIELDS";

	public static final String SEARCH_ON_ALL_FIELDS_IDENTIFIER = "ALL_FIELDS";

	public static final String SEARCH_ON_SINGLE_FIELD_IDENTIFIER = "SINGLE_FIELD";

	public static final String BLANK = "";

	public static final String NOT_AVAILABLE = "N/A";

	public static final String AUTO_COMPLETE_API_NAME = "AUTO_COMPLETE";

	public static final String UPDATES_API_NAME = "UPDATES_API";

	public static final String RETRY_COUNT = "retryCount:";

	public static final String MANAGED_ES = "MANAGED_ES:";

	public static final String RANGE = "range:";

	public static final String FILTER_HIT_COUNT = "FILTER_HIT_COUNT";

	public static final String FILTER_EXECUTION_TIME = "FILTER_EXECUTION_TIME";

	public static final String FILTER_ES_RESPONSE_TIME = "FILTER_ES_RESPONSE_TIME";

	public static final String FILTER_FAILURE_COUNT = "FILTER_FAILURE_COUNT";

	public static final String MONTHS = "Months";

	public static final String CUSTOM_DATE_RANGE = "CustomDateRange";

	public static final String CUSTOM_DATE_RANGE_WITH_MONTHS = "CustomDateRangeWithMonths";

	public static final String INCORRECT_INPUT = "IncorrectInput";

	public static final String SINGLE_DOC_NOT_FORMED_WHEN_LISTING_IS_SERVED = "SINGLE_DOC_NOT_FORMED_WHEN_LISTING_IS_SERVED";

	public static final String ADD_MONEY_USER_INSTRUMENT_LOGO_V2_POPULATED_IS_NULL = "ADD_MONEY_USER_INSTRUMENT_LOGO_V2_POPULATED_IS_NULL";

	public static final String REQUEST_ID = MDCConstants.REQUEST_ID;

	public static final String AMOUNT_SEARCH_COUNT = "AMOUNT_SEARCH_COUNT";

	public static final String RATE_LIMIT_COUNT = "RATE_LIMIT_COUNT";

	public static final String BG_RATE_LIMIT_COUNT = "BG_RATE_LIMIT_COUNT";

	public static final String PUSHED_TO_RETRY_KAFKA = "PUSHED_TO_RETRY_KAFKA";

	// NTU Cache stands for Non Transacting User Cache
	public static final String IGNORING_LISTING_ES_GET_CALL_BASED_ON_NTU_DATA = "IGNORING_LISTING_ES_GET_CALL_BASED_ON_NTU_DATA";

	public static final String TXN_DATE_DIFFERENCE = "TXN_DATE_DIFFERENCE";

	public static final String CACHE_DATE_DIFFERENCE = "CACHE_DATE_DIFFERENCE";

	public static final String RESPONSE_TIME = "RESPONSE_TIME";

	public static final String IS_SERVED_FROM_CACHE = "IS_SERVED_FROM_CACHE";

	public static final String CACHE_CREATED_DATE_CURRENT_DATE_DIFF_COUNT = "CACHE_CREATED_DATE_CURRENT_DATE_DIFF_COUNT";

	public static final String TIME_WINDOW = "TIME_WINDOW";

	public static final String PAGE_NO_WISE_COUNT = "PAGE_NO_WISE_COUNT";

	public static final String FIRST_TRANSACTION_CURRENT_DATE_DIFF = "FIRST_TRANSACTION_CURRENT_DATE_DIFF";

	public static final String QUERIED_OVER_ALIAS_IN_LISTING = "QUERIED_OVER_ALIAS_IN_LISTING";

	public static final String INVALID_SEARCH_FIELDS_VALUE = "INVALID_SEARCH_FIELDS_VALUE";

	public static final String INVALID_AMOUNT_SEARCH_VALUE = "INVALID_AMOUNT_SEARCH_VALUE";

	public static final String TXN_INDICATOR_BASED_NARRATION_AND_DATE_TIME_LABEL_USED = "TXN_INDICATOR_BASED_NARRATION_AND_DATE_TIME_LABEL_USED";

	public static final String EXCEPTION_WHILE_GETTING_NARRATION_AND_DATE_TIME_LABEL = "EXCEPTION_WHILE_GETTING_NARRATION_AND_DATE_TIME_LABEL";

	public static final String TXN_DATE_OF_LAST_TXN_IN_LISTING_CACHE_IS_BEFORE_CURRENT_FROM_DATE = "TXN_DATE_OF_LAST_TXN_IN_LISTING_CACHE_IS_BEFORE_CURRENT_FROM_DATE";

	public static final String TXN_DATE_ABSENT_IN_DETAIL_REQUEST = "TXN_DATE_ABSENT_IN_DETAIL_REQUEST";

	public static final String RECON_MISMATCH_COUNT = "RECON_MISMATCH_COUNT";

	public static final String RECON_MISMATCH_DIFF_COUNT = "RECON_MISMATCH_DIFF_COUNT";

	public static final String ES_COUNT_MORE = "esCountMore";

	public static final String RECON_FLOW = "RECON_FLOW";

	public static final String monitoringTagFormat = "%s:%s";

	public static final String FRONT_END_CACHE_RECON = "recon";

	public static final String IS_VISIBLE_FALSE_FOR_SEARCH_API = "IS_VISIBLE_FALSE_FOR_SEARCH_API";

	public static final String REPEAT_PAYMENT_FILTER_COUNT = "REPEAT_PAYMENT_FILTER_COUNT";

	public static final String RECON_API_FROM_DATE_GAP = "RECON_API_FROM_DATE_GAP";

	public static final String UPDATES_API_FROM_DATE_GAP_METRICS = "UPDATES_API_FROM_DATE_GAP";

	public static final String COUNT_MATCH = "countMatch";

	public static final String NUMBER_OF_INDEX_SAVED = "NUMBER_OF_INDEX_SAVED";

	public static final String CUSTOMER_CREATION_DATE_LOWER_THAN_FROM_DATE = "CUSTOMER_CREATION_DATE_LOWER_THAN_FROM_DATE";

	public static final String UPDATES_API_NO_TXNS = "UPDATES_API_NO_TXNS";

	public static final String UPDATES_API_INVALIDATION = "UPDATES_API_INVALIDATION";

	public static final String SERVED_FROM_ZERO_DELTA_CACHE = "servedFromZeroDeltaCache";

	public static final String CRITERIA = "criteria";

	public static final String TXN_STREAM_LAG_CACHE = "TXN_STREAM_LAG_CACHE";

	public static final String UPDATED_DATE_CURRENT_DATE_DIFF_AT_LAG_DATA_CONSUMER = "UPDATED_DATE_CURRENT_DATE_DIFF_AT_LAG_DATA_CONSUMER";

	public static final String DETAIL_API_VERSION = "detail_api_version";

	public static final String TXN_SOURCE = "txn_source";

	public static final String TRAFFIC_DIVERSION = "TRAFFIC_DIVERSION";

	public static final String WHITELISTING = "whiteListing";

	public static final String RATE_LIMITING = "rateLimiting";

	public static final String BANK_AUTH = "bank-auth";

	public static final String OCL_AUTH = "ocl-auth";

	public static final String THEME = "theme";

	public interface CacheMonitoringConstant {

		String CACHE_PROCESSING_TIME = "CACHE_PROCESSING_TIME";

		String CACHE_METHOD = "CACHE_METHOD";

		String CACHE_NAME = "CACHE_NAME";

		String CACHE_COUNT = "CACHE_COUNT";

		String CACHE_TTL_GAP = "CACHE_TTL_GAP";

		String CACHE_RATIO = "CACHE_RATIO";

		String CACHE_COUNT_TAG_FORMATTER = CACHE_NAME + COLON + "%s" + COMMA + CACHE_METHOD + COLON + "%S" + COMMA
				+ CACHE_RATIO + COLON + "%S";

		String CACHE_PROCESSING_TIME_TAG_FORMATTER = CACHE_NAME + COLON + "%s" + COMMA + CACHE_METHOD + COLON + "%S";

	}

	public enum CacheRatio {

		HIT, MISS, ERROR,

	}

	public enum CacheMonitoringMethod {

		GET, PUT, PUT_CUSTOM, EVICT, CLEAR;

	}

	public static class ConfigurablePropertiesConstants {

		public static final String BMS_API_TIME = "BMS_API_TIME";

		public static final String WHITELISTED_APIS_FOR_TRAFFIC_DIVERSION_ON_RATE_LIMIT = "white-listed-apis-for-traffic-diversion.on.rate.limit";

		public static final String WHITELISTED_APIS_FOR_TRAFFIC_DIVERSION = "white-listed-apis-for-traffic-diversion";

		public static final String IS_TRAFFIC_DIVERSION_ENABLED = "isTrafficDiversionEnabled";

		public static final String REDIRECTION_BASE_URL = "REDIRECTION_BASE_URL";

		public static final String S2S_CALL_ON_TRAFFIC_DIVERSION_ENABLED = "s2sCallOnTrafficDiversionEnabled";

		/*
		 * v2 base url -> https://pth-v2-internal.paytm.com (old) v2 K8s base url ->
		 * http://pth-v2-service.uthprod.svc.cluster.local
		 */
		public static final String PTH_V2_INTERNAL_URL = "https://pth-v2-internal.paytm.com";

	}

	public static class TokenLevelRateLimitingConstants {

		public static final String UTH_USER_RATELIMITER_STATUS = "uth_user_ratelimiter_status";

		public static final String UTH_USER_DAILY_RATELIMITER_STATUS = "uth_user_daily_ratelimiter_status";

	}

	public static class AerospikeConstants {

		public static final String AEROSPIKE = "aerospike";

		public static final String READ = "read";

		public static final String WRITE = "write";

		public static final String DELETE = "delete";

		public static final String READ_SUCCESS = "read-success";

		public static final String READ_FAILURE = "read-failure";

		public static final String WRITE_SUCCESS = "write-success";

		public static final String WRITE_FAILURE = "write-failure";

		public static final String DELETE_SUCCESS = "delete-success";

		public static final String DELETE_FAILURE = "delete-failure";

		public static final String SET = "set";

		public static final String STATUS = "status";

		public static final String EXCEPTION = "exception";

		public static final String ACTION = "action";

		public static final String BIN_NAME = "value";

	}

	public static class CacheActionConstants {

		public static final String ALLOWED = "allowed";

		public static final String BLOCKED = "blocked";

		public static final String UNAVAILABLE = "unavailable";

		public static final String SKIPPED = "skipped";

		public static final String WHITE_LIST_USER = "whitelistuser_allowed";

	}

	public static class CircuitBreakerConstants {

		public static final String DOWNSTREAM_CIRCUIT_BREAKER = "downstream_circuit_breaker";

		public static final String DOWNSTREAM_CLIENT = "downstream_client";

		public static final String CIRCUIT_BREAKER_STATE = "circuit_breaker_state";

		public static final String STATUS = "status";

		public static final String CIRCUIT_BREAKER_TRY_ACQUIRE = "circuit_breaker_try_acquire";

		public static final String EXCEPTION = "exception";

		public static final String CIRCUIT_BREAKER_EXCEPTION = "circuit_breaker_exception";

		public static final String CLOSED_STATE = "closed";

		public static final String OPEN_STATE = "open";

		public static final String STATE = "state";

	}

	public static class MDCConstants {

		public static final String REQUEST_ID = "requestId";

	}

}
