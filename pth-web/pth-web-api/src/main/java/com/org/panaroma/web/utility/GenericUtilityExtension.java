package com.org.panaroma.web.utility;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Iterables;
import com.org.panaroma.commons.constants.CommonConstants;
import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedRepeatPaymentData;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.enums.ApiVersion;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.Theme;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.utils.BeanUtil;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.config.DetailConfigKey;
import com.org.panaroma.web.config.DetailConfigUtility;
import com.org.panaroma.web.dto.DetailConfig;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.LogoOrderUtility;
import com.org.panaroma.web.dto.NameDetails;
import com.org.panaroma.web.dto.NameOrderUtility;
import com.org.panaroma.web.dto.SecondPartyInfo;
import com.org.panaroma.web.dto.UserInstrumentLogos;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.web.dto.detailAPI.ExtraDetailRequestFields;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.ListingApiParams;
import com.org.panaroma.web.dto.detailAPI.RecentTransactionInfo;
import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.InstrumentDtoV2;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.KycDetails;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailApiResponseV3;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.monitoring.MetricsPublishHelper;
import com.org.panaroma.web.scheduler.services.LagDataCacheService;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.org.panaroma.commons.constants.BankDataConstants.IS_VIRTUAL_ENTITY_ID;
import static com.org.panaroma.commons.constants.BankDataConstants.PPBL_IFSCCODE_IDENTIFIER;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.PoweredByLogoConstant.POWERED_BY_UPI_LOGO_LIST;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TXN_STREAM_LAG_BASED_TXN_DETAILS_REJECTION_ENABLED;
import static com.org.panaroma.commons.constants.Constants.CHAT;
import static com.org.panaroma.commons.constants.WebConstants.ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.ACC_REF_NUM;
import static com.org.panaroma.commons.constants.WebConstants.ADD_MONEY_BANK_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ADD_MONEY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.AMPERSAND;
import static com.org.panaroma.commons.constants.WebConstants.AUTOMATIC_ADD_MONEY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.BANK;
import static com.org.panaroma.commons.constants.WebConstants.BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.BANK_TXN_ID;
import static com.org.panaroma.commons.constants.WebConstants.BENEF_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.BENEF_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.BENEF_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.BENEF_NAME;
import static com.org.panaroma.commons.constants.WebConstants.BLACKLISTED_HANDLES_FOR_VPA_RPT_PMT;
import static com.org.panaroma.commons.constants.WebConstants.CASHBACK_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.CLOSING_BALANCE;
import static com.org.panaroma.commons.constants.WebConstants.COMBINED_WALLET_PASSBOOK;
import static com.org.panaroma.commons.constants.WebConstants.CREDIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.EMPTY_STRING;
import static com.org.panaroma.commons.constants.WebConstants.EQUAL_SYMBOL;
import static com.org.panaroma.commons.constants.WebConstants.ES_DATE_RANGE;
import static com.org.panaroma.commons.constants.WebConstants.ES_HOST_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.FILTER_TYPE_SECOND_PARTY_ID;
import static com.org.panaroma.commons.constants.WebConstants.HYPHEN;
import static com.org.panaroma.commons.constants.WebConstants.IFSC_REGEX;
import static com.org.panaroma.commons.constants.WebConstants.INWARD_TXN_REPEAT_PAYMENT_CTA_TEXT;
import static com.org.panaroma.commons.constants.WebConstants.IS_FILTER_APPLIED;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_TRANSACTION_TYPES;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_VPA;
import static com.org.panaroma.commons.constants.WebConstants.MID;
import static com.org.panaroma.commons.constants.WebConstants.MOBILE_NO_DEEPLINK_ENABLED_FOR_VPA2VPA;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.MANDATE_AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.ONUS_MERCHANT_FLAG_KEY_FROM_UPI;
import static com.org.panaroma.commons.constants.WebConstants.ON_HOLD_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ORDER_ID_CAMEL_CASE;
import static com.org.panaroma.commons.constants.WebConstants.OUTWARD_TXN_REPEAT_PAYMENT_CTA_TEXT;
import static com.org.panaroma.commons.constants.WebConstants.P2M_INTERNATIONAL_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.P2M_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.P2M_REFUND_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PASSBOOK_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_NAME;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_VPA;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_SYSTEM;
import static com.org.panaroma.commons.constants.WebConstants.PoweredByLogoConstant.CC;
import static com.org.panaroma.commons.constants.WebConstants.PoweredByLogoConstant.MANDATE;
import static com.org.panaroma.commons.constants.WebConstants.PoweredByLogoConstant.ULTRA;
import static com.org.panaroma.commons.constants.WebConstants.QR_CODE_ID;
import static com.org.panaroma.commons.constants.WebConstants.QR_ID;
import static com.org.panaroma.commons.constants.WebConstants.RAW_ACCOUNT_NO;
import static com.org.panaroma.commons.constants.WebConstants.RECIPIENT;
import static com.org.panaroma.commons.constants.WebConstants.RECURRING_MANDATE;
import static com.org.panaroma.commons.constants.WebConstants.RECURRING_MANDATE_AND_UPI_CC;
import static com.org.panaroma.commons.constants.WebConstants.RELEASED_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.REMARKS;
import static com.org.panaroma.commons.constants.WebConstants.REMIT_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.REMIT_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.REMIT_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.REMIT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_DISABLED_BANK_AND_WALLET;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_MERCHANT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RRN;
import static com.org.panaroma.commons.constants.WebConstants.SHOW_HIDDEN_TXN;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_PURPOSE;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.UPI;
import static com.org.panaroma.commons.constants.WebConstants.UPI_CC;
import static com.org.panaroma.commons.constants.WebConstants.UPI_P2P_IN_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_P2P_OUT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_P2P_OUT_REV_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_TXN_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.UPI_VIA_CC_FLAG;
import static com.org.panaroma.commons.constants.WebConstants.UpiErrorCodes.ERROR_CODE;
import static com.org.panaroma.commons.constants.WebConstants.UpiErrorCodes.NPCI_RESP_CODE;
import static com.org.panaroma.commons.constants.WebConstants.UpiErrorCodes.RESULT_CODE_ID;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.UPI_LITE_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.VERTICAL_ID;
import static com.org.panaroma.commons.constants.WebConstants.VERTICAL_NAME;
import static com.org.panaroma.commons.constants.WebConstants.VIEW_MORE_DETAILS;
import static com.org.panaroma.commons.constants.WebConstants.VPA2ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.WALLET;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_AUTOMATIC_TXN_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_P2P_IN_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_P2P_OUT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_P2P_OUT_REV_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_TXN_ID;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_TXN_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_TYPE;
import static com.org.panaroma.commons.utils.CartUtility.isThisOmsOrder;
import static com.org.panaroma.commons.utils.Utility.getOtherParticipant;
import static com.org.panaroma.commons.utils.Utility.getOtherPartyP2pPaytmVpaParticipant;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.INVALIDATED_BY_LAG_CACHE;
import static com.org.panaroma.web.utility.GenericUtility.formatString;
import static com.org.panaroma.web.utility.MandateUtility.isRecurringMandateTxn;

@Log4j2
@Component
public class GenericUtilityExtension {

	public static final String REPEAT_PAYMENT = "Pay Again";

	private static boolean poweredByUrlEnabledInFooterLogo;

	private static DetailConfigUtility detailConfigUtility;

	private static MetricsPublishHelper metricsPublishHelper;

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	private static DecimalFormat amtFormatter = new DecimalFormat("0.00");

	private static List<TransactionTypeEnum> P2P_TXN_TYPES_LIST = Arrays.asList(TransactionTypeEnum.P2P_OUTWARD,
			TransactionTypeEnum.P2P_INWARD, TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD,
			TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD, TransactionTypeEnum.P2P_OUTWARD_REVERSAL,
			TransactionTypeEnum.P2P_INWARD_REVERSAL, TransactionTypeEnum.P2P2M, TransactionTypeEnum.P2P2M_REFUND,
			TransactionTypeEnum.P2P2M_INWARD, TransactionTypeEnum.P2P2M_OUTWARD);

	@Autowired(required = false)
	public GenericUtilityExtension(
			@Value("${poweredbyurl.enable.in.footerlogo}") final boolean poweredByUrlEnabledInFooterLogo,
			final MetricsPublishHelper metricsPublishHelper,
			final ConfigurablePropertiesHolder configurablePropertiesHolder, final ObjectMapper objectMapper) {
		this.poweredByUrlEnabledInFooterLogo = poweredByUrlEnabledInFooterLogo;
		GenericUtilityExtension.metricsPublishHelper = metricsPublishHelper;
		GenericUtilityExtension.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	public static String getPoweredByLogoUrlForUpiInstrument(final TransformedTransactionHistoryDetail txn,
			final Theme theme) {

		// Step 1 : get create Key to fetch Logo from Map
		String logoNameKey = createKeyForPoweredByLogoMap(txn, theme);

		if (checkIfWeHaveLogoForGivenLogoNameInConfigMap(txn)) {
			return LogoUtility.createPoweredByUpiLogoUrl(logoNameKey, null);
		}

		// get defaultLogo based on Key
		return LogoUtility.createPoweredByUpiLogoUrl(EMPTY_STRING, theme);
	}

	public static boolean checkIfWeHaveLogoForGivenLogoNameInConfigMap(final TransformedTransactionHistoryDetail txn) {

		try {
			String vpaHandle = Utility.getVpaHandleForUpiDebitParticipant(txn);
			List<String> handleWithLogoNameMap = configurablePropertiesHolder.getProperty(POWERED_BY_UPI_LOGO_LIST,
					ArrayList.class);

			return StringUtils.isNotBlank(vpaHandle)
					&& Boolean.FALSE.equals(CollectionUtils.isEmpty(handleWithLogoNameMap))
					&& handleWithLogoNameMap.contains(vpaHandle);

		}
		catch (Exception e) {
			log.error("Issue in parsing propertyName : poweredBy.upiLogo.list | exceptionMsg:{}", e.getMessage());
		}
		return false;
	}

	/*
	 * While creating key priority will be given in order (High)Mandate + CC -> Mandate ->
	 * CC -> VPA(Low)
	 */
	public static String createKeyForPoweredByLogoMap(final TransformedTransactionHistoryDetail txn,
			final Theme theme) {
		if (Objects.isNull(txn)) {
			return null;
		}

		boolean isUpiViaCC = Utility.isUpiViaCcTxn(txn);
		boolean isUpiMandate = Utility.isRecurringMandateTransaction(txn);

		// Get Vpa Handle
		String vpaHandle = Utility.getVpaHandleForUpiDebitParticipant(txn);

		StringBuilder keyBuilder = new StringBuilder();

		// append respective identifier
		if (Theme.DARK.equals(theme)) {
			keyBuilder.append(ULTRA);
		}
		if (isUpiMandate) {
			keyBuilder.append(MANDATE);
		}
		if (isUpiViaCC) {
			keyBuilder.append(CC);
		}
		keyBuilder.append(vpaHandle);
		return keyBuilder.toString();
	}

	public static void setClosingBalanceForInstrument(final InstrumentDtoV2 instrumentDtoV2, final String txnId) {
		if (instrumentDtoV2 == null) {
			return;
		}
		if (instrumentDtoV2.getParticipantInfo() != null && instrumentDtoV2.isSelfInstrument()
				&& PaymentSystemEnum.WALLET.getPaymentSystemKey()
					.equals(instrumentDtoV2.getParticipantInfo().getPaymentSystem())
				&& instrumentDtoV2.getParticipantInfo().getParticipant() != null
				&& instrumentDtoV2.getParticipantInfo().getParticipant().getWalletData() != null
				&& StringUtils.isNotBlank(
						instrumentDtoV2.getParticipantInfo().getParticipant().getWalletData().getClosingBalance())) {
			String closingBalance = instrumentDtoV2.getParticipantInfo()
				.getParticipant()
				.getWalletData()
				.getClosingBalance();
			try {
				// to check the amount is parsable or not
				Double.parseDouble(closingBalance);
			}
			catch (NumberFormatException e) {
				log.error(
						"Invalid Amount while showing closing balance, "
								+ "amount is not parsable , having txnId : {} , and closing balance : {}",
						txnId, closingBalance);
				return;
			}
			catch (Exception e) {
				log.error("Invalid Amount while showing closing balance, having txnId : {} , and closing balance : {}",
						txnId, closingBalance);
				return;
			}

			String[] amount = closingBalance.split("\\.");
			if (amount.length > 2) {
				log.error("Invalid Amount while showing closing balance, "
						+ "amount have multiple char : '.' , having txnId : {}", txnId);
				return;
			}

			String finalAmount = null;
			if (amount.length == 2 && StringUtils.isNotBlank(amount[1])) {
				finalAmount = GenericUtilityExtension.getFormatedAmount(amount[0], amount[1], txnId);
			}
			else {
				finalAmount = GenericUtilityExtension.getFormatedAmount(amount[0], null, txnId);
			}

			if (finalAmount != null) {
				instrumentDtoV2.setClosingBalance(CLOSING_BALANCE + finalAmount);
			}
		}
	}

	public static String getFormatedAmount(final String rps, final String paisa, final String txnId) {
		if (StringUtils.isBlank(rps) || (StringUtils.isNotBlank(paisa) && paisa.length() > 2)) {
			log.error("rupees part in amount is null or paisa part length is greater than 2 for txnId : {}", txnId);
			return null;
		}
		// to convert strings like 01 o 1
		String rupees = String.valueOf(Long.valueOf(rps));
		int rupeeLength = rupees.length();

		StringBuilder finalamount = new StringBuilder();
		// here i is iterative pointer to the character in rupee string
		for (int i = 0; i < rupeeLength; i++) {
			// if rupees has even length having size greater than 3
			// then for every character at odd length we have to add a suffix ","
			if (rupeeLength % 2 == 0 && i % 2 == 1 && (rupeeLength - i) >= 3 && i != 0) {
				finalamount.append(",");
			}
			// if rupees has odd length having size greater than 3
			// then for every character at even length we have to add a suffix ","
			if (rupeeLength % 2 == 1 && i % 2 == 0 && (rupeeLength - i) >= 3 && i != 0) {
				finalamount.append(",");
			}
			finalamount.append(rupees.charAt(i));
		}

		// if paisa is all 0's then no need to append it to amount
		if (paisa != null && Long.parseLong(paisa) != 0) {
			finalamount.append(".").append(paisa);
		}
		return finalamount.toString();
	}

	public static void setRecentWidget(final RecentTransactionInfo recentInfoMap,
			final DetailApiResponse detailApiResponse, final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(recentInfoMap) || Objects.isNull(recentInfoMap.getListingApiParams())) {
			return;
		}
		RecentTransactionInfo recentInfo = new RecentTransactionInfo();
		ListingApiParams listingApiParams = new ListingApiParams();
		recentInfo.setNarration(recentInfoMap.getNarration());
		ListingApiParams listingApiParamsMap = recentInfoMap.getListingApiParams();
		if (FILTER_TYPE_SECOND_PARTY_ID.equals(listingApiParamsMap.getFilterType())) {
			SecondPartyInfo secondPartyInfo = ListingUtility.getSecondPartyInfo(txn, detailApiResponse.getStatus(),
					null, true);
			if (Objects.isNull(secondPartyInfo) || Objects.isNull(secondPartyInfo.getEntityId())) {
				return;
			}
			listingApiParams.setSecondPartyId(secondPartyInfo.getEntityId());
			if (Objects.nonNull(recentInfoMap.getName())) {
				recentInfo.setName(secondPartyInfo.getName());
			}
		}
		if (!Objects.isNull(listingApiParamsMap.getTxnType())) {
			listingApiParams.setTxnType(listingApiParamsMap.getTxnType());
		}

		if (!Objects.isNull(listingApiParamsMap.getTxnPurpose())) {
			listingApiParams.setTxnPurpose(listingApiParamsMap.getTxnPurpose());
		}
		listingApiParams.setFilterApplied(true);
		listingApiParams.setFilterType(listingApiParamsMap.getFilterType());
		recentInfo.setListingApiParams(listingApiParams);
		detailApiResponse.setRecentTransactionInfo(recentInfo);
	}

	public static String setRecentNarration(final TransformedTransactionHistoryDetail detail,
			final ClientStatusEnum status, final EsResponseTxn esResponseTxn) {
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(detail.getMainTxnType());
		if (!TransactionTypeEnum.PPBL_TRANSACTION.equals(txnType)) {
			String purpose = GenericUtility.getPurposeLabel(detail, txnType);
			detailConfigUtility = BeanUtil.getBean(DetailConfigUtility.class);
			Map<DetailConfigKey, List<DetailConfig>> detailConfigMap = detailConfigUtility.getDetailConfigMap();
			if (Objects.isNull(detailConfigMap) || detailConfigMap.size() == 0) {
				return null;
			}
			DetailConfigKey detailConfigKey = DetailConfigKey.builder().txnType(txnType).status(status).build();
			List<DetailConfig> detailConfigList = detailConfigMap.get(detailConfigKey);
			if (null == detailConfigList || detailConfigList.size() == 0) {
				return null;
			}
			for (DetailConfig detailConfig : detailConfigList) {
				if (Objects.isNull(detailConfig.getRecentTransactionInfo())) {
					return null;
				}
				if (StringUtils.isBlank(detailConfig.getRecentTransactionInfo().getRecentDynamicNarration())
						&& Objects.nonNull(esResponseTxn.getSecondPartyInfo())) {
					// esResponseTxn.getSecondPartyInfo().setName(null);
				}
				if (Objects.isNull(detailConfig.getTxnPurpose())) {
					return detailConfig.getRecentTransactionInfo().getRecentNarration();
				}
				else if (detailConfig.getTxnPurpose().equals(purpose)) {
					return detailConfig.getRecentTransactionInfo().getRecentNarration();
				}
			}
		}
		return null;
	}

	public static List<Logo> updateLogoOrder(final TransformedTransactionHistoryDetail esDto,
			final List<Logo> logoOrderInput, final TransformedParticipant participant, final boolean forSecondParty) {
		// get Transaction Type
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getTxnType());
		List<Logo> logoOrder = logoOrderInput;
		if (logoOrder != null) {
			LogoOrderUtility.updateLogoOrder(logoOrder, participant, esDto, forSecondParty);
			LogoOrderUtility.sortLogoOrder(logoOrder, txnType);
		}
		else {
			List<Logo> tempLogoOrder = new ArrayList<>();
			LogoOrderUtility.updateLogoOrder(tempLogoOrder, participant, esDto, forSecondParty);
			LogoOrderUtility.sortLogoOrder(tempLogoOrder, txnType);
			if (tempLogoOrder != null && tempLogoOrder.size() > 0) {
				logoOrder = tempLogoOrder;
			}
		}
		return logoOrder;
	}

	public static List<NameDetails> updateNameDetails(final TransformedTransactionHistoryDetail esDto,
			final List<NameDetails> nameOrderInput, final TransformedParticipant participant,
			final boolean forSecondParty) {

		if (Utility.isSelfTransferTxn(esDto)) {
			return null;
		}
		List<NameDetails> nameOrder = nameOrderInput;
		if (nameOrder != null) {
			NameOrderUtility.updateNameOrder(nameOrder, participant, esDto, forSecondParty);
			NameOrderUtility.sortNameOrder(nameOrder);
		}
		else {
			List<NameDetails> tempNameOrder = new ArrayList<>();
			NameOrderUtility.updateNameOrder(tempNameOrder, participant, esDto, forSecondParty);
			NameOrderUtility.sortNameOrder(tempNameOrder);
			if (tempNameOrder != null && tempNameOrder.size() > 0) {
				nameOrder = tempNameOrder;
			}
		}
		return nameOrder;
	}

	public static boolean isInternationalRemittance(final TransformedTransactionHistoryDetail tthd) {
		if (!Objects.isNull(tthd)
				&& (TransactionTypeEnum.P2P_OUTWARD_REMITTANCE.getTransactionTypeKey().equals(tthd.getTxnType())
						|| TransactionTypeEnum.P2P_OUTWARD_REMITTANCE_REFUND.getTransactionTypeKey()
							.equals(tthd.getTxnType()))) {
			return true;
		}
		return false;
	}

	public static RepeatPayment getOutwardRemittanceUrl(final String orderId, final String vendorUrl) {
		RepeatPayment repeatPayment = null;
		if (!StringUtils.isBlank(vendorUrl)) {
			repeatPayment = new RepeatPayment();
			repeatPayment.setUrl(getVendorUrl(orderId, vendorUrl));
			repeatPayment.setName(VIEW_MORE_DETAILS);
		}
		return repeatPayment;
	}

	private static String getVendorUrl(final String orderId, final String vendorUrl) {
		// "paytmmp://payment_bank?featuretype=outward_remittance&orderId=PTM4234208509005"
		String url = vendorUrl + AMPERSAND + ORDER_ID_CAMEL_CASE + EQUAL_SYMBOL + orderId;
		return url;
	}

	/**
	 * Sets the amount in listing response based on provided parameters.
	 */
	public static String setAmount(final TransformedTransactionHistoryDetail esDto, final EsResponseTxn esResponseTxn,
			final Map<String, String> paramMap) {
		Long amount = esDto.getAmount();
		// Check if paramMap contains necessary keys for filtering
		if (paramMap != null && paramMap.containsKey(IS_FILTER_APPLIED) && paramMap.containsKey(PAYMENT_SYSTEM)
				&& paramMap.containsKey(PASSBOOK_FILTER) && TRUE.equalsIgnoreCase(paramMap.get(PASSBOOK_FILTER))) {
			// Create a list to store payment system enums , currently it'll store only 1
			// paymentSystem value
			List<PaymentSystemEnum> list = new ArrayList<>();
			for (String paymentSystem : paramMap.get(PAYMENT_SYSTEM).split(",")) {
				list.add(PaymentSystemEnum.getPaymentSystemEnumByValue(paymentSystem));
			}
			// Special handling when "walletTypeFilter": "ALL" is received in request
			// param
			if (paramMap.containsKey(COMBINED_WALLET_PASSBOOK)) {
				amount = 0L;
				// Calculate combined amount for all wallets involved in transactions
				// whose txnIndicator is same as orderLevel txnIndicator
				for (TransformedParticipant participant : esDto.getParticipants()) {
					if (participant.getTxnIndicator().equals(esDto.getTxnIndicator()) && PaymentSystemEnum.WALLET
						.equals(PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem()))) {
						amount += participant.getAmount();
					}
				}
			}
			else {
				/**
				 * Handle filtering based on payment system and wallet type: - If serving
				 * an UPI passbook request from Uth, set the amount from the UPI
				 * participant. - If a wallet type is specified in the request, pick the
				 * amount from the WALLET participant.
				 */
				for (TransformedParticipant participant : esDto.getParticipants()) {
					if (participant.getTxnIndicator().equals(esDto.getTxnIndicator()) && list
						.contains(PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem()))) {
						// In case of UPI passbook, pick amount from the UPI participant
						// with matching txnIndicator
						amount = participant.getAmount();
						// Check if walletTypeFilter is applied, return only the amount
						// for the specified wallet type
						if (paramMap.containsKey(WALLET_TYPE) && participant.getWalletData() != null
								&& paramMap.get(WALLET_TYPE)
									.equals(participant.getWalletData().getWalletType().toString())) {
							break;
						}
					}
				}
			}
		}
		/**
		 * Handle scenarios for failure in wallet add money case: - If there is no event
		 * received from the wallet or there is a lag in wallet event processing. - For
		 * combined wallet passbook requests, ensure the amount is not set to 0 by
		 * checking for null. - If the amount is 0, set it to the amount present at the
		 * order level to prevent incorrect setting of amount as 0.
		 */
		return Currency.getCurrencyAmountInHigherDenomination(
				BigDecimal.valueOf(amount != 0L ? amount : esDto.getAmount()), esResponseTxn.getCurrency());
	}

	public static void setRepeatPaymentDetails(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final DetailApiResponse detailApiResponse) {

		if (configurablePropertiesHolder.getProperty(RPT_PAYMENT_DISABLED_BANK_AND_WALLET, Boolean.class)
				&& isRepeatPaymentForWallet(listingVisibleTxn)) {
			return;
		}

		/*
		 * if (listingVisibleTxn != null && listingVisibleTxn.getContextMap() != null &&
		 * VPA2ACCOUNT.equalsIgnoreCase(listingVisibleTxn.getContextMap().get(
		 * UPI_TXN_CATEGORY))) { return; }
		 */

		if ((TransactionIndicator.DEBIT.getTransactionIndicatorKey() == listingVisibleTxn.getTxnIndicator())
				&& Objects.nonNull(listingVisibleTxn.getRepeatPaymentData())
				&& !ClientStatusEnum.FAILURE.getStatusKey().equals(listingVisibleTxn.getStatus())) {
			log.debug("Creating RepeatPaymentDetails for txnId: {}, with isRepeatable flag as {} and url as {}",
					listingVisibleTxn.getTxnId(), listingVisibleTxn.getRepeatPaymentData().getIsRepeatable(),
					listingVisibleTxn.getRepeatPaymentData().getUrl());
			if (Boolean.FALSE.equals(listingVisibleTxn.getRepeatPaymentData().getIsRepeatable())) {
				detailApiResponse.setRepeatPayment(null);
			}
			else if (!StringUtils.isBlank(listingVisibleTxn.getRepeatPaymentData().getUrl())) {
				RepeatPayment repeatPayment = getRepeatPayment(REPEAT_PAYMENT,
						listingVisibleTxn.getRepeatPaymentData().getUrl());
				detailApiResponse.setRepeatPayment(repeatPayment);
			}
		}
	}

	public static String amountFormatForSeachableArray(String amt) {
		String formattedAmt = amtFormatter.format(Double.parseDouble(amt));
		if (formattedAmt.endsWith(".00")) {
			// If it does, remove the ".00"
			formattedAmt = formattedAmt.substring(0, formattedAmt.length() - 3);
		}
		return formattedAmt;
	}

	/**
	 * Checks if the repeat payment setup is for bank or wallet/
	 * @param listingVisibleTxn txn data to check.
	 * @return true if repeat payment will go to PPBL bank or wallet
	 */
	private static boolean isRepeatPaymentForWallet(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (listingVisibleTxn.getRepeatPaymentData() == null) {
			return false;
		}
		TransformedRepeatPaymentData rptData = listingVisibleTxn.getRepeatPaymentData();
		if (StringUtils.isBlank(rptData.getUrl())) {
			return false;
		}
		// For UPI_WALLET_CREDIT type txns
		if (rptData.getUrl().contains("@paytmwallet")) { // handle for send money to
															// wallet via UPI
			return true;
		}
		// For P2P_UPI_TO_WALLET_OUTWARD txns
		if (TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey()
			.equals(listingVisibleTxn.getTxnType())) {
			return true;
		}
		return false;
	}

	public static void setMobileNoBasedRptPymtDetails(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final DetailApiResponse detailApiResponse, final String rptPaymntToMobileUrl,
			final Map<String, Boolean> fieldsForToMobileRptPaymntUrl) {

		TransformedParticipant secondPartyParticipant = getOtherPartyP2pPaytmVpaParticipant(listingVisibleTxn);
		RepeatPayment repeatPayment = null;
		if (!Objects.isNull(secondPartyParticipant)) {
			if (TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())) {
				repeatPayment = GenericUtility.getRepeatPaymentDetails(fieldsForToMobileRptPaymntUrl,
						rptPaymntToMobileUrl, secondPartyParticipant, listingVisibleTxn,
						OUTWARD_TXN_REPEAT_PAYMENT_CTA_TEXT);
			}
			else if (TransactionTypeEnum.P2P_INWARD.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())) {
				repeatPayment = GenericUtility.getRepeatPaymentDetails(fieldsForToMobileRptPaymntUrl,
						rptPaymntToMobileUrl, secondPartyParticipant, listingVisibleTxn,
						INWARD_TXN_REPEAT_PAYMENT_CTA_TEXT);
			}
			if (Objects.nonNull(repeatPayment)) {
				detailApiResponse.setRepeatPayment(repeatPayment);
			}
		}

	}

	public static void filterRepeatPaymentDetails(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final DetailApiResponse detailApiResponse) {

		if (TransactionSource.PPBL.getTransactionSourceKey().equals(listingVisibleTxn.getStreamSource())
				|| TransactionSource.TS.getTransactionSourceKey().equals(listingVisibleTxn.getStreamSource())) {
			return;
		}

		if (TransactionIndicator.CREDIT
			.equals(TransactionIndicator.getTransactionIndicatorEnumByKey(listingVisibleTxn.getTxnIndicator()))) {
			return;
		}

		if (Boolean.FALSE.equals(listingVisibleTxn.getIsSource())) {
			detailApiResponse.setRepeatPayment(null);
			log.warn("Repeat payment cta filtered for txnId : {}, streamSource : {}, txnType : {}",
					listingVisibleTxn.getTxnId(), listingVisibleTxn.getStreamSource(), listingVisibleTxn.getTxnType());
		}

		if (DataValidationUtility.isRepeatPaymentLegacyImplEnabled()) {
			// will remove this check after completing this task: Remove legacy pay again
			// logic for the some flows
			if ((TransactionSource.isPgTypeSource(listingVisibleTxn.getStreamSource())
					|| TransactionSource.UPI.getTransactionSourceKey().equals(listingVisibleTxn.getStreamSource()))
					&& (TransactionTypeEnum.P2M.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType()))
					&& (listingVisibleTxn.getRepeatPaymentData() == null
							|| listingVisibleTxn.getRepeatPaymentData().getUrl() == null)) {

				detailApiResponse.setRepeatPayment(null);
				log.warn("Repeat payment cta filtered for txnId : {}, streamSource : {}, txnType : {}",
						listingVisibleTxn.getTxnId(), listingVisibleTxn.getStreamSource(),
						listingVisibleTxn.getTxnType());
			}
		}
	}

	private static RepeatPayment getRepeatPayment(final String label, final String url) {
		RepeatPayment repeatPayment = new RepeatPayment();
		repeatPayment.setName(label);
		repeatPayment.setUrl(url);
		return repeatPayment;
	}

	public static boolean isP2pTransactionNonBank(final TransformedTransactionHistoryDetail tthd) {
		if (!Objects.isNull(tthd)) {
			TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getMainTxnType());
			if (P2P_TXN_TYPES_LIST.contains(txnType)) {
				return true;
			}
		}
		return false;
	}

	public static boolean isP2pTransaction(final TransformedTransactionHistoryDetail tthd) {
		if (!Objects.isNull(tthd)) {
			if (isP2pTransaction(tthd.getTxnType())) {
				return true;
			}
		}
		return false;
	}

	public static boolean isP2pTransaction(final Integer txnType2) {
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(txnType2);
		if (P2P_TXN_TYPES_LIST.contains(txnType)) {
			return true;
		}
		return false;
	}

	public static KycDetails getKycDetails(final InstrumentDtoV2 instrumentDtoV2) {
		if (Boolean.TRUE.equals(instrumentDtoV2.isSelfInstrument())) {
			return getKycDetailsForSelfInstrument();
		}
		else {
			return getKycDetailsForOtherInstrument(instrumentDtoV2);
		}
	}

	private static KycDetails getKycDetailsForOtherInstrument(final InstrumentDtoV2 instrumentDtoV2) {
		KycDetails kycDetails = new KycDetails();
		if (instrumentDtoV2.getEntityDetails() != null && instrumentDtoV2.getEntityDetails().getLogoOrder() != null) {
			kycDetails.setLogoOrder(LogoOrderUtility.filterKycLogosInLogoOrder(instrumentDtoV2));
		}
		return kycDetails;
	}

	private static KycDetails getKycDetailsForSelfInstrument() {
		KycDetails kycDetails = new KycDetails();
		kycDetails.setLogoOrder(LogoOrderUtility.getKycDetailsLogoOrder());
		kycDetails.setNameOrder(NameOrderUtility.getKycNameDetails());
		return kycDetails;
	}

	// Get avenue order id from context map
	public static String getAvenueOrderIdFromContextMap(final Map<String, String> contextMap) {
		String orderId = contextMap != null && contextMap.containsKey("merchantOrderId")
				? contextMap.get("merchantOrderId") : null;
		return orderId;
	}

	public static String getLogoForTxnType(final TransformedTransactionHistoryDetail esDto) {
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getMainTxnType());
		log.debug("getting logo for transaction with transaction Id {}, entityId {}", esDto.getTxnId(),
				esDto.getEntityId());
		if (txnType == null) {
			return null;
		}
		String logoIdentifier = null;

		switch (txnType) {
			// the below case can be removed when txnType field will be corrected for all
			// data of P2P2M txnType
			case P2P2M:
				if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(esDto.getTxnIndicator())) {
					logoIdentifier = P2M_LOGO;
				}
				else {
					if (TransactionSource.UPI.getTransactionSourceKey().equals(esDto.getStreamSource())) {
						logoIdentifier = UPI_P2P_IN_LOGO;
					}
					else if (TransactionSource.WALLET.getTransactionSourceKey().equals(esDto.getStreamSource())) {
						logoIdentifier = WALLET_P2P_IN_LOGO;
					}
				}
				break;
			case P2P2M_OUTWARD:
				logoIdentifier = P2M_LOGO;
				break;
			case P2P2M_INWARD:
				// the below if can be removed because as of now we don't get P2P2M data
				// from UPI
				if (TransactionSource.UPI.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					logoIdentifier = UPI_P2P_IN_LOGO;
				}
				else if (TransactionSource.WALLET.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					logoIdentifier = WALLET_P2P_IN_LOGO;
				}
				break;
			case P2M_INTERNATIONAL:
			case P2M_REVERSAL_INTERNATIONAL:
				logoIdentifier = P2M_INTERNATIONAL_LOGO;
				break;
			case P2M:
			case ADD_AND_PAY:
			case WALLET_UPI_DEBIT_P2M:
				logoIdentifier = P2M_LOGO;
				break;
			case P2M_REFUND:
			case P2P2M_REFUND:
			case UPI_WALLET_CREDIT:
				logoIdentifier = P2M_REFUND_LOGO;
				break;
			case P2P_UPI_TO_WALLET_OUTWARD:
			case P2P_OUTWARD:
			case P2P_OUTWARD_REMITTANCE:
			case WALLET_UPI_DEBIT_P2P:
				if (TransactionSource.UPI.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					logoIdentifier = UPI_P2P_OUT_LOGO;
				}
				else if (TransactionSource.WALLET.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					logoIdentifier = WALLET_P2P_OUT_LOGO;
				}
				break;
			case P2P_UPI_TO_WALLET_INWARD:
			case P2P_INWARD:
				if (TransactionSource.UPI.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					logoIdentifier = UPI_P2P_IN_LOGO;
				}
				else if (TransactionSource.WALLET.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					logoIdentifier = WALLET_P2P_IN_LOGO;
				}
				break;

			case CASHBACK_RECEIVED:
				logoIdentifier = CASHBACK_LOGO;
				break;
			case OTHER_CREDIT:
				logoIdentifier = WALLET_P2P_IN_LOGO;
				break;
			case SYSTEM_DEBIT:
			case OTHER_DEBIT:
				logoIdentifier = WALLET_P2P_OUT_LOGO;
				break;
			case P2P_OUTWARD_REVERSAL:
			case P2P_OUTWARD_REMITTANCE_REFUND:
			case WALLET_UPI_DEBIT_REVERSAL:
				if (TransactionSource.UPI.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					logoIdentifier = UPI_P2P_OUT_REV_LOGO;
				}
				else if (TransactionSource.WALLET.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					logoIdentifier = WALLET_P2P_OUT_REV_LOGO;
				}
				break;
			case ADD_MONEY:
				String walletTxnType = esDto.getContextMap().getOrDefault(WALLET_TXN_TYPE, null);
				if (WALLET_AUTOMATIC_TXN_TYPE.equalsIgnoreCase(walletTxnType)) {
					logoIdentifier = AUTOMATIC_ADD_MONEY_LOGO;
				}
				else {
					logoIdentifier = ADD_MONEY_LOGO;
				}
				break;
			case ON_HOLD:
				logoIdentifier = ON_HOLD_LOGO;
				break;
			case RELEASED:
				logoIdentifier = RELEASED_LOGO;
				break;
			case ADD_MONEY_TO_BANK:
				logoIdentifier = ADD_MONEY_BANK_LOGO;
				break;
			case WALLET_SETTLEMENT:
				logoIdentifier = WALLET_P2P_IN_LOGO;
				break;
			case ADD_MONEY_TO_UPI_LITE:
			case DEACTIVATION_OF_UPI_LITE:
			case LITE_TOPUP_MANDATE:
				logoIdentifier = UPI_LITE_LOGO;
				return LogoUtility.getLogo(logoIdentifier, LogoType.UPI_LITE_ICON);
			default:
				// do nothing
		}
		return LogoUtility.getLogo(logoIdentifier, LogoType.TRANSACTION_CATEGORY_ICON);
	}

	public static String getLargestTrimmedWordFromText(final String string) {
		String[] words = string.split("[^a-zA-Z0-9]");
		String largestWord = "";
		for (String word : words) {
			if (largestWord.length() < word.length()) {
				largestWord = word;
			}
		}
		return largestWord;
	}

	public static void userInstrumentLogos(final List<UserInstrumentLogos> userInstrumentLogos,
			final AtomicInteger walletInstrumentCount) {
		if (userInstrumentLogos != null && userInstrumentLogos.size() > 2 && walletInstrumentCount.get() > 1) {
			List<UserInstrumentLogos> userInstrumentLogosToBeRemoved = new ArrayList<>();
			for (UserInstrumentLogos instrumentLogo : userInstrumentLogos) {
				if (PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(instrumentLogo.getPaymentSystem())) {
					userInstrumentLogosToBeRemoved.add(instrumentLogo);
				}
			}
			if (userInstrumentLogosToBeRemoved != null && userInstrumentLogosToBeRemoved.size() > 1) {
				UserInstrumentLogos finalWalletInstrumetLogos = userInstrumentLogosToBeRemoved.get(0);
				userInstrumentLogos.removeAll(userInstrumentLogosToBeRemoved);
				finalWalletInstrumetLogos
					.setLogoUrl(LogoUtility.getLogo(WalletTypesEnum.SCLW.getDisplayName(), LogoType.WALLET_ICON));
				finalWalletInstrumetLogos.setInstrumentName(WalletTypesEnum.SCLW.getDisplayName());
				userInstrumentLogos.add(finalWalletInstrumetLogos);
			}
		}
	}

	public static void shiftStoreCashInstrumentToEnd(final List<UserInstrumentLogos> userInstrumentLogos,
			final AtomicInteger storeCashInstrumentCount) {
		if (storeCashInstrumentCount.get() < 1 || Objects.isNull(userInstrumentLogos)
				|| userInstrumentLogos.size() < 2) {
			return;
		}

		UserInstrumentLogos storeCashUserInstrumentLogo = null;
		int storeCashUserLogoIdx = Iterables.indexOf(userInstrumentLogos,
				instrumentLogo -> PaymentSystemEnum.STORE_CASH.getPaymentSystemKey()
					.equals(instrumentLogo.getPaymentSystem()));
		if (storeCashUserLogoIdx >= 0) {
			String sequence = String.valueOf(userInstrumentLogos.size() + 1);
			storeCashUserInstrumentLogo = userInstrumentLogos.remove(storeCashUserLogoIdx);
			storeCashUserInstrumentLogo.setSequence(sequence);
			userInstrumentLogos.add(storeCashUserInstrumentLogo);
		}
	}

	public static boolean isValidDate(final String date1, final String date2) {
		try {
			Date d1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS Z").parse(date1);
			Date d2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS Z").parse(date2);
			return d1.compareTo(d2) <= 0;
		}
		catch (Exception e) {
			log.error("invalid date format received {}. Exception :{}", date1, e);
			return false;
		}
	}

	public static String getValueForInputParamForUrl(final String field, final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn) {
		switch (field) {
			case ACC_REF_NUM:
				if (Objects.nonNull(participant.getBankData())
						&& StringUtils.isNotBlank(participant.getBankData().getAccRefNum())) {
					return participant.getBankData().getAccRefNum();
				}
				return null;
			case ACCOUNT:
				if (Objects.nonNull(participant.getContextMap())) {
					return participant.getContextMap().get(RAW_ACCOUNT_NO);
				}
				return null;
			case IFSC_REGEX:
				if (Objects.nonNull(participant.getBankData())) {
					return participant.getBankData().getIfsc();
				}
				return null;
			case PAYEE_NAME:
				return participant.getName();
			case BANK_NAME:
				if (Objects.nonNull(participant.getBankData())) {
					return participant.getBankData().getBankName();
				}
				return null;
			case AMOUNT:
				return Currency.getCurrencyAmountInHigherDenomination(participant.getAmount(),
						participant.getCurrency());
			case REMARKS:
				return participant.getRemarks();
			case RECIPIENT:
				if (Objects.nonNull(participant.getWalletData())) {
					return participant.getWalletData().getWalletMobileNumber();
				}
				if (Objects.nonNull(participant) && Objects.nonNull(participant.getMobileData())) {
					return Utility.getValidPhoneNumber(participant.getMobileData().getMobileNumber());
				}
				return null;
			case QR_ID:
				if (Objects.nonNull(txn.getContextMap())) {
					return txn.getContextMap().get(QR_CODE_ID);
				}
				return null;
			case PAYEE_VPA:
				if (Objects.nonNull(participant.getUpiData())) {
					return participant.getUpiData().getVpa();
				}
				return null;
			case RPT_PYMNT_BENEF_ACCT_NUM:
				if (Objects.nonNull(participant)) {
					if (Objects.nonNull(participant.getContextMap())
							&& !Objects.isNull(participant.getContextMap().get(BENEF_ACCT_NUM))) {
						return participant.getContextMap().get(BENEF_ACCT_NUM);
					}
					if (participant.getBankData() != null && participant.getBankData().getAccNumber() != null) {
						return participant.getBankData().getAccNumber();
					}
				}
				log.info("Setting Repeat payment as null because BenefAccNum is null TxnId: {}", txn.getTxnId());
				return null;

			case RPT_PYMNT_REMIT_ACCT_NUM:
				if (Objects.nonNull(participant.getContextMap())
						&& !Objects.isNull(participant.getContextMap().get(REMIT_ACCT_NUM))) {
					return participant.getContextMap().get(REMIT_ACCT_NUM);
				}
				if (participant.getBankData() != null && participant.getBankData().getAccNumber() != null) {
					return participant.getBankData().getAccNumber();
				}
				log.info("Setting Repeat payment as null because RemitterAcctNum is null TxnId: {}", txn.getTxnId());
				return null;
			case RPT_PYMNT_BENEF_IFSC:
				if (Objects.nonNull(participant)) {
					if (Objects.nonNull(participant.getContextMap())
							&& !Objects.isNull(participant.getContextMap().get(BENEF_IFSC))) {
						return participant.getContextMap().get(BENEF_IFSC);
					}
					if (participant.getBankData() != null && participant.getBankData().getIfsc() != null) {
						return participant.getBankData().getIfsc();
					}
				}
				log.info("Setting Repeat payment as null because BenefIfsc is null TxnId: {}", txn.getTxnId());
				return null;

			case RPT_PYMNT_REMIT_IFSC:
				if (Objects.nonNull(participant.getContextMap())
						&& !Objects.isNull(participant.getContextMap().get(REMIT_IFSC))) {
					return participant.getContextMap().get(REMIT_IFSC);
				}
				if (participant.getBankData() != null && participant.getBankData().getIfsc() != null) {
					return participant.getBankData().getIfsc();
				}
				log.info("Setting Repeat payment as null because RemitterIfsc is null TxnId: {}", txn.getTxnId());
				return null;
			case RPT_PYMNT_BENEF_NAME:
				if (Objects.nonNull(participant)) {
					if (Objects.nonNull(participant.getContextMap())
							&& !Objects.isNull(participant.getContextMap().get(BENEF_NAME))) {
						return participant.getContextMap().get(BENEF_NAME);
					}
					if (participant.getName() != null) {
						return participant.getName();
					}
				}
				log.info("Setting Repeat payment as null because BenefName is null TxnId: {}", txn.getTxnId());
				return null;

			case RPT_PYMNT_REMIT_NAME:
				if (Objects.nonNull(participant.getContextMap())
						&& !Objects.isNull(participant.getContextMap().get(REMIT_NAME))) {
					return participant.getContextMap().get(REMIT_NAME);
				}
				if (participant.getName() != null) {
					return participant.getName();
				}
				log.info("Setting Repeat payment as null because RemitterName is null TxnId: {}", txn.getTxnId());
				return null;
			case RPT_PYMNT_BENEF_BANK_NAME:
				if (Objects.nonNull(participant)) {
					if (Objects.nonNull(participant.getContextMap())
							&& participant.getContextMap().get(BENEF_BANK_NAME) != null) {
						return participant.getContextMap().get(BENEF_BANK_NAME);
					}
					if (participant.getBankData() != null && participant.getBankData().getBankName() != null) {
						return participant.getBankData().getBankName();
					}
				}
				return null;

			case RPT_PYMNT_REMIT_BANK_NAME:
				if (Objects.nonNull(participant.getContextMap())
						&& participant.getContextMap().get(REMIT_BANK_NAME) != null) {
					return participant.getContextMap().get(REMIT_BANK_NAME);
				}
				if (participant.getBankData() != null && participant.getBankData().getBankName() != null) {
					return participant.getBankData().getBankName();
				}
				return null;
			case MERCHANT_VPA:
				if (Objects.nonNull(participant) && !Objects.isNull(participant.getUpiData())
						&& !Objects.isNull(participant.getUpiData().getVpa())) {
					return participant.getUpiData().getVpa();
				}
				log.info("Setting Repeat payment as null because MerchantVPA is null TxnId: {}", txn.getTxnId());
				return null;

			case MID:
				if (Objects.nonNull(participant) && Objects.nonNull(participant.getMerchantData())
						&& Objects.nonNull(participant.getMerchantData().getMerchantId())) {
					return participant.getMerchantData().getMerchantId();
				}
				log.info(
						"Repeat payment will not be null if mid is not present, it will get created from VPA for TxnId: {}",
						txn.getTxnId());
				return null;

			case RPT_PYMNT_MERCHANT_NAME:
				if (Objects.nonNull(participant) && !Objects.isNull(participant.getName())) {
					return participant.getName();
				}
				log.info("Setting Repeat payment as null because MerchantName is null TxnId: {}", txn.getTxnId());
				return null;
			default:
		}
		return null;
	}

	public static String setRefIdsForWallet(final List<TransformedTransactionHistoryDetail> detailList) {
		TransformedTransactionHistoryDetail walletTxn = null;
		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (TransactionSource.WALLET.getTransactionSourceKey().equals(detail.getStreamSource())) {
				walletTxn = detail;
				break;
			}
		}
		if (Objects.isNull(walletTxn)) {
			return null;
		}
		return walletTxn.getTxnId();
	}

	public static String getRrn(final TransformedParticipant participant) {
		String rrn = null;
		if (participant.getContextMap() != null
				&& !org.apache.commons.lang3.StringUtils.isEmpty(participant.getContextMap().get(RRN))) {
			rrn = participant.getContextMap().get(RRN);
		}
		else if (participant.getBankData() != null
				&& !org.apache.commons.lang3.StringUtils.isEmpty(participant.getBankData().getRrn())) {
			rrn = participant.getBankData().getRrn();
		}
		return rrn;
	}

	public static String getRrnFromUpiDoc(final TransformedTransactionHistoryDetail txn) {
		String rrn = null;
		if (Objects.nonNull(txn) && ObjectUtils.isNotEmpty(txn.getContextMap())) {
			rrn = txn.getContextMap().get(RRN);
		}
		return rrn;
	}

	public static String getRefundRefNoFromPgDoc(final TransformedTransactionHistoryDetail txn) {
		String refundRefNo = null;
		if (txn.getParticipants() != null) {
			for (TransformedParticipant participant : txn.getParticipants()) {
				if (participant.getContextMap() != null && participant.getContextMap().containsKey(BANK_TXN_ID)) {
					refundRefNo = participant.getContextMap().get(BANK_TXN_ID);
					break;
				}
			}
		}
		return refundRefNo;
	}

	public static String getInitials(final String fullName) {
		if (StringUtils.isBlank(fullName)) {
			return fullName;
		}
		String nameWithSingleSpaces = GenericUtility.singleSpacing(fullName).trim();
		StringBuilder initials = new StringBuilder();
		initials.append(Character.toUpperCase(nameWithSingleSpaces.charAt(0)));
		int lastIndexOfSpace = nameWithSingleSpaces.lastIndexOf(' ');
		if (lastIndexOfSpace != -1) {
			initials.append(Character.toUpperCase(nameWithSingleSpaces.charAt(lastIndexOfSpace + 1)));
		}
		return initials.toString();
	}

	public static String getLastNcharacters(final String input, final int numberOfLastDigitsRequired) {
		if (ObjectUtils.isEmpty(input)) {
			return input;
		}
		if ((numberOfLastDigitsRequired <= 0) || (numberOfLastDigitsRequired > input.length())) {
			return input;
		}
		return input.substring(input.length() - numberOfLastDigitsRequired);

	}

	public static String getInstrumentDetailForUpiViaCc(final String bankName, final String cardNum) {
		if (StringUtils.isBlank(bankName)) {
			return CREDIT_CARD;
		}
		if (StringUtils.isBlank(cardNum) || cardNum.length() < 2) {
			return bankName;
		}
		String cardNum1 = cardNum.substring(cardNum.length() - 2);
		return bankName + SPACE + HYPHEN + SPACE + cardNum1;
	}

	// Extra check on entityId needed in this method (participant's entityId should be
	// same as order level entityId).
	/*
	 * public static String getClosingBalance(final TransformedTransactionHistoryDetail
	 * esDto, final String walletType) { List<TransformedParticipant> participantList =
	 * esDto.getParticipants(); String closingBalance = null;
	 *
	 * if (StringUtils.isBlank(walletType)) { return closingBalance; }
	 *
	 * for (TransformedParticipant participant : participantList) { if
	 * (Objects.nonNull(participant.getWalletData())) { if
	 * (Objects.equals(participant.getWalletData().getWalletType(),
	 * Integer.parseInt(walletType))) { closingBalance =
	 * participant.getWalletData().getClosingBalance(); break; } } } return
	 * closingBalance; }
	 */

	public static void setPaginationForUpdates(final RepoResponseSearchApiDto repoResponse,
			final SearchContext searchContext) {
		if (!searchContext.isForUpdates() && searchContext.getPageNo() != 1) {
			return; // Not setting fromUpdatedDate for pageNo > 1 of searchAPI
		}
		if (searchContext.getApiVersion() != null
				&& (searchContext.getApiVersion().equalsIgnoreCase(ApiVersion.v1.name())
						|| searchContext.getApiVersion().equalsIgnoreCase(ApiVersion.v2.name()))) {
			return;
		}
		if (repoResponse.getPaginationParams() == null) {
			repoResponse.setPaginationParams(new PaginationParams());
		}
		if (searchContext.isForUpdates() && !searchContext.isInvalidForUpdates()
				&& repoResponse.getPaginationParams().getPageNo() == -1) {
			repoResponse.getPaginationParams().setPageNo(null);
		}
		long updatedDate = 0;
		if (searchContext.getFromUpdatedDate() != null) {
			updatedDate = searchContext.getFromUpdatedDate();
		}
		/*
		 * Added this check only for listing API for safer side because in PTH-1263 we are
		 * handling listing api only
		 */
		if (!searchContext.isForUpdates() && repoResponse.getTransformedTransactionHistoryDetailsIterable().isEmpty()) {
			repoResponse.getPaginationParams().setFromUpdatedDate(String.valueOf(searchContext.getToDate()));
			return;
		}
		Optional<Long> maxUpdatedDate = repoResponse.getTransformedTransactionHistoryDetailsIterable()
			.stream()
			.map(TransformedTransactionHistoryDetail::getDocUpdatedDate)
			.max(Comparator.comparing(Long::longValue));
		if (maxUpdatedDate.isPresent()) {
			updatedDate = maxUpdatedDate.get();
		}
		if (repoResponse.getPaginationParams() == null) {
			repoResponse.setPaginationParams(new PaginationParams());
		}
		repoResponse.getPaginationParams().setFromUpdatedDate(String.valueOf(updatedDate));
	}

	public static String getTransactionPurpose(final TransformedTransactionHistoryDetail detailNeededForTxn) {
		if (detailNeededForTxn != null) {
			return detailNeededForTxn.getContextMap() != null
					? detailNeededForTxn.getContextMap().get(TRANSACTION_PURPOSE) : null;
		}
		return null;
	}

	public static long getTotalPages(final Long totalHits, final int pageSize) {
		return totalHits % pageSize == 0 ? totalHits / pageSize : (totalHits / pageSize) + 1;
	}

	public static String getExecutionNo(final TransformedTransactionHistoryDetail detailNeededForTxn) {
		if (detailNeededForTxn != null) {
			return detailNeededForTxn.getContextMap() != null ? detailNeededForTxn.getContextMap().get(EXECUTION_NO)
					: null;
		}
		return null;
	}

	public static String getMandateAmount(final TransformedTransactionHistoryDetail detailNeededForTxn) {
		if (detailNeededForTxn != null) {
			return detailNeededForTxn.getContextMap() != null ? detailNeededForTxn.getContextMap().get(MANDATE_AMOUNT)
					: null;
		}
		return null;
	}

	public static boolean isTxnPoweredByUpi(final DetailApiResponseV2 responseV2) {
		if (Objects.nonNull(responseV2) && Objects.nonNull(responseV2.getReferenceIds())) {
			List<String> referenceIds = responseV2.getReferenceIds();
			Optional<String> upiReferenceId = referenceIds.stream()
				.filter(referenceId -> StringUtils.isNotBlank(referenceId) && referenceId.contains(UPI_REFERENCE_NO))
				.findFirst();
			return upiReferenceId.isPresent();
		}
		return false;
	}

	public static boolean isTxnPoweredByUpi(final TransformedTransactionHistoryDetail txn) {
		if (Objects.nonNull(txn) && Objects.nonNull(txn.getParticipants())) {
			for (TransformedParticipant participant : txn.getParticipants()) {
				if (StringUtils.isNotBlank(txn.getEntityId()) && txn.getEntityId().equals(participant.getEntityId())
						&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					return true;
				}
			}
		}
		return false;
	}

	public static boolean isTxnPoweredByUpi(final TransformedParticipant participant) {
		return Objects.nonNull(participant)
				&& EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
				&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem());
	}

	public static boolean isTxnPoweredByUpi(final DetailApiResponseV3 responseV3) {
		if (Objects.nonNull(responseV3) && Objects.nonNull(responseV3.getReferenceIds())) {
			List<String> referenceIds = responseV3.getReferenceIds();
			Optional<String> upiReferenceId = referenceIds.stream()
				.filter(referenceId -> StringUtils.isNotBlank(referenceId) && referenceId.contains(UPI_REFERENCE_NO))
				.findFirst();
			return upiReferenceId.isPresent();
		}
		return false;
	}

	public static Boolean isRecurringMandate(final TransformedTransactionHistoryDetail detail) {
		return TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey().equals(detail.getMainTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey().equals(detail.getMainTxnType());
	}

	public static Boolean isRecurringMandateAndUpiCc(final TransformedTransactionHistoryDetail detail) {
		return isRecurringMandate(detail) && isUpiViaCcTxn(detail);
	}

	public static boolean isUpiViaCcTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && ObjectUtils.isNotEmpty(txn.getContextMap())
				&& CommonConstants.TRUE.equalsIgnoreCase(txn.getContextMap().get(UPI_VIA_CC_FLAG));
	}

	public static boolean isTxnPoweredByWallet(final DetailApiResponseV3 responseV3) {
		if (Objects.nonNull(responseV3) && Objects.nonNull(responseV3.getReferenceIds())) {
			List<String> referenceIds = responseV3.getReferenceIds();
			Optional<String> walletReferenceId = referenceIds.stream()
				.filter(referenceId -> StringUtils.isNotBlank(referenceId) && referenceId.contains(WALLET_TXN_ID))
				.findFirst();
			return walletReferenceId.isPresent();
		}
		return false;
	}

	public static void formatDetailResponse(final DetailApiResponse detailApiResponse) {
		ClientStatusEnum status = ClientStatusEnum.valueOf(detailApiResponse.getStatus());
		List<InstrumentDto> firstInstrument = detailApiResponse.getFirstInstrument();
		if (firstInstrument != null) {
			firstInstrument.forEach(instrumentDTO -> instrumentDTO.setName(formatString(instrumentDTO.getName())));
			if (ClientStatusEnum.SUCCESS.equals(status)) {
				// removing pending or failed instruments
				detailApiResponse.setFirstInstrument(firstInstrument.stream()
					.filter(instrumentDTO -> status.getStatusKey().equals(instrumentDTO.getInstrumentStatus()))
					.collect(Collectors.toList()));
			}
		}

		List<InstrumentDto> secondInstrument = detailApiResponse.getSecondInstrument();
		if (secondInstrument != null) {
			secondInstrument.forEach(instrumentDTO -> instrumentDTO.setName(formatString(instrumentDTO.getName())));
			if (ClientStatusEnum.SUCCESS.equals(status)) {
				// removing pending or failed instruments
				detailApiResponse.setSecondInstrument(secondInstrument.stream()
					.filter(instrumentDTO -> status.getStatusKey().equals(instrumentDTO.getInstrumentStatus()))
					.collect(Collectors.toList()));
			}
		}
	}

	public static boolean isStoreCashTxn(final TransformedTransactionHistoryDetail tthd) {
		/*
		 * when storecash earned txn is onboarded, might need to add another condition
		 */
		return tthd.getParticipants()
			.stream()
			.anyMatch((participant) -> PaymentSystemEnum.STORE_CASH.getPaymentSystemKey()
				.equals(participant.getPaymentSystem()));
	}

	public static boolean getPoweredByUrlEnabledInFooterLogo() {
		return poweredByUrlEnabledInFooterLogo;
	}

	public static boolean isUserPpblParticipant(final TransformedTransactionHistoryDetail tthd) {
		TransactionSource transactionSource = TransactionSource.getTransactionSourceEnumByKey(tthd.getStreamSource());
		switch (transactionSource) {
			case PPBL:
			case TS:
				return true;
			case PG:
			case PPBL_PG:
				return tthd.getParticipants()
					.stream()
					.anyMatch(participant -> tthd.getEntityId().equals(participant.getEntityId())
							&& PaymentSystemEnum.BANK.getPaymentSystemKey().equals(participant.getPaymentSystem()));
			case UPI:
				return tthd.getParticipants()
					.stream()
					.anyMatch(participant -> tthd.getEntityId().equals(participant.getEntityId())
							&& ObjectUtils.isNotEmpty(participant.getBankData()) && StringUtils
								.startsWithIgnoreCase(participant.getBankData().getIfsc(), PPBL_IFSCCODE_IDENTIFIER));
			default:
				return false;
		}
	}

	/*
	 * This method check if the detail api hit is from chat. Then we check if txn would be
	 * present in ES or not by checking lag cache. In case txn wouldn't be present
	 * throwing an error.
	 */
	public static void isReqNeedToBeServed(final String txnSource,
			final ExtraDetailRequestFields extraDetailRequestFields, final DetailInputParams detailInputParams) {

		String openSource = extraDetailRequestFields.getOpenSource();
		String detailApiVersion = detailInputParams.getDetailCallVersion();
		List<String> openSourceList = configurablePropertiesHolder
			.getProperty(ConfigPropertiesEnum.TXN_STREAM_LAG_CACHE_ENABLED_OPEN_SOURCES, ArrayList.class);

		if (!openSourceList.isEmpty() && openSourceList.contains(extraDetailRequestFields.getOpenSource())) {
			boolean isReqNeedToBeServed = LagDataCacheService.isReqNeedToBeServed(txnSource,
					extraDetailRequestFields.getTxnDate());
			if (!isReqNeedToBeServed) {
				log.info("Chat detail api request rejected by lag cache for txn source : {} and txnDate : {}",
						txnSource, extraDetailRequestFields.getTxnDate());
				metricsPublishHelper.publishTxnStreamLagCacheRejectedStats(detailApiVersion, openSource, txnSource);
				if (configurablePropertiesHolder.getProperty(TXN_STREAM_LAG_BASED_TXN_DETAILS_REJECTION_ENABLED,
						Boolean.class)) {
					throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALIDATED_BY_LAG_CACHE);
				}
			}
			else {
				metricsPublishHelper.publishTxnStreamLagCacheAllowedStats(detailApiVersion, openSource, txnSource);
			}
		}
	}

	public static TransformedParticipant getVpa2AccountOtherParticipant(
			final TransformedTransactionHistoryDetail tthd) {
		// This method will return other participant in case of VPA2ACCOUNT Upi
		// transaction when bank data is not null

		if (TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& !CollectionUtils.isEmpty(tthd.getParticipants())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (Objects.nonNull(participant) && Objects.isNull(participant.getEntityId())
						&& Objects.nonNull(tthd.getOtherPartyEntityId()) && (Objects.nonNull(participant.getBankData())
								&& StringUtils.isNotBlank(participant.getBankData().getBankName()))) {
					return participant;
				}
			}
		}
		return null;
	}

	public static TransformedParticipant getNonPaytmVpaUpiParticipant(final TransformedTransactionHistoryDetail tthd) {
		// This method will return the other participant in case of other participant has
		// non paytm vpa

		if (TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& !CollectionUtils.isEmpty(tthd.getParticipants())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (Objects.nonNull(participant) && Objects.isNull(participant.getEntityId())
						&& Objects.nonNull(participant.getUpiData())
						&& Objects.nonNull(participant.getUpiData().getVpa())
						&& !GenericUtilityExtension.isVpa2AccountTxn(tthd)) {
					return participant;
				}
			}
		}
		return null;
	}

	public static TransformedParticipant get3pMerchantParticipant(final TransformedTransactionHistoryDetail tthd) {
		// This method will return the merchant participant if the merchant is 3rd party
		// In case of 3P merchant only UPI event is received

		if (TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& !CollectionUtils.isEmpty(tthd.getParticipants())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (Objects.nonNull(participant)
						&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())
						&& Objects.isNull(participant.getEntityId()) && Objects.nonNull(participant.getUpiData())
						&& Objects.nonNull(participant.getUpiData().getVpa())) {
					return participant;
				}
			}
		}
		return null;
	}

	public static TransformedParticipant getOtherPartyParticipant(final TransformedTransactionHistoryDetail txn) {
		// Sample url
		// "paytmmp://chat?featuretype=start_chat&userType=CUSTOMER&custId=12312312&custName=SomeGuy&custPhone=7949334953"

		for (TransformedParticipant participant : txn.getParticipants()) {
			if (EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& Objects.nonNull(participant.getEntityId())
					&& (Objects.isNull(participant.getContextMap())
							|| !participant.getContextMap().containsKey(IS_VIRTUAL_ENTITY_ID))
					&& !(txn.getEntityId().equals(participant.getEntityId())
							&& txn.getTxnIndicator().equals(participant.getTxnIndicator()))) {
				return participant;
			}
		}
		return null;
	}

	public static TransformedParticipant getOtherMerchantParticipant(final TransformedTransactionHistoryDetail tthd) {
		// This method will return other participant only for Paytm Ofus Merchant.

		if ((TransactionSource.isPgTypeSource(tthd.getStreamSource()) || UtilityExtension.isUpiCollectOrIntentTxn(tthd))
				&& MERCHANT_TRANSACTION_TYPES
					.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()))
				&& !CollectionUtils.isEmpty(tthd.getParticipants())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (Objects.nonNull(participant) && Objects.nonNull(participant.getEntityId())
						&& Objects.nonNull(tthd.getEntityId())
						&& Boolean.FALSE.equals(tthd.getEntityId().equals(participant.getEntityId()))
						&& Objects.nonNull(participant.getMerchantData())
						&& Objects.nonNull(participant.getMerchantData().getMerchantId())
						&& Objects.nonNull(participant.getName())
						&& Boolean.FALSE.equals(MerchantTypeEnum.ONUS.getMerchantTypeKey()
							.equals(participant.getMerchantData().getMerchantType()))) {
					return participant;
				}
			}
		}
		return null;
	}

	public static final String getLastFourDigitOfAccNo(final String accountNo) {
		return accountNo.replaceAll("\\s", "").substring(1);
	}

	public static String fetchPayeeVpa(final TransformedTransactionHistoryDetail esDto) {
		TransformedParticipant payeeParticipant = Utility.getPayeeParticipant(esDto);
		if (payeeParticipant != null && payeeParticipant.getUpiData() != null
				&& StringUtils.isNotBlank(payeeParticipant.getUpiData().getVpa())) {
			return payeeParticipant.getUpiData().getVpa();
		}
		return null;
	}

	public static Boolean isOnusMerchant(final TransformedTransactionHistoryDetail tthd) {
		TransactionSource transactionSource = TransactionSource.getTransactionSourceEnumByKey(tthd.getStreamSource());
		switch (transactionSource) {
			case OMS:
				return Boolean.TRUE;
			case PG:
				if (isThisOmsOrder(tthd)) {
					return Boolean.TRUE;
				}
				return Boolean.FALSE;
			case UPI:
				if (Objects.nonNull(tthd.getContextMap())
						&& Objects.nonNull(tthd.getContextMap().get(ONUS_MERCHANT_FLAG_KEY_FROM_UPI))
						&& TRUE.equalsIgnoreCase(tthd.getContextMap().get(ONUS_MERCHANT_FLAG_KEY_FROM_UPI))) {

					return Boolean.TRUE;
				}
				return Boolean.FALSE;
			case WALLET:
				TransformedParticipant otherParticipant = getOtherParticipant(tthd);
				if (Objects.nonNull(otherParticipant) && Objects.nonNull(otherParticipant.getMerchantData())
						&& Objects.nonNull(otherParticipant.getMerchantData().getMerchantType())
						&& otherParticipant.getMerchantData()
							.getMerchantType()
							.equals(MerchantTypeEnum.ONUS.getMerchantTypeKey())) {
					return Boolean.TRUE;
				}
				return Boolean.FALSE;
			default:
				return Boolean.FALSE;
		}
	}

	public static boolean isWalletAutomaticAddMoneyTxn(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || CollectionUtils.isEmpty(txn.getContextMap())) {
			return false;
		}

		if (TransactionSource.WALLET.getTransactionSourceKey().equals(txn.getStreamSource())
				&& TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(txn.getTxnType())) {
			String walletTxnType = txn.getContextMap().getOrDefault(WALLET_TXN_TYPE, null);
			return StringUtils.isNotEmpty(walletTxnType) && WALLET_AUTOMATIC_TXN_TYPE.equalsIgnoreCase(walletTxnType);
		}
		return false;
	}

	public static String getUpiErrorCodes(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant) {
		String errorCode = null;
		if (ObjectUtils.isNotEmpty(esDto.getContextMap())) {
			errorCode = getErrorCodeFromContextMap(esDto.getContextMap());
		}
		if (StringUtils.isBlank(errorCode) && ObjectUtils.isNotEmpty(participant.getContextMap())) {
			errorCode = getErrorCodeFromContextMap(participant.getContextMap());
		}
		return errorCode;
	}

	private static String getErrorCodeFromContextMap(final Map<String, String> contextMap) {
		// This can be made more readable by using libs like StringUtils.firstNonEmpty.
		// But it has memory overhead
		// as apache StringUtils initializes array with size 16 to perform this op.
		List<String> errorCodeFields = Arrays.asList(ERROR_CODE, RESULT_CODE_ID, NPCI_RESP_CODE);
		for (String key : errorCodeFields) {
			if (StringUtils.isNotBlank(contextMap.get(key))) {
				return contextMap.get(key);
			}
		}
		return null;
	}

	public static boolean isDeafTxn(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || Objects.isNull(txn.getContextMap())) {
			return false;
		}

		return TransactionTypeEnum.SYSTEM_DEBIT
			.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getTxnType()))
				&& "118".equalsIgnoreCase(txn.getContextMap().get("walletTxnType"))
				&& "115".equalsIgnoreCase(txn.getContextMap().get("walletBusinessTxnType"));
	}

	public static final String getFooterLogoTxnType(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail tthd) {
		if (isRecurringMandateAndUpiCc(tthd)) {
			return RECURRING_MANDATE_AND_UPI_CC;
		}
		if (isRecurringMandateTxn(tthd)) {
			return RECURRING_MANDATE;
		}
		if (isUpiViaCcTxn(tthd)) {
			return UPI_CC;
		}
		if (isTxnPoweredByUpi(tthd)) {
			return UPI;
		}
		if (isTxnPoweredByWallet(detailApiResponseV3)) {
			return WALLET;
		}
		if (isUserPpblParticipant(tthd)) {
			return BANK;
		}
		return "";
	}

	public static boolean isVpa2AccountTxn(final TransformedTransactionHistoryDetail txn) {
		return Objects.nonNull(txn) && !CollectionUtils.isEmpty(txn.getContextMap())
				&& txn.getContextMap().containsKey(UPI_TXN_CATEGORY)
				&& VPA2ACCOUNT.equalsIgnoreCase(txn.getContextMap().get(UPI_TXN_CATEGORY));
	}

	/**
	 * This method will return repeat payment CTA for VpaToVpa Category based on UPI
	 * handle. if UPI handle is paytm then it will return mobile based repeat payment CTA
	 * otherwise Vpa based deeplink
	 * @param fieldsInVpaToVpaUrl
	 * @param rptPmtVpaToVpaUrl
	 * @param fieldsInMobileNoUrl
	 * @param rptPmtMobileNoUrl
	 * @param secUserParticipant
	 * @param txn
	 * @param ctaLabel
	 * @return
	 */
	public static RepeatPayment getRepeatPaymentForVpaToVpaCategory(final Map<String, Boolean> fieldsInVpaToVpaUrl,
			final String rptPmtVpaToVpaUrl, final Map<String, Boolean> fieldsInMobileNoUrl,
			final String rptPmtMobileNoUrl, final TransformedParticipant secUserParticipant,
			final TransformedTransactionHistoryDetail txn, final String ctaLabel) {
		// List of black listed handles for vpa repeat payment URL
		List<String> listOfBlackListedHandlesForVpaRptPmt = configurablePropertiesHolder
			.getProperty(BLACKLISTED_HANDLES_FOR_VPA_RPT_PMT, List.class);

		String vpaHandle = null;

		if (Objects.nonNull(secUserParticipant.getUpiData())
				&& StringUtils.isNotBlank(secUserParticipant.getUpiData().getVpa())) {
			vpaHandle = secUserParticipant.getUpiData().getVpa().split("@")[1];
		}

		if (listOfBlackListedHandlesForVpaRptPmt.contains(vpaHandle)) {
			// Mobile no deeplink enabled for vpa to vpa P2P txns or not
			Boolean mobileNoDeeplinkEnabledForVpa2Vpa = configurablePropertiesHolder
				.getProperty(MOBILE_NO_DEEPLINK_ENABLED_FOR_VPA2VPA, Boolean.class);
			if (Boolean.TRUE.equals(mobileNoDeeplinkEnabledForVpa2Vpa)) {
				return GenericUtility.getRepeatPaymentDetails(fieldsInMobileNoUrl, rptPmtMobileNoUrl,
						secUserParticipant, txn, ctaLabel);
			}
		}
		else {
			return GenericUtility.getRepeatPaymentDetails(fieldsInVpaToVpaUrl, rptPmtVpaToVpaUrl, secUserParticipant,
					txn, ctaLabel);
		}
		return null;
	}

	public static boolean isUpiViaCcFilterRequest(final Map<String, String> paramMap) {

		return (ObjectUtils.isNotEmpty(paramMap) && paramMap.containsKey(IS_FILTER_APPLIED)
				&& paramMap.containsKey(PAYMENT_SYSTEM)
				&& paramMap.get(PAYMENT_SYSTEM).contains(PaymentSystemEnum.UPI_CREDIT_CARD.getPaymentSystemValue()));
	}

	public static boolean isOnusVerticalFilterRequest(final Map<String, String> paramMap) {
		return (ObjectUtils.isNotEmpty(paramMap) && paramMap.containsKey(IS_FILTER_APPLIED)
				&& (paramMap.containsKey(VERTICAL_NAME) || paramMap.containsKey(VERTICAL_ID)));
	}

	/**
	 * Retrieves the Elasticsearch host type from the MDC context. This value is used for
	 * metrics purpose across different APIs.
	 * @return The ES host type from MDC context, or "Unknown_ES_Host" if not found
	 */
	public static String getEsHostType() {
		return Optional.ofNullable(MDC.getCopyOfContextMap())
			.map(context -> MDC.get(ES_HOST_TYPE))
			.orElse("Unknown_ES_Host");
	}

	public static boolean isShowHiddenTxnsFilterRequest(final Map<String, String> paramMap) {
		return ObjectUtils.isNotEmpty(paramMap) && TRUE.equalsIgnoreCase(paramMap.get(IS_FILTER_APPLIED))
				&& TRUE.equalsIgnoreCase(paramMap.get(SHOW_HIDDEN_TXN));
	}

	public static boolean isDateFilterRequest(final Map<String, String> paramMap) {
		return ObjectUtils.isNotEmpty(paramMap) && paramMap.containsKey(ES_DATE_RANGE);
	}

	public static Integer getExpiryTimeForNtuCacheData() {
		Integer nonTransactingUserCacheExpiryInDays = configurablePropertiesHolder
			.getProperty(ConfigPropertiesEnum.NON_TRANSACTING_USER_CACHE_EXPIRY_IN_DAYS, Integer.class);
		return nonTransactingUserCacheExpiryInDays * 24 * 60 * 60;
	}

}
