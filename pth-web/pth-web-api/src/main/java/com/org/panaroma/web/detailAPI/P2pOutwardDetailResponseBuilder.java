package com.org.panaroma.web.detailAPI;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.PspInfo;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.*;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.config.BankConfig;
import com.org.panaroma.web.config.RptCodeConfig;
import com.org.panaroma.web.cstData.CstDataUtility;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.web.utility.DataValidationUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.StatusLogoUtility;
import com.org.panaroma.web.utility.UpiLiteViewUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.InternalCache;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.WebConstants.ACC_REF_NUM;
import static com.org.panaroma.commons.constants.WebConstants.AC_NO;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.BANK_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.BANK_TXN_ID;
import static com.org.panaroma.commons.constants.WebConstants.ERROR_MESSAGE;
import static com.org.panaroma.commons.constants.WebConstants.GV_PURCHASED;
import static com.org.panaroma.commons.constants.WebConstants.IMPS_REPORT_CODE;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_TO;
import static com.org.panaroma.commons.constants.WebConstants.ORDER_ID;
import static com.org.panaroma.commons.constants.WebConstants.PAID_SUCCESSFULLY;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_NAME;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_VPA;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_FAILED;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_PENDING;
import static com.org.panaroma.commons.constants.WebConstants.RECIPIENT;
import static com.org.panaroma.commons.constants.WebConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_DISABLED_BANK_AND_WALLET;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_ENABLED_FOR_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_ENABLED_FOR_VPA2ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RRN;
import static com.org.panaroma.commons.constants.WebConstants.TO_ACCOUNT_MODE;
import static com.org.panaroma.commons.constants.WebConstants.TO_MOBILE_MODE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_INITIATION_MODE;
import static com.org.panaroma.commons.constants.WebConstants.UPI_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_TXN_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.P2P;
import static com.org.panaroma.commons.constants.WebConstants.VPA2ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.VPA2VPA;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_TXN_ID;
import static com.org.panaroma.commons.utils.Utility.isSelfTransferTxn;
import static com.org.panaroma.web.utility.GenericUtility.getRefIdMap;
import static com.org.panaroma.web.utility.GenericUtility.getTxnPurpose;
import static com.org.panaroma.web.utility.GenericUtility.setDetailsFromBankData;
import static com.org.panaroma.web.utility.GenericUtility.setDetailsFromCardData;
import static com.org.panaroma.web.utility.GenericUtility.setDetailsFromUpiData;

@Component
@Log4j2
public class P2pOutwardDetailResponseBuilder extends AbstractTransactionTypeDetailResponseBuilder {

	@Value("${rpt.paymnt.to.mobile.url}")
	String rptPaymntToMobileUrl;

	@Value("${rpt.paymnt.url.p2p.vpa2vpa}")
	String rptPaymntUrlVpa2Vpa;

	@Value("${rpt.paymnt.url.p2p.vpa2account}")
	String rptPaymntUrlVpa2Account;

	@Value("${rpt.paymnt.url.imps.neft.xfer}")
	String rptPaymntUrlForImpsNeftAndXfer;

	private static final Map<String, Boolean> fieldsForVPA2ACCOUNT = new HashMap<>();

	private static final Map<String, Boolean> fieldsForVPA2VPA = new HashMap<>();

	private static final Map<String, Boolean> fieldsForToMobileRptPaymntUrl = new HashMap<>();

	private static final Map<String, Boolean> fieldsForImpsNeftAndXfer = new HashMap<>();

	static {
		fieldsForVPA2ACCOUNT.put(PAYEE_NAME, true);
		fieldsForVPA2ACCOUNT.put(ACC_REF_NUM, true);

		fieldsForVPA2VPA.put(PAYEE_NAME, true);
		fieldsForVPA2VPA.put(PAYEE_VPA, true);
		fieldsForVPA2VPA.put(AMOUNT, false);

		fieldsForToMobileRptPaymntUrl.put(RECIPIENT, true);

		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_NAME, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_ACCT_NUM, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_IFSC, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_BANK_NAME, false);
		fieldsForImpsNeftAndXfer.put(AMOUNT, false);
	}

	public static final String URL_NAME = "Pay Again";

	@Value("${rpt.payment.url.for.selfTransfer}")
	String rptPaymentUrlForSelfTransfer;

	@Autowired
	private BankConfig bankConfig;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private InternalCache internalCache;

	@Override
	public TransactionTypeEnum buildDetailApiResponseFor() {
		return TransactionTypeEnum.P2P_OUTWARD;
	}

	@Override
	public DetailApiResponse getResponse(final List<TransformedTransactionHistoryDetail> detailList, final String txnId)
			throws Exception {

		TransformedTransactionHistoryDetail txn = null; // for the txn that goes on the
														// listing page
		TransformedTransactionHistoryDetail detailNeededForTxn = null; // The txn which
																		// has the same
																		// txnID as whose
																		// detail is
																		// needed
		TransformedTransactionHistoryDetail cbsTxn = null;

		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (Boolean.TRUE.equals(detail.getShowInListing())) {
				txn = detail;
			}
			if (detail.getTxnId().equalsIgnoreCase(txnId)) {
				detailNeededForTxn = detail;
			}
			if (TransactionSource.PPBL.getTransactionSourceKey().equals(detail.getStreamSource())) {
				cbsTxn = detail;
			}
		}
		assert (txn != null && detailNeededForTxn != null);
		DetailApiResponse detailApiResponse = new DetailApiResponse();
		detailApiResponse.setStatus(detailNeededForTxn.getIsSource()
				? ClientStatusEnum.getStatusEnumByKey(detailNeededForTxn.getStatus()).toString()
				: ClientStatusEnum.PENDING.toString());
		detailApiResponse.setStatusLogoUrl(
				StatusLogoUtility.getStatusLogoUrl(ClientStatusEnum.valueOf(detailApiResponse.getStatus())));
		detailApiResponse.setDetailNarration(
				getDetailNarration(detailNeededForTxn, ClientStatusEnum.valueOf(detailApiResponse.getStatus())));
		detailApiResponse.setAmount(Currency.getCurrencyAmountInHigherDenomination(txn.getAmount(), txn.getCurrency()));
		detailApiResponse.setDateTime(DateTimeUtility.getDateTime(txn.getTxnDate()));
		detailApiResponse.setCurrency(Currency.getCurrencyByKey(txn.getCurrency()));
		detailApiResponse.setTxnIndicator(String.valueOf(txn.getTxnIndicator()));

		// setting notes in case status is not SUCCESS
		if (!ClientStatusEnum.SUCCESS.equals(ClientStatusEnum.valueOf(detailApiResponse.getStatus()))
				&& detailNeededForTxn.getContextMap() != null
				&& detailNeededForTxn.getContextMap().containsKey(ERROR_MESSAGE)) {
			detailApiResponse.setNotes(detailNeededForTxn.getContextMap().get(ERROR_MESSAGE));
		}

		List<InstrumentDto> creditInstruments = new ArrayList<>();
		List<InstrumentDto> debitInstruments = new ArrayList<>();
		Map<String, String> refIds = new HashMap<>();

		for (TransformedParticipant participant : txn.getParticipants()) {
			InstrumentDto instrumentDto = new InstrumentDto();
			instrumentDto.setCurrency(Currency.getCurrencyByKey(participant.getCurrency()));
			instrumentDto.setAmount(
					Currency.getCurrencyAmountInHigherDenomination(participant.getAmount(), participant.getCurrency()));
			instrumentDto.setInstrumentStatus(participant.getStatus());
			DetailApiUtil.setParticipantInfo(txn, participant, instrumentDto, buildDetailApiResponseFor(), false);
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				instrumentDto.setNarration("To");
				detailApiResponse.setRepeatPayment(getRepeatPaymentDetails(participant, txn));
				instrumentDto.setAdditionalDetail(participant.getRemarks());
				if (EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())) {
					GenericUtility.setImageData(instrumentDto, participant.getEntityId());
				}
				setData(instrumentDto, participant, false, refIds, detailList, detailNeededForTxn);
				// Adding logo Order in 2nd user instrument Dto
				List<Logo> logoOrder = GenericUtility.getListnDetailCommonLogos(participant, txn, true, false, null);
				instrumentDto.setLogoOrder(logoOrder);
				creditInstruments.add(instrumentDto);
			}
			else {
				instrumentDto.setNarration("From Your");
				// instrumentDTO.setAdditionalDetail(participant.getRemarks());
				setData(instrumentDto, participant, true, refIds, detailList, detailNeededForTxn);
				debitInstruments.add(instrumentDto);
			}
		}
		detailApiResponse.setFirstInstrument(creditInstruments);
		detailApiResponse.setSecondInstrument(debitInstruments);

		if (Utility.isWalletInterOpTxn(txn)) {
			String rrn = GenericUtilityExtension.getRrnFromUpiDoc(txn);
			if (org.apache.commons.lang3.StringUtils.isNotBlank(rrn)) {
				refIds.put(UPI_REFERENCE_NO, rrn);
			}
		}

		detailApiResponse.setReferenceIdMap(refIds);
		detailApiResponse.setReferenceIds(GenericUtility.convertRefIdMapToList(detailApiResponse.getReferenceIdMap()));
		if (cbsTxn != null && IMPS_REPORT_CODE.contains(cbsTxn.getContextMap().getOrDefault(REPORT_CODE, null))) {
			detailApiResponse.setCstorderItem(CstDataUtility.getCstData(cbsTxn, detailApiResponse));
		}
		else {
			detailApiResponse.setCstorderItem(CstDataUtility.getCstData(detailNeededForTxn, detailApiResponse));
		}
		return detailApiResponse;
	}

	public String getDetailNarration(final TransformedTransactionHistoryDetail detailNeededForTxn,
			final ClientStatusEnum status) {
		String purpose = getTxnPurpose(detailNeededForTxn.getParticipants(),
				TransactionIndicator.DEBIT.getTransactionIndicatorKey()); // special
																			// handling
																			// for gift
																			// voucher
																			// sent to
																			// other case.
		if (!StringUtils.isEmpty(purpose) && purpose.equalsIgnoreCase(GV_PURCHASED)) {
			switch (status) {
				case FAILURE:
					return "Gift Voucher Failed";
				case PENDING:
					return "Gift Voucher Pending";
				case SUCCESS:
				default:
					return "Gift Voucher Sent";
			}
		}
		switch (status) {
			case FAILURE:
				return PAYMENT_FAILED;
			case SUCCESS:
				return PAID_SUCCESSFULLY;
			case PENDING:
				return PAYMENT_PENDING;
			default:
				log.error("Unexpected status received status: {}, returning narration considering success", status);
				return "Money Sent";
		}
	}

	private void setData(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final boolean isDebit, final Map<String, String> refIds,
			final List<TransformedTransactionHistoryDetail> detailList,
			final TransformedTransactionHistoryDetail detailNeededForTxn) throws Exception {
		final String purpose = getTxnPurpose(detailNeededForTxn.getParticipants(),
				TransactionIndicator.DEBIT.getTransactionIndicatorKey());
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		if (paymentSystem == null) {
			log.error("unexpected payment system for P2P Outward detail response, participant entityId:{} ",
					participant.getEntityId());
			throw new RuntimeException("Unexpected payment system P2P Outward  detail response");
		}

		switch (paymentSystem) {
			case PG:
				if (isDebit) {
					if (GV_PURCHASED.equalsIgnoreCase(purpose)) {
						setGvData(instrumentDto, detailList, refIds);
					}
				}
				break;
			case WALLET:
				if (isDebit) {
					instrumentDto
						.setName(WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()));
					instrumentDto.setLogoUrl(LogoUtility.getLogo(instrumentDto.getName(), LogoType.WALLET_ICON));
				}
				else {
					if (GV_PURCHASED.equalsIgnoreCase(purpose)) {
						instrumentDto.setName(participant.getName());
						String logoUrl = participant.getLogoUrl() != null ? participant.getLogoUrl() : LogoUtility
							.getLogo(WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()),
									LogoType.WALLET_ICON);
						instrumentDto.setLogoUrl(logoUrl);
						instrumentDto.setInstrumentDetail(GenericUtility
							.getDroolsMaskedMobileNumber(participant.getWalletData().getWalletMobileNumber()));
						instrumentDto.setAdditionalDetail(participant.getRemarks());
					}
					else {
						instrumentDto.setName(participant.getName());
						instrumentDto.setInstrumentDetail(GenericUtility
							.getDroolsMaskedMobileNumber(participant.getWalletData().getWalletMobileNumber()));
						instrumentDto
							.setLogoUrl(participant.getLogoUrl() != null ? participant.getLogoUrl()
									: LogoUtility.getLogo(
											WalletTypesEnum
												.getWalletDisplayName(participant.getWalletData().getWalletType()),
											LogoType.WALLET_ICON));
					}
				}
				String refId = GenericUtilityExtension.setRefIdsForWallet(detailList);
				if (StringUtils.isNotBlank(refId)) {
					refIds.put(WALLET_TXN_ID, refId);
				}
				break;
			case BANK:
			case PPBL:
				if (isDebit) {
					if (Objects.nonNull(participant.getBankData())) {
						instrumentDto.setName(participant.getBankData().getBankName());
						instrumentDto.setInstrumentDetail(AC_NO + participant.getBankData().getAccNumber());
						instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
								participant.getBankData().getBankName()));
					}
				}
				else {
					instrumentDto.setName(participant.getName());
					instrumentDto.setLogoUrl(participant.getLogoUrl());

					// Todo: JiraId
					if (DataValidationUtility.isOtherPartyBankDetailsRequired(detailNeededForTxn)) {
						if (Objects.nonNull(participant.getBankData())) {
							String instrumentDetail = participant.getBankData().getBankName() + " " + AC_NO
									+ participant.getBankData().getAccNumber();
							if (!StringUtils.isBlank(instrumentDetail)) {
								instrumentDto.setInstrumentDetail(instrumentDetail);
							}
							if (StringUtils.isBlank(instrumentDto.getLogoUrl())) {
								instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
										participant.getBankData().getBankName()));
							}
						}
					}
					else {
						if (StringUtils.isBlank(instrumentDto.getLogoUrl())) {
							instrumentDto.setLogoUrl(LogoUtility.defaultLogo(PaymentSystemEnum.PPBL));
						}
					}
				}
				if (Objects.nonNull(participant.getBankData())) {
					refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
				}
				if (paymentSystem == PaymentSystemEnum.PPBL && detailNeededForTxn.getContextMap() != null) {
					String reportCode = detailNeededForTxn.getContextMap().get(REPORT_CODE);
					RptCodeConfig rptCodeConfig = GenericUtility.fetchRptCodeConfig(bankConfig, reportCode,
							detailNeededForTxn.getTxnIndicator());
					refIds.putAll(getRefIdMap(rptCodeConfig, detailNeededForTxn.getContextMap(), bankConfig));
				}
				break;
			case UPI:

				if (isDebit) {
					if (GV_PURCHASED.equalsIgnoreCase(purpose)) {
						setGvData(instrumentDto, detailList, refIds);
					}
					else if (UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(participant)) {
						UpiLiteViewUtility.setSecondInstrumentForDetailResponse(instrumentDto, P2P);
					}
					else if (Objects.nonNull(participant.getBankData())
							&& StringUtils.isNotBlank(participant.getBankData().getBankName())
							&& StringUtils.isNotBlank(participant.getBankData().getIfsc())
							&& StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
						instrumentDto.setName(participant.getBankData().getBankName());
						instrumentDto.setInstrumentDetail(
								AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
						instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
								participant.getBankData().getBankName()));
					}
				}
				else {
					instrumentDto.setName(participant.getName());
					if (GenericUtilityExtension.isVpa2AccountTxn(detailNeededForTxn)
							&& (isSelfTransferTxn(detailNeededForTxn)
									|| DataValidationUtility.isOtherPartyBankDetailsRequiredForVpa2AccountTxn())) {
						/*
						 * when txn is of Type - VPA2ACCOUNT 1. selfTransfer : true ->
						 * show bankDetail 2. selfTransfer : false -> show bankDetail
						 * based on config.
						 */
						instrumentDto.setLogoUrl(participant.getLogoUrl());
						if (Objects.nonNull(participant.getBankData())) {
							if (StringUtils.isNotBlank(participant.getBankData().getBankName())
									&& StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
								instrumentDto.setInstrumentDetail(participant.getBankData().getBankName() + " " + AC_NO
										+ getMaskedAccountNumber(participant.getBankData().getAccNumber()));
							}
							if (StringUtils.isBlank(instrumentDto.getLogoUrl())) {
								instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
										participant.getBankData().getBankName()));
							}
						}
					}
					else if (isSelfTransferTxn(detailNeededForTxn)
							|| DataValidationUtility.isOtherPartyBankDetailsRequired(detailNeededForTxn)) {
						/*
						 * Set bank Details or Upi Details if condition true
						 */
						instrumentDto.setInstrumentDetail(getInstrumentDetails(participant, detailNeededForTxn).trim());
						String logoUrl = LogoUtility.getUpiUrlString(participant);
						instrumentDto.setLogoUrl(logoUrl);
					}
					else {
						if (Objects.nonNull(participant.getUpiData())
								&& StringUtils.isNotBlank(participant.getUpiData().getVpa())
								&& !(GenericUtilityExtension.isVpa2AccountTxn(detailNeededForTxn))) {
							instrumentDto
								.setInstrumentDetail(getInstrumentDetails(participant, detailNeededForTxn).trim());
						}
						instrumentDto.setLogoUrl(LogoUtility.defaultLogo(PaymentSystemEnum.UPI));
					}
				}
				setRrn(refIds, participant);
				break;
			default:
				log.error("unexpected payment system for P2P Outward detail response, participant entityId:{} ",
						participant.getEntityId());
				throw new RuntimeException("Unexpected payment system P2P Outward  detail response");
		}
	}

	private String getInstrumentDetails(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail detailNeededForTxn) {
		StringBuilder instDetails = new StringBuilder();
		// format followed for instrumentDetails: %{bankName} A/C %{accountNo(masked)}
		// linked to %{vpa}
		// Todo: mention Jira Id here for removing here
		if (DataValidationUtility.isOtherPartyBankDetailsRequired(detailNeededForTxn)
				|| isSelfTransferTxn(detailNeededForTxn)) {
			if (participant.getBankData() != null) {
				// if bankName == null follow A/C %{accountNo(masked)} linked to %{vpa}
				if (StringUtils.isNotBlank(participant.getBankData().getBankName())) {
					instDetails.append(participant.getBankData().getBankName());
				}
				// if: AccountNo == null follow %{vpa}
				if (StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
					if (StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
						instDetails.append(" " + AC_NO);
						instDetails.append(getMaskedAccountNumber(participant.getBankData().getAccNumber()));
					}
					else {
						instDetails.setLength(0);
					}
				}
				else {
					instDetails.setLength(0);
				}
			}
		}
		if (participant.getUpiData() != null && StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
			if (StringUtils.isBlank(instDetails)) {
				String vpa = participant.getUpiData().getVpa();
				String pspHandle = Utility.getPspHandle(vpa);
				// Find the matching PSP info
				PspInfo pspInfo = internalCache.getPspInfo(pspHandle);

				if (pspInfo != null) {
					instDetails.append(vpa).append(" ").append(pspInfo.getPspDisplayText());
				}
				else {
					instDetails.append(vpa);
				}

			}
			else {
				instDetails.append(LINKED_TO);
				instDetails.append(participant.getUpiData().getVpa());
			}
		}
		return instDetails.toString();
	}

	private void setGvData(final InstrumentDto instrumentDto,
			final List<TransformedTransactionHistoryDetail> detailList, final Map<String, String> refIds) {

		TransformedTransactionHistoryDetail upiTxn = null;
		TransformedTransactionHistoryDetail pgTxn = null;
		for (TransformedTransactionHistoryDetail txn : detailList) {
			if (TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())) {
				upiTxn = txn;
			}
			if (TransactionSource.isPgTypeSource(txn.getStreamSource())) {
				pgTxn = txn;
			}
		}

		if (upiTxn != null) {
			for (TransformedParticipant debitUpiParticipant : upiTxn.getParticipants()) {
				if (TransactionIndicator.DEBIT.getTransactionIndicatorKey()
					.equals(debitUpiParticipant.getTxnIndicator())) {
					if (debitUpiParticipant.getBankData() != null) {
						setDetailsFromBankData(instrumentDto, debitUpiParticipant);
						setRrn(refIds, debitUpiParticipant);
						break;
					}
					else if (debitUpiParticipant.getUpiData() != null) {
						setDetailsFromUpiData(instrumentDto, debitUpiParticipant);
						setRrn(refIds, debitUpiParticipant);
						break;
					}
					DetailApiUtil.setParticipantInfo(upiTxn, debitUpiParticipant, instrumentDto,
							buildDetailApiResponseFor(), false);
				}
			}
			refIds.put(ORDER_ID, upiTxn.getOrderId());
		}
		else if (pgTxn != null) {
			for (TransformedParticipant debitPgParticipant : pgTxn.getParticipants()) {
				if (TransactionIndicator.DEBIT.getTransactionIndicatorKey()
					.equals(debitPgParticipant.getTxnIndicator())) {
					if (debitPgParticipant.getCardData() != null) {
						setDetailsFromCardData(instrumentDto, debitPgParticipant);
						setBankReferenceNum(refIds, debitPgParticipant);
					}
					else if (debitPgParticipant.getBankData() != null) {
						setDetailsFromBankData(instrumentDto, debitPgParticipant);
						setBankReferenceNum(refIds, debitPgParticipant);
						break;
					}
					else if (debitPgParticipant.getUpiData() != null) {
						setDetailsFromUpiData(instrumentDto, debitPgParticipant);
						setBankReferenceNum(refIds, debitPgParticipant);
						break;
					}
					DetailApiUtil.setParticipantInfo(pgTxn, debitPgParticipant, instrumentDto,
							buildDetailApiResponseFor(), false);
				}
			}
			refIds.put(ORDER_ID, pgTxn.getOrderId());
		}
	}

	private void setBankReferenceNum(final Map<String, String> refIds, final TransformedParticipant participant) {
		if (participant.getContextMap() != null && !StringUtils.isEmpty(participant.getContextMap().get(BANK_TXN_ID))) {
			refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
		}
		else if (participant.getContextMap() != null && !StringUtils.isEmpty(participant.getContextMap().get(RRN))) {
			refIds.put(BANK_REFERENCE_NO, participant.getContextMap().get(RRN));
		}
		else if (participant.getBankData() != null && !StringUtils.isEmpty(participant.getBankData().getBankTxnId())) {
			refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
		}
		else if (participant.getBankData() != null && !StringUtils.isEmpty(participant.getBankData().getRrn())) {
			refIds.put(BANK_REFERENCE_NO, participant.getBankData().getRrn());
		}
	}

	private void setRrn(final Map<String, String> refIds, final TransformedParticipant participant) {
		String rrn = GenericUtilityExtension.getRrn(participant);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(rrn)) {
			refIds.put(UPI_REFERENCE_NO, rrn);
		}
	}

	public RepeatPayment getRepeatPaymentDetails(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn) {

		if (configurablePropertiesHolder.getProperty(RPT_PAYMENT_ENABLED_FOR_SELF_TRANSFER, Boolean.class)) {
			if (isSelfTransferTxn(txn)) {
				return getRepeatPaymentForSelfTransferTxn();
			}
		}

		if (DataValidationUtility.isRepeatPaymentLegacyImplEnabled()) {
			if (TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
					&& !Objects.isNull(txn.getContextMap()) && !isSelfTransferTxn(txn)) {
				if (configurablePropertiesHolder.getProperty(RPT_PAYMENT_ENABLED_FOR_VPA2ACCOUNT, Boolean.class)
						&& VPA2ACCOUNT.equalsIgnoreCase(txn.getContextMap().get(UPI_TXN_CATEGORY))) {
					return GenericUtility.getRepeatPaymentDetails(fieldsForVPA2ACCOUNT, rptPaymntUrlVpa2Account,
							participant, txn, URL_NAME);
				}
				if (VPA2VPA.equalsIgnoreCase(txn.getContextMap().get(UPI_TXN_CATEGORY))) {
					return GenericUtilityExtension.getRepeatPaymentForVpaToVpaCategory(fieldsForVPA2VPA,
							rptPaymntUrlVpa2Vpa, fieldsForToMobileRptPaymntUrl, rptPaymntToMobileUrl, participant, txn,
							URL_NAME);
				}
			}
		}

		if (!configurablePropertiesHolder.getProperty(RPT_PAYMENT_DISABLED_BANK_AND_WALLET, Boolean.class)) {
			if (TransactionSource.WALLET.getTransactionSourceKey().equals(txn.getStreamSource())
					&& !Objects.isNull(participant.getWalletData())) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForToMobileRptPaymntUrl, rptPaymntToMobileUrl,
						participant, txn, URL_NAME);
			}
		}
		// Todo: JiraId
		/*
		 * if (DataValidationUtility.isOtherPartyBankDetailsRequired(txn)) { if
		 * (TransactionSource.TS.getTransactionSourceKey().equals(txn.getStreamSource())
		 * && ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus()) &&
		 * txn.getContextMap() != null &&
		 * RPT_PAYMENT_REPORT_CODE.contains(txn.getContextMap().get(REPORT_CODE))) {
		 * return GenericUtility.getRepeatPaymentDetails(fieldsForImpsNeftAndXfer,
		 * rptPaymntUrlForImpsNeftAndXfer, participant, txn, URL_NAME); } }
		 */

		if (TransactionSource.TS.getTransactionSourceKey().equals(txn.getStreamSource())) {
			return getRepeatPaymentForTsTxns(txn);
		}
		return null;
	}

	private RepeatPayment getRepeatPaymentForTsTxns(final TransformedTransactionHistoryDetail tsTxn) {
		if (Objects.nonNull(tsTxn) && ClientStatusEnum.SUCCESS.getStatusKey().equals(tsTxn.getStatus())
				&& Objects.nonNull(tsTxn.getContextMap()) && tsTxn.getContextMap().containsKey(TXN_INITIATION_MODE)) {
			String txnInitiationMode = tsTxn.getContextMap().get(TXN_INITIATION_MODE);
			TransformedParticipant otherParticipant = Utility.getOtherParticipant(tsTxn);
			if (TO_MOBILE_MODE.equalsIgnoreCase(txnInitiationMode)) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForToMobileRptPaymntUrl, rptPaymntToMobileUrl,
						otherParticipant, tsTxn, URL_NAME);

			}
			else if (TO_ACCOUNT_MODE.equalsIgnoreCase(txnInitiationMode)) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForImpsNeftAndXfer, rptPaymntUrlForImpsNeftAndXfer,
						otherParticipant, tsTxn, URL_NAME);
			}
		}
		return null;
	}

	private RepeatPayment getRepeatPaymentForSelfTransferTxn() {
		RepeatPayment repeatPayment = new RepeatPayment();
		repeatPayment.setName(URL_NAME);
		repeatPayment.setUrl(rptPaymentUrlForSelfTransfer);
		return repeatPayment;
	}

}
