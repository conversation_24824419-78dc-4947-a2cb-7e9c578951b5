package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.LocalizationConstants.ENGLISH_LOCALE;
import static com.org.panaroma.commons.constants.LocalizationConstants.LISTING_API_LOCALIZATION_ENABLED;
import static com.org.panaroma.commons.constants.LocalizationConstants.LOCALE;
import static com.org.panaroma.commons.constants.WebConstants.CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.CLS_PANEL_KEY_LISTING_INSTRUMENT_NARRATION_CREDIT_INDICATOR;
import static com.org.panaroma.commons.constants.WebConstants.CLS_PANEL_KEY_LISTING_INSTRUMENT_NARRATION_CREDIT_INDICATOR_3P_APP_RECEIVER;
import static com.org.panaroma.commons.constants.WebConstants.CLS_PANEL_KEY_LISTING_INSTRUMENT_NARRATION_DEBIT_INDICATOR;
import static com.org.panaroma.commons.constants.WebConstants.CST;
import static com.org.panaroma.commons.constants.WebConstants.DEBIT_INSTRUMENT_NARRATION_USED_FOR_CREDIT_TXN_INDICATOR_TXN_TYPES;
import static com.org.panaroma.commons.constants.WebConstants.DOLLAR_SIGN;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_DATE_TIME_LABEL_PREFIX;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_NARRATION_CLS_PANEL_PREFIX;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_RESPONSE_MAPPING_ENUM_DEFAULT_SUFFIX;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_USER_INSTRUMENT_NARRATION_FROM;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_USER_INSTRUMENT_NARRATION_IN;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_USER_INSTRUMENT_NARRATION_TO;
import static com.org.panaroma.commons.constants.WebConstants.MANDATE_AMOUNT_PLACEHOLDER;
import static com.org.panaroma.commons.constants.WebConstants.PLACEHOLDER_STRING_USED_AT_FE;
import static com.org.panaroma.commons.constants.WebConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.EXCEPTION_WHILE_GETTING_NARRATION_AND_DATE_TIME_LABEL;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TXN_INDICATOR_BASED_NARRATION_AND_DATE_TIME_LABEL_USED;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.web.config.BankConfig;
import com.org.panaroma.web.config.RptCodeConfig;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.enums.ListingResponseMappingEnum;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Log4j2
@Component
public class ListingResponseModifier {

	@Autowired
	private BankConfig bankConfig;

	@Autowired
	private MetricsAgent metricsAgent;

	@Autowired
	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	// Modifies the response according to new listing design
	public void modifyResponse(final EsResponseTxn esResponseTxn, final TransformedTransactionHistoryDetail esDto,
			final Map<String, String> paramMap) {
		String locale = paramMap.getOrDefault(LOCALE, ENGLISH_LOCALE);
		if (!configurablePropertiesHolder.getProperty(LISTING_API_LOCALIZATION_ENABLED, Boolean.class)) {
			locale = ENGLISH_LOCALE;
		}
		Pair<String, String> narrationAndDateTimeLabel;
		try {
			narrationAndDateTimeLabel = getNarrationAndDateTimeLabel(esResponseTxn, esDto, locale);
		}
		catch (Exception exception) {
			log.error("Some exception occurred while getting narration & dateTimeLabel for txnId : {}. Exception : {}",
					esDto.getTxnId(), CommonsUtility.exceptionFormatter(exception));
			metricsAgent.incrementCount(EXCEPTION_WHILE_GETTING_NARRATION_AND_DATE_TIME_LABEL);
			narrationAndDateTimeLabel = getNarrationAndDateTimeLabelBasedOnTxnIndicator(esDto, locale);
		}
		if (Objects.isNull(narrationAndDateTimeLabel) || StringUtils.isBlank(narrationAndDateTimeLabel.getKey())) {
			narrationAndDateTimeLabel = getNarrationAndDateTimeLabelBasedOnTxnIndicator(esDto, locale);
		}

		esResponseTxn.setNarration(narrationAndDateTimeLabel.getKey());

		// whenever userInstrumentNarration is not null, app uses userInstrumentLogosV2,
		// so setting this field
		if (CollectionUtils.isEmpty(esResponseTxn.getUserInstrumentLogosV2())) {
			esResponseTxn.setUserInstrumentLogosV2(esResponseTxn.getUserInstrumentLogos());
		}
		// Success check is placed as the fields updated in the below if block are only
		// required at App side for Success txns
		if (ClientStatusEnum.SUCCESS
			.equals(ClientStatusEnum.getStatusEnumByKey(Integer.valueOf(esResponseTxn.getStatusKey())))) {
			esResponseTxn.setDateTimeLabel(narrationAndDateTimeLabel.getValue());
			esResponseTxn.setUserInstrumentNarration(getUserInstrumentNarration(esDto));
		}
		else {
			// This is done only to reduce the size of response as these are anyhow not
			// being used for Pending & Failed txns at FE
			removeUnnecessaryFieldsForNotSuccessResponse(esResponseTxn, paramMap);
		}
		// UserInstrumentLogos field is not going to be used for any case, so making this
		// null
		esResponseTxn.setUserInstrumentLogos(null);
	}

	private Pair<String, String> getNarrationAndDateTimeLabel(final EsResponseTxn esResponseTxn,
			final TransformedTransactionHistoryDetail esDto, final String locale) {
		/*
		 * Objects.nonNull(esResponseTxn.getListingResponseMappingEnum()) means that the
		 * ListingResponseMappingEnum is already identified in previous code at the time
		 * when extra handling was being done for some flows
		 */
		if (Objects.nonNull(esResponseTxn.getListingResponseMappingEnum()) || !esDto.getIsBankData()) {
			ListingResponseMappingEnum listingResponseMappingEnum = getListingResponseMappingEnum(esResponseTxn, esDto);
			if (Objects.nonNull(listingResponseMappingEnum)) {
				return getNarrationDateTimeLabelFromResponseMappingEnum(esResponseTxn, esDto,
						listingResponseMappingEnum, locale);
			}
		}
		else {
			Map<String, String> contextMap = esDto.getContextMap();
			RptCodeConfig rptCodeConfig = GenericUtility.fetchRptCodeConfig(bankConfig, contextMap.get(REPORT_CODE),
					esDto.getTxnIndicator());
			String listingNarration = LocalizedDataCacheService.getLocalizedValue(
					LISTING_NARRATION_CLS_PANEL_PREFIX + rptCodeConfig.getRptCode(),
					rptCodeConfig.getListingNarration(), locale);
			listingNarration = modifyNarrationIfRequired(esResponseTxn, listingNarration, esDto, locale);
			String dateTimeLabel = LocalizedDataCacheService.getLocalizedValue(
					LISTING_DATE_TIME_LABEL_PREFIX + rptCodeConfig.getRptCode(),
					rptCodeConfig.getListingDateTimeLabel(), ENGLISH_LOCALE);
			return new Pair<>(listingNarration, dateTimeLabel);
		}
		return null;
	}

	private static ListingResponseMappingEnum getListingResponseMappingEnum(final EsResponseTxn esResponseTxn,
			final TransformedTransactionHistoryDetail esDto) {
		ListingResponseMappingEnum listingResponseMappingEnum = Objects
			.nonNull(esResponseTxn.getListingResponseMappingEnum()) ? esResponseTxn.getListingResponseMappingEnum()
					: ListingResponseMappingEnum.getTransactionTypeEnumByName(
							TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getTxnType()).name());

		if (GenericUtilityExtension.isWalletAutomaticAddMoneyTxn(esDto)) {
			listingResponseMappingEnum = ListingResponseMappingEnum.AUTOMATIC_ADD_MONEY;
		}

		if (TransactionTypeEnum.LITE_TOPUP_MANDATE
			.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getMainTxnType()))) {
			return UpiLiteViewUtility.getListingResponseMappingEnumForLiteTopUpMandate(esDto, esResponseTxn);
		}
		return listingResponseMappingEnum;
	}

	private Pair<String, String> getNarrationDateTimeLabelFromResponseMappingEnum(final EsResponseTxn esResponseTxn,
			final TransformedTransactionHistoryDetail esDto,
			final ListingResponseMappingEnum listingResponseMappingEnum, final String locale) {
		String listingNarration = LocalizedDataCacheService.getLocalizedValue(
				LISTING_NARRATION_CLS_PANEL_PREFIX + listingResponseMappingEnum.name().toLowerCase(),
				listingResponseMappingEnum.getNarration(), locale);

		// extra handling is done for recurring mandate setup txns as we need to replace
		// $MANDATE_AMOUNT also in the narration
		if (ListingResponseMappingEnum.RECURRING_MANDATE_SETUP.equals(listingResponseMappingEnum)
				|| ListingResponseMappingEnum.RECURRING_MANDATE_SETUP_DEFAULT.equals(listingResponseMappingEnum)) {
			listingNarration = listingNarration.replace(MANDATE_AMOUNT_PLACEHOLDER,
					GenericUtilityExtension.getMandateAmount(esDto));
		}
		String dateTimeLabel = LocalizedDataCacheService.getLocalizedValue(
				LISTING_DATE_TIME_LABEL_PREFIX + listingResponseMappingEnum.name().toLowerCase(),
				listingResponseMappingEnum.getDateTimeLabel(), ENGLISH_LOCALE);
		listingNarration = modifyNarrationIfRequired(esResponseTxn, listingNarration, esDto, locale);
		return new Pair<>(listingNarration, dateTimeLabel);
	}

	private String modifyNarrationIfRequired(final EsResponseTxn esResponseTxn, final String listingNarration,
			final TransformedTransactionHistoryDetail esDto, final String locale) {
		if (StringUtils.isNotBlank(listingNarration)) {
			boolean narrationContainsPlaceholder = listingNarration.contains(DOLLAR_SIGN);
			// no need to do anything in this case
			if (!narrationContainsPlaceholder) {
				return listingNarration;
			}
			boolean secondPartyNamePresent = (Objects.nonNull(esResponseTxn.getSecondPartyInfo())
					&& StringUtils.isNotBlank(esResponseTxn.getSecondPartyInfo().getName())) ? true : false;

			/*
			 * Replace placeholder in narration if nameOrder is null, else app will itself
			 * do this. This is done so to handle contact book name case
			 */
			if (secondPartyNamePresent) {
				return getNarrationAfterReplacingPlaceholder(esResponseTxn, listingNarration);
			}
			return getTxnTypeBasedDefaultStaticNarration(esDto, locale);
		}
		else {
			return getTxnTypeBasedDefaultStaticNarration(esDto, locale);
		}
	}

	private String getNarrationAfterReplacingPlaceholder(final EsResponseTxn esResponseTxn,
			final String listingNarration) {
		String valueToBeReplacedWithPlaceholder = null;
		if (CollectionUtils.isEmpty(esResponseTxn.getSecondPartyInfo().getNameOrder())) {
			valueToBeReplacedWithPlaceholder = esResponseTxn.getSecondPartyInfo().getName();
			// esResponseTxn.getSecondPartyInfo().setName(null);
		}
		else {
			valueToBeReplacedWithPlaceholder = PLACEHOLDER_STRING_USED_AT_FE;
		}

		// This will never return -1 as it is already checked that $ sign is present in
		// listingNarration
		int indexOfDollar = listingNarration.indexOf(DOLLAR_SIGN);
		int indexOfSpaceAfterDollar = listingNarration.substring(indexOfDollar).indexOf(SPACE);
		String updatedListingNarration;

		if (indexOfSpaceAfterDollar == -1) {
			updatedListingNarration = listingNarration.replace(listingNarration.substring(indexOfDollar),
					valueToBeReplacedWithPlaceholder);
		}
		else {
			updatedListingNarration = listingNarration.substring(0, indexOfDollar) + valueToBeReplacedWithPlaceholder
					+ listingNarration.substring(indexOfSpaceAfterDollar + indexOfDollar);
		}
		return updatedListingNarration;
	}

	private String getTxnTypeBasedDefaultStaticNarration(final TransformedTransactionHistoryDetail esDto,
			final String locale) {
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getTxnType());
		ListingResponseMappingEnum txnTypeSpecificDefaultNarrationEnum = ListingResponseMappingEnum
			.getTransactionTypeEnumByName(txnType.name() + LISTING_RESPONSE_MAPPING_ENUM_DEFAULT_SUFFIX);
		if (Objects.nonNull(txnTypeSpecificDefaultNarrationEnum)) {
			log.warn("TxnType specific Default ListingResponseMappingEnum is used for txnId : {}", esDto.getTxnId());
			return LocalizedDataCacheService.getLocalizedValue(
					LISTING_NARRATION_CLS_PANEL_PREFIX + txnTypeSpecificDefaultNarrationEnum.name().toLowerCase(),
					txnTypeSpecificDefaultNarrationEnum.getNarration(), locale);
		}
		log.warn("TxnType specific Default ListingResponseMappingEnum doesn't exist for txnId : {}", esDto.getTxnId());
		return null;
	}

	// This logic was at app end before adding this code
	private Pair<String, String> getNarrationAndDateTimeLabelBasedOnTxnIndicator(
			final TransformedTransactionHistoryDetail esDto, final String locale) {
		TransactionIndicator transactionIndicator = TransactionIndicator
			.getTransactionIndicatorEnumByKey(esDto.getTxnIndicator());
		ListingResponseMappingEnum defaultListingResponseMappingEnum = null;
		switch (transactionIndicator) {
			case DEBIT:
				defaultListingResponseMappingEnum = ListingResponseMappingEnum.DEFAULT_FOR_DEBIT_TXN_INDICATOR;
				break;
			case CREDIT:
				defaultListingResponseMappingEnum = ListingResponseMappingEnum.DEFAULT_FOR_CREDIT_TXN_INDICATOR;
				break;
			default:
				// do nothing
		}
		log.warn("TxnIndicator based narration & dateTimeLabel used for listing response for txnId : {}",
				esDto.getTxnId());
		metricsAgent.incrementCount(TXN_INDICATOR_BASED_NARRATION_AND_DATE_TIME_LABEL_USED);
		String narration = LocalizedDataCacheService.getLocalizedValue(
				LISTING_NARRATION_CLS_PANEL_PREFIX + defaultListingResponseMappingEnum.name().toLowerCase(),
				defaultListingResponseMappingEnum.getNarration(), locale);

		String dateTimeLabel = LocalizedDataCacheService.getLocalizedValue(
				LISTING_DATE_TIME_LABEL_PREFIX + defaultListingResponseMappingEnum.name().toLowerCase(),
				defaultListingResponseMappingEnum.getDateTimeLabel(), ENGLISH_LOCALE);

		return new Pair<>(narration, dateTimeLabel);
	}

	public void updateUserInstrumentRelatedInfo(final EsResponseTxn esResponseTxn,
			final TransformedTransactionHistoryDetail esDto) {

		esResponseTxn.setUserInstrumentNarration(getUserInstrumentNarration(esDto));

		// whenever userInstrumentNarration is not null, app uses userInstrumentLogosV2,
		// so setting this field
		if (CollectionUtils.isEmpty(esResponseTxn.getUserInstrumentLogosV2())) {
			esResponseTxn.setUserInstrumentLogosV2(esResponseTxn.getUserInstrumentLogos());
		}
	}

	private String getUserInstrumentNarration(final TransformedTransactionHistoryDetail esDto) {
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getTxnType());
		TransactionIndicator transactionIndicator = TransactionIndicator
			.getTransactionIndicatorEnumByKey(esDto.getTxnIndicator());
		if (DEBIT_INSTRUMENT_NARRATION_USED_FOR_CREDIT_TXN_INDICATOR_TXN_TYPES.contains(txnType)
				|| (TransactionIndicator.DEBIT.equals(transactionIndicator))
						&& !TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.equals(txnType)) {
			return LocalizedDataCacheService.getLocalizedValue(
					CLS_PANEL_KEY_LISTING_INSTRUMENT_NARRATION_DEBIT_INDICATOR, LISTING_USER_INSTRUMENT_NARRATION_FROM,
					ENGLISH_LOCALE);
		}

		// if user received money on another app then show the instrument narration as
		// "TO"
		if (TransactionTypeEnum.P2P_INWARD_3P_APP.equals(txnType)
				&& TransactionIndicator.CREDIT.equals(transactionIndicator)) {
			return LocalizedDataCacheService.getLocalizedValue(
					CLS_PANEL_KEY_LISTING_INSTRUMENT_NARRATION_CREDIT_INDICATOR_3P_APP_RECEIVER,
					LISTING_USER_INSTRUMENT_NARRATION_TO, ENGLISH_LOCALE);
		}
		return LocalizedDataCacheService.getLocalizedValue(CLS_PANEL_KEY_LISTING_INSTRUMENT_NARRATION_CREDIT_INDICATOR,
				LISTING_USER_INSTRUMENT_NARRATION_IN, ENGLISH_LOCALE);
	}

	private void removeUnnecessaryFieldsForNotSuccessResponse(final EsResponseTxn esResponseTxn,
			final Map<String, String> paramMap) {
		if (!CST.equalsIgnoreCase(paramMap.get(CLIENT))) {
			esResponseTxn.setUserInstrumentLogosV2(null);
		}
		esResponseTxn.setUserInstrumentNarration(null);
	}

}
