package com.org.panaroma.web.dto.listing.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaginationParamV4 {

	private String transactionDateEpoch;

	private String fromUpdatedDate;

	private String streamSource;

	private String txnId;

	private Integer pageNo = -1;

	private Integer nextRequestHandlerIdentifier;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
