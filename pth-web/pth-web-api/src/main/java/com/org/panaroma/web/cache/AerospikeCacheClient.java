package com.org.panaroma.web.cache;

import static com.org.panaroma.commons.constants.AerospikeConstants.CACHE_UPDATED_TIME_KEY;
import static com.org.panaroma.commons.constants.AerospikeConstants.OLDEST_TXN_CACHE_KEY;
import static com.org.panaroma.commons.constants.AerospikeConstants.V3_PREFIX;
import static com.org.panaroma.commons.constants.AerospikeConstants.VALUE_STRING;
import static com.org.panaroma.commons.constants.AerospikeConstants.ZERO_DELTA_CACHE_KEY;
import static com.org.panaroma.commons.constants.CommonCacheConstants.AEROSPIKE_EXCEPTION_COUNT;
import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.SAVE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.TOTAL_CACHE_CALLS;
import static com.org.panaroma.commons.constants.CommonCacheConstants.TYPE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE_FAILURE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.WRITE_SUCCESS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AerospikeConstants.ACTION;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AerospikeConstants.AEROSPIKE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AerospikeConstants.READ;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AerospikeConstants.READ_FAILURE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AerospikeConstants.READ_SUCCESS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AerospikeConstants.SET;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.monitoring.MonitoringConstants.CacheMonitoringConstant.CACHE_PROCESSING_TIME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.STATUS;

import com.aerospike.client.AerospikeException;
import com.aerospike.client.Bin;
import com.aerospike.client.Key;
import com.aerospike.client.Record;
import com.aerospike.client.policy.ClientPolicy;
import com.aerospike.client.policy.CommitLevel;
import com.aerospike.client.policy.WritePolicy;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.org.panaroma.commons.aerospikeExtend.AerospikeClientExtend;
import com.org.panaroma.commons.cache.CacheHelper;
import com.org.panaroma.commons.constants.CommonCacheConstants;
import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.cache.OldestTxnUpdateCache;
import com.org.panaroma.commons.dto.cache.ZeroDeltaCache;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.entity.AutoTaggingDetails;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.utility.CacheUtility;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Log4j2
@Component
@Primary
@Qualifier("AerospikeCacheClient")
public class AerospikeCacheClient implements ICacheClient {

	private static AerospikeClientExtend aerospikeClient = null;

	private String aeroSpikeUrl;

	private String aerospikeNamespace;

	private Integer aerospikePort;

	private Integer writeDefaultCacheSocketTimeout;

	private Integer writeDefaultCacheTotalTimeout;

	private Integer writeDefaultSleepBetweenRetries;

	private Integer writeDefaultCacheExpiryTime;

	private Integer readDefaultSleepBetweenRetries;

	private Integer readDefaultCacheTotalTimeout;

	private Integer readDefaultCacheSocketTimeout;

	private Integer batchSize;

	private ObjectMapper objectMapper;

	@Autowired
	MetricsAgent metricsAgent;

	private final CacheHelper cacheHelper;

	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public AerospikeCacheClient(@Value("${aerospike.host-name}") final String aeroSpikeUrl,
			@Value("${aerospike.namespace}") final String aerospikeNamespace,
			@Value("${aerospike.port}") final Integer aerospikePort,
			@Value("${aerospike.writePolicyDefault.socketTimeout}") final Integer writeDefaultCacheSocketTimeout,
			@Value("${aerospike.writePolicyDefault.totalTimeout}") final Integer writeDefaultCacheTotalTimeout,
			@Value("${aerospike.writePolicyDefault.sleepBetweenRetries}") final Integer writeDefaultSleepBetweenRetries,
			@Value("${aerospike.writePolicyDefault.expiration}") final Integer writeDefaultCacheExpiryTime,
			@Value("${aerospike.readPolicyDefault.sleepBetweenRetries}") final Integer readDefaultSleepBetweenRetries,
			@Value("${aerospike.readPolicyDefault.totalTimeout}") final Integer readDefaultCacheTotalTimeout,
			@Value("${aerospike.readPolicyDefault.socketTimeout}") final Integer readDefaultCacheSocketTimeout,
			@Value("${batch.size}") final Integer batchSize, final ObjectMapper objectMapper,
			ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.aeroSpikeUrl = aeroSpikeUrl;
		this.aerospikeNamespace = aerospikeNamespace;
		this.aerospikePort = aerospikePort;
		this.readDefaultCacheSocketTimeout = readDefaultCacheSocketTimeout;
		this.readDefaultCacheTotalTimeout = readDefaultCacheTotalTimeout;
		this.readDefaultSleepBetweenRetries = readDefaultSleepBetweenRetries;
		this.writeDefaultCacheExpiryTime = writeDefaultCacheExpiryTime;
		this.writeDefaultCacheSocketTimeout = writeDefaultCacheSocketTimeout;
		this.writeDefaultCacheTotalTimeout = writeDefaultCacheTotalTimeout;
		this.writeDefaultSleepBetweenRetries = writeDefaultSleepBetweenRetries;
		this.batchSize = batchSize;
		this.objectMapper = objectMapper;
		cacheHelper = new CacheHelper(objectMapper);
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		initializeCache();
	}

	/*
	 * Always assign aerospikeClient in last other wise it has double checked locking
	 * problem
	 */
	private void initializeCache() {
		if (aerospikeClient != null) {
			return;
		}

		synchronized (this) {
			if (aerospikeClient == null) {
				log.debug("Initializing aerospike client.");
				ClientPolicy policy = new ClientPolicy();
				policy.writePolicyDefault.expiration = this.writeDefaultCacheExpiryTime;
				policy.writePolicyDefault.commitLevel = CommitLevel.COMMIT_ALL;
				policy.writePolicyDefault.socketTimeout = this.writeDefaultCacheSocketTimeout;
				policy.writePolicyDefault.totalTimeout = this.writeDefaultCacheTotalTimeout;
				policy.writePolicyDefault.sleepBetweenRetries = this.writeDefaultSleepBetweenRetries;
				policy.readPolicyDefault.socketTimeout = this.readDefaultCacheSocketTimeout;
				policy.readPolicyDefault.totalTimeout = this.readDefaultCacheTotalTimeout;
				policy.readPolicyDefault.sleepBetweenRetries = this.readDefaultSleepBetweenRetries;
				aerospikeClient = new AerospikeClientExtend(policy, aeroSpikeUrl, aerospikePort);
			}
		}
	}

	@Override
	public Map<String, String> getLocalisedBatchRecords(final List<String> cacheBatchKey) {
		if (Objects.isNull(cacheBatchKey)) {
			log.debug("Key is null so returning with null response.");
			return null;
		}
		Map<String, String> localisedData = new HashMap<>();
		initializeCache();

		for (List<String> batchData : Lists.partition(cacheBatchKey, batchSize)) {
			Key[] keys = new Key[batchData.size()];
			int count = 0;
			for (String data : batchData) {
				keys[count++] = new Key(aerospikeNamespace, "localised", data);
			}
			Record[] records = aerospikeClient.get(null, keys, new TypeReference<Map<String, String>>() {
			}, "value");
			for (Record record : records) {
				if (record != null) {
					localisedData.putAll((Map<String, String>) record.getValue("value"));
				}
			}
		}
		return localisedData;
	}

	@Override
	public Map<String, UserDetails> getUserImageUrlMapFromCache(final List<String> keysList) {
		if (CollectionUtils.isEmpty(keysList)) {
			log.debug("not fetching from cache because Keys list is null or empty");
			return null;
		}
		log.debug("Fetching user Image records from cache for userId :{}", getStringFromList(keysList));
		Map<String, UserDetails> userIdUserImageMap = new HashMap<>();
		initializeCache();
		Key[] keys = new Key[keysList.size()];
		int count = 0;
		for (String key : keysList) {
			keys[count++] = new Key(aerospikeNamespace, "userDetailsSet", key);
		}
		Record[] records = aerospikeClient.get(null, keys, new TypeReference<Map<String, UserDetails>>() {
		}, "value");
		for (Record record : records) {
			if (record != null) {
				userIdUserImageMap.putAll(objectMapper.convertValue(record.getMap("value"),
						new TypeReference<HashMap<String, UserDetails>>() {
						}));
			}
		}
		if (!userIdUserImageMap.isEmpty()) {
			log.debug("Fetched user Image records from cache for userId :{}", getStringFromList(keysList));
		}
		return userIdUserImageMap;
	}

	@Override
	public UserDetails getUserImageDetailsFromEntityId(final String entityId) {
		try {
			if (StringUtils.isBlank(entityId)) {
				log.debug("Key is null");
				return null;
			}
			UserDetails userDetails = null;
			log.debug("Fetching userImage record from cache for  :{}", entityId);
			initializeCache();
			Key key = new Key(aerospikeNamespace, "userDetailsSet", entityId);
			Record record = aerospikeClient.get(null, key, new TypeReference<Map<String, UserDetails>>() {
			}, "value");
			if (record != null) {
				Map<String, UserDetails> hm = objectMapper.convertValue(record.getMap("value"),
						new TypeReference<Map<String, UserDetails>>() {
						});
				if (hm != null && hm.containsKey(entityId)) {
					userDetails = hm.get(entityId);
				}
			}
			log.info("Fetched userImage record from cache for entityId : {}, with details :{}", entityId, userDetails);
			return userDetails;
		}
		catch (Exception exception) {
			log.error("Exception occurred while reading from to Cache{}, entityId {}",
					CommonsUtility.exceptionFormatter(exception), entityId);
		}
		return null;
	}

	private String getStringFromList(final List<String> message) {
		StringBuilder sb = new StringBuilder();
		for (String userId : message) {
			sb.append(", ");
			sb.append(userId);
		}
		String s = sb.toString();
		return s.replaceFirst(", ", "");
	}

	public Boolean getNtuCacheData(final SearchContext searchContext, final CacheInfo cacheInfo) {
		if (Objects.isNull(cacheInfo)) {
			log.warn("Cache Info to get cache data for Listing Cache is null for entityId {}.",
					searchContext.getEntityId());
			return null;
		}
		String cacheName = cacheInfo.getCacheName();
		String cacheKey = CacheUtility.getCacheKeyCorrespondingToCacheName(searchContext, cacheInfo);
		if (StringUtils.isBlank(cacheKey)) {
			log.warn("Cache Key to get cache data is null for entityId {}.", searchContext.getEntityId());
			return null;
		}
		long startTime = System.currentTimeMillis();
		try {
			initializeCache();
			Key key = new Key(aerospikeNamespace, cacheInfo.getCacheSetName(), V3_PREFIX + cacheKey);
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, CACHE + COLON + cacheName);
			Record record = aerospikeClient.get(null, key);

			if (record == null) {
				log.debug(cacheName + " : No data found in cache for cache key {}", cacheKey);
				return null;
			}

			Object rawValue = record.getValue(VALUE_STRING);
			String stringValue;

			if (rawValue instanceof String) {
				stringValue = (String) rawValue;
			}
			else if (rawValue instanceof byte[]) {
				stringValue = new String((byte[]) rawValue);
			}
			else {
				log.error(cacheName + " : Unexpected data type in cache for cache key {}: {}", cacheKey,
						rawValue.getClass());
				return null;
			}

			// Handle JSON wrapper format like {"type":3,"object":"true"}
			Boolean cacheData;
			if (stringValue.startsWith("{") && stringValue.contains("\"object\"")) {
				try {
					// Parse JSON to extract the "object" value
					JsonNode jsonNode = objectMapper.readTree(stringValue);
					String objectValue = jsonNode.get("object").asText();
					cacheData = Boolean.parseBoolean(objectValue);
				}
				catch (Exception e) {
					log.error(cacheName + " : Failed to parse JSON wrapper for cache key {}. JSON: {}, Exception: {}",
							cacheKey, stringValue, CommonsUtility.exceptionFormatter(e));
					return null;
				}
			}
			else {
				// Direct string value
				cacheData = Boolean.parseBoolean(stringValue);
			}
			log.debug(cacheName + " : Data fetched from cache for cache key {} is {}", cacheKey, cacheData);
			return cacheData;
		}
		catch (AerospikeException exception) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName);
			log.error(cacheName + " : Some exception while getting data from cache for cache key {}. Exception : {}",
					cacheKey, CommonsUtility.exceptionFormatter(exception));
		}
		finally {
			metricsAgent.recordExecutionTime(CACHE_PROCESSING_TIME, System.currentTimeMillis() - startTime,
					CACHE + COLON + cacheName);
		}
		return null;
	}

	@Override
	public AppSideCacheData getAppSideCacheData(final SearchContext searchContext, final CacheInfo cacheInfo) {
		if (Objects.isNull(cacheInfo)) {
			log.warn("Cache Info to get cache data for appSideCacheData is null for entityId {}.",
					searchContext.getEntityId());
			return null;
		}
		String cacheName = cacheInfo.getCacheName();
		String cacheKey = CacheUtility.getCacheKeyCorrespondingToCacheName(searchContext, cacheInfo);
		if (StringUtils.isBlank(cacheKey)) {
			log.warn("Cache Key to get cache data is null for entityId {}.", searchContext.getEntityId());
			return null;
		}
		try {
			initializeCache();
			Key key = new Key(aerospikeNamespace, cacheInfo.getCacheSetName(), V3_PREFIX + cacheKey);
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, CACHE + COLON + cacheName);
			Record record = aerospikeClient.get(null, key);
			log.info(cacheName + "Data fetched from cache for cache key {} is {} for user : {}", key, record,
					searchContext.getEntityId());

			if (record == null) {
				return null;
			}
			long zeroDeltaTimeStamp = record.getLong(ZERO_DELTA_CACHE_KEY);
			long oldestTxnDateTimeStamp = record.getLong(OLDEST_TXN_CACHE_KEY);
			long cacheUpdatedDate = record.getLong(CACHE_UPDATED_TIME_KEY);
			AppSideCacheData cacheData = new AppSideCacheData();
			if (zeroDeltaTimeStamp != 0) { // value was actually non-null in cache
				cacheData.setZeroDeltaCacheData(ZeroDeltaCache.builder().fromUpdatedDate(zeroDeltaTimeStamp).build());
			}
			if (oldestTxnDateTimeStamp != 0) {
				cacheData.setOldestTxnUpdateCacheData(
						OldestTxnUpdateCache.builder().txnDate(oldestTxnDateTimeStamp).build());
			}
			if (cacheUpdatedDate != 0) {
				cacheData.setUpdatedOn(cacheUpdatedDate);
			}
			return cacheData;
		}
		catch (AerospikeException exception) {
			metricsAgent.incrementCount(AEROSPIKE_EXCEPTION_COUNT, CACHE + COLON + cacheName);
			log.error(cacheName + " : Some exception while getting data from cache for cache key {}. Exception : {}",
					cacheKey, CommonsUtility.exceptionFormatter(exception));
		}
		catch (Exception exception) {
			log.error("Exception occurred while reading from Cache for : {}, {}", cacheName,
					CommonsUtility.exceptionFormatter(exception));
		}
		return null;
	}

	// This method fetches records from cache by cache key and set.
	@Override
	public Record getRecordFromCache(final String cacheKey, final String set, final TypeReference type)
			throws Exception {
		if (StringUtils.isBlank(cacheKey) || StringUtils.isBlank(set)) {
			log.warn("Key or Set is blank to get Record Set: {}, Key: {}", set, cacheKey);
			throw new Exception(String.format("Key or Set is blank to get Record Set: %s, Key: %s", set, cacheKey));
		}
		long startTime = System.currentTimeMillis();
		try {
			initializeCache();
			Key key = new Key(aerospikeNamespace, set, V3_PREFIX + cacheKey);
			Record record = aerospikeClient.get(null, key, type);
			metricsAgent.incrementCount(AEROSPIKE, SET + COLON + set, STATUS + COLON + READ_SUCCESS);
			return record;
		}
		catch (AerospikeException e) {
			metricsAgent.incrementCount(AEROSPIKE, SET + COLON + set, STATUS + COLON + READ_FAILURE);
			log.error(set + " : Some exception while getting data from cache for cache key {}. Exception : {}",
					cacheKey, CommonsUtility.exceptionFormatter(e));
			throw e;
		}
		finally {
			metricsAgent.recordExecutionTime(AEROSPIKE, System.currentTimeMillis() - startTime, SET + COLON + set,
					ACTION + COLON + READ);
		}
	}

	public <T> T get(final String setName, final String cacheKey, final Class<T> itemType, final TypeReference type)
			throws Exception {
		if (StringUtils.isBlank(setName) || StringUtils.isBlank(cacheKey)) {
			log.warn("Key or Set is blank to get Record Set: {}, Key: {}", setName, cacheKey);
			return null;
		}
		long startTime = System.currentTimeMillis();
		try {
			initializeCache();
			Key key = new Key(aerospikeNamespace, setName, cacheKey);
			Record record = aerospikeClient.get(null, key, type);

			T obj = Objects.nonNull(record) ? objectMapper.convertValue(record.getValue(VALUE_STRING), itemType) : null;

			metricsAgent.incrementCount(AEROSPIKE, SET + COLON + setName, STATUS + COLON + READ_SUCCESS);

			return obj;
		}
		catch (AerospikeException e) {
			metricsAgent.incrementCount(AEROSPIKE, SET + COLON + setName, STATUS + COLON + READ_FAILURE);
			log.error(setName + " : Some exception while getting data from cache for cache key {}. Exception : {}",
					cacheKey, CommonsUtility.exceptionFormatter(e));
			return null;
		}
		finally {
			metricsAgent.recordExecutionTime(AEROSPIKE, System.currentTimeMillis() - startTime, SET + COLON + setName,
					ACTION + COLON + READ);
		}
	}

	private WritePolicy getWritePolicy(final int expiryTimeInSec) {
		WritePolicy writePolicy = new WritePolicy();
		writePolicy.expiration = expiryTimeInSec;
		writePolicy.commitLevel = CommitLevel.COMMIT_ALL;
		writePolicy.socketTimeout = this.writeDefaultCacheSocketTimeout;
		writePolicy.totalTimeout = this.writeDefaultCacheTotalTimeout;
		writePolicy.sleepBetweenRetries = writeDefaultSleepBetweenRetries;
		return writePolicy;
	}

	public void put(final String key, final String set, final Integer expiryTime, final Object record)
			throws Exception {
		if (StringUtils.isBlank(key) || Objects.isNull(record) || StringUtils.isBlank(set)
				|| Objects.isNull(expiryTime)) {
			log.warn("Key: {} or Data: {} or Set: {} or expiryTime: {} to be saved is null.", key, record, set,
					expiryTime);
			return;
		}
		long startTime = System.currentTimeMillis();
		try {
			initializeCache();
			WritePolicy writePolicy = getWritePolicy(expiryTime);

			Key cacheKey = new Key(aerospikeNamespace, set, key);

			aerospikeClient.put(writePolicy, cacheKey, "", new Bin(VALUE_STRING, record));
			metricsAgent.incrementCount(CommonCacheConstants.AEROSPIKE, CommonCacheConstants.SET + COLON + set,
					CommonCacheConstants.STATUS + COLON + WRITE_SUCCESS);
		}
		catch (Exception ex) {
			log.error(
					"Exception occurred while saving record Key: {}, Data: {},  Set: {}, expiryTime: {}, Exception: {}",
					key, record, set, expiryTime, CommonsUtility.exceptionFormatter(ex));
			metricsAgent.incrementCount(CommonCacheConstants.AEROSPIKE, CommonCacheConstants.SET + COLON + set,
					CommonCacheConstants.STATUS + COLON + WRITE_FAILURE);
			throw ex;
		}
		finally {
			metricsAgent.recordExecutionTime(CommonCacheConstants.AEROSPIKE, System.currentTimeMillis() - startTime,
					CommonCacheConstants.SET + COLON + set, CommonCacheConstants.ACTION + COLON + WRITE);
		}
	}

	public void evict(final String key, final String set) {
		try {
			initializeCache();
			Key cacheKey = new Key(aerospikeNamespace, set, key);
			aerospikeClient.delete((WritePolicy) null, cacheKey);
		}
		catch (Exception ex) {
			log.error("Exception occurred while delete cache Key: {}, Set: {}, Exception: {}", key, set,
					CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	@Override
	public TransformedTransactionHistoryDetail getTthdFromSet(final String txnId, final String cacheSet) {
		try {
			if (StringUtils.isBlank(txnId)) {
				return null;
			}
			log.debug("Fetching tthd record from cache for key :{}", txnId);
			Record record = getRecordFromCache(txnId, cacheSet,
					new TypeReference<TransformedTransactionHistoryDetail>() {
					});
			if (record == null) {
				log.info("No cache found for txnId {}", txnId);
				return null;
			}
			log.debug("Fetched record from cache for txnId : {}, with value :{}", txnId, record);
			return objectMapper.convertValue(record.getValue(VALUE_STRING), TransformedTransactionHistoryDetail.class);
		}
		catch (Exception e) {
			log.error("Exception occurred while reading from Cache for : {}, {}", cacheSet,
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	@Override
	public void saveAutoTaggingDetails(final String key, final String setName, final Integer expiryTime,
			final AutoTaggingDetails autoTaggingDetails) throws Exception {
		throw new UnsupportedOperationException("Operation not supported for AerospikeCacheClient");
	}

	public void saveNtuCacheData(final String cacheKey, final Boolean cacheData, final CacheInfo cacheInfo) {
		String cacheName = cacheInfo.getCacheName();
		long startTime = System.currentTimeMillis();

		try {
			initializeCache();
			metricsAgent.incrementCount(TOTAL_CACHE_CALLS, CACHE + COLON + cacheName, TYPE + COLON + SAVE);
			WritePolicy writePolicy = getWritePolicy(GenericUtilityExtension.getExpiryTimeForNtuCacheData());
			Key key = new Key(aerospikeNamespace, cacheInfo.getCacheSetName(), V3_PREFIX + cacheKey);
			aerospikeClient.put(writePolicy, key, "", new Bin(VALUE_STRING, cacheData.toString()));
			metricsAgent.incrementCount(CommonCacheConstants.AEROSPIKE,
					CommonCacheConstants.SET + COLON + cacheInfo.getCacheSetName(),
					CommonCacheConstants.STATUS + COLON + WRITE_SUCCESS);
		}
		catch (Exception e) {
			metricsAgent.incrementCount(CommonCacheConstants.AEROSPIKE,
					CommonCacheConstants.SET + COLON + cacheInfo.getCacheSetName(),
					CommonCacheConstants.STATUS + COLON + WRITE_FAILURE);
			log.error(cacheName + " : Some exception while saving data to cache for cache key {}. Exception : {}",
					cacheKey, CommonsUtility.exceptionFormatter(e));
		}
		finally {
			metricsAgent.recordExecutionTime(CommonCacheConstants.AEROSPIKE, System.currentTimeMillis() - startTime,
					CommonCacheConstants.SET + COLON + cacheInfo.getCacheSetName(),
					CommonCacheConstants.ACTION + COLON + WRITE);
		}
	}

}
