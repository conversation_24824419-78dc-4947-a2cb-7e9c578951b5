package com.org.panaroma.web.dto.updates.v2;

import com.org.panaroma.commons.utils.JsonUtils;
import com.org.panaroma.web.dto.listing.v4.ListingResponseV4;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class UpdatesResponseV4 extends ListingResponseV4 {

	private List<String> removedIds;

	private boolean invalidateStoredData = false;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
