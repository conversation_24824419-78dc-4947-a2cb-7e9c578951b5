package com.org.panaroma.web.detailAPI;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.config.BankConfig;
import com.org.panaroma.web.config.RptCodeConfig;
import com.org.panaroma.web.cstData.CstDataUtility;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.web.utility.DataValidationUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.StatusLogoUtility;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.DISABLE_REPEAT_PAYMENT_CTA_VPA_LIST_P2P_INWARD_PAYOUT;
import static com.org.panaroma.commons.constants.Constants.BANK_NAME_FOR_3P_APP_RECEIVER;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_P2P_INWARD_3P_APP_SUCCESS;
import static com.org.panaroma.commons.constants.WebConstants.AC_NO;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.BANK_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.ERROR_MESSAGE;
import static com.org.panaroma.commons.constants.WebConstants.FROM;
import static com.org.panaroma.commons.constants.WebConstants.GV_REDEEM;
import static com.org.panaroma.commons.constants.WebConstants.IMPS_REPORT_CODE;
import static com.org.panaroma.commons.constants.WebConstants.IN_YOUR;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_NAME;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_VPA;
import static com.org.panaroma.commons.constants.WebConstants.PAYER_MOBILE_NUMBER;
import static com.org.panaroma.commons.constants.WebConstants.RECIPIENT;
import static com.org.panaroma.commons.constants.WebConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_DISABLED_BANK_AND_WALLET;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.TO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_TXN_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.UPI_LITE;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.UPI_LITE_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.VPA2VPA;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_TXN_ID;
import static com.org.panaroma.commons.utils.Utility.isSelfTransferTxn;
import static com.org.panaroma.web.utility.GenericUtility.getRefIdMap;
import static com.org.panaroma.web.utility.GenericUtility.getTxnPurpose;
import static com.org.panaroma.web.utility.LogoCreator.getBankLogoFor3PAppTxn;

@Component
@Log4j2
public class P2pInward3PUpiAppResponseBuilder extends AbstractTransactionTypeDetailResponseBuilder {

	@Value("${rpt.paymnt.to.mobile.url}")
	String rptPaymntUrlWallet2Wallet;

	@Value("${rpt.paymnt.url.imps.neft.xfer.inward}")
	String rptPaymntUrlForImpsNeftAndXferInward;

	@Value("${rpt.paymnt.url.p2p.vpa2vpa}")
	String rptPaymntUrlVpa2Vpa;

	public static final String URL_NAME = "Pay";

	private static final Map<String, Boolean> fieldsForVPA2VPA = new HashMap<>();

	private static final Map<String, Boolean> fieldsForWalletToWallet = new HashMap<>();

	private static final Map<String, Boolean> fieldsForImpsNeftAndXferInward = new HashMap<>();

	static {

		fieldsForVPA2VPA.put(PAYEE_NAME, true);
		fieldsForVPA2VPA.put(PAYEE_VPA, true);
		fieldsForVPA2VPA.put(AMOUNT, false);

		fieldsForWalletToWallet.put(RECIPIENT, true);

		fieldsForImpsNeftAndXferInward.put(RPT_PYMNT_REMIT_NAME, true);
		fieldsForImpsNeftAndXferInward.put(RPT_PYMNT_REMIT_ACCT_NUM, true);
		fieldsForImpsNeftAndXferInward.put(RPT_PYMNT_REMIT_IFSC, true);
		fieldsForImpsNeftAndXferInward.put(RPT_PYMNT_REMIT_BANK_NAME, false);
		fieldsForImpsNeftAndXferInward.put(AMOUNT, false);
	}

	@Autowired
	private BankConfig bankConfig;

	private static final Map<String, Boolean> fieldsForImpsNeftAndXfer = new HashMap<>();

	static {
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_NAME, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_ACCT_NUM, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_IFSC, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_BANK_NAME, false);
		fieldsForImpsNeftAndXfer.put(AMOUNT, false);
	}

	@Override
	public TransactionTypeEnum buildDetailApiResponseFor() {
		return TransactionTypeEnum.P2P_INWARD_3P_APP;
	}

	@Override
	public DetailApiResponse getResponse(final List<TransformedTransactionHistoryDetail> detailList, final String txnId)
			throws Exception {

		TransformedTransactionHistoryDetail txn = null;
		TransformedTransactionHistoryDetail detailNeededForTxn = null;
		TransformedTransactionHistoryDetail cbsTxn = null;

		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (Boolean.TRUE.equals(detail.getShowInListing())) {
				txn = detail;
			}
			if (detail.getTxnId().equalsIgnoreCase(txnId)) {
				detailNeededForTxn = detail;
			}
			if (TransactionSource.PPBL.getTransactionSourceKey().equals(detail.getStreamSource())) {
				cbsTxn = detail;
			}
		}
		String purpose = getTxnPurpose(detailNeededForTxn.getParticipants(),
				TransactionIndicator.CREDIT.getTransactionIndicatorKey());
		DetailApiResponse detailApiResponse = new DetailApiResponse();

		detailApiResponse.setDetailNarration(getDetailNarration(purpose));

		detailApiResponse.setCurrency(Currency.getCurrencyByKey(txn.getCurrency()));
		detailApiResponse.setStatus(detailNeededForTxn.getIsSource()
				? ClientStatusEnum.getStatusEnumByKey(detailNeededForTxn.getStatus()).toString()
				: ClientStatusEnum.PENDING.toString());
		detailApiResponse.setStatusLogoUrl(
				StatusLogoUtility.getStatusLogoUrl(ClientStatusEnum.valueOf(detailApiResponse.getStatus())));
		detailApiResponse.setDateTime(DateTimeUtility.getDateTime(txn.getTxnDate()));
		detailApiResponse.setTxnIndicator(String.valueOf(txn.getTxnIndicator()));
		detailApiResponse.setAmount(Currency.getCurrencyAmountInHigherDenomination(txn.getAmount(), txn.getCurrency()));

		// setting notes in case status is not SUCCESS
		if (!ClientStatusEnum.SUCCESS.equals(ClientStatusEnum.valueOf(detailApiResponse.getStatus()))
				&& detailNeededForTxn.getContextMap() != null
				&& detailNeededForTxn.getContextMap().containsKey(ERROR_MESSAGE)) {
			detailApiResponse.setNotes(detailNeededForTxn.getContextMap().get(ERROR_MESSAGE));
		}

		Map<String, String> refIds = new HashMap<>();
		List<InstrumentDto> creditInstruments = new ArrayList<>();
		List<InstrumentDto> debitInstruments = new ArrayList<>();

		for (TransformedParticipant participant : txn.getParticipants()) {
			InstrumentDto instrumentDto = new InstrumentDto();
			instrumentDto.setCurrency(Currency.getCurrencyByKey(participant.getCurrency()));
			instrumentDto.setAmount(
					Currency.getCurrencyAmountInHigherDenomination(participant.getAmount(), participant.getCurrency()));
			instrumentDto.setInstrumentStatus(participant.getStatus());
			DetailApiUtil.setParticipantInfo(txn, participant, instrumentDto, buildDetailApiResponseFor(), false);
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				instrumentDto.setNarration(IN_YOUR);
				setData(instrumentDto, participant, false, refIds, purpose, detailList, detailNeededForTxn);
				creditInstruments.add(instrumentDto);
			}
			else {
				instrumentDto.setNarration(FROM);
				setData(instrumentDto, participant, true, refIds, purpose, detailList, detailNeededForTxn);
				instrumentDto.setAdditionalDetail(participant.getRemarks());
				if (EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())) {
					GenericUtility.setImageData(instrumentDto, participant.getEntityId());
				}
				// Adding logo Order in 2nd user instrument Dto
				List<Logo> logoOrder = GenericUtility.getListnDetailCommonLogos(participant, txn, true, false, null);
				instrumentDto.setLogoOrder(logoOrder);
				debitInstruments.add(instrumentDto);
				detailApiResponse.setRepeatPayment(getRepeatPaymentDetails(participant, txn));
			}
		}
		detailApiResponse.setFirstInstrument(debitInstruments);
		detailApiResponse.setSecondInstrument(creditInstruments);

		if (Utility.isWalletInterOpTxn(txn)) {
			String rrn = GenericUtilityExtension.getRrnFromUpiDoc(txn);
			if (org.apache.commons.lang3.StringUtils.isNotBlank(rrn)) {
				refIds.put(UPI_REFERENCE_NO, rrn);
			}
		}

		detailApiResponse.setReferenceIdMap(refIds);
		if (cbsTxn != null && IMPS_REPORT_CODE.contains(cbsTxn.getContextMap().getOrDefault(REPORT_CODE, null))) {
			detailApiResponse.setCstorderItem(CstDataUtility.getCstData(cbsTxn, detailApiResponse));
		}
		else {
			detailApiResponse.setCstorderItem(CstDataUtility.getCstData(detailNeededForTxn, detailApiResponse));
		}
		detailApiResponse.setReferenceIds(GenericUtility.convertRefIdMapToList(detailApiResponse.getReferenceIdMap()));
		return detailApiResponse;
	}

	private void setData(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final boolean isDebit, final Map<String, String> refIds, final String purpose,
			final List<TransformedTransactionHistoryDetail> detailList,
			final TransformedTransactionHistoryDetail detailNeededForTxn) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		if (paymentSystem == null) {
			log.error("unexpected payment system for P2P Inward detail response, participant entityId:{} ",
					participant.getEntityId());
			throw new RuntimeException("Unexpected payment system P2P Inward  detail response");
		}

		switch (paymentSystem) {
			case WALLET:
				if (isDebit) {
					instrumentDto.setName(participant.getName());
					instrumentDto.setLogoUrl(participant.getLogoUrl() != null ? participant.getLogoUrl()
							: LogoUtility.getLogo(
									WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()),
									LogoType.WALLET_ICON));
					instrumentDto.setInstrumentDetail(GenericUtility
						.getDroolsMaskedMobileNumber(participant.getWalletData().getWalletMobileNumber()));
				}
				else {
					if (GV_REDEEM.equalsIgnoreCase(purpose)) {
						instrumentDto.setName("Gift Voucher Account");
						String logoUrl = LogoUtility.getLogo(WalletTypesEnum.GIFT_VOUCHER.getDisplayName(),
								LogoType.WALLET_ICON);
						instrumentDto.setLogoUrl(logoUrl);
					}
					else {
						instrumentDto
							.setName(WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()));
						instrumentDto.setLogoUrl(LogoUtility.getLogo(
								WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()),
								LogoType.WALLET_ICON));
					}
				}
				String refId = GenericUtilityExtension.setRefIdsForWallet(detailList);
				if (StringUtils.isNotBlank(refId)) {
					refIds.put(WALLET_TXN_ID, refId);
				}
				break;
			case BANK:
			case PPBL:
				if (isDebit) {
					instrumentDto.setName(participant.getName());
					instrumentDto.setLogoUrl(participant.getLogoUrl());
					// Todo: JiraId
					if (DataValidationUtility.isOtherPartyBankDetailsRequired(detailNeededForTxn)) {
						if (Objects.nonNull(participant.getBankData())) {
							String instrumentDetail = participant.getBankData().getBankName();
							if (!StringUtils.isBlank(instrumentDetail)) {
								instrumentDto.setInstrumentDetail(instrumentDetail);
							}
							if (StringUtils.isBlank(instrumentDto.getLogoUrl())) {
								instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
										participant.getBankData().getBankName()));
							}
						}
					}
					else {
						if (StringUtils.isBlank(instrumentDto.getLogoUrl())) {
							instrumentDto.setLogoUrl(LogoUtility.defaultLogo(PaymentSystemEnum.PPBL));
						}
					}
				}
				else {
					if (Objects.nonNull(participant.getBankData())) {
						instrumentDto.setName(participant.getBankData().getBankName());
						instrumentDto.setInstrumentDetail(AC_NO + participant.getBankData().getAccNumber());
						instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
								participant.getBankData().getBankName()));
					}
				}
				if (Objects.nonNull(participant.getBankData())) {
					refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
				}
				if (paymentSystem == PaymentSystemEnum.PPBL && detailNeededForTxn.getContextMap() != null) {
					String reportCode = detailNeededForTxn.getContextMap().get(REPORT_CODE);
					RptCodeConfig rptCodeConfig = GenericUtility.fetchRptCodeConfig(bankConfig, reportCode,
							detailNeededForTxn.getTxnIndicator());
					refIds.putAll(getRefIdMap(rptCodeConfig, detailNeededForTxn.getContextMap(), bankConfig));
				}
				break;
			case UPI:
				if (isDebit) {
					instrumentDto.setName(participant.getName());
					if (DataValidationUtility.isOtherPartyBankDetailsRequiredForVpa2AccountTxn()) {
						if (GenericUtilityExtension.isVpa2AccountTxn(detailNeededForTxn)) {
							instrumentDto.setLogoUrl(participant.getLogoUrl());
							if (participant.getBankData() != null) {
								if (StringUtils.isNotBlank(participant.getBankData().getBankName())
										&& StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
									instrumentDto.setInstrumentDetail(participant.getBankData().getBankName() + " "
											+ AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
								}

								if (StringUtils.isBlank(instrumentDto.getLogoUrl())) {
									instrumentDto
										.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
												participant.getBankData().getBankName()));
								}
							}
						}
						else {
							if (Objects.nonNull(participant.getUpiData())
									&& StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
								instrumentDto.setInstrumentDetail(participant.getUpiData().getVpa());
							}
							String logoUrl = LogoUtility.getUpiUrlString(participant);
							instrumentDto.setLogoUrl(logoUrl);
						}
					}
					else {
						if (Objects.nonNull(participant.getUpiData())) {
							if (GenericUtilityExtension.isVpa2AccountTxn(detailNeededForTxn)) {
								instrumentDto.setLogoUrl(participant.getLogoUrl());
							}
							else if (Objects.nonNull(participant.getUpiData())
									&& StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
								instrumentDto.setInstrumentDetail(participant.getUpiData().getVpa());
							}
						}
						instrumentDto.setLogoUrl(LogoUtility.defaultLogo(PaymentSystemEnum.UPI));
					}
				}
				else {
					instrumentDto.setName(BANK_NAME_FOR_3P_APP_RECEIVER);
					instrumentDto.setLogoUrl(getBankLogoFor3PAppTxn(detailNeededForTxn));
					instrumentDto
						.setInstrumentDetail(AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
				}
				if (participant.getBankData() != null) {
					refIds.put(UPI_REFERENCE_NO, participant.getBankData().getRrn());
				}
				break;
			case PG:
				if (isDebit && GV_REDEEM.equalsIgnoreCase(purpose)) {
					instrumentDto.setName(participant.getName());
					String logoUrl = participant.getLogoUrl() != null ? participant.getLogoUrl()
							: getDefaultLogo(participant);
					instrumentDto.setLogoUrl(logoUrl);
					if (participant.getContextMap().containsKey(PAYER_MOBILE_NUMBER)) {
						instrumentDto.setInstrumentDetail(GenericUtility
							.getDroolsMaskedMobileNumber(participant.getContextMap().get(PAYER_MOBILE_NUMBER)));
					}
					break;
				}
				log.error("unexpected payment system for P2P Inward detail response, participant entityId:{} ",
						participant.getEntityId());
				throw new RuntimeException("Unexpected payment system P2P Inward  detail response");
			// refIds.put(WALLET_TXN_ID, participant.getPaymentTxnId());
			default:
				log.error("unexpected payment system for P2P Inward detail response, participant entityId:{} ",
						participant.getEntityId());
				throw new RuntimeException("Unexpected payment system P2P Inward  detail response");
		}
	}

	private String getDefaultLogo(final TransformedParticipant participant) {
		if (participant.getBankData() != null) {
			return LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
					participant.getBankData().getBankName());
		}
		return LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER);
	}

	public String getDetailNarration(final String purpose) {
		if (!StringUtils.isEmpty(purpose) && purpose.equalsIgnoreCase(GV_REDEEM)) {
			return "Gift Voucher Received";
		}
		return DETAIL_NARRATION_P2P_INWARD_3P_APP_SUCCESS.getLocaleMsgKey();
	}

	// For Jira ->PTH-718
	public boolean isRepeatPaymentDisabledForPayoutVpas(TransformedTransactionHistoryDetail txn) {
		// Payout CTA are Payouts from paytm (including setllements, gold sell and
		// cashbacks)
		// if we receive a P2P_inward from these VPAs mentioned in list, we should not
		// show Pay CTA.
		List<String> listOfDisableVpa = configurablePropertiesHolder
			.getProperty(DISABLE_REPEAT_PAYMENT_CTA_VPA_LIST_P2P_INWARD_PAYOUT, List.class);
		List<TransformedParticipant> participants = txn.getParticipants();
		for (TransformedParticipant participant : participants) {
			// first check is to fetch the Debit participant,second check is to find
			// whether its vpa belongs to listOfDisableVpa or not
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				if (participant.getUpiData() != null && listOfDisableVpa.contains(participant.getUpiData().getVpa())) {
					return true;
				}
			}
		}
		return false;
	}

	public RepeatPayment getRepeatPaymentDetails(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn) {
		if (isRepeatPaymentDisabledForPayoutVpas(txn)) {
			return null;
		}
		if (TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus())
				&& Objects.nonNull(txn.getContextMap()) && !isSelfTransferTxn(txn)) {
			if (VPA2VPA.equalsIgnoreCase(txn.getContextMap().get(UPI_TXN_CATEGORY))) {
				return GenericUtilityExtension.getRepeatPaymentForVpaToVpaCategory(fieldsForVPA2VPA,
						rptPaymntUrlVpa2Vpa, fieldsForWalletToWallet, rptPaymntUrlWallet2Wallet, participant, txn,
						URL_NAME);
			}
		}
		if (!configurablePropertiesHolder.getProperty(RPT_PAYMENT_DISABLED_BANK_AND_WALLET, Boolean.class)) {
			if (TransactionSource.WALLET.getTransactionSourceKey().equals(txn.getStreamSource())
					&& ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus())
					&& Objects.nonNull(participant.getWalletData())) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForWalletToWallet, rptPaymntUrlWallet2Wallet,
						participant, txn, URL_NAME);
			}
			if (TransactionSource.TS.getTransactionSourceKey().equals(txn.getStreamSource())
					&& ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus()) && txn.getContextMap() != null) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForImpsNeftAndXferInward,
						rptPaymntUrlForImpsNeftAndXferInward, participant, txn, URL_NAME);

			}
		}
		return null;
	}

}
