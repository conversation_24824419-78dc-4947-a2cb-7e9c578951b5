package com.org.panaroma.web.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.org.panaroma.commons.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SearchAppRequest {

	@Schema(description = "Page number")
	Integer pageNo;

	@Schema(description = "Page size")
	Integer pageSize;

	@Schema(description = "Is available on play store")
	Boolean playStore;

	@Schema(description = "language id")
	@JsonProperty("lang_id")
	Integer langId;

	@Schema(description = "language", defaultValue = "en")
	String language;

	@Schema(description = "locale", defaultValue = "en-IN")
	String locale;

	@Schema(description = "Name of device like Redmi_Note_8_Pro or Iphone 13")
	String deviceName;

	@Schema(description = "Application version")
	String version;

	@Schema(description = "Used for location")
	@JsonProperty("long")
	Integer longitude;

	@Schema(description = "Device Identifier like Xiaomi-RedmiNote8Pro-e05b660752df609f")
	String deviceIdentifier;

	@Schema(description = "Operating system version in device")
	Integer osVersion;

	@Schema(description = "Is user using ios or android")
	String client;

	@Schema(description = "Manufacturer of device")
	String deviceManufacturer;

	@Schema(description = "Type of network device support")
	String networkType;

	@Schema(description = "Latitude")
	@JsonProperty("lat")
	Integer latitude;

	@Schema(description = "child_site_id")
	@JsonProperty("child_site_id")
	Integer childSiteId;

	@Schema(description = "site_id")
	@JsonProperty("site_id")
	Integer siteId;

	@Schema(description = "Query like Anuj to be used in Contact Book search")
	String query;

	@Schema(description = "Is filter applied")
	Boolean filterApplied;

	@Schema(description = "Used in contact book search")
	Boolean fromAutoComplete;

	@Schema(description = "Is passbook filter applied")
	Boolean passbookFilter;

	@Schema(description = "Payment system like UPI or WAllET or PPBL")
	String paymentSystem;

	String walletTypeFilter;

	@Schema(description = "Upi identifier like 8188_ICIC0001694")
	String upiIdentifier;

	@Schema(description = "Date of transaction in epoch time")
	Long transactionDateEpoch;

	@Schema(description = "Transaction id")
	String paginationTxnId;

	@Schema(description = "Pagination stream source")
	Integer paginationStreamSource;

	@Schema(description = "Transaction indicator like credit and debit")
	String txnIndicator;

	@Schema(description = "Transaction status is pending, failed and success")
	String status;

	@Schema(description = "Transaction date")
	String date;

	@Schema(description = "Will show transaction between these date")
	String dateRangeValue;

	@Schema(description = "Amount range")
	String amountRange;

	@Schema(description = "search type like contactBook")
	String searchType;

	@Schema(description = "contact book numbers")
	String contactBookNumbers;

	@Schema(description = "category of transaction like money_sent,money_received")
	String txnCategory;

	@Schema(description = "transaction type like P2P_INWARD_REMITTANCE,P2P_OUTWARD_REMITTANCE")
	String txnType;

	@Schema(description = "Entity id of second party")
	String secondPartyId;

	@Schema(description = "Filter type")
	String filterType;

	@Schema(description = "purpose of transaction")
	@JsonProperty("txn_purpose")
	String txnPurpose;

	@Schema(description = "Tag of transaction")
	String tag;

	@Schema(description = "Account type like ICA and ASA")
	String acctType;

	// Spend Analytics Filter
	@Schema(description = "Spend filter type for spend analytics")
	private String spendFilterType;

	@Schema(description = "Filter type value")
	private String spendFilterTypeValue;

	@Schema(description = "FIlter source")
	private String filterSource;

	// App will send this field whenever request is for search with autocomplete
	// If this field is absent, search on all fields
	// This field can contain comma-separated multiple values for old App versions as
	// these are being sent by ios App
	private String searchFields;

	private String fromUpdatedDate;

	private String updatedDateRangeValue;

	private Integer txnCount;

	private Integer pendingTxnCount;

	private String apiVersion;

	private String passbookType;

	private String invalidateVersion;

	private String verticalId;

	private String walletTxnType;

	private String nextListingUrl;

	private String verticalName;

	private String nextRequestHandlerIdentifier;

	private String showHiddenTxn;

	private Long toDateForSubsequentRequest;

	// Sample local curl, please update this below curl in case of addition of new fields
	/*
	 * curl -X POST "localhost:8092/transaction-history/ext/v2/search" -H
	 * "client:androidapp" -H "Authorization: {userToken:token}" -H
	 * "Content-Type: application/json" -d '{ "pageNo": 2, "pageSize": 20, "playStore":
	 * true, "lang_id": 1, "language": "en", "locale": "en-IN", "deviceName":
	 * "Redmi_Note_8_Pro", "version": "9.19.3", "long": 84.7129318, "deviceIdentifier":
	 * "Xiaomi-RedmiNote8Pro-e05b660752df609f", "osVersion": 11, "client": "androidapp",
	 * "deviceManufacturer": "Xiaomi", "networkType": "WIFI", "lat": 25.7860069,
	 * "child_site_id": 1, "site_id": 1, "query": "Anuj", //to beusedinContactBooksearch
	 * "filterApplied": true, //to beusedinContactBooksearch "fromAutoComplete": true,
	 * //to beusedinContactBooksearch "passbookFilter": true,
	 * "paymentSystem":"UPI,WALLET", //comma separated string "upiIdentifier":
	 * "8188_ICIC0001694,2635_BINS0003001", //comma separated string
	 * "transactionDateEpoch": 1640528802000, "paginationTxnId":
	 * "20211226111212800110168780982708624", "paginationStreamSource": 2, "txnIndicator":
	 * "debit,credit", //comma separated string "status":"success,pending", //comma
	 * separated string "date":
	 * "1638297000000-1640975399999,1640975400000-1643653799999"/"dateRange", //comma
	 * separated string "dateRangeValue": "1640975400000,1641389722000", //comma separated
	 * string "searchType": "contactBook", //to beusedinContactBooksearch , to be copied
	 * from metadata search.contactbook.contactBookListingApiParams "contactBookNumbers":
	 * "**********,**********", //to beusedinContactBooksearch //comma separated string
	 * "txnCategory": "money_sent,money_received", //comma separated string "txnType":
	 * "P2P_INWARD_REMITTANCE,P2P_OUTWARD_REMITTANCE", //comma separated string
	 * "secondPartyId": "********", "filterType": "entity" "acctType" : "ICA" //Account
	 * type in cases of ppbl filter., "searchFields": "Name", "passbookType" : "cst" }'
	 */

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
