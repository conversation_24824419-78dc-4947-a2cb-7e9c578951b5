package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.Constants.UNDERSCORE;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.AccountTypeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.dto.InstrumentInfo;
import com.org.panaroma.web.dto.UpiCcInstrumentInfoMetadata;
import com.org.panaroma.web.dto.UpiInstrumentInfoMetadata;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;

@Log4j2
public class InstrumentInfoMapper {

	public static InstrumentInfo build(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant) {
		Integer instrumentType = getInstrumentType(esDto, participant);
		Integer subInstrumentType = getSubInstrumentType(esDto, participant);

		InstrumentInfo.InstrumentInfoBuilder instrumentInfoBuilder = InstrumentInfo.builder()
			.instrumentType(instrumentType)
			.subInstrumentType(subInstrumentType)
			.identifier(getIdentifier(esDto, participant))
			.errorCode(getErrorCodes(esDto, participant))
			.accountType(getAccountType(esDto, participant, instrumentType, subInstrumentType))
			.metadata(getInstrumentMetaData(esDto, participant, instrumentType, subInstrumentType))
			.participant(participant);

		return instrumentInfoBuilder.build();
	}

	/**
	 * Method to derive error codes sent by source system for all payment sources. During
	 * initial impl, only upi is sending error codes.
	 * @param esDto = tthd
	 * @param participant = tthd.participant
	 * @return errorCode as String
	 */
	private static String getErrorCodes(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		switch (paymentSystem) {
			case UPI:
				return GenericUtilityExtension.getUpiErrorCodes(esDto, participant);
			default:
				return null;
		}
	}

	/**
	 * This method returns map of extra info for each instrument & subinstrument type.
	 * @param esDto = tthd
	 * @param participant = tthd.participant
	 * @param instrumentType = main payment type
	 * @param subInstrumentType = sub payment type
	 * @return Map of String, String
	 */
	private static Map<String, String> getInstrumentMetaData(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant, final Integer instrumentType, final Integer subInstrumentType) {
		ObjectMapper mapper = new ObjectMapper();

		if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(instrumentType) && subInstrumentType == null
				&& ObjectUtils.isNotEmpty(participant.getBankData())) {
			UpiInstrumentInfoMetadata upiMetadata = UpiInstrumentInfoMetadata.builder()
				.ifsc(participant.getBankData().getIfsc())
				.accountNumber(Utility.getLastFourDigitOfAccountNo(participant))
				.build();
			Map<String, String> upiMetadataMap = mapper.convertValue(upiMetadata,
					new TypeReference<Map<String, String>>() {
					});

			return upiMetadataMap;
		}

		if (subInstrumentType != null
				&& subInstrumentType.equals(PaymentSystemEnum.UPI_CREDIT_CARD.getPaymentSystemKey())
				&& ObjectUtils.isNotEmpty(participant.getCardData())) {
			UpiCcInstrumentInfoMetadata upiCcMetadata = UpiCcInstrumentInfoMetadata.builder()
				.creditAccRefNumber(participant.getCardData().getCreditAccRefNum())
				.build();
			Map<String, String> upiCcMetadataMap = mapper.convertValue(upiCcMetadata,
					new TypeReference<Map<String, String>>() {
					});
			return upiCcMetadataMap;
		}
		return null;
	}

	/**
	 * @param esDto = tthd
	 * @param participant = tthd.participant
	 * @return Integer sub PaymentSystem key Note: Any upi subsystem like upi_credit_line
	 * gets onboarded, need to add condition to handle. wallet will be automatically
	 * handled.
	 */
	private static Integer getSubInstrumentType(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		switch (paymentSystem) {
			case UPI:
				if (!isUpiSubSystem(esDto)) {
					return null;
				}
				else {
					if (esDto.getSearchFields()
						.getSearchPaymentSystem()
						.contains(PaymentSystemEnum.UPI_LITE.getPaymentSystemKey().toString())) {
						return PaymentSystemEnum.UPI_LITE.getPaymentSystemKey();
					}
					if (esDto.getSearchFields()
						.getSearchPaymentSystem()
						.contains(PaymentSystemEnum.UPI_CREDIT_CARD.getPaymentSystemKey().toString())) {
						return PaymentSystemEnum.UPI_CREDIT_CARD.getPaymentSystemKey();
					}
				}
				break;
			// Commented temporarily. To be used when subwallet filter is implemented
			/*
			 * case WALLET: if (esDto.getSearchFields() == null ||
			 * esDto.getSearchFields().getSearchWalletType() == null) { return null; } for
			 * (String walletType: esDto.getSearchFields().getSearchWalletType()) { try {
			 * int key = Integer.parseInt(walletType); if
			 * (WalletTypesEnum.SCLW.getWalletKey().equals(key)) { continue; } return key;
			 * } catch (NumberFormatException ignored) {
			 * log.debug("Integer conversion failed for walletType {}", walletType); } }
			 * return WalletTypesEnum.SCLW.getWalletKey();
			 */
			default:
		}
		return null;
	}

	/**
	 * This method returns unique id thru which a txn can be identified.
	 * @param esDto = tthd
	 * @param participant = tthd.participant
	 * @return String
	 */
	private static String getIdentifier(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		switch (paymentSystem) {
			case UPI:
				if (isUpiSubSystem(esDto)) {
					return getIdentifierFromSearchFields(esDto, paymentSystem);
				}

				if (ObjectUtils.isEmpty(participant.getBankData())) {
					log.warn("BankData not populated for txnId: {}", esDto.getTxnId());
					return null;
				}
				return Utility.getLastFourDigitOfAccountNo(participant) + UNDERSCORE
						+ participant.getBankData().getIfsc();
			case WALLET:
				return null;
			case PPBL:
				return getIdentifierFromSearchFields(esDto, paymentSystem);
			default:
		}
		return null;
	}

	private static Boolean isUpiSubSystem(final TransformedTransactionHistoryDetail esDto) {
		if (Objects.isNull(esDto.getSearchFields())) {
			log.warn("SearchFields are not populated for txnId: {}. Treating this txn as normal UPI txn",
					esDto.getTxnId());
			return false;
		}
		return esDto.getSearchFields()
			.getSearchPaymentSystem()
			.contains(PaymentSystemEnum.UPI_LITE.getPaymentSystemKey().toString())
				|| esDto.getSearchFields()
					.getSearchPaymentSystem()
					.contains(PaymentSystemEnum.UPI_CREDIT_CARD.getPaymentSystemKey().toString());
	}

	private static String getIdentifierFromSearchFields(final TransformedTransactionHistoryDetail esDto,
			final PaymentSystemEnum paymentSystem) {
		if (Objects.isNull(esDto.getSearchFields())) {
			log.warn("SearchFields are not populated for txnId: {}", esDto.getTxnId());
			return null;
		}
		switch (paymentSystem) {
			case UPI:
				if (esDto.getSearchFields()
					.getSearchPaymentSystem()
					.contains(PaymentSystemEnum.UPI_CREDIT_CARD.getPaymentSystemKey().toString())) {
					// CreditAccRefNum stored as first8Char_last8Char in bankAcctId in
					// searchFields
					return esDto.getSearchFields().getBankAcctId();

					// If instrument is UPI lite and UPI is also in search fields payment
					// system then it is also supposed to come in UPI filter
				}
				else if (esDto.getSearchFields()
					.getSearchPaymentSystem()
					.contains(PaymentSystemEnum.UPI_LITE.getPaymentSystemKey().toString())
						&& esDto.getSearchFields()
							.getSearchPaymentSystem()
							.contains(PaymentSystemEnum.UPI.getPaymentSystemKey().toString())) {
					return esDto.getSearchFields().getBankAcctId();
				}
				return null;
			case PPBL:
				// As per current filter impl, if acctType is not aviailble, it is assumed
				// as SAVINGS txn.
				// hence following same here - send identifier as per searchSelfAcctType;
				// if not available, default val ISA
				Integer accountTypeKey = getSelfAccountTypeWithDefaultType(esDto);
				return AccountTypeEnum.SAVINGS.getAccountTypeEnumKey().equals(accountTypeKey) ? "ISA" : "ICA";
			default:
		}
		return null;
	}

	private static Integer getSelfAccountTypeWithDefaultType(final TransformedTransactionHistoryDetail esDto) {
		Set<String> accountType = esDto.getSearchFields().getSearchSelfAccTyp();
		return accountType.stream().filter(type -> {
			try {
				Integer.parseInt(type);
				return true;
			}
			catch (NumberFormatException e) {
				return false;
			}
		}).map(Integer::parseInt).findFirst().orElse(AccountTypeEnum.SAVINGS.getAccountTypeEnumKey());
	}

	private static Integer getInstrumentType(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant) {
		return participant.getPaymentSystem();
	}

	private static String getAccountType(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant, final Integer instrumentType, final Integer subInstrumentType) {
		// Setting accountType for upi,ppbl payment type
		if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(instrumentType) && subInstrumentType == null
				&& ObjectUtils.isNotEmpty(participant.getBankData())) {
			AccountTypeEnum accountType = AccountTypeEnum
				.getAccountTypeEnumByKey(participant.getBankData().getAccountType());
			return Objects.nonNull(accountType) ? accountType.getAccountType() : null;
		}
		else if (PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(instrumentType)) {
			Integer accountTypeKey = getSelfAccountTypeWithDefaultType(esDto);
			return AccountTypeEnum.getAccountTypeEnumByKey(accountTypeKey).getAccountType();
		}
		return null;
	}

}
