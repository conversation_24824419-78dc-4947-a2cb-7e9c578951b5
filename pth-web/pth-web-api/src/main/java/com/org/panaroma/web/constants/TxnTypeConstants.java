package com.org.panaroma.web.constants;

import com.org.panaroma.commons.dto.TransactionTypeEnum;
import java.util.Arrays;
import java.util.List;

public class TxnTypeConstants {

	public static final List<TransactionTypeEnum> NON_PPBL_DEBIT_TXN_TYPES = Arrays.asList(
			TransactionTypeEnum.P2P_OUTWARD, TransactionTypeEnum.P2P2M, TransactionTypeEnum.P2P2M_OUTWARD,
			TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD, TransactionTypeEnum.P2M,
			TransactionTypeEnum.P2M_INTERNATIONAL, TransactionTypeEnum.OTHER_DEBIT, TransactionTypeEnum.ADD_AND_PAY,
			TransactionTypeEnum.P2P_OUTWARD_REMITTANCE, TransactionTypeEnum.WALLET_UPI_DEBIT_P2M,
			TransactionTypeEnum.WALLET_UPI_DEBIT_P2P, TransactionTypeEnum.IPO_MANDATE,
			TransactionTypeEnum.ONE_TIME_MANDATE, TransactionTypeEnum.RECURRING_MANDATE,
			TransactionTypeEnum.SYSTEM_DEBIT, TransactionTypeEnum.SBMD_MANDATE);

	public static final List<TransactionTypeEnum> NON_PPBL_CREDIT_TXN_TYPES = Arrays.asList(
			TransactionTypeEnum.P2P_INWARD, TransactionTypeEnum.P2P_INWARD_3P_APP, TransactionTypeEnum.P2M_REFUND,
			TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL, TransactionTypeEnum.OTHER_CREDIT,
			TransactionTypeEnum.WALLET_SETTLEMENT, TransactionTypeEnum.P2P2M_INWARD,
			TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD, TransactionTypeEnum.P2P_INWARD_REMITTANCE,
			TransactionTypeEnum.CASHBACK_RECEIVED, TransactionTypeEnum.UPI_WALLET_CREDIT,
			TransactionTypeEnum.P2P2M_REFUND, TransactionTypeEnum.P2P_OUTWARD_REMITTANCE_REFUND,
			TransactionTypeEnum.ADD_MONEY_REFUND);

	public static final List<TransactionTypeEnum> PPBL_CREDIT_TXN_TYPES = Arrays.asList(
			TransactionTypeEnum.SALARY_CREDIT, TransactionTypeEnum.INTEREST_CREDIT, TransactionTypeEnum.NACH_CREDIT,
			TransactionTypeEnum.P2P_INWARD, TransactionTypeEnum.P2P_INWARD_3P_APP, TransactionTypeEnum.P2M_REFUND,
			TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL, TransactionTypeEnum.CASHBACK_RECEIVED,
			TransactionTypeEnum.OTHER_CREDIT, TransactionTypeEnum.CHARGES_REFUND);

	public static final List<TransactionTypeEnum> PPBL_DEBIT_TXN_TYPES = Arrays.asList(TransactionTypeEnum.P2P_OUTWARD,
			TransactionTypeEnum.P2M, TransactionTypeEnum.P2M_INTERNATIONAL, TransactionTypeEnum.CASH_WITHDRAWAL,
			TransactionTypeEnum.STANDING_INSTRUCTION, TransactionTypeEnum.CHARGES, TransactionTypeEnum.CHEQUE_DEBIT,
			TransactionTypeEnum.NACH_DEBIT, TransactionTypeEnum.OTHER_DEBIT, TransactionTypeEnum.P2P_INWARD_REVERSAL,
			TransactionTypeEnum.NACH_CREDIT_REVERSAL, TransactionTypeEnum.DEPOSIT_CREATED_REVERSAL);

	public static final List<TransactionTypeEnum> PPBL_CREDIT_SELF_TXN_TYPES = Arrays.asList(
			TransactionTypeEnum.P2P_OUTWARD_REVERSAL, TransactionTypeEnum.CASH_WITHDRAWAL_REVERSAL,
			TransactionTypeEnum.CHARGES_REVERSAL, TransactionTypeEnum.COMPENSATION, TransactionTypeEnum.CHEQUE_CREDIT,
			TransactionTypeEnum.CHEQUE_DEBIT_REVERSAL, TransactionTypeEnum.NACH_DEBIT_REVERSAL,
			TransactionTypeEnum.DEPOSIT_INTEREST, TransactionTypeEnum.ADD_MONEY,
			TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE, TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE,
			TransactionTypeEnum.LITE_TOPUP_MANDATE, TransactionTypeEnum.ON_HOLD, TransactionTypeEnum.RELEASED,
			TransactionTypeEnum.ADD_MONEY_TO_BANK, TransactionTypeEnum.CASH_DEPOSIT,
			TransactionTypeEnum.DEPOSIT_CREATED, TransactionTypeEnum.DEPOSIT_REDEEMED,
			TransactionTypeEnum.WALLET_UPI_DEBIT_REVERSAL, TransactionTypeEnum.UPI_WALLET_CREDIT_REVERSAL,
			TransactionTypeEnum.FD_RECOVERY);

}
