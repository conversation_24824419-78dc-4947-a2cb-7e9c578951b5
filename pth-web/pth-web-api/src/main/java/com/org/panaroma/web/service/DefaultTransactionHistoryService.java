package com.org.panaroma.web.service;

import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE;
import static com.org.panaroma.commons.constants.CommonConstants.ONE_DAY_IN_MILLIS;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.ReconApi.APP_CACHE_RECON_ENABLED;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.ReconApi.INVALIDATE_STORED_DATA_FLAG_ENABLE_FOR_RECON;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.UthDownStreamName.AUTH_DOWNSTREAM_NAME;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.APP_CACHE_INVALIDATE_VERSION;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.APP_CACHE_INVALIDATE_VERSION_ENABLED;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.FILTER_SOURCE;
import static com.org.panaroma.commons.constants.SpendAnalyticsConstants.SPEND_PAGE;
import static com.org.panaroma.commons.constants.WebConstants.APP_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.AUTHORIZATION;
import static com.org.panaroma.commons.constants.WebConstants.CAPITAL_QUERY;
import static com.org.panaroma.commons.constants.WebConstants.CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT;
import static com.org.panaroma.commons.constants.WebConstants.DETAIL_V2;
import static com.org.panaroma.commons.constants.WebConstants.DETAIL_V3;
import static com.org.panaroma.commons.constants.WebConstants.ENTITY_ID;
import static com.org.panaroma.commons.constants.WebConstants.FALSE;
import static com.org.panaroma.commons.constants.WebConstants.FOR_APP_CACHE_RECON;
import static com.org.panaroma.commons.constants.WebConstants.FOR_UPDATES;
import static com.org.panaroma.commons.constants.WebConstants.FROM_DATE_LISTING_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.INVALIDATE_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.IS_FILTER_APPLIED;
import static com.org.panaroma.commons.constants.WebConstants.IS_FOR_SEARCH;
import static com.org.panaroma.commons.constants.WebConstants.IS_FOR_SEARCH_AND_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_REQUEST_APPLICABLE_TO_BE_SERVED_USING_CACHE;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_REQUEST_APPLICABLE_TO_CHECK_NO_TXN_FOUND;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_REQUEST_SERVED_FROM_CACHE;
import static com.org.panaroma.commons.constants.WebConstants.NO_TXN_PRESENT_IDENTIFIED_USING_CACHE;
import static com.org.panaroma.commons.constants.WebConstants.OPEN_SOURCE;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PASSBOOK_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.QUERY;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_QUERY_AS_KEYWORD;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_DATE_MANDATORY_FOR_DETAIL_API;
import static com.org.panaroma.commons.constants.WebConstants.USER_ID_DEFAULT;
import static com.org.panaroma.commons.constants.WebConstants.USER_TOKEN;
import static com.org.panaroma.commons.utils.AppCacheUtility.getCacheState;
import static com.org.panaroma.web.constants.CacheConstants.CacheManagerName.AEROSPIKE_CACHE_MANAGER;
import static com.org.panaroma.web.constants.CacheConstants.CacheName.DETAIL_API_CACHE;
import static com.org.panaroma.web.constants.CacheConstants.DETAIL_PAGE_API_CACHING_ENABLED;
import static com.org.panaroma.web.constants.CacheConstants.DETAIL_PAGE_ROLE_OUT_FEATURE_NAME;
import static com.org.panaroma.web.constants.metadataconstants.Constants.UPDATES_API_FROM_DATE_GAP;
import static com.org.panaroma.web.constants.metadataconstants.Constants.UPDATES_API_MAX_FROM_DATE_GAP_DAYS;
import static com.org.panaroma.web.constants.metadataconstants.Constants.UPDATES_API_MAX_TIME_GAP_FOR_UPDATES_API;
import static com.org.panaroma.web.constants.metadataconstants.Constants.UPDATES_API_PAGE_SIZE;
import static com.org.panaroma.web.constants.timelineconstants.TimelineConstants.TIMELINE_ERROR_MSG_MAP;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.KNOWN_ISSUE_ERROR;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NO_TRANSACTION_FOUND_FOR_LISTING;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.OAUTH_ERROR_GENERIC;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_NAME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.CACHE_DATE_DIFFERENCE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COUNT_MATCH;
import static com.org.panaroma.web.monitoring.MonitoringConstants.CRITERIA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.DETAIL_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ERROR_THROWN;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ES_COUNT_MORE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.FIRST_TRANSACTION_CURRENT_DATE_DIFF;
import static com.org.panaroma.web.monitoring.MonitoringConstants.IS_SERVED_FROM_CACHE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.LOCALE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.NOT_AVAILABLE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.PAGE_NO;
import static com.org.panaroma.web.monitoring.MonitoringConstants.PAGE_NO_WISE_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RECON_API_FROM_DATE_GAP;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RECON_MISMATCH_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.REQUEST_ID;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RESPONSE_TIME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SEARCH_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SERVED_FROM_ZERO_DELTA_CACHE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.STATUS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TXN_DATE_ABSENT_IN_DETAIL_REQUEST;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TXN_DATE_DIFFERENCE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API_FROM_DATE_GAP_METRICS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API_INVALIDATION;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API_NO_TXNS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.monitoringTagFormat;
import static com.org.panaroma.web.utility.GenericUtilityExtension.getFooterLogoTxnType;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.constants.ConfigurationPropertiesConstants;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.cache.CacheState;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.rollout.strategy.IRolloutStrategyHelper;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.rule.engine.service.RuleEngineService;
import com.org.panaroma.web.AbstractSearchContext;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.SearchContextFactory;
import com.org.panaroma.web.cache.CacheContext;
import com.org.panaroma.web.cache.CacheKeyManager;
import com.org.panaroma.web.cache.CacheWrapper;
import com.org.panaroma.web.cache.ClientConfigService;
import com.org.panaroma.web.cache.StringSerializedCacheWrapper;
import com.org.panaroma.web.circuit.breaker.AuthDownStreamCircuitBreakerUtil;
import com.org.panaroma.web.client.oauth.IOauthClient;
import com.org.panaroma.web.constants.CacheConstants;
import com.org.panaroma.web.dto.CacheValueWrapperDto;
import com.org.panaroma.web.dto.CustomOauthUserDetailsResponse;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.ReconResponseDto;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.dto.UpdateResponseDto;
import com.org.panaroma.web.dto.ValidationResult;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.web.dto.detailAPI.ExtraDetailRequestFields;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailResponseV2Mapper;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.ShareScreenEnricher;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.Timeline;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailApiResponseV3;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailRequestV3;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailResponseV3Mapper;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailVersionResponseMapper;
import com.org.panaroma.web.dto.detailAPI.timelineDto.TimelineErrorMsgMapperDto;
import com.org.panaroma.web.dto.detailAPI.timelineDto.TimelineErrorType;
import com.org.panaroma.web.dto.detailAPI.timelineDto.TimelineFieldsDto;
import com.org.panaroma.web.dto.detailAPI.timelineDto.UpiTimelineApiResponse;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.exceptionhandler.ExceptionHandlerUtil;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import com.org.panaroma.web.repo.TransactionHistoryRepo;
import com.org.panaroma.web.service.timeline.ITimelineService;
import com.org.panaroma.web.service.timeline.ITimelineServiceFactory;
import com.org.panaroma.web.service.timeline.UpiTimelineService;
import com.org.panaroma.web.utility.AuditUtility;
import com.org.panaroma.web.utility.CacheUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.FeatureRoleOutStrategyUtility;
import com.org.panaroma.web.utility.FooterLogoUtility;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.LocalisationUtility;
import com.org.panaroma.web.utility.LogHelper;
import com.org.panaroma.web.utility.ResponseBuilder;
import com.org.panaroma.web.utility.TokenValidatorUtility;
import com.org.panaroma.web.utility.TransactionHistoryServiceUtility;
import com.org.panaroma.web.utility.WebControllerUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import io.netty.channel.ConnectTimeoutException;
import io.netty.handler.timeout.TimeoutException;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.http.HttpTimeoutException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
// @DependsOn("aerospike_cache_manager")
@Log4j2
public class DefaultTransactionHistoryService implements TransactionHistoryService {

	private final TransactionHistoryRepo repo;

	private final TransactionHistoryRepo esBlockingRepo;

	private final IOauthClient oauthClient;

	private final boolean knownIssueAtBackend;

	private final MetricsAgent metricsAgent;

	private final boolean showBankDataProp;

	private final List<String> whiteListedBankUserId;

	private final List<String> whiteListedLocalisationUserId;

	private final List<String> salaryReportCodes;

	private final boolean fetchUserImageFromCache;

	private final boolean pushMissingUserImageListToKafka;

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	private final ITimelineServiceFactory iTimelineServiceFactory;

	private final DetailResponseV2Enricher detailResponseV2Enricher;

	private final boolean enableUnmaskAccountNumber;

	private final ClientConfigService clientConfigService;

	private final SearchContextFactory searchContextFactory;

	private final CacheUtility cacheUtility;

	private final CacheWrapper<String, CacheValueWrapperDto<List<TransformedTransactionHistoryDetail>>> detailsCacheWrapper;

	private final FeatureRoleOutStrategyUtility featureRoleOutStrategyUtility;

	private ObjectMapper objectMapper;

	private final IRolloutStrategyHelper iRolloutStrategyHelper;

	private final ResponseBuilder responseBuilder;

	private final RuleEngineService ruleEngineService;

	private final WebUtilityService webUtilityService;

	private final WebUtilityServiceNonMono webUtilityServiceNonMono;

	private final UpiTimelineService upiTimelineService;

	private final RolloutStrategyHelper rolloutStrategyHelper;

	private final AuditUtility auditUtility;

	private AuthDownStreamCircuitBreakerUtil authDownStreamCircuitBreakerUtil;

	@Autowired
	public DefaultTransactionHistoryService(
			@Qualifier("EsProgressiveSearchingHistoryRepo") final TransactionHistoryRepo repo,
			@Qualifier("EsBlockingTransactionHistoryRepo") final TransactionHistoryRepo esBlockingRepo,
			final IOauthClient oauthClient, final MetricsAgent metricsAgent,
			@Value("${show-bank-data}") final boolean showBankDataProp,
			@Value("${known-issue-at-backend}") final boolean knownIssueAtBackend,
			@Value("${whitelisted-bank-users}") final List<String> whiteListedBankUserId,
			@Value("${whitelisted-localisation-users}") final List<String> whiteListedLocalisationUserId,
			@Value("${salary.reportCodes}") final List<String> salaryReportCodes,
			@Value("${fetch-user-image-from-cache}") final boolean fetchUserImageFromCache,
			@Value("${push-missing-user-image-list-to-kafka}") final boolean pushMissingUserImageListToKafka,
			final ConfigurablePropertiesHolder configurablePropertiesHolder,
			@Value("${enable.unmask.accNumber}") final boolean enableUnmaskAccountNumber,
			final DetailResponseV2Enricher detailResponseV2Enricher,
			final ITimelineServiceFactory iTimelineServiceFactory, final ClientConfigService clientConfigService,
			final SearchContextFactory searchContextFactory, final CacheUtility cacheUtility,
			@Qualifier(AEROSPIKE_CACHE_MANAGER) final CacheManager cacheManager,
			final FeatureRoleOutStrategyUtility featureRoleOutStrategyUtility,
			final IRolloutStrategyHelper iRolloutStrategyHelper, final ResponseBuilder responseBuilder,
			final RuleEngineService ruleEngineService, final WebUtilityService webUtilityService,
			final WebUtilityServiceNonMono webUtilityServiceNonMono, final UpiTimelineService upiTimelineService,
			final RolloutStrategyHelper rolloutStrategyHelper, final AuditUtility auditUtility,
			final AuthDownStreamCircuitBreakerUtil authDownStreamCircuitBreakerUtil) {
		this.repo = repo;
		this.esBlockingRepo = esBlockingRepo;
		this.oauthClient = oauthClient;
		this.metricsAgent = metricsAgent;
		this.knownIssueAtBackend = knownIssueAtBackend;
		this.showBankDataProp = showBankDataProp;
		this.whiteListedBankUserId = whiteListedBankUserId;
		this.whiteListedLocalisationUserId = whiteListedLocalisationUserId;
		this.salaryReportCodes = salaryReportCodes;
		this.fetchUserImageFromCache = fetchUserImageFromCache;
		this.pushMissingUserImageListToKafka = pushMissingUserImageListToKafka;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.detailResponseV2Enricher = detailResponseV2Enricher;
		this.iTimelineServiceFactory = iTimelineServiceFactory;
		this.enableUnmaskAccountNumber = enableUnmaskAccountNumber;
		this.clientConfigService = clientConfigService;
		this.searchContextFactory = searchContextFactory;
		this.cacheUtility = cacheUtility;
		this.responseBuilder = responseBuilder;
		this.ruleEngineService = ruleEngineService;
		this.webUtilityServiceNonMono = webUtilityServiceNonMono;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.detailsCacheWrapper = new StringSerializedCacheWrapper<>(cacheManager, DETAIL_API_CACHE.value,
				metricsAgent, new TypeReference<>() {
				});
		this.featureRoleOutStrategyUtility = featureRoleOutStrategyUtility;
		this.iRolloutStrategyHelper = iRolloutStrategyHelper;
		this.webUtilityService = webUtilityService;
		this.upiTimelineService = upiTimelineService;
		this.auditUtility = auditUtility;
		this.authDownStreamCircuitBreakerUtil = authDownStreamCircuitBreakerUtil;
	}

	private void ensureObjMapperInitialized() {
		if (objectMapper != null) {
			return;
		}
		synchronized (DefaultTransactionHistoryService.class) {
			if (objectMapper == null) {
				objectMapper = new ObjectMapper();
			}
		}
	}

	private Optional<Mono> checkKnownIssue() {
		if (knownIssueAtBackend) {
			try {
				ExceptionHandlerUtil.sendUnRetryAbleError();
			}
			catch (Exception ex) {
				return Optional.of(Mono.error(ex));
			}
		}
		return Optional.empty();
	}

	@Override
	public Mono<ResponseDto> search(final Map<String, String> paramMap, final Map<String, String> tokens)
			throws PanaromaException {
		try {
			return searchInternal(paramMap, tokens);
		}
		catch (Exception ex) {
			try {
				processParamMapForExceptionInSearch(paramMap);
			}
			catch (Exception e) {
				log.error("error while processing paramMap for exception with requestId : {}",
						ThreadContext.get("requestId"));
			}
			Exception e = customizeExceptionAsPerFilter(ex, paramMap);
			return Mono.error(e);
		}
	}

	@Override
	public Mono<UpdateResponseDto> getUpdates(final Map<String, String> paramMap, final Map<String, String> tokens)
			throws PanaromaException {
		try {
			return searchInternalForUpdates(paramMap, tokens);
		}
		catch (Exception ex) {
			try {
				processParamMapForExceptionInSearch(paramMap);
			}
			catch (Exception e) {
				log.error("error while processing paramMap for exception with requestId : {}",
						ThreadContext.get("requestId"));
			}
			Exception e = customizeExceptionAsPerFilter(ex, paramMap);
			return Mono.error(e);
		}
	}

	@Override
	public ReconResponseDto getReconResponse(final Map<String, String> paramMap, final Map<String, String> tokens)
			throws PanaromaException, IOException, InterruptedException {
		paramMap.put(FOR_APP_CACHE_RECON, TRUE);
		AbstractSearchContext iSearchContext = searchContextFactory.getSearchContext(paramMap.get(CLIENT));
		SearchContext searchContext = iSearchContext.setSearchContext(paramMap, null);
		String id = ThreadContext.get("requestId");
		/*
		 * UTH 1270 starting passing below flag for recon API 1.invalidateStoredData
		 * 2.invalidateVersion And also check validation of recon Api based on Invalidate
		 * Version
		 */
		boolean invalidateStoredData = false;
		Integer invalidateVersion = configurablePropertiesHolder.getProperty(APP_CACHE_INVALIDATE_VERSION,
				Integer.class);
		Boolean appCacheReconEnabled = configurablePropertiesHolder.getProperty(APP_CACHE_RECON_ENABLED, Boolean.class);

		// Implementing Req Validation based on InvalidateVersion just like Update Api
		// Request
		if (diffInvalidateVersion(paramMap)) {
			log.warn("Received Recon with diff invalidateVersion, app value : {} and BE value : {}",
					paramMap.get(INVALIDATE_VERSION), invalidateVersion);
			invalidateStoredData = configurablePropertiesHolder
				.getProperty(INVALIDATE_STORED_DATA_FLAG_ENABLE_FOR_RECON, Boolean.class);
			return ReconResponseDto.builder()
				.txnCountMatch(true)
				.dataBaseTxnCount(Integer.parseInt(paramMap.get("txnCount")))
				.dataBasePendingTxnCount(Integer.parseInt(paramMap.get("pendingTxnCount")))
				.invalidateStoredData(invalidateStoredData)
				.invalidateVersion(invalidateVersion)
				.build();
		}
		else if (appCacheReconEnabled == null || !appCacheReconEnabled) {
			log.warn("App Cache recon is disabled. So returning count as true without checking");
			return ReconResponseDto.builder()
				.txnCountMatch(true)
				.dataBaseTxnCount(Integer.parseInt(paramMap.get("txnCount")))
				.dataBasePendingTxnCount(Integer.parseInt(paramMap.get("pendingTxnCount")))
				.invalidateStoredData(invalidateStoredData)
				.invalidateVersion(invalidateVersion)
				.build();
		}

		ValidationResult validationResult = webUtilityServiceNonMono.validateAndGetUserId(tokens, id,
				MonitoringConstants.OAUTH_SERVICE_USER_API);
		if (validationResult == null || StringUtils.isEmpty(validationResult.getUserId())) {
			throw new IllegalStateException("Response came without userID");
		}
		String userId = validationResult.getUserId();
		searchContext.setEntityId(userId);
		boolean isBankDataFilter = getBankDataFilter(searchContext.isShowBankData(), validationResult);
		// showing bank data.
		searchContext.setShowBankData(isBankDataFilter);
		int count;
		try {
			count = esBlockingRepo.getCountInUpdatedDateRange(searchContext);
		}
		catch (IOException e) {
			throw new RuntimeException(e);
		}

		int expectedCount = Integer.parseInt(paramMap.get("txnCount"));
		if (expectedCount != count) {
			cacheUtility.pushAppSideCacheDataToKafkaForCache(searchContext, null, true);
			invalidateStoredData = configurablePropertiesHolder
				.getProperty(INVALIDATE_STORED_DATA_FLAG_ENABLE_FOR_RECON, Boolean.class);
			metricsAgent.incrementCount(RECON_MISMATCH_COUNT,
					String.format(monitoringTagFormat, ES_COUNT_MORE, expectedCount < count),
					String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)),
					String.format(monitoringTagFormat, "diff", (long) Math.abs(expectedCount - count)),
					String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)));
			log.error(
					"AppCacheRecon count mismatch for userId : {}. Expected : {}, found : {}. Date range : {}. App_version : {}. Client : {}",
					searchContext.getEntityId(), searchContext.getTxnCount(), count,
					searchContext.getUpdatedDateRangeValue(), paramMap.get(APP_VERSION), paramMap.get(CLIENT));
		}

		ReconResponseDto reconResponseDto = ReconResponseDto.builder()
			.txnCountMatch(count == expectedCount)
			.dataBaseTxnCount(count)
			.dataBasePendingTxnCount(Integer.parseInt(paramMap.get("pendingTxnCount")))
			.invalidateStoredData(invalidateStoredData)
			.invalidateVersion(invalidateVersion)
			.build();
		try {
			metricsAgent.incrementCount("appCacheReconResponseCount",
					String.format(monitoringTagFormat, COUNT_MATCH, expectedCount == count),
					String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)),
					String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)));
			String updatedDateRangeValue = searchContext.getUpdatedDateRangeValue();
			long fromDate = Long.parseLong(updatedDateRangeValue.split(",")[0]);
			int timeGapInDays = (int) Math.ceil((System.currentTimeMillis() - fromDate) / (ONE_DAY_IN_MILLIS));
			metricsAgent.recordTimeGap(RECON_API_FROM_DATE_GAP, timeGapInDays,
					String.format(monitoringTagFormat, COUNT_MATCH, expectedCount == count),
					String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)),
					String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)));
		}
		catch (Exception e) {
			log.warn("Metrics for fromDate Gap not working");
		}
		log.info("Recon response for entityId : {}, : {}", searchContext.getEntityId(), reconResponseDto);
		return reconResponseDto;
	}

	public static void processParamMapForExceptionInSearch(final Map<String, String> paramMap) {
		if (paramMap != null && TRUE.equals(paramMap.get(IS_FILTER_APPLIED))
				&& (StringUtils.isNotBlank(paramMap.get(QUERY))
						|| StringUtils.isNotBlank(paramMap.get(SEARCH_QUERY_AS_KEYWORD)))) {
			TransactionHistoryServiceUtility.setFilterTypeInParamMap(paramMap);
			TransactionHistoryServiceUtility.handleSearchQuery(paramMap);
		}
	}

	private Mono<ResponseDto> searchInternal(final Map<String, String> paramMap, final Map<String, String> tokens) {

		Optional<Mono> monoOptional = checkKnownIssue();
		if (monoOptional.isPresent()) {
			return monoOptional.get();
		}
		// since paramMap float most of the code part so placing this threadId as unique
		// identifier
		// in paramMap to be used for logging
		String id = ThreadContext.get("requestId");
		paramMap.put(REQUEST_ID, id);

		AbstractSearchContext isearchContext = searchContextFactory.getSearchContext(paramMap.get(CLIENT));
		SearchContext searchContext = isearchContext.setSearchContext(paramMap, null);
		if (Objects.isNull(searchContext)) {
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		long startTime = System.currentTimeMillis();
		return this.getListingData(searchContext, tokens, paramMap).map(responseDto -> {
			// Recording metrics for cache served calls.
			long timeTaken = System.currentTimeMillis() - startTime;
			if (StringUtils.isNotBlank(paramMap.get(LISTING_REQUEST_SERVED_FROM_CACHE))) {
				metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken, API_NAME + COLON + SEARCH_API,
						IS_SERVED_FROM_CACHE + COLON + TRUE);
			}
			else if (StringUtils.isNotBlank(paramMap.get(LISTING_REQUEST_APPLICABLE_TO_BE_SERVED_USING_CACHE))) {
				metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken, API_NAME + COLON + SEARCH_API,
						IS_SERVED_FROM_CACHE + COLON + FALSE);
			}
			// this.pushMetricTxnCountAndDateDiff(responseDto, paramMap, searchContext);
			return responseDto;
		}).doOnError(e -> {
			long timeTaken = System.currentTimeMillis() - startTime;
			if (StringUtils.isNotBlank(paramMap.get(NO_TXN_PRESENT_IDENTIFIED_USING_CACHE))) {
				metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken,
						API_NAME + COLON + NO_TRANSACTION_FOUND_FOR_LISTING, IS_SERVED_FROM_CACHE + COLON + TRUE);
			}
			else if (e instanceof PanaromaException
					&& NO_TRANSACTION_FOUND_FOR_LISTING.equals(((PanaromaException) e).getResponseCode())
					&& StringUtils.isNotBlank(paramMap.get(LISTING_REQUEST_APPLICABLE_TO_CHECK_NO_TXN_FOUND))) {
				metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken,
						API_NAME + COLON + NO_TRANSACTION_FOUND_FOR_LISTING, IS_SERVED_FROM_CACHE + COLON + FALSE);
			}
		});
	}

	private Mono<UpdateResponseDto> searchInternalForUpdates(final Map<String, String> paramMap,
			final Map<String, String> tokens) {

		Optional<Mono> monoOptional = checkKnownIssue();
		if (monoOptional.isPresent()) {
			return monoOptional.get();
		}
		// since paramMap float most of the code part so placing this threadId as unique
		// identifier
		// in paramMap to be used for logging
		String id = ThreadContext.get("requestId");
		paramMap.put(REQUEST_ID, id);
		paramMap.put(FOR_UPDATES, TRUE);

		AbstractSearchContext iSearchContext = searchContextFactory.getSearchContext(paramMap.get(CLIENT));
		SearchContext searchContext = iSearchContext.setSearchContext(paramMap, null);

		if (invalidForUpdates(searchContext, paramMap)) {
			log.warn("Setting updates as invalid. Returning search result.");
			searchContext.setInvalidForUpdates(true);
			searchContext.setForUpdates(false);
			searchContext.setFromUpdatedDate(null);
			searchContext.setPaginationTxnId(null);
			searchContext.setPaginationStreamSource(null);
			searchContext.setTransactionDateEpoch(null);
			searchContext.setPageNo(1);
			metricsAgent.incrementCount(UPDATES_API_INVALIDATION,
					String.format(monitoringTagFormat, CRITERIA, "fromUpdatedDateCheck"),
					String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)),
					String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)));
		}
		else {
			searchContext.setForUpdates(true);
			searchContext.setShowInListing(null);
			searchContext.setSearchPageSize(searchContext.getPageSize());
			searchContext.setPageSize(configurablePropertiesHolder.getProperty(UPDATES_API_PAGE_SIZE, Integer.class));
			// Decreasing fromUpdatedDate by 15 seconds to account for delayed updates in
			// ES
			searchContext.setPaginationStreamSource(null);
			searchContext.setPageNo(1);
			searchContext.setPaginationTxnId(null);
			searchContext.setFromDate(searchContext.getFromUpdatedDate()
					- configurablePropertiesHolder.getProperty(UPDATES_API_FROM_DATE_GAP, Long.class));
			searchContext.setTransactionDateEpoch(null);

		}
		long startTime = System.currentTimeMillis();
		log.info("Updated search context : {}", searchContext);
		return this.getListingUpdatesData(searchContext, tokens, paramMap).map(responseDto -> {
			long timeTaken = System.currentTimeMillis() - startTime;
			metricsAgent.recordExecutionTime(RESPONSE_TIME, timeTaken, API_NAME + COLON + UPDATES_API);
			return responseDto;
		});
	}

	public boolean invalidForUpdates(final SearchContext searchContext, final Map<String, String> paramMap) {

		boolean invalidForUpdates = false;
		Long maxTimeGap = configurablePropertiesHolder.getProperty(UPDATES_API_MAX_TIME_GAP_FOR_UPDATES_API,
				Long.class);
		if (System.currentTimeMillis() - searchContext.getFromUpdatedDate() > maxTimeGap) {
			invalidForUpdates = true;
		}
		else if (diffInvalidateVersion(paramMap)) {
			invalidForUpdates = true;
		}
		return invalidForUpdates;
	}

	public boolean diffInvalidateVersion(final Map<String, String> paramMap) {

		Boolean checkInvalidateVersionFeatureEnabled = configurablePropertiesHolder
			.getProperty(APP_CACHE_INVALIDATE_VERSION_ENABLED, Boolean.class);
		String invalidateVersion = String
			.valueOf(configurablePropertiesHolder.getProperty(APP_CACHE_INVALIDATE_VERSION, Integer.class));
		return checkInvalidateVersionFeatureEnabled && Boolean.FALSE.equals(CollectionUtils.isEmpty(paramMap))
				&& paramMap.containsKey(INVALIDATE_VERSION)
				&& Boolean.FALSE.equals(invalidateVersion.equals(paramMap.get(INVALIDATE_VERSION)));
	}

	private void pushMetricTxnCountAndDateDiff(final ResponseDto responseDto, final Map<String, String> paramMap,
			final SearchContext searchContext) {
		try {
			// pushing data for non filter requests and page no 1 only
			if (Objects.nonNull(paramMap) && Boolean.FALSE.equals(searchContext.isFilterApplied())) {
				// push txnCount
				Long count = 0L;
				String firstTxnDateInEpoch = null;
				List<EsResponseTxn> esResponseTxns = new ArrayList<>();
				while (responseDto.getTxns().iterator().hasNext()) {
					EsResponseTxn esResponseTxn = responseDto.getTxns().iterator().next();
					if (count == 0) {
						firstTxnDateInEpoch = Utility
							.getEpochTime(esResponseTxn.getDateLabel() + SPACE + esResponseTxn.getTimeLabel());
					}
					count++;
					esResponseTxns.add(esResponseTxn);
				}
				responseDto.setTxns(esResponseTxns);
				metricsAgent.countMetricsWithCountTypeString(PAGE_NO_WISE_COUNT, count,
						PAGE_NO + COLON + searchContext.getPageNo());
				// push time diff
				if (StringUtils.isNotBlank(firstTxnDateInEpoch)) {
					metricsAgent.recordTimeDiffBetweenDates(FIRST_TRANSACTION_CURRENT_DATE_DIFF,
							new Date().getTime() - Long.parseLong(firstTxnDateInEpoch));
				}

			}
		}
		catch (Exception e) {
			log.error("Exception while pushing count and date diff metric : {}", CommonsUtility.exceptionFormatter(e));
		}
	}

	private Mono<ResponseDto> getListingData(final SearchContext searchContext, final Map<String, String> tokens,
			final Map<String, String> paramMap) {
		String id = ThreadContext.get("requestId");
		String client = paramMap.get(CLIENT);
		String langId = LocalisationUtility.validateAndSetLanguageId(paramMap.get(LOCALE), client);

		if (clientConfigService.isS2sClient(client)) {
			return this.getTxnResponse(searchContext, langId, paramMap, null).contextWrite(ctx -> {
				ThreadContext.put("requestId", id);
				return ctx;
			})
				.doOnError(e -> LogHelper
					.logOnError(ex -> log.error("Error creating listing response. Exception: {}, with reqId : {}",
							CommonsUtility.exceptionFormatter((Exception) ex), id)))
				.onErrorMap(e -> customizeExceptionAsPerFilter(e, searchContext));
		}

		return webUtilityService.validateAndGetUserId(tokens, id, paramMap).flatMap(validationResult -> {
			ThreadContext.put("requestId", id);
			if (searchContext.getEntityId() != null
					&& !validationResult.getUserId().equalsIgnoreCase(searchContext.getEntityId())) {
				log.info(
						"entity Id passed is different from user id fetched from token. Setting entityId as userId from token");
			}
			searchContext.setEntityId(validationResult.getUserId());

			/**
			 * in case if we use v2/user api in that case we will make use of
			 * customerCreationDate to shrink search range so will update from date
			 * accordingly
			 */
			if (Boolean.TRUE.equals(configurablePropertiesHolder.getProperty(
					ConfigurationPropertiesConstants.WebApiV1Constants.USE_OF_CUSTOMER_CREATION_DATE_ENABLED,
					Boolean.class))) {
				GenericUtility.updateFromDateAndAddMetrics(searchContext, validationResult, paramMap);
			}
			paramMap.put(ENTITY_ID, searchContext.getEntityId());
			boolean isBankDataFilter = getBankDataFilter(searchContext.isShowBankData(), validationResult);
			// showing bank data.
			searchContext.setShowBankData(isBankDataFilter);

			// Cache Context object contains all the relevant info for performing cache
			// related tasks
			CacheContext cacheContext = cacheUtility.getCacheContext(searchContext, paramMap);

			if (Objects.isNull(cacheContext) || !cacheContext.isRequestValidToThrow4010Error()) {
				return this.getTxnResponse(searchContext, langId, paramMap, cacheContext);
			}
			else {
				// Adding this for metrics purpose.
				paramMap.put(NO_TXN_PRESENT_IDENTIFIED_USING_CACHE, TRUE);
				log.error("No transaction fetched for userId : {} from : {} to today", searchContext.getEntityId(),
						DateTimeUtility.getDateTime(searchContext.getFromDate()));
				return Mono.error(ExceptionFactory.getCustomizedFromDateException(PANAROMA_SERVICE,
						NO_TRANSACTION_FOUND_FOR_LISTING, DateTimeUtility.getDateTimeForExp(
								configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class))));
			}
		}).contextWrite(ctx -> {
			ThreadContext.put("requestId", id);
			return ctx;
		}).onErrorMap(e -> {
			return customizeExceptionAsPerFilter(e, searchContext);
		})
			.doOnError(e -> LogHelper.logOnError(ex -> log.error("Error creating listing response. Exception: {}",
					CommonsUtility.exceptionFormatter((Exception) ex))));
	}

	private Mono<UpdateResponseDto> getListingUpdatesData(final SearchContext searchContext,
			final Map<String, String> tokens, final Map<String, String> paramMap) {
		String id = ThreadContext.get("requestId");
		String client = paramMap.get(CLIENT);
		String langId = LocalisationUtility.validateAndSetLanguageId(paramMap.get(LOCALE), client);

		if (clientConfigService.isS2sClient(client)) {
			return this.getTxnUpdateResponse(searchContext, langId, paramMap, CacheState.NORMAL).contextWrite(ctx -> {
				ThreadContext.put("requestId", id);
				return ctx;
			})
				.doOnError(e -> LogHelper
					.logOnError(ex -> log.error("Error creating listing response. Exception: {}, with reqId : {}",
							CommonsUtility.exceptionFormatter((Exception) ex), id)))
				.onErrorMap(e -> customizeExceptionAsPerFilter(e, searchContext));
		}

		return this.validateAndGetUserId(tokens, id, searchContext.isShowBankData()).flatMap(validationResult -> {
			ThreadContext.put("requestId", id);
			if (searchContext.getEntityId() != null
					&& !validationResult.getUserId().equalsIgnoreCase(searchContext.getEntityId())) {
				log.info(
						"entity Id passed is different from user id fetched from token. Setting entityId as userId from token");
			}
			searchContext.setEntityId(validationResult.getUserId());
			boolean isBankDataFilter = getBankDataFilter(searchContext.isShowBankData(), validationResult);
			// showing bank data.
			searchContext.setShowBankData(isBankDataFilter);
			CacheState cacheState = CacheState.NORMAL;
			try {
				if (searchContext.isForUpdates() && !searchContext.isInvalidForUpdates() && rolloutStrategyHelper
					.isUserWhiteListed("appCacheOptimisation", searchContext.getEntityId())) {
					AppSideCacheData cacheData = cacheUtility.getAppSideCacheData(searchContext,
							CacheInfo.UTH_APP_SIDE_CACHE_DATA);
					cacheState = getCacheState(cacheData);
					if (cacheState == CacheState.ZERO_DELTA && cacheData.getZeroDeltaCacheData()
						.getFromUpdatedDate() <= searchContext.getFromUpdatedDate()) {
						log.info("Returning empty response based on Zero Delta cache for user : {}",
								searchContext.getEntityId());
						metricsAgent.incrementCount(UPDATES_API_NO_TXNS,
								String.format(monitoringTagFormat, SERVED_FROM_ZERO_DELTA_CACHE, true));
						return emptyUpdatesApiResponse(searchContext, langId, paramMap);
					}
					else if (cacheState == CacheState.OLD_TXN_UPDATES) {
						modifySearchContextForOldTxns(searchContext, cacheData);
					}
				}
				long fromDate = searchContext.getFromDate();
				int timeGapInDays = (int) Math.ceil((System.currentTimeMillis() - fromDate) / (ONE_DAY_IN_MILLIS));
				metricsAgent.recordTimeGap(UPDATES_API_FROM_DATE_GAP_METRICS, timeGapInDays,
						String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)),
						String.format(monitoringTagFormat, "validForUpdates", searchContext.isForUpdates()),
						String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)));
			}
			catch (Exception e) {
				log.error("Cache couldn't be used due to exception. Serving via normal ES search.", e);
			}
			return this.getTxnUpdateResponse(searchContext, langId, paramMap, cacheState);
		}).contextWrite(ctx -> {
			ThreadContext.put("requestId", id);
			return ctx;
		})
			.onErrorMap(e -> customizeExceptionAsPerFilter(e, searchContext))
			.doOnError(e -> LogHelper.logOnError(ex -> log.error("Error creating listing response. Exception: {}",
					CommonsUtility.exceptionFormatter((Exception) ex))));
	}

	public void modifySearchContextForOldTxns(final SearchContext searchContext, final AppSideCacheData cacheData) {
		if (cacheData.getOldestTxnUpdateCacheData().getTxnDate() >= searchContext.getFromDate()) {
			metricsAgent.incrementCount("oldTxnDateCache", String.format(monitoringTagFormat, "valid", "false"),
					String.format(monitoringTagFormat, "reason", "tooRecentFromDate"));
			log.warn("cache stored date is more recent than default search context fromDate");
			return;
		}
		long timeGapSinceCachedTime = System.currentTimeMillis() - cacheData.getOldestTxnUpdateCacheData().getTxnDate();
		long maxLimitForGapInDays = configurablePropertiesHolder.getProperty(UPDATES_API_MAX_FROM_DATE_GAP_DAYS,
				Long.class);
		long maxLimitInEpochMilli = maxLimitForGapInDays * 24 * 60 * 60 * 1000;
		if (timeGapSinceCachedTime > maxLimitInEpochMilli) {
			metricsAgent.incrementCount("oldTxnDateCache", String.format(monitoringTagFormat, "valid", "false"),
					String.format(monitoringTagFormat, "reason", "tooOldFromDate"));
			log.warn("stored date is too old. stored value : {}, max allowed : {} days",
					cacheData.getOldestTxnUpdateCacheData().getTxnDate(), maxLimitForGapInDays);
			return;
		}
		searchContext.setFromDate(cacheData.getOldestTxnUpdateCacheData().getTxnDate());
		log.info("modified search context for oldest txns : {}", searchContext);
	}

	private Mono<UpdateResponseDto> emptyUpdatesApiResponse(final SearchContext searchContext, final String langId,
			final Map<String, String> paramMap) {

		PaginationParams paginationParams = new PaginationParams();
		paginationParams.setFromUpdatedDate(Long.toString(searchContext.getFromUpdatedDate()));

		RepoResponseSearchApiDto responseSearchApiDto = new RepoResponseSearchApiDto();
		responseSearchApiDto.setTransformedTransactionHistoryDetailsIterable(Collections.emptyList());
		responseSearchApiDto.setTotalHits(0L);
		responseSearchApiDto.setPaginationParams(paginationParams);

		Mono<RepoResponseSearchApiDto> repoResponseMono = Mono.just(responseSearchApiDto);

		return responseBuilder
			.buildTxnUpdateResponseDto(repoResponseMono, searchContext, langId, salaryReportCodes, paramMap,
					pushMissingUserImageListToKafka, fetchUserImageFromCache)
			.doOnError(e -> LogHelper.logOnError(ex -> log.error("error while receiving response from repo")));
	}

	public Exception customizeExceptionAsPerFilter(final Throwable exception, final SearchContext searchContext) {
		if (exception instanceof PanaromaException) {
			PanaromaException panaromaException = (PanaromaException) exception;

			if (searchContext.isForSearchAndFilter()) {
				return ExceptionFactory.getFilterCustomizedException(panaromaException,
						SEARCH_FILTER + panaromaException.getResponseCode(), null);
			}
			else if (searchContext.isForSearch()) {
				Map<String, String> dynamicMap = new HashMap<>();
				if (StringUtils.isNotBlank(searchContext.getQuery())) {
					dynamicMap.put(CAPITAL_QUERY, searchContext.getQuery());
				}
				else {
					dynamicMap.put(CAPITAL_QUERY, searchContext.getSearchQueryAsKeyword());
				}
				return ExceptionFactory.getFilterCustomizedException(panaromaException,
						SEARCH + panaromaException.getResponseCode(), dynamicMap);
			}
			else if (searchContext.isFilterApplied() && !searchContext.isPassbookFilter()
					&& NO_TRANSACTION_FOUND_FOR_LISTING.equals(panaromaException.getResponseCode())) {
				// Showing default filter msg for no txn found error for filters except
				// passbook filters.
				return ExceptionFactory.getFilterCustomizedException(panaromaException, DEFAULT, null);
			}
			else {
				return panaromaException;
			}
		}
		return (Exception) exception;
	}

	public Exception customizeExceptionAsPerFilter(final Throwable exception, final Map<String, String> paramMap) {
		PanaromaException panaromaException = null;
		if (exception instanceof PanaromaException) {
			panaromaException = (PanaromaException) exception;
		}
		else {
			panaromaException = ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		if (TRUE.equals(paramMap.get(IS_FOR_SEARCH_AND_FILTER))) {
			return ExceptionFactory.getFilterCustomizedException(panaromaException,
					SEARCH_FILTER + panaromaException.getResponseCode(), null);
		}
		else if (TRUE.equals(paramMap.get(IS_FOR_SEARCH))) {
			Map<String, String> dynamicMap = new HashMap<>();
			if (StringUtils.isNotBlank(paramMap.get(QUERY))) {
				dynamicMap.put(CAPITAL_QUERY, paramMap.get(QUERY));
			}
			else {
				dynamicMap.put(CAPITAL_QUERY, paramMap.get(SEARCH_QUERY_AS_KEYWORD));
			}
			return ExceptionFactory.getFilterCustomizedException(panaromaException,
					SEARCH + panaromaException.getResponseCode(), dynamicMap);
		}
		else if (NO_TRANSACTION_FOUND_FOR_LISTING.equals(panaromaException.getResponseCode())
				&& TRUE.equalsIgnoreCase(paramMap.get(IS_FILTER_APPLIED))
				&& !TRUE.equalsIgnoreCase(paramMap.get(PASSBOOK_FILTER))) {
			// Showing default filter msg for no txn found error for filters except
			// passbook filters.
			return ExceptionFactory.getFilterCustomizedException(panaromaException, DEFAULT, null);
		}
		else {
			return panaromaException;
		}
	}

	@Override
	public Mono<DetailApiResponse> getDetails(final TransactionSource transactionSource, final String passedIds,
			final Map<String, String> tokenMap, final boolean showBankData, final String client, final String locale,
			final String userId) throws PanaromaException {
		try {
			// pair's key contains txnId and value contains groupId (if present) in the
			// below pair
			String[] idPair = WebControllerUtility.getIdToQueryEs(passedIds);
			String txnId = idPair[0];
			String txnDate = idPair[1];
			// GroupId will be null if it's not there in txnId
			String groupId = idPair[3];

			DetailInputParams detailInputParams = new DetailInputParams();
			detailInputParams.setClient(client);
			ExtraDetailRequestFields extraDetailRequestFields = new ExtraDetailRequestFields();
			extraDetailRequestFields.setTxnDate(txnDate);
			return getDetailsInternal(transactionSource, txnId, tokenMap, showBankData, detailInputParams, locale,
					userId, groupId, extraDetailRequestFields);
		}
		catch (Exception ex) {
			return Mono.error(ex);
		}
	}

	private Mono<DetailApiResponse> getDetailsInternal(final TransactionSource transactionSource, final String txnId,
			final Map<String, String> tokenMap, final boolean showBankData, final DetailInputParams detailInputParams,
			final String locale, final String userId, final String groupId,
			final ExtraDetailRequestFields extraDetailRequestFields) throws PanaromaException {
		Optional<Mono> monoOptional = checkKnownIssue();
		if (monoOptional.isPresent()) {
			return monoOptional.get();
		}

		if (Objects.isNull(extraDetailRequestFields) || StringUtils.isBlank(extraDetailRequestFields.getTxnDate())) {
			String openSource = Objects.nonNull(extraDetailRequestFields)
					&& StringUtils.isNotBlank(extraDetailRequestFields.getOpenSource())
							? extraDetailRequestFields.getOpenSource() : NOT_AVAILABLE;
			Boolean throwErrorIfTxnDateAbsent = configurablePropertiesHolder
				.getProperty(TXN_DATE_MANDATORY_FOR_DETAIL_API, Boolean.class);
			log.error(
					"txnDate absent for Detail API request for txnId : {}, openSource : {}. Error thrown on the basis of value"
							+ "of txn-date-mandatory-for-detail-api flag : {}",
					txnId, openSource, throwErrorIfTxnDateAbsent);
			metricsAgent.incrementCount(TXN_DATE_ABSENT_IN_DETAIL_REQUEST, OPEN_SOURCE + COLON + openSource,
					ERROR_THROWN + COLON + throwErrorIfTxnDateAbsent);
			// Adding this check to not hit payment_history_alias for details api in any
			// case whenever txn-date-mandatory-for-detail-api flag is true
			if (throwErrorIfTxnDateAbsent) {
				throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
			}
		}
		if (Objects.nonNull(extraDetailRequestFields) && StringUtils.isNotBlank(extraDetailRequestFields.getTxnDate())
				&& StringUtils.isNotBlank(extraDetailRequestFields.getOpenSource())) {
			GenericUtilityExtension.isReqNeedToBeServed(transactionSource.getTransactionSource(),
					extraDetailRequestFields, detailInputParams);
		}

		String requestId = ThreadContext.get("requestId");
		String client = detailInputParams.getClient();
		String langId = LocalisationUtility.validateAndSetLanguageId(locale, client);
		if (clientConfigService.isS2sClient(client)) {
			return this
				.getDetailResponse(userId, txnId, transactionSource,
						clientConfigService.getClientConfigMap().get(client).isShowBankData(), detailInputParams,
						langId, locale, groupId, extraDetailRequestFields)
				.contextWrite(ctx -> {
					ThreadContext.put("requestId", requestId);
					return ctx;
				});
		}
		return webUtilityService.validateAndGetUserId(tokenMap, requestId, MonitoringConstants.OAUTH_SERVICE_USER_API)
			.flatMap(validationResult -> {
				try {
					ThreadContext.put(WebConstants.REQUEST_ID, requestId);
					ThreadContext.put(WebConstants.USERID, validationResult.getUserId());
					boolean isBankDataFilter = getBankDataFilter(showBankData, validationResult);
					return this.getDetailResponse(validationResult.getUserId(), txnId, transactionSource,
							isBankDataFilter, detailInputParams, langId, locale, groupId, extraDetailRequestFields);
				}
				catch (Exception ex) {
					return Mono.error(ex);
				}
			})
			.contextWrite(ctx -> {
				ThreadContext.put("requestId", requestId);
				return ctx;
			});
	}

	private boolean getBankDataFilter(final boolean showBankData, final ValidationResult validationResult) {
		boolean isBankDataFilter = showBankDataProp;
		if (showBankDataProp) {
			/*
			 * if (validationResult.isKycConsentPresent()) {
			 * log.info("Kyc consent is present, including bank data"); } else { log.
			 * info("Kyc consent is not present, so setting the showBankData as false and not including bankData"
			 * ); isBankDataFilter = false; }
			 */
		}
		else {
			// log.info("showBankData flag is false, so not including bank data");
			if (whiteListedBankUserId.size() > 0 && whiteListedBankUserId.contains(validationResult.getUserId())) {
				log.info("including bank data as user is whitelisted");
				isBankDataFilter = true;
			}
		}
		return isBankDataFilter;
	}

	private Mono<DetailApiResponse> getDetailResponse(final String userId, final String txnId,
			final TransactionSource transactionSource, final boolean isBankData,
			final DetailInputParams detailInputParams, final String langId, final String locale, final String groupId,
			final ExtraDetailRequestFields extraDetailRequestFields) {
		// TODO : handling of showBankData is false
		Mono<List<TransformedTransactionHistoryDetail>> detailedList = getCachedDetails(userId, txnId,
				transactionSource, isBankData, groupId, extraDetailRequestFields);
		String openSource = extraDetailRequestFields == null ? null : extraDetailRequestFields.getOpenSource();
		return ResponseBuilder
			.buildDetailApiResponse(detailedList, userId, txnId, detailInputParams, langId, locale,
					whiteListedLocalisationUserId, extraDetailRequestFields, iRolloutStrategyHelper)
			.doOnEach(LogHelper.logOnCompletion(res -> log
				.debug("successfully created response for detail API {}. DetailV2 Source: {}", res, openSource)))
			.doOnError(e -> LogHelper.logOnError(
					ex -> log.error("error while creating response for detail. DetailV2 Source: {}", openSource)));
	}

	private void pushDetailCacheMonitoringMetrics(final String txnId,
			final List<TransformedTransactionHistoryDetail> detailList, final boolean isCached,
			final Long cacheCreatedDate) {
		detailList.forEach(tthd -> {
			if (txnId.equalsIgnoreCase(tthd.getTxnId())) {
				metricsAgent.recordTimeDiffBetweenDates(TXN_DATE_DIFFERENCE,
						System.currentTimeMillis() - tthd.getTxnDate(), "api:" + DETAIL_API,
						STATUS + COLON + ClientStatusEnum.getStatusEnumByKey(tthd.getStatus()).getStatusValue(),
						"CACHE_STATUS" + COLON + isCached);

				if (Objects.nonNull(cacheCreatedDate)) {
					metricsAgent.recordTimeDiffBetweenDates(CACHE_DATE_DIFFERENCE,
							System.currentTimeMillis() - cacheCreatedDate, "api:" + DETAIL_API,
							STATUS + COLON + ClientStatusEnum.getStatusEnumByKey(tthd.getStatus()).getStatusValue(),
							"CACHE_STATUS" + COLON + isCached);
				}
			}
		});
	}

	private Mono<List<TransformedTransactionHistoryDetail>> getCachedDetails(final String userId, final String txnId,
			final TransactionSource transactionSource, final Boolean isBankData, final String groupId,
			final ExtraDetailRequestFields extraDetailRequestFields) {
		boolean isCacheEnabled = featureRoleOutStrategyUtility.checkFeatureEnabled(userId,
				DETAIL_PAGE_API_CACHING_ENABLED, DETAIL_PAGE_ROLE_OUT_FEATURE_NAME, Boolean.FALSE);
		if (isCacheEnabled) {
			String detailCacheKey = CacheKeyManager.getDetailPageCacheKey(userId, txnId, transactionSource, isBankData);
			Optional<CacheValueWrapperDto<List<TransformedTransactionHistoryDetail>>> cachedDetailData = detailsCacheWrapper
				.get(detailCacheKey);
			if (cachedDetailData.isPresent()) {
				CacheValueWrapperDto<List<TransformedTransactionHistoryDetail>> cacheValueWrapperDto = cachedDetailData
					.get();
				List<TransformedTransactionHistoryDetail> tthdList = cacheValueWrapperDto.getValue();
				pushDetailCacheMonitoringMetrics(txnId, tthdList, Boolean.TRUE, cacheValueWrapperDto.getCreatedDate());
				log.info("inside getCachedDetails() records present in cache key :- {} ", detailCacheKey);
				return Mono.just(tthdList);
			}
		}
		Flux<TransformedTransactionHistoryDetail> detailedList = repo
			.getDetails(userId, txnId, transactionSource.getTransactionSourceKey(), isBankData, groupId,
					extraDetailRequestFields)
			.map(data -> {
				auditUtility.pushDataToKafkaForAudit(data);
				return data;
			});
		return detailedList.collectList().map(data -> {
			if (isCacheEnabled) {
				pushDetailCacheMonitoringMetrics(txnId, data, Boolean.FALSE, null);
				String detailCacheKey = CacheKeyManager.getDetailPageCacheKey(userId, txnId, transactionSource,
						isBankData);
				log.info("inside getCachedDetails() put record in cache key :- {}", detailCacheKey);
				CacheValueWrapperDto<List<TransformedTransactionHistoryDetail>> cacheValueWrapperDto = new CacheValueWrapperDto<>(
						data, System.currentTimeMillis());
				Optional<Duration> ttlOptional = getDynamicDetailCacheTtl(data, txnId);
				if (ttlOptional.isPresent()) {
					detailsCacheWrapper.put(detailCacheKey, cacheValueWrapperDto, ttlOptional.get());
				}
				else {
					detailsCacheWrapper.put(detailCacheKey, cacheValueWrapperDto);
				}
			}
			return data;
		});
	}

	private Optional<Duration> getDynamicDetailCacheTtl(final List<TransformedTransactionHistoryDetail> data,
			final String txnId) {
		for (TransformedTransactionHistoryDetail tthd : data) {
			if (tthd.getTxnId().equalsIgnoreCase(txnId)) {
				Duration duration = Duration.ofMillis(System.currentTimeMillis() - tthd.getTxnDate());
				log.info("inside getDynamicDetailCacheTtl() txnId:- {} duration:- {}", txnId, duration);
				if (ClientStatusEnum.PENDING.getStatusKey().equals(tthd.getStatus())) {
					return Optional.empty();
				}
				else {
					if (duration.getSeconds() >= CacheConstants.CacheDuration.TXN_GAP_DAYS_3.getTxnGap().getSeconds()) {
						return Optional.of(CacheConstants.CacheDuration.TXN_GAP_DAYS_3.getTtl());
					}
					else if (duration.getSeconds() >= CacheConstants.CacheDuration.TXN_GAP_DAYS_1.getTxnGap()
						.getSeconds()) {
						return Optional.of(CacheConstants.CacheDuration.TXN_GAP_DAYS_1.getTtl());
					}
					else {
						return Optional.of(CacheConstants.CacheDuration.TXN_GAP_DAYS_0.getTtl());
					}
				}
			}
		}
		return Optional.empty();
	}

	private Mono<ResponseDto> getTxnResponse(final SearchContext searchContext, final String langId,
			final Map<String, String> paramMap, final CacheContext cacheContext) {
		Mono<RepoResponseSearchApiDto> repoResponseMono;

		// Ignoring Progressive Search if Date Range Filter is applied or filterSource is
		// spendPage on request
		if (SPEND_PAGE.equalsIgnoreCase(paramMap.get(FILTER_SOURCE))
				|| (StringUtils.isNotBlank(searchContext.getEsDateRange())
						&& searchContext.getEsDateRange().contains(","))) {
			// the above esDateRange checks means go to blocking when multiple data ranges
			// used
			repoResponseMono = esBlockingRepo.search(searchContext, paramMap);
		}
		else {
			repoResponseMono = repo.search(searchContext, paramMap, cacheContext);
		}

		return responseBuilder
			.buildTxnListingResponseDto(repoResponseMono, searchContext, langId, salaryReportCodes, paramMap,
					pushMissingUserImageListToKafka, fetchUserImageFromCache)
			.flatMap(response -> {
				if (LocalisationUtility.needLocalisation(langId) && (whiteListedLocalisationUserId.contains("-1")
						|| whiteListedLocalisationUserId.contains(searchContext.getEntityId()))) {
					return Mono.just(
							LocalisationUtility.localiseListingDetails(response, langId, searchContext.getEntityId()));
				}
				return Mono.just(response);
			})
			.doOnError(e -> LogHelper.logOnError(ex -> log.error("error while receiving response from repo")));
	}

	private Mono<UpdateResponseDto> getTxnUpdateResponse(final SearchContext searchContext, final String langId,
			final Map<String, String> paramMap, final CacheState storedCacheState) {
		Mono<RepoResponseSearchApiDto> repoResponseMono;
		repoResponseMono = repo.search(searchContext, paramMap, null);
		repoResponseMono = repoResponseMono.map(response -> {
			if (searchContext.isForUpdates() && !searchContext.isInvalidForUpdates()
					&& checkIfResponseHasNoUpdates(response, searchContext)) {
				metricsAgent.incrementCount(UPDATES_API_NO_TXNS,
						String.format(monitoringTagFormat, SERVED_FROM_ZERO_DELTA_CACHE, false));
				cacheUtility.pushAppSideCacheDataToKafkaForCache(searchContext, CacheState.ZERO_DELTA, false);
				modifyPaginationParamsForSingleTxn(response);
			}
			else if (response.getTransformedTransactionHistoryDetailsIterable().size() == searchContext.getPageSize()
					&& searchContext.isForUpdates()) {
				metricsAgent.incrementCount(UPDATES_API_INVALIDATION,
						String.format(monitoringTagFormat, CRITERIA, "txnSize"),
						String.format(monitoringTagFormat, CLIENT, paramMap.get(CLIENT)),
						String.format(monitoringTagFormat, APP_VERSION, paramMap.get(APP_VERSION)));
				log.warn("Update search from ES returned {} entries. Need to search ES again for search response",
						response.getTransformedTransactionHistoryDetailsIterable().size());
				searchContext.setInvalidForUpdates(true);
				searchContext.setPageSize(searchContext.getSearchPageSize());
				searchContext.setFromUpdatedDate(null);
				searchContext.setShowInListing(TRUE);
				searchContext.setPaginationTxnId(null);
				searchContext.setPaginationStreamSource(null);
				searchContext.setTransactionDateEpoch(null);
				searchContext.setPageNo(1);
				return repo.searchWithoutMono(searchContext, paramMap, null);
			}
			else if (storedCacheState == CacheState.OLD_TXN_UPDATES) {
				cacheUtility.pushAppSideCacheDataToKafkaForCache(searchContext, CacheState.OLD_TXN_UPDATES, false);
			}

			return response;
		});

		return responseBuilder
			.buildTxnUpdateResponseDto(repoResponseMono, searchContext, langId, salaryReportCodes, paramMap,
					pushMissingUserImageListToKafka, fetchUserImageFromCache)
			.doOnError(e -> LogHelper.logOnError(ex -> log.error("error while receiving response from repo")));
	}

	public void modifyPaginationParamsForSingleTxn(final RepoResponseSearchApiDto response) {
		if (response.getPaginationParams() == null) {
			return;
		}
		if (StringUtils.isEmpty(response.getPaginationParams().getFromUpdatedDate())) {
			return;
		}
		long fromUpdatedDate = Long.parseLong(response.getPaginationParams().getFromUpdatedDate()) + 1;
		long previousDate = System.currentTimeMillis() - 24 * 60 * 60 * 1000L; // so that
																				// non
																				// transacting
																				// users
																				// don't
																				// go out
																				// of
																				// range
																				// for
																				// updates
		long paginationValue = Math.max(fromUpdatedDate, previousDate);
		response.getPaginationParams().setFromUpdatedDate(Long.toString(paginationValue));
	}

	public boolean checkIfResponseHasNoUpdates(final RepoResponseSearchApiDto response,
			final SearchContext searchContext) {
		if (response.getTransformedTransactionHistoryDetailsIterable().isEmpty()) {
			return true;
		}
		if (response.getTransformedTransactionHistoryDetailsIterable().size() > 1) {
			return false;
		}
		/*
		 * Below is the scenario where the txn fetched is the same served before, due to
		 * the inclusive setup of updates API.
		 */
		TransformedTransactionHistoryDetail entry = response.getTransformedTransactionHistoryDetailsIterable().get(0);
		return Objects.equals(entry.getDocUpdatedDate(), searchContext.getFromUpdatedDate());
	}

	public Mono<ValidationResult> validateAndGetUserId(final Map<String, String> tokens, final String reqId,
			final boolean showBankData) {

		log.debug("trying to get response from Oauth for reqId {} ", reqId);
		String oauthToken = TokenValidatorUtility.getTokenFromMap(tokens.get(AUTHORIZATION), USER_TOKEN);
		if (StringUtils.isBlank(oauthToken)) {
			log.error("User Token is empty or null for reqId {} ", reqId);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}
		long webClientstartTime = System.currentTimeMillis();

		if (!authDownStreamCircuitBreakerUtil.isDownstreamApiCallAllowed(AUTH_DOWNSTREAM_NAME)) {
			log.error("Skipping call to Auth. Circuit breaker Opened state for natty");
			throw ExceptionFactory.getException(PANAROMA_SERVICE, OAUTH_ERROR_GENERIC);
		}

		Mono<CustomOauthUserDetailsResponse> detailsResponseMono = oauthClient
			.getUserDetailsFromOauth(oauthToken, USER_ID_DEFAULT, reqId)
			.doOnError(e -> {
				if (e instanceof ConnectTimeoutException || e instanceof SocketTimeoutException
						|| e instanceof TimeoutException || e instanceof HttpTimeoutException) {
					authDownStreamCircuitBreakerUtil.incrementFailureCount(AUTH_DOWNSTREAM_NAME);
					log.error(
							"Exception in Oauth client Exchange method in DefaultTransactionHistory Service: {}, requestId : {}",
							CommonsUtility.exceptionFormatterWithoutStacktrace((Exception) e));
				}
				else {
					log.error(
							"Exception in Oauth client Exchange method in DefaultTransactionHistory Service: {}, requestId : {}",
							CommonsUtility.exceptionFormatter((Exception) e));
				}
				ExceptionHandlerUtil.handleOauthValidationException(e, reqId, webClientstartTime);
			})
			.doOnSuccess(response -> this.handleOauthResponse(response, reqId));
		Mono<ValidationResult> validationResultMono = detailsResponseMono
			.flatMap(response -> Mono.just(ValidationResult.builder()
				.userId(response.getUserId())
				.customerCreationDate(response.getDefaultInfo().getCustomerCreationDate())
				.build()))
			.contextWrite(ctx -> {
				ThreadContext.put("requestId", reqId);
				return ctx;
			});
		// TODO : integrate TncService.getKycConsent() and return the combined mono of
		// validationResult with both data.
		return validationResultMono.elapsed().flatMap(obj -> {
			log.debug("Time taken by oauth service to process user api request : {} . latency :{}. reqId {}",
					obj.getT1(), System.currentTimeMillis() - webClientstartTime, reqId);
			metricsAgent.recordApiExecutionTime(MonitoringConstants.OAUTH_SERVICE_USER_API, obj.getT1());
			return Mono.just(obj.getT2());
		})
			.doOnError(e -> LogHelper.logOnError(ex -> log.error("Error creating listing response. Exception: {}",
					CommonsUtility.exceptionFormatter((Exception) ex))))
			.contextWrite(ctx -> {
				ThreadContext.put("requestId", reqId);
				return ctx;
			});
	}

	public void handleOauthResponse(final CustomOauthUserDetailsResponse response, final String reqId) {
		if (response.getStatus() == null) {
			log.debug("Successful response from Oauth for reqId: {}, userId: {}", reqId, response.getUserId());
		}
		else {
			log.error("Error response from Oauth for reqId: {}, response: {}", reqId, response);
			ExceptionHandlerUtil.handleOauthValidationException(response);
		}

	}

	public Mono<DetailApiResponseV2> getDetailApiResponseV2(final TransactionSource transactionSource,
			final Map<String, String> tokenMap, final boolean showBankData, final String locale, final String userId,
			final DetailInputParams detailInputParams, final ExtraDetailRequestFields extraDetailRequestFields,
			final String txnId, final String groupId, final TimelineFieldsDto timelineFieldsDto) {

		Mono<DetailApiResponse> detailApiResponseMono = getDetailsInternal(transactionSource, txnId, tokenMap,
				showBankData, detailInputParams, locale, userId, groupId, extraDetailRequestFields);
		return detailApiResponseMono.map(detailApiResponse -> {
			DetailApiResponseV2 detailApiResponseV2 = DetailResponseV2Mapper.getDetailResponseV2(detailApiResponse,
					detailInputParams.getListingVisibleTxn(), timelineFieldsDto,
					detailInputParams.getDetailCallVersion());
			ShareScreenEnricher.enrichShareDetailInDetailResponseV2(detailApiResponseV2);

			detailResponseV2Enricher.enrichDetailsV2(detailApiResponseV2, detailInputParams.getListingVisibleTxn());
			return detailApiResponseV2;
		});
	}

	@Override
	public Mono<DetailApiResponseV2> getDetailsV2(final TransactionSource transactionSource, final String passedIds,
			final Map<String, String> tokenMap, final boolean showBankData, final ClientStatusEnum status,
			final String client, final String locale, final String userId, final boolean showOnlyTimeline,
			final ExtraDetailRequestFields extraDetailRequestFields) throws PanaromaException {

		try {
			String appVersion = null;
			String openSource = null;
			if (extraDetailRequestFields != null) {
				appVersion = extraDetailRequestFields.getAppVersion();
				openSource = extraDetailRequestFields.getOpenSource();
			}
			TimelineFieldsDto timelineFieldsDto = new TimelineFieldsDto(appVersion, client);
			// pair's key contains txnId and value contains groupId (if present) in the
			// below pair
			String[] idPair = WebControllerUtility.getIdToQueryEs(passedIds);
			String txnId = idPair[0];
			String txnDate = idPair[1];
			// GroupId will be null if it's not there in txnId
			String groupId = idPair[3];

			extraDetailRequestFields.setTxnDate(txnDate);
			if (showOnlyTimeline) {
				return getOnlyTimelineResponse(transactionSource, tokenMap, status, showOnlyTimeline, openSource,
						timelineFieldsDto, txnId);
			}
			else {
				DetailInputParams detailInputParams = getDetailInputParams(client, DETAIL_V2);
				Mono<DetailApiResponseV2> detailApiResponseV2Mono = getDetailApiResponseV2(transactionSource, tokenMap,
						showBankData, locale, userId, detailInputParams, extraDetailRequestFields, txnId, groupId,
						timelineFieldsDto);
				return detailApiResponseV2Mono.map(detailApiResponseV2 -> {
					// apply whitelisting on %on response.
					localiseDetailResponse(client, locale, userId, txnId, detailApiResponseV2);
					log.info("DetailApiResponseV2 Mapped response from detailApiResponse for txnId: {} is {}", txnId,
							detailApiResponseV2);
					return detailApiResponseV2;
				});
			}
		}
		catch (Exception ex) {
			log.error("DetailApiResponseV2 error {}", Mono.error(ex));
			return Mono.error(ex);
		}
	}

	private Mono<DetailApiResponseV2> getOnlyTimelineResponse(final TransactionSource transactionSource,
			final Map<String, String> tokenMap, final ClientStatusEnum status, final boolean showOnlyTimeline,
			final String openSource, final TimelineFieldsDto timelineFieldsDto, final String txnId) throws Exception {
		if (Boolean.parseBoolean(
				configurablePropertiesHolder.getProperty(ConfigPropertiesEnum.TIMELINE_ENABLED, String.class))) {
			log.info("Request received for timeline txnId: {} , Detail Source: {}", txnId, openSource);
			Mono<DetailApiResponseV2> timeLineResponse = getTimelineResponse(transactionSource, txnId, status,
					showOnlyTimeline, timelineFieldsDto);
			return timeLineDataWithTokenValidation(tokenMap, txnId, showOnlyTimeline, timeLineResponse);
		}
		else {
			log.error(
					"Error Got Request for timeline where timeline is disable Source: {}, TxnId: {}, Detail Source: {}",
					transactionSource, txnId, openSource);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, KNOWN_ISSUE_ERROR);
		}
	}

	@Override
	public Mono<DetailApiResponseV3> getDetailsV3(final DetailRequestV3 detailRequestV3,
			final Map<String, String> tokenMap) throws PanaromaException {

		try {
			TimelineFieldsDto timelineFieldsDto = new TimelineFieldsDto(detailRequestV3.getAppVersion(),
					detailRequestV3.getClient());

			if (detailRequestV3.isShowOnlyTimeline()) {
				return getOnlyTimelineResponse(detailRequestV3.getTransactionSource(), tokenMap,
						detailRequestV3.getStatus(), detailRequestV3.isShowOnlyTimeline(),
						detailRequestV3.getOpenSource(), timelineFieldsDto, detailRequestV3.getTxnIdToQueryEs())
					.map(DetailResponseV3Mapper::mapDetailResponseV3UsingDetailResponseV2);
			}
			DetailInputParams detailInputParams = getDetailInputParams(detailRequestV3.getClient(), DETAIL_V3);

			/*
			 * For few detail api hits for eg. when openSource = uth_detail, we don't get
			 * txnDate value appended in txnId field but we get it in separate txnDate
			 * field in request body
			 */
			String txnDate = StringUtils.isNotBlank(detailRequestV3.getTxnDateToQueryEs())
					? detailRequestV3.getTxnDateToQueryEs() : detailRequestV3.getTxnDate();

			Mono<DetailApiResponseV2> detailApiResponseV2Mono = getDetailApiResponseV2(
					detailRequestV3.getTransactionSource(), tokenMap, detailRequestV3.isShowBankData(),
					detailRequestV3.getLocale(), detailRequestV3.getUserId(), detailInputParams,
					new ExtraDetailRequestFields(detailRequestV3.getAppVersion(), detailRequestV3.getOpenSource(),
							txnDate),
					detailRequestV3.getTxnIdToQueryEs(), detailRequestV3.getGroupIdToQueryEs(), timelineFieldsDto);

			return detailApiResponseV2Mono.map(detailApiResponseV2 -> {
				DetailApiResponseV3 detailApiResponseV3 = DetailResponseV3Mapper.getDetailEnhancedResponseForV3(
						detailApiResponseV2, detailInputParams.getListingVisibleTxn(),
						detailInputParams.getIsAccountNumberUnmaskingEnabled(), detailRequestV3.getLocale());

				ruleEngineService.applyRules(detailInputParams.getListingVisibleTxn(), detailApiResponseV3);
				FooterLogoUtility.updatePoweredByFooterLogo(detailApiResponseV3, detailRequestV3.getTheme(),
						getFooterLogoTxnType(detailApiResponseV3, detailInputParams.getListingVisibleTxn()),
						detailInputParams.getListingVisibleTxn());
				DetailVersionResponseMapper.filterFieldsForDetailVersion(detailApiResponseV3,
						Double.parseDouble(detailRequestV3.getDetailVersion()),
						detailInputParams.getListingVisibleTxn());

				localiseDetailResponse(detailRequestV3.getClient(), detailRequestV3.getLocale(),
						detailRequestV3.getUserId(), detailRequestV3.getTxnIdToQueryEs(), detailApiResponseV3);
				log.info("DetailApiResponseV3 Mapped response for txnId: {} is {}", detailRequestV3.getTxnIdToQueryEs(),
						detailApiResponseV3);
				return detailApiResponseV3;
			});
		}
		catch (Exception ex) {
			log.error("DetailApiResponseV3 error for txnId :{} error {}", detailRequestV3.getTxnId(), Mono.error(ex));
			return Mono.error(ex);
		}
	}

	private DetailInputParams getDetailInputParams(final String client, final String apiVersion) {
		DetailInputParams detailInputParams = new DetailInputParams();
		detailInputParams.setClient(client);
		detailInputParams.setIsAccountNumberUnmaskingEnabled(enableUnmaskAccountNumber);
		detailInputParams.setDetailCallVersion(apiVersion);
		return detailInputParams;
	}

	private void localiseDetailResponse(final String client, final String locale, final String userId,
			final String txnId, final Object detailApiResponse) {
		String langId = LocalisationUtility.validateAndSetLanguageId(locale, client);
		if (LocalisationUtility.needLocalisation(langId)
				&& (whiteListedLocalisationUserId.contains("-1") || whiteListedLocalisationUserId.contains(userId))) {
			LocalisationUtility.localiseDetailApiResponse(detailApiResponse, langId, txnId);
		}
	}

	private Mono<DetailApiResponseV2> getTimelineResponse(final TransactionSource source, final String txnId,
			final ClientStatusEnum status, final boolean showOnlyTimeline, final TimelineFieldsDto timelineFieldsDto)
			throws Exception {
		ITimelineService timelineService = iTimelineServiceFactory.getTimelineServiceBySource(source);

		if (timelineService == null) {
			log.error("Not Enabled Timeline For This Source: {}, TxnId: {}", source, txnId);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		Mono<Timeline> response = upiTimelineService.getTimelineResponse(source, txnId, status, showOnlyTimeline,
				timelineFieldsDto);

		return response.flatMap(timelineResponse -> {
			DetailApiResponseV2 detailApiResponse = new DetailApiResponseV2();
			detailApiResponse.setTimeline(timelineResponse);
			log.info("Timeline Response: {} for txnId: {}, ", timelineResponse, txnId);
			return Mono.just(detailApiResponse);
		});
	}

	private Mono<DetailApiResponseV2> timeLineDataWithTokenValidation(final Map<String, String> tokenMap,
			final String txnId, final boolean showOnlyTimeline, final Mono<DetailApiResponseV2> timeLineResponse) {
		ensureObjMapperInitialized();
		if (Objects.isNull(tokenMap)) {
			log.error("Either token map or userID is null for this txnId: {}", txnId);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}
		Mono<ValidationResult> validationResult = webUtilityService.validateAndGetUserId(tokenMap, txnId,
				MonitoringConstants.OAUTH_SERVICE_USER_API);
		final String[] userId = new String[1];
		final Boolean[] isTokenValidated = { Boolean.FALSE };
		validationResult.subscribe(valResult -> {
			userId[0] = valResult.getUserId();
		});
		return timeLineResponse.flatMap(response -> {
			if (Objects.nonNull(response.getTimeline().getResponse())) {
				UpiTimelineApiResponse upiTimelineApiResponse = objectMapper
					.convertValue(response.getTimeline().getResponse(), UpiTimelineApiResponse.class);
				log.debug("UpiTimelineResponse : {} for this txnId :{}, ", upiTimelineApiResponse, txnId);
				isTokenValidated[0] = StringUtils.equals(userId[0],
						upiTimelineApiResponse.getPayerDetails().getCustId());
				response.setUserId(userId[0]);
				response.setRequestId(ThreadContext.get(WebConstants.REQUEST_ID));
				if (isTokenValidated[0]) {
					return timeLineResponse;
				}
				else {
					return getTimelineResponseForAuthMismatch(timeLineResponse, txnId);
				}
			}
			else {
				return getTimelineResponseForAuthMismatch(timeLineResponse, txnId);
			}
		});
	}

	private Mono<DetailApiResponseV2> getTimelineResponseForAuthMismatch(
			final Mono<DetailApiResponseV2> timeLineResponse, final String txnId) {
		return timeLineResponse.flatMap(timeLineresponse -> {
			Timeline timeline = timeLineresponse.getTimeline();
			timeline.setResponse(null);
			timeline.setIsAvailable(Boolean.FALSE);
			timeline.setRefreshTimeline(Boolean.FALSE);
			String errorMsg = TIMELINE_ERROR_MSG_MAP.get(TimelineErrorMsgMapperDto.builder()
				.showOnlyTimeline(Boolean.TRUE)
				.timelineErrorType(TimelineErrorType.AUTHENTICATION_ERROR)
				.build());
			timeline.setMessage(errorMsg);
			timeLineresponse.setTimeline(timeline);
			log.info("Timeline Response for authMismatch: {} for txnId: {}, ", timeLineresponse.getTimeline(), txnId);
			return Mono.just(timeLineresponse);
		});
	}

}