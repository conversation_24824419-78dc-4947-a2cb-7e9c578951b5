package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.AuditConstants.AUDIT_DATA_KAFKA_PUSH;
import static com.org.panaroma.commons.constants.AuditConstants.DUAL_WRITE_AUDIT_CLIENT;
import static com.org.panaroma.commons.constants.AuditConstants.DUAL_WRITE_AUDIT_START_DATE;
import static com.org.panaroma.commons.constants.AuditConstants.MIN_TIME_DIFF_FOR_DUAL_WRITE_AUDIT;
import static com.org.panaroma.commons.constants.AuditConstants.PUSH_TO_KAFKA_FOR_AUDIT_ENABLED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.monitoring.MonitoringConstants.FAILURE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.STATUS;

import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.stereotype.Component;

import java.text.ParseException;

@Component
@Log4j2
public class AuditUtility {

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	private final IKafkaClient kafkaClient;

	private final MetricsAgent metricsAgent;

	AuditUtility(final ConfigurablePropertiesHolder configurablePropertiesHolder, final IKafkaClient kafkaClient,
			final MetricsAgent metricsAgent) {
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.kafkaClient = kafkaClient;
		this.metricsAgent = metricsAgent;
	}

	public void pushDataToKafkaForAudit(final TransformedTransactionHistoryDetail tthd) {
		if (eligibleForAudit(tthd)) {
			try {
				kafkaClient.pushIntoKafka(DUAL_WRITE_AUDIT_CLIENT, tthd.getEntityId(), tthd);
			}
			catch (TaskRejectedException ex) {
				metricsAgent.incrementCount(AUDIT_DATA_KAFKA_PUSH, STATUS + COLON + FAILURE);
				log.error("Async task rejected for pushing audit data with uid : {}, reason : {}", tthd.getTxnId(),
						CommonsUtility.exceptionFormatter(ex));
			}
			catch (Exception ex) {
				metricsAgent.incrementCount(AUDIT_DATA_KAFKA_PUSH, STATUS + COLON + FAILURE);
				log.error("Exception while pushing data to kafka for audit with uid : {}. Exception : {}",
						tthd.getTxnId(), CommonsUtility.exceptionFormatter(ex));
			}
		}
	}

	private boolean eligibleForAudit(final TransformedTransactionHistoryDetail tthd) {
		if (!configurablePropertiesHolder.getProperty(PUSH_TO_KAFKA_FOR_AUDIT_ENABLED, Boolean.class)) {
			return false;
		}
		long dualWriteAuditStartTime = 0;
		String dualWriteAuditStartTimeInString = configurablePropertiesHolder.getProperty(DUAL_WRITE_AUDIT_START_DATE,
				String.class);
		try {
			dualWriteAuditStartTime = DateTimeUtility.getTimeFromStringFormattedDate(dualWriteAuditStartTimeInString);
		}
		catch (Exception e) {
			log.error("Exception while parsing date for dualWriteAudit date : {} Exception : {}",
					dualWriteAuditStartTimeInString, CommonsUtility.exceptionFormatter(e));
		}

		if (dualWriteAuditStartTime > tthd.getTxnDate()) {
			return false;
		}

		return System.currentTimeMillis() - tthd.getDocUpdatedDate() >= configurablePropertiesHolder
			.getProperty(MIN_TIME_DIFF_FOR_DUAL_WRITE_AUDIT, Long.class);
	}

	public static void main(String[] args) throws ParseException {
		System.out.println(DateTimeUtility.getTimeFromStringFormattedDate("            2024-08-09 00:00:00.000 +0530"));
	}

}
