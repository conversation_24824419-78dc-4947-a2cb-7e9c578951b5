package com.org.panaroma.web.cache;

import com.aerospike.client.Record;
import com.fasterxml.jackson.core.type.TypeReference;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.entity.AutoTaggingDetails;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.web.SearchContext;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface ICacheClient extends Serializable {

	Map<String, String> getLocalisedBatchRecords(List<String> cacheBatchKey);

	Map<String, UserDetails> getUserImageUrlMapFromCache(List<String> key);

	UserDetails getUserImageDetailsFromEntityId(String entityId);

	AppSideCacheData getAppSideCacheData(SearchContext searchContext, CacheInfo cacheInfo);

	Record getRecordFromCache(final String key, final String set, final TypeReference type) throws Exception;

	TransformedTransactionHistoryDetail getTthdFromSet(final String txnId, final String cacheSet);

	void saveAutoTaggingDetails(final String key, final String setName, final Integer expiryTime,
			final AutoTaggingDetails autoTaggingDetails) throws Exception;

}
