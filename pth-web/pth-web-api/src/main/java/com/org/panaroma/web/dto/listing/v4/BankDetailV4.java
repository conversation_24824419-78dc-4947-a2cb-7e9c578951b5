package com.org.panaroma.web.dto.listing.v4;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BankDetailV4 {

	private String logo;

	private String name;

	private String accountNumber;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
