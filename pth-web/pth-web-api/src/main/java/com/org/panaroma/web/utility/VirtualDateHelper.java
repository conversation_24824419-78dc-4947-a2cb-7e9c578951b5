package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.CommonConstants.PAGINATION_TXN_ID;
import static com.org.panaroma.commons.constants.CommonConstants.TRANSACTION_DATE_EPOCH;
import static com.org.panaroma.commons.constants.ConfigPropertiesEnum.LIMIT_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST_FEATURE_ENABLED;
import static com.org.panaroma.commons.constants.ConfigPropertiesEnum.VIRTUAL_FROM_DATE_SPECIFIC_ERROR_CODES_DISABLED;
import static com.org.panaroma.commons.constants.WebConstants.FALSE;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_FROM_DATE_FORMAT;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PAGE_NO;
import static com.org.panaroma.commons.constants.WebConstants.ONE;
import static com.org.panaroma.commons.constants.WebConstants.TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.VIRTUAL_FROM_DATE_USED;
import static com.org.panaroma.commons.constants.WebConstants.NEXT_REQ_HANDLER_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.VIRTUAL_FROM_DATE_VALUE_IN_EPOCH_PARAM_MAP_KEY_WHEN_VFD_IS_USED;
import static com.org.panaroma.commons.utils.DateTimeUtility.listingDateFormat;
import static com.org.panaroma.commons.utils.UtilityExtension.isLimitMonthsToScanInSingleApiRequestFeatureApplicable;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NOT_SEARCHING_BEYOND_VIRTUAL_FROM_DATE;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.RETURNING_AFTER_SCANNING_TILL_VIRTUAL_FROM_DATE;

import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.dto.ListingSubsequentRequestHandlerIdentifierEnum;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.text.ParseException;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class VirtualDateHelper {

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public VirtualDateHelper(final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		VirtualDateHelper.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	/**
	 * Retrieves the virtual "from-date" by subtracting a configurable number of days from
	 * the current date. The number of days to subtract is fetched from the configuration
	 * properties.
	 * @return A String representing the calculated virtual "from-date" after subtracting
	 * the configured number of days from today. Example: If today's date is "2024-03-01"
	 * and the configured virtual days to subtract is "7", this method will return
	 * "2024-02-23".
	 */
	public static String getVirtualFromDate() {
		String daysToSubtract = getVirtualFromDateListingFilterInDays();
		return DateTimeUtility.subtractDaysToListingFormattedDateFromNow(Integer.parseInt(daysToSubtract));
	}

	// Checking if the property is enabled on Bo Panel
	public static Boolean isVirtualFromDateEnabled(final Map<String, String> paramMap) {
		return TRUE.equals(paramMap.get(ConfigPropertiesEnum.VIRTUAL_FROM_DATE_OVER_FROM_DATE_ENABLED.getKey()));
	}

	/**
	 * Retrieves the configuration flag that indicates whether virtual from date feature
	 * is enabled.
	 * @return String representation of the flag - "true" if virtual from date is enabled,
	 * "false" otherwise
	 */
	public static String getVirtualFromDateOverFromDateEnabledFlag() {
		String virtualFromDateFlag = configurablePropertiesHolder
			.getProperty(ConfigPropertiesEnum.VIRTUAL_FROM_DATE_OVER_FROM_DATE_ENABLED, String.class);
		return TRUE.equals(virtualFromDateFlag) ? TRUE : FALSE;
	}

	public static String getVirtualFromDateListingFilterInDays() {
		return configurablePropertiesHolder.getProperty(ConfigPropertiesEnum.VIRTUAL_FROM_DATE_LISTING_FILTER_IN_DAYS,
				String.class);
	}

	/**
	 * Determines if a request should be blocked based on pagination transaction date and
	 * virtual from date criteria. This method validates requests against Virtual From
	 * Date (VFD) rules by: 1. Checking if VFD feature is enabled 2. Validating
	 * transaction date against VFD 3. Checking for specific error identifiers
	 * @param paramMap Map containing request parameters including transaction date and
	 * next request handler identifier
	 */
	public static ResponseDto handleRequestBasedOnPaginationTxnDateAndVfdIdentifier(
			final Map<String, String> paramMap) {
		// Early return if VFD is not enabled
		if (!isVirtualFromDateEnabled(paramMap)) {
			return null;
		}

		// Handle error identifier case
		if (ListingSubsequentRequestHandlerIdentifierEnum.THROW_VIRTUAL_FROM_DATE_RELATED_ERROR_IF_FLAG_IS_TRUE
			.getSubsequentRequestHandlerIdentifierKey()
			.toString()
			.equals(paramMap.get(NEXT_REQ_HANDLER_IDENTIFIER))) {
			return handleRequestOnTheBasisOfSomeCustomLogic(paramMap);
		}

		// To avoid potential NPE when parsing txnDate, explicitly checking for the
		// presence of the key
		// before validation to handle any missing or invalid transaction date.

		Long virtualFromDateEpoch = DateTimeUtility.getEpochMillis(getVirtualFromDate(), LISTING_FROM_DATE_FORMAT);

		if (paramMap.containsKey(TRANSACTION_DATE_EPOCH) && paramMap.containsKey(PAGINATION_TXN_ID)) {
			long transactionDateEpoch = Long.parseLong(paramMap.get(TRANSACTION_DATE_EPOCH));
			if (virtualFromDateEpoch != null && transactionDateEpoch < virtualFromDateEpoch) {
				handleRequestOnTheBasisOfSomeCustomLogic(paramMap);
			}
		}

		if (paramMap.containsKey(TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY)) {
			long toDateForSubsequentRequest = Long
				.parseLong(paramMap.get(TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY));
			if (virtualFromDateEpoch != null && toDateForSubsequentRequest < virtualFromDateEpoch) {
				handleRequestOnTheBasisOfSomeCustomLogic(paramMap);
			}
		}
		return null;
	}

	private static ResponseDto handleRequestOnTheBasisOfSomeCustomLogic(final Map<String, String> paramMap) {
		Boolean isLimitMonthsToScanInSingleApiRequestFeatureEnabled = configurablePropertiesHolder
			.getProperty(LIMIT_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST_FEATURE_ENABLED, Boolean.class);

		if (isLimitMonthsToScanInSingleApiRequestFeatureApplicable(
				isLimitMonthsToScanInSingleApiRequestFeatureEnabled)) {
			throw ExceptionFactory.getException(PANAROMA_SERVICE, RETURNING_AFTER_SCANNING_TILL_VIRTUAL_FROM_DATE);
		}
		else if (Boolean.parseBoolean(configurablePropertiesHolder
			.getProperty(VIRTUAL_FROM_DATE_SPECIFIC_ERROR_CODES_DISABLED, String.class))) {
			return getEmptyTxnResponseForVirtualFromDateHandling(paramMap);
		}
		throw ExceptionFactory.getException(PANAROMA_SERVICE, NOT_SEARCHING_BEYOND_VIRTUAL_FROM_DATE);
	}

	// TODO What if there are no txns visible on screen, then empty txns will create issue
	// because no message too.
	/*
	 * But in case few txns are visible to the user on screen, if we throw error there can
	 * be scenario that came last time also when management was saying that don't throw
	 * error in such cases because user is blocked on the screen
	 *
	 * But I think same issue must not come as we will get this error code added in list
	 * of error codes on FE that Ok CTA must appear in pop up message instead of Retry
	 */
	private static ResponseDto getEmptyTxnResponseForVirtualFromDateHandling(final Map<String, String> paramMap) {
		ResponseDto emptyResponse = ResponseDto.builder()
			.setCurrentPage(paramMap.get("pageNo"))
			.setTxns(Collections.emptyList())
			.build();
		/*
		 * if (PthVersion.V_1_0.isSmallerThanEqual(PthVersionUtility.getPthVersion()) ||
		 * paramMap.getOrDefault(API_VERSION,
		 * ApiVersion.v3.name()).compareToIgnoreCase(ApiVersion.v3.name()) >= 0) {
		 * emptyResponse.setInvalidateVersion(
		 * configurablePropertiesHolder.getProperty(APP_CACHE_INVALIDATE_VERSION,
		 * Integer.class)); }
		 */
		emptyResponse.setPaginationParams(getSamePaginationParamsSentInLastRequest(paramMap));
		return emptyResponse;
	}

	public static PaginationParams getSamePaginationParamsSentInLastRequest(final Map<String, String> paramMap) {

		PaginationParams paginationParams = new PaginationParams();
		paginationParams.setPaginationTxnId(paramMap.get("paginationTxnId"));
		paginationParams.setPaginationStreamSource(paramMap.get("paginationStreamSource"));
		paginationParams.setTransactionDateEpoch(String.valueOf(paramMap.get("transactionDateEpoch")));
		// Settings the appropriate nextRequestHandlerIdentifier for blocking the upcoming
		// listing request.
		paginationParams.setNextRequestHandlerIdentifier(
				ListingSubsequentRequestHandlerIdentifierEnum.THROW_VIRTUAL_FROM_DATE_RELATED_ERROR_IF_FLAG_IS_TRUE
					.getSubsequentRequestHandlerIdentifierKey());
		paginationParams.setPageNo(Integer.parseInt(paramMap.get("pageNo")));
		if (StringUtils.isNotBlank(paramMap.get(TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY))) {
			paginationParams.setToDateForSubsequentRequest(
					Long.valueOf(paramMap.get(TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY)));
		}
		return paginationParams;
	}

	public static RepoResponseSearchApiDto getRepoResponseSearchApiDtoWithEmptyList(
			final Map<String, String> paramMap) {
		return new RepoResponseSearchApiDto(Collections.emptyList(), null,
				getSamePaginationParamsSentInLastRequest(paramMap));
	}

	/**
	 * Determines and returns the applicable from date by comparing the provided from date
	 * with the virtual from date.
	 *
	 * This method performs the following: 1. Retrieves the virtual from date (VFD) which
	 * is calculated based on configured days to subtract from current date 2. Compares
	 * the VFD with the provided fromDateString 3. If VFD is more recent (later) than the
	 * provided fromDateString, use VFD as from date 4. Sets a flag in paramMap to
	 * indicate that VFD was used
	 * @param fromDateString The initial from date string in listing date format
	 * (yyyy-MM-dd)
	 * @param paramMap Map to store request parameters and flags
	 * @return The applicable from date - either the original fromDateString or
	 * virtualFromDate if it's more recent
	 * @throws ParseException if there's an error parsing the date strings
	 *
	 * Example: If fromDateString = "2024-01-01" and virtualFromDate = "2024-02-01", the
	 * method will return "2024-02-01" and set VIRTUAL_FROM_DATE_USED flag to TRUE
	 */
	public static String getApplicableFromDate(final String fromDateString, final Map<String, String> paramMap) {
		String virtualFromDate = null;
		try {
			virtualFromDate = VirtualDateHelper.getVirtualFromDate();
			if (virtualFromDate != null && DateTimeUtility.isFirstDateLatest(virtualFromDate, fromDateString)) {
				// Set flag to indicate Virtual From Date is used
				paramMap.put(VIRTUAL_FROM_DATE_USED, TRUE);
				paramMap.put(VIRTUAL_FROM_DATE_VALUE_IN_EPOCH_PARAM_MAP_KEY_WHEN_VFD_IS_USED,
						String.valueOf(listingDateFormat.parse(virtualFromDate).getTime()));
				return virtualFromDate;
			}
		}
		catch (Exception e) {
			log.error(
					"Some exception occurred while comparing virtual from date & from date. fromDate : {}, virtualFromDate : {}"
							+ " Exception : {}",
					fromDateString, virtualFromDate, CommonsUtility.exceptionFormatter(e));
		}
		return fromDateString;
	}

	/**
	 * Checks if the specified map contains the key that indicates if the virtual from
	 * date is used.
	 * @param paramMap The map to check for the presence of the key. If the map is null,
	 * it returns false.
	 * @return Boolean indicating whether the key (VIRTUAL_FROM_DATE_USED) exists in the
	 * map. Returns false if the map is null or the key is not found.
	 */
	public static Boolean isVirtualFromDateUsed(final Map<String, String> paramMap) {
		if (paramMap == null) {
			return false;
		}
		return TRUE.equalsIgnoreCase(paramMap.get(VIRTUAL_FROM_DATE_USED));
	}

	/**
	 * Updates pagination parameters based on the virtual date flag for the next request.
	 * This method handles the following scenarios: 1. Handling pagination for the special
	 * case where the page number is -1. 2. Handling pagination when they are null for the
	 * last page scenario.
	 * @param response The ResponseDto containing transaction data and pagination details.
	 */
	public static void updatePaginationParamsWhenVirtualDateUsed(final ResponseDto response,
			final Map<String, String> paramMap) {

		PaginationParams paginationParams = response.getPaginationParams();

		// Handle null pagination parameters or the special case where page number is -1
		// (last page).
		if (Objects.isNull(paginationParams) || paginationParams.getPageNo() == -1) {
			handlePaginationForLastPage(paginationParams, response, paramMap);
		}
	}

	/**
	 * Handles pagination when parameters are null or for the special case of the last
	 * page (-1).
	 */
	private static void handlePaginationForLastPage(PaginationParams paginationParams, final ResponseDto response,
			final Map<String, String> paramMap) {
		if (Objects.isNull(paginationParams)) {
			paginationParams = new PaginationParams();
		}

		if (response.getTxns().size() > 0) {
			EsResponseTxn lastTransaction = response.getTxns().get(response.getTxns().size() - 1);

			paginationParams.setPaginationTxnId(lastTransaction.getSourceTxnId());
			paginationParams.setPaginationStreamSource(lastTransaction.getStreamSourceKey());
			paginationParams.setTransactionDateEpoch(String.valueOf(lastTransaction.getTxnDate()));
			// Settings the appropriate nextRequestHandlerIdentifier for blocking the
			// upcoming
			// listing request.
			paginationParams.setNextRequestHandlerIdentifier(
					ListingSubsequentRequestHandlerIdentifierEnum.THROW_VIRTUAL_FROM_DATE_RELATED_ERROR_IF_FLAG_IS_TRUE
						.getSubsequentRequestHandlerIdentifierKey());
			paginationParams.setPageNo(Integer.parseInt(paramMap.getOrDefault(PAGE_NO, ONE)) + 1);
			response.setPaginationParams(paginationParams);
		}
	}

}
