package com.org.panaroma.web;

import static com.org.panaroma.commons.constants.WebConstants.ES_TXN_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.FILTER_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.IS_FILTER_APPLIED;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PASSBOOK_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_TXN_CATEGORY_MAP;
import static com.org.panaroma.commons.constants.WebConstants.SELF_TRANSFER_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.SELF_TRANSFER_KEYWORD;
import static com.org.panaroma.commons.constants.WebConstants.TO_DATE;
import static com.org.panaroma.commons.constants.WebConstants.TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.TXN_TYPE;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.INVALID_PARAM_SEARCH_AFTER;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.MISSING_PARAMETER;
import static com.org.panaroma.web.monitoring.MonitoringConstants.IS_VISIBLE_FALSE_FOR_SEARCH_API;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.TxnCategoryEnum;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.web.dto.PassbookType;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.filter.FiltersFactory;
import com.org.panaroma.web.filter.IFilterType;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.passbookType.IPassbookTypeHandler;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.RoutingUtility;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public abstract class AbstractSearchContext {

	Logger log = LoggerFactory.getLogger(AbstractSearchContext.class);

	@Autowired
	FiltersFactory filtersFactory;

	@Autowired
	List<IPassbookTypeHandler> passbookTypeHandlerList;

	@Autowired
	MetricsAgent metricsAgent;

	public abstract SearchContext setSearchContext(final Map<String, String> paramMap, final Object reqObj);

	public abstract boolean canHandle(String client);

	public void validateSearchContext(final SearchContext searchContext, final Map<String, String> paramMap)
			throws PanaromaException {

		if (searchContext.getPageNo() == 1 && searchContext.getTransactionDateEpoch() != null) {
			log.info("set transactionDateEpoch to null as page number is 1");
			searchContext.setTransactionDateEpoch(null);
		}
		/**
		 * As for dc pageNo will start from > 1 value. but for first hit we wont have
		 * pagination params. So special handling for dcFirst hit.
		 */
		if (searchContext.getPageNo() > 1 && !RoutingUtility.isDcFirstPage(paramMap)
				&& !GenericUtility.isForUpdates(paramMap)
				&& !paramMap.containsKey(TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY)
				&& (searchContext.getTransactionDateEpoch() == null || searchContext.getPaginationStreamSource() == null
						|| searchContext.getPaginationTxnId() == null)) {
			log.error("For page number more that 1, transactionDateEpoch, paginationStreamSource must be present");
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAM_SEARCH_AFTER);
		}

		if (searchContext.getFromDate() > searchContext.getToDate()) {
			log.error("fromDate can't be more than toDate");
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		if (searchContext.getSecondPartyType() != null && searchContext.getEsSecondPartyId() == null) {
			log.error("second party Id is needed");
			throw ExceptionFactory.getException(PANAROMA_SERVICE, MISSING_PARAMETER);
		}

		if (searchContext.getPageSize() < 1) {
			log.error("page size cannot be less than 1");
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		if (searchContext.getStreamSource() == null) {
			log.debug(
					"streamSource  is not provided, hence we need to show only transactions with showInListing flag as true");
			searchContext.setShowInListing("true");
		}

		if (!TRUE.equalsIgnoreCase(searchContext.getIsVisible())) {
			log.error("isVisible flag is false for Search API request. Corresponding paramMap is : {}", paramMap);
			metricsAgent.incrementCount(IS_VISIBLE_FALSE_FOR_SEARCH_API);
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}

		if (!searchContext.isForUpdates()) {
			log.debug("removing fromUpdatedDate value if being passed, since not for updates");
			searchContext.setFromUpdatedDate(null);
		}

		if (searchContext.getUpdatedDateRangeValue() != null) {
			if (StringUtils.isBlank(searchContext.getUpdatedDateRangeValue())) {
				log.error("updatedDateRangeValue is blank");
				throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
			}
			else {
				String[] dates = searchContext.getUpdatedDateRangeValue().split(",");
				if (dates.length == 2) {
					try {
						Long from = Long.parseLong(dates[0]);
						Long to = Long.parseLong(dates[1]);
						if (from > to) {
							log.error("from > to in updatedDateRangeValue");
							throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
						}
					}
					catch (NumberFormatException e) {
						log.error("Non number sent in updatedDateRangeValue : {}",
								searchContext.getUpdatedDateRangeValue());
						throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
					}
				}
				else {
					log.error("updatedDateRangeValue requires date in format from,to. received : {}",
							searchContext.getUpdatedDateRangeValue());
					throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				}
			}

		}
		if (searchContext.isForAppCacheRecon()) {
			if (searchContext.getUpdatedDateRangeValue() == null || searchContext.getTxnCount() == null
					|| searchContext.getPendingTxnCount() == null) {
				log.error("Missing one or more of updatedDateRangeValue or txnCount or pendingTxnCount");
				throw ExceptionFactory.getException(PANAROMA_SERVICE, MISSING_PARAMETER);
			}
		}

		log.debug("Search context validated. searchContext : {}", searchContext);
	}

	public void preProcessParamMap(final Map<String, String> paramMap) {

		// sanitizing paramMap
		paramMap.values().removeIf(Objects::isNull);
		paramMap.values().removeIf(value -> value.equalsIgnoreCase(""));

		// getting keys related to txnCategory and putting in txnType field in paramMap
		// for searching
		if (paramMap.containsKey(TXN_CATEGORY)) {
			paramMap.put(IS_FILTER_APPLIED, "true");
			String txnCategory = paramMap.get(TXN_CATEGORY);
			String[] txnCategoryValues = txnCategory.split(",");
			StringBuilder finalTxnTypeKeys = new StringBuilder();
			Iterator<String> iterator = Arrays.asList(txnCategoryValues).iterator();
			while (iterator.hasNext()) {
				String currentTxnCategory = iterator.next();
				if (TxnCategoryEnum.SELF_TRANSFER.getTxnCategory().equals(currentTxnCategory)) {
					if (txnCategoryValues.length == 1) {
						paramMap.put(SEARCH_SELF_TRANSFER, SELF_TRANSFER_KEYWORD);
					}
					continue;
				}
				TxnCategoryEnum txnCategoryEnum = TxnCategoryEnum.getTxnCategoryEnumByName(currentTxnCategory);
				if (iterator.hasNext() && !Objects.isNull(txnCategoryEnum)) {
					finalTxnTypeKeys.append(TxnCategoryEnum.getKeysByTxnCategory(txnCategoryEnum)).append(",");
				}
				else if (!Objects.isNull(txnCategoryEnum)) {
					finalTxnTypeKeys.append(TxnCategoryEnum.getKeysByTxnCategory(txnCategoryEnum));
				}
				else {
					log.error("Invalid txnCategory provided :{}", currentTxnCategory);
					throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				}
			}

			if (paramMap.containsKey(TXN_TYPE)) {
				// special handling for the case when some key(s) is present in txnType
				// key in map and same is derived from txnCategory
				StringBuilder allKeys = new StringBuilder();
				allKeys.append(paramMap.get(TXN_TYPE)).append(",").append(finalTxnTypeKeys);
				String uniqueKeys = String.join(",",
						Arrays.asList(allKeys.toString().split(",")).stream().distinct().collect(Collectors.toList()));
				paramMap.put(ES_TXN_TYPE, uniqueKeys);
			}
			else if (StringUtils.isNotBlank(finalTxnTypeKeys.toString())) {
				/**
				 * Self transfer filter support For self transfer filter request with some
				 * other txnCategory filter, populate searchTxnCategory in SearchContext
				 * from paramMap to create query with "should" clause otherwise just
				 * populate esTxnType or searchSelfTransfer in SearchContext to create
				 * normal term(s) query.
				 */
				try {
					if (paramMap.get(TXN_CATEGORY).contains(SELF_TRANSFER_CATEGORY) && finalTxnTypeKeys.length() >= 1) {
						Map<String, String> searchTxnCategoryMap = new HashMap<>();
						searchTxnCategoryMap.put(ES_TXN_TYPE, finalTxnTypeKeys.toString());
						searchTxnCategoryMap.put(SEARCH_SELF_TRANSFER, SELF_TRANSFER_KEYWORD);
						paramMap.put(SEARCH_TXN_CATEGORY_MAP,
								(new ObjectMapper()).writeValueAsString(searchTxnCategoryMap));
					}
					else {
						paramMap.put(ES_TXN_TYPE, finalTxnTypeKeys.toString());
					}
				}
				catch (Exception e) {
					throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				}
			}
		}

		/*
		 * toDate param is added in paramMap when toDateForSubsequentRequest is present in
		 * request because that is the toDate of this current request
		 */
		if (paramMap.containsKey(TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY)) {
			paramMap.put(TO_DATE, paramMap.get(TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY));
		}

	}

	public void handleAndValidateFilters(final Map<String, String> paramMap,
			final Map<String, String> accountTypeParamMap) {
		if (paramMap.containsKey(IS_FILTER_APPLIED) && TRUE.equalsIgnoreCase(paramMap.get(IS_FILTER_APPLIED))) {
			if (paramMap.containsKey(FILTER_TYPE) && StringUtils.isNotEmpty(paramMap.get(FILTER_TYPE))) {
				Set<String> filterTypesList = new HashSet<>(0);
				String id = ThreadContext.get("requestId");
				log.debug("Filters are applied for this transaction, requestId: {} filterType: {}", id,
						paramMap.get(FILTER_TYPE));
				filterTypesList.addAll(Arrays.asList(StringUtils.split(paramMap.get(FILTER_TYPE), ',')));
				for (String filterType : filterTypesList) {
					// Ignoring txnCategory filterType from this error since its filter
					// handler not implemented
					if (TXN_CATEGORY.equalsIgnoreCase(filterType)) {
						continue;
					}

					IFilterType iFilterType = filtersFactory.getFilterType(paramMap, filterType, filterTypesList);
					if (iFilterType == null) {
						log.error("invalid filter :{} in filterType param in paramMap", filterType);
						throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
					}
					iFilterType.validateFilter(paramMap);
					iFilterType.setSearchContext(paramMap, accountTypeParamMap, filterTypesList);
				}
			}
			else {
				if (!paramMap.containsKey(TXN_CATEGORY)) {
					log.error("filterType param is not present in paramMap");
					throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
				}
			}
		}
	}

	protected void handleAndValidatePassbookTypes(final Map<String, String> paramMap) {
		if (!paramMap.containsKey(PASSBOOK_TYPE)) {
			return;
		}
		PassbookType passbookType;
		try {
			passbookType = PassbookType.valueOf(paramMap.get(PASSBOOK_TYPE));
		}
		catch (IllegalArgumentException e) {
			log.error("Unsupported passbook type passed. {}", e.getMessage());
			throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_PARAMETER);
		}
		passbookTypeHandlerList.forEach(handler -> {
			if (handler.canHandle(passbookType)) {
				handler.validate(paramMap);
				handler.handle(paramMap);
			}
		});
	}

}