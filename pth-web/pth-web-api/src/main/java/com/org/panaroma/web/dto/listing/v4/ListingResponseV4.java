package com.org.panaroma.web.dto.listing.v4;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.JsonUtils;
import com.org.panaroma.web.dto.listing.TxnResponseV4;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ListingResponseV4 {

	private String entityId;

	private List<TxnResponseV4> txns;

	private PaginationParamV4 paginationParams;

	private EntendedInfoV4 extendedInfo;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
