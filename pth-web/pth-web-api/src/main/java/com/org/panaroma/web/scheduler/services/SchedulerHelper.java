package com.org.panaroma.web.scheduler.services;

import static com.org.panaroma.commons.constants.CommonConstants.RECON_CONFIG_KAFKA_CLIENT_NAME;

import com.org.panaroma.commons.dto.ExecutionTimeRange;
import com.org.panaroma.commons.dto.ReconCronConfig;
import com.org.panaroma.commons.dto.ReconKafkaObject;
import com.org.panaroma.commons.dto.SearchContext;
import com.org.panaroma.commons.entity.ReconStatusData;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.web.recon.IReconTagHandler;
import com.org.panaroma.web.recon.ReconTagHandlerFactory;
import com.org.panaroma.web.repo.ReconStatusDataRepo;
import java.time.LocalTime;
import java.util.Objects;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class SchedulerHelper {

	private IKafkaClient kafkaClient;

	private ReconStatusDataRepo reconStatusDataRepo;

	private ReconTagHandlerFactory reconTagHandlerFactory;

	@Autowired
	public SchedulerHelper(final IKafkaClient kafkaClient, final ReconStatusDataRepo reconStatusDataRepo,
			final ReconTagHandlerFactory reconTagHandlerFactory) {
		this.kafkaClient = kafkaClient;
		this.reconStatusDataRepo = reconStatusDataRepo;
		this.reconTagHandlerFactory = reconTagHandlerFactory;
	}

	@Transactional
	public void saveAndPushIntoKafka(final long currentTime, final ReconCronConfig cronConfig,
			final ReconStatusData reconStatusData) throws Exception {
		this.createDtoAndPushIntoKafka(cronConfig, currentTime, reconStatusData);
		this.updateCurrentStatusInDb(cronConfig, currentTime, reconStatusData);
	}

	private void updateCurrentStatusInDb(final ReconCronConfig cronConfig, final long currentTime,
			final ReconStatusData storedStatusData) {
		long fromDate;
		long toDate;
		if (Objects.isNull(storedStatusData)) {
			fromDate = currentTime - cronConfig.getFromDateDiff();
			toDate = currentTime - cronConfig.getToDateDiff();
		}
		else {
			fromDate = storedStatusData.getToDate();
			toDate = storedStatusData.getToDate() + cronConfig.getFromDateDiff() - cronConfig.getToDateDiff();
		}
		ReconStatusData reconStatusData = ReconStatusData.builder()
			.reconTag(cronConfig.getName())
			.fromDate(fromDate)
			.toDate(toDate)
			.build();
		this.reconStatusDataRepo.save(reconStatusData);
		log.info("Recon status data is saved reconData: {}", reconStatusData);
	}

	private void createDtoAndPushIntoKafka(final ReconCronConfig cronConfig, final long currentTime,
			final ReconStatusData reconStatusData) throws Exception {
		IReconTagHandler reconTagHandler = reconTagHandlerFactory.getReconTagHandler(cronConfig.getTag());
		ReconKafkaObject reconKafkaObject = reconTagHandler.getReconKafkaObject(cronConfig);
		reconKafkaObject.setName(cronConfig.getName());
		reconKafkaObject.setSourceCallRequired(cronConfig.isSourceCallRequired());
		addFromAndToDateToQuery(cronConfig, reconKafkaObject.getSearchContext(), currentTime, reconStatusData);

		kafkaClient.pushIntoKafkaInSync(RECON_CONFIG_KAFKA_CLIENT_NAME, reconKafkaObject.getName(), reconKafkaObject);
	}

	public static void addFromAndToDateToQuery(final ReconCronConfig cronConfig, final SearchContext searchContext,
			final long currentTime, final ReconStatusData reconStatusData) {
		if (Objects.isNull(reconStatusData)) {
			searchContext.setFromDate(currentTime - cronConfig.getFromDateDiff());
			searchContext.setToDate(currentTime - cronConfig.getToDateDiff());
		}
		else {
			searchContext.setFromDate(reconStatusData.getToDate());
			searchContext
				.setToDate(reconStatusData.getToDate() + cronConfig.getFromDateDiff() - cronConfig.getToDateDiff());
		}
	}

	public ReconStatusData getLatestReconStatusData(final ReconCronConfig cronConfig) {
		ReconStatusData reconStatusData = this.reconStatusDataRepo.findByreconTag(cronConfig.getName());
		log.info("Recon status Data : {}", reconStatusData);
		return reconStatusData;
	}

	public boolean eligibleForNewReconTask(final ReconStatusData reconStatusData, final ReconCronConfig cronConfig,
			final long currentTime) {

		IReconTagHandler reconTagHandler = reconTagHandlerFactory.getReconTagHandler(cronConfig.getTag());
		if (!reconTagHandler.isPreValidated(cronConfig)) {
			return false;
		}
		if (!isBetweenExecutionTimeRange(cronConfig.getExecutionTimeRange())) {
			return false;
		}
		if (Objects.isNull(reconStatusData)) {
			return true;
		}
		long fromDate = currentTime - cronConfig.getFromDateDiff();
		if (fromDate >= reconStatusData.getToDate()) {
			return true;
		}
		return false;
	}

	private Boolean isBetweenExecutionTimeRange(final ExecutionTimeRange range) {
		try {
			if (Objects.isNull(range)) {
				return Boolean.TRUE;
			}
			LocalTime currentTime = LocalTime.now();
			LocalTime from = LocalTime.parse(range.getFromTime());
			LocalTime to = LocalTime.parse(range.getToTime());

			if (currentTime.isAfter(from) && currentTime.isBefore(to)) {
				return Boolean.TRUE;
			}
			else {
				return Boolean.FALSE;
			}
		}
		catch (Exception e) {
			log.error("Exception while parsing execution time for recon scheduler : {}",
					CommonsUtility.exceptionFormatter((Exception) e));
			return Boolean.TRUE;
		}
	}

}
