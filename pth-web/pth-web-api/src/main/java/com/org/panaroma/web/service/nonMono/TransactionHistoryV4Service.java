package com.org.panaroma.web.service.nonMono;

import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.dto.UpdateResponseDto;
import com.org.panaroma.web.dto.listing.v4.ListingResponseV4;
import com.org.panaroma.web.dto.updates.v2.UpdatesResponseV4;
import com.org.panaroma.web.utility.listing.ListingResponseMapperUtility;
import com.org.panaroma.web.utility.ListingSubsequentRequestHandlerUtility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class TransactionHistoryV4Service {

	private final RouteChangerServiceNonMono transactionHistoryServiceNonMono;

	private final ListingSubsequentRequestHandlerUtility listingSubsequentRequestHandlerUtility;

	private final RequestResponseModifierServiceV4 requestResponseModifierServiceV4;

	@Autowired
	public TransactionHistoryV4Service(final RouteChangerServiceNonMono transactionHistoryServiceNonMono,
			final ListingSubsequentRequestHandlerUtility listingSubsequentRequestHandlerUtility,
			final RequestResponseModifierServiceV4 requestResponseModifierServiceV4) {
		this.transactionHistoryServiceNonMono = transactionHistoryServiceNonMono;
		this.listingSubsequentRequestHandlerUtility = listingSubsequentRequestHandlerUtility;
		this.requestResponseModifierServiceV4 = requestResponseModifierServiceV4;
	}

	public ListingResponseV4 preProcessRequestWithVirtualFromDateIdentifier(final Map<String, String> paramMap) {
		return ListingResponseMapperUtility.mapToNewResponse(
				listingSubsequentRequestHandlerUtility.preProcessRequestWithVirtualFromDateIdentifier(paramMap));
	}

	public UpdatesResponseV4 getUpdates(final Map<String, String> paramMap, final Map<String, String> tokens)
			throws Exception {
		UpdateResponseDto updateResponseDto = transactionHistoryServiceNonMono.getUpdates(paramMap, tokens);
		UpdatesResponseV4 updatesResponseV4 = ListingResponseMapperUtility.mapToNewResponse(updateResponseDto);
		requestResponseModifierServiceV4.modifyUpdatesResponse(updatesResponseV4);
		return updatesResponseV4;
	}

	public ListingResponseV4 search(final Map<String, String> paramMap, final Map<String, String> tokens)
			throws Throwable {

		requestResponseModifierServiceV4.modifySearchRequest(paramMap);
		listingSubsequentRequestHandlerUtility.preProcessForSubsequentRequestsHandling(paramMap);
		ResponseDto listingV3Response = transactionHistoryServiceNonMono.search(paramMap, tokens);
		listingSubsequentRequestHandlerUtility.postProcessForSubsequentRequestsHandling(listingV3Response, paramMap);
		ListingResponseV4 listingResponseV4 = ListingResponseMapperUtility.mapToNewResponse(listingV3Response);
		requestResponseModifierServiceV4.modifySearchResponse(listingResponseV4);
		return listingResponseV4;
	}

}
