package com.org.panaroma.web.dto.listing;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.JsonUtils;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.CtaNode;
import com.org.panaroma.web.dto.listing.v4.InstrumentInfoV4;
import com.org.panaroma.web.dto.listing.v4.SecondPartyInfoV4;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TxnResponseV4 {

	private Double amount;

	private Integer txnIndicator;

	private SecondPartyInfoV4 secondPartyInfo;

	private Integer statusKey;

	private String narration;

	private String txnTag;

	private String txnId;

	private String sourceTxnId;

	private Long docUpdatedDate;

	private String streamSource;

	private Boolean maskAmount;

	private Map<String, CtaNode> ctasMap;

	private Long txnDate;

	private String txnActionLabel;

	private List<InstrumentInfoV4> userInstrumentInfo;

	private Integer txnCategory;

	private String errorCode;

	private String remarks;

	private Boolean isSelfTransfer;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private List<String> searchAbleStrings;

	private Boolean isHiddenTxn;

	private String repeatPaymentUrl;

	private Integer txnType;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
