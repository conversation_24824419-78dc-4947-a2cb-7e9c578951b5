package com.org.panaroma.web.controller.nonMonoController;

import static com.org.panaroma.commons.constants.Constants.V2;
import static com.org.panaroma.commons.constants.WebConstants.APP_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.AUTHORIZATION;
import static com.org.panaroma.commons.constants.WebConstants.CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.DETAIL_V2;
import static com.org.panaroma.commons.constants.WebConstants.ENTITY_ID;
import static com.org.panaroma.commons.constants.WebConstants.FILTER_FOR_ALL_UPI_ACCOUNTS;
import static com.org.panaroma.commons.constants.WebConstants.OPEN_SOURCE;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_API_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.UPI;
import static com.org.panaroma.commons.constants.WebConstants.USER_TOKEN;
import static com.org.panaroma.web.constants.swaggerconstants.DescriptionConstants.AUTHORIZATION_DESCRIPTION;
import static com.org.panaroma.web.constants.swaggerconstants.DescriptionConstants.CLIENT_DESCRIPTION;
import static com.org.panaroma.web.constants.swaggerconstants.DescriptionConstants.LISTING_V2_API_DESCRIPTION;
import static com.org.panaroma.web.constants.swaggerconstants.DescriptionConstants.PARAMMAP_DESCRIPTION;
import static com.org.panaroma.web.constants.swaggerconstants.SwaggerResponseConstants.ERROR_RESPONSE_200_4002;
import static com.org.panaroma.web.constants.swaggerconstants.SwaggerResponseConstants.ERROR_RESPONSE_200_4003;
import static com.org.panaroma.web.constants.swaggerconstants.SwaggerResponseConstants.ERROR_RESPONSE_400;
import static com.org.panaroma.web.constants.swaggerconstants.SwaggerResponseConstants.ERROR_RESPONSE_401;
import static com.org.panaroma.web.constants.swaggerconstants.SwaggerResponseConstants.LISTING_ERROR_RESPONSE_200_4006;
import static com.org.panaroma.web.constants.swaggerconstants.SwaggerResponseConstants.LISTING_RESPONSE_4011;
import static com.org.panaroma.web.constants.swaggerconstants.SwaggerResponseConstants.LISTING_V2_RESPONSE_200;
import static com.org.panaroma.web.constants.swaggerconstants.SwaggerResponseConstants.SEARCH_API_REQUEST_BODY;
import static com.org.panaroma.web.monitoring.MonitoringConstants.FAILURE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.LOCALE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SEARCH;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SUCCESS;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.enums.ApiVersion;
import com.org.panaroma.web.analytics.dto.RequestInfo;
import com.org.panaroma.web.analytics.service.UthAnalyticsService;
import com.org.panaroma.web.dto.BaseResponse;
import com.org.panaroma.web.dto.Client;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.dto.SearchAppRequest;
import com.org.panaroma.web.dto.detailAPI.ExtraDetailRequestFields;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import com.org.panaroma.web.service.nonMono.RouteChangerServiceNonMono;
import com.org.panaroma.web.service.nonMono.TransactionHistoryV4Service;
import com.org.panaroma.web.utility.ErrorResponseUtility;
import com.org.panaroma.web.utility.HttpRequestMonitoringUtility;
import com.org.panaroma.web.utility.TokenValidatorUtility;
import com.org.panaroma.web.utility.WebControllerUtility;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Log4j2
@RequestMapping({ "/pth/ext/v2" })
public class PanaromaWebControllerV2NonMono {

	RouteChangerServiceNonMono transactionHistoryServiceNonMono;

	ErrorResponseUtility errorResponseUtility;

	ObjectMapper objectMapper;

	HttpRequestMonitoringUtility httpRequestMonitoringUtility;

	UthAnalyticsService uthAnalyticsService;

	TransactionHistoryV4Service transactionHistoryV4Service;

	@Autowired
	public PanaromaWebControllerV2NonMono(final RouteChangerServiceNonMono transactionHistoryServiceNonMono,
			final ErrorResponseUtility errorResponseUtility, final ObjectMapper objectMapper,
			final HttpRequestMonitoringUtility httpRequestMonitoringUtility,
			final UthAnalyticsService uthAnalyticsService,
			final TransactionHistoryV4Service transactionHistoryV4Service) {
		this.transactionHistoryServiceNonMono = transactionHistoryServiceNonMono;
		this.errorResponseUtility = errorResponseUtility;
		this.objectMapper = objectMapper;
		this.httpRequestMonitoringUtility = httpRequestMonitoringUtility;
		this.uthAnalyticsService = uthAnalyticsService;
		this.transactionHistoryV4Service = transactionHistoryV4Service;
	}

	@GetMapping(value = { "/{sourceContext}/detail", "/detail" }, produces = "application/json; charset=UTF-8")
	public Object getDetails(
			@PathVariable(name = "sourceContext", required = false) final TransactionSource passsedTransactionSource,
			@RequestParam("txnId") final String txnId,
			@RequestParam(value = LOCALE, defaultValue = "en-IN") final String locale,
			@RequestParam(value = "showBankData", defaultValue = "false") final boolean showBankData,
			@RequestParam(value = "showOnlyTimeline", defaultValue = "false") final boolean showOnlyTimeline,
			@RequestParam(value = "status", required = false) final ClientStatusEnum status,
			@RequestParam(value = "client", required = false) final Client client,
			@RequestParam(value = "version", required = false) final String appVersion,
			@RequestParam(value = "openSource", required = false) final String openSource,
			@RequestHeader(AUTHORIZATION) final Map<String, String> tokenMap) throws Exception {

		// As response logs are getting printed using Logbook. search "Logbook" &&
		// "request" to check these logs.
		/*
		 * log.
		 * info("detailV2 Request received to fetch details. txnId/GrpId: {}, Transaction Source {},locale {}, DetailV2 Source {} & token {}"
		 * , txnId, passsedTransactionSource, locale, openSource,
		 * LoggerUtility.createMaskedMap(tokenMap));
		 */
		// Adding below support for ios App bug.
		/*
		 * TransactionSource transactionSource = passsedTransactionSource; if
		 * (transactionSource == null) { log.
		 * info("detailV2 Request received with transactionSource as null. So setting it to TS."
		 * ); transactionSource = TransactionSource.TS; }
		 */
		String userToken = TokenValidatorUtility.getTokenFromMap(
				tokenMap.getOrDefault(AUTHORIZATION, tokenMap.get(AUTHORIZATION.toLowerCase())), USER_TOKEN);
		TransactionSource transactionSource = WebControllerUtility.getSourceForDetailRequest(passsedTransactionSource,
				txnId);
		String clientId = client != null ? client.name() : null;
		RequestInfo requestInfo = RequestInfo.builder()
			.appVersion(appVersion)
			.appClient(clientId)
			.locale(locale)
			.openSource(openSource)
			.txnSource(transactionSource != null ? transactionSource.name() : null)
			.txnId(txnId)
			.showBankData(showBankData)
			.status(status != null ? status.getStatusValue() : null)
			.url(MonitoringConstants.DETAIL_V2_WEBV2_URL.replaceAll("<sourceContext>",
					transactionSource != null ? transactionSource.name() : ""))
			.build();
		try {
			ExtraDetailRequestFields extraDetailRequestFields = new ExtraDetailRequestFields(appVersion, openSource,
					null);
			DetailApiResponseV2 response = transactionHistoryServiceNonMono.getDetailsV2(transactionSource, txnId,
					tokenMap, showBankData, status, clientId, locale, null, showOnlyTimeline, extraDetailRequestFields);
			uthAnalyticsService.dumpResponseForUthAnalytics(response, requestInfo, userToken);
			return response;
		}
		catch (Throwable ex) {
			Map<String, String> paramMap = new HashMap<>();
			paramMap.put(OPEN_SOURCE, openSource);
			paramMap.put(APP_VERSION, appVersion);
			BaseResponse baseResponse = this.errorResponseUtility.handleError((Throwable) ex, DETAIL_V2, clientId,
					paramMap);
			uthAnalyticsService.dumpResponseForUthAnalytics(baseResponse, requestInfo, userToken);
			return baseResponse;
		}
	}

	@Operation(summary = "TOMCAT LISTING V2 API", description = LISTING_V2_API_DESCRIPTION,
			parameters = {
					@Parameter(name = AUTHORIZATION, required = true, description = AUTHORIZATION_DESCRIPTION,
							in = ParameterIn.HEADER, schema = @Schema(implementation = Map.class)),
					@Parameter(name = "paramMap", required = true, description = PARAMMAP_DESCRIPTION,
							in = ParameterIn.QUERY, schema = @Schema(implementation = Map.class)),
					@Parameter(name = CLIENT, required = true, description = CLIENT_DESCRIPTION,
							in = ParameterIn.HEADER, schema = @Schema(implementation = Client.class)) },
			requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(required = true,
					content = { @Content(mediaType = "application/json",
							schema = @Schema(implementation = SearchAppRequest.class),
							examples = { @ExampleObject(name = "Request body", value = SEARCH_API_REQUEST_BODY) }) }))
	@ApiResponses({
			@ApiResponse(responseCode = "200", description = "OK", content = @Content(mediaType = "application/json",
					schema = @Schema(implementation = ResponseDto.class),
					examples = { @ExampleObject(name = "Success response", value = LISTING_V2_RESPONSE_200),
							@ExampleObject(name = "Errot response 1", value = ERROR_RESPONSE_200_4003),
							@ExampleObject(name = "Errot response 2", value = ERROR_RESPONSE_200_4002),
							@ExampleObject(name = "Errot response 3 ", value = LISTING_RESPONSE_4011),
							@ExampleObject(name = "Errot response 4", value = LISTING_ERROR_RESPONSE_200_4006) })),
			@ApiResponse(responseCode = "400", description = "Bad Request",
					content = @Content(mediaType = "application/json",
							schema = @Schema(implementation = BaseResponse.class),
							examples = @ExampleObject(name = "Bad Request response", value = ERROR_RESPONSE_400))),
			@ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content(
					mediaType = "application/json", schema = @Schema(implementation = BaseResponse.class),
					examples = @ExampleObject(name = "Authorization error response", value = ERROR_RESPONSE_401))) })

	@PostMapping(value = { "/search" }, produces = "application/json; charset=UTF-8")
	public Object search(@RequestHeader final Map<String, String> tokenMap,
			@RequestHeader(value = CLIENT) final Client client, @RequestParam final Map<String, String> paramMap,
			@RequestBody final SearchAppRequest searchAppRequest) throws Throwable {

		long apiStartTime = Instant.now().toEpochMilli();

		try {
			paramMap.put(SEARCH_API_VERSION, ApiVersion.v2.name());
			httpRequestMonitoringUtility.preprocessRequestAndPushMetrics(paramMap, searchAppRequest, tokenMap, client,
					"search", V2);

			// As response logs are getting printed using Logbook. search "Logbook" &&
			// "request" to check these logs.
			/*
			 * log.info("request received for searchV2 with parameters {} & token : {}",
			 * paramMap, LoggerUtility.createMaskedMap(tokenMap));
			 */
			ResponseDto response;
			try {
				response = transactionHistoryServiceNonMono.search(paramMap, tokenMap);

				// push execution time metrics for success response
				httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, SEARCH,
						V2, SUCCESS, apiStartTime);
				return response;
			}
			catch (Exception ex) {
				BaseResponse baseResp = this.errorResponseUtility.handleError((Throwable) ex, SEARCH,
						paramMap.get(CLIENT), paramMap);
				// push execution time metrics for failure response
				httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, SEARCH,
						V2, FAILURE, apiStartTime);
				return baseResp;
			}
		}
		catch (Exception e) {
			httpRequestMonitoringUtility.publishPostRequestRequestExecutionLevelMetrics(paramMap, tokenMap, SEARCH, V2,
					FAILURE, apiStartTime);
			throw e;
		}
	}

	@PostMapping(value = { "/cstSearch" }, produces = "application/json; charset=UTF-8")
	public Object cstSearch(@RequestHeader(AUTHORIZATION) final Map<String, String> tokenMap,
			@RequestHeader(value = ENTITY_ID) final String entityId, @RequestParam final Map<String, String> paramMap,
			@RequestBody final SearchAppRequest searchAppRequest) throws Throwable {

		// As response logs are getting printed using Logbook. search "Logbook" &&
		// "request" to check these logs.
		/*
		 * log.
		 * info("request received for cst listing with parameters {}, paramMap : {} and tokenMap : {}"
		 * , searchAppRequest, paramMap, LoggerUtility.createMaskedMap(tokenMap));
		 */
		// handle all PaymentSystem/Vertical Related special Hanlding for CST here for
		// filtering
		paramMap.put(OPEN_SOURCE, searchAppRequest.getPassbookType());
		paramMap.put(ENTITY_ID, entityId);
		if (searchAppRequest != null) {
			switch (searchAppRequest.getPaymentSystem()) {
				case UPI:
					// below handling done to fetch all upi txns of all upi accounts
					searchAppRequest.setUpiIdentifier(FILTER_FOR_ALL_UPI_ACCOUNTS);
					break;
				default:
					throw new Exception("PaymentSystem : " + searchAppRequest.getPaymentSystem() + " not allowed");
			}
		}
		// rest handling is same as normal listing/search
		return this.search(tokenMap, null, paramMap, searchAppRequest);
	}

}