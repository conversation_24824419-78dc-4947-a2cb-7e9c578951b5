package com.org.panaroma.web.utility.listing;

import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnFlowTypeEnum;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.enums.LogoOrderTypeEnum;
import com.org.panaroma.commons.utils.BankUtility;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.InstrumentInfo;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.dto.SecondPartyInfo;
import com.org.panaroma.web.dto.UpdateResponseDto;
import com.org.panaroma.web.dto.UserInstrumentLogos;
import com.org.panaroma.web.dto.listing.v4.BankDetailV4;
import com.org.panaroma.web.dto.listing.v4.EntendedInfoV4;
import com.org.panaroma.web.dto.listing.v4.InstrumentInfoV4;
import com.org.panaroma.web.dto.listing.v4.ListingResponseV4;
import com.org.panaroma.web.dto.listing.v4.PaginationParamV4;
import com.org.panaroma.web.dto.listing.v4.SecondPartyInfoV4;
import com.org.panaroma.web.dto.listing.TxnResponseV4;
import com.org.panaroma.web.dto.updates.v2.UpdatesResponseV4;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.org.panaroma.commons.constants.WebConstants.PLACEHOLDER_STRING_USED_AT_FE;

public class ListingResponseMapperUtility {

	public static ListingResponseV4 mapToNewResponse(final ResponseDto responseDto) {

		if (responseDto == null) {
			return null;
		}
		return ListingResponseV4.builder()
			.entityId(responseDto.getEntityId())
			.txns(mapTransactions(responseDto.getTxns(), responseDto.getEntityId()))
			.paginationParams(mapPaginationParams(responseDto.getPaginationParams()))
			.extendedInfo(mapExtendedInfo(responseDto))
			.build();
	}

	public static UpdatesResponseV4 mapToNewResponse(final UpdateResponseDto updateResponseDto) {
		if (updateResponseDto == null) {
			return null;
		}
		UpdatesResponseV4 updatesResponseV4 = new UpdatesResponseV4();
		updatesResponseV4.setEntityId(updateResponseDto.getEntityId());
		updatesResponseV4.setTxns(mapTransactions(updateResponseDto.getTxns(), updateResponseDto.getEntityId()));
		updatesResponseV4.setPaginationParams(mapPaginationParams(updateResponseDto.getPaginationParams()));
		updatesResponseV4.setExtendedInfo(mapExtendedInfo(updateResponseDto));
		updatesResponseV4.setRemovedIds(updateResponseDto.getRemovedIds());
		updatesResponseV4.setInvalidateStoredData(updateResponseDto.isInvalidateStoredData());

		return updatesResponseV4;
	}

	private static List<TxnResponseV4> mapTransactions(final List<EsResponseTxn> txnsV3, final String selfEntityId) {
		return txnsV3.stream().map(txnV3 -> {
			TxnResponseV4 responseV4 = new TxnResponseV4();

			// Map fields with transformations
			responseV4
				.setAmount((StringUtils.isNotBlank(txnV3.getAmount()) ? Double.parseDouble(txnV3.getAmount()) : null));
			responseV4.setTxnIndicator((StringUtils.isNotBlank(txnV3.getTxnIndicator()))
					? Integer.parseInt(txnV3.getTxnIndicator()) : null);
			if (!isNarrationNeedsToBeRemoved(txnV3.getNarration(), txnV3.getSecondPartyInfo().getName())) {
				responseV4.setNarration(txnV3.getNarration());
			}
			responseV4.setTxnId(txnV3.getTxnId());
			responseV4.setStatusKey(
					(StringUtils.isNotBlank(txnV3.getStatusKey())) ? Integer.parseInt(txnV3.getStatusKey()) : null);
			responseV4.setSourceTxnId(txnV3.getSourceTxnId());
			responseV4.setTxnTag((Objects.nonNull(txnV3.getTxnTags()) && txnV3.getTxnTags().size() > 0)
					? txnV3.getTxnTags().get(0) : null);
			responseV4.setTxnType(txnV3.getTxnType());
			responseV4.setSearchAbleStrings(txnV3.getSearchAbleStrings());
			responseV4.setTxnDate(txnV3.getTxnDate());
			responseV4.setCtasMap(txnV3.getCtasMap());
			responseV4.setDocUpdatedDate(txnV3.getDocUpdatedDate());
			responseV4.setStreamSource(txnV3.getStreamSource());
			responseV4.setTxnActionLabel(txnV3.getDateTimeLabel());
			responseV4.setIsHiddenTxn((txnV3.getIsHiddenTxn()) ? true : null);
			responseV4.setMaskAmount((txnV3.isMaskAmount()) ? true : null);
			responseV4.setTxnCategory(
					(Objects.nonNull(txnV3.getSearchFilters())) ? txnV3.getSearchFilters().getTxnCategory() : null);

			// Map secondPartyInfo
			responseV4.setSecondPartyInfo(mapSecondPartyInfo(txnV3, selfEntityId));

			// Map userInstrumentInfo
			responseV4.setUserInstrumentInfo(
					mapUserInstrumentInfo(txnV3.getUserInstrumentInfo(), txnV3.getUserInstrumentLogosV2()));

			if (Objects.nonNull(txnV3.getUserInstrumentInfo()) && txnV3.getUserInstrumentInfo().size() > 0
					&& Objects.nonNull(txnV3.getUserInstrumentInfo().get(0).getParticipant())) {
				responseV4.setRemarks(txnV3.getUserInstrumentInfo().get(0).getParticipant().getRemarks());
			}
			responseV4.setErrorCode(txnV3.getUserInstrumentInfo().get(0).getErrorCode());
			if (Objects.nonNull(txnV3.getContextualInfo())
					&& TxnFlowTypeEnum.SELF_TRANSFER_TXN.equals(txnV3.getContextualInfo().getFlowType())) {
				responseV4.setIsSelfTransfer(true);
			}

			return responseV4;
		}).collect(Collectors.toList());
	}

	private static SecondPartyInfoV4 mapSecondPartyInfo(final EsResponseTxn txnV3, final String selfEntityId) {

		if (Objects.isNull(txnV3) || Objects.isNull(txnV3.getSecondPartyInfo())) {
			return null;
		}

		SecondPartyInfo infoV3 = txnV3.getSecondPartyInfo();

		SecondPartyInfoV4 infoV4 = new SecondPartyInfoV4();

		List<Logo> urlLogoList = Collections.emptyList();
		if (infoV3.getLogoOrder() != null && infoV3.getLogoOrder().size() > 0) {
			urlLogoList = infoV3.getLogoOrder()
				.stream()
				.filter(Objects::nonNull)
				.filter(logo -> LogoOrderTypeEnum.URL.equals(logo.getType()))
				.collect(Collectors.toList());
		}

		// Simplified checks since urlLogoList is never null
		if (urlLogoList.size() > 0) {

			// As per app logic, we need not to send default logo urls in logoUrl fields
			// instead need to set it to fallbackLogoUrl only.
			if (Objects.nonNull(LogoUtility.getBankCategoryLogo(txnV3.getTxnType()))
					&& LogoUtility.getBankCategoryLogo(txnV3.getTxnType()).equals(urlLogoList.get(0).getValue())) {
				infoV4.setFallbackLogoUrl(urlLogoList.get(0).getValue());
			}
			else {
				infoV4.setLogoUrl(urlLogoList.get(0).getValue());
			}
		}

		if (urlLogoList.size() > 1) {
			infoV4.setFallbackLogoUrl(urlLogoList.get(1).getValue());
		}

		if (infoV4.getLogoUrl() == null && (Objects.isNull(LogoUtility.getBankCategoryLogo(txnV3.getTxnType()))
				|| !LogoUtility.getBankCategoryLogo(txnV3.getTxnType()).equals(infoV3.getLogoUrl()))) {
			infoV4.setLogoUrl(infoV3.getLogoUrl()); // Fallback to the original
			// logo URL if no logo order
			// is present
		}

		if (infoV4.getFallbackLogoUrl() == null) {
			infoV4.setFallbackLogoUrl(infoV3.getLogoUrl());
		}

		// If fallbackLogoUrl contains default bank logo and txnType is P2P then remove
		// fallbackLogoUrl.
		// But if logoOrder is null(cashback or upiRemittance case) then don't remove
		// fallbackUrl. else FE will show nameInitials.
		if (Objects.nonNull(LogoUtility.getBankCategoryLogo(txnV3.getTxnType()))
				&& LogoUtility.getBankCategoryLogo(txnV3.getTxnType()).equals(infoV4.getFallbackLogoUrl())
				&& (TransactionTypeEnum
					.isP2PTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(txnV3.getTxnType())))
				&& Objects.nonNull(infoV3.getLogoOrder())) {
			infoV4.setFallbackLogoUrl(null);
		}

		// In case first priority is to NameInitials(that is STRING type in logoOrder, as
		// String is only being used for name initials),
		// We will not send any url, as default fallback is to name initials only at app
		// end.
		if (infoV3.getLogoOrder() != null && infoV3.getLogoOrder().size() > 0
				&& LogoOrderTypeEnum.STRING.equals(infoV3.getLogoOrder().get(0).getType())) {
			infoV4.setLogoUrl(null);
			infoV4.setFallbackLogoUrl(null);
		}

		infoV4.setName(infoV3.getName());
		infoV4.setIdentifier(getIdentifier(txnV3, selfEntityId));
		if (Objects.nonNull(txnV3.getContextualInfo())
				&& StringUtils.isNotBlank(txnV3.getContextualInfo().getFeReportedReceiverCustId())
				&& (TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey().equals(txnV3.getTxnType()))) {
			infoV4.setReportedCustId(txnV3.getContextualInfo().getFeReportedReceiverCustId());
		}
		if (Objects.nonNull(infoV3.getParticipant()) && Objects.nonNull(infoV3.getParticipant().getMobileData())
				&& !isSelfTransfer(txnV3)) {
			infoV4.setMobileNumber(Utility.getValidPhoneNumber(infoV3.getParticipant(),
					TransactionTypeEnum.getTransactionTypeEnumByKey(txnV3.getTxnType()), false));
		}

		BankDetailV4 bankData = null;
		if (Objects.nonNull(infoV3.getParticipant()) && isBankDataNeedsToBeAdded(txnV3)
				&& Objects.nonNull(infoV3.getParticipant().getBankData())) {
			bankData = new BankDetailV4();
			bankData.setName(BankUtility.getCurrentlyUsedBankName(infoV3.getParticipant().getBankData().getIfsc(),
					infoV3.getParticipant().getBankData().getBankName()));
			bankData.setLogo(LogoUtility.getBankLogo(infoV3.getParticipant().getBankData().getIfsc(),
					infoV3.getParticipant().getBankData().getBankName()));
			bankData.setAccountNumber(infoV3.getParticipant().getBankData().getAccNumber());
		}
		infoV4.setBankDetail(bankData);

		return infoV4;
	}

	private static String getIdentifier(final EsResponseTxn txnV3, final String selfEntityId) {

		if (Objects.isNull(txnV3) || Objects.isNull(txnV3.getSecondPartyInfo())) {
			return null;
		}

		SecondPartyInfo infoV3 = txnV3.getSecondPartyInfo();

		if (StringUtils.isNotBlank(infoV3.getEntityId())) {
			return infoV3.getEntityId();
		}

		// In case of self transfer we need to send selfEntityId as identifier.
		if (Objects.nonNull(txnV3.getContextualInfo())
				&& TxnFlowTypeEnum.SELF_TRANSFER_TXN.equals(txnV3.getContextualInfo().getFlowType())) {
			return selfEntityId;
		}

		if (Objects.nonNull(infoV3.getParticipant())) {

			if (Objects.nonNull(infoV3.getParticipant().getBankData())
					&& StringUtils.isNotBlank(infoV3.getParticipant().getBankData().getAccRefNum())) {
				return infoV3.getParticipant().getBankData().getAccRefNum();
			}

			if (Objects.nonNull(infoV3.getParticipant().getMerchantData())
					&& StringUtils.isNotBlank(infoV3.getParticipant().getMerchantData().getMerchantId())) {
				return infoV3.getParticipant().getMerchantData().getMerchantId();
			}
			if (Objects.nonNull(infoV3.getParticipant().getUpiData())
					&& StringUtils.isNotBlank(infoV3.getParticipant().getUpiData().getVpa())) {
				return infoV3.getParticipant().getUpiData().getVpa();
			}
		}
		return null;
	}

	private static List<InstrumentInfoV4> mapUserInstrumentInfo(final List<InstrumentInfo> instrumentInfosV3,
			final List<UserInstrumentLogos> userInstrumentLogosV3) {
		return Optional.ofNullable(instrumentInfosV3).orElse(Collections.emptyList()).stream().map(instrumentInfo -> {

			InstrumentInfoV4 infoV4 = new InstrumentInfoV4();

			// First check with subInstrumentType, if not available then check with
			// instrumentType
			if (Objects.nonNull(instrumentInfo.getSubInstrumentType())) {
				infoV4.setLogoUrl(Optional.ofNullable(userInstrumentLogosV3)
					.orElse(Collections.emptyList())
					.stream()
					.filter(logo -> logo.getPaymentSystem().equals(instrumentInfo.getSubInstrumentType()))
					.map(UserInstrumentLogos::getLogoUrl)
					.findFirst()
					.orElse(null));

				infoV4.setInstrumentName(Optional.ofNullable(userInstrumentLogosV3)
					.orElse(Collections.emptyList())
					.stream()
					.filter(logo -> logo.getPaymentSystem().equals(instrumentInfo.getSubInstrumentType()))
					.map(UserInstrumentLogos::getInstrumentName)
					.findFirst()
					.orElse(null));
			}

			// If not found on subInstrument, check on instrument.
			if (Objects.isNull(infoV4.getLogoUrl())) {
				infoV4.setLogoUrl(Optional.ofNullable(userInstrumentLogosV3)
					.orElse(Collections.emptyList())
					.stream()
					.filter(logo -> logo.getPaymentSystem().equals(instrumentInfo.getInstrumentType()))
					.map(UserInstrumentLogos::getLogoUrl)
					.findFirst()
					.orElse(null));
			}

			if (Objects.isNull(infoV4.getInstrumentName())) {
				infoV4.setInstrumentName(Optional.ofNullable(userInstrumentLogosV3)
					.orElse(Collections.emptyList())
					.stream()
					.filter(logo -> logo.getPaymentSystem().equals(instrumentInfo.getInstrumentType()))
					.map(UserInstrumentLogos::getInstrumentName)
					.findFirst()
					.orElse(null));
			}

			infoV4.setInstrumentType(instrumentInfo.getInstrumentType());
			infoV4.setSubInstrumentType(instrumentInfo.getSubInstrumentType());
			infoV4.setIdentifier(instrumentInfo.getIdentifier());
			infoV4.setAccountType(instrumentInfo.getAccountType());
			return infoV4;
		}).collect(Collectors.toList());
	}

	private static PaginationParamV4 mapPaginationParams(final PaginationParams paramsV3) {
		if (paramsV3 == null) {
			return null;
		}
		PaginationParamV4 paramV4 = new PaginationParamV4();
		paramV4.setTransactionDateEpoch(paramsV3.getTransactionDateEpoch());
		paramV4.setStreamSource(paramsV3.getPaginationStreamSource());
		paramV4.setTxnId(paramsV3.getPaginationTxnId());
		paramV4.setPageNo(paramsV3.getPageNo());
		paramV4.setFromUpdatedDate(paramsV3.getFromUpdatedDate());
		paramV4.setNextRequestHandlerIdentifier(paramsV3.getNextRequestHandlerIdentifier());
		return paramV4;
	}

	private static EntendedInfoV4 mapExtendedInfo(final ResponseDto responseV3) {
		EntendedInfoV4 entendedInfo = new EntendedInfoV4();
		if (Objects.nonNull(responseV3) && Objects.nonNull(responseV3.getPaginationParams())) {
			entendedInfo.setBgAppSyncNextListingUrl(responseV3.getPaginationParams().getBgAppSyncNextListingUrl());
			entendedInfo.setNextListingUrl(responseV3.getPaginationParams().getNextListingUrl());
		}

		entendedInfo.setInvalidateVersion(responseV3.getInvalidateVersion());
		return entendedInfo;
	}

	private static boolean isNarrationNeedsToBeRemoved(final String narration, final String name) {
		if (narration == null || narration.isEmpty()) {
			return false;
		}
		return (PLACEHOLDER_STRING_USED_AT_FE.equals(narration) || narration.equals(name));
	}

	private static boolean isBankDataNeedsToBeAdded(final EsResponseTxn txnV3) {

		if (Objects.isNull(txnV3) || Objects.isNull(txnV3.getContextualInfo())
				|| Objects.isNull(txnV3.getContextualInfo().getFlowType())) {
			return false;
		}
		TxnFlowTypeEnum txnFlowTypeEnum = txnV3.getContextualInfo().getFlowType();
		return TxnFlowTypeEnum.VPA_TO_ACCOUNT_TXN.equals(txnFlowTypeEnum)
				|| TxnFlowTypeEnum.SELF_TRANSFER_TXN.equals(txnFlowTypeEnum);
	}

	private static boolean isSelfTransfer(final EsResponseTxn txnV3) {
		if (Objects.isNull(txnV3) || Objects.isNull(txnV3.getContextualInfo())
				|| Objects.isNull(txnV3.getContextualInfo().getFlowType())) {
			return false;
		}
		return TxnFlowTypeEnum.SELF_TRANSFER_TXN.equals(txnV3.getContextualInfo().getFlowType());
	}

}
