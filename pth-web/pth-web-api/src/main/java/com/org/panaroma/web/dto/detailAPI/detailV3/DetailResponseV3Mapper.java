package com.org.panaroma.web.dto.detailAPI.detailV3;

import com.google.gson.Gson;
import com.org.panaroma.commons.dto.CardType;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.StatusEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.ParentDetails;
import com.org.panaroma.commons.dto.es.TransformedBankData;
import com.org.panaroma.commons.dto.es.TransformedCardData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoOrderTypeEnum;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.cstData.cstDeeplink.NeedHelpCtaBuilder;
import com.org.panaroma.web.dto.NameDetails;
import com.org.panaroma.web.dto.NameOrderTypeEnum;
import com.org.panaroma.web.dto.detailAPI.ParticipantInfo;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.EntityDetails;
import com.org.panaroma.web.utility.CtasNodeUtility;
import com.org.panaroma.web.utility.DataValidationUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.DeeplinkUtility;
import com.org.panaroma.web.utility.DetailResponseUtility;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.PaymentModeUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

import static com.org.panaroma.commons.constants.Constants.BANK_NAME_FOR_3P_APP_RECEIVER;
import static com.org.panaroma.commons.constants.Constants.DUMMY_VPA;
import static com.org.panaroma.commons.constants.Constants.IS_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_SETUP_FAILURE;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_SETUP_PENDING;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_SETUP_SUCCESS;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_UPI_LITE_ADD_MONEY_FAILURE;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_UPI_LITE_ADD_MONEY_PENDING;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_UPI_LITE_ADD_MONEY_SUCCESS;
import static com.org.panaroma.commons.constants.WebConstants.BY;
import static com.org.panaroma.commons.constants.WebConstants.CREDIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.DEBIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT_USER_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.FALSE;
import static com.org.panaroma.commons.constants.WebConstants.FROM;
import static com.org.panaroma.commons.constants.WebConstants.HYPHEN;
import static com.org.panaroma.commons.constants.WebConstants.IS_MERGED_DOCUMENT;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_BANK;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_SYSTEM_TYPE_GV;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_SYSTEM_TYPE_UPI;
import static com.org.panaroma.commons.constants.WebConstants.LogoPurpose.KYC_IMAGE;
import static com.org.panaroma.commons.constants.WebConstants.LogoPurpose.KYC_INITIALS;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO_ONE;
import static com.org.panaroma.commons.constants.WebConstants.NamePurpose.KYC;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_USER;
import static com.org.panaroma.commons.constants.WebConstants.SHOW_BENEF_ACC_NUMBER;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.TO;
import static com.org.panaroma.commons.constants.WebConstants.TO_ACCOUNT_MODE;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_INITIATION_MODE;
import static com.org.panaroma.commons.constants.WebConstants.UMN_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_LINKED_BANK_ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.USER;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_TPAP_HANDLES_LIST;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.ADD_AND_PAY;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.ADD_MONEY_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.CASHBACK_RECEIVED;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.CHARGES;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.CHARGES_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.CHARGES_REVERSAL;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.CHEQUE_CREDIT;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.CHEQUE_DEBIT;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.CHEQUE_DEBIT_REVERSAL;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.IPO_MANDATE;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.NACH_DEBIT;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.NACH_DEBIT_REVERSAL;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.ONE_TIME_MANDATE;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2M;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2M_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P2M;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P2M_INWARD;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P2M_OUTWARD;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P2M_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_INWARD;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_INWARD_REMITTANCE;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_INWARD_REVERSAL;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_OUTWARD;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_OUTWARD_REMITTANCE;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_OUTWARD_REMITTANCE_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_OUTWARD_REVERSAL;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.RECURRING_MANDATE;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.SBMD_MANDATE;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.STANDING_INSTRUCTION;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.SYSTEM_DEBIT;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.UPI_WALLET_CREDIT;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.UPI_WALLET_CREDIT_REVERSAL;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.WALLET_UPI_DEBIT_P2M;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.WALLET_UPI_DEBIT_P2P;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.WALLET_UPI_DEBIT_REVERSAL;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2P_INWARD_3P_APP;

import static com.org.panaroma.web.utility.LogoCreator.getBankLogoFor3PAppTxn;
import static com.org.panaroma.web.utility.MandateUtility.isRecurringMandateTxn;

@Log4j2
@Component
public class DetailResponseV3Mapper {

	private static final Map<Integer, String> secondInstrumentNarrationMap;

	private static final List<Integer> paymentSystemSupportedForUserName = Arrays.asList(
			PaymentSystemEnum.UPI.getPaymentSystemKey(), PaymentSystemEnum.BANK.getPaymentSystemKey(),
			PaymentSystemEnum.PPBL.getPaymentSystemKey());

	private static final List<Integer> detailV3SupportedTxnTypes;

	private static final List<Integer> bankInFirstInstrumentSupportedTxnTypes;

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public DetailResponseV3Mapper(final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		DetailResponseV3Mapper.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	private static final Gson gson = new Gson();

	static {
		bankInFirstInstrumentSupportedTxnTypes = List.of(P2P_INWARD.getTransactionTypeKey(),
				P2P_INWARD_3P_APP.getTransactionTypeKey(), P2P_OUTWARD.getTransactionTypeKey(),
				P2P_OUTWARD_REVERSAL.getTransactionTypeKey(), P2P_INWARD_REVERSAL.getTransactionTypeKey(),
				WALLET_UPI_DEBIT_P2P.getTransactionTypeKey(), UPI_WALLET_CREDIT_REVERSAL.getTransactionTypeKey(),
				UPI_WALLET_CREDIT.getTransactionTypeKey(), P2P_INWARD_REMITTANCE.getTransactionTypeKey(),
				P2P_OUTWARD_REMITTANCE.getTransactionTypeKey(), P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey(),
				P2P_UPI_TO_WALLET_INWARD.getTransactionTypeKey(), WALLET_UPI_DEBIT_REVERSAL.getTransactionTypeKey(),
				P2P_OUTWARD_REMITTANCE_REFUND.getTransactionTypeKey());

		detailV3SupportedTxnTypes = List.of(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey(),
				P2P_OUTWARD_REVERSAL.getTransactionTypeKey(), P2P_INWARD.getTransactionTypeKey(),
				P2P_INWARD_3P_APP.getTransactionTypeKey(), P2M.getTransactionTypeKey(),
				P2M_REFUND.getTransactionTypeKey(), ADD_MONEY_REFUND.getTransactionTypeKey(),
				P2P2M.getTransactionTypeKey(), P2P2M_REFUND.getTransactionTypeKey(),
				ADD_AND_PAY.getTransactionTypeKey(), P2P_UPI_TO_WALLET_INWARD.getTransactionTypeKey(),
				P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey(), STANDING_INSTRUCTION.getTransactionTypeKey(),
				CHEQUE_DEBIT.getTransactionTypeKey(), CHEQUE_CREDIT.getTransactionTypeKey(),
				CHEQUE_DEBIT_REVERSAL.getTransactionTypeKey(), NACH_DEBIT.getTransactionTypeKey(),
				NACH_DEBIT_REVERSAL.getTransactionTypeKey(), WALLET_UPI_DEBIT_P2M.getTransactionTypeKey(),
				WALLET_UPI_DEBIT_P2P.getTransactionTypeKey(), UPI_WALLET_CREDIT.getTransactionTypeKey(),
				WALLET_UPI_DEBIT_REVERSAL.getTransactionTypeKey(), UPI_WALLET_CREDIT_REVERSAL.getTransactionTypeKey(),
				P2P2M_INWARD.getTransactionTypeKey(), P2P2M_OUTWARD.getTransactionTypeKey(),
				P2P_INWARD_REMITTANCE.getTransactionTypeKey(), P2P_OUTWARD_REMITTANCE.getTransactionTypeKey(),
				P2P_OUTWARD_REMITTANCE_REFUND.getTransactionTypeKey(), P2P_INWARD_REVERSAL.getTransactionTypeKey(),
				RECURRING_MANDATE.getTransactionTypeKey(), IPO_MANDATE.getTransactionTypeKey(),
				CASHBACK_RECEIVED.getTransactionTypeKey(), CHARGES.getTransactionTypeKey(),
				CHARGES_REVERSAL.getTransactionTypeKey(), CHARGES_REFUND.getTransactionTypeKey(),
				SYSTEM_DEBIT.getTransactionTypeKey(), SBMD_MANDATE.getTransactionTypeKey(),
				ONE_TIME_MANDATE.getTransactionTypeKey());

		Map<Integer, String> map = new HashMap<>();
		map.put(TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey(), FROM);
		map.put(P2P_OUTWARD_REVERSAL.getTransactionTypeKey(), TO);
		map.put(P2P_INWARD.getTransactionTypeKey(), TO);
		map.put(P2M.getTransactionTypeKey(), FROM);
		map.put(P2M_REFUND.getTransactionTypeKey(), TO);
		map.put(ADD_MONEY_REFUND.getTransactionTypeKey(), TO);
		map.put(P2P2M.getTransactionTypeKey(), FROM);
		map.put(P2P2M_INWARD.getTransactionTypeKey(), TO);
		map.put(P2P2M_OUTWARD.getTransactionTypeKey(), FROM);
		map.put(P2P_INWARD_REMITTANCE.getTransactionTypeKey(), TO);
		map.put(P2P_OUTWARD_REMITTANCE.getTransactionTypeKey(), FROM);
		map.put(P2P_OUTWARD_REMITTANCE_REFUND.getTransactionTypeKey(), TO);
		map.put(ADD_AND_PAY.getTransactionTypeKey(), FROM);
		map.put(P2P_INWARD_REVERSAL.getTransactionTypeKey(), FROM);
		secondInstrumentNarrationMap = Collections.unmodifiableMap(map);

	}

	public static DetailApiResponseV3 mapDetailResponseV3UsingDetailResponseV2(
			final DetailApiResponseV2 detailApiResponseV2) {
		return gson.fromJson(gson.toJson(detailApiResponseV2), DetailApiResponseV3.class);
	}

	public static DetailApiResponseV3 getDetailEnhancedResponseForV3(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final Boolean isAccountNumberUnMaskingEnabled,
			final String locale) {

		try {

			/*
			 * Mapper mapper = new DozerBeanMapper(); DetailApiResponseV3
			 * detailApiResponseV3 =
			 * mapper.map(detailApiResponseV2,DetailApiResponseV3.class);
			 */
			DetailApiResponseV3 detailApiResponseV3 = mapDetailResponseV3UsingDetailResponseV2(detailApiResponseV2);

			// Todo: Jira Id
			if (DataValidationUtility.isOtherPartyBankDetailsRequired(listingVisibleTxn)
					|| (DataValidationUtility.isOtherPartyBankDetailsRequiredForVpa2AccountTxn()
							&& GenericUtilityExtension.isVpa2AccountTxn(listingVisibleTxn))) {
				modifyBankDetailsForFirstInstrument(detailApiResponseV3, listingVisibleTxn);
			}
			if (isHandledTxnTypes(listingVisibleTxn)) {
				modifyResponseForHandledTxnTypes(listingVisibleTxn, isAccountNumberUnMaskingEnabled,
						detailApiResponseV3);
				setParentDetails(detailApiResponseV3, listingVisibleTxn);
				CtasNodeUtility.setDetailCtaMap(listingVisibleTxn, detailApiResponseV3, locale);
			}
			else {
				mapFieldFromDetailV2toV3(detailApiResponseV3);
				modifyBankDetailsForSecondInstrument(detailApiResponseV3, listingVisibleTxn);
				CtasNodeUtility.setDetailCtaMap(listingVisibleTxn, detailApiResponseV3, locale);
			}
			CtasNodeUtility.setDetailInstrumentsCtaMap(listingVisibleTxn, detailApiResponseV3);
			// will remove this flag check if everything works fine
			if (configurablePropertiesHolder.getProperty("view-details-cta-enabled", Boolean.class)) {
				DeeplinkUtility.setViewDetailsCta(listingVisibleTxn, detailApiResponseV3, locale);
			}

			if (configurablePropertiesHolder.getProperty("need-help-cta-enabled", Boolean.class)) {
				NeedHelpCtaBuilder.setNeedHelpCta(listingVisibleTxn, detailApiResponseV3, locale);
			}

			// Removing this for incorrect msg issue
			// populatePaymentModeDetails(detailApiResponseV3,listingVisibleTxn);

			if (TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey()
				.equals(listingVisibleTxn.getMainTxnType())) {
				modifyAddMoneyToUpiLiteResponseForLiteTopupMandate(detailApiResponseV3, listingVisibleTxn);
			}
			return detailApiResponseV3;
		}
		catch (Exception e) {
			log.error("Exception while enriching detailV3 response for txnId : {} exception : {}",
					listingVisibleTxn.getTxnId(), CommonsUtility.exceptionFormatter(e));
			throw e;
		}

	}

	/*
	 * Below method was added as a part of https://jira.mypaytm.com/browse/PTH-497. This
	 * method is responsible for doing any additional handling in the response created
	 * using UpiLiteTopUpAndDeActivationResponseBuilder class because most of the detail
	 * api response for LITE_TOPUP_MANDATE is similar to ADD_MONEY_TO_UPI_LITE
	 */
	private static void modifyAddMoneyToUpiLiteResponseForLiteTopupMandate(
			final DetailApiResponseV3 detailApiResponseV3, final TransformedTransactionHistoryDetail tthd) {
		if (StringUtils.isNotBlank(tthd.getUmn())) {

			Map<String, String> refIdsMap;
			if (Objects.nonNull(detailApiResponseV3.getReferenceIdMap())) {
				/*
				 * TreeMap is used here to maintain lexicographical order of reference Ids
				 * name UMN should come first & then UPI Ref No
				 */
				refIdsMap = new TreeMap<>(detailApiResponseV3.getReferenceIdMap());
			}
			else {
				refIdsMap = new TreeMap<>();
			}
			refIdsMap.put(UMN_REFERENCE_NO, tthd.getUmn());
			detailApiResponseV3.setReferenceIds(GenericUtility.convertRefIdMapToList(refIdsMap));
		}

		if (Objects.nonNull(tthd) && Objects.nonNull(tthd.getContextMap())
				&& tthd.getContextMap().containsKey(EXECUTION_NO)) {
			String executionNo = GenericUtilityExtension.getExecutionNo(tthd);
			if (EXECUTION_NO_ONE.equals(executionNo)) {
				makeChangesSpecificToLiteTopupMandateSetup(detailApiResponseV3);
			}
			else {
				makeChangesSpecificToLiteTopupMandatePayment(detailApiResponseV3);
			}
		}

	}

	/*
	 * This method takes care of any extra handling in detail api response for executionNo
	 * = 1 case of LITE_TOPUP_MANDATE over the ADD_MONEY_TO_UPI_LITE detail api response
	 */
	private static void makeChangesSpecificToLiteTopupMandateSetup(final DetailApiResponseV3 detailApiResponseV3) {

		ClientStatusEnum statusEnum = ClientStatusEnum.valueOf(detailApiResponseV3.getStatus());
		switch (statusEnum) {
			case SUCCESS -> detailApiResponseV3
				.setDetailNarration(DETAIL_NARRATION_RECURRING_MANDATE_SETUP_SUCCESS.getLocaleMsgKey());
			case FAILURE -> detailApiResponseV3
				.setDetailNarration(DETAIL_NARRATION_RECURRING_MANDATE_SETUP_FAILURE.getLocaleMsgKey());
			case PENDING -> detailApiResponseV3
				.setDetailNarration(DETAIL_NARRATION_RECURRING_MANDATE_SETUP_PENDING.getLocaleMsgKey());
		}

		for (InstrumentDtoV3 instrumentDtoV3 : detailApiResponseV3.firstInstrument) {
			instrumentDtoV3.setNarration("For");
			instrumentDtoV3.setName("UPI Lite - Automatic Add Money");
		}

		detailApiResponseV3.setDateTimeLabel("Created at");

	}

	/*
	 * This method takes care of any extra handling in detail api response for executionNo
	 * > 1 case of LITE_TOPUP_MANDATE over the ADD_MONEY_TO_UPI_LITE detail api response
	 */
	private static void makeChangesSpecificToLiteTopupMandatePayment(final DetailApiResponseV3 detailApiResponseV3) {

		ClientStatusEnum statusEnum = ClientStatusEnum.valueOf(detailApiResponseV3.getStatus());
		switch (statusEnum) {
			case SUCCESS ->
				detailApiResponseV3.setDetailNarration(DETAIL_NARRATION_UPI_LITE_ADD_MONEY_SUCCESS.getLocaleMsgKey());
			case FAILURE ->
				detailApiResponseV3.setDetailNarration(DETAIL_NARRATION_UPI_LITE_ADD_MONEY_FAILURE.getLocaleMsgKey());
			case PENDING ->
				detailApiResponseV3.setDetailNarration(DETAIL_NARRATION_UPI_LITE_ADD_MONEY_PENDING.getLocaleMsgKey());
		}

	}

	private static void populatePaymentModeDetails(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (TransactionTypeEnum.P2M
			.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(listingVisibleTxn.getMainTxnType()))) {
			detailApiResponseV3.setPaymentMode(PaymentModeUtility.getPaymentModeDetails(listingVisibleTxn));
			detailApiResponseV3.setPaymentModeLogoUrl(PaymentModeUtility.getPaymentModeLogoUrl(listingVisibleTxn));
		}
	}

	/**
	 * Sets the bank detail in instrument name field in the format : (bankName) - (last 4
	 * digits of AccNum) and sets instrument detail as null
	 * @param detailApiResponseV3 detail page
	 */

	private static void modifyBankDetailsForSecondInstrument(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail txn) {
		for (InstrumentDtoV3 instrumentDtoV3 : detailApiResponseV3.getSecondInstrument()) {
			TransformedParticipant bankParticipant = null;
			if (instrumentDtoV3.getParticipantInfo() != null
					&& instrumentDtoV3.getParticipantInfo().getParticipant() != null) {
				bankParticipant = instrumentDtoV3.getParticipantInfo().getParticipant();
			}
			if (invalidForSettingBankDetailsInSecondInstrument(txn, bankParticipant)) {
				continue;
			}
			String bankName = bankParticipant.getBankData().getBankName();
			String bankAccNum = bankParticipant.getBankData().getAccNumber();
			String bankIfsc = bankParticipant.getBankData().getIfsc();
			if (bankName == null) {
				continue;
			}
			if (!isHybridTxn(detailApiResponseV3)) {
				EntityDetails entityDetails = new EntityDetails();
				String bankNameAccNo = getBankNameAccNum(null, bankName, bankAccNum);
				NameDetails nameDetails = new NameDetails(NameOrderTypeEnum.STRING, bankNameAccNo);
				entityDetails.setNameOrder(Collections.singletonList(nameDetails));
				Logo logo;
				if (ObjectUtils.isNotEmpty(txn)
						&& TransactionTypeEnum.P2P_INWARD_3P_APP.getTransactionTypeKey().equals(txn.getMainTxnType())) {
					logo = new Logo(LogoOrderTypeEnum.URL, getBankLogoFor3PAppTxn(txn));
				}
				else {
					logo = new Logo(LogoOrderTypeEnum.URL, LogoUtility.getBankLogo(bankIfsc, bankName));
				}
				entityDetails.setLogoOrder(Collections.singletonList(logo));
				instrumentDtoV3.setEntityDetails(entityDetails);
			}
			else {

				if (ObjectUtils.isNotEmpty(txn)
						&& TransactionTypeEnum.P2P_INWARD_3P_APP.getTransactionTypeKey().equals(txn.getMainTxnType())) {
					instrumentDtoV3.setName(BANK_NAME_FOR_3P_APP_RECEIVER);
				}
				else {
					instrumentDtoV3.setName(getBankNameAccNum(null, bankName, bankAccNum));
				}
			}
			instrumentDtoV3.setInstrumentDetail(null);
		}
	}

	/**
	 * Checks :
	 * <ul>
	 * <li>paymentSystem in PPBL/BANK/UPI</li>
	 * <li>card data not available since it is the preferred instrumentDetail</li>
	 * <li>accNum and bankName not available since then the detail is via NET-BANKING</li>
	 * </ul>
	 * @param transformedParticipant participant to check
	 * @return if all checks pass
	 */
	private static boolean invalidForSettingBankDetailsInSecondInstrument(final TransformedTransactionHistoryDetail txn,
			final TransformedParticipant transformedParticipant) {
		if (transformedParticipant == null) {
			return true;
		}
		PaymentSystemEnum participantPaymentSystem = PaymentSystemEnum
			.getPaymentSystemEnumByKey(transformedParticipant.getPaymentSystem());
		if (!(PaymentSystemEnum.PPBL == participantPaymentSystem || PaymentSystemEnum.BANK == participantPaymentSystem
				|| PaymentSystemEnum.UPI == participantPaymentSystem)) {
			return true;
		}

		// Handling for UPI Lite
		// Txn done using upiLIte will be invalid for setting bankDetails
		// InSecondInstrument for Add Money and self Transfer
		if (UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(transformedParticipant) && Boolean.TRUE
			.equals(TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(txn.getMainTxnType())
					|| isSelfTransfer(txn))) {
			return true;
		}

		if (transformedParticipant.getCardData() != null) {
			return true;
		}
		return transformedParticipant.getBankData() == null
				|| transformedParticipant.getBankData().getBankName() == null;
	}

	/**
	 * Modifies bank details for First Instrument
	 * <p>
	 * Adds linked bank details and bank logo in instrument map, if bank details available
	 * in participant of First Instrument.
	 * </p>
	 * @param detailApiResponseV3 detail page
	 * @param listingVisibleTxn ES entry
	 */
	private static void modifyBankDetailsForFirstInstrument(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {

		if (isInvalidForFirstInstrumentBankModification(detailApiResponseV3, listingVisibleTxn)) {
			return;
		}
		String txnInitiationMode = null;

		if (listingVisibleTxn.getContextMap() != null
				&& configurablePropertiesHolder.getProperty(SHOW_BENEF_ACC_NUMBER, Boolean.class)) {
			txnInitiationMode = listingVisibleTxn.getContextMap().get(TXN_INITIATION_MODE);
		}

		for (InstrumentDtoV3 instrumentDtoV3 : detailApiResponseV3.getFirstInstrument()) {
			TransformedParticipant bankParticipant = null;
			if (instrumentDtoV3.getParticipantInfo() != null
					&& instrumentDtoV3.getParticipantInfo().getParticipant() != null) {
				bankParticipant = instrumentDtoV3.getParticipantInfo().getParticipant();
			}
			if (TransactionSource.PPBL.getTransactionSourceKey().equals(listingVisibleTxn.getStreamSource())) {
				bankParticipant = Utility.getOtherParticipant(listingVisibleTxn);
			}
			if (bankParticipant == null || bankParticipant.getBankData() == null) {
				continue;
			}
			String bankName;
			String bankIfsc;
			String bankAccNum;
			bankName = bankParticipant.getBankData().getBankName();
			bankIfsc = bankParticipant.getBankData().getIfsc();
			bankAccNum = bankParticipant.getBankData().getAccNumber();
			if (bankName == null) {
				continue;
			}
			InstrumentDetailsDtoV3 bankDetails;
			String bankNameAccNo = getBankNameAccNum(txnInitiationMode, bankName, bankAccNum);
			bankDetails = new InstrumentDetailsDtoV3(LINKED_BANK, bankNameAccNo);
			bankDetails.setLogoOrder(new LinkedList<>());
			String logo = LogoUtility.getBankLogoOrNull(bankIfsc, bankName);
			if (logo != null) {
				bankDetails.getLogoOrder().add(new Logo(LogoOrderTypeEnum.URL, logo));
			}
			if (instrumentDtoV3.getInstrumentDetailsMap() == null) {
				instrumentDtoV3.setInstrumentDetailsMap(new LinkedList<>());
			}
			instrumentDtoV3.setInstrumentDetailsMap(instrumentDtoV3.getInstrumentDetailsMap()
				.stream()
				.filter(instrumentDetailsDto -> !instrumentDetailsDto.getLabel().equalsIgnoreCase(LINKED_BANK))
				.collect(Collectors.toList()));
			instrumentDtoV3.getInstrumentDetailsMap().add(bankDetails);
			if (instrumentDtoV3.getInstrumentDetailsMap().isEmpty()) {
				instrumentDtoV3.setInstrumentDetailsMap(null);
			}
		}
	}

	private static String getBankNameAccNum(final String txnInitiationMode, final String bankName,
			final String bankAccNum) {
		if (bankAccNum != null) {
			if (txnInitiationMode != null && TO_ACCOUNT_MODE.equalsIgnoreCase(txnInitiationMode)) {
				return String.format("%s - %s", bankName, bankAccNum);
			}
			else {
				return String.format("%s - %s", bankName, GenericUtilityExtension.getLastNcharacters(bankAccNum, 4));
			}
		}
		else {
			return bankName;
		}
	}

	private static boolean isInvalidForFirstInstrumentBankModification(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (!bankInFirstInstrumentSupportedTxnTypes.contains(listingVisibleTxn.getTxnType())) {
			return true;
		}
		if (isSelfTransfer(listingVisibleTxn)) {
			return true;
		}
		if (UpiLiteUtility.isUpiLiteTxn(listingVisibleTxn)
				&& P2P_INWARD.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(listingVisibleTxn.getTxnType()))) {
			return true;
		}
		if (Utility.isUpiFirTxn(listingVisibleTxn)
				&& P2P_INWARD.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(listingVisibleTxn.getTxnType()))) {
			return true;
		}
		if (UpiLiteUtility.isUpiLiteOfflineTxn(listingVisibleTxn) && P2P_OUTWARD
			.equals(TransactionTypeEnum.getTransactionTypeEnumByKey(listingVisibleTxn.getTxnType()))) {
			return true;
		}
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(listingVisibleTxn.getStreamSource())) {
			if (detailApiResponseV3.getFirstInstrument().size() > 1) {
				log.warn("Not handled hybrid 1st instrument for bank txns");
				return true;
			}
		}
		return false;
	}

	private static void modifyResponseForHandledTxnTypes(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final Boolean isAccountNumberUnMaskingEnabled, final DetailApiResponseV3 detailApiResponseV3) {
		updateEntityDetailInSecondInstrument(detailApiResponseV3, listingVisibleTxn);
		updateInstrumentDetail(detailApiResponseV3, listingVisibleTxn, isAccountNumberUnMaskingEnabled);
		updateInstrumentDetailMapForSecondInstrument(detailApiResponseV3, listingVisibleTxn);
		updateNarrationForDetailV3(detailApiResponseV3, listingVisibleTxn);

		updateEntityDetailInFirstInstrument(detailApiResponseV3, listingVisibleTxn);
	}

	/**
	 * @param listingVisibleTxn ES entry to check
	 * @return if txn is valid for changes as per mentioned jira
	 */
	private static boolean isHandledTxnTypes(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		return detailV3SupportedTxnTypes.contains(listingVisibleTxn.getTxnType()) && !isSelfTransfer(listingVisibleTxn);
	}

	private static void mapFieldFromDetailV2toV3(final DetailApiResponseV3 detailApiResponseV3) {
		if (!isHybridTxn(detailApiResponseV3)) {
			InstrumentDtoV3 secondInstrument = (ObjectUtils.isNotEmpty(detailApiResponseV3.getSecondInstrument()))
					? detailApiResponseV3.getSecondInstrument().get(0) : null;
			populateEntityDetailUsingSourceDetails(secondInstrument);
		}
	}

	private static void populateEntityDetailUsingSourceDetails(final InstrumentDtoV3 secondInstrument) {

		if (ObjectUtils.isEmpty(secondInstrument)) {
			return;
		}
		EntityDetails entityDetails = new EntityDetails();
		if (ObjectUtils.isNotEmpty(secondInstrument.getSourceDetails())) {
			entityDetails.setLogoOrder(secondInstrument.getSourceDetails().getLogoOrder());
		}
		entityDetails.getLogoOrder().add(new Logo(LogoOrderTypeEnum.URL, secondInstrument.getLogoUrl()));
		secondInstrument.setLogoUrl(null);
		secondInstrument.getSourceDetails().setLogoOrder(null);
		NameDetails name = new NameDetails();
		name.setType(NameOrderTypeEnum.STRING);
		name.setValue(secondInstrument.getName());
		secondInstrument.setName(null);
		entityDetails.setNameOrder(Collections.singletonList(name));
		secondInstrument.setEntityDetails(entityDetails);
	}

	private static void updateNarrationForDetailV3(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		String narration = secondInstrumentNarrationMap.getOrDefault(listingVisibleTxn.getTxnType(),
				getDefaultNarrationBasedOnTxnIndicator(listingVisibleTxn));

		if (isHybridTxn(detailApiResponseV3) && !GenericUtilityExtension.isStoreCashTxn(listingVisibleTxn)
				&& !GenericUtilityExtension.isDeafTxn(listingVisibleTxn)) {
			narration = BY;
			if (TransactionTypeEnum.P2M_REFUND.getTransactionType()
				.equalsIgnoreCase(detailApiResponseV3.getTxnType())) {
				narration = TO;
			}
		}
		for (InstrumentDtoV3 instrumentDto : detailApiResponseV3.getSecondInstrument()) {
			instrumentDto.setNarration(narration);
		}
	}

	private static String getDefaultNarrationBasedOnTxnIndicator(
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (listingVisibleTxn.getTxnIndicator().equals(TransactionIndicator.CREDIT.getTransactionIndicatorKey())) {
			return TO;
		}
		return FROM;
	}

	private static void updateInstrumentDetail(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn,
			final Boolean isAccountNumberUnMaskingEnabled) {
		if (ObjectUtils.isEmpty(detailApiResponseV3.getSecondInstrument())) {
			return;
		}
		if (isSelfTransfer(listingVisibleTxn)) {
			return;
		}
		for (InstrumentDtoV3 instrumentDtoV3 : detailApiResponseV3.getSecondInstrument()) {
			if (isHybridTxn(detailApiResponseV3)) {
				instrumentDtoV3.setName(
						getNewInstrumentDetail(listingVisibleTxn, instrumentDtoV3, isAccountNumberUnMaskingEnabled));
				instrumentDtoV3.setInstrumentDetail(null);
			}
			else {
				instrumentDtoV3.setInstrumentDetail(
						getNewInstrumentDetail(listingVisibleTxn, instrumentDtoV3, isAccountNumberUnMaskingEnabled));
				instrumentDtoV3.setName(null);
			}
		}
		setVpaInstrumentDetails(detailApiResponseV3, listingVisibleTxn);
	}

	private static void setVpaInstrumentDetails(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		// Setting instrument name to payer vpa in case txn is recurring mandate and bank
		// acct num is nt present
		// In Mandate txns only one second instrument is possible.
		if (StringUtils.isBlank(detailApiResponseV3.getSecondInstrument().get(0).getInstrumentDetail())
				&& isRecurringMandateTxn(listingVisibleTxn)) {
			TransformedParticipant mandateParticipant = getMandateUserParticipant(listingVisibleTxn);
			if (Objects.nonNull(mandateParticipant) && Objects.nonNull(mandateParticipant.getUpiData())) {
				detailApiResponseV3.getSecondInstrument()
					.get(0)
					.setInstrumentDetail(mandateParticipant.getUpiData().getVpa());
			}
		}
		else if (StringUtils.isBlank(detailApiResponseV3.getSecondInstrument().get(0).getInstrumentDetail())
				&& detailApiResponseV3.getSecondInstrument()
					.get(0)
					.getParticipantInfo()
					.getParticipant()
					.getBankData() == null
				&& detailApiResponseV3.getSecondInstrument()
					.get(0)
					.getParticipantInfo()
					.getParticipant()
					.getUpiData() != null) {
			// If the bank data is null, then we will check the VPA in case of a payment
			// system using UPI.
			String vpa = detailApiResponseV3.getSecondInstrument()
				.get(0)
				.getParticipantInfo()
				.getParticipant()
				.getUpiData()
				.getVpa();
			if (!DUMMY_VPA.equalsIgnoreCase(vpa)) {
				detailApiResponseV3.getSecondInstrument().get(0).setInstrumentDetail(vpa);
			}

		}
	}

	private static void updateInstrumentDetailMapForSecondInstrument(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (ObjectUtils.isEmpty(detailApiResponseV3.getSecondInstrument())
				|| UpiLiteUtility.isUpiLiteTxn(listingVisibleTxn)) {
			return;
		}
		TransformedParticipant participant = null;

		// Now run through all instrument and populate instrumentDetailMap
		for (InstrumentDtoV3 instrumentDto : detailApiResponseV3.getSecondInstrument()) {
			if (instrumentDto != null && instrumentDto.getParticipantInfo() != null) {
				participant = instrumentDto.getParticipantInfo().getParticipant();

				// Determine payment system type once
				String paymentSystemType = getPaymentSystemType(participant);

				// Handle based on payment system type
				if (PAYMENT_SYSTEM_TYPE_UPI.equals(paymentSystemType)) {
					// case 2 : normal UPI then populate vpa and bank
					// [PTH-1085] Add VPA and bank details for both mandate and regular
					// UPI transactions.
					DetailResponseUtility.populateVpaDetailsOnInstrumentDetailMap(instrumentDto, participant,
							configurablePropertiesHolder.getProperty(PAYTM_TPAP_HANDLES_LIST, ArrayList.class),
							listingVisibleTxn);
					DetailResponseUtility.populateBankDetailsOnInstrumentDetailMap(instrumentDto, participant,
							listingVisibleTxn, paymentSystemType);
				}
				else if (PAYMENT_SYSTEM_TYPE_GV.equals(paymentSystemType)) {
					// Handle GV (Gift Voucher) transactions specifically
					DetailResponseUtility.populateBankDetailsOnInstrumentDetailMap(instrumentDto, participant,
							listingVisibleTxn, paymentSystemType);
				}
				// OTHER payment systems are ignored
			}
		}
	}

	private static String getPaymentSystemType(final TransformedParticipant participant) {
		String paymentSystemType = null;
		if (GenericUtilityExtension.isTxnPoweredByUpi(participant)) {
			paymentSystemType = PAYMENT_SYSTEM_TYPE_UPI;
		}
		else if (PaymentSystemEnum.GV.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
			paymentSystemType = PAYMENT_SYSTEM_TYPE_GV;
		}
		return paymentSystemType;
	}

	private static TransformedParticipant getMandateUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		Optional<TransformedParticipant> transformedParticipant = tthd.getParticipants()
			.stream()
			.filter(participant -> EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType()))
			.findFirst();
		return transformedParticipant.orElse(null);
	}

	private static void updateEntityDetailInSecondInstrument(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (isSelfTransfer(listingVisibleTxn)) {
			return;
		}
		List<InstrumentDtoV3> secondInstrument = detailApiResponseV3.getSecondInstrument();
		for (InstrumentDtoV3 instrumentDto : secondInstrument) {
			EntityDetails entityDetails = new EntityDetails();
			entityDetails
				.setNameOrder(getNameOrderInEntityDetails(instrumentDto.getParticipantInfo(), listingVisibleTxn));
			entityDetails
				.setLogoOrder(getLogoOrderInEntityDetails(instrumentDto.getParticipantInfo(), listingVisibleTxn));
			instrumentDto.setEntityDetails(entityDetails);
		}
	}

	private static void updateEntityDetailInFirstInstrument(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		List<InstrumentDtoV3> firstInstrument = detailApiResponseV3.getFirstInstrument();
		for (InstrumentDtoV3 instrumentDto : firstInstrument) {
			DetailResponseUtility.handleNullNameForInstrument(instrumentDto, listingVisibleTxn);
		}
	}

	private static boolean isSelfTransfer(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		return ObjectUtils.isNotEmpty(listingVisibleTxn.getContextMap())
				&& TRUE.equals(listingVisibleTxn.getContextMap().get(IS_SELF_TRANSFER));
	}

	private static List<Logo> getLogoOrderInEntityDetails(final ParticipantInfo participantInfo,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		List<Logo> logoOrder = new ArrayList<>();
		logoOrder.add(new Logo(LogoOrderTypeEnum.KYC_IMAGE, KYC_IMAGE));
		if (isValidToPickUsernameFromBankEvent(participantInfo, listingVisibleTxn)) {

			String userNameFromBankEvent = GenericUtility.singleSpacing(participantInfo.getParticipant().getName());
			Logo userNameLogo = new Logo();
			userNameLogo.setType(LogoOrderTypeEnum.STRING);
			userNameLogo.setValue(GenericUtilityExtension.getInitials(userNameFromBankEvent));

			logoOrder.add(userNameLogo);
		}

		logoOrder.add(new Logo(LogoOrderTypeEnum.KYC_INITIALS, KYC_INITIALS));
		logoOrder.add(new Logo(LogoOrderTypeEnum.URL, LogoUtility.getLogo(DEFAULT_USER_LOGO, LogoType.OTHER)));
		return logoOrder;
	}

	private static List<Logo> getLogoOrderInEntityDetailsForFirstInstrument(final ParticipantInfo participantInfo) {
		List<Logo> logoOrder = new ArrayList<>();
		if (StringUtils.isNotBlank(participantInfo.getParticipant().getEntityId())) {
			logoOrder.add(new Logo(LogoOrderTypeEnum.KYC_IMAGE, KYC_IMAGE));
			logoOrder.add(new Logo(LogoOrderTypeEnum.KYC_INITIALS, KYC_INITIALS));
		}

		logoOrder.add(new Logo(LogoOrderTypeEnum.URL, LogoUtility.getLogo(DEFAULT_USER_LOGO, LogoType.OTHER)));
		return logoOrder;
	}

	private static List<NameDetails> getNameOrderInEntityDetails(final ParticipantInfo participantInfo,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {

		List<NameDetails> nameOrder = new ArrayList<>();
		if (isValidToPickUsernameFromBankEvent(participantInfo, listingVisibleTxn)) {

			String userNameFromBankEvent = GenericUtility.singleSpacing(participantInfo.getParticipant().getName());
			NameDetails nameDetails = new NameDetails();
			nameDetails.setType(NameOrderTypeEnum.STRING);
			nameDetails.setValue(WordUtils.capitalizeFully(userNameFromBankEvent));
			nameOrder.add(nameDetails);
		}
		setDefaultNameOrder(nameOrder);
		return nameOrder;
	}

	private static boolean isValidToPickUsernameFromBankEvent(final ParticipantInfo participantInfo,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (TransactionSource.OMS.getTransactionSourceKey().equals(listingVisibleTxn.getTxnType())
				&& (Objects.isNull(listingVisibleTxn.getContextMap().get(IS_MERGED_DOCUMENT))
						|| FALSE.equalsIgnoreCase(listingVisibleTxn.getContextMap().get(IS_MERGED_DOCUMENT)))) {
			return false;
		}

		return ObjectUtils.isNotEmpty(participantInfo) && ObjectUtils.isNotEmpty(participantInfo.getParticipant())
				&& StringUtils.isNotBlank(participantInfo.getParticipant().getName())
				&& paymentSystemSupportedForUserName.contains(participantInfo.getParticipant().getPaymentSystem());
	}

	private static List<NameDetails> getNameOrderInEntityDetailsForFirstInstrument(
			final ParticipantInfo participantInfo) {

		List<NameDetails> nameOrder = new ArrayList<>();
		if (ObjectUtils.isNotEmpty(participantInfo) && ObjectUtils.isNotEmpty(participantInfo.getParticipant())
				&& StringUtils.isBlank(participantInfo.getParticipant().getName())) {
			if (StringUtils.isNotBlank(participantInfo.getParticipant().getEntityId())) {
				setDefaultNameOrder(nameOrder);
			}
			else {
				nameOrder.add(new NameDetails(NameOrderTypeEnum.STRING, USER));
			}
		}
		return nameOrder;
	}

	private static String getCardIssuerAndNumber(final TransformedCardData cardData, final int numberOfLastDigits) {
		if (ObjectUtils.isEmpty(cardData)) {
			return null;
		}
		String cardIssuerAndNumber = null;
		String cardIssuer = cardData.getCardIssuer();
		String cardNum = cardData.getCardNum();
		String cardType = CardType.CREDIT.getCardTypeKey().equals(cardData.getCardType()) ? CREDIT_CARD : DEBIT_CARD;
		if (ObjectUtils.isNotEmpty(cardIssuer) && ObjectUtils.isNotEmpty(cardType)) {
			cardIssuerAndNumber = cardIssuer;
			cardIssuerAndNumber += SPACE + cardType;

			if (StringUtils.isNotBlank(cardNum)) {
				cardIssuerAndNumber += SPACE + HYPHEN + SPACE
						+ GenericUtilityExtension.getLastNcharacters(cardNum, numberOfLastDigits);
			}
		}
		return cardIssuerAndNumber;
	}

	private static String getBankNameAndNumber(final TransformedBankData bankData, final int numberOfLastDigits,
			final Boolean isAccountNumberUnMaskingEnabled) {
		if (ObjectUtils.isEmpty(bankData)) {
			return null;
		}
		String bankName = bankData.getBankName();
		String acctNumber = bankData.getAccNumber();

		if (StringUtils.isBlank(bankName)) {
			return null;
		}
		String bankNameAndNumber = bankName;

		if (StringUtils.isNotBlank(acctNumber)) {
			bankNameAndNumber += (isAccountNumberUnMaskingEnabled)
					? (SPACE + HYPHEN + SPACE
							+ GenericUtilityExtension.getLastNcharacters(acctNumber, numberOfLastDigits))
					: (GenericUtility.getDroolsMaskedAccountNumber(acctNumber, isAccountNumberUnMaskingEnabled));
		}
		return bankNameAndNumber;
	}

	private static String getNewInstrumentDetail(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final InstrumentDtoV3 instrumentDtoV3, final Boolean isAccountNumberUnMaskingEnabled) {
		String instrumentDetail;
		instrumentDetail = instrumentDtoV3.getName();
		if (Objects.nonNull(instrumentDtoV3.getParticipantInfo())
				&& (Utility.isUpiViaCcTxn(instrumentDtoV3.getParticipantInfo().getParticipant()) || UpiLiteUtility
					.isUpiLiteTxnAndPaymentInstrument(instrumentDtoV3.getParticipantInfo().getParticipant()))) {
			return instrumentDetail;
		}
		ParticipantInfo participantInfo = instrumentDtoV3.getParticipantInfo();
		if (ObjectUtils.isNotEmpty(participantInfo) && ObjectUtils.isNotEmpty(participantInfo.getParticipant())) {

			PaymentSystemEnum paymentSystem = PaymentSystemEnum
				.getPaymentSystemEnumByKey(participantInfo.getParticipant().getPaymentSystem());
			if (paymentSystem == null) {
				log.error(
						"unexpected payment system for detail V3 response to create instrumentDetail, participant entityId:{} ",
						participantInfo.getParticipant().getEntityId());
				throw new RuntimeException("Unexpected payment system for detail V3 response");
			}
			switch (paymentSystem) {
				case PG:
					if (participantInfo.getParticipant().getCardData() != null) {
						instrumentDetail = getCardIssuerAndNumber(participantInfo.getParticipant().getCardData(), 4);
					}
					break;
				case BANK:
				case PPBL:
					if (participantInfo.getParticipant().getCardData() != null) {
						instrumentDetail = getCardIssuerAndNumber(participantInfo.getParticipant().getCardData(), 4);
					}
					else {
						instrumentDetail = getBankNameAndNumber(participantInfo.getParticipant().getBankData(), 4,
								true);
					}
					break;
				case UPI:
					if (ObjectUtils.isNotEmpty(listingVisibleTxn)
							&& TransactionTypeEnum.P2P_INWARD_3P_APP.getTransactionTypeKey()
								.equals(listingVisibleTxn.getMainTxnType())) {
						instrumentDetail = BANK_NAME_FOR_3P_APP_RECEIVER;
					}
					else {
						instrumentDetail = getBankNameAndNumber(participantInfo.getParticipant().getBankData(), 4,
								isAccountNumberUnMaskingEnabled);
					}
					if (StringUtils.isBlank(instrumentDetail)
							&& P2M_REFUND.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
							&& TransactionSource.OMS.getTransactionSourceKey()
								.equals(listingVisibleTxn.getStreamSource())) {
						instrumentDetail = UPI_LINKED_BANK_ACCOUNT;
					}
					break;
				default:
			}
		}
		return instrumentDetail;
	}

	private static void setDefaultNameOrder(final List<NameDetails> nameOrder) {
		NameDetails kycName = new NameDetails();
		kycName.setType(NameOrderTypeEnum.KYC);
		kycName.setValue(KYC);

		nameOrder.add(kycName);
		nameOrder.add(new NameDetails(NameOrderTypeEnum.STRING, PAYTM_USER));
	}

	public static boolean isHybridTxn(final DetailApiResponseV3 detailApiResponseV3) {
		List<InstrumentDtoV3> secondInstrument = detailApiResponseV3.getSecondInstrument();
		return ObjectUtils.isNotEmpty(secondInstrument) && secondInstrument.size() > 1;
	}

	public static void setParentDetails(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail tthd) {
		if (P2M_REFUND.getTransactionTypeKey().equals(tthd.getTxnType())
				&& (TransactionSource.isPgTypeSource(tthd.getStreamSource()))
				|| TransactionSource.OMS.getTransactionSourceKey().equals(tthd.getStreamSource())) {
			ParentDetails parentDetails = tthd.getParentDetails();

			if (Objects.nonNull(parentDetails) && Boolean.TRUE.equals(parentDetails.isParentExistsInDb())
					&& StatusEnum.SUCCESS.toString().equals(detailApiResponseV3.getStatus())) {
				ParentDetailsDto parentTxnDetailsDto = new ParentDetailsDto();
				parentTxnDetailsDto.setTxnDate(DateTimeUtility.getDateTime(parentDetails.getTxnDate()));
				parentTxnDetailsDto.setTxnId(parentDetails.getTxnId());
				parentTxnDetailsDto.setTxnSource(parentDetails.getTxnSource());
				detailApiResponseV3.setParentDetails(parentTxnDetailsDto);
			}
		}
	}

}