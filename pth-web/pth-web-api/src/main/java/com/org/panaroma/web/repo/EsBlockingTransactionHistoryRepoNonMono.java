package com.org.panaroma.web.repo;

import static com.org.panaroma.commons.constants.WebConstants.*;
import static com.org.panaroma.commons.utils.IndexUtility.getIndexListInRange;
import static com.org.panaroma.web.constants.TagsConstants.GET_AUTO_COMPLETE_SUGGESTIONS_FROM_ES_FUNC;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.DETAIL_COULD_NOT_BE_LOADED;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.ES_EXCEPTION;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NO_TRANSACTION_FOUND_FOR_LISTING;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.TRANSFORMATION_ERROR;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AUTO_COMPLETE_LIMIT_BREACHED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AUTO_COMPLETE_LIMIT_BREACHED_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COMMA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ES_REST_CLIENT_AUTO_COMPLETE_API_SERVICE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.LIMIT_BREACHED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.LIMIT_TIME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.NOT_AVAILABLE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RETRY_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SEARCH_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SEARCH_TYPE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SERVICE_ELASTIC_SEARCH;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.config.ElasticSearchIndexRangeConfig;
import com.org.panaroma.commons.config.IndexConfig;
import com.org.panaroma.commons.config.SearchIndexRangeConfig;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.IndexUtility;
import com.org.panaroma.web.utility.ResultsMapperExtended;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.cache.CacheContext;
import com.org.panaroma.web.dto.SearchCriteriaEnum;
import com.org.panaroma.web.dto.autoComplete.AutoCompleteEsHighlightedResp;
import com.org.panaroma.web.dto.detailAPI.ExtraDetailRequestFields;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.exceptionhandler.ExceptionHandlerUtil;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.GroupingUtility;
import com.org.panaroma.web.utility.SearchIntelligentUtility;
import com.org.panaroma.web.utility.TransactionHistoryMonitoringUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

@Repository
@Log4j2
@Qualifier("EsBlockingTransactionHistoryRepoNonMono")
@NoArgsConstructor
public class EsBlockingTransactionHistoryRepoNonMono implements TransactionHistoryRepoNonMono {

	// protected RestHighLevelClient restHighLevelClient;

	// protected RestHighLevelClient managedRestHighLevelClient;

	protected RestHighLevelClient managedV2RestHighLevelClient;

	@Autowired
	private ResultsMapperExtended resultMapper;

	@Autowired
	protected MetricsAgent metricsAgent;

	@Value("${elastic-search-index}")
	private String indexName;

	@Value("${elastic-search-index-prefix-for-details}")
	private String indexPrefix;

	// @Value("${primary.es.cluster}")
	// private String primaryEsCluster;

	@Value("${from-date-listing-filter}")
	private String fromDateString;

	@Value("${elastic-search-index-prefix-for-details}")
	private String esIndexPreficForDetail;

	@Value("${detail-retriable-count}")
	private int retriableCount;

	@Value("${detail-retriable-wait-time}")
	private int retringWaitTime;

	@Value("${max.Time.To.Print.AutoComplete.Logger}")
	private Integer maxTimeToPrintAutoCompleteLogger;

	@Value("${updated.index.month.list}")
	private List<String> updatedIndexMonthList;

	// protected List<String> whiteListedUsersForManagedEs;

	// protected Double rollOutPercentage;

	@Autowired
	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	protected SearchIndexRangeConfig searchIndexRangeConfig;

	@Autowired
	protected ElasticSearchIndexRangeConfig elasticSearchIndexRangeConfig;

	private AtomicInteger atomicIntForEsHitsForDetailMtx = new AtomicInteger(0);

	private static final String[] PRE_TAGS_ARRAY = new String[] { "<" };

	private static final String[] POST_TAGS_ARRAY = new String[] { ">" };

	@Autowired
	public EsBlockingTransactionHistoryRepoNonMono(
			// @Qualifier("oldRestEsClient") final RestHighLevelClient
			// restHighLevelClient,
			// @Qualifier("managedEsClient") final RestHighLevelClient
			// managedRestHighLevelClient,
			@Qualifier("managedV2EsClient") final RestHighLevelClient managedV2RestHighLevelClient) {
		// @Value("#{'${whitelisted.users.list.for.managed.es}'.split(',')}") final
		// List<String> whiteListedUsersForManagedEs,
		// @Value("${managed.roll.out.percentage}" final Double rollOutPercentage
		// this.restHighLevelClient = restHighLevelClient;
		// this.managedRestHighLevelClient = managedRestHighLevelClient;
		this.managedV2RestHighLevelClient = managedV2RestHighLevelClient;
		// this.whiteListedUsersForManagedEs = whiteListedUsersForManagedEs;
		// this.rollOutPercentage = rollOutPercentage;
		// log.info("rollOutPercentage : {}", this.rollOutPercentage);
	}

	@Override
	public RepoResponseSearchApiDto searchWithoutMono(final SearchContext searchContext,
			final Map<String, String> paramMap, final CacheContext cacheContext) throws PanaromaException {
		throw new IllegalStateException("searchWithoutMono not supported for ES blocking repo");
	}

	@Override
	public RepoResponseSearchApiDto search(final SearchContext searchContext, final Map<String, String> paramMap,
			final CacheContext cacheContext) {
		return this.search(searchContext, paramMap);
	}

	@Override
	public RepoResponseSearchApiDto search(final SearchContext searchContext, final Map<String, String> paramMap) {

		try {
			// creating query to hit ES
			SearchSourceBuilder searchSourceBuilder = EsUtil.getSourceBuilder();
			searchSourceBuilder.size(searchContext.getPageSize() + 1); // get one more
																		// record than
																		// page Size to
																		// check if next
																		// page exists or
																		// not
			// QueryBuilder queryBuilder =
			// getQueryBuilderForDesiredParameters(searchContext);
			QueryBuilder queryBuilder = EsUtil.getQueryBuilderForDesiredParameters(new ArrayList<>(), searchContext,
					null, FILTER);
			searchSourceBuilder.query(queryBuilder);

			EsUtil.setSortParamInSearchRequest(searchSourceBuilder);
			searchSourceBuilder.fetchSource(null, "searchFields");

			if (searchContext.getTransactionDateEpoch() != null) {
				log.info(
						"Without Progressive Search, Search After called with parameters TransactionDateEpoch: {}, txnId :{}, StreamSource: {}",
						searchContext.getTransactionDateEpoch(), searchContext.getPaginationTxnId(),
						searchContext.getPaginationStreamSource());
				searchSourceBuilder.searchAfter(
						Arrays
							.asList(searchContext.getTransactionDateEpoch(), searchContext.getPaginationTxnId(),
									searchContext.getEntityId(), searchContext.getPaginationStreamSource())
							.toArray());
			}
			List<String> indexes = getIndexList(searchContext);
			SearchRequest searchRequest = new SearchRequest(indexes.toArray(new String[] {}), searchSourceBuilder);
			searchRequest.source(searchSourceBuilder);
			searchRequest.routing(searchContext.getEntityId());
			searchRequest.indicesOptions(IndicesOptions.LENIENT_EXPAND_OPEN);

			// Boolean usingManagedEs =
			// this.isWhiteListedForManagedEs(searchContext.getEntityId());

			log.info("Without Progressive Search, Trying getting listing data from ES, entityId: {}",
					searchContext.getEntityId());
			final long apiStartTime = Instant.now().toEpochMilli();
			final SearchResponse searchResponseMono = getSearchResponse(searchRequest, null);

			String tag = MonitoringConstants.ES_REST_CLIENT_SEARCH_API;
			String searchType = NOT_AVAILABLE;
			if (StringUtils.isNotBlank(searchContext.getSearchTypeForMetrics())) {
				searchType = searchContext.getSearchTypeForMetrics();
			}
			tag += COMMA + SEARCH_TYPE + COLON + searchType;

			if (StringUtils.isNotBlank(searchContext.getSearchAdditionalMetricParams())) {
				tag += COMMA + searchContext.getSearchAdditionalMetricParams();
			}
			tag += COMMA + PASSBOOK_FILTER + COLON + searchContext.isPassbookFilter() + COMMA + PAYMENT_SYSTEM + COLON
					+ searchContext.getEsPaymentSystem();
			long timeTaken = Instant.now().toEpochMilli() - apiStartTime;
			metricsAgent.recordApiExecutionTime(timeTaken, tag,
					ES_HOST_TYPE + COLON + GenericUtilityExtension.getEsHostType());

			metricsAgent.pushEsResponseTimeForFilter(paramMap, timeTaken);
			log.debug(
					"Without Progressive Search, Time taken by restHighLevelClient to process search with searchType : {} , request : {}",
					searchType, timeTaken);

			SearchHits hits = searchResponseMono.getHits();
			List<SearchHit> hitsList = Arrays.asList(hits.getHits());

			if (hitsList.isEmpty()) {
				log.error("Without Progressive Search, No transaction fetched for userId {} from {} to today",
						searchContext.getEntityId(), DateTimeUtility.getDateTime(searchContext.getFromDate()));
				throw ExceptionFactory.getCustomizedFromDateException(PANAROMA_SERVICE,
						NO_TRANSACTION_FOUND_FOR_LISTING, DateTimeUtility.getDateTimeForExp(
								configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class)));
			}

			// setting pagination params, i.e. txnDate and streamsource of last fetch
			// record from ES
			PaginationParams paginationParams = new PaginationParams();
			TransformedTransactionHistoryDetail lastFetchedRecord = null;
			if (hitsList.size() == searchContext.getPageSize() + 1) {
				// removing last record as this was fetched just to check whether next
				// page exists or not.
				// this record need not to be sent in response
				hitsList = hitsList.subList(0, hitsList.size() - 1);// taking sublist to
																	// prevent last
																	// element from
																	// setting in response

				lastFetchedRecord = resultMapper.mapSearchHit(hitsList.get(hitsList.size() - 1),
						TransformedTransactionHistoryDetail.class);
				paginationParams.setPaginationStreamSource(String.valueOf(lastFetchedRecord.getStreamSource()));
				paginationParams.setPaginationTxnId(lastFetchedRecord.getTxnId());
				paginationParams.setTransactionDateEpoch(String.valueOf(lastFetchedRecord.getTxnDate()));
				paginationParams.setPageNo(searchContext.getPageNo() + 1);
			}
			if (Objects.isNull(lastFetchedRecord)) {
				lastFetchedRecord = resultMapper.mapSearchHit(hitsList.get(hitsList.size() - 1),
						TransformedTransactionHistoryDetail.class);
			}
			metricsAgent.recordDateDiffWithCurrentDate(lastFetchedRecord.getTxnDate(), SEARCH_API, null);
			// creating response
			List<TransformedTransactionHistoryDetail> historyDetailsIterable = getHistoryDetailsList(hitsList);
			List<TransformedTransactionHistoryDetail> dedupedTxns = UtilityExtension
				.removeDuplicateDocuments(historyDetailsIterable);

			RepoResponseSearchApiDto response = new RepoResponseSearchApiDto(dedupedTxns, hits.getTotalHits(),
					paginationParams);
			log.debug("Without Progressive Search, returned Response from search API :{}", response);
			return response;
		}
		catch (Exception ex) {
			if (ex instanceof PanaromaException) {
				throw ((PanaromaException) ex);
			}
			else {
				throw ExceptionFactory.getException(PANAROMA_SERVICE, ES_EXCEPTION);
			}
		}

	}

	private SearchResponse search(final SearchRequest searchRequest, final IndexConfig indexConfig) throws IOException {
		RestHighLevelClient restHighLevelClient = this.getEsClient(indexConfig);
		return restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
	}
	// will remove below code after testing on beta if everything works fine
	/*
	 * private SearchResponse search(final SearchRequest searchRequest,final IndexConfig
	 * indexConfig) throws Exception { RestHighLevelClient restHighLevelClient =
	 * this.getEsClient(indexConfig);
	 *
	 * CompletableFuture<SearchResponse> completableFuture = new CompletableFuture<>();
	 * restHighLevelClient.searchAsync(searchRequest, RequestOptions.DEFAULT, new
	 * ActionListener<SearchResponse>() {
	 *
	 * @Override public void onResponse(final SearchResponse searchResponse) {
	 * completableFuture.complete(searchResponse); }
	 *
	 * @Override public void onFailure(final Exception e) {
	 * completableFuture.completeExceptionally(e); } });
	 *
	 * try { return completableFuture.get(); } catch (InterruptedException |
	 * ExecutionException e) { throw new Exception(e); } }
	 */

	// need to check
	@Override
	public List<TransformedTransactionHistoryDetail> getDetails(final String userId, final String txnId,
			final Integer transactionSource, final boolean isBankData, final String groupId,
			final ExtraDetailRequestFields extraDetailRequestFields) throws PanaromaException {
		List<TransformedTransactionHistoryDetail> response = new ArrayList<>();
		// currently using transaction Id to fetch records from DB.
		// might change it to groupId
		log.debug("Detail API: querying ES for entityId {}, groupId {}, stream source key {} ", userId, txnId,
				transactionSource);
		QueryBuilder queryBuilder = null;
		String queriedOverField = TRANSACTION_ID;
		if (StringUtils.isNotBlank(groupId)) {
			queryBuilder = QueryBuilders.boolQuery()
				.filter(QueryBuilders.termQuery(ENTITY_ID, userId))
				.filter(QueryBuilders.termQuery(GROUP_ID, groupId))
				.filter(QueryBuilders.termQuery(IS_VISIBLE, true));
			queriedOverField = GROUP_ID;
		}
		else {
			queryBuilder = QueryBuilders.boolQuery()
				.filter(QueryBuilders.termQuery(ENTITY_ID, userId))
				.filter(QueryBuilders.termQuery(TRANSACTION_ID, txnId))
				.filter(QueryBuilders.termQuery(IS_VISIBLE, true))
				.filter(QueryBuilders.termQuery(STREAM_SOURCE, transactionSource));
		}

		SearchSourceBuilder searchSourceBuilder = EsUtil.getSourceBuilder();
		// handling searchContext show bankData
		handleShowBankDataQuery(isBankData, (BoolQueryBuilder) queryBuilder);
		searchSourceBuilder.query(queryBuilder);
		String[] indices = null;
		Long txnDate = null;
		IndexConfig indexConfig = null;
		if (extraDetailRequestFields != null && StringUtils.isNotBlank(extraDetailRequestFields.getTxnDate())) {
			txnDate = Long.parseLong(extraDetailRequestFields.getTxnDate());
		}
		if (Objects.nonNull(txnDate)) {
			indices = IndexUtility.getIndexNames(txnDate, esIndexPreficForDetail, updatedIndexMonthList);
			IndexUtility.convertToRegexOfGivenIndices(indices);
			try {
				indexConfig = searchIndexRangeConfig.getIndexConfigUsingRange(txnDate, 0);
			}
			catch (Exception e) {
				log.warn("Exception while getting indexConfig Exception : {}", CommonsUtility.exceptionFormatter(e));
			}
		}
		SearchRequest searchRequest = null;
		if (indices == null || indices.length == 0) {
			// search on alias
			log.warn("Quering ES over Alias for txnId: {}", txnId);
			searchRequest = new SearchRequest(indexName);
		}
		else {
			// search on fetched index list
			searchRequest = new SearchRequest(indices);
		}
		searchSourceBuilder.fetchSource(null, "searchFields.search*");
		searchRequest.source(searchSourceBuilder);
		searchRequest.routing(userId);
		log.debug("Trying getting detail data from ES, entityId: {}", userId);

		List<TransformedTransactionHistoryDetail> detailList = performSearchAndMap(userId, txnId, transactionSource,
				searchRequest, 0, queriedOverField, indexConfig);
		detailList = UtilityExtension.removeDuplicateDocuments(detailList);

		// return performSearchAndMap(userId, txnId, transactionSource, searchRequest,0,
		// queriedOverField).flatMapMany(detailList -> {
		if (StringUtils.isNotBlank(groupId)) {
			return detailList;
		}

		TransformedTransactionHistoryDetail detail = detailList.get(0);
		List<TransformedTransactionHistoryDetail> tthdList = new ArrayList<>();
		List<String> txnIdList = new ArrayList<>();
		// if POJO has groupId set then we need to fetch all other records with that
		// groupId as response will be set as per POJO
		if (detail.getGroupId() != null) {
			SearchRequest groupSearchRequest = new SearchRequest(indexName);
			QueryBuilder queryBuilder1 = createQueryBuilder(userId, detail.getGroupId(), detail.getMainTxnType(),
					groupSearchRequest);
			searchSourceBuilder.query(queryBuilder1);
			groupSearchRequest.source(searchSourceBuilder);
			SearchResponse searchResponse = getSearchResponse(groupSearchRequest, indexConfig);
			// return getSearchResponse(groupSearchRequest).flatMapMany(searchResponse ->
			// {

			AtomicBoolean isListingTxnFound = new AtomicBoolean(false);
			searchResponse.getHits().forEach(hit -> {
				TransformedTransactionHistoryDetail groupedTxn = resultMapper.mapSearchHit(hit,
						TransformedTransactionHistoryDetail.class);
				if (groupedTxn != null && detail.getStreamSource().equals(groupedTxn.getStreamSource())
						&& detail.getTxnId().equalsIgnoreCase(groupedTxn.getTxnId())) {
					isListingTxnFound.set(true);
				}
				tthdList.add(groupedTxn);
				txnIdList.add(groupedTxn.getTxnId());
			});
			if (!isListingTxnFound.get()) {
				log.info("Listing Transaction not found in detail search for txnId : {}", detail.getTxnId());
				tthdList.add(detail);
				txnIdList.add(detail.getTxnId());
			}
		}
		else {
			// try regrouping ......
			GroupingUtility.sendDataToKafkaForRegrouping(detail);
			tthdList.add(detail);
			txnIdList.add(detail.getTxnId());
		}

		List<TransformedTransactionHistoryDetail> dedupedTthdList = UtilityExtension.removeDuplicateDocuments(tthdList);
		log.info("Response from ES for detail API with list of txnIds : {}", tthdList);
		return dedupedTthdList;
	}

	private void handleShowBankDataQuery(final boolean showBankData, final BoolQueryBuilder queryBuilder) {
		// search context show bank data handling separately
		SearchCriteriaEnum searchCriteriaEnum = SearchCriteriaEnum.showBankData;
		String esMapping = SearchContextToESMappings.get(searchCriteriaEnum.toString());
		if (!showBankData) {
			BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
			boolQueryBuilder.should(QueryBuilders.termQuery(esMapping, false));
			BoolQueryBuilder mustNotBoolQueryBuilder = QueryBuilders.boolQuery();
			boolQueryBuilder.should(mustNotBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(esMapping)));
			boolQueryBuilder.minimumShouldMatch(1);
			queryBuilder.filter(boolQueryBuilder);
		}
	}

	// need to check
	private List<TransformedTransactionHistoryDetail> performSearchAndMap(final String userId, final String txnId,
			final Integer transactionSource, final SearchRequest searchRequest, final int retryCount,
			final String queriedOverField, final IndexConfig indexConfig) {
		long apiStartTime = Instant.now().toEpochMilli();
		SearchResponse searchResponse = getSearchResponse(searchRequest, indexConfig);
		try {
			long timeTaken = Instant.now().toEpochMilli() - apiStartTime;
			metricsAgent.recordApiExecutionTime(timeTaken, MonitoringConstants.ES_REST_CLIENT_DETAIL_API,
					RETRY_COUNT + retryCount, "totalRespSizeFromEs:" + searchResponse.getHits().totalHits,
					queriedOverField, ES_HOST_TYPE + COLON + GenericUtilityExtension.getEsHostType());
			log.debug(
					"Time taken by restHighLevelClient  to process details request : {} , " + "quering over field : {}",
					timeTaken, queriedOverField);
			log.debug("Response received from ES for userId:{}, txnId:{} with retryCount :{} , "
					+ "quering over field : {}", userId, txnId, retryCount, queriedOverField);

			if (searchResponse.getHits().totalHits == 0) {
				log.info(
						"Zero record fetched for set parameters userId {}, and transaction Id {} and stream source {}, "
								+ "quering over field : {}",
						userId, txnId, transactionSource, queriedOverField);
				throw ExceptionFactory.getException(PANAROMA_SERVICE, DETAIL_COULD_NOT_BE_LOADED);
			}

			List<TransformedTransactionHistoryDetail> tthdList = new ArrayList<>();
			AtomicBoolean isListingTxnFound = new AtomicBoolean(false);
			searchResponse.getHits().forEach(hit -> {
				TransformedTransactionHistoryDetail groupedTxn = resultMapper.mapSearchHit(hit,
						TransformedTransactionHistoryDetail.class);
				if (groupedTxn != null && transactionSource.equals(groupedTxn.getStreamSource())
						&& txnId.equalsIgnoreCase(groupedTxn.getTxnId())) {
					isListingTxnFound.set(true);
				}
				tthdList.add(groupedTxn);
			});
			// the below section states that es response does not have txn for which
			// detail is requested, so retry ES call
			if (!isListingTxnFound.get()) {
				log.info("Listing Transaction not found in detail search for txnId : {} , " + "quering over field : {}",
						txnId, queriedOverField);
				throw ExceptionFactory.getException(PANAROMA_SERVICE, DETAIL_COULD_NOT_BE_LOADED);
			}
			return tthdList;
		}
		catch (Exception e) {
			if (e instanceof PanaromaException
					&& DETAIL_COULD_NOT_BE_LOADED.equals(((PanaromaException) e).getResponseCode())
					&& retryCount < retriableCount) {
				try {
					Thread.sleep(retringWaitTime);
				}
				catch (InterruptedException ex) {
					log.error("Thread couldn't sleep for timePeriod: {} because of exception : {}", retringWaitTime,
							CommonsUtility.exceptionFormatter(ex));
				}
				return performSearchAndMap(userId, txnId, transactionSource, searchRequest, retryCount + 1,
						queriedOverField, indexConfig);
			}
			else {
				log.error(
						"Exception wile fetching fetching listing txn from ES, quering over field : {} "
								+ "with userId:{}, txnId:{} with retryCount :{} is = {}",
						queriedOverField, userId, txnId, retryCount, CommonsUtility.exceptionFormatter((Exception) e));
				if (e instanceof NullPointerException) {
					throw ExceptionFactory.getException(PANAROMA_SERVICE, INTERNAL_SERVER_ERROR);
				}
				else if (e instanceof PanaromaException
						&& (DETAIL_COULD_NOT_BE_LOADED.equals(((PanaromaException) e).getResponseCode()))) {
					throw ExceptionFactory.getException(PANAROMA_SERVICE, DETAIL_COULD_NOT_BE_LOADED);
				}
				throw ExceptionFactory.getException(PANAROMA_SERVICE, ES_EXCEPTION);
			}
		}
	}

	private QueryBuilder createQueryBuilder(final String userId, final String groupId, final Integer txnType,
			final SearchRequest groupSearchRequest) {
		if (TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey().equals(txnType)
				|| TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD.getTransactionTypeKey().equals(txnType)) {
			QueryBuilder queryBuilder = QueryBuilders.boolQuery().filter(QueryBuilders.termQuery(GROUP_ID, groupId));
			return queryBuilder;
		}
		else {
			QueryBuilder queryBuilder = QueryBuilders.boolQuery()
				.filter(QueryBuilders.termQuery(ENTITY_ID, userId))
				.filter(QueryBuilders.termQuery(GROUP_ID, groupId));
			groupSearchRequest.routing(userId);
			return queryBuilder;
		}
	}

	private SearchResponse getSearchResponse(final SearchRequest searchRequest, final IndexConfig indexConfig)
			throws PanaromaException {
		SearchResponse searchResponse;
		try {
			searchResponse = this.search(searchRequest, indexConfig);
			log.info("Response received from ES for detail : {}", searchResponse);
		}
		catch (Exception e) {
			log.error("Error retrieving response from ES {}", CommonsUtility.exceptionFormatter(e));
			throw ExceptionFactory.getException(PANAROMA_SERVICE, ES_EXCEPTION);
		}
		return searchResponse;
	}

	// will remove below code after testing on beta if everything works fine
	/*
	 * private SearchResponse search(final SearchRequest searchRequest, final IndexConfig
	 * indexConfig) throws Exception { RestHighLevelClient restHighLevelClient =
	 * this.getEsClient(indexConfig);
	 *
	 * CompletableFuture<SearchResponse> completableFuture = new CompletableFuture<>();
	 * restHighLevelClient.searchAsync(searchRequest, RequestOptions.DEFAULT, new
	 * ActionListener<SearchResponse>() {
	 *
	 * @Override public void onResponse(final SearchResponse searchResponse) {
	 * completableFuture.complete(searchResponse); }
	 *
	 * @Override public void onFailure(final Exception e) {
	 * completableFuture.completeExceptionally(e); } });
	 *
	 * try { return completableFuture.get(); } catch (InterruptedException |
	 * ExecutionException e) { throw new Exception(e); } }
	 */

	List<String> getIndexList(final SearchContext searchContext) {
		long fromDate = searchContext.getFromDate();
		long toDate = searchContext.getToDate();
		if (StringUtils.isEmpty(searchContext.getEsDateRange())) {
			return getIndexListInRange(indexPrefix, fromDate, toDate);
		}
		String[] dateRanges = searchContext.getEsDateRange().split(",");
		Set<String> indices = new HashSet<>(); // to remove repeated indices if any
		long filterFromDate;
		long filterToDate;
		for (String dateRange : dateRanges) {
			String[] dates = dateRange.split("-");
			if (dates.length == 2) {
				try {
					filterFromDate = Math.max(fromDate, Long.parseLong(dates[0]));
					filterToDate = Math.min(toDate, Long.parseLong(dates[1]));
					indices.addAll(getIndexListInRange(indexPrefix, filterFromDate, filterToDate));
				}
				catch (NumberFormatException e) {
					log.error("Non numeric epoch values passed in date. Ignoring filter in index selection");
				}
			}
			else {
				log.error("Date format is incorrectly passed. Expected <fromDate>-<toDate>. Ignoring filter.");
			}
		}
		return indices.stream().toList();
	}

	protected RestHighLevelClient getEsClient(final IndexConfig indexConfig) {
		if (Objects.nonNull(indexConfig)) {
			return elasticSearchIndexRangeConfig.getRestHighLevelClient(indexConfig);
		}
		return managedV2RestHighLevelClient;
	}

	protected List<TransformedTransactionHistoryDetail> getHistoryDetailsList(final List<SearchHit> searchHits) {
		return searchHits.stream()
			.map(hit -> resultMapper.mapSearchHit(hit, TransformedTransactionHistoryDetail.class))
			.filter(Objects::nonNull)
			.collect(Collectors.toCollection(LinkedList::new));
	}

	@Override
	public Iterable<Map<String, List<String>>> getAutoCompleteSuggestionsFromEs(final SearchContext searchContext,
			final String reqId, final Map<String, String> paramMap) {
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		searchSourceBuilder.size(searchContext.getPageSize());
		QueryBuilder queryBuilder = EsUtil.getQueryBuilderForDesiredParameters(new ArrayList<>(), searchContext, reqId,
				FILTER);
		searchSourceBuilder.query(queryBuilder);
		String[] searchableFields = SearchIntelligentUtility.getFieldsForQuery(searchContext.getAutoCompleteQuery());
		HighlightBuilder highlightBuilder = EsUtil.getEsQueryHighLighter(searchableFields, ES_HIGHLIGHT_TYPE_PLAIN,
				POST_TAGS_ARRAY, PRE_TAGS_ARRAY);
		searchSourceBuilder.highlighter(highlightBuilder);
		searchSourceBuilder.fetchSource(false);
		SearchRequest searchRequest = new SearchRequest(getIndexList(searchContext).toArray(new String[] {}));
		searchRequest.source(searchSourceBuilder);
		searchRequest.routing(searchContext.getEntityId());
		searchRequest.requestCache(false);
		long apiStartTime = Instant.now().toEpochMilli();
		try {
			final SearchResponse searchResponseMono = getSearchResponse(searchRequest, null);
			long apiEndTime = Instant.now().toEpochMilli();
			StringBuilder tag = new StringBuilder();
			long timeTaken = apiEndTime - apiStartTime;
			String[] tags = TransactionHistoryMonitoringUtility.getMetricsTagsForFilters(paramMap);
			if (null != tags) {
				String tagStr = String.join(",", tags);
				tag = tag.append(tagStr).append(COMMA);
			}
			tag.append(SERVICE_ELASTIC_SEARCH)
				.append(COLON)
				.append(ES_REST_CLIENT_AUTO_COMPLETE_API_SERVICE)
				.append(COMMA)
				.append(ES_HOST_TYPE)
				.append(COLON)
				.append(GenericUtilityExtension.getEsHostType());
			if (timeTaken > maxTimeToPrintAutoCompleteLogger) {
				log.info(
						"Time Taken : {} by ES query to fetch response for UserId : {} , and query : {} , "
								+ "TookTime greater than : {}",
						timeTaken, searchContext.getEntityId(), searchContext.getAutoCompleteQuery(),
						maxTimeToPrintAutoCompleteLogger);
				tag.append(COMMA).append(LIMIT_BREACHED).append(COLON).append(AUTO_COMPLETE_LIMIT_BREACHED);
				tag.append(COMMA).append(LIMIT_TIME).append(COLON).append(maxTimeToPrintAutoCompleteLogger);
				metricsAgent.incrementCount(AUTO_COMPLETE_LIMIT_BREACHED_COUNT, LIMIT_BREACHED);
			}
			metricsAgent.recordApiExecutionTime(tag.toString(), timeTaken);
			log.debug(
					"Time taken by restHighLevelClient to process autoComplete for UserId : {} , and query : {}"
							+ " request is : {}",
					searchContext.getEntityId(), searchContext.getAutoCompleteQuery(), timeTaken);
			List<SearchHit> hits = Arrays.asList(searchResponseMono.getHits().getHits());
			if (hits.size() == 0) {
				log.error("No Suggestion found for userId : {} , with query : {} , from : {} , upto Today",
						searchContext.getEntityId(), searchContext.getAutoCompleteQuery(), searchContext.getFromDate());
				throw ExceptionFactory.getCustomizedFromDateException(PANAROMA_SERVICE,
						NO_TRANSACTION_FOUND_FOR_LISTING, DateTimeUtility.getDateTimeForExp(
								configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class)));
			}
			Iterable<Map<String, List<String>>> iterable = getSuggestionsIterable(hits);
			return iterable;

		}
		catch (Exception e) {
			log.error(
					"Exception while converting es response to SearchResponse for UserId : {} , "
							+ "and query : {} , with exception : {}",
					searchContext.getEntityId(), searchContext.getAutoCompleteQuery(),
					CommonsUtility.exceptionFormatter((Exception) e));
			PanaromaException ex = ExceptionHandlerUtil.getTaggedException((Exception) e, PANAROMA_SERVICE,
					TRANSFORMATION_ERROR, GET_AUTO_COMPLETE_SUGGESTIONS_FROM_ES_FUNC);
			throw ex;
		}
	}

	protected Iterable<Map<String, List<String>>> getSuggestionsIterable(final List<SearchHit> searchHits) {

		return () -> new Iterator() {
			Iterator<SearchHit> searchHitIterator = searchHits.iterator();

			@Override
			public boolean hasNext() {
				return searchHitIterator.hasNext();
			}

			@Override
			public Map<String, List<String>> next() {
				try {
					return objectMapper
						.readValue(searchHitIterator.next().toString(), AutoCompleteEsHighlightedResp.class)
						.getHighlight();
				}
				catch (Exception e) {
					throw ExceptionFactory.getException(PANAROMA_SERVICE, TRANSFORMATION_ERROR);
				}
			}
		};
	}

}
