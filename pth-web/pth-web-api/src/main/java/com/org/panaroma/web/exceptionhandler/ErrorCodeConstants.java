package com.org.panaroma.web.exceptionhandler;

public class ErrorCodeConstants {

	public static final String MAPPER_EXCEPTION = "4001";

	public static final String ES_EXCEPTION = "4002";

	public static final String INTERNAL_SERVER_ERROR = "4003";

	public static final String INVALID_PARAMETER = "4004";

	public static final String MISSING_PARAMETER = "4005";

	public static final String INVALID_PARAM_SEARCH_AFTER = "4006";

	public static final String QUERY_EXCEPTION = "4007";

	public static final String INVALID_EPOCH = "4008";

	public static final String TRANSFORMATION_ERROR = "4009";

	public static final String NO_TRANSACTION_FOUND_FOR_LISTING = "4010";

	public static final String ITERATION_EXCEPTION = "4011";

	public static final String AUTHORIZATION_ERROR = "4012";

	public static final String DETAIL_COULD_NOT_BE_LOADED = "4013";

	public static final String OAUTH_ERROR_GENERIC = "4014";

	public static final String KNOWN_ISSUE_ERROR = "4015";

	public static String CLIENT_ID_MISSING = "4016";

	public static String REQUEST_TOKEN_MISSING = "4017";

	public static String ENTITY_ID_MISSING = "4018";

	public static String INVALID_JWT_TOKEN = "4019";

	public static String RATE_LIMITER_ERROR = "4020";

	public static String TOKEN_RATE_LIMITER_ERROR = "4029";

	public static String TOKEN_DAILY_RATE_LIMITER_ERROR = "4030";

	public static final String AUTO_COMPLETE_TRANSFORMATION_ERROR = "4021";

	public static final String AUTO_COMPLETE_DISABLED = "4022";

	public static final String VALIDATION_ERROR = "4023";

	public static final String CUSTOM_RETRY = "4024";

	public static final String TRAFFIC_DIVERSION_CUSTOM_ERROR = "4035";

	public static final String INVALIDATED_BY_LAG_CACHE = "4033";

	public static final String NOT_SEARCHING_BEYOND_SOME_CONFIGURED_DATE = "4034";

	public static final String NOT_SEARCHING_BEYOND_VIRTUAL_FROM_DATE = "4036";

	public static final String NO_TXN_FOUND_FOR_LISTING_WHEN_VIRTUAL_FROM_DATE_FLAG_IS_ENABLED = "4037";

	/*
	 * Even though 4036 & 4037 error codes exist in the system, we are introducing 4038 in
	 * PTH-1263 because after the changes in this jira i.e. limiting the scan to max n
	 * months in single listing request, we will not be able to differentiate between
	 * whether any txns are visible to the user on his/her screen at the time we are
	 * sending this error. If txns are visible we show 4036 else we show 4037. But after
	 * PTH-1263, for new versions i.e. pthVersion >= 5.0 only 4038 will be thrown if
	 * virtual from date feature is turned on. Just to add on, if feature that is made
	 * live in PTH-1263 is turned off, 4036 & 4037 will be thrown instead of 4038
	 */
	public static final String RETURNING_AFTER_SCANNING_TILL_VIRTUAL_FROM_DATE = "4038";

	public static final String FAILURE = "failure";

	public static final String SUCCESS = "success";

}
