package com.org.panaroma.web.repo;

import static com.org.panaroma.commons.config.DateRange.DATE_FORMAT;
import static com.org.panaroma.commons.constants.ConfigPropertiesEnum.VIRTUAL_FROM_DATE_SPECIFIC_ERROR_CODES_DISABLED;
import static com.org.panaroma.commons.constants.WebConstants.EPOCH_BEGINNING_OF_MONTH_TILL_WHICH_MAX_SCAN_TO_BE_DONE_PARAM_MAP_KEY;
import static com.org.panaroma.commons.constants.WebConstants.FILTER;
import static com.org.panaroma.commons.constants.WebConstants.FROM_DATE_LISTING_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.MAX_PAGE_SIZE_FUNCTIONALITY_ENABLE;
import static com.org.panaroma.commons.constants.WebConstants.MAX_PAGE_SIZE_OF_RANGE;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PARAM_MAP_KEY_CONTAINING_FLAG_THAT_COMPLETE_DATA_SCAN_IS_DONE;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.SELF_TRANSFER_KEYWORD;
import static com.org.panaroma.commons.constants.WebConstants.SearchContextToESMappings;
import static com.org.panaroma.commons.constants.WebConstants.THREAD_CONTEXT_KEY_CONTAINING_EPOCH_TILL_WHICH_SCAN_HAS_BEEN_DONE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_CATEGORY;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.ES_EXCEPTION;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NO_TRANSACTION_FOUND_FOR_LISTING;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NO_TXN_FOUND_FOR_LISTING_WHEN_VIRTUAL_FROM_DATE_FLAG_IS_ENABLED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COMMA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.HOST;
import static com.org.panaroma.web.monitoring.MonitoringConstants.QUERIED_OVER_ALIAS_IN_LISTING;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RANGE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.REQUEST_ID;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SEARCH_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API;

import static com.org.panaroma.commons.utils.DateTimeUtility.getTimeFromStringFormattedDate;
import com.org.panaroma.commons.config.DateRange;
import com.org.panaroma.commons.config.ElasticSearchIndexRangeConfig;
import com.org.panaroma.commons.config.IndexConfig;
import com.org.panaroma.commons.config.IndexConfigWrapper;
import com.org.panaroma.commons.dto.TxnCategoryEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.NumberFormatUtility;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.cache.CacheContext;
import com.org.panaroma.web.dto.ProgressiveSearchHitsResult;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import com.org.panaroma.web.utility.CacheUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.TransactionHistoryMonitoringUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.io.IOException;
import java.text.ParseException;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.org.panaroma.commons.utils.MaskUtility;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.web.utility.ResultsMapperExtended;
import com.org.panaroma.web.utility.VirtualDateHelper;

import lombok.extern.log4j.Log4j2;
import reactor.core.publisher.Mono;

@Log4j2
@Repository
@Qualifier("EsProgressiveSearchingHistoryRepo")
public class EsProgressiveRecordsSearchingRepo extends EsBlockingTransactionHistoryRepo {

	private ResultsMapperExtended resultMapper;

	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	private CacheUtility cacheUtility;

	private String esIndexAlias;

	@Autowired
	public EsProgressiveRecordsSearchingRepo(final ResultsMapperExtended resultsMapper,
			final ElasticSearchIndexRangeConfig elasticSearchIndexRangeConfig,
			// @Qualifier("oldRestEsClient") final RestHighLevelClient
			// restHighLevelClient,
			// @Qualifier("managedEsClient") final RestHighLevelClient
			// managedRestHighLevelClient,
			@Qualifier("managedV2EsClient") final RestHighLevelClient managedV2RestHighLevelClient,
			// @Value("#{'${whitelisted.users.list.for.managed.es}'.split(',')}")
			// final List<String> whiteListedUsersForManagedEs,
			// @Value("${managed.roll.out.percentage}") final Double rollOutPercentage,
			final ConfigurablePropertiesHolder configurablePropertiesHolder,
			@Value("${elastic-search-index}") final String esIndexAlias, final CacheUtility cacheUtility) {
		this.resultMapper = resultsMapper;
		// this.restHighLevelClient = restHighLevelClient;
		// this.managedRestHighLevelClient = managedRestHighLevelClient;
		this.managedV2RestHighLevelClient = managedV2RestHighLevelClient;
		// this.whiteListedUsersForManagedEs = whiteListedUsersForManagedEs;
		// this.rollOutPercentage = rollOutPercentage;
		// log.info("rollOutPercentage : {}", this.rollOutPercentage);
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.cacheUtility = cacheUtility;
		this.esIndexAlias = esIndexAlias;
	}

	@Override
	public Mono<RepoResponseSearchApiDto> search(final SearchContext searchContext, final Map<String, String> paramMap,
			final CacheContext cacheContext) throws PanaromaException {
		return Mono.just(searchWithoutMono(searchContext, paramMap, cacheContext));
	}

	@Override
	public RepoResponseSearchApiDto searchWithoutMono(final SearchContext searchContext,
			final Map<String, String> paramMap, final CacheContext cacheContext) throws PanaromaException {
		ProgressiveSearchHitsResult progressiveSearchHitsResult;

		long apiSearchStartTime = Instant.now().toEpochMilli();
		// now search the required number of records in iterative fashion
		progressiveSearchHitsResult = fetchRecords(searchContext, searchContext.getPageSize(), paramMap);

		long timeTaken = Instant.now().toEpochMilli() - apiSearchStartTime;

		log.info(
				"Time taken by fetchRecords to process search request : {}, "
						+ "with no of ES calls: {} pageNo: {} with entityId : {} and reqId : {}",
				timeTaken, progressiveSearchHitsResult.getNoOfEsCalls(), searchContext.getPageNo(),
				searchContext.getEntityId(), paramMap.get(REQUEST_ID));

		// converting ES JSON response to TTHD list.
		List<TransformedTransactionHistoryDetail> txns = getHistoryDetailsList(
				progressiveSearchHitsResult.getHitsList());

		// setting pagination data
		PaginationParams paginationParams = setPaginationData(txns, searchContext);

		// trimming the extra txn fetched to check for further available data.
		if (txns.size() > searchContext.getPageSize()) {
			txns = txns.subList(0, Math.min(txns.size(), searchContext.getPageSize()));
		}

		List<TransformedTransactionHistoryDetail> dedupedTxns = UtilityExtension.removeDuplicateDocuments(txns);

		// creating response
		final RepoResponseSearchApiDto response = new RepoResponseSearchApiDto(dedupedTxns,
				progressiveSearchHitsResult.getTotalHits(), paginationParams);

		// metrics

		String searchAndFiltersTags = TransactionHistoryMonitoringUtility
			.getSearchTypeTagFromSearchContext(searchContext);

		String filterTags = TransactionHistoryMonitoringUtility.getJoinedMetricsTagsForFilters(paramMap);

		if (StringUtils.isNotBlank(filterTags)) {
			searchAndFiltersTags = searchAndFiltersTags + COMMA + filterTags;
		}

		String api = SEARCH_API;
		if (searchContext.isForUpdates()) {
			api = UPDATES_API;
		}
		metricsAgent.recordNumberOfEsCalls(progressiveSearchHitsResult.getNoOfEsCalls(), api, searchContext.getPageNo(),
				searchAndFiltersTags);

		metricsAgent.pushEsResponseTimeForFilter(paramMap, timeTaken);

		if (progressiveSearchHitsResult.getHitsList().isEmpty() && !searchContext.isForUpdates()) {
			log.error("No transaction fetched for userId {} from {} to today", searchContext.getEntityId(),
					DateTimeUtility.getDateTime(searchContext.getFromDate()));
			if (VirtualDateHelper.isVirtualFromDateUsed(paramMap)) {
				// In this case VFD is our new fromDate so we don't need to push this data
				// to NTU cache We will throw custom exception from here only
				if (Boolean.parseBoolean(configurablePropertiesHolder
					.getProperty(VIRTUAL_FROM_DATE_SPECIFIC_ERROR_CODES_DISABLED, String.class))) {
					throw ExceptionFactory.getCustomizedFromDateException(PANAROMA_SERVICE,
							NO_TRANSACTION_FOUND_FOR_LISTING, DateTimeUtility
								.getDateTimeForExp(DateTimeUtility.getDateTime(searchContext.getFromDate())));
				}
				else {
					throw ExceptionFactory.getCustomizedException(PANAROMA_SERVICE,
							NO_TXN_FOUND_FOR_LISTING_WHEN_VIRTUAL_FROM_DATE_FLAG_IS_ENABLED,
							VirtualDateHelper.getVirtualFromDateListingFilterInDays());
				}
			}
			cacheUtility.saveDataForNonTransactingUserInCacheIfRequired(searchContext, cacheContext);
			throw ExceptionFactory.getCustomizedFromDateException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING,
					DateTimeUtility.getDateTimeForExp(
							configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class)));
		}

		if (txns.size() > 1) {
			TransformedTransactionHistoryDetail lastFetchedRecord = txns.get(txns.size() - 1);
			if (lastFetchedRecord != null) {
				metricsAgent.recordDateDiffWithCurrentDate(lastFetchedRecord.getTxnDate(), api, searchAndFiltersTags);
			}
		}

		log.info("returned Response from search API :{}", response);
		return response;
	}

	private ProgressiveSearchHitsResult fetchRecords(final SearchContext searchContext, final int pageSize,
			final Map<String, String> paramMap) {
		ProgressiveSearchHitsResult progressiveSearchHitsResult = new ProgressiveSearchHitsResult();
		try {
			long newStartTime;
			modifyFromDateForDateFilter(searchContext);
			final long initialFromDate = searchContext.getFromDate();
			SearchContext searchContextNow = new SearchContext();
			BeanUtils.copyProperties(searchContext, searchContextNow);
			int noOfRecordsLeftNow = pageSize;

			boolean maxPageSizeFunctionalityEnable = enableMaxPageSizeFunctionality(searchContext);

			IndexConfigWrapper indexConfigWrapper = new IndexConfigWrapper();

			int searchRequestPageSize = pageSize;

			// check if max page size of a index functionality is enable or not
			if (Boolean.TRUE.equals(maxPageSizeFunctionalityEnable)) {
				// Passing max page limit here instead of noOfRecordLeft for fetching as
				// much txns possible from this Range
				searchRequestPageSize = configurablePropertiesHolder.getProperty(MAX_PAGE_SIZE_OF_RANGE, Integer.class);
				;
			}

			SearchRequest searchRequest = createSearchRequest(searchContextNow, searchRequestPageSize, null,
					indexConfigWrapper, initialFromDate, paramMap, progressiveSearchHitsResult.getNoOfEsCalls());

			if (searchRequest == null) {
				return progressiveSearchHitsResult;
			}
			log.info("Trying getting listing data from ES, entityId: {}, reqId : {}", searchContextNow.getEntityId(),
					paramMap.get(REQUEST_ID));

			while (searchRequest.indices().length != 0) {
				SearchResponse searchHits = getSearchHits(searchRequest, indexConfigWrapper.getIndexConfig(),
						searchContext, paramMap);
				progressiveSearchHitsResult.setNoOfEsCalls(progressiveSearchHitsResult.getNoOfEsCalls() + 1);
				SearchHits hits = searchHits.getHits();
				progressiveSearchHitsResult
					.setTotalHits(progressiveSearchHitsResult.getTotalHits() + hits.getTotalHits());
				List<SearchHit> currentHitsList = Arrays.asList(hits.getHits());
				List<SearchHit> hitsList = progressiveSearchHitsResult.getHitsList();

				// terminating progressive search if fetched txns are greater than left
				// now txns for this page
				if (currentHitsList.size() >= noOfRecordsLeftNow + 1) {
					// taking sublist to prevent last element from setting in response
					hitsList.addAll(currentHitsList);
					progressiveSearchHitsResult.setHitsList(hitsList);
					break;
				}
				else {
					// when the no. of records present in these indexes are less than
					// required no. of records.
					noOfRecordsLeftNow = noOfRecordsLeftNow - currentHitsList.size();
					hitsList.addAll(currentHitsList);
					progressiveSearchHitsResult.setHitsList(hitsList);
					if (initialFromDate == searchContextNow.getFromDate()
							|| searchContext.getFromDate() >= searchContextNow.getToDate()) {
						if (initialFromDate == searchContextNow.getFromDate()) {
							log.debug(">>>>>Returning because initialFromDate == searchContextNow.getFromDate() {}",
									searchContextNow.getFromDate());
						}
						else {
							log.debug(
									">>>>>>>Returning because searchContext.fromDate {} >= searchContextNow.toDate {}",
									searchContext.getFromDate(), searchContextNow.getToDate());
						}
						// search has already been done for given period.
						return progressiveSearchHitsResult;
					}
					newStartTime = getNewStartTime(searchContextNow.getToDate());
					searchContextNow.setToDate(newStartTime);
					searchContextNow.setPageSize(noOfRecordsLeftNow);
					// searchContextNow = createCustomSearchContext(noOfRecordsLeftNow,
					// null);

					searchRequestPageSize = noOfRecordsLeftNow;

					// check if max page size of a index functionality is enable or not
					if (Boolean.TRUE.equals(maxPageSizeFunctionalityEnable)) {
						// Passing max page limit here instead of noOfRecordLeft for
						// fetching as much txns possible from this Range
						searchRequestPageSize = configurablePropertiesHolder.getProperty(MAX_PAGE_SIZE_OF_RANGE,
								Integer.class);
						;
					}
					// Passing max page limit here instead of noOfRecordLeft for fetching
					// as much txns possible from this Range
					searchRequest = createSearchRequest(searchContextNow, searchRequestPageSize, newStartTime,
							indexConfigWrapper, initialFromDate, paramMap,
							progressiveSearchHitsResult.getNoOfEsCalls());

					if (searchRequest == null) {
						return progressiveSearchHitsResult;
					}
				}
			}
		}
		catch (Exception e) {
			log.error("Error in fetching records from ES in fetchRecords {}", CommonsUtility.exceptionFormatter(e));
			throw ExceptionFactory.getException(PANAROMA_SERVICE, ES_EXCEPTION);
		}
		return progressiveSearchHitsResult;
	}

	public void modifyFromDateForDateFilter(final SearchContext searchContext) {
		if (searchContext.getEsDateRange() == null) {
			return;
		}
		long actualFromDate = searchContext.getFromDate();
		long filterFromDate = Long.parseLong(searchContext.getEsDateRange().split("-")[0]);
		searchContext.setFromDate(Math.max(actualFromDate, filterFromDate));
		long filterToDate = Long.parseLong(searchContext.getEsDateRange().split("-")[1]);
		searchContext.setToDate(Math.min(searchContext.getToDate(), filterToDate));
	}

	public boolean enableMaxPageSizeFunctionality(final SearchContext searchContext) {
		if (searchContext.isForUpdates() && !searchContext.isInvalidForUpdates()) {
			return false;
		}
		return configurablePropertiesHolder.getProperty(MAX_PAGE_SIZE_FUNCTIONALITY_ENABLE, Boolean.class);
	}

	public long getNewStartTime(final long startTime) {
		// return 1 ms less from current index start time so that new search is for next
		// range of indexes
		return startTime - 1;
	}

	public SearchResponse getSearchHits(final SearchRequest searchRequest, final IndexConfig indexConfig,
			final SearchContext searchContext, final Map<String, String> paramMap) {
		long apiStartTime = Instant.now().toEpochMilli();
		final SearchResponse searchResponse = getSearchResponse(searchRequest, indexConfig);
		long timeTaken = Instant.now().toEpochMilli() - apiStartTime;
		log.info("Response received from ES for searchRequest : {} is : {} & time taken is : {}", searchRequest,
				MaskUtility.maskSearchResponse(searchResponse), timeTaken);

		String searchAndFiltersTags = TransactionHistoryMonitoringUtility
			.getSearchTypeTagFromSearchContext(searchContext);
		// Passbook tags already added in filters tags
		String filterTags = TransactionHistoryMonitoringUtility.getJoinedMetricsTagsForFilters(paramMap);

		if (StringUtils.isNotBlank(filterTags)) {
			searchAndFiltersTags = searchAndFiltersTags + COMMA + filterTags;
		}

		String executionTimeTag = MonitoringConstants.ES_REST_CLIENT_SEARCH_API;

		metricsAgent.recordApiExecutionTimeRangeWise(executionTimeTag, timeTaken,
				RANGE + indexConfig.getRange().getEnd(), searchAndFiltersTags,
				HOST + COLON + GenericUtilityExtension.getEsHostType());

		String apiCountTag = RANGE + indexConfig.getRange().getEnd();

		metricsAgent.incrementApiHitCountRangeWise(apiCountTag, SEARCH_API, searchAndFiltersTags);
		log.debug(
				"Time taken by restHighLevelClient to process search request : {} indexConfigRange {} , with reqId : {}",
				timeTaken, indexConfig.getRange().getEnd(), paramMap.get(REQUEST_ID));
		return searchResponse;
	}

	private SearchResponse getSearchResponse(final SearchRequest searchRequest, final IndexConfig indexConfig) {
		SearchResponse searchResponse;
		try {
			searchResponse = this.searchInternal(searchRequest, indexConfig);
		}
		catch (Exception e) {
			log.error("Error retrieving response from ES : {}", e.getMessage());
			throw ExceptionFactory.getException(PANAROMA_SERVICE, ES_EXCEPTION);
		}
		return searchResponse;
	}

	private SearchResponse searchInternal(final SearchRequest searchRequest, final IndexConfig indexConfig)
			throws IOException {
		RestHighLevelClient restHighLevelClient = elasticSearchIndexRangeConfig.getRestHighLevelClient(indexConfig);
		return restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
	}

	public SearchRequest createSearchRequest(final SearchContext searchContext, final int noOfRecordsLeft,
			final Long startTime, final IndexConfigWrapper indexConfigWrapper, final long initialFromDate,
			final Map<String, String> paramMap, final int numberOfEsCall) throws ParseException {
		SearchRequest searchRequest = null;
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		long startTimeNow;
		if (startTime == null) {
			startTimeNow = searchContext.getToDate();
			if (searchContext.getTransactionDateEpoch() != null) {
				log.info("Search After called with parameters TransactionDateEpoch: {}, txnId :{}, StreamSource: {}",
						searchContext.getTransactionDateEpoch(), searchContext.getPaginationTxnId(),
						searchContext.getPaginationStreamSource());
				startTimeNow = Long.parseLong(searchContext.getTransactionDateEpoch());
				searchSourceBuilder.searchAfter(
						Arrays
							.asList(searchContext.getTransactionDateEpoch(), searchContext.getPaginationTxnId(),
									searchContext.getEntityId(), searchContext.getPaginationStreamSource())
							.toArray());
			}
		}
		else {
			startTimeNow = startTime;
		}

		IndexConfig indexConfig = searchIndexRangeConfig.getIndexConfigUsingRange(startTimeNow,
				searchContext.getToDate());
		indexConfigWrapper.setIndexConfig(indexConfig);

		if (indexConfig != null) {

			if (!needToScanFurtherProgressiveSearchingRanges(paramMap, indexConfig)) {
				return null;
			}

			// Getting from date for search request for this fetch Index Config
			long searchRequestFromDateForIndexConfig = getFromDateForSearchRequestOfThisIndex(searchContext,
					indexConfig, initialFromDate);
			searchContext.setFromDate(searchRequestFromDateForIndexConfig);

			// Getting To Date for search request for this fetch Index Config
			long searchRequestToDateForIndexConfig = getToDateForSearchRequestOfThisIndex(searchContext, indexConfig,
					numberOfEsCall);
			searchContext.setToDate(searchRequestToDateForIndexConfig);

			if (!indexConfig.getIndexes().isEmpty() && this.esIndexAlias.equals(indexConfig.getIndexes().get(0))) {
				log.error("Not able to generate monthly indices in index config, this can increase Load over ES, "
						+ "need to fix this ASAP, indexConfigIndices : {}", indexConfig.getIndexes());
				metricsAgent.incrementCount(QUERIED_OVER_ALIAS_IN_LISTING);
			}
			searchRequest = buildSearchRequest(indexConfig.getIndexes(), noOfRecordsLeft, searchContext,
					searchSourceBuilder, paramMap);
			// below is done so that we can find next range of indexes using this
			// searchContext in next run if required.
			long endRangeForNewIndexes = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getStart(),
					DATE_FORMAT);
			searchContext.setToDate(endRangeForNewIndexes);
		}
		else {
			paramMap.put(PARAM_MAP_KEY_CONTAINING_FLAG_THAT_COMPLETE_DATA_SCAN_IS_DONE, "true");
		}
		return searchRequest;
	}

	/**
	 * @param searchContext (built search context for this request)
	 * @param indexConfig (progressive search index config range)
	 * @param initialFromDate (from date getting in request)
	 * @return fromDate (return fromDate for search request of this specific progressive
	 * indexConfig)
	 * @throws ParseException (if any parsing exception occurred in date parsing)
	 */
	private long getFromDateForSearchRequestOfThisIndex(final SearchContext searchContext,
			final IndexConfig indexConfig, final long initialFromDate) throws ParseException {

		long fromDateForSearchRequest;
		long indexConfigFromDate = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getStart(),
				DATE_FORMAT);

		/*
		 * 1. If Api request From Date is smaller than from date of from date of this
		 * config else from date is start date of index config
		 */
		if (indexConfigFromDate <= initialFromDate) {
			fromDateForSearchRequest = initialFromDate;
		}
		else {
			fromDateForSearchRequest = indexConfigFromDate;
		}

		return fromDateForSearchRequest;
	}

	/**
	 * @param searchContext (built search context for this request)
	 * @param indexConfig (progressive search index config range)
	 * @return (return toDate for search request of this specific progressive indexConfig)
	 * @throws ParseException (if any parsing exception occurred in date parsing)
	 */
	private long getToDateForSearchRequestOfThisIndex(final SearchContext searchContext, final IndexConfig indexConfig,
			final int numberOfEsCall) throws ParseException {

		long toDateForSearchRequest = searchContext.getToDate();

		if (numberOfEsCall > 0) {
			long indexConfigToDate = DateRange.getTimeFromStringFormattedDate(indexConfig.getRange().getEnd(),
					DATE_FORMAT);
			// minus 1 as end date is mentioned as starting of new month
			toDateForSearchRequest = indexConfigToDate - 1;
		}

		/*
		 * if call of greater than page 1 and pagination txn epoch (txnDate of last txn of
		 * previous page) lies between this index config then toDate is that txn date
		 * Epoch else end date of this index config
		 */
		if (NumberFormatUtility.isLongParsable(searchContext.getTransactionDateEpoch()) && searchIndexRangeConfig
			.isDateLieInIndexConfig(Long.parseLong(searchContext.getTransactionDateEpoch()), indexConfig)) {
			toDateForSearchRequest = Long.parseLong(searchContext.getTransactionDateEpoch());
		}

		return toDateForSearchRequest;
	}

	private SearchRequest buildSearchRequest(final List<String> indices, final int pageSize,
			final SearchContext searchContext, final SearchSourceBuilder searchSourceBuilder,
			final Map<String, String> paramMap) {
		searchSourceBuilder.size(pageSize + 1); // get one more record than page Size to
												// check if next page exists or not
		QueryBuilder queryBuilder = EsUtil.getQueryBuilderForDesiredParameters(indices, searchContext,
				paramMap.get(REQUEST_ID), FILTER);
		BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
		boolQuery.must(queryBuilder);

		// TODO need to move this change to searchContext PTH-259
		if (paramMap.containsKey(TXN_CATEGORY)) {
			String txnCategory = paramMap.get(TXN_CATEGORY);
			if (txnCategory.contains(TxnCategoryEnum.MONEY_SENT.getTxnCategory())
					&& !txnCategory.contains(TxnCategoryEnum.SELF_TRANSFER.getTxnCategory())) {
				// Add the mustNot condition to exclude SELF_TRANSFER
				boolQuery.mustNot(QueryBuilders.termQuery(SearchContextToESMappings.get(SEARCH_SELF_TRANSFER),
						SELF_TRANSFER_KEYWORD));
			}
		}
		searchSourceBuilder.query(boolQuery);

		// Commented as we need searchFields to construct PTL related fields
		// searchSourceBuilder.fetchSource(null, "searchFields");
		log.debug("searchSourceBuilder {}", searchSourceBuilder);
		if (!searchContext.isForUpdates() || searchContext.isInvalidForUpdates()) {
			EsUtil.setSortParamInSearchRequest(searchSourceBuilder);
		}
		SearchRequest searchRequest = new SearchRequest(indices.toArray(new String[indices.size()]));
		searchRequest.source(searchSourceBuilder);
		if (searchContext.isForUpdates() && !searchContext.isInvalidForUpdates()) {
			searchRequest.indices(getIndexList(searchContext).toArray(new String[0]));
			searchRequest.indicesOptions(IndicesOptions.LENIENT_EXPAND_OPEN);
		}
		searchRequest.routing(searchContext.getEntityId());
		return searchRequest;
	}

	/*
	 * todo override below method protected RestHighLevelClient getEsClient(final
	 * SearchRequest searchRequest) { if (Objects.nonNull(searchRequest) && this
	 * .isWhiteListedForManagedEs(searchRequest.routing())) {
	 * log.info("Getting data from managed ES for custId : {}", searchRequest.routing());
	 * return this.managedRestHighLevelClient; } return restHighLevelClient; }
	 */

	/**
	 * Sets pagination data for search API. <br>
	 * fromUpdatedDate : 1st page of search API response and updatesAPI response.<br>
	 * transactionDateEpoch, paginationTxnId, paginationStreamSource, pageNo : served for
	 * search API response.
	 * @param txns list of txns in the final response
	 * @param searchContext searchContext for which response is served
	 * @return pagination data for next page request
	 */

	public PaginationParams setPaginationData(final List<TransformedTransactionHistoryDetail> txns,
			final SearchContext searchContext) {
		PaginationParams paginationParams = new PaginationParams();
		/*
		 * scannedTill entry will be present in ThreadContext only when complete scan
		 * hasn't been done because of limiting the search max till some defined limit &
		 * we are returning the response to the client
		 */
		if (ThreadContext.containsKey(THREAD_CONTEXT_KEY_CONTAINING_EPOCH_TILL_WHICH_SCAN_HAS_BEEN_DONE)) {
			paginationParams.setPageNo(searchContext.getPageNo() + 1);
			Long toDateForNextPage = Long
				.valueOf(ThreadContext.get(THREAD_CONTEXT_KEY_CONTAINING_EPOCH_TILL_WHICH_SCAN_HAS_BEEN_DONE)) - 1;
			paginationParams.setToDateForSubsequentRequest(toDateForNextPage);
		}
		/*
		 * In case complete data scan has been done, send pagination params only if
		 * txns.size > searchContext.pageSize else send pagination params as null
		 */
		else if (txns.size() > searchContext.getPageSize()) {
			TransformedTransactionHistoryDetail lastFetchedRecord = txns.get(searchContext.getPageSize() - 1);
			paginationParams.setPaginationStreamSource(String.valueOf(lastFetchedRecord.getStreamSource()));
			paginationParams.setPaginationTxnId(lastFetchedRecord.getTxnId());
			paginationParams.setTransactionDateEpoch(String.valueOf(lastFetchedRecord.getTxnDate()));
			paginationParams.setPageNo(searchContext.getPageNo() + 1);
		}
		return paginationParams;
	}

	private boolean needToScanFurtherProgressiveSearchingRanges(final Map<String, String> paramMap,
			final IndexConfig indexConfig) throws ParseException {
		if (StringUtils.isNotBlank(paramMap.get(EPOCH_BEGINNING_OF_MONTH_TILL_WHICH_MAX_SCAN_TO_BE_DONE_PARAM_MAP_KEY))
				&& !"now".equalsIgnoreCase(indexConfig.getRange().getEnd())) {
			Long epochOfBeginningOfMonthTillWhichMaxScanIsToBeDone = Long
				.parseLong(paramMap.get(EPOCH_BEGINNING_OF_MONTH_TILL_WHICH_MAX_SCAN_TO_BE_DONE_PARAM_MAP_KEY));
			long indexConfigEndTime = getTimeFromStringFormattedDate(indexConfig.getRange().getEnd());
			if (indexConfigEndTime <= epochOfBeginningOfMonthTillWhichMaxScanIsToBeDone) {
				ThreadContext.put(THREAD_CONTEXT_KEY_CONTAINING_EPOCH_TILL_WHICH_SCAN_HAS_BEEN_DONE,
						String.valueOf(indexConfigEndTime));
				return false;
			}
		}
		return true;
	}

}
