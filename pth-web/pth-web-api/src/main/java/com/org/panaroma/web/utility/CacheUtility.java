package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE;
import static com.org.panaroma.commons.constants.CommonCacheConstants.CACHE_DATA_TO_KAFKA_PUSH;
import static com.org.panaroma.commons.constants.CommonCacheConstants.HISTORY;
import static com.org.panaroma.commons.constants.CommonCacheConstants.NON_TRANSACTING_USER_KAFKA_CLIENT_NAME;
import static com.org.panaroma.commons.constants.CommonCacheConstants.SUB_CACHE;
import static com.org.panaroma.commons.constants.Constants.COMMA;
import static com.org.panaroma.commons.constants.Constants.PIPE_SYMBOL;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_CACHE;
import static com.org.panaroma.commons.constants.WebConstants.NON_TRANSACTING_USER_CACHE;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_SYSTEM;
import static com.org.panaroma.commons.dto.cache.CacheState.OLD_TXN_UPDATES;
import static com.org.panaroma.commons.dto.cache.CacheState.ZERO_DELTA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.CACHE_CREATED_DATE_CURRENT_DATE_DIFF_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.monitoring.MonitoringConstants.FAILURE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.REQUEST_ID;
import static com.org.panaroma.web.monitoring.MonitoringConstants.STATUS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SUCCESS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TIME_WINDOW;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TXN_DATE_OF_LAST_TXN_IN_LISTING_CACHE_IS_BEFORE_CURRENT_FROM_DATE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.monitoringTagFormat;

import com.google.common.collect.Iterators;
import com.org.panaroma.commons.cache.dto.ListingSpecificCacheData;
import com.org.panaroma.commons.cache.dto.NonTransactingUserCacheData;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.cache.AppSideCacheData;
import com.org.panaroma.commons.dto.cache.CacheState;
import com.org.panaroma.commons.dto.cache.ZeroDeltaCache;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.cache.AerospikeCacheClient;
import com.org.panaroma.web.cache.CacheContext;
import com.org.panaroma.web.cache.ICacheClient;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class CacheUtility {

	private final AerospikeCacheClient cacheClient;

	private final IKafkaClient iKafkaClient;

	private final MetricsAgent metricsAgent;

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	private Utility utility;

	@Autowired
	public CacheUtility(final AerospikeCacheClient cacheClient, final IKafkaClient iKafkaClient,
			final MetricsAgent metricsAgent, final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final Utility utility) {
		this.cacheClient = cacheClient;
		this.iKafkaClient = iKafkaClient;
		this.metricsAgent = metricsAgent;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.utility = utility;
	}

	public void pushAppSideCacheDataToKafkaForCache(final SearchContext searchContext, final CacheState cacheState,
			final boolean forCacheInvalidate) {

		AppSideCacheData appSideCacheData = new AppSideCacheData();
		if (cacheState == ZERO_DELTA) {
			appSideCacheData.setZeroDeltaCacheData(
					ZeroDeltaCache.builder().fromUpdatedDate(searchContext.getFromUpdatedDate()).build());
		}
		else if (cacheState == OLD_TXN_UPDATES || forCacheInvalidate) {
			log.warn("Deleting AppSideCacheData with cacheState : {}", cacheState);
			appSideCacheData.setForDeletion(true);
		}
		appSideCacheData.setUpdatedOn(System.currentTimeMillis());
		CacheUpdaterKafkaDto cacheUpdaterKafkaDto = CacheUpdaterKafkaDto.builder()
			.cacheMetaInfo(Collections.singletonMap(CacheInfo.UTH_APP_SIDE_CACHE_DATA,
					getCacheKeyCorrespondingToCacheName(searchContext, CacheInfo.UTH_APP_SIDE_CACHE_DATA)))
			.appSideCacheData(appSideCacheData)
			.entityId(searchContext.getEntityId())
			.originator(HISTORY)
			.createdDate(System.currentTimeMillis())
			.build();
		pushAppSideCacheDataToKafka(cacheUpdaterKafkaDto);
	}

	public void saveDataForNonTransactingUserInCacheIfRequired(final SearchContext searchContext,
			final CacheContext cacheContext) {

		Map<CacheInfo, String> cacheInfoToCacheKeyMappingToPushData = CacheUtility
			.getCacheKeyToCacheNameMapping(cacheContext, searchContext);

		// the above map will be of 0 size if request in not valid for being pushed to
		// cache
		if (Objects.isNull(cacheInfoToCacheKeyMappingToPushData) || cacheInfoToCacheKeyMappingToPushData.isEmpty()) {
			return;
		}

		for (CacheInfo cacheInfo : cacheInfoToCacheKeyMappingToPushData.keySet()) {
			String key = cacheInfoToCacheKeyMappingToPushData.get(cacheInfo);
			cacheClient.saveNtuCacheData(key, Boolean.TRUE, cacheInfo);
			log.info("Saved data : {} in NTU cache for entityId : {}", Boolean.TRUE, key);
		}

	}

	private void pushAppSideCacheDataToKafka(final CacheUpdaterKafkaDto cacheUpdaterKafkaDto) {
		if (Objects.isNull(cacheUpdaterKafkaDto) || Objects.isNull(cacheUpdaterKafkaDto.getCacheMetaInfo())) {
			// cache keys have not been added for cache update
			return;
		}
		String cacheSubType = "appSideCacheData"; // for metrics and logs
		try {
			// push to kafka
			this.iKafkaClient.pushIntoKafka(NON_TRANSACTING_USER_KAFKA_CLIENT_NAME, cacheUpdaterKafkaDto.getEntityId(),
					cacheUpdaterKafkaDto);
			// update metrics
			metricsAgent.incrementCount(CACHE_DATA_TO_KAFKA_PUSH,
					String.format(monitoringTagFormat, CACHE, CacheInfo.UTH_APP_SIDE_CACHE_DATA),
					String.format(monitoringTagFormat, SUB_CACHE, cacheSubType),
					String.format(monitoringTagFormat, STATUS, SUCCESS));
		}
		catch (TaskRejectedException ex) {
			// task was rejected by kafka.
			metricsAgent.incrementCount(CACHE_DATA_TO_KAFKA_PUSH,
					String.format(monitoringTagFormat, CACHE, CacheInfo.UTH_APP_SIDE_CACHE_DATA),
					String.format(monitoringTagFormat, SUB_CACHE, cacheSubType),
					String.format(monitoringTagFormat, STATUS, FAILURE));
			log.error(
					CacheInfo.UTH_APP_SIDE_CACHE_DATA
							+ " : Async task rejected for updating : {} with uid : {}, reason : {}",
					cacheSubType, cacheUpdaterKafkaDto.getUid(), CommonsUtility.exceptionFormatter(ex));
		}
		catch (Exception ex) {
			metricsAgent.incrementCount(CACHE_DATA_TO_KAFKA_PUSH,
					String.format(monitoringTagFormat, CACHE, CacheInfo.UTH_APP_SIDE_CACHE_DATA),
					String.format(monitoringTagFormat, SUB_CACHE, cacheSubType),
					String.format(monitoringTagFormat, STATUS, FAILURE));
			log.error(CacheInfo.UTH_APP_SIDE_CACHE_DATA
					+ " : Exception while pushing data to kafka for updating : {} with uid : {}." + " Exception : {}",
					cacheSubType, cacheUpdaterKafkaDto.getUid(), CommonsUtility.exceptionFormatter(ex));
		}
	}

	public static String getCacheKeyCorrespondingToCacheName(final SearchContext searchContext,
			final CacheInfo cacheInfo) {
		String entityId = searchContext.getEntityId();
		if (StringUtils.isBlank(entityId)) {
			log.warn("EntityId is null while creating key to hit cache");
			return null;
		}

		switch (cacheInfo) {
			case UTH_NTU_CACHE:
				return entityId;
			case UTH_APP_SIDE_CACHE_DATA:
				return entityId;
			case UPI_PASSBOOK_LISTING_CACHE:
				String upiIdentifier = searchContext.getEsUpiIdentifier();
				if (StringUtils.isBlank(upiIdentifier)) {
					log.warn("UPI identifier is null while creating key to hit UPI Passbook Cache for entityId : {}",
							entityId);
					return null;
				}
				return entityId + PIPE_SYMBOL + upiIdentifier;
			default:
				throw new RuntimeException("Unsupported cache type : " + cacheInfo.getCacheName());
		}
	}

	public static Map<CacheInfo, String> getCacheKeyToCacheNameMapping(final CacheContext cacheContext,
			final SearchContext searchContext) {
		if (ObjectUtils.isEmpty(cacheContext) || ObjectUtils.isEmpty(searchContext)) {
			return null;
		}
		Map<CacheInfo, String> cacheKeyToCacheNameMappingToPushData = new HashMap<>();

		if (cacheContext.isRequestValidToBeSavedInNtuCache()) {
			cacheKeyToCacheNameMappingToPushData.put(CacheInfo.UTH_NTU_CACHE,
					getCacheKeyCorrespondingToCacheName(searchContext, CacheInfo.UTH_NTU_CACHE));
		}
		return cacheKeyToCacheNameMappingToPushData;
	}

	public CacheContext getCacheContext(final SearchContext searchContext, final Map<String, String> paramMap) {

		CacheContext cacheContext = new CacheContext(configurablePropertiesHolder, searchContext, paramMap, utility);

		if (cacheContext.isRequestValidForGettingDataFromNtuCache()) {
			cacheContext.setCacheUsed(CacheInfo.UTH_NTU_CACHE);
			enrichCacheContextUsingCacheData(searchContext, cacheContext);
		}
		return cacheContext;
	}

	private void enrichCacheContextUsingCacheData(final SearchContext searchContext, final CacheContext cacheContext) {
		if (Objects.isNull(cacheContext.getCacheUsed())) {
			return;
		}

		Boolean cacheData = cacheClient.getNtuCacheData(searchContext, cacheContext.getCacheUsed());
		log.info("Data received from NTU cache : {} for entityId : {}", cacheData, searchContext.getEntityId());

		if (Boolean.TRUE.equals(cacheData)) {
			cacheContext.setRequestValidToThrow4010Error(true);
		}

		cacheContext.setRequestValidToBeSavedInNtuCache(
				cacheContext.isRequestValidToBeSavedInNtuCache(searchContext, cacheData));
	}

	/**
	 * retrieves the AppSideCacheData stored in cache
	 * @return data from cache
	 */
	public AppSideCacheData getAppSideCacheData(final SearchContext searchContext, final CacheInfo cacheInfo) {
		log.debug("Getting appSideCache data for entityId : {}", searchContext.getEntityId());
		AppSideCacheData appSideCacheData = cacheClient.getAppSideCacheData(searchContext, cacheInfo);
		if (appSideCacheData == null) {
			log.debug("No user meta data found for entityId : {}", searchContext.getEntityId());
			return null;
		}
		log.info("AppSideCacheData fetched : {}", appSideCacheData);
		return appSideCacheData;
	}

}
