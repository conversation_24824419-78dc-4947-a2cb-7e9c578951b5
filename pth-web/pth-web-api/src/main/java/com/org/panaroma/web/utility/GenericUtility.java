package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.LocalizationConstants.ENGLISH_LOCALE;
import static com.org.panaroma.commons.constants.WebConstants.ACCOUNT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.AC_NO;
import static com.org.panaroma.commons.constants.WebConstants.AMPERSAND;
import static com.org.panaroma.commons.constants.WebConstants.BENEF_NAME;
import static com.org.panaroma.commons.constants.WebConstants.BHIM_UPI_NAME;
import static com.org.panaroma.commons.constants.WebConstants.CARD;
import static com.org.panaroma.commons.constants.WebConstants.CARD_NO;
import static com.org.panaroma.commons.constants.WebConstants.CLOSE_BRACE_REGEX;
import static com.org.panaroma.commons.constants.WebConstants.CREDIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.CUSTOMER_CREATION_DATE;
import static com.org.panaroma.commons.constants.WebConstants.DEBIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT;
import static com.org.panaroma.commons.constants.WebConstants.DETAILS_CUSTOM_NARRATION;
import static com.org.panaroma.commons.constants.WebConstants.DYNAMIC_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.EMPTY_STRING;
import static com.org.panaroma.commons.constants.WebConstants.EQUAL_SYMBOL;
import static com.org.panaroma.commons.constants.WebConstants.FAILED;
import static com.org.panaroma.commons.constants.WebConstants.FALSE;
import static com.org.panaroma.commons.constants.WebConstants.FOR_UPDATES;
import static com.org.panaroma.commons.constants.WebConstants.GIFT_VOUCHER;
import static com.org.panaroma.commons.constants.WebConstants.GV_PURCHASED;
import static com.org.panaroma.commons.constants.WebConstants.GV_REDEEM;
import static com.org.panaroma.commons.constants.WebConstants.IS_SEC_INST_DEBIT_INSTRUMENT;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_BANK_ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.LISTING;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_CUSTOM_NARRATION;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_CUSTOM_NARRATION_NAME;
import static com.org.panaroma.commons.constants.WebConstants.LOCALIZATION_KEY_STORECASH_DISPLAY_NAME;
import static com.org.panaroma.commons.constants.WebConstants.OMS_NARRATION;
import static com.org.panaroma.commons.constants.WebConstants.PATTERN_STRING;
import static com.org.panaroma.commons.constants.WebConstants.PAYTMGIFTVOUCHER;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_GIFT_VOUCHER;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_STORECASH;
import static com.org.panaroma.commons.constants.WebConstants.PENDING;
import static com.org.panaroma.commons.constants.WebConstants.PERCENT_OPEN_BRACE_REGEX;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_LOAN;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.PPBL_CONSTANT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.REPLACABLE_BENEF_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMT_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.SPACE_REPLACEMENT;
import static com.org.panaroma.commons.constants.WebConstants.STORECASH_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.SUCCESS;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;
import static com.org.panaroma.commons.constants.WebConstants.UPI_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.VIA_NET_BANKING;
import static com.org.panaroma.commons.constants.WebConstants.WALLET;
import static com.org.panaroma.commons.utils.DateTimeUtility.getCustomerCreationDateFormattedDate;
import static com.org.panaroma.commons.utils.DateTimeUtility.getMonthStartEpoch;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.CharMatcher;
import com.google.common.collect.Iterables;
import com.org.panaroma.commons.constants.LocaleMsgConstants;
import com.org.panaroma.commons.dto.CardType;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.IfscUtility;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.Pair;
import com.org.panaroma.commons.utils.UpiInternationalUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.config.BankConfig;
import com.org.panaroma.web.config.BankDataConfigEnum;
import com.org.panaroma.web.config.RptCodeConfig;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.NameDetails;
import com.org.panaroma.web.dto.NarrationMapperDto;
import com.org.panaroma.web.dto.SecondPartyInfo;
import com.org.panaroma.web.dto.UserInstrumentLogos;
import com.org.panaroma.web.dto.ValidationResult;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.ImageData;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.web.enums.ListingResponseMappingEnum;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.avro.reflect.Nullable;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class GenericUtility {

	private static Map<TransactionTypeEnum, String> secondPartyNameExcludedForTxnTypes;

	private static Map<Pair<TransactionTypeEnum, ClientStatusEnum>, String> transactionTypeStatusNarrationMap;

	private static final Map<NarrationMapperDto, String> narrationMap;

	private static List<String> nonOmsNarrationCartVerticalIds;

	private static final ObjectMapper objectMapper;

	private static MetricsAgent metricsAgent;

	// TODO : temporary map need to integrate it afterward into the ReportCodeConfig json
	// itself
	private static Map<Pair<String, ClientStatusEnum>, Pair<String, String>> reportCodeStatusNarrationMap;

	static {
		Map<TransactionTypeEnum, String> map = new HashMap<>();
		// map.put(TransactionTypeEnum.CASHBACK_RECEIVED, "cashback");
		// map.put(TransactionTypeEnum.ON_HOLD,"onHold");
		// map.put(TransactionTypeEnum.RELEASED,"released");
		map.put(TransactionTypeEnum.OTHER_CREDIT, "otherCredit");
		map.put(TransactionTypeEnum.OTHER_DEBIT, "other debit");
		// map.put(TransactionTypeEnum.ADD_MONEY,"add money");
		map.put(TransactionTypeEnum.ADD_MONEY_TO_BANK, "add money to bank");
		secondPartyNameExcludedForTxnTypes = Collections.unmodifiableMap(map);
		transactionTypeStatusNarrationMap = new HashMap<>();
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P_OUTWARD, ClientStatusEnum.FAILURE),
				"Payment to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P_OUTWARD, ClientStatusEnum.SUCCESS),
				"Paid to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P_OUTWARD, ClientStatusEnum.PENDING),
				"Payment to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2P_OUTWARD_REVERSAL, ClientStatusEnum.FAILURE), "");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.P2P_OUTWARD_REVERSAL, ClientStatusEnum.SUCCESS),
				"Refund for failed transfer to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2P_OUTWARD_REVERSAL, ClientStatusEnum.PENDING), "");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P_INWARD, ClientStatusEnum.FAILURE), "");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P_INWARD, ClientStatusEnum.SUCCESS),
				"Received from");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P_INWARD, ClientStatusEnum.PENDING), "");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2M_REFUND, ClientStatusEnum.FAILURE),
				"Refund from");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2M_REFUND, ClientStatusEnum.SUCCESS),
				"Refund from");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2M_REFUND, ClientStatusEnum.PENDING),
				"Refund from");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M, ClientStatusEnum.FAILURE),
				"Payment to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M, ClientStatusEnum.SUCCESS),
				"Paid to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M, ClientStatusEnum.PENDING),
				"Payment to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M_OUTWARD, ClientStatusEnum.FAILURE),
				"Payment to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M_OUTWARD, ClientStatusEnum.SUCCESS),
				"Paid to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M_OUTWARD, ClientStatusEnum.PENDING),
				"Payment to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M_INWARD, ClientStatusEnum.SUCCESS),
				"Received from");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2M, ClientStatusEnum.FAILURE),
				"Payment to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2M, ClientStatusEnum.SUCCESS), "Paid to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2M, ClientStatusEnum.PENDING),
				"Payment to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2M_INTERNATIONAL, ClientStatusEnum.FAILURE), "Payment to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2M_INTERNATIONAL, ClientStatusEnum.SUCCESS), "Paid to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2M_INTERNATIONAL, ClientStatusEnum.PENDING), "Payment to");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL, ClientStatusEnum.FAILURE),
				"Refund for failed transfer to");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL, ClientStatusEnum.SUCCESS),
				"Refund for failed transfer to");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.P2M_REVERSAL_INTERNATIONAL, ClientStatusEnum.PENDING),
				"Refund for failed transfer to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M_REFUND, ClientStatusEnum.FAILURE),
				"Refund from");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M_REFUND, ClientStatusEnum.SUCCESS),
				"Refund from");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.P2P2M_REFUND, ClientStatusEnum.PENDING),
				"Refund from");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ADD_MONEY, ClientStatusEnum.FAILURE),
				"Add Money to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ADD_MONEY, ClientStatusEnum.SUCCESS),
				"Money added to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ADD_MONEY, ClientStatusEnum.PENDING),
				"Add Money to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.CASHBACK_RECEIVED, ClientStatusEnum.FAILURE), "");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.CASHBACK_RECEIVED, ClientStatusEnum.SUCCESS), "Cashback Received from");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.CASHBACK_RECEIVED, ClientStatusEnum.PENDING), "");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.OTHER_CREDIT, ClientStatusEnum.FAILURE),
				"");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.OTHER_CREDIT, ClientStatusEnum.SUCCESS),
				"Money Received");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.OTHER_CREDIT, ClientStatusEnum.PENDING),
				"");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.OTHER_DEBIT, ClientStatusEnum.FAILURE),
				"");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.OTHER_DEBIT, ClientStatusEnum.SUCCESS),
				"Money Sent");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.OTHER_DEBIT, ClientStatusEnum.PENDING),
				"");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ON_HOLD, ClientStatusEnum.FAILURE), "");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ON_HOLD, ClientStatusEnum.SUCCESS),
				"Money on hold for Order at");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ON_HOLD, ClientStatusEnum.PENDING), "");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.RELEASED, ClientStatusEnum.FAILURE), "");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.RELEASED, ClientStatusEnum.SUCCESS),
				"Money on hold released by");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.RELEASED, ClientStatusEnum.PENDING), "");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ADD_AND_PAY, ClientStatusEnum.FAILURE),
				"Payment to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ADD_AND_PAY, ClientStatusEnum.SUCCESS),
				"Paid to");
		transactionTypeStatusNarrationMap.put(new Pair<>(TransactionTypeEnum.ADD_AND_PAY, ClientStatusEnum.PENDING),
				"Payment to");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.ADD_MONEY_TO_BANK, ClientStatusEnum.FAILURE),
				"Add Money to Paytm Payments Bank");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.ADD_MONEY_TO_BANK, ClientStatusEnum.SUCCESS),
				"Money added to Paytm Payments Bank");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.ADD_MONEY_TO_BANK, ClientStatusEnum.PENDING),
				"Add Money to Paytm Payments Bank");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD, ClientStatusEnum.SUCCESS), "Paid to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD, ClientStatusEnum.FAILURE), "Payment to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD, ClientStatusEnum.PENDING), "Transfer to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD, ClientStatusEnum.SUCCESS), "Received from");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.P2P_INWARD_REMITTANCE, ClientStatusEnum.SUCCESS),
				"Received foreign remittance from");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.P2P_OUTWARD_REMITTANCE, ClientStatusEnum.SUCCESS),
				"Paid for Outward Remittance to");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.P2P_OUTWARD_REMITTANCE_REFUND, ClientStatusEnum.SUCCESS),
				"Refund of Outward Remittance sent to");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.WALLET_UPI_DEBIT_P2P, ClientStatusEnum.FAILURE),
				"Money transfer pending");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.WALLET_UPI_DEBIT_P2P, ClientStatusEnum.SUCCESS), "Money sent to");
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.WALLET_UPI_DEBIT_P2P, ClientStatusEnum.PENDING),
				"Money transfer failed to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.UPI_WALLET_CREDIT, ClientStatusEnum.SUCCESS), "Received from");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.UPI_WALLET_CREDIT, ClientStatusEnum.FAILURE), "");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.UPI_WALLET_CREDIT, ClientStatusEnum.PENDING), "");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.WALLET_UPI_DEBIT_P2M, ClientStatusEnum.FAILURE), "Payment to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.WALLET_UPI_DEBIT_P2M, ClientStatusEnum.SUCCESS), "Paid to");
		transactionTypeStatusNarrationMap
			.put(new Pair<>(TransactionTypeEnum.WALLET_UPI_DEBIT_P2M, ClientStatusEnum.PENDING), "Payment to");
		// Wallet UPI Debit Reversal
		transactionTypeStatusNarrationMap.put(
				new Pair<>(TransactionTypeEnum.WALLET_UPI_DEBIT_REVERSAL, ClientStatusEnum.SUCCESS),
				"Refund for failed transfer to");

		narrationMap = new HashMap<>();

		narrationMap.put(NarrationMapperDto.builder()
			.streamSource(TransactionSource.PG)
			.txnType(TransactionTypeEnum.P2M)
			.txnIndicator(TransactionIndicator.DEBIT)
			.merchantType(MerchantTypeEnum.ONUS)
			.clientStatus(ClientStatusEnum.SUCCESS)
			.build(), "Paid for");
		narrationMap.put(NarrationMapperDto.builder()
			.streamSource(TransactionSource.PG)
			.txnType(TransactionTypeEnum.P2M)
			.txnIndicator(TransactionIndicator.DEBIT)
			.merchantType(MerchantTypeEnum.ONUS)
			.clientStatus(ClientStatusEnum.FAILURE)
			.build(), "Payment for");
		narrationMap.put(NarrationMapperDto.builder()
			.streamSource(TransactionSource.PG)
			.txnType(TransactionTypeEnum.P2M)
			.txnIndicator(TransactionIndicator.DEBIT)
			.merchantType(MerchantTypeEnum.ONUS)
			.clientStatus(ClientStatusEnum.PENDING)
			.build(), "Payment for");

		narrationMap.put(NarrationMapperDto.builder()
			.streamSource(TransactionSource.PPBL_PG)
			.txnType(TransactionTypeEnum.P2M)
			.txnIndicator(TransactionIndicator.DEBIT)
			.merchantType(MerchantTypeEnum.ONUS)
			.clientStatus(ClientStatusEnum.SUCCESS)
			.build(), "Paid for");
		narrationMap.put(NarrationMapperDto.builder()
			.streamSource(TransactionSource.PPBL_PG)
			.txnType(TransactionTypeEnum.P2M)
			.txnIndicator(TransactionIndicator.DEBIT)
			.merchantType(MerchantTypeEnum.ONUS)
			.clientStatus(ClientStatusEnum.FAILURE)
			.build(), "Payment for");
		narrationMap.put(NarrationMapperDto.builder()
			.streamSource(TransactionSource.PPBL_PG)
			.txnType(TransactionTypeEnum.P2M)
			.txnIndicator(TransactionIndicator.DEBIT)
			.merchantType(MerchantTypeEnum.ONUS)
			.clientStatus(ClientStatusEnum.PENDING)
			.build(), "Payment for");

		reportCodeStatusNarrationMap = new HashMap<>();
		reportCodeStatusNarrationMap.put(new Pair<>("20261_D", ClientStatusEnum.SUCCESS),
				new Pair<>("Fixed Deposit Created", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20261_D", ClientStatusEnum.PENDING),
				new Pair<>("Creation of Fixed Deposit", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20261_D", ClientStatusEnum.FAILURE),
				new Pair<>("Creation of Fixed Deposit", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20263_D", ClientStatusEnum.SUCCESS),
				new Pair<>("Fixed Deposit Created", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20263_D", ClientStatusEnum.PENDING),
				new Pair<>("Creation of Fixed Deposit", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20263_D", ClientStatusEnum.FAILURE),
				new Pair<>("Creation of Fixed Deposit", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20264_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Refund for failed Fixed Deposit creation", "Refund Received"));
		reportCodeStatusNarrationMap.put(new Pair<>("20264_C", ClientStatusEnum.PENDING),
				new Pair<>("Refund for failed Fixed Deposit creation", "Refund Pending"));
		reportCodeStatusNarrationMap.put(new Pair<>("20264_C", ClientStatusEnum.FAILURE),
				new Pair<>("Refund for failed Fixed Deposit creation", "Refund Failed"));
		reportCodeStatusNarrationMap.put(new Pair<>("20265_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Transferred from Fixed Deposit", "Transferred Successfully"));
		reportCodeStatusNarrationMap.put(new Pair<>("20265_C", ClientStatusEnum.PENDING),
				new Pair<>("Transfer from Fixed Deposit", "Transfer Pending"));
		reportCodeStatusNarrationMap.put(new Pair<>("20265_C", ClientStatusEnum.FAILURE),
				new Pair<>("Transfer from Fixed Deposit", "Transfer Failed"));
		reportCodeStatusNarrationMap.put(new Pair<>("20260_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Transferred from Fixed Deposit", "Transferred Successfully"));
		reportCodeStatusNarrationMap.put(new Pair<>("20260_C", ClientStatusEnum.PENDING),
				new Pair<>("Transfer from Fixed Deposit", "Transfer Pending"));
		reportCodeStatusNarrationMap.put(new Pair<>("20260_C", ClientStatusEnum.FAILURE),
				new Pair<>("Transfer from Fixed Deposit", "Transfer Failed"));
		reportCodeStatusNarrationMap.put(new Pair<>("20250_D", ClientStatusEnum.SUCCESS),
				new Pair<>("Fixed Deposit Created", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20250_D", ClientStatusEnum.PENDING),
				new Pair<>("Creation of Fixed Deposit", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20250_D", ClientStatusEnum.FAILURE),
				new Pair<>("Creation of Fixed Deposit", "Money Debited"));
		reportCodeStatusNarrationMap.put(new Pair<>("20205_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Interest Earned", "Interest Received"));
		reportCodeStatusNarrationMap.put(new Pair<>("20205_C", ClientStatusEnum.PENDING),
				new Pair<>("Interest Earned", "Interest Received"));
		reportCodeStatusNarrationMap.put(new Pair<>("20205_c", ClientStatusEnum.FAILURE),
				new Pair<>("Interest Earned", "Interest Received"));

		reportCodeStatusNarrationMap.put(new Pair<>("20275_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Transferred from Fixed Deposit", "Transferred Successfully"));
		reportCodeStatusNarrationMap.put(new Pair<>("20275_C", ClientStatusEnum.PENDING),
				new Pair<>("Transfer from Fixed Deposit", "Transfer Pending"));
		reportCodeStatusNarrationMap.put(new Pair<>("20275_C", ClientStatusEnum.FAILURE),
				new Pair<>("Transfer from Fixed Deposit", "Transfer Failed"));
		reportCodeStatusNarrationMap.put(new Pair<>("20276_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Interest earned from Fixed Deposit", "Interest Earned"));
		reportCodeStatusNarrationMap.put(new Pair<>("20276_C", ClientStatusEnum.PENDING),
				new Pair<>("Interest earned from Fixed Deposit", "Interest Earned"));
		reportCodeStatusNarrationMap.put(new Pair<>("20276_C", ClientStatusEnum.FAILURE),
				new Pair<>("Interest earned from Fixed Deposit", "Interest Earned"));
		reportCodeStatusNarrationMap.put(new Pair<>("20277_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Transferred from Fixed Deposit", "Transferred Successfully"));
		reportCodeStatusNarrationMap.put(new Pair<>("20277_C", ClientStatusEnum.PENDING),
				new Pair<>("Transfer from Fixed Deposit", "Transfer Pending"));
		reportCodeStatusNarrationMap.put(new Pair<>("20277_C", ClientStatusEnum.FAILURE),
				new Pair<>("Transfer from Fixed Deposit", "Transfer Failed"));
		reportCodeStatusNarrationMap.put(new Pair<>("23002_D", ClientStatusEnum.SUCCESS),
				new Pair<>("Amount Recovered for Fixed Deposit", "Amount Deducted"));
		reportCodeStatusNarrationMap.put(new Pair<>("20267_C", ClientStatusEnum.SUCCESS),
				new Pair<>("FD Redemption for Online Payment", "FD Redemption for Online Payment"));
		// For Upi_International
		reportCodeStatusNarrationMap.put(new Pair<>("20512_D", ClientStatusEnum.PENDING),
				new Pair<>("Payment to", "Payment Pending"));
		reportCodeStatusNarrationMap.put(new Pair<>("20512_D", ClientStatusEnum.FAILURE),
				new Pair<>("Payment to", "Payment Failed"));
		reportCodeStatusNarrationMap.put(new Pair<>("20512_D", ClientStatusEnum.SUCCESS),
				new Pair<>("Paid to", "Money Paid"));

		reportCodeStatusNarrationMap.put(new Pair<>("20504_C", ClientStatusEnum.PENDING),
				new Pair<>("Refund pending", "Refund Pending"));
		reportCodeStatusNarrationMap.put(new Pair<>("20504_C", ClientStatusEnum.FAILURE),
				new Pair<>("Refund Failed", "Refund Failed"));
		reportCodeStatusNarrationMap.put(new Pair<>("20504_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Money Refunded", "Money Refunded"));

		// For Visa Recurring Domestic
		reportCodeStatusNarrationMap.put(new Pair<>("21208_D", ClientStatusEnum.SUCCESS),
				new Pair<>("Automatic Payment to", "Money Paid"));

		// For Visa Recurring Domestic Reversal
		reportCodeStatusNarrationMap.put(new Pair<>("21209_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Refund of Automatic Payment to", "Money Refunded"));

		// For Visa Recurring Non-Domestic
		reportCodeStatusNarrationMap.put(new Pair<>("22208_D", ClientStatusEnum.SUCCESS),
				new Pair<>("Automatic Payment to", "Money Paid"));

		// For Visa Recurring Non-Domestic Reversal
		reportCodeStatusNarrationMap.put(new Pair<>("22209_C", ClientStatusEnum.SUCCESS),
				new Pair<>("Refund of Automatic Payment to", "Money Refunded"));

		objectMapper = new ObjectMapper();

	}

	@Autowired
	public GenericUtility(final MetricsAgent metricsAgent) {
		this.metricsAgent = metricsAgent;
	}

	@Value("${ondc.verticalId}")
	public void setOndcVerticalId(final List<String> ondcVerticalId) {
		GenericUtility.nonOmsNarrationCartVerticalIds = ondcVerticalId;
	}

	public static String getTimeFrameLabel(final long fromDate, final long toDate) {
		LocalDate fDate = Instant.ofEpochMilli(fromDate).atZone(ZoneId.systemDefault()).toLocalDate();
		LocalDate tDate = Instant.ofEpochMilli(toDate).atZone(ZoneId.systemDefault()).toLocalDate();
		log.info("from date : {} and to Date : {}", fDate.toString(), tDate.toString());
		return fDate.getMonth().toString() + fDate.getYear() + " - " + tDate.getMonth().toString() + tDate.getYear();
	}

	public static String getTxnPurpose(final TransformedTransactionHistoryDetail detailNeededForTxn) {
		if (detailNeededForTxn != null) {
			return detailNeededForTxn.getContextMap() != null ? detailNeededForTxn.getContextMap().get(TXN_PURPOSE)
					: null;
		}
		return null;
	}

	public static String getTxnPurpose(final List<TransformedParticipant> participants,
			final Integer transactionIndicator) {
		for (TransformedParticipant participant : participants) {
			if (participant.getTxnIndicator().equals(transactionIndicator) && participant.getContextMap() != null) {
				return participant.getContextMap().get(TXN_PURPOSE);
			}
		}
		return null;
	}

	/**
	 * Checks if the participants represent a wallet to wallet transfer between 2
	 * participants, initiated by wallet, and a debit transaction
	 * @param txn txn DTO that needs to be checked
	 * @return true if there are 2 participants and both are wallet
	 */
	public static boolean isWalletToWallet(final TransformedTransactionHistoryDetail txn) {
		List<TransformedParticipant> participants = txn.getParticipants();
		String entityId = null;
		if (!TransactionSource.WALLET.getTransactionSourceKey().equals(txn.getStreamSource())
				|| !TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(txn.getTxnIndicator())
				|| participants.size() != 2) {
			return false;
		}
		for (TransformedParticipant participant : participants) {
			if (entityId == null) {
				entityId = participant.getEntityId();
			}
			else if (!entityId.equals(participant.getEntityId())) {
				return false;
			}
			if (!PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
				return false;
			}
		}
		return true;
	}

	public static List<UserInstrumentLogos> getUserInstrumentLogos(final TransformedTransactionHistoryDetail esDto) {
		List<UserInstrumentLogos> userInstrumentLogos = new ArrayList<>();
		// Special handling for ADD_AND_PAY, need to show debit from wallet only in
		// listing
		if (TransactionTypeEnum.ADD_AND_PAY.getTransactionTypeKey().equals(esDto.getMainTxnType())
				|| Utility.isAddnPayRefundTxn(esDto)) {
			UserInstrumentLogos userInstrument = new UserInstrumentLogos();
			userInstrument.setSequence("1");
			userInstrument.setLogoUrl(LogoUtility.getLogo(WalletTypesEnum.SCLW.getDisplayName(), LogoType.WALLET_ICON));
			userInstrument.setInstrumentName(WalletTypesEnum.SCLW.getDisplayName());
			userInstrumentLogos.add(userInstrument);
			return userInstrumentLogos;
		}
		// Special handling for ADD_MONEY_BANK, need to show credit to ppbl only in
		// listing
		if (TransactionTypeEnum.ADD_MONEY_TO_BANK.getTransactionTypeKey().equals(esDto.getMainTxnType())) {
			UserInstrumentLogos userInstrument = new UserInstrumentLogos();
			userInstrument.setSequence("1");
			userInstrument.setLogoUrl(LogoUtility.getBankLogo("PYTM0123456", "paytm payments bank"));
			userInstrument.setInstrumentName("Paytm payments bank");
			userInstrumentLogos.add(userInstrument);
			return userInstrumentLogos;
		}
		// add userInstrument for p2p, p2m, addMoney and upi_to_wallet for upiLite
		if (UpiLiteUtility.isTxnUsingUpiLiteInstrument(esDto)) {
			userInstrumentLogos.add(UpiLiteViewUtility.getUserInstrumentLogosForUpiLite());
			// return userInstrumentLogos;
		}
		AtomicInteger walletInstrumentCount = new AtomicInteger(0);
		AtomicInteger storeCashInstrumentCount = new AtomicInteger(0);
		AtomicInteger sequence = new AtomicInteger(userInstrumentLogos.size() + 1);
		// Setting sequence as 1,2,3... as of now. We can change it as per requirement
		List<TransformedParticipant> refinedParticipants = Utility.getRefinedParticipantListForView(esDto);
		refinedParticipants.forEach(participant -> {
			// Skipping Ignored Participant
			if (isValidParticipantForUserInstrumentLogo(esDto, participant)) {
				UserInstrumentLogos logo = new UserInstrumentLogos();
				String userLogo = null;
				String instrumentName = null;
				logo.setSequence(String.valueOf(sequence.get()));
				List<Logo> logos = GenericUtility.getListnDetailCommonLogos(participant, esDto, false, true, null);
				if (logos != null && !logos.isEmpty()) {
					userLogo = logos.get(0).getValue();
					instrumentName = logos.get(0).getLogoName();
					logo.setLogoUrl(userLogo);
					logo.setInstrumentName(instrumentName);
				}
				else {
					logo.setPaymentSystem(participant.getPaymentSystem());
					if (PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
						walletInstrumentCount.getAndIncrement();
					}
					if (PaymentSystemEnum.STORE_CASH.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
						storeCashInstrumentCount.getAndIncrement();
					}
					List<String> instrumentLogoDetails = getInstrumentLogo(esDto, participant);
					logo.setLogoUrl(instrumentLogoDetails.get(0));
					logo.setInstrumentName(instrumentLogoDetails.get(1));
				}
				List<Logo> logoOrder = getListnDetailCommonLogos(participant, esDto, true, true, null);
				logo.setLogoOrder(logoOrder);
				userInstrumentLogos.add(logo);
				sequence.getAndIncrement();
			}
		});
		GenericUtilityExtension.userInstrumentLogos(userInstrumentLogos, walletInstrumentCount);
		GenericUtilityExtension.shiftStoreCashInstrumentToEnd(userInstrumentLogos, storeCashInstrumentCount);
		return userInstrumentLogos;
	}

	private static boolean isValidParticipantForUserInstrumentLogo(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant) {
		boolean isUpiLiteTxn = UpiLiteUtility.isTxnUsingUpiLiteInstrument(esDto);
		boolean isStoreCashParticipant = PaymentSystemEnum.STORE_CASH.getPaymentSystemKey()
			.equals(participant.getPaymentSystem());

		return !Utility.isIgnoredParticipant(participant) && esDto.getEntityId().equals(participant.getEntityId())
				&& esDto.getTxnIndicator().equals(participant.getTxnIndicator())
				// UpiLite is separately handled. So added below condition to proceed with
				// logo building logic
				// only for mlp participant in case of UPILite+MLP Hybrid txn
				&& ((isUpiLiteTxn && isStoreCashParticipant) || (!isUpiLiteTxn));
	}

	public static List<UserInstrumentLogos> getUserInstrumentLogosV2(final TransformedTransactionHistoryDetail esDto) {
		TransactionTypeEnum transactionTypeEnum = TransactionTypeEnum
			.getTransactionTypeEnumByKey(esDto.getMainTxnType());
		/*
		 * this could also be populated for only Add Money Wallet streamSource listing hit
		 * as in other we don't need to change anything. Populated this to maintain
		 * consistency
		 */
		if (TransactionTypeEnum.ADD_MONEY.equals(transactionTypeEnum)) {
			UserInstrumentLogos userInstrument = new UserInstrumentLogos();
			// logoDetails[0] contails logoUrl and logoDetails[1] contains instrumentName
			List<String> logoDetails = LogoUtility.getUserLogoV2ForAddMoney(esDto);
			userInstrument.setSequence("1");
			if (CollectionUtils.isNotEmpty(logoDetails)) {
				userInstrument.setLogoUrl(logoDetails.get(0));
				userInstrument.setInstrumentName(logoDetails.get(1));
			}
			List<UserInstrumentLogos> userInstrumentLogos = new ArrayList<>();
			userInstrumentLogos.add(userInstrument);
			return userInstrumentLogos;
		}
		return null;
	}

	public static SecondPartyInfo getSecondPartyInfo(final TransformedTransactionHistoryDetail esDto,
			final Map<String, UserDetails> userIdImageUrlMapFromCache) {
		List<Logo> logoOrder = null;
		String logoUrl;
		List<Logo> logos;
		List<NameDetails> nameOrder = null;
		for (TransformedParticipant participant : esDto.getParticipants()) {
			// P2M AutoRefund Bug
			if (TransactionTypeEnum.P2M.getTransactionTypeKey().equals(esDto.getTxnType())
					&& (esDto.getEntityId().equals(participant.getEntityId())
							|| esDto.getTxnIndicator().equals(participant.getTxnIndicator()))) {
				continue;
			}
			// check introduced on transaction indicator for the case where all
			// participants have same id as that of esdto eg. self collect case
			if (!(esDto.getEntityId().equals(participant.getEntityId())
					&& esDto.getTxnIndicator().equals(participant.getTxnIndicator()))) {
				// logoUrl if present than set as_it_is else set it depending on
				// transaction type
				logos = GenericUtility.getListnDetailCommonLogos(participant, esDto, false, false,
						userIdImageUrlMapFromCache);
				if (logos != null && !logos.isEmpty()) {
					logoUrl = logos.get(0).getValue();
				}
				else {
					logoUrl = getLogoUrl(participant, esDto);
				}
				logoOrder = getListnDetailCommonLogos(participant, esDto, true, false, userIdImageUrlMapFromCache);
				logoOrder = GenericUtilityExtension.updateLogoOrder(esDto, logoOrder, participant, false);
				nameOrder = GenericUtilityExtension.updateNameDetails(esDto, nameOrder, participant, false);

				boolean isNameToBeSkipped = secondPartyNameExcludedForTxnTypes
					.containsKey(TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getMainTxnType()));
				String name = isNameToBeSkipped ? null : getSecondPartyName(esDto, participant);
				String secondPartyEntityId = esDto.getOtherPartyEntityId();
				;
				// both entityId should be different
				if (StringUtils.isBlank(secondPartyEntityId)
						&& !esDto.getEntityId().equals(participant.getEntityId())) {
					secondPartyEntityId = participant.getEntityId();
				}
				return new SecondPartyInfo(logoUrl, logoOrder, name, secondPartyEntityId, nameOrder);
			}
			else if ((esDto.getEntityId().equals(participant.getEntityId())
					&& esDto.getTxnIndicator().equals(participant.getTxnIndicator()))
					&& Boolean.TRUE.equals(esDto.getIsBankData())) {
				logoOrder = GenericUtilityExtension.updateLogoOrder(esDto, logoOrder, participant, true);
				nameOrder = GenericUtilityExtension.updateNameDetails(esDto, nameOrder, participant, true);
			}
		}
		// case where participant.size=1
		logos = GenericUtility.getListnDetailCommonLogos(null, esDto, false, false, userIdImageUrlMapFromCache);
		if (logos != null && !logos.isEmpty()) {
			logoUrl = logos.get(0).getValue();
			return new SecondPartyInfo(logoUrl, logoOrder, null, null, nameOrder);
		}
		return new SecondPartyInfo(GenericUtilityExtension.getLogoForTxnType(esDto), logoOrder, null, null, nameOrder);
	}

	public static String getSecondPartyName(final TransformedTransactionHistoryDetail esDto,
			final TransformedParticipant participant) {
		String name = formatString(participant.getName());
		// If uthNarration is present in cart details then set name with uthNarration
		if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
			if (Objects.nonNull(esDto.getCartDetails()) && CollectionUtils.isNotEmpty(esDto.getCartDetails().getItems())
					&& Objects.nonNull(esDto.getCartDetails().getItems().get(0))
					&& StringUtils.isNotBlank(esDto.getCartDetails().getItems().get(0).getUthNarration())) {
				name = esDto.getCartDetails().getItems().get(0).getUthNarration();
			}
		}
		return name;
	}

	public static List<Logo> getListnDetailCommonLogos(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail txn, final boolean isForLogoOrder, final boolean fetchUserLogo,
			final Map<String, UserDetails> userIdImageUrlMapFromCache) {
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(txn.getMainTxnType());
		if (txnType == null) {
			return null;
		}
		List<Logo> logoOrder = null;
		// write your specific code here
		switch (txnType) {
			case CASHBACK_RECEIVED:
				if (fetchUserLogo) {
					return LogoCreator.getCashBackLogosForUser(participant, txn, isForLogoOrder);
				}
				else {
					return LogoCreator.getCashBackLogosForSecondUser(participant, txn, isForLogoOrder);
				}
			case PPBL_TRANSACTION:
				if (fetchUserLogo) {
					return LogoCreator.getPpblLogosForUser(participant, txn, isForLogoOrder);
				}
				else {
					return LogoCreator.getPpblLogosForSecondUser(participant, txn, isForLogoOrder);
				}
				// Cases in which merchant entity is involved as second party are handled
				// here
			case WALLET_UPI_DEBIT_P2M:
			case P2M_REFUND:
			case ADD_AND_PAY:
			case P2M_INTERNATIONAL:
			case P2M_REVERSAL_INTERNATIONAL:
			case P2M: {
				if (!fetchUserLogo) {
					if (MandateUtility.txnValidForRecurring(txn)) {
						logoOrder = LogoCreator.getRecurringMandateLogosForSecondUser(participant, isForLogoOrder);
					}
					else {
						logoOrder = LogoCreator.getP2mLogosForSecondUser(participant, txn, isForLogoOrder);
					}
				}
				return logoOrder;
			}
			// P2P Cases for nameInitials and PhoneNumber
			case P2P_OUTWARD:
			case P2P_INWARD:
			case P2P_OUTWARD_REVERSAL:
			case WALLET_UPI_DEBIT_REVERSAL:
			case P2P_UPI_TO_WALLET_INWARD:
			case P2P_UPI_TO_WALLET_OUTWARD:
			case P2P2M:
			case P2P2M_INWARD:
			case P2P2M_OUTWARD:
			case P2P_INWARD_REMITTANCE:
			case P2P2M_REFUND:
			case P2P_OUTWARD_REMITTANCE:
			case P2P_OUTWARD_REMITTANCE_REFUND:
			case WALLET_UPI_DEBIT_P2P:
			case UPI_WALLET_CREDIT:
			case UPI_WALLET_CREDIT_REVERSAL: {
				// logoOrder for2nd user
				if (!fetchUserLogo && isForLogoOrder) {
					return LogoCreator.getLogoOrderDetails(participant, txn, userIdImageUrlMapFromCache);
				}
				return null;
			}
			case IPO_MANDATE:
			case ONE_TIME_MANDATE: {
				if (!fetchUserLogo) {
					logoOrder = LogoCreator.getIpoMandateLogosForSecondUser(participant, isForLogoOrder);
				}
				return logoOrder;
			}
			case SBMD_MANDATE:
			case RECURRING_MANDATE: {
				if (!fetchUserLogo) {
					logoOrder = LogoCreator.getRecurringMandateLogosForSecondUser(participant, isForLogoOrder);
				}
				return logoOrder;
			}
			default:
		}
		return null;
	}
	/*
	 * These Logo Functions will not be called for TxnTypes: 1. AddMoneyoBank 2. P2m where
	 * PG is involved (P2M UPI is sorted) 3. P2mRefund 4. P2p2m This is Because These
	 * Transaction Types can have multiple InstrumentDtos
	 */

	public static void setDtlRespWithListnDtlCommonLogos(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final DetailApiResponse detailApiResponse) {
		TransactionTypeEnum txnType = TransactionTypeEnum
			.getTransactionTypeEnumByKey(listingVisibleTxn.getMainTxnType());
		// TxnType is not in Map or detailApi response is null then return
		if (!IS_SEC_INST_DEBIT_INSTRUMENT.containsKey(txnType) || detailApiResponse == null) {
			return;
		}
		// flag to check that user's txnIndicator in debit or credit
		boolean isUserDebitUser = TransactionIndicator.DEBIT.getTransactionIndicatorKey()
			.equals(listingVisibleTxn.getTxnIndicator()) ? true : false;
		// For Logo Url
		String userLogo = null;
		String secondUserLogo = null;
		// for logoOrder
		List<Logo> userLogoOrder = null;
		List<Logo> secondUserLogoOrder = null;
		// Fetch Logos
		for (TransformedParticipant participant : listingVisibleTxn.getParticipants()) {
			if (listingVisibleTxn.getEntityId().equals(participant.getEntityId())
					&& listingVisibleTxn.getTxnIndicator().equals(participant.getTxnIndicator())) {
				// Fetch UserLogo
				List<Logo> logos = GenericUtility.getListnDetailCommonLogos(participant, listingVisibleTxn, false, true,
						null);
				if (logos != null && !logos.isEmpty()) {
					userLogo = logos.get(0).getValue();
				}
				// Fetch User LogoOrder
				userLogoOrder = GenericUtility.getListnDetailCommonLogos(participant, listingVisibleTxn, true, true,
						null);
			}
			else {
				// Fetch SecondPartyLogo
				List<Logo> logos = GenericUtility.getListnDetailCommonLogos(participant, listingVisibleTxn, false,
						false, null);
				if (logos != null && !logos.isEmpty()) {
					secondUserLogo = logos.get(0).getValue();
				}
				// Fetch SecondParty LogoOrder
				secondUserLogoOrder = GenericUtility.getListnDetailCommonLogos(participant, listingVisibleTxn, true,
						false, null);

			}
		}
		// In case a transaction has no secondParty Participant, So this block of code
		// fetch Logo Url for SecondParty
		if (StringUtils.isEmpty(secondUserLogo) && listingVisibleTxn.getParticipants() != null
				&& listingVisibleTxn.getParticipants().size() < 2) {
			List<Logo> logos = GenericUtility.getListnDetailCommonLogos(null, listingVisibleTxn, false, false, null);
			if (logos != null && !logos.isEmpty()) {
				secondUserLogo = logos.get(0).getValue();
			}
		}
		// Special handling for PPBL cases
		// As for PPBL Cases, User Instrument is always shown in SecondInstrument in
		// Detail and
		// SecondParty Logo is shown in FirstInstrument in Detail
		if (TransactionTypeEnum.PPBL_TRANSACTION.getTransactionTypeKey().equals(listingVisibleTxn.getMainTxnType())
				&& TransactionSource.PPBL.getTransactionSourceKey().equals(listingVisibleTxn.getStreamSource())) {
			if (detailApiResponse.getSecondInstrument() != null && !detailApiResponse.getSecondInstrument().isEmpty()
					&& StringUtils.isNotBlank(userLogo)) {
				detailApiResponse.getSecondInstrument().get(0).setLogoUrl(userLogo);
			}
			if (detailApiResponse.getFirstInstrument() != null && !detailApiResponse.getFirstInstrument().isEmpty()
					&& StringUtils.isNotBlank(secondUserLogo)) {
				detailApiResponse.getFirstInstrument().get(0).setLogoUrl(secondUserLogo);
			}
			return;
		}
		// Set Response here
		// Setting logo Url and Logo Order in Second Instrument in Detail Response
		if (detailApiResponse.getSecondInstrument() != null && !detailApiResponse.getSecondInstrument().isEmpty()) {
			if ((IS_SEC_INST_DEBIT_INSTRUMENT.get(txnType) && isUserDebitUser)
					|| (!IS_SEC_INST_DEBIT_INSTRUMENT.get(txnType) && !isUserDebitUser)) {
				if (StringUtils.isNotBlank(userLogo)) {
					detailApiResponse.getSecondInstrument().get(0).setLogoUrl(userLogo);
				}
				// Setting logoOrder in User Instrument
				detailApiResponse.getSecondInstrument().get(0).setLogoOrder(userLogoOrder);
			}
			else {
				if (StringUtils.isNotBlank(secondUserLogo)) {
					detailApiResponse.getSecondInstrument().get(0).setLogoUrl(secondUserLogo);
				}
				// Setting logoOrder in Second User's Instrument
				detailApiResponse.getSecondInstrument().get(0).setLogoOrder(secondUserLogoOrder);
			}
		}
		// Setting logo Url and Logo Order in First Instrument in Detail Response
		if (detailApiResponse.getFirstInstrument() != null && !detailApiResponse.getFirstInstrument().isEmpty()) {
			if ((IS_SEC_INST_DEBIT_INSTRUMENT.get(txnType) && isUserDebitUser)
					|| (!IS_SEC_INST_DEBIT_INSTRUMENT.get(txnType) && !isUserDebitUser)) {
				if (StringUtils.isNotBlank(secondUserLogo)) {
					detailApiResponse.getFirstInstrument().get(0).setLogoUrl(secondUserLogo);
				}
				// Setting logoOrder in Second User's Instrument
				detailApiResponse.getFirstInstrument().get(0).setLogoOrder(secondUserLogoOrder);
			}
			else {
				if (StringUtils.isNotBlank(userLogo)) {
					detailApiResponse.getFirstInstrument().get(0).setLogoUrl(userLogo);
				}
				// Setting logoOrder in User's Instrument
				detailApiResponse.getFirstInstrument().get(0).setLogoOrder(userLogoOrder);
			}
		}
	}

	public static void listingVsDtlLogoUrlLog(final List<TransformedTransactionHistoryDetail> detailList,
			final String txnId, final DetailApiResponse detailApiResponse) {
		TransformedTransactionHistoryDetail tthdForListingLogo = null;
		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (!StringUtils.isEmpty(txnId) && txnId.equalsIgnoreCase(detail.getTxnId())
					&& detail.getShowInListing() != null && detail.getShowInListing().booleanValue()) {
				tthdForListingLogo = detail;
				break;
			}
		}
		if (tthdForListingLogo == null) {
			log.warn("TxnId : {} , Show in listing is false in all txns in detailList", txnId);
			return;
		}
		String secondPartyLogoListing = null;
		SecondPartyInfo secondPartyInfo = GenericUtility.getSecondPartyInfo(tthdForListingLogo, null);
		if (secondPartyInfo != null && !StringUtils.isEmpty(secondPartyInfo.getLogoUrl())) {
			secondPartyLogoListing = secondPartyInfo.getLogoUrl();
		}

		if (StringUtils.isEmpty(secondPartyLogoListing) && TransactionTypeEnum.PPBL_TRANSACTION.getTransactionTypeKey()
			.equals(tthdForListingLogo.getMainTxnType())) {
			secondPartyLogoListing = fetchLogoFromReportCode(tthdForListingLogo);
		}
		List<String> userLogosListing = null;
		List<UserInstrumentLogos> userInstrumentLogos = GenericUtility.getUserInstrumentLogos(tthdForListingLogo);
		if (userInstrumentLogos != null && !userInstrumentLogos.isEmpty()) {
			userLogosListing = new ArrayList<>();
			for (UserInstrumentLogos instLogo : userInstrumentLogos) {
				if (instLogo != null && !StringUtils.isEmpty(instLogo.getLogoUrl())) {
					userLogosListing.add(instLogo.getLogoUrl());
				}
			}
		}

		if (detailApiResponse.getSecondInstrument() != null && !detailApiResponse.getSecondInstrument().isEmpty()) {
			List<String> userLogosDetail = new ArrayList<>();
			for (InstrumentDto instrumentDto : detailApiResponse.getSecondInstrument()) {
				if (!StringUtils.isEmpty(instrumentDto.getLogoUrl())) {
					userLogosDetail.add(instrumentDto.getLogoUrl());
				}
			}
			log.debug(
					"For TxnId : {} , TxnType : {} , and StreamSource : {} , User's Detail Logo : {} and User's Listing logo : {}",
					tthdForListingLogo.getTxnId(), tthdForListingLogo.getMainTxnType(),
					tthdForListingLogo.getStreamSource(), userLogosDetail, userLogosListing);
		}
		if (detailApiResponse.getFirstInstrument() != null && !detailApiResponse.getFirstInstrument().isEmpty()) {
			log.debug(
					"For TxnId : {} , TxnType : {} , and StreamSource : {} , SecondParty's Detail Logo : {} and SecondParty's Listing logo : {}",
					tthdForListingLogo.getTxnId(), tthdForListingLogo.getMainTxnType(),
					tthdForListingLogo.getStreamSource(), detailApiResponse.getFirstInstrument().get(0).getLogoUrl(),
					secondPartyLogoListing);
		}
	}

	// Function Specially For Logger, will remove it when we will remove logger
	private static String fetchLogoFromReportCode(final TransformedTransactionHistoryDetail tthd) {
		EsResponseTxn esResponseTxn = new EsResponseTxn();
		esResponseTxn.setStatus(getStatusLabel(ClientStatusEnum.getStatusEnumByKey(tthd.getStatus())));
		SecondPartyInfo secondPartyInfo = new SecondPartyInfo();
		esResponseTxn.setSecondPartyInfo(secondPartyInfo);
		ListingUtility.setBankData(tthd, esResponseTxn.getSecondPartyInfo(), esResponseTxn.getStatus(), null, false,
				esResponseTxn, true);
		return secondPartyInfo.getLogoUrl();
	}

	public static RptCodeConfig getReportCodeConfig(final BankConfig bankConfig, final String reportCode,
			final TransformedTransactionHistoryDetail tthd, final Map<String, String> contextMap) {
		RptCodeConfig rptCodeConfig = null;
		// https://wiki.mypaytm.com/x/HypjHQ. Point no: 6
		if (("20502".equals(reportCode) && "add-money@paytm".equals(contextMap.get("benefAcctNum")))
				|| ("60202".equals(reportCode) && "AddMoneyWallet".equals(contextMap.get("merchantId")))) {
			rptCodeConfig = new RptCodeConfig();
			rptCodeConfig.setDetailNarration("Money Added");
			rptCodeConfig.setInstrumentNarration("To Your");
			rptCodeConfig.setInstrumentName("Wallet");
			rptCodeConfig.setImage("#{walletUrl}paytm-wallet.png");
			rptCodeConfig.setDesc1("Reference No. : #{rrn_default}");
		}
		else {
			rptCodeConfig = GenericUtility.fetchRptCodeConfig(bankConfig, reportCode, tthd.getTxnIndicator());
		}
		return rptCodeConfig;
	}

	private static String getLogoUrl(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail esDto) {

		String logoUrl = LogoUtility.getLogoHelper(participant);
		if (StringUtils.isNotBlank(logoUrl)) {
			return logoUrl;
		}
		Logo logo = LogoUtility.checkAndGetLogoByVpa(participant, esDto);
		if (Objects.nonNull(logo)) {
			logoUrl = logo.getValue();
			return logoUrl;
		}
		return GenericUtilityExtension.getLogoForTxnType(esDto);
	}

	public static String getDateLabel(final String timeStr) {
		return DateTimeUtility.getDateTime(timeStr).substring(0, 10);
	}

	public static String getTimeLabel(final String timeStr) {
		return DateTimeUtility.getDateTime(timeStr).substring(11);
	}

	public static List<String> getInstrumentLogo(final TransformedTransactionHistoryDetail txn,
			final TransformedParticipant participant) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		String logoUrl = null;
		String instrumentName = null;
		log.debug("trying to get user instrument logo for participant entityId {}", participant.getEntityId());
		try {
			if (paymentSystem == null) {
				log.info("null -  payment system while getting instrument Logo for listing");
				return null;
			}
			switch (paymentSystem) {
				case WALLET:
					if (participant.getWalletData() != null) {
						logoUrl = LogoUtility.getLogo(
								WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()),
								LogoType.WALLET_ICON);
						instrumentName = WalletTypesEnum
							.getWalletDisplayName(participant.getWalletData().getWalletType());
					}
					else {
						logoUrl = LogoUtility.getLogo(WalletTypesEnum.SCLW.getDisplayName(), LogoType.WALLET_ICON);
						instrumentName = WalletTypesEnum.SCLW.getDisplayName();
					}
					break;
				case UPI:
					if (Utility.isUpiViaCcTxn(participant)) {
						logoUrl = LogoUtility.getCardLogo(participant.getCardData().getCardNetwork());

						if (StringUtils.isNotBlank(participant.getCardData().getCardNetwork())) {
							instrumentName = WordUtils.capitalizeFully(participant.getCardData().getCardNetwork())
									+ SPACE + CARD;
						}
						else {
							instrumentName = CREDIT_CARD;
						}
					}
					else if (Objects.nonNull(participant.getBankData())) {
						logoUrl = LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
								participant.getBankData().getBankName());

						if (StringUtils.isNotBlank(participant.getBankData().getBankName())) {
							instrumentName = participant.getBankData().getBankName();
						}
						else {
							if (Objects.nonNull(txn)
									&& TransactionSource.OMS.getTransactionSourceKey().equals(txn.getStreamSource())
									&& TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey()
										.equals(txn.getTxnType())) {
								logoUrl = LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER);
							}
							instrumentName = StringUtils.isNotBlank(participant.getBankData().getIfsc())
									? IfscUtility.getBank(participant.getBankData().getIfsc()) : BHIM_UPI_NAME;
						}
					}
					else {
						logoUrl = LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER);
						instrumentName = BHIM_UPI_NAME;
					}
					break;
				case PG:
				case EMI:
				case PAYTM_CASH:
				case NET_BANKING:
				case BANK_MANDATE:
				case LOYALTY_POINT:
				case EMI_DEBIT_CARD:
				case BANK:
					if (participant.getCardData() != null) {
						logoUrl = LogoUtility.getBankLogo(null, participant.getCardData().getCardIssuer());
						instrumentName = participant.getCardData().getCardIssuer();
					}
					else if (participant.getBankData() != null) {
						logoUrl = LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
								participant.getBankData().getBankName());

						if (StringUtils.isNotBlank(participant.getBankData().getBankName())) {
							instrumentName = participant.getBankData().getBankName();
						}
						else {
							instrumentName = StringUtils.isNotBlank(participant.getBankData().getIfsc())
									? IfscUtility.getBank(participant.getBankData().getIfsc()) : LINKED_BANK_ACCOUNT;
						}
					}
					else if (participant.getUpiData() != null) {
						logoUrl = LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER);
						instrumentName = BHIM_UPI_NAME;
					}
					else {
						// set logo url as default bank logo.
						logoUrl = LogoUtility.getBankLogo(null, null);
						instrumentName = LINKED_BANK_ACCOUNT;
					}
					break;
				case PAYTM_POSTPAID:
					logoUrl = LogoUtility.getLogo(POSTPAID_LOGO, LogoType.WALLET_ICON);
					instrumentName = POSTPAID_LOAN;
					break;
				case MGV:
				case OTHER:
					if (participant.getOtherData() != null) {
						if (StringUtils.isEmpty(participant.getOtherData().getLogoUrl())) {
							logoUrl = LogoUtility.getLogo(WalletTypesEnum.GIFT_VOUCHER.getDisplayName(),
									LogoType.WALLET_ICON);
							instrumentName = WalletTypesEnum.GIFT_VOUCHER.getDisplayName();
						}
						else {
							logoUrl = participant.getOtherData().getLogoUrl();
							instrumentName = ACCOUNT_NAME;
						}
					}
					break;
				case GV:
					logoUrl = LogoUtility.getLogo(GIFT_VOUCHER, LogoType.GIFT_VOUCHER_ICON);
					instrumentName = LocalizedDataCacheService
						.getLocalizedValue(LocaleMsgConstants.DETAIL_INSTRUMENT_NAME_GIFT_CARD);
					break;
				case PPBL:
				case TS:
					logoUrl = LogoUtility.getLogo(PPBL_CONSTANT_LOGO, LogoType.TRANSACTION_CATEGORY_ICON);
					instrumentName = ACCOUNT_NAME;
					break;
				case STORE_CASH:
					logoUrl = LogoUtility.getLogo(STORECASH_LOGO, LogoType.STORECASH_ICON);
					instrumentName = LocalizedDataCacheService
						.getLocalizedValue(LOCALIZATION_KEY_STORECASH_DISPLAY_NAME, PAYTM_STORECASH, ENGLISH_LOCALE);
					break;
				default:
					log.error("unexpected payment system while getting instrument Logo for listing");
			}
			List<String> logoDetails = new ArrayList<>();
			logoDetails.add(logoUrl);
			logoDetails.add(instrumentName);
			return logoDetails;
		}
		catch (Exception e) {
			log.error(" error while getting user instrument for participant entity Id :{}, payment Txn Id: {}",
					participant.getEntityId(), participant.getPaymentTxnId());
			return null;
		}
	}

	public static String getBankNarration(final String reportCode, final Integer txnIndic, final String status,
			final String apiIdentifier) {
		char indic;
		if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(txnIndic)) {
			indic = 'C';
		}
		else {
			indic = 'D';
		}
		Pair<String, ClientStatusEnum> rptCodeStatusPair = new Pair<>(reportCode + "_" + indic,
				ClientStatusEnum.getStatusEnumByValue(status.toLowerCase()));
		if (reportCodeStatusNarrationMap.containsKey(rptCodeStatusPair)) {
			if (apiIdentifier.equals(LISTING)) {
				return reportCodeStatusNarrationMap.get(rptCodeStatusPair).getKey();
			}
			else {
				return reportCodeStatusNarrationMap.get(rptCodeStatusPair).getValue();
			}
		}
		return null;
	}

	public static String getNarrationLabel(final TransformedTransactionHistoryDetail detail,
			final ClientStatusEnum status, final EsResponseTxn esResponseTxn) {
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(detail.getMainTxnType());
		String purpose = getPurposeLabel(detail, txnType);
		// p2p2m_credit case narration.
		if (TransactionTypeEnum.P2P2M.equals(txnType)) {
			TransactionIndicator txnIndicator = TransactionIndicator
				.getTransactionIndicatorEnumByKey(Integer.valueOf((getTxnIndicator(detail))));
			switch (txnIndicator) {
				case CREDIT:
					esResponseTxn.setListingResponseMappingEnum(ListingResponseMappingEnum.P2P2M_INWARD);
					return "Received from";
				case DEBIT:
					esResponseTxn.setListingResponseMappingEnum(ListingResponseMappingEnum.P2P2M_OUTWARD);
					break;
				default:
			}
		}
		else if (TransactionTypeEnum.WALLET_SETTLEMENT.equals(txnType)) {
			return "Settlement Received from Paytm";
		}
		else if (TransactionTypeEnum.P2P_OUTWARD.equals(txnType) && !StringUtils.isEmpty(purpose)
				&& purpose.equalsIgnoreCase(GV_PURCHASED)) {
			esResponseTxn.setListingResponseMappingEnum(ListingResponseMappingEnum.P2P_OUTWARD_GIFT_VOUCHER);
			return "Gift Voucher sent to";
		}
		else if (TransactionTypeEnum.P2P_INWARD.equals(txnType) && !StringUtils.isEmpty(purpose)
				&& purpose.equalsIgnoreCase(GV_REDEEM)) {
			esResponseTxn.setListingResponseMappingEnum(ListingResponseMappingEnum.P2P_INWARD_GIFT_VOUCHER);
			return "Gift Voucher Received from";
		}
		else if (TransactionTypeEnum.ADD_MONEY.equals(txnType) && GV_PURCHASED.equalsIgnoreCase(purpose)) {
			return "Add Money to";
		}
		else if (TransactionTypeEnum.P2P_OUTWARD_REMITTANCE.equals(txnType)) {
			return "Paid for Outward Remittance to";
		}
		else if (TransactionTypeEnum.P2P_OUTWARD_REMITTANCE_REFUND.equals(txnType)) {
			return "Refund of Outward Remittance sent to";
		}
		else if (TransactionTypeEnum.UPI_WALLET_CREDIT_REVERSAL.equals(txnType)) {
			return "Reversal of money received from";
		}
		String genericNarration = getNarrationByTthd(detail, status);

		if (StringUtils.isNotBlank(genericNarration)) {
			return OMS_NARRATION.equals(genericNarration) ? "" : genericNarration;
		}

		// handling only activation and deactivation case of UPI Wallet
		if (TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.equals(txnType)
				|| TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.equals(txnType)) {
			return UpiLiteViewUtility.getListingNarrationByPurposeCode(detail, esResponseTxn);
		}

		Pair<TransactionTypeEnum, ClientStatusEnum> txnTypeStatusPair = new Pair<>(txnType, status);

		if (transactionTypeStatusNarrationMap.get(txnTypeStatusPair) != null) {
			return transactionTypeStatusNarrationMap.get(txnTypeStatusPair);
		}
		else {
			return "";
		}
	}

	public static String getPurposeLabel(final TransformedTransactionHistoryDetail detail) {
		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(detail.getMainTxnType());
		return getPurposeLabel(detail, txnType);
	}

	public static String getPurposeLabel(final TransformedTransactionHistoryDetail detail,
			final TransactionTypeEnum txnType) {
		String purpose = getTxnPurpose(detail);
		if (TransactionTypeEnum.P2P_OUTWARD.equals(txnType)) {
			purpose = getTxnPurpose(detail.getParticipants(), TransactionIndicator.DEBIT.getTransactionIndicatorKey());
		}
		else if (TransactionTypeEnum.P2P_INWARD.equals(txnType)) {
			purpose = getTxnPurpose(detail.getParticipants(), TransactionIndicator.CREDIT.getTransactionIndicatorKey());
		}
		if (TransactionSource.isPgTypeSource(detail.getStreamSource())
				&& (PAYTM_GIFT_VOUCHER.equalsIgnoreCase(purpose) || PAYTMGIFTVOUCHER.equalsIgnoreCase(purpose))) {
			purpose = GV_PURCHASED; // it is because pg send "Paytm Gift Voucher" as
									// purpose
		}
		return purpose;
	}
	/*
	 * This method uses txnType, streamSource, txnIndicator, merchantType, and status as a
	 * filter to return narration from narration Map
	 */

	public static String getNarrationByTthd(final TransformedTransactionHistoryDetail detail,
			final ClientStatusEnum status) {

		TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(detail.getMainTxnType());
		TransactionSource streamSource = TransactionSource.getTransactionSourceEnumByKey(detail.getStreamSource());
		TransactionIndicator txnIndicator = TransactionIndicator
			.getTransactionIndicatorEnumByKey(detail.getTxnIndicator());

		MerchantTypeEnum merchantType = null;
		String merchantName = null;

		for (TransformedParticipant participant : detail.getParticipants()) {
			if (Objects.nonNull(participant)
					&& EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
				if (Objects.nonNull(participant.getMerchantData())) {
					merchantType = MerchantTypeEnum
						.getMerchantTypeByKey(participant.getMerchantData().getMerchantType());
					merchantName = participant.getName();
				}
				break;
			}
		}
		NarrationMapperDto narrationMapperDto = NarrationMapperDto.builder()
			.streamSource(streamSource)
			.txnType(txnType)
			.txnIndicator(txnIndicator)
			.merchantType(merchantType)
			.clientStatus(status)
			.build();
		return getNarrationBasedOnCartDetails(detail, merchantName, narrationMapperDto);
	}

	private static String getNarrationBasedOnCartDetails(final TransformedTransactionHistoryDetail detail,
			final String merchantName, final NarrationMapperDto narrationMapperDto) {
		String narration = getNarrationFromMap(narrationMapperDto);
		if (StringUtils.isNotBlank(narration) && detail.getCartDetails() != null
				&& detail.getCartDetails().getItems() != null && detail.getCartDetails().getItems().size() > 0) {
			if (StringUtils.isNotBlank(detail.getCartDetails().getItems().get(0).getUthNarration())) {
				return OMS_NARRATION;
			}
			else if (StringUtils.isNotBlank(detail.getCartDetails().getItems().get(0).getName())
					&& detail.getCartDetails().getItems().get(0).getName().equals(merchantName)) {
				if (StringUtils.isNotBlank(detail.getCartDetails().getItems().get(0).getVerticalId())
						&& nonOmsNarrationCartVerticalIds
							.contains(detail.getCartDetails().getItems().get(0).getVerticalId())) {
					return narration;
				}
				return OMS_NARRATION;
			}
		}
		return narration;
	}
	/*
	 * This method accepts NarrationMapperDTO object and returns narration from narration
	 * map using NarrationMapperDTO object as a key
	 */

	private static String getNarrationFromMap(final NarrationMapperDto narrationMapperDto) {
		return narrationMap.get(narrationMapperDto);
	}

	public static TransformedParticipant getParticipantForGv(final TransformedTransactionHistoryDetail detail,
			final Integer txnIndicator) {
		if (detail != null) {
			Optional<TransformedParticipant> requiredParticipant = detail.getParticipants()
				.stream()
				.filter(participant -> txnIndicator.equals(participant.getTxnIndicator()))
				.findFirst();
			return requiredParticipant.orElse(null);
		}
		return null;
	}

	public static void setDetailsFromUpiData(final InstrumentDto instrumentDto,
			final TransformedParticipant participant) {
		if (participant != null && participant.getUpiData() != null) {
			instrumentDto.setName(participant.getUpiData().getVpa());
			instrumentDto.setLogoUrl(LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER));
		}
	}

	public static void setDetailsFromBankData(final InstrumentDto instrumentDto,
			final TransformedParticipant participant) {
		if (participant != null && participant.getBankData() != null) {
			instrumentDto.setName(participant.getBankData().getBankName());
			if (!StringUtils.isBlank(participant.getBankData().getAccNumber())) {
				instrumentDto.setInstrumentDetail(AC_NO + participant.getBankData().getAccNumber());
			}
			else {
				instrumentDto.setInstrumentDetail(VIA_NET_BANKING);
			}
			instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
					participant.getBankData().getBankName()));
		}
	}

	public static void setDetailsFromCardData(final InstrumentDto instrumentDto,
			final TransformedParticipant participant) {
		if (participant != null && participant.getCardData() != null) {
			instrumentDto.setInstrumentDetail(CARD_NO + " " + participant.getCardData().getCardNum());
			String cardType = CardType.CREDIT.getCardTypeKey().equals(participant.getCardData().getCardType())
					? CREDIT_CARD : DEBIT_CARD;
			instrumentDto.setName(participant.getCardData().getCardIssuer() + " " + cardType);
			instrumentDto.setLogoUrl(LogoUtility.getBankLogo(null, participant.getCardData().getCardIssuer()));
		}
	}

	public static String formatString(final String passedName) {
		String name = passedName;
		if ("SCLW".equalsIgnoreCase(name)) {
			return WALLET; // specific handling to show Paytm Wallet
		}
		// this change is regarding
		// Camel case should only be done in case all the letters are in either caps or
		// small case.
		// But in case there is only one word and that is in all caps, we won't do
		// camelcasing
		Integer numberOfWords = countNumberOfWords(name);
		// no need to process further if string is blank
		if (numberOfWords == 0) {
			return name;
		}
		String stringOfOnlyAlphabets = name.replaceAll("[^A-Za-z]", "");
		Boolean isAllCaps = CharMatcher.javaUpperCase().matchesAllOf(stringOfOnlyAlphabets);
		Boolean isAllLowerCase = CharMatcher.javaLowerCase().matchesAllOf(stringOfOnlyAlphabets);
		if (isAllCaps && numberOfWords == 1) {
			return name;
		}
		if (isAllCaps || isAllLowerCase) {
			return WordUtils.capitalizeFully(StringUtils.normalizeSpace(name));
		}
		return name;
	}

	public static Integer countNumberOfWords(final String passedString) {
		if (StringUtils.isBlank(passedString)) {
			return 0;
		}
		String[] numberOfWords = passedString.split("\\s+");
		return numberOfWords.length;
	}

	public static List<String> convertRefIdMapToList(final Map<String, String> map) {
		List<String> list = new ArrayList<>();
		map.values().removeIf(value -> StringUtils.isEmpty(value) || "0".equals(value.trim()));
		for (Entry<String, String> entry : map.entrySet()) {
			list.add(entry.getKey() + ": " + entry.getValue());
		}
		return list;
	}

	public static String getStatusLabel(final ClientStatusEnum statusEnum) {
		String statusString;
		switch (statusEnum) {
			case FAILURE:
				statusString = FAILED;
				break;
			case SUCCESS:
				statusString = SUCCESS;
				break;
			default:
				statusString = PENDING;
				break;
		}
		return statusString;
	}

	public static String getStatus(final TransformedTransactionHistoryDetail detailNeededForTxn,
			final List<TransformedTransactionHistoryDetail> detailList) {
		if (Boolean.TRUE.equals(detailNeededForTxn.getIsSource())) {
			return ClientStatusEnum.getStatusEnumByKey(detailNeededForTxn.getStatus()).toString();
		}
		else {
			TransformedTransactionHistoryDetail parentTxn = null;
			for (TransformedTransactionHistoryDetail detail : detailList) {
				if (Boolean.TRUE.equals(detail.getIsSource())) {
					parentTxn = detail;
					break;
				}
			}
			return getStatus(detailNeededForTxn, parentTxn);
		}
	}

	public static String getStatus(final TransformedTransactionHistoryDetail currentTxn,
			final TransformedTransactionHistoryDetail parentTxn) {
		if (ClientStatusEnum.FAILURE.getStatusKey().equals(currentTxn.getStatus()) && parentTxn == null) {
			return ClientStatusEnum.FAILURE.toString();
		}
		return ClientStatusEnum.getStatusEnumByKey(currentTxn.getStatus()).toString();

		/*
		 * if (parentTxn == null) { return ClientStatusEnum.PENDING.toString(); } else {
		 * return ClientStatusEnum.getStatusEnumByKey(parentTxn.getStatus()).toString(); }
		 */
	}

	public static ClientStatusEnum getStatusForListing(final TransformedTransactionHistoryDetail esDto) {
		return getStatusForListing(esDto, true);
	}

	public static ClientStatusEnum getStatusForListing(final TransformedTransactionHistoryDetail esDto,
			final boolean showMisMatchLog) {
		// Handling historical data in which originalStatus is null
		if (Objects.isNull(esDto.getOriginalStatus())) {
			return Utility.getViewStatusFormTthd(esDto);
		}
		// getting Existing View Status which is set at Ingester level
		ClientStatusEnum existingViewStatus = ClientStatusEnum.getStatusEnumByKey(esDto.getStatus());
		// getting derived view status by original status of stream source
		esDto.setStatus(esDto.getOriginalStatus());
		ClientStatusEnum derivedViewStatus = Utility.getViewStatusFormTthd(esDto);
		esDto.setStatus(existingViewStatus.getStatusKey());
		// if Existing View status and derived status are equal then return existing view
		// status
		if (existingViewStatus.equals(derivedViewStatus)) {
			return existingViewStatus;
		}
		// Flag to show status misMatch log or not.
		if (showMisMatchLog) {
			// if Existing View status and derived status are not equal then return
			// derived status and log for monitoring
			ClientStatusEnum originalStatus = ClientStatusEnum.getStatusEnumByKey(esDto.getOriginalStatus());
			TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getMainTxnType());
			TransactionSource source = TransactionSource.getTransactionSourceEnumByKey(esDto.getStreamSource());

			log.warn(
					"Mismatch b/w existing view status and derived view status TxnId: {}, txnType: {}, Source: {}, "
							+ "ExistingStatus: {}, DerivedStatus: {}, Original Status: {}",
					esDto.getTxnId(), txnType, source, existingViewStatus, derivedViewStatus, originalStatus);
		}
		// UPI Internation, case : psp - other & bank - Paytm
		// special handling for UPI
		if (UpiInternationalUtility.isUpiInternationalAndOtherPspUpiEvent(esDto)
				|| UpiLiteUtility.isUpiLiteTxn(esDto)) {
			return existingViewStatus;
		}
		return derivedViewStatus;
	}

	public static String getDroolsMaskedMobileNumber(final String passedMobileNumber) {
		if (StringUtils.isEmpty(passedMobileNumber)) {
			return passedMobileNumber;
		}
		return "%{MobNum : " + passedMobileNumber + "}";
	}

	public static String getTxnIndicator(final TransformedTransactionHistoryDetail esDto) {
		TransactionIndicator txnIndicator = Utility.getUthVisibleTransactionIndicator(esDto);
		return txnIndicator != null ? txnIndicator.getTransactionIndicatorKey().toString() : null;
	}
	/*
	 * This method is filters participants on basis of primary following
	 * (PaymentSystemValue, TxnIndicator and type of Wallet if it is PaymentSystemValue) A
	 * participant is duplicate if participant's key is already present in participant map
	 * and it's updatedTime is less then the value (updatedTime) present in map for that
	 * key. this will mean we have already encountered the recent participant of same
	 * type.
	 *
	 * We are using Wallet type in key because paymentSystem can be wallet but
	 * distinguishing factor will be WalletType
	 */

	public static boolean isParticipantDuplicate(final Map<String, Long> participantMap,
			final TransformedParticipant participant) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		Long updateTime = Long.valueOf(participant.getUpdatedDate());
		String type = null;
		if (PaymentSystemEnum.WALLET.getPaymentSystemValue().equals(paymentSystem.getPaymentSystemValue())) {
			type = (participant.getWalletData() != null && participant.getWalletData().getWalletType() != null)
					? participant.getWalletData().getWalletType().toString() : null;
		}
		String[] stringArray = { paymentSystem.getPaymentSystemValue(), type,
				participant.getTxnIndicator().toString() };
		String key = StringUtils.join(stringArray, "_");
		if (!participantMap.containsKey(key)) {
			participantMap.put(key, updateTime);
			return false;
		}
		else {
			Long existingUpdateTime = participantMap.get(key);
			if (existingUpdateTime >= updateTime) {
				return true;
			}
			else {
				participantMap.put(key, updateTime);
				return false;
			}
		}
	}

	// Method to sort Participants based on different criteria
	public static void sortParticipants(final TransformedTransactionHistoryDetail tthd) {
		if (GenericUtilityExtension.isStoreCashTxn(tthd)) {
			// sort by Updated date first then apply shifting Mlp to last index of array
			sortParticipantsByUpdatedDate(tthd.getParticipants());
			moveStoreCashParticipantToLastIndex(tthd.getParticipants());
		}
		else {
			sortParticipantsByUpdatedDate(tthd.getParticipants());
		}

	}
	/*
	 * This method is to sort participants on the basis of UpdatedDate, participant with
	 * greatest updatedDate will be first, and participant with smallest updatedDate will
	 * be last. this method compares UpdatedDates(epoch) as integers
	 */

	public static void sortParticipantsByUpdatedDate(final List<TransformedParticipant> participants) {
		Comparator<TransformedParticipant> c = new Comparator<TransformedParticipant>() {
			@Override
			public int compare(final TransformedParticipant o1, final TransformedParticipant o2) {
				return o2.getUpdatedDate().compareTo(o1.getUpdatedDate());
			}
		};
		Collections.sort(participants, c);
	}

	public static void moveStoreCashParticipantToLastIndex(final List<TransformedParticipant> participants) {
		TransformedParticipant storeCashParticipant;
		int storeCashParticipantIdx = Iterables.indexOf(participants,
				participant -> PaymentSystemEnum.STORE_CASH.getPaymentSystemKey()
					.equals(participant.getPaymentSystem()));
		if (storeCashParticipantIdx >= 0) {
			storeCashParticipant = participants.remove(storeCashParticipantIdx);
			participants.add(storeCashParticipant);
		}
	}

	public static String getNarration(final TransformedParticipant participant, final String parentEntityId,
			final String customNarrationType) {
		if (!LISTING_CUSTOM_NARRATION.equals(customNarrationType)
				&& !DETAILS_CUSTOM_NARRATION.equals(customNarrationType)) {
			return null;
		}
		if (parentEntityId.equals(participant.getEntityId())) {
			String narration = (participant.getContextMap() == null) ? null
					: participant.getContextMap().get(customNarrationType);
			if (!StringUtils.isAllBlank(narration)) {
				if (LISTING_CUSTOM_NARRATION.equals(customNarrationType)
						&& participant.getContextMap().containsKey(LISTING_CUSTOM_NARRATION_NAME)) {
					String replacement = participant.getContextMap().get(LISTING_CUSTOM_NARRATION_NAME);
					narration = narration.replaceAll(DYNAMIC_IDENTIFIER, replacement);
				}
				return narration;
			}
		}
		return null;
	}

	public static RepeatPayment getRepeatPaymentDetails(final Map<String, Boolean> fieldsInUrl, final String passedUrl,
			final TransformedParticipant participant, final TransformedTransactionHistoryDetail txn,
			final String urlName) {
		String url = passedUrl;
		try {
			RepeatPayment repeatPayment = new RepeatPayment();
			repeatPayment.setName(urlName);
			Pattern pattern = Pattern.compile(PATTERN_STRING);
			Matcher matcher = pattern.matcher(url);
			while (matcher.find()) {
				String valueToReplace = url.substring(matcher.start(), matcher.end());
				boolean isMandatory = fieldsInUrl.get(valueToReplace);
				String valueCorrespondingToParam = GenericUtilityExtension.getValueForInputParamForUrl(valueToReplace,
						participant, txn);
				valueToReplace = PERCENT_OPEN_BRACE_REGEX + valueToReplace.substring(2, valueToReplace.length() - 1)
						+ CLOSE_BRACE_REGEX;
				if (isMandatory) {
					if (Objects.isNull(valueCorrespondingToParam)) {
						return null;
					}
					else {
						valueCorrespondingToParam = valueCorrespondingToParam.replaceAll(SPACE, SPACE_REPLACEMENT);
						url = url.replaceAll(valueToReplace, valueCorrespondingToParam);
					}
				}
				else {
					if (!Objects.isNull(valueCorrespondingToParam)) {
						valueCorrespondingToParam = valueCorrespondingToParam.replaceAll(SPACE, SPACE_REPLACEMENT);
						url = url.replaceAll(valueToReplace, valueCorrespondingToParam);
					}
					else {
						StringBuilder substringToDelete = new StringBuilder();
						String inputParamValue = getInputParamValue(valueToReplace);
						substringToDelete.append(AMPERSAND)
							.append(inputParamValue)
							.append(EQUAL_SYMBOL)
							.append(valueToReplace);
						url = url.replaceAll(substringToDelete.toString(), EMPTY_STRING);
					}
				}
				matcher = pattern.matcher(url);
			}
			repeatPayment.setUrl(url);
			return repeatPayment;
		}
		catch (Exception e) {
			log.error("Some error occurred while getting repeat payment url details : {}",
					CommonsUtility.exceptionFormatter(e));
			return null;
		}
	}

	private static String getInputParamValue(final String valueToReplace) {
		switch (valueToReplace) {
			case REPLACABLE_BENEF_BANK_NAME: {
				return RPT_PYMT_BANK_NAME;
			}
			default:
				return valueToReplace.substring(3, valueToReplace.length() - 2);
		}
	}

	public static RptCodeConfig fetchRptCodeConfig(final BankConfig bankConfig, final String reportCode,
			final Integer txnIndicator) {
		if (bankConfig == null) {
			return null;
		}
		RptCodeConfig rptCodeConfig = bankConfig.getRptCodeConfig(reportCode, txnIndicator);
		if (rptCodeConfig == null) {
			rptCodeConfig = bankConfig.getRptCodeConfig(DEFAULT, txnIndicator);
		}
		return rptCodeConfig;
	}

	public static List<String> getRefIdList(final RptCodeConfig rptCodeConfig, final Map<String, String> contextMap,
			final BankConfig bankConfig) {
		if (rptCodeConfig == null || contextMap == null) {
			return new ArrayList<>();
		}
		List<String> list = new ArrayList<>();
		list.add(BankDataConfigEnum.getStaticKeyFroDynamicKey(rptCodeConfig.getDesc1(), contextMap,
				bankConfig.getBankStaticPropertiesConfig(), null));
		list.add(BankDataConfigEnum.getStaticKeyFroDynamicKey(rptCodeConfig.getDesc2(), contextMap,
				bankConfig.getBankStaticPropertiesConfig(), null));
		list.add(BankDataConfigEnum.getStaticKeyFroDynamicKey(rptCodeConfig.getDesc3(), contextMap,
				bankConfig.getBankStaticPropertiesConfig(), null));
		list.add(BankDataConfigEnum.getStaticKeyFroDynamicKey(rptCodeConfig.getDesc4(), contextMap,
				bankConfig.getBankStaticPropertiesConfig(), null));
		list.removeIf(value -> StringUtils.isEmpty(value) || "0".equals(value.trim()));
		return list;
	}

	public static Map<String, String> getRefIdMap(final RptCodeConfig rptCodeConfig,
			final Map<String, String> contextMap, final BankConfig bankConfig) {
		return getRefIdList(rptCodeConfig, contextMap, bankConfig).stream()
			.map(entry -> entry.split(":"))
			.filter(entry -> entry.length == 2)
			.collect(Collectors.toMap(entry -> entry[0], entry -> entry[1]));
	}

	public static String singleSpacing(final String string) {
		if (string == null) {
			return null;
		}
		return string.replaceAll("\\s+", " ");
	}

	public static String removeAllSpecialCharacters(final String inputString) {
		if (inputString == null) {
			return null;
		}
		return inputString.replaceAll("[^a-zA-Z0-9_.\\-]", "");
	}

	public static String getDroolsMaskedAccountNumber(final String accountNumber,
			@Nullable final Boolean isAccountNumberUnMaskingEnabled) {
		if (isAccountNumberUnMaskingEnabled != null && isAccountNumberUnMaskingEnabled) {
			return accountNumber;
		}
		if (StringUtils.isBlank(accountNumber)) {
			return accountNumber;
		}
		// replace all those characters that have at least 4 characters after it
		return "%{AcctNum : " + accountNumber + "}";
	}

	public static String getBenefNameFromBankTxn(final TransformedTransactionHistoryDetail bankTxn) {
		if (bankTxn != null && bankTxn.getParticipants() != null) {
			for (TransformedParticipant participant : bankTxn.getParticipants()) {
				if (bankTxn.getTxnIndicator() != null && bankTxn.getTxnIndicator().equals(participant.getTxnIndicator())
						&& participant.getContextMap() != null) {
					return participant.getContextMap().get(BENEF_NAME);
				}
			}
		}
		return null;
	}

	private static ClientStatusEnum setCbsStatus(final String reportCode, final ClientStatusEnum txnStatus) {
		ClientStatusEnum status = null;
		switch (reportCode) {
			case "20261":
			case "20263":
				status = ClientStatusEnum.PENDING;
				break;
			default:
				status = txnStatus;
		}
		return status;
	}

	public static void setImageData(final InstrumentDto instrumentDto, final String entityId) {
		ImageData imageData = new ImageData();
		imageData.setCustomerId(entityId);
		instrumentDto.setImageData(imageData);
	}

	public static boolean isForUpdates(final Map<String, String> paramMap) {
		return paramMap != null && paramMap.getOrDefault(FOR_UPDATES, FALSE).equalsIgnoreCase(TRUE);
	}

	public static void updateFromDateAndAddMetrics(final SearchContext searchContext,
			final ValidationResult validationResult, final Map<String, String> paramMap) {
		Long fromDate = searchContext.getFromDate();
		Long updatedFromDate = getUpdatedFromDateWithCustomerCreationDate(validationResult, fromDate, paramMap);
		if (Objects.equals(updatedFromDate, fromDate)) {
			metricsAgent.incrementCount(MonitoringConstants.CUSTOMER_CREATION_DATE_LOWER_THAN_FROM_DATE);
		}
		else {
			long timeDiff = updatedFromDate - fromDate;
			searchContext.setFromDate(updatedFromDate);
			long daysDiff = TimeUnit.DAYS.convert(timeDiff, TimeUnit.MILLISECONDS);
			// will do its optimization
			daysDiff -= TimeUnit.DAYS.convert((updatedFromDate - getMonthStartEpoch(updatedFromDate)),
					TimeUnit.MILLISECONDS);
			log.debug("Index saved for daysDiff : {} days by updating fromDate {} with customerCreationDate {}",
					daysDiff, fromDate, updatedFromDate);
			if (daysDiff / 3 > 0) {
				String tag = MonitoringConstants.NUMBER_OF_INDEX_SAVED + MonitoringConstants.COLON + daysDiff / 3;
				metricsAgent.incrementCount(MonitoringConstants.NUMBER_OF_INDEX_SAVED, tag);
			}
		}
	}

	/**
	 * if customerCreationDate > fromDate it means user don't have transaction till
	 * fromDate so will change fromDate to customerCreationDate and add customerCreation
	 * date in paramMap to use it in postProcessListingRequestForRouting
	 */
	public static Long getUpdatedFromDateWithCustomerCreationDate(final ValidationResult validationResult,
			final Long fromDate, final Map<String, String> paramMap) {
		if (validationResult == null) {
			return fromDate;
		}

		try {
			if (validationResult.getCustomerCreationDate() != null) {
				// log.info("CustomerCreationDate received from Oauth :
				// {}",validationResult.getCustomerCreationDate());
				Long customerCreationDate = getCustomerCreationDateFormattedDate(
						validationResult.getCustomerCreationDate());
				paramMap.put(CUSTOMER_CREATION_DATE, String.valueOf(customerCreationDate));
				if (fromDate <= customerCreationDate) {
					log.debug(
							"Updating fromDate :{} with customerCreationDate :{} for entityId : {}, as customerCreationDate is greater than "
									+ "or equal to fromDate",
							fromDate, customerCreationDate, validationResult.getUserId());
					return customerCreationDate;
				}
			}
		}
		catch (ParseException e) {
			log.error("Exception in parsing customerCreationDate :{}", validationResult.getCustomerCreationDate());
		}
		return fromDate;
	}

}