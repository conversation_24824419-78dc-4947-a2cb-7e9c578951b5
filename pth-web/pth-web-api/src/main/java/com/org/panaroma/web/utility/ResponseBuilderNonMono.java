package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.APP_CACHE_INVALIDATE_VERSION;
import static com.org.panaroma.commons.constants.RolloutStrategyConstants.TAGS_ROLL_OUT_STRATEGY_NAME;
import static com.org.panaroma.commons.constants.WebConstants.DETAIL_V2;
import static com.org.panaroma.commons.constants.WebConstants.DETAIL_V3;
import static com.org.panaroma.commons.constants.WebConstants.IS_RECENT_PAGE_LISTING;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.RECENT_WIDGET_NARRATION;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_API_VERSION;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.DETAIL_COULD_NOT_BE_LOADED;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.KNOWN_ISSUE_ERROR;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.TRANSFORMATION_ERROR;
import static com.org.panaroma.web.monitoring.MonitoringConstants.REQUEST_ID;

import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.enums.ApiVersion;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.rollout.strategy.IRolloutStrategyHelper;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.cache.ICacheClient;
import com.org.panaroma.web.detailAPI.DetailApiResponseUtility;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.dto.UpdateResponseDto;
import com.org.panaroma.web.dto.cst.CstListingResponseDto;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.web.dto.detailAPI.ExtraDetailRequestFields;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("respBuilderNonMono")
@Log4j2
@RequiredArgsConstructor
public class ResponseBuilderNonMono {

	private static MetricsAgent metricAgent;

	private static IKafkaClient iKafkaClient;

	private static ICacheClient iCacheClient;

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public ResponseBuilderNonMono(final MetricsAgent metricsAgent, final IKafkaClient iKafkaClient,
			final ICacheClient iCacheClient, final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		ResponseBuilderNonMono.metricAgent = metricsAgent;
		ResponseBuilderNonMono.iCacheClient = iCacheClient;
		ResponseBuilderNonMono.iKafkaClient = iKafkaClient;
		ResponseBuilderNonMono.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	public static DetailApiResponse buildDetailApiResponse(
			final List<TransformedTransactionHistoryDetail> detailedListMono, final String userId, final String txnId,
			final DetailInputParams detailInputParams, final String langId,
			final List<String> whiteListedLocalisationIds, final ExtraDetailRequestFields extraDetailRequestFields,
			final IRolloutStrategyHelper iRolloutStrategyHelper, final String locale) {
		List<TransformedTransactionHistoryDetail> detailedList = detailedListMono; // Blocking
																					// to
																					// get
																					// the
																					// list
																					// synchronously
		DetailApiResponse detailApiResponse = new DetailApiResponse();
		String openSource = extraDetailRequestFields == null ? null : extraDetailRequestFields.getOpenSource();
		// setting Response
		if (detailedList != null) {
			try {
				detailApiResponse = DetailApiResponseUtility.getDetailApiResponse(detailedList, txnId,
						detailInputParams, locale);
				detailApiResponse.setUserId(userId);
				detailApiResponse.setRequestId(ThreadContext.get(WebConstants.REQUEST_ID));
			}
			catch (Exception e) {
				log.error("error transforming ES response for detail API txnId:{} DetailV2 Source: {}  exception: {}",
						txnId, openSource, CommonsUtility.exceptionFormatter(e));
				if (e instanceof PanaromaException) {
					PanaromaException panaromaException = (PanaromaException) e;
					if (KNOWN_ISSUE_ERROR.equals(panaromaException.getResponseCode())) {
						throw panaromaException;
					}
				}
				throw ExceptionFactory.getException(PANAROMA_SERVICE, DETAIL_COULD_NOT_BE_LOADED);
			}
			detailApiResponse
				.setTagEnable(iRolloutStrategyHelper.isUserWhiteListed(TAGS_ROLL_OUT_STRATEGY_NAME, userId));
			GenericUtilityExtension.formatDetailResponse(detailApiResponse);
			if (!Arrays.asList(DETAIL_V2, DETAIL_V3).contains(detailInputParams.getDetailCallVersion())) {
				log.info("detail API response built : {}", detailApiResponse);
			}
		}
		if (LocalisationUtility.needLocalisation(langId)
				&& !(DETAIL_V2.equals(detailInputParams.getDetailCallVersion())
						|| DETAIL_V3.equals(detailInputParams.getDetailCallVersion()))
				&& (whiteListedLocalisationIds.contains("-1") || whiteListedLocalisationIds.contains(userId))) {
			LocalisationUtility.localiseDetailApiResponse(detailApiResponse, langId, txnId);
		}
		return detailApiResponse;
	}

	public static ResponseDto buildTxnListingResponseDto(final RepoResponseSearchApiDto repoResponse,
			final SearchContext searchContext, final String langId, final List<String> salaryReportCodes,
			final Map<String, String> paramMap, final boolean pushMissingUserImageListToKafka,
			final boolean fetchUserImageFromCache) throws PanaromaException {

		boolean isRecentPageListing = Boolean.parseBoolean(paramMap.get(IS_RECENT_PAGE_LISTING));
		try {
			Map<String, UserDetails> userIdImageUrlMapFromCache = ListingUtility.getStringUserDetailsMap(
					pushMissingUserImageListToKafka, fetchUserImageFromCache, iCacheClient, iKafkaClient, repoResponse);
			ResponseDto.ResponseDtoBuilder responseDtoBuilder = ResponseDto.builder()
				.setEntityId(searchContext.getEntityId())
				.setFromDate(GenericUtility.getDateLabel(String.valueOf(searchContext.getFromDate())))
				.setToDate(GenericUtility.getDateLabel(String.valueOf(searchContext.getToDate())))
				.setUthFromDate(GenericUtility.getDateLabel(String.valueOf(searchContext.getFromDate())))
				.setRecentWidgetNarration(isRecentPageListing ? paramMap.get(RECENT_WIDGET_NARRATION) : null)
				.setTxns(ListingUtility.getEsResponseTxnIterable(
						repoResponse.getTransformedTransactionHistoryDetailsIterable(), langId, salaryReportCodes,
						userIdImageUrlMapFromCache, paramMap))
				.setTotalPages(String.valueOf(GenericUtilityExtension.getTotalPages(repoResponse.getTotalHits(),
						searchContext.getPageSize())))
				.setCurrentPage(String.valueOf(searchContext.getPageNo()));
			GenericUtilityExtension.setPaginationForUpdates(repoResponse, searchContext);
			if (repoResponse.getPaginationParams() != null
					&& ((repoResponse.getPaginationParams().getPaginationTxnId() != null
							|| repoResponse.getPaginationParams().getFromUpdatedDate() != null)
							|| repoResponse.getPaginationParams().getToDateForSubsequentRequest() != null)) {
				responseDtoBuilder.setPaginationParams(repoResponse.getPaginationParams());
			}
			if (paramMap.getOrDefault(SEARCH_API_VERSION, ApiVersion.v3.name())
				.compareToIgnoreCase(ApiVersion.v3.name()) >= 0) {
				responseDtoBuilder.setInvalidateVersion(
						configurablePropertiesHolder.getProperty(APP_CACHE_INVALIDATE_VERSION, Integer.class));
			}
			// if (Integer.parseInt(responseDtoBuilder.getTotalPages()) >
			// Integer.parseInt(responseDtoBuilder.getCurrentPage())) {
			// responseDtoBuilder.setPaginationParams(repoResponse.getPaginationParams());
			// }
			ResponseDto response = responseDtoBuilder.build();
			// As response logs are getting printed using Logbook. search "Logbook" &&
			// "response" to check these logs.
			// log.info("Listing Api Response {}", response);
			return response;
		}
		catch (Exception e) {
			log.error("Exception while creating listing response. Exception:{}", CommonsUtility.exceptionFormatter(e));
			if (e instanceof PanaromaException) {
				throw ((PanaromaException) e);
			}
			else {
				log.error("error while converting ES response to Response DTO with " + "reqId : {} and entityId : {}",
						paramMap.get(REQUEST_ID), searchContext.getEntityId());
				throw ExceptionFactory.getException(PANAROMA_SERVICE, TRANSFORMATION_ERROR);
			}
		}
	}

	public static UpdateResponseDto buildTxnUpdateResponseDto(final RepoResponseSearchApiDto repoResponse,
			final SearchContext searchContext, final String langId, final List<String> salaryReportCodes,
			final Map<String, String> paramMap, final boolean pushMissingUserImageListToKafka,
			final boolean fetchUserImageFromCache) {
		boolean isRecentPageListing = Boolean.parseBoolean(paramMap.get(IS_RECENT_PAGE_LISTING));
		try {
			UpdateResponseDto.UpdateResponseDtoBuilder updateResponseDtoBuilder = UpdateResponseDto.builder()
				.setEntityId(searchContext.getEntityId())
				.setFromDate(GenericUtility.getDateLabel(String.valueOf(searchContext.getFromDate())))
				.setToDate(GenericUtility.getDateLabel(String.valueOf(searchContext.getToDate())))
				.setUthFromDate(GenericUtility.getDateLabel(String.valueOf(searchContext.getFromDate())))
				.setRecentWidgetNarration(isRecentPageListing ? paramMap.get(RECENT_WIDGET_NARRATION) : null)
				.setTotalPages(String.valueOf(GenericUtilityExtension.getTotalPages(repoResponse.getTotalHits(),
						searchContext.getPageSize())))
				.setCurrentPage(String.valueOf(searchContext.getPageNo()))
				.setPaginationParams(repoResponse.getPaginationParams());
			GenericUtilityExtension.setPaginationForUpdates(repoResponse, searchContext);
			if (searchContext.isInvalidForUpdates()) {
				updateResponseDtoBuilder.setInvalidateStoredData(true);
			}
			List<TransformedTransactionHistoryDetail> txns = repoResponse
				.getTransformedTransactionHistoryDetailsIterable();
			List<String> removedIds = txns.stream()
				.filter(txn -> txn.getShowInListing().equals(false))
				.map(TransformedTransactionHistoryDetail::getTxnId)
				.collect(Collectors.toList());

			if (!removedIds.isEmpty()) {
				updateResponseDtoBuilder.setRemovedIds(removedIds);
			}
			Map<String, UserDetails> userIdImageUrlMapFromCache = ListingUtility.getStringUserDetailsMap(
					pushMissingUserImageListToKafka, fetchUserImageFromCache, iCacheClient, iKafkaClient, repoResponse);
			List<EsResponseTxn> listingEntries = txns.stream()
				.filter(txn -> txn.getShowInListing().equals(true))
				.map(txn -> ListingUtility.convertToEsResponseTxn(txn, langId, salaryReportCodes,
						userIdImageUrlMapFromCache, paramMap))
				.toList();
			updateResponseDtoBuilder.setTxns(listingEntries);
			updateResponseDtoBuilder.setInvalidateVersion(
					configurablePropertiesHolder.getProperty(APP_CACHE_INVALIDATE_VERSION, Integer.class));
			log.debug("successfully built responseDTO from ESDTO");

			UpdateResponseDto updateResponseDto = updateResponseDtoBuilder.build();

			log.info("removeIds list before removing upi txnId : {}", updateResponseDto.getRemovedIds());

			// Removing source TxnId from removeIds list which are there txn responses
			if (ObjectUtils.isNotEmpty(updateResponseDto.getTxns())
					&& ObjectUtils.isNotEmpty(updateResponseDto.getRemovedIds())) {
				for (EsResponseTxn esResponseTxn : updateResponseDto.getTxns()) {
					updateResponseDto.getRemovedIds().remove(esResponseTxn.getSourceTxnId());
				}
			}

			log.info("removeIds list after removing upi txnId : {}", updateResponseDto.getRemovedIds());

			return updateResponseDto;

		}
		catch (PanaromaException e) {
			log.error("Exception while creating listing response. Exception:{}", CommonsUtility.exceptionFormatter(e));
			throw e;
		}
		catch (Exception e) {
			log.error("Exception while creating listing response. Exception:{}", CommonsUtility.exceptionFormatter(e));
			if (e instanceof PanaromaException) {
				throw ((PanaromaException) e);
			}
			else {
				log.error("error while converting ES response to Response DTO with " + "reqId : {} and entityId : {}",
						paramMap.get(REQUEST_ID), searchContext.getEntityId());
				throw ExceptionFactory.getException(PANAROMA_SERVICE, TRANSFORMATION_ERROR);
			}
		}
	}

	public CstListingResponseDto buildCstTxnListingResponseDto(final RepoResponseSearchApiDto repoResponse,
			final SearchContext searchContext, final Map<String, String> paramMap, final List<String> salaryReportCodes,
			final String langId, final ICacheClient cacheClient, final IKafkaClient kafkaClient) {
		try {
			Map<String, UserDetails> userIdImageUrlMapFromCache = ListingUtility.getStringUserDetailsMap(false, true,
					cacheClient, kafkaClient, repoResponse);

			CstListingResponseDto cstListingResponseDto = CstListingResponseDto.builder()
				.currentPage(String.valueOf(searchContext.getPageNo()))
				.entityId(searchContext.getEntityId())
				.totalPages(String.valueOf(GenericUtilityExtension.getTotalPages(repoResponse.getTotalHits(),
						searchContext.getPageSize())))
				.txns(ResponseBuilder.getCstResponseTxnIterable(
						repoResponse.getTransformedTransactionHistoryDetailsIterable(), langId, salaryReportCodes,
						userIdImageUrlMapFromCache, paramMap))
				.build();
			if (repoResponse.getPaginationParams() != null
					&& repoResponse.getPaginationParams().getPaginationTxnId() != null) {
				cstListingResponseDto.setPaginationParams(repoResponse.getPaginationParams());
			}
			return cstListingResponseDto;

		}
		catch (Exception e) {
			log.error("Exception while creating cst response. Exception:{}", CommonsUtility.exceptionFormatter(e));
			if (e instanceof PanaromaException) {
				throw ((PanaromaException) e);
			}
			else {
				log.error("Unhandled Exception while creating cst response. Exception:{}",
						CommonsUtility.exceptionFormatter(e));
				throw ExceptionFactory.getException(PANAROMA_SERVICE, TRANSFORMATION_ERROR);
			}
		}
	}

}
