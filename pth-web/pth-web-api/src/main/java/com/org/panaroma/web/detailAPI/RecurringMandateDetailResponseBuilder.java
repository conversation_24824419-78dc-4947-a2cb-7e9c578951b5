package com.org.panaroma.web.detailAPI;

import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_FAILURE;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_PENDING;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_SUCCESS;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_SETUP_FAILURE;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_SETUP_PENDING;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_RECURRING_MANDATE_SETUP_SUCCESS;
import static com.org.panaroma.commons.constants.WebConstants.ERROR_MESSAGE;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO_ONE;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.RECURRING_MANDATE_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.VALIDITY_START_DATE;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.dd_MMM_YYYY;
import static com.org.panaroma.commons.constants.WebConstants.NOTES;
import static com.org.panaroma.commons.constants.WebConstants.VALIDITY_END_DATE;

import com.org.panaroma.commons.constants.CommonConstants;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class RecurringMandateDetailResponseBuilder extends MandateDetailResponseBuilder {

	@Override
	public TransactionTypeEnum buildDetailApiResponseFor() {
		return TransactionTypeEnum.RECURRING_MANDATE;
	}

	@Override
	public String getNarrationForStatus(final ClientStatusEnum status,
			final TransformedTransactionHistoryDetail detailNeededForTxn) {
		String narration = "";
		String txnPurpose = GenericUtilityExtension.getTransactionPurpose(detailNeededForTxn);

		// Handle regular mandate events only (port events are filtered out from ES)
		switch (status) {
			case SUCCESS:
				if (CommonConstants.PURPOSE_COLLECT.equals(txnPurpose)
						&& EXECUTION_NO_ONE.equals(GenericUtilityExtension.getExecutionNo(detailNeededForTxn))) {
					narration = DETAIL_NARRATION_RECURRING_MANDATE_SETUP_SUCCESS.getLocaleMsgKey();
				}
				else {
					narration = DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_SUCCESS.getLocaleMsgKey();
				}
				break;
			case FAILURE:
				if (CommonConstants.PURPOSE_COLLECT.equals(txnPurpose)
						&& EXECUTION_NO_ONE.equals(GenericUtilityExtension.getExecutionNo(detailNeededForTxn))) {
					narration = DETAIL_NARRATION_RECURRING_MANDATE_SETUP_FAILURE.getLocaleMsgKey();
				}
				else {
					narration = DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_FAILURE.getLocaleMsgKey();
				}
				break;
			case PENDING:
				if (CommonConstants.PURPOSE_COLLECT.equals(txnPurpose)
						&& EXECUTION_NO_ONE.equals(GenericUtilityExtension.getExecutionNo(detailNeededForTxn))) {
					narration = DETAIL_NARRATION_RECURRING_MANDATE_SETUP_PENDING.getLocaleMsgKey();
				}
				else {
					narration = DETAIL_NARRATION_RECURRING_MANDATE_EXECUTION_PENDING.getLocaleMsgKey();
				}
				break;
			default:
		}
		return narration;
	}

	@Override
	public String getNotes(final DetailApiResponse detailApiResponse, final TransformedTransactionHistoryDetail txn) {
		if (Objects.nonNull(txn.getContextMap())) {
			if (ClientStatusEnum.FAILURE.equals(ClientStatusEnum.valueOf(detailApiResponse.getStatus()))
					&& txn.getContextMap().containsKey(ERROR_MESSAGE)
					&& StringUtils.isNotBlank(txn.getContextMap().get(ERROR_MESSAGE))) {
				return txn.getContextMap().get(ERROR_MESSAGE);
			}

			if (txn.getContextMap().containsKey(NOTES) && StringUtils.isNotBlank(txn.getContextMap().get(NOTES))) {
				String notes = txn.getContextMap().get(NOTES);

				if ((ClientStatusEnum.SUCCESS.equals(ClientStatusEnum.valueOf(detailApiResponse.getStatus()))
						|| ClientStatusEnum.PENDING.equals(ClientStatusEnum.valueOf(detailApiResponse.getStatus())))
						&& CommonConstants.PURPOSE_COLLECT.equals(GenericUtilityExtension.getTransactionPurpose(txn))) {
					try {
						SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dd_MMM_YYYY);
						Date date1 = new Date(Long.parseLong(txn.getContextMap().get(VALIDITY_START_DATE)));
						String validityStartDate = simpleDateFormat.format(date1);
						Date date2 = new Date(Long.parseLong(txn.getContextMap().get(VALIDITY_END_DATE)));
						String validityEndDate = simpleDateFormat.format(date2);

						notes = notes.replace("%validityStartDate", validityStartDate);
						notes = notes.replace("%validityEndDate", validityEndDate);
					}
					catch (Exception e) {
						log.info(
								"ValidityStartDate / ValidityEndDate placeholder doesn't exit in notes:{} for txnId : {}",
								notes, txn.getTxnId());
					}
				}
				return notes;
			}
		}
		return null;
	}

	@Override
	public String getLogoUrl(final TransformedParticipant participant, final TransformedTransactionHistoryDetail txn) {
		if (StringUtils.isNotBlank(participant.getLogoUrl())) {
			return participant.getLogoUrl();
		}
		return LogoUtility.getLogo(RECURRING_MANDATE_LOGO, LogoType.OTHER);
	}

	@Override
	public void setInstrumentNarration(final InstrumentDto instrumentDto) {
		instrumentDto.setNarration("From");
	}

}
