package com.org.panaroma.web.dto.listing.v4;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EntendedInfoV4 {

	private String bgAppSyncNextListingUrl;

	private String nextListingUrl;

	private Integer invalidateVersion;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
