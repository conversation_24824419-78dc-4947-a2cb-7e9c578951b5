package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.WebConstants.DYNAMIC_IDENTIFIER;

import com.org.panaroma.commons.service.AsyncDataProcessor;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.web.cache.CacheKeyManager;
import com.org.panaroma.web.cache.ClientConfigService;
import com.org.panaroma.web.cache.ICacheClient;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.dto.cstData.CstDto;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.RecentTransactionInfo;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.InstrumentDtoV2;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class LocalisationUtility {

	private static Map<String, String> languageMap;

	private static Map<String, String> cacheNarrationKeysMap;

	private static List<String> detailFieldsList;

	private static ICacheClient cacheClient;

	private static AsyncDataProcessor asyncDataProcessor;

	private static MetricsAgent metricAgent;

	private static List<String> listingFieldsList;

	private static Set<String> prepopulateDataList;

	private static ClientConfigService clientConfigService;

	@Autowired
	public LocalisationUtility(@Value("${localise.detail.fields}") final List<String> detailFieldsList,
			@Value("${localise.listing.fields}") final List<String> listingFieldsList,
			@Value("${prepopulate.data.list}") final Set<String> prepopulateDataList, final ICacheClient cacheClient,
			final AsyncDataProcessor asyncDataProcessor, final MetricsAgent metricAgent,
			final ClientConfigService clientConfigService) {
		this.cacheClient = cacheClient;
		this.detailFieldsList = detailFieldsList;
		this.asyncDataProcessor = asyncDataProcessor;
		this.metricAgent = metricAgent;
		this.listingFieldsList = listingFieldsList;
		this.prepopulateDataList = prepopulateDataList;
		this.clientConfigService = clientConfigService;
	}

	@PostConstruct
	public void init() {
		log.info("prepopulating localisation data");
		pushToKafkaIfRequired(null, "init", prepopulateDataList);
	}

	static {
		languageMap = new HashMap<>();
		cacheNarrationKeysMap = new HashMap<>();
		languageMap.put("en-IN", "1");
		languageMap.put("hi-IN", "2");
		languageMap.put("bn-IN", "3");
		languageMap.put("or-IN", "4");
		languageMap.put("mr-IN", "5");
		languageMap.put("ml-IN", "6");
		languageMap.put("kn-IN", "7");
		languageMap.put("ta-IN", "8");
		languageMap.put("te-IN", "9");
		languageMap.put("gu-IN", "10");
		languageMap.put("pa-IN", "11");

		cacheNarrationKeysMap.put("Add Money to", "Add Money to %s");
		cacheNarrationKeysMap.put("Cashback Received from", "Cashback Received from %s");
		cacheNarrationKeysMap.put("Money added to", "Money added to %s");
		cacheNarrationKeysMap.put("Money on hold for Order at", "Money on hold for Order at %s");
		cacheNarrationKeysMap.put("Money on hold released by", "Money on hold released by %s");
		cacheNarrationKeysMap.put("Paid to", "Paid to %s");
		cacheNarrationKeysMap.put("Received from", "Received from %s");
		cacheNarrationKeysMap.put("Refund for failed transfer to", "Refund for failed transfer to %s");
		cacheNarrationKeysMap.put("Refund from", "Refund from %s");
		cacheNarrationKeysMap.put("Sent to", "Sent to %s");
		cacheNarrationKeysMap.put("Transfer to", "Transfer to %s");
		cacheNarrationKeysMap.put("Settlement Received from", "Settlement Received from %s");
		cacheNarrationKeysMap.put("Gift Voucher Sent To", "Gift Voucher Sent To %s");
		cacheNarrationKeysMap.put("Gift Voucher Received from", "Gift Voucher Received from %s");
		cacheNarrationKeysMap.put("Payment to", "Payment to %s");
		cacheNarrationKeysMap.put("Automatic Payment made to", "Automatic Payment made to %s");
		cacheNarrationKeysMap.put("Interest earned for", "Interest earned for %s");
		cacheNarrationKeysMap.put("Cash Withdrawal at", "Cash Withdrawal at %s");
		cacheNarrationKeysMap.put("ATM Cash Withdrawal at", "ATM Cash Withdrawal at %s");
		cacheNarrationKeysMap.put("Cash Deposited at", "Cash Deposited at %s");
		cacheNarrationKeysMap.put("Money paid to %s via cheque", "Money paid to %s via cheque");
		cacheNarrationKeysMap.put("Deducted by", "Deducted by %s");
		cacheNarrationKeysMap.put("Charges Paid for payment at", "Charges Paid for payment at %s");
		cacheNarrationKeysMap.put("Charges Refunded for payment at", "Charges Refunded for payment at %s");
		cacheNarrationKeysMap.put("Charges paid for payment to", "Charges paid for payment to %s");
		cacheNarrationKeysMap.put("Refund for failed cheque payment to", "Refund for failed cheque payment to %s");
		cacheNarrationKeysMap.put("Refund for failed payment to", "Refund for failed payment to %s");
		cacheNarrationKeysMap.put("Money reversed for failed transfer from",
				"Money reversed for failed transfer from %s");
		cacheNarrationKeysMap.put("Cash Deposit at ATM,", "Cash Deposit at ATM,%s");
		cacheNarrationKeysMap.put("Recent Payments With", "Recent Payments With %s");
		cacheNarrationKeysMap.put("Pending Add Money to", "Pending Add Money to %s");
		cacheNarrationKeysMap.put("Added to", "Added to %s");
		cacheNarrationKeysMap.put("Failed Add Money to", "Failed Add Money to %s");
		cacheNarrationKeysMap.put("Deposited at", "Deposited at %s");
		cacheNarrationKeysMap.put("Withdrawal at", "Withdrawal at %s");
		cacheNarrationKeysMap.put("Earned for", "Earned for %s");
		cacheNarrationKeysMap.put("Cashback received for payment at", "Cashback received for payment at %s");
		cacheNarrationKeysMap.put("Tip paid to", "Tip paid to %s");
		cacheNarrationKeysMap.put("Surcharge for payment at", "Surcharge for payment at %s");
		cacheNarrationKeysMap.put("Refunded for payment at", "Refunded for payment at %s");
	}

	public static String validateAndSetLanguageId(final String locale, final String client) {
		if (clientConfigService.isS2sClient(client)
				&& !clientConfigService.getClientConfigMap().get(client).isShowLocalisedData()) {
			return "1";
		}

		if (StringUtils.isEmpty(locale) || !languageMap.keySet().contains(locale)) {
			return "1";
		}

		return languageMap.get(locale);
	}

	public static boolean needLocalisation(final String langId) {
		if (StringUtils.isEmpty(langId) || "1".equalsIgnoreCase(langId)) {
			return false;
		}
		try {
			Integer langId1 = Integer.parseInt(langId);
			if (langId1 >= 2 && langId1 <= 11) {
				return true;
			}
		}
		catch (Exception e) {
			return false;
		}
		return false;
	}

	public static void localiseDetailApiResponse(final Object detailApiResponse, final String langId,
			final String txnId) {
		if (Objects.isNull(detailApiResponse)) {
			return;
		}
		log.info("localising content of detail API for lang Id :{} ", langId);
		Set<String> dataToLocalise = collectDataToLocalize(detailApiResponse, detailFieldsList);
		Set<String> unlocalisedData = new HashSet<>();
		if (Objects.nonNull(dataToLocalise) && dataToLocalise.size() > 0) {
			Map<String, String> localisedData = cacheClient
				.getLocalisedBatchRecords(CacheKeyManager.getCacheBatchKey(langId, dataToLocalise));
			try {
				for (String fields : detailFieldsList) {
					setLocalisedDetailResponse(detailApiResponse, unlocalisedData, localisedData, fields);
				}
			}
			catch (Exception e) {
				log.error("Exception while localising data for txnId : {}.exception : {}", txnId,
						CommonsUtility.exceptionFormatter(e));
			}
			metricAgent.recordCachedLocalized(localisedData.size(), langId, MonitoringConstants.DETAIL_API);
			metricAgent.recordRequiredLocalized(dataToLocalise.size(), langId, MonitoringConstants.DETAIL_API);
		}
		pushToKafkaIfRequired(langId, txnId, unlocalisedData);
	}

	private static void setLocalisedDetailResponse(final Object detailApiResponse, final Set<String> unlocalisedData,
			final Map<String, String> localisedData, final String fields) throws Exception {
		if (fields.contains(".")) {
			String firstField = fields.substring(0, fields.indexOf("."));
			Field field = detailApiResponse.getClass().getDeclaredField(firstField);
			field.setAccessible(true);
			Object object = field.get(detailApiResponse);
			if (null == object) {
				return;
			}
			String innerField = fields.substring(fields.indexOf(".") + 1);
			setLocalisedDetailResponse(object, unlocalisedData, localisedData, innerField);
		}
		else if (detailApiResponse instanceof List) {
			if ((InstrumentDto.class).equals(((List) detailApiResponse).get(0).getClass())) {
				List<InstrumentDto> list1 = (List) detailApiResponse;
				for (InstrumentDto instrumentDto : list1) {
					setLocalisedDetailResponse(instrumentDto, unlocalisedData, localisedData, fields);
				}
			}
			else if ((InstrumentDtoV2.class).equals(((List) detailApiResponse).get(0).getClass())) {
				List<InstrumentDtoV2> list1 = (List) detailApiResponse;
				for (InstrumentDtoV2 instrumentDto : list1) {
					setLocalisedDetailResponse(instrumentDto, unlocalisedData, localisedData, fields);
				}
			}
		}
		else {
			Field innerField = detailApiResponse.getClass().getDeclaredField(fields);
			innerField.setAccessible(true);
			if (Objects.nonNull(localisedData.get(cacheNarrationKeysMap.get(innerField.get(detailApiResponse))))) {
				String localizedString = localisedData
					.get(cacheNarrationKeysMap.get(innerField.get(detailApiResponse)));
				if (detailApiResponse instanceof RecentTransactionInfo) {
					localizedString = localizedString.replace(DYNAMIC_IDENTIFIER,
							((RecentTransactionInfo) detailApiResponse).getName());
					((RecentTransactionInfo) detailApiResponse).setName(null);
				}
				innerField.set(detailApiResponse, localizedString);
			}
			else if (Objects.nonNull(localisedData.get(innerField.get(detailApiResponse)))) {
				innerField.set(detailApiResponse, localisedData.get(innerField.get(detailApiResponse)));
			}
			else if (Objects.nonNull(innerField.get(detailApiResponse))) {
				unlocalisedData.add(cacheNarrationKeysMap.getOrDefault(innerField.get(detailApiResponse),
						(String) innerField.get(detailApiResponse)));
			}
		}
	}

	public static Set<String> collectDataToLocalize(final Object detailApiResponse,
			final List<String> localiseDetailList) {
		Set<String> dataToLocalise = new HashSet<>();
		String stringToLocalize = "";
		try {
			for (String fields : localiseDetailList) {
				if (fields.contains(".")) {
					Field field = detailApiResponse.getClass()
						.getDeclaredField(fields.substring(0, fields.indexOf(".")));
					field.setAccessible(true);
					Object object = field.get(detailApiResponse);
					if (null == object) {
						continue;
					}
					Field innerField = null;
					if (field.getType().equals(CstDto.class)) {
						CstDto cstDto = (CstDto) object;
						innerField = cstDto.getClass().getDeclaredField(fields.substring(fields.indexOf(".") + 1));
						innerField.setAccessible(true);
						stringToLocalize = (String) innerField.get(cstDto);
					}
					else if (object instanceof List) {
						if ((InstrumentDto.class).equals(((List) object).get(0).getClass())) {
							List<InstrumentDto> list1 = (List) object;
							for (InstrumentDto i : list1) {
								innerField = i.getClass().getDeclaredField(fields.substring(fields.indexOf(".") + 1));
								innerField.setAccessible(true);
								stringToLocalize = (String) innerField.get(i);
							}
						}
						else if ((InstrumentDtoV2.class).equals(((List) object).get(0).getClass())) {
							List<InstrumentDtoV2> list1 = (List) object;
							for (InstrumentDtoV2 i : list1) {
								innerField = i.getClass().getDeclaredField(fields.substring(fields.indexOf(".") + 1));
								innerField.setAccessible(true);
								stringToLocalize = (String) innerField.get(i);
							}
						}
					}
					else {
						innerField = object.getClass().getDeclaredField(fields.substring(fields.indexOf(".") + 1));
						innerField.setAccessible(true);
						stringToLocalize = (String) innerField.get(object);
					}
				}
				else {
					Field field = detailApiResponse.getClass().getDeclaredField(fields);
					field.setAccessible(true);
					stringToLocalize = (String) field.get(detailApiResponse);
				}
				if (Objects.nonNull(cacheNarrationKeysMap.get(stringToLocalize))) {
					stringToLocalize = cacheNarrationKeysMap.get(stringToLocalize);
				}
				if (StringUtils.isNotBlank(stringToLocalize)) {
					dataToLocalise.add(stringToLocalize);
				}
			}
		}
		catch (Exception e) {
			log.error("Exception while collecting localisation data .exception : {}",
					CommonsUtility.exceptionFormatter(e));
		}
		return dataToLocalise;
	}

	public static ResponseDto localiseListingDetails(final ResponseDto responseDto, final String langId,
			final String entityId) {
		log.info("localising content of listing API for lang Id :{} ", langId);
		Set<String> dataToLocalise = new HashSet<>();
		Iterable<EsResponseTxn> esResponseTxns = responseDto.getTxns();
		List<EsResponseTxn> txnList = new ArrayList<>();
		Iterator<EsResponseTxn> esResponseTxnIterator = esResponseTxns.iterator();
		while (esResponseTxnIterator.hasNext()) {
			EsResponseTxn esResponseTxn = esResponseTxnIterator.next();
			for (String fields : listingFieldsList) {
				try {
					Field field = esResponseTxn.getClass().getDeclaredField(fields);
					field.setAccessible(true);
					String stringToLocalize = (String) field.get(esResponseTxn);
					if (Objects.nonNull(cacheNarrationKeysMap.get(stringToLocalize))) {
						stringToLocalize = cacheNarrationKeysMap.get(stringToLocalize);
					}
					if (StringUtils.isNotBlank(stringToLocalize)) {
						dataToLocalise.add(stringToLocalize);
					}
				}
				catch (Exception e) {
					log.error("Exception while collecting localisation data .exception : {}",
							CommonsUtility.exceptionFormatter(e));
				}
			}
			txnList.add(esResponseTxn);
		}
		responseDto.setTxns(txnList);
		log.info("data to localise : {}", dataToLocalise);
		return getResponseDto(responseDto, langId, entityId, dataToLocalise);
	}

	private static ResponseDto getResponseDto(final ResponseDto responseDto, final String langId, final String entityId,
			final Set<String> dataToLocalise) {
		if (Objects.nonNull(dataToLocalise) && dataToLocalise.size() == 0) {
			log.info("Nothing to localize for entity Id {} and langId {}", entityId, langId);
			return responseDto;
		}

		Set<String> unlocalisedData = new HashSet<>();
		Map<String, String> localisedData = cacheClient
			.getLocalisedBatchRecords(CacheKeyManager.getCacheBatchKey(langId, dataToLocalise));
		Iterator<EsResponseTxn> esResponseTxnIterator = responseDto.getTxns().iterator();
		while (esResponseTxnIterator.hasNext()) {
			EsResponseTxn esResponseTxn = esResponseTxnIterator.next();
			for (String fields : listingFieldsList) {
				try {
					Field field = esResponseTxn.getClass().getDeclaredField(fields);
					field.setAccessible(true);
					if (Objects.nonNull(localisedData.get(cacheNarrationKeysMap.get(field.get(esResponseTxn))))) {
						String localizedString = localisedData.get(cacheNarrationKeysMap.get(field.get(esResponseTxn)));
						localizedString = localizedString.replace(DYNAMIC_IDENTIFIER,
								esResponseTxn.getSecondPartyInfo().getName());
						// esResponseTxn.getSecondPartyInfo().setName("");
						field.set(esResponseTxn, localizedString);
					}
					else if (Objects.nonNull(localisedData.get(field.get(esResponseTxn)))) {
						field.set(esResponseTxn, localisedData.get(field.get(esResponseTxn)));
					}
					else if (StringUtils.isNotBlank((String) field.get(esResponseTxn))) {
						unlocalisedData.add(cacheNarrationKeysMap.getOrDefault(field.get(esResponseTxn),
								(String) field.get(esResponseTxn)));
					}
				}
				catch (Exception e) {
					log.error("Exception while localising data for entityId : {}.exception : {}", entityId,
							CommonsUtility.exceptionFormatter(e));
				}
				finally {
					pushToKafkaIfRequired(langId, entityId, unlocalisedData);
				}
			}
		}
		metricAgent.recordCachedLocalized(localisedData.size(), langId, MonitoringConstants.SEARCH_API);
		metricAgent.recordRequiredLocalized(dataToLocalise.size(), langId, MonitoringConstants.SEARCH_API);
		return responseDto;
	}

	private static void pushToKafkaIfRequired(final String langId, final String id, final Set<String> unlocalisedData) {
		/**
		 * Remove this when you want to start producing localisation data to kafka.
		 */
		/*
		 * if (Objects.nonNull(unlocalisedData) && unlocalisedData.size() > 0) {
		 * List<String> recordList = new ArrayList<>();
		 * recordList.addAll(unlocalisedData); MarketplaceKafkaRecord
		 * marketplaceKafkaRecord = new MarketplaceKafkaRecord();
		 * marketplaceKafkaRecord.setData(recordList);
		 * marketplaceKafkaRecord.setLanguageId(langId); log.
		 * info("pushing data to kafka in Async manner for Id : {}. Data Size : {} , data : {},{} ..."
		 * , id, recordList.size(), recordList.get(0), recordList.get(recordList.size() -
		 * 1)); asyncDataProcessor.process(marketplaceKafkaRecord,id); }
		 */
	}

}
