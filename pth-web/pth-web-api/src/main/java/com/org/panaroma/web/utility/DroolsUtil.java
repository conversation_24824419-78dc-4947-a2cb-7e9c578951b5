package com.org.panaroma.web.utility;

import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.UthCategoryEnum;
import com.org.panaroma.commons.dto.WalletType;
import com.org.panaroma.commons.dto.es.RefundDetails;
import com.org.panaroma.commons.dto.es.TransformedBankData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.CtaType;
import com.org.panaroma.commons.enums.LogoOrderTypeEnum;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.utils.AutoTaggingUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.IfscUtility;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.SecondPartyInfo;
import com.org.panaroma.web.dto.UserInstrumentLogos;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.ParticipantInfo;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.CtaNode;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.EntityDetails;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.InstrumentDetailsDto;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.InstrumentDtoV2;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailApiResponseV3;
import com.org.panaroma.web.dto.detailAPI.detailV3.InstrumentDtoV3;
import com.org.panaroma.web.dto.detailAPI.detailV3.RefundDetailsDto;
import com.org.panaroma.web.enums.ListingResponseMappingEnum;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import lombok.extern.log4j.Log4j2;
import org.apache.avro.reflect.Nullable;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.org.panaroma.commons.constants.BankDataConstants.PAYTM_BANK_IFSC;
import static com.org.panaroma.commons.constants.BankDataConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.BankDataConstants.VISA_RECURRING_REPORT_CODE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.SHOW_TAG_ENABLE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TAGS_UPDATION_ENABLE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TAG_FEATURE_ENABLE;
import static com.org.panaroma.commons.constants.LocaleMsgConstants.DETAIL_NARRATION_SELF_TRANSFER_SUCCESS;
import static com.org.panaroma.commons.constants.WebConstants.BENEF_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.COMMA;
import static com.org.panaroma.commons.constants.WebConstants.CUSTOM_SECOND_PARTY_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.DROOLS_ACCT_NUM_DYNAMIC_KEY_REGEX;
import static com.org.panaroma.commons.constants.WebConstants.DROOLS_MOB_NUM_DYNAMIC_KEY_REGEX;
import static com.org.panaroma.commons.constants.WebConstants.FAILED;
import static com.org.panaroma.commons.constants.WebConstants.FASTAG;
import static com.org.panaroma.commons.constants.WebConstants.FASTAG_ID;
import static com.org.panaroma.commons.constants.WebConstants.GV_PURCHASED;
import static com.org.panaroma.commons.constants.WebConstants.GV_REDEEM;
import static com.org.panaroma.commons.constants.WebConstants.HYPHEN;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_BANK;
import static com.org.panaroma.commons.constants.WebConstants.LINKED_MOBILE_NO;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.FOR;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.LAN;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.LOAN_APPLICATION_NUMBER;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.LOAN_REPAYMENT;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.MONEY_ON_HOLD_FOR;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.MONEY_RELEASED_FOR;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.PAID_FOR;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.REFUND_FOR;
import static com.org.panaroma.commons.constants.WebConstants.LoanRepaymentConstants.REPAYMENT_OF_LOAN;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_DEFAULT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.MONEY_DEDUCTED_ON;
import static com.org.panaroma.commons.constants.WebConstants.MONEY_TRANSFERRED;
import static com.org.panaroma.commons.constants.WebConstants.PAYER_MOBILE_NUMBER;
import static com.org.panaroma.commons.constants.WebConstants.PAYTMGIFTVOUCHER;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_GIFT_VOUCHER;
import static com.org.panaroma.commons.constants.WebConstants.PENDING;
import static com.org.panaroma.commons.constants.WebConstants.RECEIVED_IN2;
import static com.org.panaroma.commons.constants.WebConstants.RMT_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.SELF;
import static com.org.panaroma.commons.constants.WebConstants.SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.SENT_FROM;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.SUCCESS;
import static com.org.panaroma.commons.constants.WebConstants.TO;
import static com.org.panaroma.commons.constants.WebConstants.TOLL_CROSSED_ON;
import static com.org.panaroma.commons.constants.WebConstants.TO_YOUR;
import static com.org.panaroma.commons.constants.WebConstants.TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.TRANSFERRED_TO;
import static com.org.panaroma.commons.constants.WebConstants.TRANSFERRED_TO_SELF;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;
import static com.org.panaroma.commons.constants.WebConstants.UPI_ID;
import static com.org.panaroma.commons.constants.WebConstants.UTH_CATEGORY_OTHERS;
import static com.org.panaroma.commons.constants.WebConstants.UpiLiteConstants.UPI_LITE;
import static com.org.panaroma.commons.constants.WebConstants.VEHICLE_REG_NO;
import static com.org.panaroma.commons.constants.WebConstants.VISA_OCT_REVERSAL_REPORT_CODE;
import static com.org.panaroma.commons.constants.WebConstants.VisaOctConstant.VISA_DIRECT_CREDIT;
import static com.org.panaroma.commons.constants.WebConstants.VisaOctConstant.VISA_DIRECT_REVERSAL;
import static com.org.panaroma.commons.constants.WebConstants.WALLET;
import static com.org.panaroma.commons.constants.WebConstants.WalletMaintenanceChargeConstants.WMC_REVERSAL_NAME;
import static com.org.panaroma.commons.enums.LogoType.OTHER;
import static com.org.panaroma.commons.enums.LogoType.TRANSACTION_CATEGORY_ICON;
import static com.org.panaroma.commons.enums.LogoType.WALLET_ICON;
import static com.org.panaroma.commons.utils.CartUtility.isThisOmsOrder;
import static com.org.panaroma.web.utility.CtasNodeUtility.getReactivateWalletCta;
import static com.org.panaroma.web.utility.CtasNodeUtility.getWalletMaintenanceChargeDescCta;
import static com.org.panaroma.web.utility.GenericUtility.isRelayEvent;

//utility for drool files
@Log4j2
@Component
public class DroolsUtil {

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public DroolsUtil(final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		DroolsUtil.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	public static void getMaskedAccountNumberForV3(final List<InstrumentDtoV3> instrumentDtos, final String preMasking,
			final String postMasking, final int endDigitsSize) {
		for (InstrumentDtoV3 instrumentDto : instrumentDtos) {
			instrumentDto.setInstrumentDetail(getMaskedAccountNumber(instrumentDto.getInstrumentDetail(), preMasking,
					postMasking, endDigitsSize));
			instrumentDto
				.setName(getMaskedAccountNumber(instrumentDto.getName(), preMasking, postMasking, endDigitsSize));
		}
	}

	public static void setRefundDetails(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail tthd) {
		if (!CollectionUtils.isEmpty(tthd.getRefundDetails())) {
			List<RefundDetailsDto> refundDetailsList = new ArrayList<>();

			for (RefundDetails refundDetails : tthd.getRefundDetails()) {
				if (ClientStatusEnum.SUCCESS.getStatusKey().equals(refundDetails.getStatus())) {

					RefundDetailsDto refundDetailsDto = new RefundDetailsDto();
					refundDetailsDto
						.setStatus(ClientStatusEnum.getStatusEnumByKey(refundDetails.getStatus()).toString());
					refundDetailsDto.setAmount(Currency.getCurrencyAmountInHigherDenomination(refundDetails.getAmount(),
							tthd.getCurrency()));
					refundDetailsDto.setTxnId(refundDetails.getTxnId());
					refundDetailsDto
						.setTxnSource(TransactionSource.getTransactionSourceEnumByKey(tthd.getStreamSource())
							.getTransactionSource());
					refundDetailsDto.setDateTime(DateTimeUtility.getDateTime(refundDetails.getDate()));

					refundDetailsList.add(refundDetailsDto);
				}
			}
			detailApiResponseV3.setRefundDetails(refundDetailsList);
		}
	}

	public static void getMaskedAccountNumberForV2(final List<InstrumentDtoV2> instrumentDtos, final String preMasking,
			final String postMasking, final int endDigitsSize) {
		if (Objects.isNull(instrumentDtos)) {
			return;
		}
		for (InstrumentDtoV2 instrumentDto : instrumentDtos) {
			if (Objects.isNull(instrumentDto)) {
				continue;
			}
			List<InstrumentDetailsDto> instrumentDetailsMap = instrumentDto.getInstrumentDetailsMap();
			if (instrumentDetailsMap == null || instrumentDetailsMap.isEmpty()
					|| !instrumentDetailsMap.stream()
						.filter(instrumentDetailsDto -> LINKED_BANK.equalsIgnoreCase(instrumentDetailsDto.getLabel()))
						.findAny()
						.isPresent()) {
				return;
			}
			InstrumentDetailsDto instrumentDetails = instrumentDetailsMap.stream()
				.filter(instrumentDetailsDto -> LINKED_BANK.equalsIgnoreCase(instrumentDetailsDto.getLabel()))
				.findFirst()
				.get();
			getMaskedAccountNumber(instrumentDetails, preMasking, postMasking, endDigitsSize);
			instrumentDetailsMap.stream()
				.filter(instrumentDetailsDto -> LINKED_BANK.equalsIgnoreCase(instrumentDetailsDto.getLabel()))
				.findFirst()
				.get()
				.setValue(instrumentDetails.getValue());
		}

	}

	public static String getMaskedAccountNumber(final String accountNumber, final String preMasking,
			final String postMasking, final int endDigitsSize) {
		try {
			String maskedAccountNumber = null;
			Pattern pttrn = Pattern.compile(DROOLS_ACCT_NUM_DYNAMIC_KEY_REGEX);
			StringBuilder keyBuilder = new StringBuilder();
			keyBuilder.append(accountNumber);
			Matcher mtch = pttrn.matcher(keyBuilder);
			while (mtch.find()) {
				int startIndex = mtch.start();
				int endIndex = mtch.end();
				String dykey = keyBuilder.substring(startIndex + 2, endIndex - 1);
				String[] dykeys = dykey.split(" : ");
				if (dykeys != null && dykeys.length >= 2 && dykeys[1] != null) {
					keyBuilder.replace(startIndex, endIndex,
							preMasking + dykeys[1].substring(dykeys[1].length() - endDigitsSize) + postMasking);
					maskedAccountNumber = keyBuilder.toString();
				}
				else {
					return null;
				}
				mtch = pttrn.matcher(keyBuilder);
			}
			return StringUtils.isNotBlank(maskedAccountNumber) ? maskedAccountNumber : accountNumber;
		}
		catch (Exception e) {
			log.warn("Masked accountNumber is null for actual value :{}  due to the Exception: {}", accountNumber,
					CommonsUtility.exceptionFormatter(e));
			return null;
		}
	}

	private static void getMaskedAccountNumber(final InstrumentDetailsDto instrumentDetailsDto, final String preMasking,
			final String postMasking, final int endDigitsSize) {
		instrumentDetailsDto
			.setValue(getMaskedAccountNumber(instrumentDetailsDto.getValue(), preMasking, postMasking, endDigitsSize));
	}

	public static void getMaskedMobileNumberForV2(final List<InstrumentDtoV2> instrumentDtos,
			final int startIndexFromEndForMasking, final int endIndexFromEndForMasking) {
		if (Objects.isNull(instrumentDtos)) {
			return;
		}
		for (InstrumentDtoV2 instrumentDto : instrumentDtos) {
			if (Objects.isNull(instrumentDto)) {
				continue;
			}
			List<InstrumentDetailsDto> instrumentDetailsMap = instrumentDto.getInstrumentDetailsMap();
			if (instrumentDetailsMap == null || instrumentDetailsMap.isEmpty() || !instrumentDetailsMap.stream()
				.filter(instrumentDetailsDto -> LINKED_MOBILE_NO.equalsIgnoreCase(instrumentDetailsDto.getLabel()))
				.findAny()
				.isPresent()) {
				return;
			}
			InstrumentDetailsDto instrumentDetails = instrumentDetailsMap.stream()
				.filter(instrumentDetailsDto -> LINKED_MOBILE_NO.equalsIgnoreCase(instrumentDetailsDto.getLabel()))
				.findFirst()
				.get();
			getMaskedMobileNumber(instrumentDetails, startIndexFromEndForMasking, endIndexFromEndForMasking);
			instrumentDetailsMap.stream()
				.filter(instrumentDetailsDto -> LINKED_MOBILE_NO.equalsIgnoreCase(instrumentDetailsDto.getLabel()))
				.findFirst()
				.get()
				.setValue(instrumentDetails.getValue());

		}
	}

	private static void getMaskedMobileNumber(final InstrumentDetailsDto instrumentDetailsDto,
			final int startIndexFromEndForMasking, final int endIndexFromEndForMasking) {
		try {
			String key = instrumentDetailsDto.getValue();
			Pattern pttrn = Pattern.compile(DROOLS_MOB_NUM_DYNAMIC_KEY_REGEX);
			StringBuilder keyBuilder = new StringBuilder();
			keyBuilder.append(key);
			Matcher mtch = pttrn.matcher(keyBuilder);
			while (mtch.find()) {
				int startIndex = mtch.start();
				int endIndex = mtch.end();
				String dykey = keyBuilder.substring(startIndex + 2, endIndex - 1);
				String[] dykeys = dykey.split(" : ");
				if (dykeys != null && dykeys.length >= 2 && dykeys[1] != null) {
					int length = dykeys[1].length();
					if (length < 10) {
						log.error("Invalid mobile number passed, setting it as null");
						return;
					}
					dykeys[1] = dykeys[1].replace(dykeys[1].substring(length - startIndexFromEndForMasking,
							length - endIndexFromEndForMasking), "XXXX");

					keyBuilder.replace(startIndex, endIndex, dykeys[1]);
					instrumentDetailsDto.setValue(keyBuilder.toString());
				}
				else {
					instrumentDetailsDto.setValue(null);
				}
				mtch = pttrn.matcher(keyBuilder);
			}
		}
		catch (Exception e) {
			instrumentDetailsDto.setValue(null);
			return;
		}
	}

	// This function masks MobileNumber in instrumentDetails
	public static void getMaskedMobileNumber(final List<InstrumentDto> instrumentDtos,
			final int startIndexFromEndForMasking, final int endIndexFromEndForMasking) {
		if (Objects.isNull(instrumentDtos)) {
			return;
		}
		for (InstrumentDto instrumentDto : instrumentDtos) {
			if (Objects.isNull(instrumentDto)) {
				continue;
			}
			try {
				String key = instrumentDto.getInstrumentDetail();
				Pattern pttrn = Pattern.compile(DROOLS_MOB_NUM_DYNAMIC_KEY_REGEX);
				StringBuilder keyBuilder = new StringBuilder();
				keyBuilder.append(key);
				Matcher mtch = pttrn.matcher(keyBuilder);
				while (mtch.find()) {
					int startIndex = mtch.start();
					int endIndex = mtch.end();
					String dykey = keyBuilder.substring(startIndex + 2, endIndex - 1);
					String[] dykeys = dykey.split(" : ");
					if (dykeys != null && dykeys.length >= 2 && dykeys[1] != null) {
						int length = dykeys[1].length();
						if (length < 10) {
							log.error("Invalid mobile number passed, setting it as null");
							return;
						}
						dykeys[1] = dykeys[1].replace(dykeys[1].substring(length - startIndexFromEndForMasking,
								length - endIndexFromEndForMasking), "XXXX");

						keyBuilder.replace(startIndex, endIndex, dykeys[1]);
						instrumentDto.setInstrumentDetail(keyBuilder.toString());
					}
					else {
						instrumentDto.setInstrumentDetail(null);
						return;
					}
					mtch = pttrn.matcher(keyBuilder);
				}
			}
			catch (Exception e) {
				instrumentDto.setInstrumentDetail(null);
				return;
			}
		}
	}

	public static void setSecondPartyNameSuffix(final EsResponseTxn response, final String suffix) {
		if (Objects.nonNull(response) && Objects.nonNull(response.getSecondPartyInfo())) {
			String name = response.getSecondPartyInfo().getName();

			if (StringUtils.isNotBlank(name)) {
				response.getSecondPartyInfo().setName(name + SPACE + suffix);
			}
			else {
				response.getSecondPartyInfo().setName(suffix);
			}
		}

	}

	public static void setSelfNarrationDetailV2(final DetailApiResponseV2 detailApiResponseV2, final String narration) {

		if (detailApiResponseV2 == null) {
			return;
		}
		setInstrumentNarration(detailApiResponseV2.getFirstInstrument(), narration, true);
		setInstrumentNarration(detailApiResponseV2.getSecondInstrument(), narration, true);
	}

	public static void setOtherNarrationDetailV2(final DetailApiResponseV2 detailApiResponseV2,
			final String narration) {

		if (detailApiResponseV2 == null) {
			return;
		}
		setInstrumentNarration(detailApiResponseV2.getFirstInstrument(), narration, false);
		setInstrumentNarration(detailApiResponseV2.getSecondInstrument(), narration, false);
	}

	public static void setBankOtherInstrumentName(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail tthd, final String defaultString, final String prefixString,
			final String... keys) {
		if (detailApiResponseV2 == null || tthd == null || tthd.getContextMap() == null) {
			return;
		}
		InstrumentDtoV2 otherInstrument = getOtherInstrument(detailApiResponseV2);
		TransformedParticipant selfParticipant = getBankSelfParticipant(detailApiResponseV2);
		if (keys != null) {
			for (String key : keys) {
				if (StringUtils.isNotBlank(tthd.getContextMap().get(key))) {
					otherInstrument.setName(prefixString + tthd.getContextMap().get(key));
					return;
				}
				if (selfParticipant != null && selfParticipant.getContextMap() != null
						&& StringUtils.isNotBlank(selfParticipant.getContextMap().get(key))) {
					otherInstrument.setName(prefixString + selfParticipant.getContextMap().get(key));
					return;
				}
			}
		}
		otherInstrument.setNameVerifiedLogo(null); // since setting name to default..it
													// should be null
		otherInstrument.setName(defaultString);
	}

	private static TransformedParticipant getBankSelfParticipant(final DetailApiResponseV2 detailApiResponseV2) {
		if (detailApiResponseV2.getSecondInstrument() != null && detailApiResponseV2.getSecondInstrument().size() > 0
				&& detailApiResponseV2.getSecondInstrument().get(0) != null
				&& detailApiResponseV2.getSecondInstrument().get(0).getParticipantInfo() != null) {

			return detailApiResponseV2.getSecondInstrument().get(0).getParticipantInfo().getParticipant();
		}
		return null;
	}

	public static InstrumentDtoV2 getOtherInstrument(final DetailApiResponseV2 detailApiResponseV2) {
		InstrumentDtoV2 instrumentDtoV2 = getInstrumentBasedOnSelfFlag(detailApiResponseV2.getFirstInstrument(), true);
		if (instrumentDtoV2 != null) {
			return instrumentDtoV2;
		}
		instrumentDtoV2 = getInstrumentBasedOnSelfFlag(detailApiResponseV2.getSecondInstrument(), true);
		if (instrumentDtoV2 != null) {
			return instrumentDtoV2;
		}
		return null;
	}

	private static InstrumentDtoV2 getInstrumentBasedOnSelfFlag(final List<InstrumentDtoV2> instruments,
			final boolean checkSelfInstrument) {
		if (instruments != null) {
			for (InstrumentDtoV2 instrumentDtoV2 : instruments) {
				if (checkSelfInstrument && instrumentDtoV2.isSelfInstrument()) {
					return instrumentDtoV2;
				}
				else if (!instrumentDtoV2.isSelfInstrument()) {
					return instrumentDtoV2;
				}
			}
		}
		return null;
	}

	private static void setInstrumentNarration(final List<InstrumentDtoV2> instruments, final String narration,
			final boolean checkSelfInstrument) {
		if (instruments != null) {
			for (InstrumentDtoV2 instrumentDtoV2 : instruments) {
				if (checkSelfInstrument && instrumentDtoV2.isSelfInstrument()) {
					instrumentDtoV2.setNarration(narration);
				}
				else if (!checkSelfInstrument && !instrumentDtoV2.isSelfInstrument()) {
					instrumentDtoV2.setNarration(narration);
				}
			}
		}
	}

	// TODO Need to have a common method for internal masking logic for V1 and V2.
	// This function masks AccountNumber in instrumentDetails
	public static void getMaskedAccountNumberForV1(final List<InstrumentDto> instrumentDtos, final String preMasking,
			final String postMasking, final int endDigitsSize) {
		if (Objects.isNull(instrumentDtos)) {
			return;
		}
		for (InstrumentDto instrumentDto : instrumentDtos) {
			if (Objects.isNull(instrumentDto)) {
				continue;
			}
			try {
				String key = instrumentDto.getInstrumentDetail();
				Pattern pttrn = Pattern.compile(DROOLS_ACCT_NUM_DYNAMIC_KEY_REGEX);
				StringBuilder keyBuilder = new StringBuilder();
				keyBuilder.append(key);
				Matcher mtch = pttrn.matcher(keyBuilder);
				while (mtch.find()) {
					int startIndex = mtch.start();
					int endIndex = mtch.end();
					String dykey = keyBuilder.substring(startIndex + 2, endIndex - 1);
					String[] dykeys = dykey.split(" : ");
					if (dykeys != null && dykeys.length >= 2 && dykeys[1] != null) {
						keyBuilder.replace(startIndex, endIndex,
								preMasking + dykeys[1].substring(dykeys[1].length() - endDigitsSize) + postMasking);
						instrumentDto.setInstrumentDetail(keyBuilder.toString());
					}
					else {
						instrumentDto.setInstrumentDetail(null);
						break;
					}
					mtch = pttrn.matcher(keyBuilder);
				}
			}
			catch (Exception e) {
				log.info("Setting InstrumentDetail as null because of the Exception: {}",
						CommonsUtility.exceptionFormatter(e));
				instrumentDto.setInstrumentDetail(null);
				continue;
			}
		}
	}

	public static String getVpaInfo(final TransformedTransactionHistoryDetail tthd,
			final TransformedParticipant transformedParticipant) {
		if (tthd != null && transformedParticipant != null
				&& !(tthd.getEntityId().equals(transformedParticipant.getEntityId())
						&& tthd.getTxnIndicator().equals(transformedParticipant.getTxnIndicator()))
				&& PaymentSystemEnum.UPI.getPaymentSystemKey() == transformedParticipant.getPaymentSystem()
				&& transformedParticipant.getUpiData() != null) {
			return transformedParticipant.getUpiData().getVpa();
		}
		return WebConstants.BLANK_STRING;
	}

	public static void setInstrumentDetailsInDetailResponse(final TransformedParticipant transformedParticipant,
			final DetailApiResponse detailApiResponse, final String tpap) {
		if (detailApiResponse == null) {
			return;
		}
		for (InstrumentDto instrumentDto : detailApiResponse.getFirstInstrument()) {
			modifyInstrumentDetail(instrumentDto, transformedParticipant, tpap);
		}
		for (InstrumentDto instrumentDto : detailApiResponse.getSecondInstrument()) {
			modifyInstrumentDetail(instrumentDto, transformedParticipant, tpap);
		}
	}

	public static void setInstrumentDetailsMapInDetailResponse(final DetailApiResponseV2 detailApiResponse,
			final String tpap) {
		if (detailApiResponse == null) {
			return;
		}

		for (InstrumentDtoV2 instrumentDto : detailApiResponse.getFirstInstrument()) {
			if (instrumentDto.getInstrumentDetailsMap() != null && instrumentDto.getInstrumentDetailsMap().size() >= 1
					&& UPI_ID.equalsIgnoreCase(instrumentDto.getInstrumentDetailsMap().get(0).getLabel())) {
				String value = instrumentDto.getInstrumentDetailsMap().get(0).getValue() + " " + tpap;
				instrumentDto.getInstrumentDetailsMap().get(0).setValue(value);
			}
		}
	}

	private static void modifyInstrumentDetail(final InstrumentDto instrumentDto,
			final TransformedParticipant transformedParticipant, final String tpap) {
		if (instrumentDto.getParticipantInfo() != null && instrumentDto.getParticipantInfo().getParticipant() != null
				&& StringUtils.equalsIgnoreCase(instrumentDto.getParticipantInfo().getParticipant().getEntityId(),
						transformedParticipant.getEntityId())
				&& StringUtils.equalsIgnoreCase(instrumentDto.getParticipantInfo().getParticipant().getName(),
						transformedParticipant.getName())
				&& StringUtils.equalsIgnoreCase(instrumentDto.getParticipantInfo().getParticipant().getPaymentTxnId(),
						transformedParticipant.getPaymentTxnId())
				&& instrumentDto.getParticipantInfo().getParticipant().getUpiData() != null
				&& transformedParticipant.getUpiData() != null
				&& StringUtils.equalsIgnoreCase(
						instrumentDto.getParticipantInfo().getParticipant().getUpiData().getVpa(),
						transformedParticipant.getUpiData().getVpa())) {
			instrumentDto.setInstrumentDetail(instrumentDto.getInstrumentDetail() + " " + tpap);
		}
	}

	public static void setInstrumentDetailsInDetailWalletResponse(final TransformedParticipant transformedParticipant,
			final DetailApiResponseV2 detailApiResponse) {
		if (detailApiResponse == null) {
			return;
		}
		for (InstrumentDtoV2 instrumentDto : detailApiResponse.getFirstInstrument()) {
			modifyInstrumentDetailWallet(instrumentDto, transformedParticipant);
		}
		for (InstrumentDtoV2 instrumentDto : detailApiResponse.getSecondInstrument()) {
			modifyInstrumentDetailWallet(instrumentDto, transformedParticipant);
		}
	}

	private static void modifyInstrumentDetailWallet(final InstrumentDtoV2 instrumentDto,
			final TransformedParticipant transformedParticipant) {
		if (instrumentDto.getParticipantInfo() != null && instrumentDto.getParticipantInfo().getParticipant() != null
				&& StringUtils.equalsIgnoreCase(instrumentDto.getParticipantInfo().getParticipant().getEntityId(),
						transformedParticipant.getEntityId())
				&& StringUtils.equalsIgnoreCase(instrumentDto.getParticipantInfo().getParticipant().getPaymentTxnId(),
						transformedParticipant.getPaymentTxnId())
				&& instrumentDto.getInstrumentDetail() != null) {
			if (!instrumentDto.getInstrumentDetail().contains(WebConstants.WALLET_LINKED_TO)) {
				instrumentDto.setInstrumentDetail(WebConstants.WALLET_LINKED_TO + instrumentDto.getInstrumentDetail());
			}
		}
	}

	public static void setInstrumentDetailMapForAddMoney(final InstrumentDtoV2 instrumentDto) {
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();
		if (checkIfWalletDataExists(instrumentDto.getParticipantInfo())) {
			instrumentList.add(new InstrumentDetailsDto(LINKED_MOBILE_NO,
					instrumentDto.getParticipantInfo().getParticipant().getWalletData().getWalletMobileNumber()));
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}
	}

	private static boolean checkIfWalletDataExists(final ParticipantInfo participantInfo) {
		return Objects.nonNull(participantInfo) && Objects.nonNull(participantInfo.getParticipant())
				&& Objects.nonNull(participantInfo.getParticipant().getWalletData())
				&& Objects.nonNull(participantInfo.getParticipant().getWalletData().getWalletMobileNumber());
	}

	private static boolean checkIfBankDataExists(final ParticipantInfo participantInfo) {
		return Objects.nonNull(participantInfo) && Objects.nonNull(participantInfo.getParticipant())
				&& Objects.nonNull(participantInfo.getParticipant().getBankData())
				&& Objects.nonNull(participantInfo.getParticipant().getBankData().getAccNumber())
				&& Objects.nonNull(participantInfo.getParticipant().getBankData().getBankName());
	}

	private static boolean checkIfUpiDataExists(final ParticipantInfo participantInfo) {
		return Objects.nonNull(participantInfo) && Objects.nonNull(participantInfo.getParticipant())
				&& Objects.nonNull(participantInfo.getParticipant().getUpiData())
				&& Objects.nonNull(participantInfo.getParticipant().getUpiData().getVpa());
	}

	public static void setInstrumentDetailMapForP2pOutward(final InstrumentDtoV2 instrumentDto, final String status,
			final TransformedTransactionHistoryDetail tthd, @Nullable final Boolean isAccountNumberUnMaskingEnabled) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum
			.getPaymentSystemEnumByKey(instrumentDto.getParticipantInfo().getPaymentSystem());
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();
		if (PaymentSystemEnum.WALLET.equals(paymentSystem)) {
			if (ClientStatusEnum.FAILURE.getStatusValue().equalsIgnoreCase(status)
					&& checkIfWalletDataExists(instrumentDto.getParticipantInfo())) {
				instrumentList.add(new InstrumentDetailsDto(LINKED_MOBILE_NO,
						GenericUtility.getDroolsMaskedMobileNumber(instrumentDto.getParticipantInfo()
							.getParticipant()
							.getWalletData()
							.getWalletMobileNumber())));
			}
			else {
				instrumentList.add(new InstrumentDetailsDto(RECEIVED_IN2, WALLET));
				if (checkIfWalletDataExists(instrumentDto.getParticipantInfo())) {
					instrumentList.add(new InstrumentDetailsDto(LINKED_MOBILE_NO,
							GenericUtility.getDroolsMaskedMobileNumber(instrumentDto.getParticipantInfo()
								.getParticipant()
								.getWalletData()
								.getWalletMobileNumber())));
				}
			}
		}
		else if (PaymentSystemEnum.BANK.equals(paymentSystem)) {
			if (checkIfBankDataExists(instrumentDto.getParticipantInfo())) {

				// Todo: JiraId:
				if (DataValidationUtility.isOtherPartyBankDetailsRequired(tthd)) {
					String value = instrumentDto.getParticipantInfo().getParticipant().getBankData().getBankName()
							+ SPACE
							+ GenericUtility.getDroolsMaskedAccountNumber(
									instrumentDto.getParticipantInfo().getParticipant().getBankData().getAccNumber(),
									isAccountNumberUnMaskingEnabled);
					instrumentList.add(new InstrumentDetailsDto(LINKED_BANK, value));
				}
			}
		}
		else if (PaymentSystemEnum.UPI.equals(paymentSystem)) {

			if (UpiLiteUtility.isUpiLiteOfflineTxn(tthd)) {
				// Set instrument as UPI Lite in firstInstrument for P2P Outward txn done
				// via UPI Lite Offline.
				instrumentList.add(new InstrumentDetailsDto(RECEIVED_IN2, UPI_LITE));
			}
			else {
				if (checkIfUpiDataExists(instrumentDto.getParticipantInfo())
						&& !(GenericUtilityExtension.isVpa2AccountTxn(tthd))) {
					instrumentList.add(new InstrumentDetailsDto(UPI_ID,
							instrumentDto.getParticipantInfo().getParticipant().getUpiData().getVpa()));
				}
				if (checkIfBankDataExists(instrumentDto.getParticipantInfo())) {

					// Todo: JiraId
					if (DataValidationUtility.isOtherPartyBankDetailsRequired(tthd)) {
						String value = instrumentDto.getParticipantInfo().getParticipant().getBankData().getBankName()
								+ SPACE
								+ GenericUtility.getDroolsMaskedAccountNumber(instrumentDto.getParticipantInfo()
									.getParticipant()
									.getBankData()
									.getAccNumber(), isAccountNumberUnMaskingEnabled);
						instrumentList.add(new InstrumentDetailsDto(LINKED_BANK, value));
					}
				}
			}

		}
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())
					&& participant.getContextMap() != null && participant.getContextMap().containsKey(TXN_PURPOSE)
					&& GV_PURCHASED.equalsIgnoreCase(participant.getContextMap().get(TXN_PURPOSE))) {
				if (checkIfWalletDataExists(instrumentDto.getParticipantInfo())) {
					instrumentList = new ArrayList<>();
					instrumentList.add(new InstrumentDetailsDto(LINKED_MOBILE_NO,
							GenericUtility.getDroolsMaskedMobileNumber(instrumentDto.getParticipantInfo()
								.getParticipant()
								.getWalletData()
								.getWalletMobileNumber())));

				}
			}
		}
		if (!instrumentList.isEmpty()) {
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}
	}

	public static void setInstrumentDetailMapForP2pInward(final InstrumentDtoV2 instrumentDto,
			final TransformedTransactionHistoryDetail tthd, @Nullable final Boolean isAccountNumberUnMaskingEnabled) {
		if (Objects.isNull(instrumentDto.getParticipantInfo())
				|| Objects.isNull(instrumentDto.getParticipantInfo().getPaymentSystem())) {
			return;
		}
		PaymentSystemEnum paymentSystem = PaymentSystemEnum
			.getPaymentSystemEnumByKey(instrumentDto.getParticipantInfo().getPaymentSystem());
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();
		if (PaymentSystemEnum.WALLET.equals(paymentSystem)) {
			instrumentList.add(new InstrumentDetailsDto(SENT_FROM, WALLET));
			if (checkIfWalletDataExists(instrumentDto.getParticipantInfo())) {
				instrumentList.add(new InstrumentDetailsDto(LINKED_MOBILE_NO,
						GenericUtility.getDroolsMaskedMobileNumber(instrumentDto.getParticipantInfo()
							.getParticipant()
							.getWalletData()
							.getWalletMobileNumber())));
			}
		}
		else if (PaymentSystemEnum.UPI.equals(paymentSystem)) {
			if (UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(instrumentDto.getParticipantInfo().getParticipant())) {
				// set upi Lite as instrument for p2pInward
				instrumentList.add(new InstrumentDetailsDto(SENT_FROM, WebConstants.UpiLiteConstants.UPI_LITE));
			}
			else {
				if (checkIfUpiDataExists(instrumentDto.getParticipantInfo())) {
					instrumentList.add(new InstrumentDetailsDto(UPI_ID,
							instrumentDto.getParticipantInfo().getParticipant().getUpiData().getVpa()));
				}
				if (checkIfBankDataExists(instrumentDto.getParticipantInfo())) {

					// Todo: JiraId
					if (DataValidationUtility.isOtherPartyBankDetailsRequired(tthd)) {
						String value = instrumentDto.getParticipantInfo().getParticipant().getBankData().getBankName()
								+ SPACE
								+ GenericUtility.getDroolsMaskedAccountNumber(instrumentDto.getParticipantInfo()
									.getParticipant()
									.getBankData()
									.getAccNumber(), isAccountNumberUnMaskingEnabled);
						instrumentList.add(new InstrumentDetailsDto(LINKED_BANK, value));
					}
				}
			}
		}
		else if (PaymentSystemEnum.BANK.equals(paymentSystem)) {
			if (checkIfBankDataExists(instrumentDto.getParticipantInfo())) {

				// Todo: JiraId
				if (DataValidationUtility.isOtherPartyBankDetailsRequired(tthd)) {
					String value = instrumentDto.getParticipantInfo().getParticipant().getBankData().getBankName()
							+ SPACE
							+ GenericUtility.getDroolsMaskedAccountNumber(
									instrumentDto.getParticipantInfo().getParticipant().getBankData().getAccNumber(),
									isAccountNumberUnMaskingEnabled);
					instrumentList.add(new InstrumentDetailsDto(LINKED_BANK, value));
				}
			}
		}

		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())
					&& participant.getContextMap() != null && participant.getContextMap().containsKey(TXN_PURPOSE)
					&& GV_REDEEM.equalsIgnoreCase(participant.getContextMap().get(TXN_PURPOSE))
					&& instrumentDto.getParticipantInfo() != null
					&& instrumentDto.getParticipantInfo().getParticipant().getContextMap() != null
					&& instrumentDto.getParticipantInfo()
						.getParticipant()
						.getContextMap()
						.get(PAYER_MOBILE_NUMBER) != null) {
				instrumentList = new ArrayList<>();
				instrumentList.add(new InstrumentDetailsDto(LINKED_MOBILE_NO,
						GenericUtility.getDroolsMaskedMobileNumber(instrumentDto.getParticipantInfo()
							.getParticipant()
							.getContextMap()
							.get(PAYER_MOBILE_NUMBER))));
			}
		}
		if (!instrumentList.isEmpty()) {
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}

	}

	public static void setInstrumentDetailMapForP2p2m(final InstrumentDtoV2 instrumentDto,
			final TransformedTransactionHistoryDetail tthd) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum
			.getPaymentSystemEnumByKey(instrumentDto.getParticipantInfo().getPaymentSystem());
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();
		TransactionIndicator transactionIndicator = TransactionIndicator
			.getTransactionIndicatorEnumByKey(tthd.getTxnIndicator());
		if (PaymentSystemEnum.WALLET.equals(paymentSystem)
				&& checkIfWalletDataExists(instrumentDto.getParticipantInfo())
				&& Objects.nonNull(transactionIndicator)) {
			if (TransactionIndicator.DEBIT.equals(transactionIndicator)) {
				instrumentList.add(new InstrumentDetailsDto(RECEIVED_IN2, WALLET));
			}
			else {
				instrumentList.add(new InstrumentDetailsDto(SENT_FROM, WALLET));
			}
			instrumentList.add(new InstrumentDetailsDto(LINKED_MOBILE_NO,
					instrumentDto.getParticipantInfo().getParticipant().getWalletData().getWalletMobileNumber()));
		}
		if (!instrumentList.isEmpty()) {
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}
	}

	public static void setInstrumentDetailMapForP2m(final InstrumentDtoV2 instrumentDto,
			final TransformedTransactionHistoryDetail tthd) {
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();
		if (checkIfUpiDataExists(instrumentDto.getParticipantInfo())) {
			instrumentList.add(new InstrumentDetailsDto(UPI_ID,
					instrumentDto.getParticipantInfo().getParticipant().getUpiData().getVpa()));
		}
		List<InstrumentDetailsDto> fastagList = new ArrayList<>();
		for (TransformedParticipant transformedParticipant : tthd.getParticipants()) {
			if (transformedParticipant.getMerchantData() != null
					&& TransactionIndicator.CREDIT.getTransactionIndicatorKey()
						.equals(transformedParticipant.getTxnIndicator())
					&& FASTAG.equalsIgnoreCase(transformedParticipant.getMerchantData().getMerchantSubCategory())) {

				String additionalDetail = instrumentDto.getAdditionalDetail();
				if (null != additionalDetail) {
					String[] fastagTokens = additionalDetail.split("\n");
					Map<String, String> fastagMap = new HashMap<>();
					for (String fastagToken : fastagTokens) {
						int index = fastagToken.indexOf(":");
						fastagMap.put(fastagToken.substring(0, index + 1).trim(),
								fastagToken.substring(index + 1).trim());
					}
					if (fastagMap.containsKey(VEHICLE_REG_NO)
							&& StringUtils.isNotBlank(fastagMap.get(VEHICLE_REG_NO))) {
						fastagList.add(new InstrumentDetailsDto(VEHICLE_REG_NO, fastagMap.get(VEHICLE_REG_NO)));
					}
					if (fastagMap.containsKey(TOLL_CROSSED_ON)
							&& StringUtils.isNotBlank(fastagMap.get(TOLL_CROSSED_ON))) {
						fastagList.add(new InstrumentDetailsDto(TOLL_CROSSED_ON, fastagMap.get(TOLL_CROSSED_ON)));
					}
					if (fastagMap.containsKey(MONEY_DEDUCTED_ON)
							&& StringUtils.isNotBlank(fastagMap.get(MONEY_DEDUCTED_ON))) {
						fastagList.add(new InstrumentDetailsDto(MONEY_DEDUCTED_ON, fastagMap.get(MONEY_DEDUCTED_ON)));
					}
					if (fastagMap.containsKey(FASTAG_ID) && StringUtils.isNotBlank(fastagMap.get(FASTAG_ID))) {
						fastagList.add(new InstrumentDetailsDto(FASTAG_ID, fastagMap.get(FASTAG_ID)));
					}
					instrumentDto.setAdditionalDetail(null);
				}
			}
		}
		if (fastagList.size() >= 1) {
			instrumentDto.setInstrumentDetailsMap(fastagList);
		}
		else if (!instrumentList.isEmpty()) {
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}
	}

	public static void setInstrumentDetailMapForMandate(final InstrumentDtoV2 instrumentDto,
			final TransformedTransactionHistoryDetail tthd) {
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				instrumentList.add(new InstrumentDetailsDto(UPI_ID, participant.getUpiData().getVpa()));
			}
		}
		if (!instrumentList.isEmpty()) {
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}
	}

	public static void setInstrumentDetailMapForXferP2pOutward(final InstrumentDtoV2 instrumentDto,
			final TransformedTransactionHistoryDetail tthd, @Nullable final Boolean isAccountNumberUnMaskingEnabled) {
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();

		if (!ObjectUtils.isEmpty(instrumentDto.getParticipantInfo())
				&& !ObjectUtils.isEmpty(instrumentDto.getParticipantInfo().getParticipant())
				&& instrumentDto.getParticipantInfo().getParticipant().getContextMap() != null
				&& instrumentDto.getParticipantInfo().getParticipant().getContextMap().containsKey(BENEF_ACCT_NUM)) {

			String paytmBankName = IfscUtility.getBankNameWithCamelCase(PAYTM_BANK_IFSC);

			// Todo: JiraId
			if (DataValidationUtility.isOtherPartyBankDetailsRequired(tthd)) {
				String value = paytmBankName + SPACE
						+ GenericUtility.getDroolsMaskedAccountNumber(
								instrumentDto.getParticipantInfo().getParticipant().getContextMap().get(BENEF_ACCT_NUM),
								isAccountNumberUnMaskingEnabled);
				instrumentList.add(new InstrumentDetailsDto(LINKED_BANK, value));
			}
		}

		if (!instrumentList.isEmpty()) {
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}

	}

	public static void setInstrumentDetailMapForXferP2pInward(final InstrumentDtoV2 instrumentDto,
			@Nullable final Boolean isAccountNumberUnMaskingEnabled, final TransformedTransactionHistoryDetail tthd) {
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();

		if (!ObjectUtils.isEmpty(instrumentDto.getParticipantInfo())
				&& !ObjectUtils.isEmpty(instrumentDto.getParticipantInfo().getParticipant())
				&& instrumentDto.getParticipantInfo().getParticipant().getContextMap() != null
				&& instrumentDto.getParticipantInfo().getParticipant().getContextMap().containsKey(RMT_ACCT_NUM)) {

			String paytmBankName = IfscUtility.getBankNameWithCamelCase(PAYTM_BANK_IFSC);

			// Todo: JiraId
			if (DataValidationUtility.isOtherPartyBankDetailsRequired(tthd)) {
				String value = paytmBankName + SPACE
						+ GenericUtility.getDroolsMaskedAccountNumber(
								instrumentDto.getParticipantInfo().getParticipant().getContextMap().get(RMT_ACCT_NUM),
								isAccountNumberUnMaskingEnabled);
				instrumentList.add(new InstrumentDetailsDto(LINKED_BANK, value));
			}
		}

		if (!instrumentList.isEmpty()) {
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}

	}

	public static void setInstrumentDetailMapForUpiWalletCreditReversal(final InstrumentDtoV2 instrumentDto,
			final TransformedTransactionHistoryDetail tthd) {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum
			.getPaymentSystemEnumByKey(instrumentDto.getParticipantInfo().getPaymentSystem());
		List<InstrumentDetailsDto> instrumentList = new ArrayList<>();
		if (PaymentSystemEnum.UPI.equals(paymentSystem)) {
			if (checkIfUpiDataExists(instrumentDto.getParticipantInfo())
					&& !(GenericUtilityExtension.isVpa2AccountTxn(tthd))) {
				instrumentList.add(new InstrumentDetailsDto(UPI_ID,
						instrumentDto.getParticipantInfo().getParticipant().getUpiData().getVpa()));
			}
		}
		if (!instrumentList.isEmpty()) {
			instrumentDto.setInstrumentDetailsMap(instrumentList);
		}
	}

	public static boolean isNotSelfParticipant(final TransformedTransactionHistoryDetail tthd,
			final TransformedParticipant transformedParticipant) {
		return !(tthd.getEntityId().equals(transformedParticipant.getEntityId())
				&& tthd.getTxnIndicator().equals(transformedParticipant.getTxnIndicator()));
	}

	public static void setInstrumentShareNarrationDetailV2Debit(final TransformedTransactionHistoryDetail tthd,
			final DetailApiResponseV2 detailApiResponseV2, final String narration) {
		if (detailApiResponseV2 == null || StringUtils.isEmpty(narration)) {
			return;
		}
		for (InstrumentDtoV2 instrumentDto : detailApiResponseV2.getFirstInstrument()) {
			if (isDebitInstrument(tthd, instrumentDto)) {
				setInstrumentShareNarration(detailApiResponseV2.getFirstInstrument(), narration, true);
			}
		}
		for (InstrumentDtoV2 instrumentDto : detailApiResponseV2.getSecondInstrument()) {
			if (isDebitInstrument(tthd, instrumentDto)) {
				setInstrumentShareNarration(detailApiResponseV2.getSecondInstrument(), narration, true);
			}
		}
	}

	public static void setInstrumentShareNarrationDetailV2Credit(final TransformedTransactionHistoryDetail tthd,
			final DetailApiResponseV2 detailApiResponseV2, final String narration) {
		if (detailApiResponseV2 == null || StringUtils.isEmpty(narration)) {
			return;
		}
		for (InstrumentDtoV2 instrumentDto : detailApiResponseV2.getFirstInstrument()) {
			if (isCreditInstrument(tthd, instrumentDto)) {
				setInstrumentShareNarration(detailApiResponseV2.getFirstInstrument(), narration, true);
			}
		}
		for (InstrumentDtoV2 instrumentDto : detailApiResponseV2.getSecondInstrument()) {
			if (isCreditInstrument(tthd, instrumentDto)) {
				setInstrumentShareNarration(detailApiResponseV2.getSecondInstrument(), narration, true);
			}
		}
	}

	public static void setFirstInstrumentShareScreenNarration(final DetailApiResponseV2 detailApiResponseV2,
			final String narration) {
		if (detailApiResponseV2 == null || StringUtils.isEmpty(narration)) {
			return;
		}
		if (detailApiResponseV2.getFirstInstrument() != null && detailApiResponseV2.getFirstInstrument().size() > 0) {
			detailApiResponseV2.getFirstInstrument().get(0).setShareScreenNarration(narration);
		}
	}

	public static void setSecondInstrumentShareScreenNarration(final DetailApiResponseV2 detailApiResponseV2,
			final String narration) {
		if (detailApiResponseV2 == null || StringUtils.isEmpty(narration)) {
			return;
		}
		if (detailApiResponseV2.getSecondInstrument() != null && detailApiResponseV2.getSecondInstrument().size() > 0) {
			detailApiResponseV2.getSecondInstrument().get(0).setShareScreenNarration(narration);
		}
	}

	private static boolean isDebitInstrument(final TransformedTransactionHistoryDetail tthd,
			final InstrumentDtoV2 instrumentDto) {
		if (tthd != null && instrumentDto != null && instrumentDto.getParticipantInfo() != null
				&& !instrumentDto.getParticipantInfo().getIsDerivedParticipant()
				&& instrumentDto.getParticipantInfo().getParticipant() != null) {
			TransformedParticipant transformedParticipant = instrumentDto.getParticipantInfo().getParticipant();
			if (tthd.getEntityId().equals(transformedParticipant.getEntityId())
					&& tthd.getTxnIndicator().equals(transformedParticipant.getTxnIndicator())
					&& TransactionIndicator.DEBIT.getTransactionIndicatorKey()
						.equals(transformedParticipant.getTxnIndicator())) {
				return true;
			}
		}
		return false;
	}

	private static boolean isCreditInstrument(final TransformedTransactionHistoryDetail tthd,
			final InstrumentDtoV2 instrumentDto) {
		if (tthd != null && instrumentDto != null && instrumentDto.getParticipantInfo() != null
				&& !instrumentDto.getParticipantInfo().getIsDerivedParticipant()
				&& instrumentDto.getParticipantInfo().getParticipant() != null) {
			TransformedParticipant transformedParticipant = instrumentDto.getParticipantInfo().getParticipant();
			if (tthd.getEntityId().equals(transformedParticipant.getEntityId())
					&& tthd.getTxnIndicator().equals(transformedParticipant.getTxnIndicator())
					&& TransactionIndicator.CREDIT.getTransactionIndicatorKey()
						.equals(transformedParticipant.getTxnIndicator())) {
				return true;
			}
		}
		return false;
	}

	private static void setInstrumentShareNarration(final List<InstrumentDtoV2> instruments, final String narration,
			final boolean checkSelfInstrument) {
		if (instruments != null) {
			for (InstrumentDtoV2 instrumentDtoV2 : instruments) {
				if (checkSelfInstrument && instrumentDtoV2.isSelfInstrument()) {
					instrumentDtoV2.setShareScreenNarration(narration);
				}
				else if (!checkSelfInstrument && !instrumentDtoV2.isSelfInstrument()) {
					instrumentDtoV2.setShareScreenNarration(narration);
				}
			}
		}
	}

	// This function maps Transfer Reference No. with Vendor Name in DetailApiResponse
	public static void setAgentRefId(final DetailApiResponse detailApiResponse, final Map<String, String> contextMap,
			final String suffix) {
		String vendorName = contextMap.getOrDefault(WebConstants.VENDOR_NAME, WebConstants.PARTNER);
		String refNo = contextMap.get(WebConstants.TRANSFER_REF_NO);
		if (StringUtils.isNotBlank(refNo)) {
			if (Objects.isNull(detailApiResponse.getReferenceIdMap())) {
				detailApiResponse.setReferenceIdMap(new HashMap<String, String>());
			}
			if (Objects.isNull(detailApiResponse.getReferenceIds())) {
				detailApiResponse.setReferenceIds(new ArrayList<String>());
			}
			detailApiResponse.getReferenceIdMap().put(vendorName + suffix, refNo);
			detailApiResponse.getReferenceIds().add(vendorName + suffix + " : " + refNo);
		}
	}

	public static void setListingLogoOrderForWalletCashback(final TransformedTransactionHistoryDetail tthd,
			final EsResponseTxn listingResponse) {
		List<Logo> logoList;

		// add custom logo in wallet cashback transactions.
		logoList = listingResponse.getSecondPartyInfo().getLogoOrder() != null
				? listingResponse.getSecondPartyInfo().getLogoOrder() : new ArrayList<Logo>();

		if (tthd.getContextMap() != null && !StringUtils.isBlank(tthd.getContextMap().get(CUSTOM_SECOND_PARTY_LOGO))
				&& listingResponse.getSecondPartyInfo() != null) {

			logoList.add(new Logo(LogoOrderTypeEnum.URL, tthd.getContextMap().get(CUSTOM_SECOND_PARTY_LOGO)));
			listingResponse.getSecondPartyInfo().setLogoOrder(logoList);
		}
	}

	public static void setDetailLogoOrderForWalletCashback(final TransformedTransactionHistoryDetail tthd,
			final InstrumentDto instrumentDto) {

		List<Logo> logoList;

		// add custom logo in wallet cashback transactions.
		logoList = instrumentDto.getLogoOrder() != null ? instrumentDto.getLogoOrder() : new ArrayList<Logo>();

		if (tthd.getContextMap() != null && !StringUtils.isBlank(tthd.getContextMap().get(CUSTOM_SECOND_PARTY_LOGO))) {
			logoList.add(new Logo(LogoOrderTypeEnum.URL, tthd.getContextMap().get(CUSTOM_SECOND_PARTY_LOGO)));
			instrumentDto.setLogoOrder(logoList);
		}
	}

	public static void setDetailNarrationDetailV2(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail tthd, final String narration) {
		// Don't set default narration in case of VISA reportCode
		if (detailApiResponseV2 == null || (ObjectUtils.isNotEmpty(tthd.getContextMap())
				&& VISA_RECURRING_REPORT_CODE.contains(tthd.getContextMap().get(REPORT_CODE)))) {
			return;
		}
		detailApiResponseV2.setDetailNarration(narration);
	}

	public static void setDetailNarrationDetail(final DetailApiResponse detailApiResponse,
			final TransformedTransactionHistoryDetail tthd, final String narration) {

		// Don't set default narration in case of VISA reportCode
		if (detailApiResponse == null || (ObjectUtils.isNotEmpty(tthd.getContextMap())
				&& VISA_RECURRING_REPORT_CODE.contains(tthd.getContextMap().get(REPORT_CODE)))) {
			return;
		}
		detailApiResponse.setDetailNarration(narration);
	}

	// If uthNarration is present in cart details then set first instrument name with
	// uthNarration
	public static void setInstrumentNameFromCartDetails(final DetailApiResponse detailApiResponse,
			final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(detailApiResponse) || Objects.isNull(tthd)
				|| org.apache.commons.collections4.CollectionUtils.isEmpty(detailApiResponse.getFirstInstrument())) {
			return;
		}

		if (isThisOmsOrder(tthd) && Objects.nonNull(tthd.getCartDetails())
				&& org.apache.commons.collections4.CollectionUtils.isNotEmpty(tthd.getCartDetails().getItems())
				&& Objects.nonNull(tthd.getCartDetails().getItems().get(0))
				&& StringUtils.isNotBlank(tthd.getCartDetails().getItems().get(0).getUthNarration())) {
			detailApiResponse.getFirstInstrument()
				.get(0)
				.setName(tthd.getCartDetails().getItems().get(0).getUthNarration());
		}
	}

	public static void setTxnType(final DetailApiResponse detailApiResponse, final Integer mainTxnType) {

		TransactionTypeEnum txnTypeEnum = TransactionTypeEnum.getTransactionTypeEnumByKey(mainTxnType);

		if (detailApiResponse == null || txnTypeEnum == null) {
			return;
		}

		detailApiResponse.setTxnType(txnTypeEnum.getTransactionType());
	}

	public static void setTxnTypeV2(final DetailApiResponseV2 detailApiResponseV2, final Integer mainTxnType) {

		TransactionTypeEnum txnTypeEnum = TransactionTypeEnum.getTransactionTypeEnumByKey(mainTxnType);

		if (detailApiResponseV2 == null || txnTypeEnum == null) {
			return;
		}

		detailApiResponseV2.setTxnType(txnTypeEnum.getTransactionType());
	}

	public static void setTags(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail detail) {
		if (Objects.nonNull(detail) && Objects.nonNull(detail.getTags())) {
			detailApiResponseV2
				.setTags(detail.getTags().stream().map(TransformedTag::getTag).collect(Collectors.toList()));
		}
		detailApiResponseV2.setTagEnable(detailApiResponseV2.isTagEnable()
				&& isTagsEnabled(detailApiResponseV2.getStatus(), detail.getTxnDate(), TAG_FEATURE_ENABLE, detail));
	}

	public static void setTags(final DetailApiResponse detailApiResponse,
			final TransformedTransactionHistoryDetail detail) {
		if (Objects.nonNull(detail) && Objects.nonNull(detail.getTags())) {
			detailApiResponse
				.setTags(detail.getTags().stream().map(TransformedTag::getTag).collect(Collectors.toList()));
		}
		detailApiResponse.setTagEnable(
				isTagsEnabled(detailApiResponse.getStatus(), detail.getTxnDate(), TAG_FEATURE_ENABLE, detail));
	}

	public static void checkAndSetShowTagsFlag(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail detail) {
		if (Objects.nonNull(detail) && Objects.nonNull(detail.getTags())) {
			Boolean showTagEnable = configurablePropertiesHolder.getProperty(SHOW_TAG_ENABLE, Boolean.class);
			if (Boolean.TRUE.equals(showTagEnable)
					&& ClientStatusEnum.SUCCESS.getStatusValue().equalsIgnoreCase(detailApiResponseV3.getStatus())) {
				detailApiResponseV3
					.setTags(detail.getTags().stream().map(TransformedTag::getTag).collect(Collectors.toList()));
			}
			else {
				detailApiResponseV3.setTags(Collections.emptyList());
			}
		}
	}

	public static void setTagUpdationFlag(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail detail) {
		Boolean showTagEnable = configurablePropertiesHolder.getProperty(SHOW_TAG_ENABLE, Boolean.class);
		detailApiResponseV3.setTagsUpdationEnable(
				isTagsEnabled(detailApiResponseV3.getStatus(), detail.getTxnDate(), TAGS_UPDATION_ENABLE, detail)
						&& Boolean.TRUE.equals(showTagEnable));
	}

	public static void setUthCategory(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail detail) {
		try {
			if (Objects.nonNull(detail)) {
				// Setting category of txns this is being used for getting system tags in
				// get Tags API
				UthCategoryEnum category = AutoTaggingUtility.getUthCategory(detail);
				if (Objects.nonNull(category)) {
					detailApiResponseV2.setUthCategory(category.getUthCategoryId());
				}
			}
		}
		catch (Exception e) {
			log.error("Exception while setting categoryId in response. Exception : {}",
					CommonsUtility.exceptionFormatter(e));
		}
	}

	public static void setTagEnableFlag(final TransformedTransactionHistoryDetail tthd,
			final EsResponseTxn listingResponse) {
		listingResponse.setTagEnable(false);
	}

	private static Boolean isTagsEnabled(final String status, final Long txnDate, final String featureEnable,
			final TransformedTransactionHistoryDetail tthd) {
		long maxNumberOfDaysDiffAllowed = configurablePropertiesHolder.getProperty("maxAllowedDayDiffForTagging",
				Long.class);

		/**
		 * This compareTo will return :- 0 if both values are equal. > 0 if first duration
		 * is longer. < 0 if first duration is shorter.
		 */
		int result = Duration.ofDays(maxNumberOfDaysDiffAllowed)
			.compareTo(com.org.panaroma.commons.utils.DateTimeUtility.getDurationBetweenTwoEpochMillis(txnDate,
					System.currentTimeMillis()));

		Boolean tagEnable = true;
		try {
			tagEnable = configurablePropertiesHolder.getProperty(featureEnable, Boolean.class);
		}
		catch (Exception ex) {
			log.error("Exception while getting tag enable property Exception: {}",
					CommonsUtility.exceptionFormatter(ex));
		}

		return Boolean.TRUE.equals(tagEnable) && (result >= 0)
				&& ClientStatusEnum.SUCCESS.getStatusValue().equalsIgnoreCase(status) && !isRelayEvent(tthd)
				&& AutoTaggingUtility.isTaggingApplicableForThisTxn(tthd);
	}

	/*
	 * Method to create Split bill CTA and add in detail response This is called from rule
	 * file
	 */
	public static void createSplitBillCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label) {
		CtasNodeUtility.createSplitBillCtaNode(detailApiResponseV2, listingVisibleTxn, label);
	}

	/*
	 * Method to create location CTA and add in detail response This is called from rule
	 * file
	 */
	public static void createLocationCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label) {
		CtasNodeUtility.createLocationCtaNode(detailApiResponseV2, listingVisibleTxn, label);
	}

	/*
	 * Method to create account check balance CTA and add in detail response This is
	 * called from rule file
	 */
	public static void createAndAddAccountCheckBalanceCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label,
			final Boolean isSelfTransfer) {
		CtasNodeUtility.addAccountCheckBalanceCtaNodeInUpiInstrument(detailApiResponseV2, listingVisibleTxn, label,
				isSelfTransfer);
	}

	public static Integer getTransactionType(final TransformedTransactionHistoryDetail tthd) {
		return tthd.getMainTxnType();
	}

	public static String getPurpose(final TransformedTransactionHistoryDetail tthd) {
		String purpose = GenericUtility.getTxnPurpose(tthd);
		if (TransactionTypeEnum.P2P_OUTWARD.getTransactionTypeKey().equals(getTransactionType(tthd))) {
			purpose = GenericUtility.getTxnPurpose(tthd.getParticipants(),
					TransactionIndicator.DEBIT.getTransactionIndicatorKey());
		}
		else if (TransactionTypeEnum.P2P_INWARD.getTransactionTypeKey().equals(getTransactionType(tthd))) {
			purpose = GenericUtility.getTxnPurpose(tthd.getParticipants(),
					TransactionIndicator.CREDIT.getTransactionIndicatorKey());
		}
		if (TransactionSource.isPgTypeSource(tthd.getStreamSource())
				&& (PAYTM_GIFT_VOUCHER.equalsIgnoreCase(purpose) || PAYTMGIFTVOUCHER.equalsIgnoreCase(purpose))) {
			purpose = GV_PURCHASED;
		}
		return (purpose != null ? purpose : "");
	}

	public static void setShareScreenMetaMap(final DetailApiResponseV2 detailApiResponseV2) {
		Map<String, Object> metaMap = new HashMap<>();
		Map<String, String> unicode = new HashMap<>();
		unicode.put("${0}", "20B9");
		unicode.put("${1}", "1F60D");
		metaMap.putIfAbsent(WebConstants.ICON, unicode);
		detailApiResponseV2.setShareScreenMetaMap(metaMap);
	}

	public static String getMaskedNumber(final String number, final int unmaskedNoOfDigit) {
		if (StringUtils.isBlank(number) || unmaskedNoOfDigit <= 0) {
			return number;
		}
		return number.replaceAll(".(?=.{" + unmaskedNoOfDigit + "})", "X");
	}

	public static void addChatProfileCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail tthd) {
		CtaNode ctaNode = CtasNodeUtility.createChatProfileCtaNode(tthd);
		if (Objects.isNull(detailApiResponseV2.getCtasMap())) {
			detailApiResponseV2.setCtasMap(new HashMap<>());
		}
		if (Objects.nonNull(ctaNode) && Objects.nonNull(ctaNode.getValue()) && ctaNode.getValue() instanceof Map<?, ?>
				&& !CollectionUtils.isEmpty((Map<?, ?>) ctaNode.getValue())) {
			detailApiResponseV2.getCtasMap().put(CtaType.CHAT_PROFILE.getCtaType(), ctaNode);
		}
	}

	public static void addChatProfileCtaNode(final EsResponseTxn listingResponse,
			final TransformedTransactionHistoryDetail tthd) {
		CtaNode ctaNode = CtasNodeUtility.createChatProfileCtaNode(tthd);
		if (Objects.isNull(listingResponse.getCtasMap())) {
			listingResponse.setCtasMap(new HashMap<>());
		}
		if (Objects.nonNull(ctaNode) && Objects.nonNull(ctaNode.getValue()) && ctaNode.getValue() instanceof Map<?, ?>
				&& !CollectionUtils.isEmpty((Map<?, ?>) ctaNode.getValue())) {
			listingResponse.getCtasMap().put(CtaType.CHAT_PROFILE.getCtaType(), ctaNode);
		}
	}

	/**
	 * This is for Listing page of Wallet initiated walletToWallet transfer of money
	 * withing same user's account. <br>
	 * To be run after checking with
	 * {@link GenericUtility#isWalletToWallet(TransformedTransactionHistoryDetail)} <br>
	 * @param tthd ES source doc
	 * @param listingResponse final listing response pojo
	 */
	public static void setWalletToWalletData(final TransformedTransactionHistoryDetail tthd,
			final EsResponseTxn listingResponse) {
		SecondPartyInfo secondPartyInfo = new SecondPartyInfo();
		listingResponse.setNarration(TRANSFERRED_TO);
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				secondPartyInfo.setName(WalletType.getWalletTypeByKey(participant.getWalletData().getWalletType())
					.getWalletTypeUserViewValue());
				secondPartyInfo.setLogoUrl(LogoUtility.getLogo(
						WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()),
						WALLET_ICON));
				listingResponse.setSecondPartyInfo(secondPartyInfo);
			}
			else {
				UserInstrumentLogos userInstrumentLogos = new UserInstrumentLogos();
				userInstrumentLogos.setLogoUrl(LogoUtility.getLogo(
						WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()),
						WALLET_ICON));
				listingResponse.setUserInstrumentLogos(Collections.singletonList(userInstrumentLogos));
			}
		}
		listingResponse.setTxnIndicator(TransactionIndicator.CREDIT.getTransactionIndicatorKey().toString());
		listingResponse.setUserInstrumentNarration("Sent from");
		listingResponse.setUserInstrumentLogosV2(listingResponse.getUserInstrumentLogos());
	}

	/**
	 * This is for details page of Wallet initiated walletToWallet transfer of money
	 * withing same user's account. <br>
	 * To be run after checking with
	 * {@link GenericUtility#isWalletToWallet(TransformedTransactionHistoryDetail)} <br>
	 * @param tthd ES source doc
	 * @param detailApiResponseV2 final DetailV2 response pojo
	 */
	public static void setWalletToWalletData(final TransformedTransactionHistoryDetail tthd,
			final DetailApiResponseV2 detailApiResponseV2) {
		detailApiResponseV2.setDetailNarration(MONEY_TRANSFERRED);
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				detailApiResponseV2.setClosingBalance(participant.getWalletData().getClosingBalance());
				InstrumentDtoV2 debitInstrument = detailApiResponseV2.getSecondInstrument().get(0);
				debitInstrument.setNarration("From your");
				debitInstrument
					.setName(WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()));
				debitInstrument.setLogoUrl(LogoUtility.getLogo(debitInstrument.getName(), LogoType.WALLET_ICON));
			}
			else {
				InstrumentDtoV2 creditInstrument = detailApiResponseV2.getFirstInstrument().get(0);
				creditInstrument.setNarration("To your");
				creditInstrument
					.setName(WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()));
				creditInstrument.setLogoUrl(LogoUtility.getLogo(creditInstrument.getName(), LogoType.WALLET_ICON));
			}
		}
		detailApiResponseV2.setTxnIndicator(TransactionIndicator.CREDIT.getTransactionIndicatorKey().toString());
		detailApiResponseV2.setRepeatPayment(null);
		detailApiResponseV2.setChatRedirectInfo(null);
		InstrumentDtoV2 creditInstrument = detailApiResponseV2.getFirstInstrument().get(0);
		creditInstrument.setAdditionalDetail(null);
		creditInstrument.setNameVerifiedLogo(null);
		creditInstrument.setVerifiedNameLabel(null);
		creditInstrument.setInstrumentDetail(null);
		creditInstrument.setInstrumentDetailsMap(null);
		creditInstrument.setEntityDetails(new EntityDetails());

	}

	public static void setSelfTransferListing(final TransformedTransactionHistoryDetail tthd,
			final EsResponseTxn response) {
		if (Objects.isNull(tthd) || Objects.isNull(response)) {
			return;
		}
		// Setting LogoOrder
		List<Logo> logos = new ArrayList<>();
		String logoUrl = LogoUtility.getLogo(SELF_TRANSFER, OTHER);
		logos.add(new Logo(LogoOrderTypeEnum.URL, logoUrl));
		response.getSecondPartyInfo().setLogoOrder(logos);

		String beneficiaryBankDetail = getBankNameWithAccNo(tthd, TransactionIndicator.CREDIT);
		if (StringUtils.isNotBlank(beneficiaryBankDetail)) {

			// Setting beneficiary bank name with account number in Name
			response.getSecondPartyInfo().setName(beneficiaryBankDetail);

			// Setting Narrations for different transaction status
			String status = response.getStatus();
			switch (status) {
				case SUCCESS:
					response.setNarration(TRANSFERRED_TO_SELF + COMMA);
					break;
				case FAILED:
					response.setNarration(SELF + HYPHEN + TRANSFER + SPACE + FAILED + SPACE + TO);
					break;
				case PENDING:
					response.setNarration(SELF + HYPHEN + TRANSFER + SPACE + PENDING + SPACE + TO);
					break;
				default:
					// Do Nothing
			}
		}
	}

	public static void setSelfTransferDetail(final TransformedTransactionHistoryDetail tthd,
			final DetailApiResponseV2 response) {
		if (Objects.isNull(tthd) || Objects.isNull(response)) {
			return;
		}
		// Setting detailNarration for Success case, No change for Pending and Failure
		if (ClientStatusEnum.SUCCESS.getStatusKey().equals(tthd.getStatus())) {
			response.setDetailNarration(DETAIL_NARRATION_SELF_TRANSFER_SUCCESS.getLocaleMsgKey());
		}

		for (InstrumentDtoV2 firstInstrumentDto : response.getFirstInstrument()) {
			String beneficiaryBankDetail = getBankNameWithAccNo(tthd, TransactionIndicator.CREDIT);
			firstInstrumentDto.setNarration(StringUtils.isBlank(beneficiaryBankDetail) ? TO : TO_YOUR);
			if (StringUtils.isNotBlank(beneficiaryBankDetail)) {
				firstInstrumentDto.setName(beneficiaryBankDetail);
				firstInstrumentDto.setNameVerifiedLogo(null);
			}

			if (Objects.nonNull(firstInstrumentDto.getSourceDetails().getLogoOrder())) {
				List<Logo> logos = firstInstrumentDto.getSourceDetails().getLogoOrder();
				firstInstrumentDto.getEntityDetails().setLogoOrder(logos);
				firstInstrumentDto.getSourceDetails().setLogoOrder(null);
			}

			firstInstrumentDto.setInstrumentDetail(null);
			firstInstrumentDto.setInstrumentDetailsMap(null);
			firstInstrumentDto.setVerifiedNameLabel(null);
		}

		for (InstrumentDtoV2 secondInstrumentDto : response.getSecondInstrument()) {
			String remitterBankDetail = getBankNameWithAccNo(tthd, TransactionIndicator.DEBIT);

			TransformedParticipant instrumentParticipant = Objects.isNull(secondInstrumentDto.getParticipantInfo())
					? null : secondInstrumentDto.getParticipantInfo().getParticipant();

			// Not Setting Remitter Bank Details if txns done by UPI Lite
			if (StringUtils.isNotBlank(remitterBankDetail)
					&& Boolean.FALSE.equals(UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(instrumentParticipant))) {
				secondInstrumentDto.setName(remitterBankDetail);
				secondInstrumentDto.setInstrumentDetail(null);
			}
		}

	}

	private static String getBankNameWithAccNo(final TransformedTransactionHistoryDetail tthd,
			final TransactionIndicator txnIndicator) {
		if (Objects.isNull(tthd) || Objects.isNull(tthd.getParticipants()) || Objects.isNull(txnIndicator)) {
			return null;
		}
		TransformedBankData bankData = null;
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (txnIndicator.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				bankData = participant.getBankData();
			}
		}
		String bankDetail = null;
		if (Objects.nonNull(bankData) && Objects.nonNull(bankData.getBankName())
				&& StringUtils.isNotBlank(bankData.getAccNumber())) {
			String bankAccLastFourDigits = StringUtils.right(bankData.getAccNumber(), 4);
			String numbersOnlyAccountNumber = bankAccLastFourDigits.replaceAll("[^0-9]", "");
			String encodedAccountNumber = String.format("%4s", numbersOnlyAccountNumber).replace(' ', 'x');
			bankDetail = bankData.getBankName() + SPACE + HYPHEN + SPACE + encodedAccountNumber;
		}
		else if (Objects.nonNull(bankData) && StringUtils.isNotBlank(bankData.getBankName())) {
			return bankData.getBankName();
		}
		return bankDetail;
	}

	public static void addRequestMoneyCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail tthd) {
		CtaNode ctaNode = CtasNodeUtility.createRequestMoneyCtaNode(tthd);
		if (Objects.isNull(detailApiResponseV2.getCtasMap())) {
			detailApiResponseV2.setCtasMap(new HashMap<>());
		}
		if (null != ctaNode && !CollectionUtils.isEmpty((HashMap<String, String>) ctaNode.getValue())) {
			detailApiResponseV2.getCtasMap().put(CtaType.REQUEST_MONEY.getCtaType(), ctaNode);
		}
	}

	public static void createAndAddViewUpiLiteCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		CtasNodeUtility.addViewUpiLiteCtaNodeInUpiInstrument(detailApiResponseV2, listingVisibleTxn);
	}

	public static void createAndAddReactivateUpiLiteCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		CtasNodeUtility.addReactivateUpiLiteCtaNodeInUpiInstrument(detailApiResponseV2, listingVisibleTxn);
	}

	public static void setWalletMaintenanceChargeDetail(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail tthd) {
		detailApiResponseV2.setDetailNarration(WebConstants.WalletMaintenanceChargeConstants.CHARGES_DEDUCTED);
		// detailApiResponseV2.setNotes(WebConstants.WalletMaintenanceChargeConstants.WALLET_MAINTENANCE_CHARGE_IS_LEVIED_ON_YOUR_ACCOUNT);
		// detailApiResponseV2.setAmountSplit(getAmountSplit(tthd));
		detailApiResponseV2.getFirstInstrument().get(0).setNarration(WebConstants.WalletMaintenanceChargeConstants.FOR);
		detailApiResponseV2.getFirstInstrument().get(0).setInstrumentDetail(null);
		detailApiResponseV2.getFirstInstrument().get(0).setAdditionalDetail(null);
		detailApiResponseV2.getFirstInstrument().get(0).setParticipantInfo(null);
		detailApiResponseV2.getFirstInstrument().get(0).setName(WebConstants.WalletMaintenanceChargeConstants.WMC_NAME);
		detailApiResponseV2.setCtasMap(new HashMap<>());
		detailApiResponseV2.getFirstInstrument()
			.get(0)
			.setLogoUrl(LogoUtility.getLogo(WebConstants.WalletMaintenanceChargeConstants.WMC_DEDUCTED, OTHER));
		detailApiResponseV2.getFirstInstrument()
			.get(0)
			.getEntityDetails()
			.setLogoOrder(setLogoOrderForWalletMaintenanceCharge(tthd,
					WebConstants.WalletMaintenanceChargeConstants.WMC_DEDUCTED));

		detailApiResponseV2.getCtasMap().put(CtaType.DESCRIPTION.getCtaType(), getWalletMaintenanceChargeDescCta());
		detailApiResponseV2.getCtasMap().put(CtaType.REACTIVATE_WALLET.getCtaType(), getReactivateWalletCta());

		detailApiResponseV2.setDateTimeLabel(WebConstants.WalletMaintenanceChargeConstants.DEDUCTED_AT);

		setShareScreenFalse(detailApiResponseV2);
	}

	private static void setShareScreenFalse(final DetailApiResponseV2 detailApiResponseV2) {
		detailApiResponseV2.setShareScreen(false);
		detailApiResponseV2.setShareScreenNarration(null);
		detailApiResponseV2.setShareScreenTitle(null);
		detailApiResponseV2.setShareScreenText(null);
		detailApiResponseV2.setShareScreenMetaMap(null);
	}

	public static void setWalletMaintenanceChargeReversalDetail(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail tthd) {
		detailApiResponseV2.setDetailNarration(WebConstants.WalletMaintenanceChargeConstants.CHARGES_REVERSED);
		// detailApiResponseV2.setNotes(WebConstants.WalletMaintenanceChargeConstants.WALLET_MAINTENANCE_CHARGE_REFUNDED_TO_YOUR_ACCOUNT);
		// detailApiResponseV2.setAmountSplit(getAmountSplit(tthd));
		detailApiResponseV2.getFirstInstrument().get(0).setNarration(WebConstants.WalletMaintenanceChargeConstants.FOR);
		detailApiResponseV2.getFirstInstrument().get(0).setInstrumentDetail(null);
		detailApiResponseV2.getFirstInstrument().get(0).setAdditionalDetail(null);
		detailApiResponseV2.getFirstInstrument().get(0).setParticipantInfo(null);
		detailApiResponseV2.getFirstInstrument().get(0).setName(WMC_REVERSAL_NAME);
		detailApiResponseV2.setCtasMap(new HashMap<>());
		detailApiResponseV2.getFirstInstrument()
			.get(0)
			.setLogoUrl(LogoUtility.getLogo(WebConstants.WalletMaintenanceChargeConstants.WMC_REVERSED, OTHER));
		detailApiResponseV2.getFirstInstrument()
			.get(0)
			.getEntityDetails()
			.setLogoOrder(setLogoOrderForWalletMaintenanceCharge(tthd,
					WebConstants.WalletMaintenanceChargeConstants.WMC_REVERSED));

		setShareScreenFalse(detailApiResponseV2);
	}

	public static void setLoanRepaymentListing(final TransformedTransactionHistoryDetail tthd,
			final EsResponseTxn esResponseTxn) {
		esResponseTxn.setUserInstrumentNarration(WebConstants.LoanRepaymentConstants.SENT_FROM);
		esResponseTxn.setUserInstrumentLogosV2(esResponseTxn.getUserInstrumentLogos());
		if (StringUtils.isBlank(esResponseTxn.getSecondPartyInfo().getName())) {
			esResponseTxn.setNarration(PAID_FOR);
			esResponseTxn.getSecondPartyInfo().setName(LOAN_REPAYMENT);
		}
	}

	public static void setLoanRepaymentDetail(final TransformedTransactionHistoryDetail tthd,
			final DetailApiResponse response) {
		if (StringUtils.isBlank(response.getFirstInstrument().get(0).getName())) {
			response.getFirstInstrument().get(0).setNarration(WebConstants.LoanRepaymentConstants.FOR);
			response.getFirstInstrument().get(0).setName(REPAYMENT_OF_LOAN);
		}
	}

	public static void setDetailViewForLoanRepaymentAndItsRefund(final DetailApiResponse detailApiResponse,
			final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(detailApiResponse.getFirstInstrument())
				|| detailApiResponse.getFirstInstrument().size() == 0) {
			return;
		}
		InstrumentDto firstInstrument = detailApiResponse.getFirstInstrument().get(0);
		if (TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(tthd.getTxnType())) {
			setDetailViewForLoanRepaymentRefund(firstInstrument, tthd);
			return;
		}

		/**
		 * In case of ON_HOLD and RELEASED Repayment txns narration and name will be
		 * hardCoded.
		 */
		if (TransactionTypeEnum.ON_HOLD.getTransactionTypeKey().equals(tthd.getTxnType())
				|| TransactionTypeEnum.RELEASED.getTransactionTypeKey().equals(tthd.getTxnType())) {
			firstInstrument.setNarration(FOR);
			firstInstrument.setName(REPAYMENT_OF_LOAN);
		}
		firstInstrument.setInstrumentDetail(LOAN_APPLICATION_NUMBER + SPACE + tthd.getContextMap().get(LAN));
	}

	public static void setDetailViewForLoanRepaymentRefund(final InstrumentDto firstInstrument,
			final TransformedTransactionHistoryDetail tthd) {
		/**
		 * In case of P2M Repayment refund txns narration and name will be hardCoded.
		 */
		firstInstrument.setNarration(FOR);
		firstInstrument.setName(REPAYMENT_OF_LOAN);
		firstInstrument.setInstrumentDetail(LOAN_APPLICATION_NUMBER + SPACE + tthd.getContextMap().get(LAN));
	}

	public static void setListingViewForLoanRepaymentAndItsRefund(final EsResponseTxn listingResponse,
			final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(listingResponse.getSecondPartyInfo())) {
			return;
		}
		if (TransactionTypeEnum.ON_HOLD.getTransactionTypeKey().equals(tthd.getTxnType())) {
			listingResponse.setNarration(MONEY_ON_HOLD_FOR);
			listingResponse.getSecondPartyInfo().setName(LOAN_REPAYMENT);
		}
		else if (TransactionTypeEnum.RELEASED.getTransactionTypeKey().equals(tthd.getTxnType())) {
			listingResponse.setNarration(MONEY_RELEASED_FOR);
			listingResponse.getSecondPartyInfo().setName(LOAN_REPAYMENT);
		}
		else if (TransactionTypeEnum.P2M_REFUND.getTransactionTypeKey().equals(tthd.getTxnType())) {
			setListingViewForLoanRepaymentRefund(listingResponse);
		}
	}

	public static void setListingViewForLoanRepaymentRefund(final EsResponseTxn listingResponse) {
		listingResponse.setNarration(REFUND_FOR);
		listingResponse.getSecondPartyInfo().setName(LOAN_REPAYMENT);
	}

	public static void setNachDishonourListing(final EsResponseTxn esResponseTxn) {
		esResponseTxn.setUserInstrumentNarration(WebConstants.NachDishonourConstants.SENT_FROM);
		esResponseTxn.setUserInstrumentLogosV2(esResponseTxn.getUserInstrumentLogos());
		esResponseTxn.getSecondPartyInfo()
			.setLogoUrl(LogoUtility.getLogo(WebConstants.NachDishonourConstants.UPI_P2P_OUT_LOGO,
					TRANSACTION_CATEGORY_ICON));
	}

	public static void setNachDishonourDetail(final DetailApiResponse response) {
		response.getFirstInstrument().get(0).setName(WebConstants.NachDishonourConstants.NACH_RETURN_CHARGES);
	}

	public static void setNachDishonourDetailV2(final DetailApiResponseV2 response) {
		response.setDateTimeLabel(WebConstants.NachDishonourConstants.DATE_TIME_LABEL);
		response.getSecondInstrument().get(0).setCtasMap(null);
	}

	public static void setVisaOctListingNarration(final TransformedTransactionHistoryDetail tthd,
			final EsResponseTxn response) {
		String reportCode = tthd.getContextMap().get(REPORT_CODE);
		if (VISA_OCT_REVERSAL_REPORT_CODE.contains(reportCode)) {
			String merchanName = response.getSecondPartyInfo().getName();
			if (Objects.isNull(merchanName)) {
				response.setNarration(VISA_DIRECT_REVERSAL);
				response.setListingResponseMappingEnum(ListingResponseMappingEnum.VISA_DIRECT_REVERSAL_DEFAULT);
			}
		}
		else {
			String merchanName = response.getSecondPartyInfo().getName();
			if (Objects.isNull(merchanName)) {
				response.setNarration(VISA_DIRECT_CREDIT);
				response.setListingResponseMappingEnum(ListingResponseMappingEnum.VISA_DIRECT_CREDIT_DEFAULT);
			}
		}
	}

	public static List<Logo> setLogoOrderForWalletMaintenanceCharge(final TransformedTransactionHistoryDetail tthd,
			final String logoName) {
		List<Logo> logoOrder = new ArrayList<>();
		String walletMaintenanceChargeLogo = LogoUtility.getLogo(logoName, LogoType.OTHER);
		logoOrder.add(new Logo(LogoOrderTypeEnum.URL, walletMaintenanceChargeLogo));

		logoOrder.add(
				new Logo(LogoOrderTypeEnum.URL, LogoUtility.getLogo(MERCHANT_DEFAULT_LOGO, TRANSACTION_CATEGORY_ICON)));
		return logoOrder;
	}

	public static void setNachDishonourRefundListing(final EsResponseTxn esResponseTxn) {
		esResponseTxn.setUserInstrumentNarration(WebConstants.NachDishonourConstants.RECEIVED_IN);
		esResponseTxn.setUserInstrumentLogosV2(esResponseTxn.getUserInstrumentLogos());
		esResponseTxn.getSecondPartyInfo()
			.setLogoUrl(LogoUtility.getLogo(WebConstants.NachDishonourConstants.UPI_P2P_IN_LOGO,
					LogoType.TRANSACTION_CATEGORY_ICON));
	}

	public static void setUpiFirTxnDetail(final DetailApiResponse detailApiResponse) {
		if (Objects.isNull(detailApiResponse)) {
			return;
		}
		detailApiResponse.setRepeatPayment(null);
		if (ObjectUtils.isEmpty(detailApiResponse.getFirstInstrument())) {
			return;
		}
		detailApiResponse.getFirstInstrument().get(0).setAdditionalDetail("Foreign Remittance Received");
		detailApiResponse.getFirstInstrument().get(0).setInstrumentDetail(null);
	}

	public static void setUpiFirTxnDetailV2(final DetailApiResponseV2 detailApiResponseV2) {
		if (Objects.isNull(detailApiResponseV2)) {
			return;
		}
		detailApiResponseV2.setChatRedirectInfo(null);
		detailApiResponseV2.setCtasMap(null);
		if (ObjectUtils.isEmpty(detailApiResponseV2.getFirstInstrument())) {
			return;
		}
		detailApiResponseV2.getFirstInstrument().get(0).setInstrumentDetailsMap(null);
		detailApiResponseV2.getFirstInstrument().get(0).setCtasMap(null);
	}

	public static void setSecondPartyNameSuffixForDeaf(final EsResponseTxn response) {
		if (Objects.nonNull(response)) {
			response.getSecondPartyInfo()
				.setLogoUrl(LogoUtility.getLogo(WebConstants.WALLET_P2P_OUT_LOGO, LogoType.TRANSACTION_CATEGORY_ICON));
		}
	}

}