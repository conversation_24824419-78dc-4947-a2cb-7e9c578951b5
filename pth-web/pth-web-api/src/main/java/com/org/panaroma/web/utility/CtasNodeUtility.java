package com.org.panaroma.web.utility;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.commons.dto.CcEmiStatusEnum;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.EmiInfo;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.cart.CartItems;
import com.org.panaroma.commons.dto.es.TransformedBankData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.es.TransformedUPIData;
import com.org.panaroma.commons.enums.CtaType;
import com.org.panaroma.commons.enums.CtaValueType;
import com.org.panaroma.commons.localization.LocalizedDataCacheService;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.web.config.CtasPropertyConfig;
import com.org.panaroma.web.configuration.DeepLinks;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.detailAPI.GenericCtaInfo;
import com.org.panaroma.web.dto.detailAPI.TxnInfo;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.CtaNode;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.InstrumentDtoV2;
import com.org.panaroma.web.dto.detailAPI.detailV3.DetailApiResponseV3;
import com.org.panaroma.web.dto.detailAPI.detailV3.InstrumentDtoV3;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.BankDataConstants.IS_VIRTUAL_ENTITY_ID;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_CREATE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_REVOKE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_UPDATE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.DEEPLINK_DEAF_TXN_FAQ_CTA;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.ENABLE_DEAF_TXN_FAQ_CTA;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.LIST_OF_CTA_TYPE_ENABLED_FOR_DETAIL_PAGE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.LISTING_DESCRIPTION_CTA_FOR_RECURRING_MANDATE_ENABLED;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.LISTING_DESCRIPTION_CTA_FOR_IPO_MANDATE_ENABLED;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.DETAIL_DESCRIPTION_CTA_FOR_RECURRING_MANDATE_ENABLED;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.DETAIL_DESCRIPTION_CTA_FOR_IPO_MANDATE_ENABLED;
import static com.org.panaroma.commons.constants.Constants.BANK_NAME;
import static com.org.panaroma.commons.constants.LocalizationConstants.ENGLISH_LOCALE;
import static com.org.panaroma.commons.constants.WebConstants.ACCOUNT_NUMBER;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT_PARAM;
import static com.org.panaroma.commons.constants.WebConstants.AMPERSAND;
import static com.org.panaroma.commons.constants.WebConstants.BANK_NAME_PARAM;
import static com.org.panaroma.commons.constants.WebConstants.DEAF_TXN_CTA_LABEL;
import static com.org.panaroma.commons.constants.WebConstants.DEAF_TXN_CTA_LABEL_FOR_LOCALISATION;
import static com.org.panaroma.commons.constants.WebConstants.DEEPLINK;
import static com.org.panaroma.commons.constants.WebConstants.EQUAL_SYMBOL;
import static com.org.panaroma.commons.constants.WebConstants.GV_RECEIVER_ID;
import static com.org.panaroma.commons.constants.WebConstants.GV_RECEIVER_NAME;
import static com.org.panaroma.commons.constants.WebConstants.GV_RECEIVER_PHONE_NO;
import static com.org.panaroma.commons.constants.WebConstants.IFSC;
import static com.org.panaroma.commons.constants.WebConstants.IPO_MONEY_BLOCKED_CTA_LABEL;
import static com.org.panaroma.commons.constants.WebConstants.IPO_MONEY_UNBLOCKED_CTA_LABEL;
import static com.org.panaroma.commons.constants.WebConstants.LOCALIZATION_KEY_VIEW_STORECASH_BALANCE_CTA_LABEL;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_TRANSACTION_TYPES;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO_ONE;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.RECURRING_AUTOMATIC_PAYMENT_CTA_LABEL;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.dd_MMM_YYYY;
import static com.org.panaroma.commons.constants.WebConstants.NAME;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.SPACE_REPLACEMENT;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_ID;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_PURPOSE;
import static com.org.panaroma.commons.constants.WebConstants.VALIDITY;
import static com.org.panaroma.commons.constants.WebConstants.VALIDITY_END_DATE;
import static com.org.panaroma.commons.constants.WebConstants.VIEW_STORECASH_BALANCE_CTA;
import static com.org.panaroma.commons.constants.WebConstants.WHY_IS_THIS_MONEY_ON_HOLD;
import static com.org.panaroma.commons.constants.WebConstants.mandatoryParamMapForCtaChatProfile;
import static com.org.panaroma.commons.dto.Currency.getCurrencyAmountInHigherDenomination;
import static com.org.panaroma.commons.enums.CtaType.DESCRIPTION;
import static com.org.panaroma.commons.enums.CtaType.REACTIVATE_WALLET;
import static com.org.panaroma.commons.enums.CtaValueType.LINK;
import static com.org.panaroma.commons.utils.Utility.getOtherParticipant;

@Component
@Log4j2
public class CtasNodeUtility {

	private static boolean enableRequestMoneyCta;

	private static String upiLiteCtaDeeplink;

	private static String ipoMandateH5BaseUrl;

	private static String storecashCtaDeepLink;

	private static String convertEmiDeeplink;

	private static String viewEmiDeeplink;

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	private static String onHoldH5BaseUrl;

	private static RolloutStrategyHelper rolloutStrategyHelper;

	private static CtasPropertyConfig ctasPropertyConfig;

	private static List<String> listOfCtaTypeEnabledForDetailResponse;

	@Autowired
	public CtasNodeUtility(@Value("${ipoMandate.h5.base.url}") final String ipoMandateH5BaseUrl,
			@Value("${deepLink.storecash.cta}") final String storecashCtaDeepLink,
			@Value("${onHold.h5.base.url}") final String onHoldH5BaseUrl,
			@Value("${deepLink.convert.emi.cta}") final String convertEmiDeeplink,
			@Value("${deepLink.view.emi.cta}") final String viewEmiDeeplink,
			final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final RolloutStrategyHelper rolloutStrategyHelper, final DeepLinks deeplinks,
			final CtasPropertyConfig ctasPropertyConfig) throws JsonProcessingException {
		CtasNodeUtility.ipoMandateH5BaseUrl = ipoMandateH5BaseUrl;
		CtasNodeUtility.storecashCtaDeepLink = storecashCtaDeepLink;
		CtasNodeUtility.convertEmiDeeplink = convertEmiDeeplink;
		CtasNodeUtility.viewEmiDeeplink = viewEmiDeeplink;
		CtasNodeUtility.configurablePropertiesHolder = configurablePropertiesHolder;
		CtasNodeUtility.rolloutStrategyHelper = rolloutStrategyHelper;
		CtasNodeUtility.onHoldH5BaseUrl = onHoldH5BaseUrl;
		CtasNodeUtility.deeplinks = deeplinks;
		CtasNodeUtility.ctasPropertyConfig = ctasPropertyConfig;
	}

	private static DeepLinks deeplinks;

	@Autowired
	public void setStaticEnableRequestMoneyCta(@Value("${enable.requestMoney.cta}") final Boolean enableRequestMoneyCta,
			@Value("${deepLink.upi.lite.cta}") final String upiLiteCtaDeeplink) {
		CtasNodeUtility.enableRequestMoneyCta = enableRequestMoneyCta;
		CtasNodeUtility.upiLiteCtaDeeplink = upiLiteCtaDeeplink;
	}

	private static Map<String, List<String>> mandatoryParamMap = new HashMap<>();

	static {
		// Mandatory param in case of request money cta
		List<String> requestMoneyParamList = new ArrayList<>();
		requestMoneyParamList.add(WebConstants.IR_TRANSACTION_ID);
		requestMoneyParamList.add(WebConstants.URN);

		// map with key as type and value as list of mandatory param
		mandatoryParamMap.put(WebConstants.REQUEST_MONEY_TYPE, requestMoneyParamList);
	}

	public static void createSplitBillCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label) {
		CtaNode ctaNode = new CtaNode();
		ctaNode.setCtaType(CtaType.SPLIT_BILL.getCtaType());
		ctaNode.setLabel(label);
		ctaNode.setValueType(CtaValueType.PARAMS.getCtaValueType());
		Map<String, String> paramMap = new HashMap<>();
		String merchantName = null;
		String remark = null;
		for (TransformedParticipant tp : listingVisibleTxn.getParticipants()) {
			if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(tp.getEntityType())
					|| (EntityTypesEnum.USER.getEntityTypeKey().equals(tp.getEntityType())
							&& !listingVisibleTxn.getEntityId().equals(tp.getEntityId())
							&& TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(tp.getTxnIndicator()))) {
				merchantName = tp.getName();
			}
			if (EntityTypesEnum.USER.getEntityTypeKey().equals(tp.getEntityType())
					&& listingVisibleTxn.getEntityId().equals(tp.getEntityId())) {
				remark = tp.getRemarks();
			}
		}
		if (Strings.isNotBlank(merchantName)) {
			paramMap.put(WebConstants.SPLIT_NAME, merchantName);
		}
		else {
			return;
		}
		if (Strings.isNotBlank(detailApiResponseV2.getAmount())) {
			paramMap.put(WebConstants.AMOUNT_LABEL_SMALLCAPS, detailApiResponseV2.getAmount());
		}
		else {
			return;
		}
		if (Strings.isNotBlank(remark)) {
			paramMap.put(WebConstants.SPLIT_DESC, remark);
		}
		ctaNode.setValue(paramMap);
		ctaNode.setOrder(NumberUtils.LONG_ONE.intValue());
		ctaNode.setVersion(NumberUtils.LONG_ONE.intValue());

		if (Objects.isNull(detailApiResponseV2.getCtasMap())) {
			detailApiResponseV2.setCtasMap(new HashMap<>());
		}
		detailApiResponseV2.getCtasMap().put(CtaType.SPLIT_BILL.getCtaType(), ctaNode);
	}

	public static void createLocationCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label) {
		try {
			if (Objects.nonNull(listingVisibleTxn) && Objects.nonNull(listingVisibleTxn.getLocation())) {
				if (Objects.nonNull(listingVisibleTxn.getLocation().getGeoLocation())
						&& Objects.nonNull(listingVisibleTxn.getLocation().getGeoLocation().getLat())
						&& Objects.nonNull(listingVisibleTxn.getLocation().getGeoLocation().getLon())
						&& Boolean.FALSE.equals(Utility
							.isDefaultLocationCoordinates(listingVisibleTxn.getLocation().getGeoLocation()))) {
					CtaNode ctaNode = new CtaNode();
					ctaNode.setCtaType(CtaType.LOCATION.getCtaType());
					ctaNode.setLabel(label);
					ctaNode.setValueType(CtaValueType.PARAMS.getCtaValueType());
					Map<String, String> paramMap = new HashMap<>();
					paramMap.put(WebConstants.LATITUDE,
							listingVisibleTxn.getLocation().getGeoLocation().getLat().toString());
					paramMap.put(WebConstants.LONGITUDE,
							listingVisibleTxn.getLocation().getGeoLocation().getLon().toString());
					paramMap.put(WebConstants.TIMEZONE, listingVisibleTxn.getLocation().getTimeZone());

					ctaNode.setValue(paramMap);
					ctaNode.setOrder(NumberUtils.LONG_ONE.intValue());
					ctaNode.setVersion(NumberUtils.LONG_ONE.intValue());

					if (Objects.isNull(detailApiResponseV2.getCtasMap())) {
						detailApiResponseV2.setCtasMap(new HashMap<>());
					}
					detailApiResponseV2.getCtasMap().put(CtaType.LOCATION.getCtaType(), ctaNode);
				}
			}
		}
		catch (NullPointerException ex) {
			log.error("null pointer exception not able to get lat long for txnId {}", listingVisibleTxn.getTxnId());
			return;
		}
	}

	public static void createViewStoreCashBalanceCtaNode(final List<InstrumentDtoV3> instrumentDtoV3,
			final String label) {
		if (StringUtils.isBlank(storecashCtaDeepLink) || !isStoreCashCtaEnabled()) {
			return;
		}

		for (InstrumentDtoV3 instrumentDto : instrumentDtoV3) {
			if (Objects.nonNull(instrumentDto.getParticipantInfo())
					&& Objects.nonNull(instrumentDto.getParticipantInfo().getParticipant())
					&& PaymentSystemEnum.STORE_CASH.getPaymentSystemKey()
						.equals(instrumentDto.getParticipantInfo().getPaymentSystem())) {

				CtaNode ctaNode = CtaNode.builder()
					.ctaType(DESCRIPTION.getCtaType())
					.valueType(LINK.getCtaValueType())
					.label(label)
					.value(Collections.singletonMap(DEEPLINK, storecashCtaDeepLink))
					.build();
				if (Objects.isNull(instrumentDto.getCtasMap())) {
					instrumentDto.setCtasMap(new HashMap<>());
				}
				instrumentDto.getCtasMap().put(DESCRIPTION.getCtaType(), ctaNode);
				return;
			}
		}
	}

	private static Boolean isStoreCashCtaEnabled() {
		Boolean storecashCtaEnabled = configurablePropertiesHolder.getProperty(WebConstants.STORECASH_CTA_ENABLED,
				Boolean.class);
		return Objects.nonNull(storecashCtaEnabled) && Boolean.TRUE.equals(storecashCtaEnabled);
	}

	public static CtaNode createChatProfileCtaNode(final TransformedTransactionHistoryDetail tthd) {
		if (null == tthd) {
			return null;
		}
		try {
			CtaNode ctaNode = new CtaNode();
			ctaNode.setCtaType(CtaType.CHAT_PROFILE.getCtaType());
			ctaNode.setValueType(CtaValueType.PARAMS.getCtaValueType());
			if (tthd.getTxnIndicator() == null) {
				return null;
			}
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(tthd.getTxnIndicator())) {
				ctaNode.setLabel(WebConstants.CHAT_CTA_LABEL);
			}
			else if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(tthd.getTxnIndicator())) {
				ctaNode.setLabel(WebConstants.CHAT_AND_PAY);
			}
			Map<String, String> paramMap = new HashMap<>();
			boolean isCtaValid = true;

			TransformedParticipant otherUserParticipant;
			TransformedParticipant otherUpiParticipant;
			TransformedParticipant otherBankParticipant;
			TransformedParticipant otherMerchantParticipant;

			TransactionTypeEnum txnType = TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getMainTxnType());

			/**
			 * This if condition will handle special onus txn where vertical id = 174. In
			 * that case we have to send a special deeplink cta
			 */
			if (eligibleForSpecialDeepLinkCta(tthd)) {
				String verticalId = tthd.getCartDetails().getItems().get(0).getVerticalId();
				GenericCtaInfo genericCtaInfo = ctasPropertyConfig.getGenericCtaInfo(verticalId, CtaType.CHAT_PROFILE);
				String deeplink = DeeplinkUtility.getDeeplink(genericCtaInfo, verticalId, tthd);
				if (StringUtils.isBlank(deeplink)) {
					return null;
				}
				ctaNode.setValueType(CtaValueType.LINK.getCtaValueType());
				paramMap.put(DEEPLINK, deeplink);
			}
			else if (configurablePropertiesHolder.getProperty(WebApiV1Constants.CHAT_PROFILE_CTA_FOR_NON_ONUS_ENABLED,
					Boolean.class)) {
				if ((otherUserParticipant = getOtherUserParticipant(tthd)) != null) {
					// This will create chatProfile when other party is Paytm USER
					paramMap.put(WebConstants.CHAT_IDENTIFIER, otherUserParticipant.getEntityId());
					paramMap.put(WebConstants.NAME, otherUserParticipant.getName());
					paramMap.put(WebConstants.MOBILE_NUMBER,
							Utility.getValidPhoneNumber(otherUserParticipant, txnType, false));
					paramMap.put(WebConstants.CHAT_PROFILE_TYPE, WebConstants.CUSTOMER_TYPE);
				}
				else if ((otherMerchantParticipant = getOtherMerchantParticipant(tthd)) != null) {
					// This will create chatProfile when other party is paytm Merchant,
					// excluding ONUS Merchant.
					paramMap.put(WebConstants.CHAT_IDENTIFIER,
							otherMerchantParticipant.getMerchantData().getMerchantId());
					paramMap.put(WebConstants.NAME, otherMerchantParticipant.getName());
					paramMap.put(WebConstants.CHAT_PROFILE_TYPE, WebConstants.MERCHANT_TYPE);
				}
				else if ((otherUpiParticipant = getOtherUpiParticipant(tthd)) != null) {
					// This will create chatProfile when other party is 3rd party USER or
					// Merchant to whom you paid using vpa
					TransformedUPIData upiData = otherUpiParticipant.getUpiData();
					paramMap.put(WebConstants.CHAT_IDENTIFIER, upiData.getVpa());
					paramMap.put(WebConstants.VPA, upiData.getVpa());
					if (tthd.getContextMap() != null) {
						paramMap.put(WebConstants.TXN_CATEGORY,
								tthd.getContextMap().getOrDefault(WebConstants.UPI_TXN_CATEGORY, null));
					}
					paramMap.put(WebConstants.NAME, otherUpiParticipant.getName());
					if (Objects.isNull(otherUpiParticipant.getMerchantData())) {
						paramMap.put(WebConstants.CHAT_PROFILE_TYPE, WebConstants.VPA_TYPE);
					}
					else {
						paramMap.put(WebConstants.CHAT_PROFILE_TYPE, WebConstants.VPAM_TYPE);
					}
				}
				else if ((otherBankParticipant = getOtherBankParticipant(tthd)) != null) {
					// This will create chatProfile when other party is paid using
					// bankData
					TransformedBankData bankData = otherBankParticipant.getBankData();
					paramMap.put(WebConstants.CHAT_IDENTIFIER, tthd.getOtherPartyEntityId());
					paramMap.put(WebConstants.BANK_NAME_PARAM, bankData.getBankName());
					paramMap.put(WebConstants.ACC_REF_ID, tthd.getOtherPartyEntityId());
					paramMap.put(WebConstants.MASKED_ACC_NO, DroolsUtil.getMaskedNumber(bankData.getAccNumber(), 4));
					paramMap.put(WebConstants.NAME, otherBankParticipant.getName());
					paramMap.put(WebConstants.MOBILE_NUMBER,
							Utility.getValidPhoneNumber(otherBankParticipant, txnType, false));
					paramMap.put(WebConstants.BANK_LOGO_URL, otherBankParticipant.getLogoUrl());
					paramMap.put(WebConstants.IFSC, bankData.getIfsc());
					paramMap.put(WebConstants.CHAT_PROFILE_TYPE, WebConstants.BANK_TYPE);
				}
				else if (Utility.isGiftVoucherPurchasedForOthersCase(tthd) && tthd.getContextMap() != null) {
					paramMap.put(WebConstants.CHAT_IDENTIFIER, tthd.getContextMap().get(GV_RECEIVER_ID));
					paramMap.put(WebConstants.NAME, tthd.getContextMap().get(GV_RECEIVER_NAME));
					paramMap.put(WebConstants.MOBILE_NUMBER, tthd.getContextMap().get(GV_RECEIVER_PHONE_NO));
				}
			}
			isCtaValid = validateCta(paramMap,
					mandatoryParamMapForCtaChatProfile.get(paramMap.get(WebConstants.CHAT_PROFILE_TYPE)));
			if (!isCtaValid) {
				return null;
			}
			ctaNode.setValue(paramMap);
			ctaNode.setOrder(NumberUtils.LONG_ONE.intValue());
			ctaNode.setVersion(NumberUtils.LONG_ONE.intValue());
			return ctaNode;
		}
		catch (Exception ex) {
			log.warn("error occurred for txnId {} while setting chat profile cta ", tthd.getTxnId());
			return null;
		}
	}

	/**
	 * method to check if this specific tthd is eligible for special deeplink chat_profile
	 * cta
	 */
	private static boolean eligibleForSpecialDeepLinkCta(final TransformedTransactionHistoryDetail tthd) {

		if (!configurablePropertiesHolder.getProperty(WebApiV1Constants.CHAT_PROFILE_CTA_FOR_ONUS_ENABLED,
				Boolean.class)) {
			return false;
		}

		if (Objects.isNull(tthd) || Objects.isNull(tthd.getCartDetails())
				|| ObjectUtils.isEmpty(tthd.getCartDetails().getItems())) {
			return false;
		}

		CartItems cartItem = tthd.getCartDetails().getItems().get(0);

		if (StringUtils.isNotBlank(cartItem.getVerticalId())) {
			return true;
		}

		return false;
	}

	private static TransformedParticipant getOtherUserParticipant(final TransformedTransactionHistoryDetail tthd) {
		if (!CollectionUtils.isEmpty(tthd.getParticipants())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (participant != null && participant.getEntityId() != null && tthd.getEntityId() != null
						&& !tthd.getEntityId().equals(participant.getEntityId())
						&& (participant.getContextMap() == null
								|| !participant.getContextMap().containsKey(IS_VIRTUAL_ENTITY_ID))
						&& EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())) {
					return participant;
				}
			}
		}
		return null;
	}

	private static TransformedParticipant getOtherUpiParticipant(final TransformedTransactionHistoryDetail tthd) {
		// When paying to UPI 3rd party USER & MERCHANT we get entityId : NULL always.
		// UPI Data nonNUll
		// excluding VPA which contain : ifsc.npci
		// ifsc.npci present when you do UPI By entering Bank details to other party(which
		// not VPA Transfer type Txn)

		if (TransactionSource.UPI.getTransactionSourceKey().equals(tthd.getStreamSource())
				&& !CollectionUtils.isEmpty(tthd.getParticipants())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (participant != null && (participant.getEntityId() == null
						|| (participant.getEntityId() != null && !tthd.getEntityId().equals(participant.getEntityId())))
						&& participant.getUpiData() != null && StringUtils.isNotBlank(participant.getUpiData().getVpa())
						&& !GenericUtilityExtension.isVpa2AccountTxn(tthd)) {
					return participant;
				}
			}
		}
		return null;
	}

	private static TransformedParticipant getOtherBankParticipant(final TransformedTransactionHistoryDetail tthd) {
		/*
		 * if (tthd.getOtherPartyEntityId() != null &&
		 * (TransactionSource.PPBL.getTransactionSourceKey().equals(tthd.getStreamSource()
		 * ) ||
		 * TransactionSource.TS.getTransactionSourceKey().equals(tthd.getStreamSource()))
		 * &&
		 * OUTWARD_REPORT_CODES_SENT_TO_CHAT.contains(tthd.getContextMap().get(REPORT_CODE
		 * )) && !CollectionUtils.isEmpty(tthd.getParticipants())) { for
		 * (TransformedParticipant participant : tthd.getParticipants()) { if (participant
		 * != null && !tthd.getEntityId().equals(participant.getEntityId()) &&
		 * participant.getBankData() != null) { return participant; } } }
		 */
		return null;
	}

	private static TransformedParticipant getOtherMerchantParticipant(final TransformedTransactionHistoryDetail tthd) {
		// This will for only for Paytm Merchant excluding ONUS Merchant.
		// Show chatProfile CTA when PG Event is present.
		// show chatProfile CTA when user did UPI Collect.
		if ((TransactionSource.isPgTypeSource(tthd.getStreamSource()) || UtilityExtension.isUpiCollectOrIntentTxn(tthd))
				&& MERCHANT_TRANSACTION_TYPES
					.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(tthd.getTxnType()))
				&& !CollectionUtils.isEmpty(tthd.getParticipants())) {
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (participant != null && participant.getEntityId() != null && tthd.getEntityId() != null
						&& Boolean.FALSE.equals(tthd.getEntityId().equals(participant.getEntityId()))
						&& participant.getMerchantData() != null
						&& participant.getMerchantData().getMerchantId() != null
						&& Boolean.FALSE.equals(MerchantTypeEnum.ONUS.getMerchantTypeKey()
							.equals(participant.getMerchantData().getMerchantType()))) {
					return participant;
				}
			}
		}
		return null;
	}

	/*
	 * Method to create account check balance CTA and add in Upi Instrument Dto of Detail
	 * Response This is called from rule file
	 */
	public static void addAccountCheckBalanceCtaNodeInUpiInstrument(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label,
			final Boolean isSelfTransfer) {
		if (Objects.isNull(detailApiResponseV2) || ObjectUtils.isEmpty(detailApiResponseV2.getSecondInstrument())
				|| ObjectUtils.isEmpty(detailApiResponseV2.getFirstInstrument())) {
			return;
		}

		if (Objects.nonNull(detailApiResponseV2.getSecondInstrument().get(0).getParticipantInfo()) && Utility
			.isUpiViaCcTxn(detailApiResponseV2.getSecondInstrument().get(0).getParticipantInfo().getParticipant())) {
			return;
		}

		if (UpiLiteUtility.isUpiLiteOfflineTxn(listingVisibleTxn)) {
			return;
		}

		// Adding Check Balance CTA in firstInstrument for Self Transfer transactions
		if (isSelfTransfer) {
			addCheckBalanceCta(detailApiResponseV2.getFirstInstrument(), label, listingVisibleTxn);
		}
		addCheckBalanceCta(detailApiResponseV2.getSecondInstrument(), label, listingVisibleTxn);
	}

	public static void addCheckBalanceCta(final List<InstrumentDtoV2> instrumentList, final String label,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {
		/*
		 * participantInfo -> not null participantInfo.paymentSystem -> UPI Or Ppbl
		 */
		for (InstrumentDtoV2 instrumentDto : instrumentList) {
			if (Objects.nonNull(instrumentDto.getParticipantInfo())
					&& Objects.nonNull(instrumentDto.getParticipantInfo().getParticipant())
					&& (PaymentSystemEnum.UPI.getPaymentSystemKey()
						.equals(instrumentDto.getParticipantInfo().getPaymentSystem())
							|| PaymentSystemEnum.PPBL.getPaymentSystemKey()
								.equals(instrumentDto.getParticipantInfo().getPaymentSystem()))
					&& isValidInstrumentForCheckBalance(listingVisibleTxn,
							instrumentDto.getParticipantInfo().getParticipant())) {
				TransformedParticipant participant = instrumentDto.getParticipantInfo().getParticipant();

				// creating accountCheckBalance cta node
				CtaNode accountCheckBalance = getAccountCheckBalanceCta(participant, label);

				// Adding account check balance cta node if it not null
				if (Objects.nonNull(accountCheckBalance)) {
					Map<String, CtaNode> ctasMap = instrumentDto.getCtasMap() == null ? new HashMap<>()
							: instrumentDto.getCtasMap();
					ctasMap.put(CtaType.ACCOUNT_CHECK_BALANCE.getCtaType(), accountCheckBalance);
					instrumentDto.setCtasMap(ctasMap);
				}
			}
		}
	}

	private static CtaNode getAccountCheckBalanceCta(final TransformedParticipant participant, final String label) {

		CtaNode accountCheckBalance = null;

		if (participant != null && participant.getBankData() != null
				&& StringUtils.isNotBlank(participant.getBankData().getIfsc())
				&& StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
			accountCheckBalance = new CtaNode();
			accountCheckBalance.setCtaType(CtaType.ACCOUNT_CHECK_BALANCE.getCtaType());
			accountCheckBalance.setLabel(label);
			accountCheckBalance.setValueType(CtaValueType.PARAMS.getCtaValueType());

			Map<String, String> params = new HashMap<>();
			params.put(ACCOUNT_NUMBER, participant.getBankData().getAccNumber());
			params.put(IFSC, participant.getBankData().getIfsc());

			accountCheckBalance.setValue(params);
		}
		return accountCheckBalance;
	}

	public static CtaNode createRequestMoneyCtaNode(final TransformedTransactionHistoryDetail tthd) {
		try {
			// check thd and enableRequestMoneyCta
			if (null == tthd || !enableRequestMoneyCta) {
				return null;
			}
			// No cta in case of debit
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(tthd.getTxnIndicator())) {
				return null;
			}
			// Make Map of required value and validate them
			Map<String, String> paramMap = new HashMap<>();
			boolean isValidCta = true;

			if (TransactionTypeEnum.P2P_INWARD_REMITTANCE.getTransactionTypeKey().equals(tthd.getMainTxnType())
					&& tthd.getContextMap() != null
					&& tthd.getContextMap().containsKey(WebConstants.REQUEST_MONEY_ENABLE)
					&& WebConstants.REQUEST_MONEY_TRUE
						.equalsIgnoreCase(tthd.getContextMap().get(WebConstants.REQUEST_MONEY_ENABLE))) {
				paramMap.put(WebConstants.IR_TRANSACTION_ID, tthd.getContextMap().get(WebConstants.PG_TRANSACTION_ID));
				paramMap.put(WebConstants.URN, tthd.getContextMap().get(WebConstants.TRANSFER_REF_NO));
			}
			isValidCta = validateCta(paramMap, mandatoryParamMap.get(WebConstants.REQUEST_MONEY_TYPE));
			if (!isValidCta) {
				return null;
			}
			// Now creating Cta for Request Money
			CtaNode ctaNode = new CtaNode();
			ctaNode.setCtaType(CtaType.REQUEST_MONEY.getCtaType());
			ctaNode.setValueType(CtaValueType.PARAMS.getCtaValueType());
			ctaNode.setLabel(WebConstants.REQUEST_MONEY);
			ctaNode.setValue(paramMap);
			ctaNode.setOrder(NumberUtils.LONG_ONE.intValue());
			ctaNode.setVersion(NumberUtils.LONG_ONE.intValue());
			return ctaNode;
		}
		catch (Exception ex) {
			// Mainly because of null value for any one of the above param
			log.error("exception occurred for txnId {} while setting request money cta", tthd.getTxnId());
			return null;
		}
	}

	public static void addViewUpiLiteCtaNodeInUpiInstrument(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(detailApiResponseV2) || ObjectUtils.isEmpty(detailApiResponseV2.getSecondInstrument())
				|| ObjectUtils.isEmpty(detailApiResponseV2.getFirstInstrument())) {
			return;
		}

		// Adding View Upi Lite CTA in firstInstrument for Activation
		if (TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.getTransactionTypeKey().equals(tthd.getMainTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey().equals(tthd.getMainTxnType())) {
			addViewUpiLiteCta(detailApiResponseV2.getFirstInstrument(), tthd);
		}
		else {
			// Adding View Upi Lite CTA in secondInstrument for Activation
			addViewUpiLiteCta(detailApiResponseV2.getSecondInstrument(), tthd);
		}
	}

	public static void addViewUpiLiteCta(final List<InstrumentDtoV2> instrumentList,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {

		for (InstrumentDtoV2 instrumentDto : instrumentList) {
			if (Objects.nonNull(instrumentDto.getParticipantInfo())
					&& Objects.nonNull(instrumentDto.getParticipantInfo().getParticipant()) && UpiLiteUtility
						.isUpiLiteTxnAndPaymentInstrument(instrumentDto.getParticipantInfo().getParticipant())) {

				// creating viewUpiLite cta node
				CtaNode viewUpiLite = getViewUpiLiteCtaNode(listingVisibleTxn);

				// Adding account check balance cta node if it not null
				if (Objects.nonNull(viewUpiLite)) {
					Map<String, CtaNode> ctasMap = instrumentDto.getCtasMap() == null ? new HashMap<>()
							: instrumentDto.getCtasMap();
					ctasMap.put(CtaType.VIEW_UPI_LITE.getCtaType(), viewUpiLite);
					instrumentDto.setCtasMap(ctasMap);
				}
			}
		}
	}

	public static CtaNode getViewUpiLiteCtaNode(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		try {
			// check tthd
			if (null == listingVisibleTxn) {
				return null;
			}
			// Now creating Cta for Request Money
			return CtaNode.builder()
				.ctaType(CtaType.VIEW_UPI_LITE.getCtaType())
				.valueType(CtaValueType.LINK.getCtaValueType())
				.value(Collections.singletonMap(DEEPLINK, upiLiteCtaDeeplink))
				.label(WebConstants.UpiLiteConstants.VIEW_UPI_LITE)
				.build();

		}
		catch (Exception ex) {
			// Mainly because of null value for any one of the above param
			log.error("exception occurred for txnId {} while setting upi lite cta", listingVisibleTxn.getTxnId());
			return null;
		}
	}

	public static void addReactivateUpiLiteCtaNodeInUpiInstrument(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(detailApiResponseV2) || ObjectUtils.isEmpty(detailApiResponseV2.getSecondInstrument())
				|| ObjectUtils.isEmpty(detailApiResponseV2.getFirstInstrument())) {
			return;
		}

		// Adding View Upi Lite CTA in firstInstrument for Activation
		addReactivateUpiLiteCta(detailApiResponseV2.getFirstInstrument(), tthd);

	}

	public static void addReactivateUpiLiteCta(final List<InstrumentDtoV2> instrumentList,
			final TransformedTransactionHistoryDetail listingVisibleTxn) {

		for (InstrumentDtoV2 instrumentDto : instrumentList) {
			if (Objects.nonNull(instrumentDto.getParticipantInfo())
					&& Objects.nonNull(instrumentDto.getParticipantInfo().getParticipant()) && UpiLiteUtility
						.isUpiLiteTxnAndPaymentInstrument(instrumentDto.getParticipantInfo().getParticipant())) {

				// creating viewUpiLite cta node
				CtaNode reactivateUpiLite = getReactivateUpiLiteCtaNode(listingVisibleTxn);

				// Adding account check balance cta node if it not null
				if (Objects.nonNull(reactivateUpiLite)) {
					Map<String, CtaNode> ctasMap = instrumentDto.getCtasMap() == null ? new HashMap<>()
							: instrumentDto.getCtasMap();
					ctasMap.put(CtaType.REACTIVATE_LITE.getCtaType(), reactivateUpiLite);
					instrumentDto.setCtasMap(ctasMap);
				}
			}
		}
	}

	public static CtaNode getReactivateUpiLiteCtaNode(final TransformedTransactionHistoryDetail tthd) {
		try {
			// check tthd
			if (null == tthd) {
				return null;
			}

			// Now creating Cta for Request Money
			return CtaNode.builder()
				.ctaType(CtaType.REACTIVATE_LITE.getCtaType())
				.valueType(CtaValueType.LINK.getCtaValueType())
				.value(Collections.singletonMap(DEEPLINK, upiLiteCtaDeeplink))
				.label(WebConstants.UpiLiteConstants.REACTIVATE)
				.build();

		}
		catch (Exception ex) {
			// Mainly because of null value for any one of the above param
			log.error("exception occurred for txnId {} while setting upi lite cta", tthd.getTxnId());
			return null;
		}
	}

	// This method is used to set cta map in listing response.
	public static void setListingCtaMap(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final EsResponseTxn response) {
		// PTH-719-> In this method separate flags for Recurring Mandate and Ipo are made
		// for CTA on listing.
		CtaNode ctaNode = getMandateCtaForListing(listingVisibleTxn, response);
		if (Objects.nonNull(ctaNode)) {
			Map<String, CtaNode> ctasMap = Objects.isNull(response.getCtasMap()) ? new HashMap<>()
					: response.getCtasMap();
			ctasMap.put(ctaNode.getCtaType(), ctaNode);
			response.setCtasMap(ctasMap);
		}
	}

	// If flags are enabled then hyperlink will be set in Listing page for description Cta
	// type otherwise ctaNode will be returned as null.
	public static CtaNode getMandateCtaForListing(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final EsResponseTxn response) {
		CtaNode ctaNode = null;
		boolean isListingCtaEnabledForRecurringMandate = configurablePropertiesHolder
			.getProperty(LISTING_DESCRIPTION_CTA_FOR_RECURRING_MANDATE_ENABLED, Boolean.class);
		boolean isListingCtaEnabledForIpo = configurablePropertiesHolder
			.getProperty(LISTING_DESCRIPTION_CTA_FOR_IPO_MANDATE_ENABLED, Boolean.class);

		if (TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
				&& ClientStatusEnum.SUCCESS.getStatusKey().equals(listingVisibleTxn.getStatus())
				&& isListingCtaEnabledForIpo) {
			ctaNode = getListingIpoCtaNode(listingVisibleTxn);
		}
		if (MandateUtility.txnValidForRecurring(listingVisibleTxn) && isListingCtaEnabledForRecurringMandate) {
			ctaNode = getListingRecurringCtaNode(listingVisibleTxn);
		}
		return ctaNode;
	}

	public static CtaNode getListingIpoCtaNode(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		String txnPurpose = GenericUtilityExtension.getTransactionPurpose(listingVisibleTxn);
		CtaNode ctaNode = null;
		if (PURPOSE_CREATE.equals(txnPurpose) || PURPOSE_UPDATE.equals(txnPurpose)) {
			ctaNode = createIpoMandateH5CtaNode(listingVisibleTxn, IPO_MONEY_BLOCKED_CTA_LABEL);
		}
		return ctaNode;
	}

	// This method is used to set RECURRING_AUTOMATIC_PAYMENT_CTA cta map in listing
	// response.
	public static CtaNode getListingRecurringCtaNode(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		String txnPurpose = listingVisibleTxn.getContextMap().get(TRANSACTION_PURPOSE);
		String executionNo = GenericUtilityExtension.getExecutionNo(listingVisibleTxn);
		CtaNode ctaNode = null;

		if (PURPOSE_COLLECT.equals(txnPurpose) && EXECUTION_NO_ONE.equals(executionNo)
				&& ClientStatusEnum.SUCCESS.getStatusKey().equals(listingVisibleTxn.getStatus())) {
			ctaNode = MandateUtility.createRecurringMandateH5CtaNode(listingVisibleTxn,
					RECURRING_AUTOMATIC_PAYMENT_CTA_LABEL);
		}
		return ctaNode;
	}

	// This method is used to set cta map in detailV3 response.
	public static void setDetailCtaMap(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final DetailApiResponseV3 response, final String locale) {
		// PTH-847-> In this method separate flags for Recurring Mandate and Ipo are made
		// for CTA on detail.
		CtaNode ctaNode = getMandateCtaForDetail(listingVisibleTxn, response, locale);

		if (Boolean.TRUE.equals(configurablePropertiesHolder.getProperty(ENABLE_DEAF_TXN_FAQ_CTA, Boolean.class))
				&& GenericUtilityExtension.isDeafTxn(listingVisibleTxn)) {
			String deafTxnFaqDeeplink = configurablePropertiesHolder.getProperty(DEEPLINK_DEAF_TXN_FAQ_CTA,
					String.class);
			String label = LocalizedDataCacheService.getLocalizedValue(DEAF_TXN_CTA_LABEL_FOR_LOCALISATION,
					DEAF_TXN_CTA_LABEL, locale);
			ctaNode = getCtaNode(CtaType.DESCRIPTION, label, deafTxnFaqDeeplink);
		}
		if (Objects.nonNull(ctaNode)) {
			Map<String, CtaNode> ctasMap = Objects.isNull(response.getCtasMap()) ? new HashMap<>()
					: response.getCtasMap();
			ctasMap.put(ctaNode.getCtaType(), ctaNode);
			response.setCtasMap(ctasMap);
		}
	}

	// If flags are enabled then hyperlink will be set in Detail page for description Cta
	// type otherwise ctaNode will be returned as null.
	public static CtaNode getMandateCtaForDetail(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final DetailApiResponseV3 response, final String locale) {
		CtaNode ctaNode = null;
		boolean isDetailCtaEnabledForRecurringMandate = configurablePropertiesHolder
			.getProperty(DETAIL_DESCRIPTION_CTA_FOR_RECURRING_MANDATE_ENABLED, Boolean.class);

		boolean isDetailCtaEnabledForIpo = configurablePropertiesHolder
			.getProperty(DETAIL_DESCRIPTION_CTA_FOR_IPO_MANDATE_ENABLED, Boolean.class);

		if (TransactionTypeEnum.IPO_MANDATE.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
				&& ClientStatusEnum.SUCCESS.getStatusKey().equals(listingVisibleTxn.getStatus())
				&& isDetailCtaEnabledForIpo) {
			ctaNode = getDetailIpoCtaNode(listingVisibleTxn);
		}
		if (MandateUtility.txnValidForRecurring(listingVisibleTxn) && isDetailCtaEnabledForRecurringMandate) {
			ctaNode = MandateUtility.getDetailRecurringCtaNode(listingVisibleTxn);
		}
		return ctaNode;
	}

	public static void setDetailInstrumentsCtaMap(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final DetailApiResponseV3 response) {
		if (GenericUtilityExtension.isStoreCashTxn(listingVisibleTxn)) {
			createAndAddStoreCashCtaNode(response, listingVisibleTxn, LocalizedDataCacheService.getLocalizedValue(
					LOCALIZATION_KEY_VIEW_STORECASH_BALANCE_CTA_LABEL, VIEW_STORECASH_BALANCE_CTA, ENGLISH_LOCALE));
		}
	}

	public static CtaNode getDetailIpoCtaNode(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		String txnPurpose = GenericUtilityExtension.getTransactionPurpose(listingVisibleTxn);
		String ctaLabel;

		if (PURPOSE_CREATE.equals(txnPurpose) || PURPOSE_UPDATE.equals(txnPurpose)) {
			ctaLabel = IPO_MONEY_BLOCKED_CTA_LABEL;
		}
		else if (PURPOSE_REVOKE.equals(txnPurpose)) {
			ctaLabel = IPO_MONEY_UNBLOCKED_CTA_LABEL;
		}
		else {
			return null;
		}

		return createIpoMandateH5CtaNode(listingVisibleTxn, ctaLabel);

	}

	// This method is used to set IPO Mandate Money blocked cta.
	public static CtaNode createIpoMandateH5CtaNode(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final String label) {
		String deepLink = getIpoMandateH5DeepLink(listingVisibleTxn);

		if (StringUtils.isBlank(deepLink)) {
			return null;
		}

		CtaNode ctaNode = new CtaNode();
		ctaNode.setCtaType(CtaType.DESCRIPTION.getCtaType());
		ctaNode.setValueType(CtaValueType.LINK.getCtaValueType());
		ctaNode.setLabel(label);
		ctaNode.setValue(Collections.singletonMap(DEEPLINK, deepLink));
		return ctaNode;
	}

	public static void createAndAddStoreCashCtaNode(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label) {
		/*
		 * check txntype loop thru second instr & set
		 */
		TransactionTypeEnum txnType = TransactionTypeEnum
			.getTransactionTypeEnumByKey(listingVisibleTxn.getMainTxnType());

		switch (txnType) {
			case P2M:
			case P2M_REFUND:
				createViewStoreCashBalanceCtaNode(detailApiResponseV3.getSecondInstrument(), label);
				break;
			default:
				// do nothing
		}
	}

	// This method create deeplink for IPO mandate H5 page.
	public static String getIpoMandateH5DeepLink(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (StringUtils.isBlank(ipoMandateH5BaseUrl)) {
			return null;
		}
		String name = "";
		String bankName = "";
		for (TransformedParticipant participant : listingVisibleTxn.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				name = participant.getName();
			}
			else {
				bankName = participant.getBankData().getBankName();
			}
		}
		String validity = listingVisibleTxn.getContextMap().getOrDefault(VALIDITY_END_DATE, null);

		if (StringUtils.isBlank(name) || StringUtils.isBlank(bankName) || StringUtils.isBlank(validity)) {
			return null;
		}
		String ipoH5Cta = ipoMandateH5BaseUrl + AMPERSAND + NAME + EQUAL_SYMBOL + name + AMPERSAND + BANK_NAME_PARAM
				+ EQUAL_SYMBOL + bankName + AMPERSAND + VALIDITY + EQUAL_SYMBOL
				+ Utility.getDateTime(validity, dd_MMM_YYYY);
		ipoH5Cta = ipoH5Cta.replaceAll(SPACE, SPACE_REPLACEMENT);
		return ipoH5Cta;
	}

	public static boolean isValidInstrumentForCheckBalance(final TransformedTransactionHistoryDetail tthd,
			final TransformedParticipant participant) {
		// this func will check whether the instrument is valid to add check balance or
		// not.
		if (Objects.isNull(participant)) {
			return false;
		}
		else {
			return Boolean.FALSE.equals(UpiLiteUtility.isUpiLiteOutwardTxn(tthd, participant));
		}
	}

	public static boolean validateCta(final Map<String, String> paramMap, final List<String> paramList) {
		if (!CollectionUtils.isEmpty(paramList)) {
			for (String param : paramList) {
				if (Objects.isNull(paramMap.get(param)) || StringUtils.isBlank(paramMap.get(param))) {
					log.info("No Cta due to null value for {} ", param);
					return false;
				}
			}
		}
		return true;
	}

	public static CtaNode getWalletMaintenanceChargeDescCta() {
		CtaNode description = new CtaNode();
		description.setLabel(WebConstants.WalletMaintenanceChargeConstants.CHARGES_DESCRIPTION);
		description.setCtaType(DESCRIPTION.getCtaType());
		description.setValueType(LINK.getCtaValueType());
		Map<String, String> map = new HashMap<>();
		map.put(DEEPLINK, deeplinks.getWmcDescription());
		description.setValue(map);
		return description;
	}

	public static CtaNode getReactivateWalletCta() {
		CtaNode reactivateWallet = new CtaNode();
		reactivateWallet.setLabel(WebConstants.WalletMaintenanceChargeConstants.REACTIVATE_PAYTM_WALLET);
		reactivateWallet.setCtaType(REACTIVATE_WALLET.getCtaType());
		reactivateWallet.setValueType(LINK.getCtaValueType());
		Map<String, String> map = new HashMap<>();
		map.put(DEEPLINK, deeplinks.getWalletReactivate());
		reactivateWallet.setValue(map);
		return reactivateWallet;
	}

	public static void setWalletRefundButton(final TransformedTransactionHistoryDetail tthd,
			final EsResponseTxn responseTxn) {
		if (responseTxn.getCtasMap() == null) {
			responseTxn.setCtasMap(new HashMap<>());
		}
		Map<String, String> paramMap = new HashMap<>();
		TransformedParticipant otherParticipant = getOtherParticipant(tthd);
		if (!validateParticipantForWalletRefund(otherParticipant)) {
			return;
		}
		String sourceName;
		if (otherParticipant.getCardData() != null
				&& StringUtils.isNotBlank(otherParticipant.getCardData().getCardIssuer())) {
			sourceName = String.format(WebConstants.S_CARD, otherParticipant.getCardData().getCardIssuer());
		}
		else {
			sourceName = otherParticipant.getBankData().getBankName();
		}
		paramMap.put(BANK_NAME, sourceName);
		paramMap.put(TRANSACTION_ID, tthd.getTxnId());
		paramMap.put(AMOUNT_PARAM, getCurrencyAmountInHigherDenomination(tthd.getAmount(), tthd.getCurrency()));
		CtaNode ctaNode = new CtaNode();
		ctaNode.setCtaType(CtaType.WALLET_REFUND.getCtaType());
		ctaNode.setValueType(CtaValueType.PARAMS.getCtaValueType());
		ctaNode.setLabel(WebConstants.REFUND_TO_SOURCE);
		ctaNode.setValue(paramMap);
		responseTxn.getCtasMap().put(CtaType.WALLET_REFUND.getCtaType(), ctaNode);
	}

	private static boolean validateParticipantForWalletRefund(final TransformedParticipant otherParticipant) {
		boolean validForBankName;
		boolean validForCardName;
		validForBankName = otherParticipant != null && otherParticipant.getBankData() != null
				&& StringUtils.isNotBlank(otherParticipant.getBankData().getBankName());
		validForCardName = otherParticipant != null && otherParticipant.getCardData() != null
				&& StringUtils.isNotBlank(otherParticipant.getCardData().getCardIssuer());
		return validForBankName || validForCardName;
	}

	public static void setOnHoldDeeplink(final EsResponseTxn responseTxn) {
		if (responseTxn.getCtasMap() == null) {
			responseTxn.setCtasMap(new HashMap<>());
		}
		CtaNode ctaNode = new CtaNode();
		ctaNode.setCtaType(CtaType.ON_HOLD.getCtaType());
		ctaNode.setLabel(WHY_IS_THIS_MONEY_ON_HOLD);
		ctaNode.setValueType(CtaValueType.LINK.getCtaValueType());
		Map<String, String> values = new HashMap<>();
		values.put(DEEPLINK, onHoldH5BaseUrl);
		ctaNode.setValue(values);
		responseTxn.getCtasMap().put(CtaType.ON_HOLD.getCtaType(), ctaNode);
	}

	public static void setUpiCcEmiCta(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail txn) {
		if (Boolean.FALSE
			.equals(Utility.isUpiViaCcTxn(txn) && ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus()))) {
			return;
		}

		Boolean isUpiCcEmiCtaEnable = configurablePropertiesHolder.getProperty(WebApiV1Constants.UPI_CC_EMI_CTA_ENABLE,
				Boolean.class);
		if (Boolean.FALSE.equals(isUpiCcEmiCtaEnable)
				&& Boolean.FALSE.equals(rolloutStrategyHelper.isUserWhiteListed("UpiCcEmiCta", txn.getEntityId()))) {
			return;
		}

		TransformedParticipant participant = getUpiDebitUserParticipant(txn);
		if (Objects.isNull(participant) || Objects.isNull(participant.getUpiData())
				|| Objects.isNull(participant.getUpiData().getUpiCcInfo())
				|| Objects.isNull(participant.getUpiData().getUpiCcInfo().getEmiInfo())) {
			return;
		}

		EmiInfo emiInfo = participant.getUpiData().getUpiCcInfo().getEmiInfo();
		CcEmiStatusEnum emiStatus = CcEmiStatusEnum.getEmiStatusEnumByValue(emiInfo.getStatus());

		if (CcEmiStatusEnum.NOT_ACTIVATED.equals(emiStatus)) {
			checkAndSetConvertEmiCta(detailApiResponseV3, txn);
		}
		else if (CcEmiStatusEnum.ACTIVATED.equals(emiStatus) && ObjectUtils.isNotEmpty(emiInfo.getEmiList())) {
			checkAndSetViewEmiCta(detailApiResponseV3, txn, participant);
		}
	}

	private static void checkAndSetViewEmiCta(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail txn, final TransformedParticipant userUpiParticipant) {
		TxnInfo upiTxnInfo = getUpiTxnInfo(detailApiResponseV3);
		if (Objects.isNull(upiTxnInfo)) {
			return;
		}

		EmiInfo emiInfo = userUpiParticipant.getUpiData().getUpiCcInfo().getEmiInfo();

		StringBuilder deeplinkBuilder = new StringBuilder(viewEmiDeeplink);
		deeplinkBuilder.append("&am=")
			.append(Currency.getCurrencyAmountInHigherDenomination(userUpiParticipant.getAmount(),
					userUpiParticipant.getCurrency()));
		deeplinkBuilder.append("&emi=").append(emiInfo.getEmiList().get(0).getAmount());
		deeplinkBuilder.append("&pt=").append(emiInfo.getEmiList().get(0).getTenure());
		deeplinkBuilder.append("&dt=").append(emiInfo.getEmiList().get(0).getStartDate());
		deeplinkBuilder.append("&pta=").append(emiInfo.getEmiList().get(0).getTotalAmt());

		deeplinkBuilder.append("&txnId=").append(upiTxnInfo.getTxnId());
		deeplinkBuilder.append("&txnDate=").append(upiTxnInfo.getTxnDate());

		TransformedParticipant secParticipant = Utility.getSecPartyParticipant(txn);
		if (Objects.nonNull(secParticipant) && Objects.nonNull(secParticipant.getUpiData())
				&& StringUtils.isNotBlank(secParticipant.getUpiData().getVpa())) {
			deeplinkBuilder.append("&payeeVpa=").append(secParticipant.getUpiData().getVpa());
		}

		InstrumentDtoV3 instrumentDtoV3 = getUpiCcUserInstrument(detailApiResponseV3);
		if (Objects.nonNull(instrumentDtoV3)) {
			deeplinkBuilder.append("&pc=").append(instrumentDtoV3.getName());
		}

		InstrumentDtoV3 secUserInst = detailApiResponseV3.getFirstInstrument().get(0);
		deeplinkBuilder.append("&pn=").append(secUserInst.getName());

		CtaNode ctaNode = getCtaNode(CtaType.MANAGE_AUTOMATIC_PAYMENT, "View Smart EMI", deeplinkBuilder.toString());

		if (Objects.nonNull(ctaNode)) {
			if (Objects.isNull(detailApiResponseV3.getCtasMap())) {
				detailApiResponseV3.setCtasMap(new HashMap<>());
			}
			detailApiResponseV3.getCtasMap().put(CtaType.MANAGE_AUTOMATIC_PAYMENT.getCtaType(), ctaNode);
		}
	}

	private static InstrumentDtoV3 getUpiCcUserInstrument(final DetailApiResponseV3 detailApiResponseV3) {
		if (Objects.isNull(detailApiResponseV3) || ObjectUtils.isEmpty(detailApiResponseV3.getSecondInstrument())) {
			return null;
		}

		InstrumentDtoV3 upiCcInstrument = null;
		for (InstrumentDtoV3 instrumentDtoV3 : detailApiResponseV3.getSecondInstrument()) {
			if (Objects.nonNull(instrumentDtoV3) && Objects.nonNull(instrumentDtoV3.getParticipantInfo())
					&& PaymentSystemEnum.UPI.getPaymentSystemKey()
						.equals(instrumentDtoV3.getParticipantInfo().getPaymentSystem())) {
				upiCcInstrument = instrumentDtoV3;
				break;
			}
		}
		return upiCcInstrument;
	}

	private static void checkAndSetConvertEmiCta(final DetailApiResponseV3 detailApiResponseV3,
			final TransformedTransactionHistoryDetail txn) {
		TxnInfo upiTxnInfo = getUpiTxnInfo(detailApiResponseV3);
		if (Objects.isNull(upiTxnInfo)) {
			return;
		}

		/**
		 * This compareTo will return :- 0 if both values are equal. > 0 if first duration
		 * is longer. < 0 if first duration is shorter.
		 */
		Integer creditCardCycleDays = configurablePropertiesHolder
			.getProperty(WebApiV1Constants.UPI_CC_EMI_CREDIT_CARD_CYCLE_DAYS, Integer.class);
		int result = Duration.ofDays(creditCardCycleDays)
			.compareTo(DateTimeUtility.getDurationBetweenTwoEpochMillis(upiTxnInfo.getTxnDate(),
					System.currentTimeMillis()));

		Long minAmtLimitToShowConvertEmiCta = configurablePropertiesHolder
			.getProperty(WebApiV1Constants.MIN_AMT_LIMIT_IN_PAISA_TO_SHOW_CONVERT_EMI_CTA, Long.class);

		if (result < 0 || Objects.isNull(txn.getAmount()) || txn.getAmount() < minAmtLimitToShowConvertEmiCta) {
			return;
		}

		TransformedParticipant secParticipant = Utility.getSecPartyParticipant(txn);
		StringBuilder deeplinkBuilder = new StringBuilder(convertEmiDeeplink);
		deeplinkBuilder.append("&txnId=").append(upiTxnInfo.getTxnId());
		deeplinkBuilder.append("&txnDate=").append(upiTxnInfo.getTxnDate());

		if (Objects.nonNull(secParticipant) && Objects.nonNull(secParticipant.getUpiData())
				&& StringUtils.isNotBlank(secParticipant.getUpiData().getVpa())) {
			deeplinkBuilder.append("&payeeVpa=").append(secParticipant.getUpiData().getVpa());
		}

		CtaNode ctaNode = getCtaNode(CtaType.MANAGE_AUTOMATIC_PAYMENT, "Convert to EMI", deeplinkBuilder.toString());

		if (Objects.nonNull(ctaNode)) {
			if (Objects.isNull(detailApiResponseV3.getCtasMap())) {
				detailApiResponseV3.setCtasMap(new HashMap<>());
			}
			detailApiResponseV3.getCtasMap().put(CtaType.MANAGE_AUTOMATIC_PAYMENT.getCtaType(), ctaNode);
		}
	}

	private static CtaNode getCtaNode(final CtaType ctaType, final String label, final String deeplink) {
		if (Objects.isNull(ctaType)) {
			return null;
		}

		CtaNode ctaNode = new CtaNode();
		ctaNode.setCtaType(ctaType.getCtaType());
		ctaNode.setLabel(label);
		ctaNode.setValueType(CtaValueType.LINK.getCtaValueType());
		ctaNode.setValue(Collections.singletonMap(DEEPLINK, deeplink));

		return ctaNode;
	}

	private static TxnInfo getUpiTxnInfo(final DetailApiResponseV3 detailApiResponseV3) {
		if (Objects.isNull(detailApiResponseV3) || ObjectUtils.isEmpty(detailApiResponseV3.getGroupedTxns())) {
			return null;
		}

		TxnInfo upiTxnInfo = null;
		for (TxnInfo txnInfo : detailApiResponseV3.getGroupedTxns()) {
			if (TransactionSource.UPI.getTransactionSourceKey().equals(txnInfo.getStreamSource())) {
				upiTxnInfo = txnInfo;
				break;
			}
		}
		return upiTxnInfo;
	}

	private static TransformedParticipant getUpiDebitUserParticipant(final TransformedTransactionHistoryDetail txn) {
		if (Objects.isNull(txn) || ObjectUtils.isEmpty(txn.getParticipants())) {
			return null;
		}

		TransformedParticipant resParticipant = null;
		for (TransformedParticipant participant : txn.getParticipants()) {
			if (Objects.nonNull(participant) && txn.getEntityId().equals(participant.getEntityId())
					&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())
					&& PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
				resParticipant = participant;
			}
		}
		return resParticipant;
	}

	public static void populateListOfEnabledCtaTypeForDetailResponse() {
		listOfCtaTypeEnabledForDetailResponse = configurablePropertiesHolder
			.getProperty(LIST_OF_CTA_TYPE_ENABLED_FOR_DETAIL_PAGE, ArrayList.class);
	}

	public static void removeCtaFromDetailResponse(DetailApiResponseV3 detailApiResponseV3) {
		if (Objects.isNull(detailApiResponseV3)) {
			return;
		}
		populateListOfEnabledCtaTypeForDetailResponse();
		if (ObjectUtils.isNotEmpty(detailApiResponseV3.getCtasMap())) {
			detailApiResponseV3.getCtasMap()
				.keySet()
				.removeIf(cta -> !listOfCtaTypeEnabledForDetailResponse.contains(cta));
		}
	}

	public static void removeCtaFromInstrument(final InstrumentDtoV3 instrumentDto) {
		if (Objects.isNull(instrumentDto)) {
			return;
		}
		populateListOfEnabledCtaTypeForDetailResponse();
		if (ObjectUtils.isNotEmpty(instrumentDto.getCtasMap())) {
			instrumentDto.getCtasMap().keySet().removeIf(cta -> !listOfCtaTypeEnabledForDetailResponse.contains(cta));
		}
	}

}