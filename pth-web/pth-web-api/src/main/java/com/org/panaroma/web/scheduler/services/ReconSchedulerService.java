package com.org.panaroma.web.scheduler.services;

import static com.org.panaroma.commons.constants.WebConstants.CONFIG_READER_CRON;
import static com.org.panaroma.commons.constants.WebConstants.DOT;
import static com.org.panaroma.commons.constants.WebConstants.RECON_CONFIG_PREFIX;

import com.google.gson.Gson;
import com.org.panaroma.commons.dto.ReconCronConfig;
import com.org.panaroma.commons.entity.ReconStatusData;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.extern.log4j.Log4j2;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@EnableScheduling
public class ReconSchedulerService {

	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	private Gson gson;

	private SchedulerHelper schedulerHelper;

	@Autowired
	public ReconSchedulerService(final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final SchedulerHelper schedulerHelper) {
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		gson = new Gson();
		this.schedulerHelper = schedulerHelper;
	}

	/***
	 *
	 */
	@Scheduled(fixedDelayString = "${recon.config.cron1.delay.in.milli}")
	@SchedulerLock(name = CONFIG_READER_CRON, lockAtMostForString = "${recon.config.cron1.lock.at.most.in.milli}")
	public void scheduledTask() {
		log.info("Recon Scheduler executed");
		// can fetch configs from db
		long currentTime = Instant.now().toEpochMilli();
		List<ReconCronConfig> reconCronConfigs = this.getCronConfig();
		if (Objects.nonNull(reconCronConfigs)) {
			for (ReconCronConfig cronConfig : reconCronConfigs) {
				try {
					if (Boolean.TRUE.equals(cronConfig.getIsActive())) {
						ReconStatusData reconStatusData = this.schedulerHelper.getLatestReconStatusData(cronConfig);
						if (this.schedulerHelper.eligibleForNewReconTask(reconStatusData, cronConfig, currentTime)) {
							log.info("Eligible for new recon task. Pushing data into kafka. cronConfig {}", cronConfig);
							this.schedulerHelper.saveAndPushIntoKafka(currentTime, cronConfig, reconStatusData);
						}
					}
				}
				catch (Exception e) {
					log.error("Exception while pushing data into kafka. Exception : {}",
							CommonsUtility.exceptionFormatter(e));
				}
			}
		}
	}

	/**
	 * Fetches the cron config from the properties.
	 * uth.recon.config.upiOriginalStatusPending3To4Days :
	 * {\"tag\":\"UPI_ORIGINAL_STATUS_PENDING\",\"isActive\":true,\"fromDateDiff\":345600000,\"toDateDiff\":259200000,\"executionTimeRange\":{\"fromTime\":\"00:00\",\"toTime\":\"01:00\"}}
	 * uth.recon.config.upiOnusPending1To2Days :
	 * {\"tag\":\"UPI_ONUS_PENDING_AFTER_N_DAYS\",\"isActive\":true,\"fromDateDiff\":172800000,\"toDateDiff\":86400000,\"executionTimeRange\":{\"fromTime\":\"05:00\",\"toTime\":\"06:00\",\"isSourceCallRequired\":
	 * \"false\"}} uth.recon.config.manualTestRecon :
	 * {"tag":"ON_DEMAND_TXN_ID_RECON","isActive":true,"fromDateDiff":172800000,"toDateDiff":86400000,"executionTimeRange":{"fromTime":"05:00","toTime":"06:00"},"isSourceCallRequired":
	 * "true","onDemandReconParams":{"txnIds":["123456"]}}
	 * uth.recon.config.searchFieldEmptyRecon :
	 * {\"tag\":\"SEARCH_FIELD_EMPTY_RECON\",\"isActive\":true,\"fromDateDiff\":172800000,\"toDateDiff\":86400000,\"executionTimeRange\":{\"fromTime\":\"05:00\",\"toTime\":\"06:00\",\"isSourceCallRequired\":
	 * \"false\”}},
	 */
	private List<ReconCronConfig> getCronConfig() {
		try {
			Map<String, ReconCronConfig> configs = configurablePropertiesHolder
				.getPropertiesWithPrefix(RECON_CONFIG_PREFIX, ReconCronConfig.class);

			if (Objects.isNull(configs)) {
				log.error("No recon config found in properties");
				return null;
			}
			configs.forEach((key, value) -> value.setName(key.substring(key.lastIndexOf(DOT) + 1)));
			List<ReconCronConfig> reconCronConfigs = new ArrayList<>(configs.values());
			log.info("List fetched from configs : {}", reconCronConfigs);
			return reconCronConfigs;
		}
		catch (Exception e) {
			log.error("Exception while fetching recon config from properties. Exception : {}",
					CommonsUtility.exceptionFormatter(e));
			return null;
		}
	}

}