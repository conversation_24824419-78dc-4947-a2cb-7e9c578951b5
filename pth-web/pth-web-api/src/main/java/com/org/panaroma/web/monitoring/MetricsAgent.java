package com.org.panaroma.web.monitoring;

import static com.org.panaroma.commons.constants.WebConstants.IS_MERGED_DOCUMENT;
import static com.org.panaroma.commons.constants.WebConstants.PASSBOOK_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_SYSTEM;
import static com.org.panaroma.commons.constants.WebConstants.SUB_CLIENT;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.FAILURE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_EXECUTION_TIME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_HIT_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_NAME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_RESPONSE_CODE_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_STATUS_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.AVAILABLE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.CACHE_LOCALISED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLLAPSED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COMMA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.CONTAINER_HOST;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COUNT_OF_RECORDS_CONFIGURABLE_PROPERTIES;
import static com.org.panaroma.web.monitoring.MonitoringConstants.DEFAULT_PAGE_NO;
import static com.org.panaroma.web.monitoring.MonitoringConstants.DETAIL_ORDER_IN_LISTING;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ERROR_TYPE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ES_API_HIT_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.FILTER_ES_RESPONSE_TIME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.GROUPING_VIA_DETAILS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.METRICS_API_VERSION;
import static com.org.panaroma.web.monitoring.MonitoringConstants.METRICS_SOURCE_CONTEXT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.NO_OF_ES_CALLS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ONLY_TIMELINE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.PAGE_NO;
import static com.org.panaroma.web.monitoring.MonitoringConstants.PAYMENT_SYSTEM_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RECORD_DATE_DIFF;
import static com.org.panaroma.web.monitoring.MonitoringConstants.REQUIRED_LOCALISED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SINGLE_DOC_NOT_FORMED_WHEN_LISTING_IS_SERVED;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SOURCE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.STATUS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TIMELINE_BAD_RESP_CODE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TIMELINE_BAD_STATUS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TIMELINE_FAILURE_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TIMELINE_RESPONSE_CODE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TIMELINE_STATUS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TIMELINE_STATUS_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TIMELINE_SYNC_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TXN_HISTORY_APP;
import static com.org.panaroma.web.monitoring.MonitoringConstants.TXN_TYPE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UTH_STATUS;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.dto.detailAPI.timelineDto.TimelineErrorType;
import com.org.panaroma.web.utility.TransactionHistoryMonitoringUtility;
import com.timgroup.statsd.NonBlockingStatsDClient;
import com.timgroup.statsd.StatsDClient;
import java.util.Map;
import java.util.Objects;
import jakarta.annotation.PostConstruct;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Log4j2
@Component
@NoArgsConstructor
public class MetricsAgent {

	private StatsDClient prometheusMetricsClient;

	@Value("${prometheus.explorer}")
	private String prometheusExplorer;

	@Value("${prometheus.hostname}")
	private String prometheusHostname;

	@Value("${prometheus.port}")
	private int prometheusPort;

	@Value("${container.hostname}")
	private String containerHost;

	@PostConstruct
	public void init() {
		String containerHostName = CONTAINER_HOST + COLON + this.containerHost;
		log.info("containerHostName to push in Prometheus metric: {}", containerHostName);
		prometheusMetricsClient = new NonBlockingStatsDClient(prometheusExplorer, prometheusHostname, prometheusPort,
				TXN_HISTORY_APP, containerHostName);
	}

	public void incrementApiHitCount(final String tag, final String locale, final String passedPageNo,
			final String sourceContext, final String apiVersion, final String showOnlyTimeline,
			final String clientStatusEnum, final String txnIndicator, final String txnCategory,
			final String filterApplied, final String subClient) {
		String pageNo = passedPageNo;
		String showOnlyTimelineValue = showOnlyTimeline;
		String clientStatusEnumValue = clientStatusEnum;
		// this is done to handle search api version
		StringBuilder tagBuilder = new StringBuilder();
		if (StringUtils.isNotBlank(sourceContext)) {
			tagBuilder.append(METRICS_SOURCE_CONTEXT).append(sourceContext);
		}
		if (StringUtils.isNotBlank(tagBuilder)) {
			tagBuilder.append(COMMA);
		}
		if (StringUtils.isNotBlank(apiVersion)) {
			tagBuilder.append(METRICS_API_VERSION).append(apiVersion);
		}
		if (StringUtils.isNotEmpty(tag)) {
			if (StringUtils.isBlank(pageNo)) {
				pageNo = DEFAULT_PAGE_NO;
			}
			if (StringUtils.isBlank(showOnlyTimeline)) {
				showOnlyTimelineValue = "false";
			}
			if (StringUtils.isNotEmpty(sourceContext) && StringUtils.isNotEmpty(apiVersion)) {
				if (StringUtils.isNotEmpty(clientStatusEnum)) {
					prometheusMetricsClient.increment(API_HIT_COUNT, tag, "locale:" + locale, PAGE_NO + ":" + pageNo,
							tagBuilder.toString(), "showOnlyTimeline:" + showOnlyTimelineValue,
							"clientStatusEnum:" + clientStatusEnumValue, SUB_CLIENT + ":" + subClient);
				}
				else {
					prometheusMetricsClient.increment(API_HIT_COUNT, tag, "locale:" + locale, PAGE_NO + ":" + pageNo,
							tagBuilder.toString(), "showOnlyTimeline:" + showOnlyTimelineValue,
							SUB_CLIENT + ":" + subClient);
				}
			}
			else {
				prometheusMetricsClient.increment(API_HIT_COUNT, tag, "locale:" + locale, PAGE_NO + ":" + pageNo,
						"txnIndicator:" + txnIndicator, "clientStatusEnum:" + clientStatusEnum,
						"txnCategory:" + txnCategory, "filterApplied:" + filterApplied, SUB_CLIENT + ":" + subClient,
						tagBuilder.toString());
			}
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'incrementApiHitCount'");
		}
	}

	public void incrementApiHitCountRangeWise(final String rangeTag, final String apiIdentifier,
			final String filtersAndSearchTags, String esHostTypeTag) {
		if (StringUtils.isNotEmpty(rangeTag)) {
			prometheusMetricsClient.increment(ES_API_HIT_COUNT, API_NAME + COLON + apiIdentifier, rangeTag,
					filtersAndSearchTags, esHostTypeTag);
		}
		else {
			log.error(
					"Could not get tag to push Prometheus metric in 'incrementApiHitCountRangeWise' as rangeTag is empty.");
		}
	}

	public void recordApiExecutionTimeRangeWise(final String tag, final long timeTaken, final String rangeIdentifierTag,
			final String filtersAndSearchTags, String esHostTypeTag) {
		if (StringUtils.isNotEmpty(tag)) {
			prometheusMetricsClient.recordExecutionTime(API_EXECUTION_TIME, timeTaken, tag, rangeIdentifierTag,
					filtersAndSearchTags, esHostTypeTag);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'recordApiExecutionTimeRangeWise'");
		}
	}

	public void recordApiExecutionTimeRangeWise(final String tag, final long timeTaken, final String rangeIdentifierTag,
			final String filtersAndSearchTags) {
		if (StringUtils.isNotEmpty(tag)) {
			prometheusMetricsClient.recordExecutionTime(API_EXECUTION_TIME, timeTaken, tag, rangeIdentifierTag,
					filtersAndSearchTags);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'recordApiExecutionTimeRangeWise'");
		}
	}

	public void recordApiExecutionTime(final String tag, final long timeTaken) {
		if (StringUtils.isNotEmpty(tag)) {
			prometheusMetricsClient.recordExecutionTime(API_EXECUTION_TIME, timeTaken, tag);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'recordApiExecutionTime'");
		}
	}

	public void recordApiExecutionTime(final long timeTaken, final String... tag) {
		if (tag != null) {
			prometheusMetricsClient.recordExecutionTime(API_EXECUTION_TIME, timeTaken, tag);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'recordApiExecutionTime'");
		}
	}

	public void recordTimeGap(final String aspect, final long timeGap, final String... tag) {
		if (tag != null) {
			prometheusMetricsClient.recordHistogramValue(aspect, timeGap, tag);
		}
	}

	public void apiResponseStatusCount(final String tag) {
		if (StringUtils.isNotEmpty(tag)) {
			prometheusMetricsClient.increment(API_STATUS_COUNT, tag);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'apiResponseStatusCount'");
		}
	}

	public void apiResponseCodeCount(final String tag, final String apiTag, final String exceptionHandler,
			final String httpCodeTag) {
		if (StringUtils.isNotEmpty(tag)) {
			prometheusMetricsClient.increment(API_RESPONSE_CODE_COUNT, tag, apiTag, exceptionHandler, httpCodeTag);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'apiResponseCodeCount'");
		}
	}

	public void apiResponseCodeCountWithEsHost(final String tag, final String apiTag, final String esHostTag,
			final String exceptionHandler) {
		if (StringUtils.isNotEmpty(tag)) {
			prometheusMetricsClient.increment(API_RESPONSE_CODE_COUNT, tag, apiTag, esHostTag, exceptionHandler);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'apiResponseCodeCount'");
		}
	}

	public void recordCachedLocalized(final int value, final String langId, final String apiName) {
		prometheusMetricsClient.gauge(CACHE_LOCALISED, value, "langId:" + langId, "apiName:" + apiName);
	}

	public void recordRequiredLocalized(final int value, final String langId, final String apiName) {
		prometheusMetricsClient.gauge(REQUIRED_LOCALISED, value, "langId:" + langId, "apiName:" + apiName);
	}

	public void recordDateDiffWithCurrentDate(final Long txnDate, final String apiName, final String tags) {
		long days = Utility.getDiffWithCurrentDate(txnDate);
		String finalTags = "apiName:" + apiName;
		if (StringUtils.isNotBlank(tags)) {
			finalTags += COMMA + tags;
		}
		if (days >= 0) {
			prometheusMetricsClient.recordHistogramValue(RECORD_DATE_DIFF, days, finalTags);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'apiLastRecordDateDiff'");
		}
	}

	public void recordCountOfRecords(final Number res) {
		prometheusMetricsClient.count(COUNT_OF_RECORDS_CONFIGURABLE_PROPERTIES, res.longValue());
	}

	public void recordOrderOfDetail(final Long res) {
		prometheusMetricsClient.recordHistogramValue(DETAIL_ORDER_IN_LISTING, res);
	}

	public void incrementGroupingViaDetails(final Integer streamSource, final Integer txnType, final String tag) {
		if (!Objects.isNull(streamSource) && !Objects.isNull(txnType) && StringUtils.isNotEmpty(tag)) {
			try {
				prometheusMetricsClient.increment(GROUPING_VIA_DETAILS, "streamSource:" + streamSource.toString(),
						"txnType:" + txnType.toString(), tag);
			}
			catch (Exception e) {
				log.error("Some error occured while pushing metric : {}", CommonsUtility.exceptionFormatter(e));
			}
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'incrementGroupingViaDetails'");
		}
	}

	// Timeline API Monitoring method

	public void incrementCount(final String identifier, final String... tags) {
		try {
			prometheusMetricsClient.increment(identifier, tags);
		}
		catch (Exception ex) {
			log.error("Exception while pushing metrics identifier: {}, tags: {}, exception: {}", identifier, tags,
					CommonsUtility.exceptionFormatter(ex));
		}
	}

	public void recordExecutionTime(final String identifier, final long timeTaken, final String... tags) {
		try {
			prometheusMetricsClient.recordExecutionTime(identifier, timeTaken, tags);
		}
		catch (Exception ex) {
			log.error("Exception while pushing metrics identifier: {}, tags: {}, exception: {}", identifier, tags,
					CommonsUtility.exceptionFormatter(ex));
		}
	}

	public void timelineApiResponseStatusCount(final String status, final TransactionSource source,
			final boolean onlyTimeline) {
		if (StringUtils.isNotEmpty(status) && Objects.nonNull(source)) {
			String statusTag = STATUS + COLON + status;
			String sourceTag = SOURCE + COLON + source.toString();
			String onlyTimelineTag = ONLY_TIMELINE + COLON + onlyTimeline;
			prometheusMetricsClient.increment(TIMELINE_STATUS_COUNT, statusTag, sourceTag, onlyTimelineTag);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'timelineApiStatusCount'");
		}
	}

	public void timelineApiFailureCount(final TransactionSource source, final TimelineErrorType errorType,
			final boolean onlyTimeline, final String passedRespCode) {
		String respCode = passedRespCode;
		if (Objects.nonNull(source) && Objects.nonNull(errorType)) {
			if (StringUtils.isEmpty(respCode)) {
				respCode = TIMELINE_BAD_RESP_CODE;
			}
			String sourceTag = SOURCE + COLON + source.toString();
			String errorTypeTag = ERROR_TYPE + COLON + errorType.toString();
			String onlyTimelineTag = ONLY_TIMELINE + COLON + onlyTimeline;
			String respCodeTag = TIMELINE_RESPONSE_CODE + COLON + respCode;

			prometheusMetricsClient.increment(TIMELINE_FAILURE_COUNT, sourceTag, errorTypeTag, onlyTimelineTag,
					respCodeTag);
			this.timelineApiResponseStatusCount(FAILURE, source, onlyTimeline);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'timelineFailureCount'");
		}
	}

	public void timelineApiFailureCount(final TransactionSource source, final TimelineErrorType errorType,
			final boolean onlyTimeline) {
		if (Objects.nonNull(source) && Objects.nonNull(errorType)) {
			String sourceTag = SOURCE + COLON + source.toString();
			String errorTypeTag = ERROR_TYPE + COLON + errorType.toString();
			String onlyTimelineTag = ONLY_TIMELINE + COLON + onlyTimeline;

			prometheusMetricsClient.increment(TIMELINE_FAILURE_COUNT, sourceTag, errorTypeTag, onlyTimelineTag);
			this.timelineApiResponseStatusCount(FAILURE, source, onlyTimeline);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'timelineFailureCount'");
		}
	}

	public void timelineStatusMismatchCount(final TransactionSource source, final TimelineErrorType errorType,
			final boolean onlyTimeline, final ClientStatusEnum uthStatus, final ClientStatusEnum passedTimelineStatus) {
		String timelineStatus = Objects.isNull(passedTimelineStatus) ? TIMELINE_BAD_STATUS
				: passedTimelineStatus.toString();
		if (Objects.nonNull(source) && Objects.nonNull(errorType) && Objects.nonNull(uthStatus)) {
			String sourceTag = SOURCE + COLON + source.toString();
			String errorTypeTag = ERROR_TYPE + COLON + errorType.toString();
			String onlyTimelineTag = ONLY_TIMELINE + COLON + onlyTimeline;
			String uthStatusTag = UTH_STATUS + COLON + uthStatus.toString();
			String timelineStatusTag = TIMELINE_STATUS + COLON + timelineStatus;

			prometheusMetricsClient.increment(TIMELINE_FAILURE_COUNT, sourceTag, errorTypeTag, onlyTimelineTag,
					uthStatusTag, timelineStatusTag);
			this.timelineApiResponseStatusCount(FAILURE, source, onlyTimeline);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'timelineStatusMisMatchCount'");
		}
	}

	public void syncTimelineCount(final TransactionSource source, final Boolean available,
			final Boolean passedCollapsed) {
		Boolean collapsed = passedCollapsed;
		if (Objects.nonNull(source) && Objects.nonNull(available)) {
			if (passedCollapsed == null) {
				collapsed = true;
			}
			String sourceTag = SOURCE + COLON + source.toString();
			String availableTag = AVAILABLE + COLON + available;
			String collapsedTag = COLLAPSED + COLON + collapsed;
			prometheusMetricsClient.increment(TIMELINE_SYNC_COUNT, sourceTag, availableTag, collapsedTag);
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'syncTimelineCount'");
		}
	}

	public void recordNumberOfEsCalls(final int noOfEsCalls, final String apiName, final int pageNo,
			final String tags) {
		prometheusMetricsClient.recordHistogramValue(NO_OF_ES_CALLS, noOfEsCalls, "apiName:" + apiName,
				"pageNo:" + pageNo, tags);
	}

	public void recordPaymentSystemCount(final String paymentSystem, final String isPassbookRequest) {
		if (StringUtils.isNotEmpty(paymentSystem)) {
			try {
				for (String paymentSystem1 : paymentSystem.split(",")) {
					String paymentTag = PAYMENT_SYSTEM + COLON + paymentSystem1;
					String isPassbookFilterTag = PASSBOOK_FILTER + COLON + isPassbookRequest;
					prometheusMetricsClient.increment(PAYMENT_SYSTEM_COUNT, paymentTag, isPassbookFilterTag);
				}
			}
			catch (Exception e) {
				log.error("Exception occurred while populating payment system count metric . exception : {}",
						CommonsUtility.exceptionFormatter(e));
			}
		}
		else {
			log.error("Could not get tag to push Prometheus metric in 'recordPaymentSystemCount'");
		}

	}

	public void countMetricsWithCountTypeString(final String countType, final Long value, final String... tags) {
		if (!StringUtils.isEmpty(countType)) {
			try {
				prometheusMetricsClient.count(countType, value, tags);
			}
			catch (Exception e) {
				log.error("Error in writing count to metrics, CountType:{} exception:- {}", countType,
						CommonsUtility.exceptionFormatter(e));
			}
		}
	}

	public void pushEsResponseTimeForFilter(final Map<String, String> paramMap, final long timeTaken) {
		String[] tags = TransactionHistoryMonitoringUtility.getMetricsTagsForFilters(paramMap);

		if (ObjectUtils.isNotEmpty(tags)) {
			this.recordExecutionTime(FILTER_ES_RESPONSE_TIME, timeTaken, tags);
		}
	}

	// Similar function could also be made for other txnTypes to keep an eye on uth
	// pipeline performance
	public void pushSingleDocMetricsWhileServingAddMoneyListing(final TransformedTransactionHistoryDetail esDto) {
		if (Objects.isNull(esDto)) {
			return;
		}
		try {
			Map<String, String> contextMap = esDto.getContextMap();
			TransactionTypeEnum transactionTypeEnum = TransactionTypeEnum
				.getTransactionTypeEnumByKey(esDto.getMainTxnType());
			TransactionSource streamSource = TransactionSource.getTransactionSourceEnumByKey(esDto.getStreamSource());
			TransformedParticipant debitParticipant = Utility.getDebitParticipant(esDto);
			if (Objects.nonNull(contextMap) && !contextMap.containsKey(IS_MERGED_DOCUMENT)
					&& (TransactionSource.WALLET.getTransactionSourceKey().equals(esDto.getStreamSource())
							|| (TransactionSource.isPgTypeSource(esDto.getStreamSource())
									&& Objects.nonNull(debitParticipant) && PaymentSystemEnum.UPI.getPaymentSystemKey()
										.equals(debitParticipant.getPaymentSystem())))) {
				log.warn("Single doc not formed when Add Money Listing is served for txnId : {}, streamSource : {}",
						esDto.getTxnId(), esDto.getStreamSource());
				// txnType is also pushed as in future we might push this metric for other
				// txnTypes too
				prometheusMetricsClient.increment(SINGLE_DOC_NOT_FORMED_WHEN_LISTING_IS_SERVED,
						TXN_TYPE + COLON + transactionTypeEnum, SOURCE + COLON + streamSource);
			}
		}
		catch (Exception ex) {
			log.error("Exception {} while pushing single doc metrics while Add Money listing is served.",
					CommonsUtility.exceptionFormatter(ex));
		}
	}

	public void recordTimeDiffBetweenDates(final String identifierAspect, final long timeDiff, final String... tags) {
		try {
			prometheusMetricsClient.recordHistogramValue(identifierAspect, timeDiff, tags);
		}
		catch (Exception ex) {
			log.error("Exception while pushing timeDiff metrics identifier: {}, tags: {}, exception: {}",
					identifierAspect, tags, CommonsUtility.exceptionFormatter(ex));
		}
	}

}
