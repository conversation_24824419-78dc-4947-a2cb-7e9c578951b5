package com.org.panaroma.web.cache;

import static com.org.panaroma.commons.constants.WebConstants.IS_NTU_CACHE_ENABLED;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_REQUEST_APPLICABLE_TO_CHECK_NO_TXN_FOUND;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;

import com.org.panaroma.commons.enums.CacheInfo;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.util.Map;
import java.util.Objects;

import lombok.Data;

@Data
public class CacheContext {

	/*
	 * need to check NTU cache to verify that user is transacting user else we will throw
	 * 4010 directly.
	 */

	private boolean isRequestValidToBeSavedInNtuCache;

	private boolean isRequestValidForGettingDataFromNtuCache;

	private boolean isNtuCacheEnabled;

	private boolean isRequestValidToThrow4010Error;

	private CacheInfo cacheUsed;

	public CacheContext(final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final SearchContext searchContext, final Map<String, String> paramMap, final Utility utility) {
		this.isNtuCacheEnabled = configurablePropertiesHolder.getProperty(IS_NTU_CACHE_ENABLED, Boolean.class);
		this.isRequestValidForGettingDataFromNtuCache = isRequestValidForGettingDataFromNtuCache(searchContext,
				paramMap, utility);
	}

	/*
	 * The below method is used to get data from Ntu cache even if some filter applied
	 * request arrives. This is done only to throw 4010 error straightaway if user is
	 * marked non transacting
	 */
	private boolean isRequestValidForGettingDataFromNtuCache(final SearchContext searchContext,
			final Map<String, String> paramMap, final Utility utility) {
		// we don't serve listing requests with page no. > 1 from cache
		boolean isRequestValidForGettingDataFromNtuCache = isNtuCacheEnabled && searchContext.getPageNo() == 1
				&& !searchContext.isForUpdates()
				&& utility.checkIfWhiteListed(searchContext.getEntityId(), CacheInfo.UTH_NTU_CACHE.getCacheName());
		return isRequestValidForGettingDataFromNtuCache;
	}

	public boolean isRequestValidToBeSavedInNtuCache(final SearchContext searchContext, final Boolean cacheData) {

		if (!isRequestValidForGettingDataFromNtuCache) {
			return false;
		}

		// we won't put Ntu data to Ntu cache if some filter is applied
		if (searchContext.isFilterApplied()) {
			return false;
		}

		if (Objects.nonNull(cacheData)) {
			return false;
		}

		return true;
	}

}
