package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.APP_CACHE_INVALIDATE_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.CUSTOMER_CREATION_DATE;
import static com.org.panaroma.commons.constants.WebConstants.ENTITY_ID;
import static com.org.panaroma.commons.constants.WebConstants.FROM_DATE_LISTING_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.ONUS_VERTICAL_FILTER_FROM_DATE;
import static com.org.panaroma.commons.constants.WebConstants.PAGE_NO;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PAYMENT_SYSTEM;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_API_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.UPI_CC_FILTER_FROM_DATE;
import static com.org.panaroma.commons.constants.WebConstants.ONUS_VERTICAL_FILTER_FROM_DATE;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.CUSTOM_RETRY;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.DETAIL_COULD_NOT_BE_LOADED;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.ES_EXCEPTION;
import static com.org.panaroma.web.utility.RoutingUtility.validForAwsToDcRoutingForDateFilter;

import com.org.panaroma.commons.enums.ApiVersion;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.rollout.strategy.RolloutStrategyHelper;
import com.org.panaroma.web.dto.ResponseDto;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.text.ParseException;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class RoutingUtilityNonMono {

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	private static RolloutStrategyHelper rolloutStrategyHelper;

	RoutingUtilityNonMono(final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final RolloutStrategyHelper rolloutStrategyHelper) {
		RoutingUtilityNonMono.configurablePropertiesHolder = configurablePropertiesHolder;
		RoutingUtilityNonMono.rolloutStrategyHelper = rolloutStrategyHelper;
	}

	public static ResponseDto handle4010forNonMono(final Map<String, String> paramMap, final Throwable exception)
			throws Throwable {
		if (Boolean.FALSE.equals(RoutingUtility.isNonTransactingException(exception))) {
			throw exception;
		}
		if (RoutingUtility.isDcFirstPage(paramMap)) {
			return handle4010ForDcNonMono(paramMap, exception);
		}
		else {
			return handle4010ForAwsNonMono(paramMap, exception);
		}
	}

	public static ResponseDto handle4010ForAwsNonMono(final Map<String, String> paramMap, final Throwable exception)
			throws Throwable {

		String customerCreationDate = paramMap.getOrDefault(CUSTOMER_CREATION_DATE, null);
		String paymentSystem = paramMap.getOrDefault(PAYMENT_SYSTEM, "");

		if (paymentSystem.contains("upi_lite") && Boolean.FALSE
			.equals(configurablePropertiesHolder.getProperty("nonTransactingRoutingToDc_upiLite", Boolean.class))) {
			throw exception;
		}

		if (!paymentSystem.contains("upi_lite") && Boolean.FALSE
			.equals(configurablePropertiesHolder.getProperty("nonTransactingRoutingToDc_allTxns", Boolean.class))) {
			throw exception;
		}

		if (RoutingUtility.isRequestValidForHandling4010OnAws(paramMap)) {
			String currentFromDateString = configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER,
					String.class);
			Long currentFromDate = null;
			if (paymentSystem.contains("upi_lite") || Boolean.TRUE
				.equals(configurablePropertiesHolder.getProperty("nonTransactingRoutingToDc_allTxns", Boolean.class))) {
				try {
					currentFromDate = DateTimeUtility.listingDateFormat.parse(currentFromDateString).getTime();
				}
				catch (ParseException e) {
					log.error("Error in parsing from Date :{}",
							configurablePropertiesHolder.getProperty(FROM_DATE_LISTING_FILTER, String.class));
				}

				/*
				 * In case we get 4010 error from pth-service & upi-cc-filter-from-date is
				 * greater than pth-service from date, then we don't need to go to
				 * pth-v2-service for further data. So throwing CUSTOM_RETRY error is
				 * avoided by returning from here itself
				 */
				if (GenericUtilityExtension.isUpiViaCcFilterRequest(paramMap) && DateTimeUtility.isFirstDateLatest(
						configurablePropertiesHolder.getProperty(UPI_CC_FILTER_FROM_DATE, String.class),
						currentFromDateString)) {
					throw exception;
				}
				/*
				 * In case we get 4010 error from pth-service &
				 * onus-vertical-filter-from-date is greater than pth-service from date,
				 * then we don't need to go to pth-v2-service for further data. So
				 * throwing CUSTOM_RETRY error is avoided by returning from here itself
				 */
				if (GenericUtilityExtension.isOnusVerticalFilterRequest(paramMap) && DateTimeUtility.isFirstDateLatest(
						configurablePropertiesHolder.getProperty(ONUS_VERTICAL_FILTER_FROM_DATE, String.class),
						currentFromDateString)) {
					throw exception;
				}

				/**
				 * if this condition satisfies than it means user doesn't have transaction
				 * in DC as user arrived on PAYTM after UTH AWS from date , so in this
				 * case we don't need to route the request on DC as there it will again
				 * throw 4010
				 */
				if (customerCreationDate != null && currentFromDate != null
						&& (Long.parseLong(customerCreationDate) > currentFromDate)) {
					log.info(
							"User is NTU on AWS and his customerCreationDate > currentFromDate {} > {} ,then routing to DC will still result in"
									+ " the same exception so throwing 4010 and not throwing 507",
							customerCreationDate, currentFromDate);
					throw exception;
				}
				if (!validForAwsToDcRoutingForDateFilter(paramMap)) {
					throw exception;
				}
				throw ExceptionFactory.getException(PANAROMA_SERVICE, CUSTOM_RETRY);
			}
		}
		throw exception;
	}

	public static ResponseDto handle4010ForDcNonMono(final Map<String, String> paramMap, final Throwable exception)
			throws Throwable {
		if (Objects.nonNull(paramMap) && Boolean.FALSE
			.equals(rolloutStrategyHelper.isUserWhiteListed("DcNonTransactingHandling", paramMap.get(ENTITY_ID)))) {
			throw exception;
		}
		ResponseDto emptyResponse = ResponseDto.builder()
			.setCurrentPage(paramMap.get(PAGE_NO))
			.setTxns(Collections.emptyList())
			.build();
		if (paramMap.getOrDefault(SEARCH_API_VERSION, ApiVersion.v3.name())
			.compareToIgnoreCase(ApiVersion.v3.name()) >= 0) {
			emptyResponse.setInvalidateVersion(
					configurablePropertiesHolder.getProperty(APP_CACHE_INVALIDATE_VERSION, Integer.class));
		}
		return emptyResponse;
	}

	public static void handleDetailRoutingPostProcessing(final Exception ex) throws Exception {

		if (Boolean.FALSE.equals(configurablePropertiesHolder.getProperty("isDetailRoutingEnabled", Boolean.class))) {
			throw ex;
		}

		if (ex instanceof PanaromaException) {
			PanaromaException panaromaException = (PanaromaException) ex;
			if (ES_EXCEPTION.equals(panaromaException.getResponseCode())
					|| DETAIL_COULD_NOT_BE_LOADED.equals(panaromaException.getResponseCode())) {
				throw ExceptionFactory.getException(PANAROMA_SERVICE, CUSTOM_RETRY);
			}
		}
		throw ex;
	}

}
