package com.org.panaroma.web.repo;

import static com.org.panaroma.commons.constants.CommonConstants.PAGINATION_TXN_ID;
import static com.org.panaroma.commons.constants.CommonConstants.TRANSACTION_DATE_EPOCH;
import static com.org.panaroma.commons.constants.ConfigPropertiesEnum.MAX_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST;
import static com.org.panaroma.commons.constants.ConfigPropertiesEnum.LIMIT_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST_FEATURE_ENABLED;
import static com.org.panaroma.commons.constants.ConfigPropertiesEnum.VIRTUAL_FROM_DATE_SPECIFIC_ERROR_CODES_DISABLED;
import static com.org.panaroma.commons.constants.Constants.IFSC_QUERY_USED_FOR_UPI_FILTER;
import static com.org.panaroma.commons.constants.WebConstants.EPOCH_BEGINNING_OF_MONTH_TILL_WHICH_MAX_SCAN_TO_BE_DONE_PARAM_MAP_KEY;
import static com.org.panaroma.commons.constants.WebConstants.MAX_PAGE_SIZE_OF_RANGE;
import static com.org.panaroma.commons.constants.WebConstants.PANAROMA_SERVICE;
import static com.org.panaroma.commons.constants.WebConstants.PARAM_MAP_KEY_CONTAINING_FLAG_THAT_COMPLETE_DATA_SCAN_IS_DONE;
import static com.org.panaroma.commons.constants.WebConstants.TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.utils.DateTimeUtility.getEpochOfBeginningOfNthPreviousMonthFromGivenTime;
import static com.org.panaroma.commons.utils.UtilityExtension.isLimitMonthsToScanInSingleApiRequestFeatureApplicable;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.ES_EXCEPTION;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NOT_SEARCHING_BEYOND_VIRTUAL_FROM_DATE;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NO_TRANSACTION_FOUND_FOR_LISTING;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.NO_TXN_FOUND_FOR_LISTING_WHEN_VIRTUAL_FROM_DATE_FLAG_IS_ENABLED;
import static com.org.panaroma.web.exceptionhandler.ErrorCodeConstants.RETURNING_AFTER_SCANNING_TILL_VIRTUAL_FROM_DATE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COMMA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.REQUEST_ID;
import static com.org.panaroma.web.monitoring.MonitoringConstants.SEARCH_API;
import static com.org.panaroma.web.monitoring.MonitoringConstants.UPDATES_API;

import com.org.panaroma.commons.config.ElasticSearchIndexRangeConfig;
import com.org.panaroma.commons.config.IndexConfigWrapper;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.webApi.PaginationParams;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.exceptionhandler.webExceptions.PanaromaException;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.web.utility.ResultsMapperExtended;
import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.web.SearchContext;
import com.org.panaroma.web.cache.CacheContext;
import com.org.panaroma.web.dto.ProgressiveSearchHitsResult;
import com.org.panaroma.web.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.utility.CacheUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.TransactionHistoryMonitoringUtility;
import com.org.panaroma.web.utility.VirtualDateHelper;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

@Log4j2
@Repository
@Qualifier("EsProgressiveSearchingHistoryRepoNonMono")
public class EsProgressiveRecordsSearchingRepoNonMono extends EsBlockingTransactionHistoryRepoNonMono {

	private ResultsMapperExtended resultMapper;

	private ConfigurablePropertiesHolder configurablePropertiesHolder;

	private CacheUtility cacheUtility;

	private String esIndexAlias;

	private EsProgressiveRecordsSearchingRepo esProgressiveRecordsSearchingRepo;

	private List<String> apiNotRequiredPaginationParamsList = List.of("anyTxn", "latestTxn");

	@Autowired
	public EsProgressiveRecordsSearchingRepoNonMono(final ResultsMapperExtended resultsMapper,
			final ElasticSearchIndexRangeConfig elasticSearchIndexRangeConfig,
			// @Qualifier("oldRestEsClient") final RestHighLevelClient
			// restHighLevelClient,
			// @Qualifier("managedEsClient") final RestHighLevelClient
			// managedRestHighLevelClient,
			@Qualifier("managedV2EsClient") final RestHighLevelClient managedV2RestHighLevelClient,
			// @Value("#{'${whitelisted.users.list.for.managed.es}'.split(',')}")
			// final List<String> whiteListedUsersForManagedEs,
			// @Value("${managed.roll.out.percentage}") final Double rollOutPercentage,
			final ConfigurablePropertiesHolder configurablePropertiesHolder,
			@Value("${elastic-search-index}") final String esIndexAlias, final CacheUtility cacheUtility,
			final EsProgressiveRecordsSearchingRepo esProgressiveRecordsSearchingRepo) {
		this.resultMapper = resultsMapper;
		// this.restHighLevelClient = restHighLevelClient;
		// this.managedRestHighLevelClient = managedRestHighLevelClient;
		this.managedV2RestHighLevelClient = managedV2RestHighLevelClient;
		// this.whiteListedUsersForManagedEs = whiteListedUsersForManagedEs;
		// this.rollOutPercentage = rollOutPercentage;
		// log.info("rollOutPercentage : {}", this.rollOutPercentage);
		this.configurablePropertiesHolder = configurablePropertiesHolder;
		this.cacheUtility = cacheUtility;
		this.esIndexAlias = esIndexAlias;
		this.esProgressiveRecordsSearchingRepo = esProgressiveRecordsSearchingRepo;
	}

	@Override
	public RepoResponseSearchApiDto search(final SearchContext searchContext, final Map<String, String> paramMap,
			final CacheContext cacheContext) throws PanaromaException {
		RepoResponseSearchApiDto repoResponseMono = searchWithoutMono(searchContext, paramMap, cacheContext);

		// Jira id : PTH-920
		if (TRUE.equalsIgnoreCase(MdcUtility.getConstantValue(IFSC_QUERY_USED_FOR_UPI_FILTER))) {
			List<TransformedTransactionHistoryDetail> filteredUpiFilterTxns = UtilityExtension
				.filterEsDocsIfDataFetchedUsingIfscQueryForUpiFilter(
						repoResponseMono.getTransformedTransactionHistoryDetailsIterable(),
						searchContext.getEsUpiIdentifier());
			repoResponseMono.setTransformedTransactionHistoryDetailsIterable(filteredUpiFilterTxns);
		}

		return repoResponseMono;
	}

	@Override
	public RepoResponseSearchApiDto searchWithoutMono(final SearchContext searchContext,
			final Map<String, String> paramMap, final CacheContext cacheContext) throws PanaromaException {
		ProgressiveSearchHitsResult progressiveSearchHitsResult;

		long apiSearchStartTime = Instant.now().toEpochMilli();
		// now search the required number of records in iterative fashion
		progressiveSearchHitsResult = fetchRecords(searchContext, searchContext.getPageSize(), paramMap);

		long timeTaken = Instant.now().toEpochMilli() - apiSearchStartTime;
		log.info("Time taken by fetchRecords to process search request : {}ms , No of ES calls: {} pageNo: {}",
				timeTaken, progressiveSearchHitsResult.getNoOfEsCalls(), searchContext.getPageNo());

		// converting ES JSON response to TTHD list.
		List<TransformedTransactionHistoryDetail> txns = getHistoryDetailsList(
				progressiveSearchHitsResult.getHitsList());

		// adding this condition for some specific api where we need only 1 txn from db
		// and we don't want to set pagination parameters in this case
		if (paramMap != null && paramMap.get("apiName") != null
				&& (apiNotRequiredPaginationParamsList.contains(paramMap.get("apiName")))) {
			searchContext.setPageSize(1);
		}

		// setting pagination data
		PaginationParams paginationParams = esProgressiveRecordsSearchingRepo.setPaginationData(txns, searchContext);

		// trimming the extra txn fetched to check for further available data.
		if (txns.size() > searchContext.getPageSize()) {
			txns = txns.subList(0, Math.min(txns.size(), searchContext.getPageSize()));
		}

		List<TransformedTransactionHistoryDetail> dedupedTxns = UtilityExtension.removeDuplicateDocuments(txns);
		// creating response
		final RepoResponseSearchApiDto response = new RepoResponseSearchApiDto(dedupedTxns,
				progressiveSearchHitsResult.getTotalHits(), paginationParams);

		// metrics

		String searchAndFiltersTags = TransactionHistoryMonitoringUtility
			.getSearchTypeTagFromSearchContext(searchContext);

		String filterTags = TransactionHistoryMonitoringUtility.getJoinedMetricsTagsForFilters(paramMap);

		if (StringUtils.isNotBlank(filterTags)) {
			searchAndFiltersTags = searchAndFiltersTags + COMMA + filterTags;
		}

		String api = SEARCH_API;
		if (searchContext.isForUpdates()) {
			api = UPDATES_API;
		}
		metricsAgent.recordNumberOfEsCalls(progressiveSearchHitsResult.getNoOfEsCalls(), api, searchContext.getPageNo(),
				searchAndFiltersTags);

		metricsAgent.pushEsResponseTimeForFilter(paramMap, timeTaken);

		Boolean isLimitMonthsToScanInSingleApiRequestFeatureEnabled = configurablePropertiesHolder
			.getProperty(LIMIT_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST_FEATURE_ENABLED, Boolean.class);

		if (progressiveSearchHitsResult.getHitsList().isEmpty() && !searchContext.isForUpdates()
				&& !(isLimitMonthsToScanInSingleApiRequestFeatureApplicable(
						isLimitMonthsToScanInSingleApiRequestFeatureEnabled))) {
			log.error("No transaction fetched for userId {} from {} to today", searchContext.getEntityId(),
					DateTimeUtility.getDateTime(searchContext.getFromDate()));
			if (VirtualDateHelper.isVirtualFromDateUsed(paramMap)) {
				// In this case VFD is our new fromDate so we don't need to push this data
				// to NTU cache We will throw custom exception from here only.
				if (Boolean.parseBoolean(configurablePropertiesHolder
					.getProperty(VIRTUAL_FROM_DATE_SPECIFIC_ERROR_CODES_DISABLED, String.class))) {
					if (paramMap.containsKey(TRANSACTION_DATE_EPOCH) && paramMap.containsKey(PAGINATION_TXN_ID)) {
						return VirtualDateHelper.getRepoResponseSearchApiDtoWithEmptyList(paramMap);
					}
					throw ExceptionFactory.getCustomizedFromDateException(PANAROMA_SERVICE,
							NO_TRANSACTION_FOUND_FOR_LISTING, DateTimeUtility
								.getDateTimeForExp(DateTimeUtility.getDateTime(searchContext.getFromDate())));
				}
				else {
					if (paramMap.containsKey(TRANSACTION_DATE_EPOCH) && paramMap.containsKey(PAGINATION_TXN_ID)) {
						throw ExceptionFactory.getException(PANAROMA_SERVICE, NOT_SEARCHING_BEYOND_VIRTUAL_FROM_DATE);
					}
					throw ExceptionFactory.getCustomizedException(PANAROMA_SERVICE,
							NO_TXN_FOUND_FOR_LISTING_WHEN_VIRTUAL_FROM_DATE_FLAG_IS_ENABLED,
							VirtualDateHelper.getVirtualFromDateListingFilterInDays());
				}

			}
			cacheUtility.saveDataForNonTransactingUserInCacheIfRequired(searchContext, cacheContext);
			throw ExceptionFactory.getCustomizedFromDateException(PANAROMA_SERVICE, NO_TRANSACTION_FOUND_FOR_LISTING,
					DateTimeUtility.getDateTimeForExp(DateTimeUtility.getDateTime(searchContext.getFromDate())));
		}

		if (progressiveSearchHitsResult.getHitsList().isEmpty() && !searchContext.isForUpdates()
				&& isLimitMonthsToScanInSingleApiRequestFeatureApplicable(
						isLimitMonthsToScanInSingleApiRequestFeatureEnabled)) {
			/*
			 * check for scannedAllIndexes key present in paramMap is added explicitly
			 * because whenever this is present, we will be sure that complete es data
			 * till virtual from date has been scanned. Before PTH-1263, without scanning
			 * the complete eligible es data, there is no chance of hits list being empty
			 */
			if (VirtualDateHelper.isVirtualFromDateUsed(paramMap)
					&& paramMap.containsKey(PARAM_MAP_KEY_CONTAINING_FLAG_THAT_COMPLETE_DATA_SCAN_IS_DONE)) {
				throw ExceptionFactory.getCustomizedException(PANAROMA_SERVICE,
						RETURNING_AFTER_SCANNING_TILL_VIRTUAL_FROM_DATE);
			}
		}

		if (txns.size() > 1) {
			TransformedTransactionHistoryDetail lastFetchedRecord = txns.get(txns.size() - 1);
			if (lastFetchedRecord != null) {
				metricsAgent.recordDateDiffWithCurrentDate(lastFetchedRecord.getTxnDate(), api, searchAndFiltersTags);
			}
			List<String> txnIdList = response.getTransformedTransactionHistoryDetailsIterable()
				.stream()
				.map(TransformedTransactionHistoryDetail::getTxnId)
				.toList();

			log.info(
					"Returned Response from ES for search API -> with list of txnId {} , total hits : {} & paginationParam : {}",
					txnIdList, response.getTotalHits(), response.getPaginationParams());
		}
		else {
			log.info("Returned Response from ES for search API -> with no txns & paginationParam : {}",
					response.getPaginationParams());
		}

		return response;
	}

	private ProgressiveSearchHitsResult fetchRecords(final SearchContext searchContext, final int pageSize,
			final Map<String, String> paramMap) {
		ProgressiveSearchHitsResult progressiveSearchHitsResult = new ProgressiveSearchHitsResult();
		try {
			long newStartTime;
			esProgressiveRecordsSearchingRepo.modifyFromDateForDateFilter(searchContext);
			final long initialFromDate = searchContext.getFromDate();

			SearchContext searchContextNow = new SearchContext();
			BeanUtils.copyProperties(searchContext, searchContextNow);

			int noOfRecordsLeftNow = pageSize;

			boolean maxPageSizeFunctionalityEnable = esProgressiveRecordsSearchingRepo
				.enableMaxPageSizeFunctionality(searchContext);

			IndexConfigWrapper indexConfigWrapper = new IndexConfigWrapper();

			int searchRequestPageSize = pageSize;

			// check if max page size of a index functionality is enable or not
			if (Boolean.TRUE.equals(maxPageSizeFunctionalityEnable)) {
				// Passing max page limit here instead of noOfRecordLeft for fetching as
				// much txns possible from this Range
				searchRequestPageSize = configurablePropertiesHolder.getProperty(MAX_PAGE_SIZE_OF_RANGE, Integer.class);
				;
			}

			populateEpochTillWhichMaxScanCanBeDoneInParamMapIfSupported(searchContext, paramMap);

			SearchRequest searchRequest = esProgressiveRecordsSearchingRepo.createSearchRequest(searchContextNow,
					searchRequestPageSize, null, indexConfigWrapper, initialFromDate, paramMap,
					progressiveSearchHitsResult.getNoOfEsCalls());

			if (searchRequest == null) {
				return progressiveSearchHitsResult;
			}
			log.debug("Trying getting listing data from ES, entityId: {}, reqId : {}", searchContextNow.getEntityId(),
					paramMap.get(REQUEST_ID));

			while (searchRequest.indices().length != 0) {
				SearchResponse searchHits = esProgressiveRecordsSearchingRepo.getSearchHits(searchRequest,
						indexConfigWrapper.getIndexConfig(), searchContext, paramMap);
				progressiveSearchHitsResult.setNoOfEsCalls(progressiveSearchHitsResult.getNoOfEsCalls() + 1);
				SearchHits hits = searchHits.getHits();
				progressiveSearchHitsResult
					.setTotalHits(progressiveSearchHitsResult.getTotalHits() + hits.getTotalHits());
				List<SearchHit> currentHitsList = Arrays.asList(hits.getHits());
				List<SearchHit> hitsList = progressiveSearchHitsResult.getHitsList();

				// terminating progressive search if fetched txns are greater than left
				// now txns for this page
				if (currentHitsList.size() >= noOfRecordsLeftNow + 1) {
					// taking sublist to prevent last element from setting in response
					hitsList.addAll(currentHitsList);
					progressiveSearchHitsResult.setHitsList(hitsList);
					break;
				}
				else {
					// when the no. of records present in these indexes are less than
					// required no. of records.
					noOfRecordsLeftNow = noOfRecordsLeftNow - currentHitsList.size();
					hitsList.addAll(currentHitsList);
					progressiveSearchHitsResult.setHitsList(hitsList);
					if (initialFromDate == searchContextNow.getFromDate()
							|| searchContext.getFromDate() >= searchContextNow.getToDate()) {
						if (initialFromDate == searchContextNow.getFromDate()) {
							log.debug(">>>>>Returning because initialFromDate == searchContextNow.getFromDate() {}",
									searchContextNow.getFromDate());
						}
						else {
							log.debug(
									">>>>>>>Returning because searchContext.fromDate {} >= searchContextNow.toDate {}",
									searchContext.getFromDate(), searchContextNow.getToDate());
						}
						// search has already been done for given period.
						paramMap.put(PARAM_MAP_KEY_CONTAINING_FLAG_THAT_COMPLETE_DATA_SCAN_IS_DONE, "true");
						return progressiveSearchHitsResult;
					}
					newStartTime = esProgressiveRecordsSearchingRepo.getNewStartTime(searchContextNow.getToDate());
					searchContextNow.setToDate(newStartTime);
					searchContextNow.setPageSize(noOfRecordsLeftNow);
					// searchContextNow = createCustomSearchContext(noOfRecordsLeftNow,
					// null);

					searchRequestPageSize = noOfRecordsLeftNow;

					// check if max page size of a index functionality is enable or not
					if (Boolean.TRUE.equals(maxPageSizeFunctionalityEnable)) {
						// Passing max page limit here instead of noOfRecordLeft for
						// fetching as much txns possible from this Range
						searchRequestPageSize = configurablePropertiesHolder.getProperty(MAX_PAGE_SIZE_OF_RANGE,
								Integer.class);
						;
					}
					// Passing max page limit here instead of noOfRecordLeft for fetching
					// as much txns possible from this Range
					searchRequest = esProgressiveRecordsSearchingRepo.createSearchRequest(searchContextNow,
							searchRequestPageSize, newStartTime, indexConfigWrapper, initialFromDate, paramMap,
							progressiveSearchHitsResult.getNoOfEsCalls());

					if (searchRequest == null) {
						return progressiveSearchHitsResult;
					}
				}
			}
		}
		catch (Exception e) {
			log.error("Error in fetching records from ES in fetchRecords {}", CommonsUtility.exceptionFormatter(e));
			throw ExceptionFactory.getException(PANAROMA_SERVICE, ES_EXCEPTION);
		}
		return progressiveSearchHitsResult;
	}

	// https://jira.mypaytm.com/browse/PTH-1263
	private void populateEpochTillWhichMaxScanCanBeDoneInParamMapIfSupported(final SearchContext searchContext,
			final Map<String, String> paramMap) {
		Boolean isLimitMonthsToScanInSingleApiRequestFeatureEnabled = configurablePropertiesHolder
			.getProperty(LIMIT_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST_FEATURE_ENABLED, Boolean.class);
		if (!isLimitMonthsToScanInSingleApiRequestFeatureApplicable(
				isLimitMonthsToScanInSingleApiRequestFeatureEnabled)) {
			return;
		}
		Integer maxMonthsToScanInSingleApiRequest = configurablePropertiesHolder
			.getProperty(MAX_MONTHS_TO_SCAN_IN_SINGLE_LISTING_API_REQUEST, Integer.class);

		Long epochOfBeginningOfMonthTillWhichMaxScanIsToBeDone = null;
		if (searchContext.getPageNo() > 1) {
			Long timeTillWhichScanIsDoneInPreviousRequest = null;

			// TODO Sumit Kumar Tack
			/*
			 * We can optimise this further by sending time till which scan is done
			 * instead of sending last txn details whenever any such case is encountered
			 * where pageSize is not filled after scanning max allowed range
			 */
			if (paramMap.containsKey(TO_DATE_FOR_SUBSEQUENT_LISTING_REQUEST_PARAM_MAP_KEY)) {
				/*
				 * This field will be present in paramMap whenever we didn't scan the
				 * complete eligible es data for previous request because max months to
				 * scan limit functionality was enabled. Assume a case that we had scanned
				 * till 1st April 2025 00:00:00.000 in previous request. Now in current
				 * request we need to scan till 31st March 2025 23:59:59:999, so this
				 * value is sent in toDateForSubsequentRequest field. Assume that value of
				 * listing.max.months.to.scan.in.single.api.request property is 3 months
				 * If we don't do plus 1 in toDateForSubsequentRequest field value,
				 * getEpochOfBeginningOfNthPreviousMonthFromGivenTime method will return
				 * 1st Dec 2024 00:00:00.000 but we want 1st Jan 2025 00:00:00.000
				 */

				// searchContext.getToDate() is used because value of
				// toDateForSubsequentRequest is also populated in this field
				timeTillWhichScanIsDoneInPreviousRequest = searchContext.getToDate() + 1;
			}
			else if (StringUtils.isNotBlank(searchContext.getTransactionDateEpoch())) {
				timeTillWhichScanIsDoneInPreviousRequest = Long.valueOf(searchContext.getTransactionDateEpoch());
			}
			epochOfBeginningOfMonthTillWhichMaxScanIsToBeDone = getEpochOfBeginningOfNthPreviousMonthFromGivenTime(
					maxMonthsToScanInSingleApiRequest, timeTillWhichScanIsDoneInPreviousRequest);

		}
		else {
			epochOfBeginningOfMonthTillWhichMaxScanIsToBeDone = getEpochOfBeginningOfNthPreviousMonthFromGivenTime(
					maxMonthsToScanInSingleApiRequest, searchContext.getToDate());
		}

		paramMap.put(EPOCH_BEGINNING_OF_MONTH_TILL_WHICH_MAX_SCAN_TO_BE_DONE_PARAM_MAP_KEY,
				String.valueOf(epochOfBeginningOfMonthTillWhichMaxScanIsToBeDone));
	}

}
