package com.org.panaroma.web.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InstrumentInfo {

	@Schema(description = "Payment type of instrument like UPI, WALLET")
	private Integer instrumentType;

	@Schema(description = "sub-Payment type of instrument like UPI Credit Line, FoodWallet")
	private Integer subInstrumentType;

	@Schema(description = "Error code received from source system")
	private String errorCode;

	@Schema(description = "Unique identifier of instrument")
	private String identifier;

	@Schema(description = "Account Type like savings, current etc")
	private String accountType;

	@Schema(description = "extra info based on payment type")
	private Map<String, String> metadata;

	@JsonIgnore
	private TransformedParticipant participant;

	@Override
	public String toString() {
		return JsonUtils.toJson(this);
	}

}
