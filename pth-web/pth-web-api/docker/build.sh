#!/usr/bin/env bash
. accounts-panaroma-web/accounts-panaroma-web-api/docker/utils.sh

mvn -pl pth-web/pth-web-api -am clean install -P mth
#mvn clean install -U sonar:sonar
#mvn clean install -DskipTests=true

#mvn deploy

if commandExecutor  ". accounts-panaroma-web/accounts-panaroma-web-api/docker/getversion.sh";then
  search './accounts-panaroma-web/accounts-panaroma-web-api/target/' "middleware-transaction-history.jar"
else
  ret 1
fi
