#!/usr/bin/env bash

case "$PTH_API_INGESTION_SERVICE_ENV" in
      "qa") JAVA_OPTS="-server -Xmx3G -Xms3G -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -XX:+PrintGCDateStamps -verbose:gc -XX:+PrintGCDetails -Xloggc:/log/pth-api-ingestion-service/${HOSTNAME}_gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=1g -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/log/pth-api-ingestion-service/${HOSTNAME}_dump.hprof -XX:MetaspaceSize=100M  -XX:NewSize=150M -XX:MaxNewSize=150M -javaagent:/apps/pth-api-ingestion-service/bin/jacocoagent.jar=port=36420,destfile=/log/pth-api-ingestion-service/$PROJECTNAME_${HOSTNAME}_jacoco.exec,output=tcpserver,append=false,includes=*"
      ;;
      "eksite") JAVA_OPTS="-server -Xmx2G -Xms2G -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector  -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/log/pth-api-ingestion-service/${HOSTNAME}_dump.hprof -XX:MetaspaceSize=100M  -XX:NewSize=150M -XX:MaxNewSize=150M -XX:-OmitStackTraceInFastThrow -javaagent:/apps/pth-api-ingestion-service/bin/jacocoagent.jar=port=36420,destfile=/log/pth-api-ingestion-service/$PROJECTNAME_${HOSTNAME}_jacoco.exec,output=tcpserver,append=false,includes=*"
      ;;
      "stage1") JAVA_OPTS="-server -Xmx2G -Xms2G -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector  -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/log/pth-api-ingestion-service/${HOSTNAME}_dump.hprof -XX:MetaspaceSize=100M  -XX:NewSize=150M -XX:MaxNewSize=150M -XX:-OmitStackTraceInFastThrow -javaagent:/apps/pth-api-ingestion-service/bin/jacocoagent.jar=port=36420,destfile=/log/pth-api-ingestion-service/$PROJECTNAME_${HOSTNAME}_jacoco.exec,output=tcpserver,append=false,includes=*"
      ;;
      "stage2") JAVA_OPTS="-server -Xmx2G -Xms2G -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector  -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/log/pth-api-ingestion-service/${HOSTNAME}_dump.hprof -XX:MetaspaceSize=100M  -XX:NewSize=150M -XX:MaxNewSize=150M -XX:-OmitStackTraceInFastThrow -javaagent:/apps/pth-api-ingestion-service/bin/jacocoagent.jar=port=36420,destfile=/log/pth-api-ingestion-service/$PROJECTNAME_${HOSTNAME}_jacoco.exec,output=tcpserver,append=false,includes=*"
      ;;
      "dev") JAVA_OPTS="-server -Xmx3G -Xms3G -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -XX:+PrintGCDateStamps -verbose:gc -XX:+PrintGCDetails -Xloggc:/log/pth-api-ingestion-service/${HOSTNAME}_gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=1g -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/log/pth-api-ingestion-service/${HOSTNAME}_dump.hprof -XX:MetaspaceSize=100M  -XX:NewSize=150M -XX:MaxNewSize=150M"
      ;;
      "prod") JAVA_OPTS="-server -Xmx3G -Xms3G -DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/log/pth-api-ingestion-service/${HOSTNAME}_dump.hprof -XX:MetaspaceSize=200M  -XX:NewSize=1052M -XX:MaxNewSize=1152M -XX:-OmitStackTraceInFastThrow"
      ;;
esac
