# is there any change done by devops ask?
FROM 339712810712.dkr.ecr.ap-south-1.amazonaws.com/base-images:java17
ENV PROJECTNAME="pth-api-ingestion-service"
ENV JACOCO_VER="0.8.8"
WORKDIR /apps/$PROJECTNAME/
RUN mkdir lib;mkdir conf;mkdir data; mkdir bin

ADD pth-web/api-ingestion-service/docker/jvm.sh /apps/$PROJECTNAME/bin/jvm.sh
ADD pth-web/api-ingestion-service/docker/start.sh /apps/$PROJECTNAME/bin/start.sh
RUN curl -fLo jacoco-$JACOCO_VER.zip https://repo1.maven.org/maven2/org/jacoco/jacoco/$JACOCO_VER/jacoco-$JACOCO_VER.zip || [ $? -eq 0 ] && echo "file Downlodeded" || echo "error! skipping jacoco download"
RUN unzip -o jacoco-$JACOCO_VER.zip -d jacoco-$JACOCO_VER || :
RUN mv jacoco-$JACOCO_VER/lib/jacocoagent.jar /apps/$PROJECTNAME/bin/jacocoagent.jar || :
RUN mv jacoco-$JACOCO_VER/lib/jacococli.jar /apps/$PROJECTNAME/bin/jacococli.jar || :
RUN echo "Asia/Kolkata" > /etc/timezone
#RUN dpkg-reconfigure -f noninteractive tzdata

COPY pth-web/api-ingestion-service/target/${PROJECTNAME}.jar /apps/${PROJECTNAME}/lib/
#EXPOSE 9200

RUN chmod +x /apps/pth-api-ingestion-service/bin/start.sh

#ENV PTH_API_INGESTION_SERVICE_ENV qa

#ENTRYPOINT exec /apps/$PROJECTNAME/bin/start.sh