server.port                                        = 8080


app.name                                           = @project.artifactId@
app.version                                        = @project.version@

es-host-list                                       = es-app-staging-1.uth.paytm.local,es-app-staging-2.uth.paytm.local,es-app-staging-3.uth.paytm.local
es-port                                            = 9200

logging.config                                     = classpath:log4j2-stage3.json
server.tomcat.accesslog.enabled                    = true
server.tomcat.accesslog.pattern                    = [%{yyyy-MM-dd HH:mm:ss.SSS}t] %h \"%r\" %s %b %Dms \"%{Referer}i\" \"%{User-agent}i\" \"%{requestIdLogging}r\"
server.tomcat.accesslog.directory                  = /log/pth-api-ingestion-service
server.tomcat.accesslog.prefix                     = ${HOSTNAME}_access
server.tomcat.accesslog.rotate                     = true
server.tomcat.accesslog.rename-on-rotate           = true
server.tomcat.accesslog.file-date-format           = .yyyy-MM-dd
server.tomcat.accesslog.buffered                   = false
server.tomcat.accesslog.request-attributes-enabled = true

kafka.target[0].client                             = upi
kafka.target[0].topics                             = middleware_transaction_history_upi_data_stage3
kafka.target[0].bootstrap-servers                  = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target[0].confluent-kafka-registry-url       = http://schema-registry.uth.paytm.local:8081/
kafka.target[0].batch-size-bytes                   = 30000
kafka.target[0].linger-ms                          = 15
kafka.target[0].request-timeout-ms                 = 5000

kafka.target[1].client                             = wallet
kafka.target[1].topics=middleware_transaction_history_pg_data_stage3
kafka.target[1].bootstrap-servers                  = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target[1].confluent-kafka-registry-url       = http://schema-registry.uth.paytm.local:8081/
kafka.target[1].batch-size-bytes                   = 30000
kafka.target[1].linger-ms                          = 15
kafka.target[1].request-timeout-ms                 = 5000

kafka.target[2].client                             = pg
kafka.target[2].topics                             = middleware_transaction_history_pg_data_stage3
kafka.target[2].bootstrap-servers                  = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target[2].confluent-kafka-registry-url       = http://schema-registry.uth.paytm.local:8081/
kafka.target[2].batch-size-bytes                   = 30000
kafka.target[2].linger-ms                          = 15
kafka.target[2].request-timeout-ms                 = 5000


kafka.target[3].client                             = tsp_upi
kafka.target[3].topics                             = pth_upi_relay_data_stage3
kafka.target[3].bootstrap-servers                  = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target[3].confluent-kafka-registry-url       = http://schema-registry.uth.paytm.local:8081/
kafka.target[3].batch-size-bytes                   = 30000
kafka.target[3].linger-ms                          = 15
kafka.target[3].request-timeout-ms                 = 5000

kafka.target[4].client                             = mandate
kafka.target[4].topics                             = pth_mandate_data_stage3
kafka.target[4].bootstrap-servers                  = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target[4].confluent-kafka-registry-url       = http://schema-registry.uth.paytm.local:8081/
kafka.target[4].batch-size-bytes                   = 30000
kafka.target[4].linger-ms                          = 15
kafka.target[4].request-timeout-ms                 = 5000

#Kafka properties
kafka.target-v2-list[0].kafka-client-name               = chat_back_filling
kafka.target-v2-list[0].kafka-producer-key              = CHAT_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[0].topics                          = middleware_transaction_history_chat_back_fill_data_stage3
kafka.target-v2-list[0].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[0].batch-size-bytes                = 3000
kafka.target-v2-list[0].confluent-kafka-registry-url    = http://schema-registry.uth.paytm.local:8081/

kafka.target-v2-list[0].linger-ms                       = 15
kafka.target-v2-list[0].request-timeout-ms              = 5000

kafka.target-v2-list[1].kafka-client-name               = upi_back_filling
kafka.target-v2-list[1].kafka-producer-key              = UPI_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[1].topics                          = middleware_transaction_history_back_fill_data_stage3
kafka.target-v2-list[1].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[1].batch-size-bytes                = 3000
kafka.target-v2-list[1].linger-ms                       = 15
kafka.target-v2-list[1].request-timeout-ms              = 5000

kafka.target-v2-list[2].kafka-client-name               = search_back_filling
kafka.target-v2-list[2].kafka-producer-key              = UPI_BACK_FILLING_DATA_PRODUCER
kafka.target-v2-list[2].topics                          = middleware_transaction_history_back_fill_data_stage3
kafka.target-v2-list[2].bootstrap-servers               = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
kafka.target-v2-list[2].batch-size-bytes                = 3000
kafka.target-v2-list[2].linger-ms                       = 15
kafka.target-v2-list[2].request-timeout-ms              = 5000

validate-transaction-data                          = false
jwt.varification.enable=false
check.pointing.enable                              = true

#Prometheus Monitoring Constants
prometheus.explorer                                         = PANAROMA_ADAPTOR_stage3
prometheus.hostname                                         = localhost
prometheus.port                                             = 8130

public.proxy.ip.address                                      = ************
public.proxy.port.number                                     = 8080

s3.bucket.name = flink-checkpoints-middleware-nonprod
s3.bucket.region = ap-south-1
s3.bucket.access.key.value = MIDDLEWARE_PAYMENTS_INTEGRATION_S3_ACCESSKEYID
s3.bucket.secret.key.value = MIDDLEWARE_PAYMENTS_INTEGRATION_S3_SECRETACCESSKEY

kafka.bootstrap-servers = stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
backfill.ppbl.topic.name = middleware_transaction_history_gg_backfill_data_stage3

container.hostname                                = ${HOSTNAME}

#isWhiteListingRequired = 1 :: whiteListing required for some users
#isWhiteListingRequired = -1 :: whiteListing not required or open for all
#isWhiteListingRequired = 0 :: close for all
white.listing.flag.for.add.money.debit.participant = 1
rollOut.percent.for.add.money.debit.participant = 10
white.listed.users.list = 1000012882,1107229601,1107235986,100000006395,1107236181
git.commit.id=1
git.branch=2