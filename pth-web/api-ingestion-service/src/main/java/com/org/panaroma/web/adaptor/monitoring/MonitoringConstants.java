package com.org.panaroma.web.adaptor.monitoring;

public class MonitoringConstants {

	public static final String API_HIT_COUNT = "API_HIT_COUNT";

	public static final String PANAROMA_APPLICATION_ADAPTOR = "application:PanaromaAdaptor";

	public static final String ADAPTOR_API = "ADAPTOR_API";

	public static final String UPDATE_TXN_STATUS_API = "UPDATE_TXN_STATUS_API";

	public static final String API_EXECUTION_TIME = "API_EXECUTION_TIME";

	public static final String API_NAME = "api_name";

	public static final String CLIENT = "client";

	public static final String API_STATUS_COUNT = "API_STATUS_COUNT";

	public static final String API_RESPONSE_CODE_COUNT = "API_RESPONSE_CODE_COUNT";

	public static final String STATUS = "status";

	public static final String RESPONSE_CODE = "responseCode";

	public static final String HTTP_CODE = "httpCode";

	public static final String CONTAINER_HOST = "containerHost";

	public static final String COLON = ":";

	public static final String TXN_TYPE = "txnType";

	public static final String ADAPTOR_EVENTS_DELAY = "ADAPTOR_EVENTS_DELAY";

	public static final String IS_BACKFILL = "isBackfill";

	public static final String KAFKA_SINK_TIME = "KAFKA_SINK_TIME";

	public static final String INVALID_EVENT_COUNT = "INVALID_EVENT_COUNT";

	public static final String BANK_STATUS_COUNT = "BANK_STATUS_COUNT";

	public static final String CREDIT_BANK_NAME = "CREDIT_BANK_NAME:";

	public static final String DEBIT_BANK_NAME = "DEBIT_BANK_NAME:";

	public static final String EXPIRE_NON_SUCCESS_EVENTS_FILTER = "EXPIRE_NON_SUCCESS_EVENTS_FILTER";

	public static final String HTTP_CODE_STR = "httpCodeStr";

	// Mandate Port Validation Constants
	public static final String MANDATE_PORT_IN_VALIDATION_FAILED = "MANDATE_PORT_IN_VALIDATION_FAILED";

	public static final String PORT_TYPE = "portType";

}
