package com.org.panaroma.web.adaptor.utility;

import static com.org.panaroma.commons.constants.CommonConstants.MANDATE_JOURNEY_FLOW_SUPPORTED_TXN_TYPES;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_UPDATE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_EXPIRE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;
import static com.org.panaroma.commons.constants.WebConstants.MANDATE_STATUS;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.TXN_TYPE;

import java.util.Optional;

import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.STATUS;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.EXPIRE_NON_SUCCESS_EVENTS_FILTER;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.MANDATE_PORT_IN_VALIDATION_FAILED;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.PORT_TYPE;
import static com.org.panaroma.web.adaptor.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.enums.MandateStatusEnum;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.web.adaptor.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.adaptor.monitoring.MetricsAgent;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class EventsValidationUtility {

	@Autowired
	private MetricsAgent metricsAgent;

	public boolean isUpiEventNeedToBeDiscarded(final TransactionHistoryDetails data) {

		// Check for SBMD_MANDATE with PURPOSE_UPDATE
		if (TransactionTypeEnum.SBMD_MANDATE.getTransactionTypeKey().equals(data.getTxnType().getTransactionTypeKey())
				&& PURPOSE_UPDATE.equalsIgnoreCase(Utility.getPurpose(data))) {
			log.info("Discarded SBMD_MANDATE UPDATE event. systemId: {}", data.getSystemId());
			return true;
		}

		// Check for expire non-success events
		return isBlacklistedMandateExpireEvent(data);
	}

	/**
	 * Validates Port-In/Port-Out mandate events with purpose codes and mandate status
	 * @param data The transaction history details
	 * @return true if the port event is valid for processing
	 * @throws AdaptorException if validation fails
	 */
	public void validatePortEvent(final TransactionHistoryDetails data) {
		String txnPurpose = Utility.getPurpose(data);

		// Validate Port-In/Port-Out events
		if (PURPOSE_IN_PORT.equalsIgnoreCase(txnPurpose) || PURPOSE_OUT_PORT.equalsIgnoreCase(txnPurpose)) {
			// Reject specific port event status combinations that should not be processed
			validatePortEventStatusCombination(data, txnPurpose);

			// Validate mandate status for remaining valid events
			validatePortEventStatus(data, txnPurpose);
		}
	}

	/**
	 * Validates specific port event status combinations that should be rejected.
	 *
	 * Business Rules: - InPort -> Pending: Should not be processed - InPort -> Failure:
	 * Should not be processed - OutPort -> Pending: Should not be processed
	 * @param data The transaction history details
	 * @param txnPurpose The transaction purpose (IN_PORT or OUT_PORT)
	 * @throws AdaptorException if the event should be rejected
	 */
	private void validatePortEventStatusCombination(final TransactionHistoryDetails data, final String txnPurpose) {
		ClientStatusEnum eventStatus = data.getStatus();

		if (PURPOSE_IN_PORT.equalsIgnoreCase(txnPurpose)) {
			if (ClientStatusEnum.PENDING.equals(eventStatus)) {
				rejectPortEventCombination("InPort -> Pending events are not allowed", txnPurpose, eventStatus, data);
			}
			else if (ClientStatusEnum.FAILURE.equals(eventStatus)) {
				rejectPortEventCombination("InPort -> Failure events are not allowed", txnPurpose, eventStatus, data);
			}
		}
		else if (PURPOSE_OUT_PORT.equalsIgnoreCase(txnPurpose)) {
			if (ClientStatusEnum.PENDING.equals(eventStatus)) {
				rejectPortEventCombination("OutPort -> Pending events are not allowed", txnPurpose, eventStatus, data);
			}
		}
	}

	/**
	 * Validates mandate status for Port events.
	 *
	 * Business Rules: - PORT_IN: mandateStatus must always be ACTIVE - PORT_OUT:
	 * mandateStatus must be PORTED (for OUT_PORT SUCCESS events)
	 * @param data The transaction history details
	 * @param txnPurpose The transaction purpose (IN_PORT or OUT_PORT)
	 * @throws AdaptorException if validation fails
	 */
	private void validatePortEventStatus(final TransactionHistoryDetails data, final String txnPurpose) {

		String mandateStatus = Optional.ofNullable(data.getContextMap())
			.map(ctx -> ctx.get(MANDATE_STATUS))
			.orElse(null);

		if (mandateStatus == null) {
			log.error("Port event validation failed - mandateStatus is missing. UMN: {}, systemId: {}", data.getUmn(),
					data.getSystemId());
			throw ExceptionFactory.getException("ADAPTOR_SERVICE", INVALID_PARAMETER);
		}

		boolean isActive = MandateStatusEnum.ACTIVE.name().equalsIgnoreCase(mandateStatus);
		boolean isPorted = MandateStatusEnum.PORTED.name().equalsIgnoreCase(mandateStatus);

		if (PURPOSE_IN_PORT.equalsIgnoreCase(txnPurpose) && !isActive) {
			rejectPortEvent("Port-In event rejected - mandateStatus must be ACTIVE", mandateStatus, txnPurpose, data);
		}
		else if (PURPOSE_OUT_PORT.equalsIgnoreCase(txnPurpose) && !isPorted) {
			rejectPortEvent("Port-Out event rejected - mandateStatus must be PORTED", mandateStatus, txnPurpose, data);
		}
	}

	/**
	 * Logs error, pushes metrics, and throws exception for mandate status validation
	 * failure.
	 */
	private void rejectPortEvent(String errorMessage, String mandateStatus, String txnPurpose,
			TransactionHistoryDetails data) {
		log.error("{} Current mandateStatus: {}, UMN: {}, systemId: {}", errorMessage, mandateStatus, data.getUmn(),
				data.getSystemId());

		metricsAgent.incrementCount(MANDATE_PORT_IN_VALIDATION_FAILED, PORT_TYPE + COLON + txnPurpose,
				MANDATE_STATUS + COLON + mandateStatus, TXN_TYPE + COLON + data.getTxnType());

		throw ExceptionFactory.getException("ADAPTOR_SERVICE", INVALID_PARAMETER);
	}

	/**
	 * Logs error, pushes metrics, and throws exception for rejected port event status
	 * combinations.
	 */
	private void rejectPortEventCombination(String errorMessage, String txnPurpose, ClientStatusEnum eventStatus,
			TransactionHistoryDetails data) {
		log.error("{} Port event rejected: {} -> {}, UMN: {}, systemId: {}", errorMessage, txnPurpose, eventStatus,
				data.getUmn(), data.getSystemId());

		throw ExceptionFactory.getException("ADAPTOR_SERVICE", INVALID_PARAMETER);
	}

	/**
	 * Checks if an event is an expire non-success event As per JIRA PTH-1069, we need to
	 * discard expire non-success events for all mandate types
	 * @param data The transaction history details
	 * @return true if the event should be blacklisted, false otherwise
	 */
	private boolean isBlacklistedMandateExpireEvent(final TransactionHistoryDetails data) {
		// First check if the transaction type is a mandate type
		if (!MANDATE_JOURNEY_FLOW_SUPPORTED_TXN_TYPES.contains(data.getTxnType())) {
			return false;
		}

		// Check if it's an EXPIRE action
		String txnPurpose = Utility.getPurpose(data);
		if (PURPOSE_EXPIRE.equalsIgnoreCase(txnPurpose)) {
			// Check if it's not a SUCCESS status
			if (!ClientStatusEnum.SUCCESS.equals(data.getStatus())) {
				log.info("Discarded expire non-success event. txnId: {}, txnType: {}, status: {}", data.getSystemId(),
						data.getTxnType(), data.getStatus());

				// Increment metrics for filtered expire events
				metricsAgent.incrementCount(EXPIRE_NON_SUCCESS_EVENTS_FILTER, TXN_TYPE + COLON + data.getTxnType(),
						STATUS + COLON + data.getStatus());

				return true;
			}
		}

		return false;
	}

}
